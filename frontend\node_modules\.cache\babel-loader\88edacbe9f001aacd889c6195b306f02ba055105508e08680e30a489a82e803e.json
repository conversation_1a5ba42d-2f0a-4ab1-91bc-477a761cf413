{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\ui\\\\GameIcons.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ScanIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 9,\n  columnNumber: 3\n}, this);\n_c = ScanIcon;\nexport const AppsIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 16,\n  columnNumber: 3\n}, this);\n_c2 = AppsIcon;\nexport const GroupIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 7H17c-.8 0-1.54.37-2.01.99l-2.54 3.4c-.74.99-.74 2.31 0 3.3l1.04 1.4c.38.51.97.81 1.6.81H16v4h4zm-12.5 0v-7.5h1.75L7.91 7.5A1.5 1.5 0 0 0 6.5 6H5c-.8 0-1.54.37-2.01.99L.45 10.4c-.74.99-.74 2.31 0 3.3l1.04 1.4c.38.51.97.81 1.6.81H4.5v6h3z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 22,\n  columnNumber: 3\n}, this);\n_c3 = GroupIcon;\nexport const MiningIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 28,\n  columnNumber: 3\n}, this);\n_c4 = MiningIcon;\nexport const MarketIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 35,\n  columnNumber: 3\n}, this);\n_c5 = MarketIcon;\nexport const SkillsIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 41,\n  columnNumber: 3\n}, this);\n_c6 = SkillsIcon;\nexport const LogsIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 47,\n  columnNumber: 3\n}, this);\n_c7 = LogsIcon;\nexport const RankingIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M7 4V2c0-1.1.9-2 2-2h6c1.1 0 2 .9 2 2v2h5v2h-2v14c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2V6H2V4h5zM9 4h6V2H9v2zm3 12l4-4-1.41-1.41L12 13.17 9.41 10.59 8 12l4 4z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 53,\n  columnNumber: 3\n}, this);\n_c8 = RankingIcon;\nexport const BankIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M11.5 1L2 6v2h20V6l-9.5-5zM4 8v11h3v-2h2v2h2v-2h2v2h2v-2h2v2h3V8H4zm8 2v5h-1v-5h1z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 59,\n  columnNumber: 3\n}, this);\n_c9 = BankIcon;\nexport const ConfigIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 65,\n  columnNumber: 3\n}, this);\n_c0 = ConfigIcon;\nexport const TerminalIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M20 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4V8h16v10z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M6 10l2.5 2L6 14v-4zm5 4h6v-2h-6v2z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 71,\n  columnNumber: 3\n}, this);\n_c1 = TerminalIcon;\nexport const SecurityIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V16H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 78,\n  columnNumber: 3\n}, this);\n_c10 = SecurityIcon;\nexport const HomeIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 84,\n  columnNumber: 3\n}, this);\n_c11 = HomeIcon;\nexport const ChatIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 90,\n  columnNumber: 3\n}, this);\n_c12 = ChatIcon;\nexport const ShopIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 15a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v12z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 96,\n  columnNumber: 3\n}, this);\n_c13 = ShopIcon;\nexport const NewsIcon = ({\n  className = \"\",\n  size = 24\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: size,\n  height: size,\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  className: className,\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 102,\n  columnNumber: 3\n}, this);\n_c14 = NewsIcon;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"ScanIcon\");\n$RefreshReg$(_c2, \"AppsIcon\");\n$RefreshReg$(_c3, \"GroupIcon\");\n$RefreshReg$(_c4, \"MiningIcon\");\n$RefreshReg$(_c5, \"MarketIcon\");\n$RefreshReg$(_c6, \"SkillsIcon\");\n$RefreshReg$(_c7, \"LogsIcon\");\n$RefreshReg$(_c8, \"RankingIcon\");\n$RefreshReg$(_c9, \"BankIcon\");\n$RefreshReg$(_c0, \"ConfigIcon\");\n$RefreshReg$(_c1, \"TerminalIcon\");\n$RefreshReg$(_c10, \"SecurityIcon\");\n$RefreshReg$(_c11, \"HomeIcon\");\n$RefreshReg$(_c12, \"ChatIcon\");\n$RefreshReg$(_c13, \"ShopIcon\");\n$RefreshReg$(_c14, \"NewsIcon\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ScanIcon", "className", "size", "width", "height", "viewBox", "fill", "children", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AppsIcon", "_c2", "GroupIcon", "_c3", "MiningIcon", "_c4", "MarketIcon", "_c5", "SkillsIcon", "_c6", "LogsIcon", "_c7", "RankingIcon", "_c8", "BankIcon", "_c9", "ConfigIcon", "_c0", "TerminalIcon", "_c1", "SecurityIcon", "_c10", "HomeIcon", "_c11", "ChatIcon", "_c12", "ShopIcon", "_c13", "NewsIcon", "_c14", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/ui/GameIcons.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const ScanIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n    <path d=\"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z\"/>\n  </svg>\n);\n\nexport const AppsIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z\"/>\n  </svg>\n);\n\nexport const GroupIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 7H17c-.8 0-1.54.37-2.01.99l-2.54 3.4c-.74.99-.74 2.31 0 3.3l1.04 1.4c.38.51.97.81 1.6.81H16v4h4zm-12.5 0v-7.5h1.75L7.91 7.5A1.5 1.5 0 0 0 6.5 6H5c-.8 0-1.54.37-2.01.99L.45 10.4c-.74.99-.74 2.31 0 3.3l1.04 1.4c.38.51.97.81 1.6.81H4.5v6h3z\"/>\n  </svg>\n);\n\nexport const MiningIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n    <path d=\"M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6z\"/>\n  </svg>\n);\n\nexport const MarketIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z\"/>\n  </svg>\n);\n\nexport const SkillsIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n  </svg>\n);\n\nexport const LogsIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z\"/>\n  </svg>\n);\n\nexport const RankingIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M7 4V2c0-1.1.9-2 2-2h6c1.1 0 2 .9 2 2v2h5v2h-2v14c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2V6H2V4h5zM9 4h6V2H9v2zm3 12l4-4-1.41-1.41L12 13.17 9.41 10.59 8 12l4 4z\"/>\n  </svg>\n);\n\nexport const BankIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M11.5 1L2 6v2h20V6l-9.5-5zM4 8v11h3v-2h2v2h2v-2h2v2h2v-2h2v2h3V8H4zm8 2v5h-1v-5h1z\"/>\n  </svg>\n);\n\nexport const ConfigIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z\"/>\n  </svg>\n);\n\nexport const TerminalIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M20 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4V8h16v10z\"/>\n    <path d=\"M6 10l2.5 2L6 14v-4zm5 4h6v-2h-6v2z\"/>\n  </svg>\n);\n\nexport const SecurityIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V16H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z\"/>\n  </svg>\n);\n\nexport const HomeIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"/>\n  </svg>\n);\n\nexport const ChatIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"/>\n  </svg>\n);\n\nexport const ShopIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 15a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v12z\"/>\n  </svg>\n);\n\nexport const NewsIcon: React.FC<IconProps> = ({ className = \"\", size = 24 }) => (\n  <svg width={size} height={size} viewBox=\"0 0 24 24\" fill=\"currentColor\" className={className}>\n    <path d=\"M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z\"/>\n  </svg>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO1B,OAAO,MAAMC,QAA6B,GAAGA,CAAC;EAAEC,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBACzEH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,gBAC3FR,OAAA;IAAMS,CAAC,EAAC;EAA4O;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACtPb,OAAA;IAAMS,CAAC,EAAC;EAAmC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC1C,CACN;AAACC,EAAA,GALWb,QAA6B;AAO1C,OAAO,MAAMc,QAA6B,GAAGA,CAAC;EAAEb,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBACzEH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAAuI;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC9I,CACN;AAACG,GAAA,GAJWD,QAA6B;AAM1C,OAAO,MAAME,SAA8B,GAAGA,CAAC;EAAEf,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBAC1EH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAA+U;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACtV,CACN;AAACK,GAAA,GAJWD,SAA8B;AAM3C,OAAO,MAAME,UAA+B,GAAGA,CAAC;EAAEjB,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBAC3EH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,gBAC3FR,OAAA;IAAMS,CAAC,EAAC;EAA8F;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACxGb,OAAA;IAAMS,CAAC,EAAC;EAA+C;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACtD,CACN;AAACO,GAAA,GALWD,UAA+B;AAO5C,OAAO,MAAME,UAA+B,GAAGA,CAAC;EAAEnB,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBAC3EH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAAyQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAChR,CACN;AAACS,GAAA,GAJWD,UAA+B;AAM5C,OAAO,MAAME,UAA+B,GAAGA,CAAC;EAAErB,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBAC3EH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAA8F;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACrG,CACN;AAACW,GAAA,GAJWD,UAA+B;AAM5C,OAAO,MAAME,QAA6B,GAAGA,CAAC;EAAEvB,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBACzEH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAA4H;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACnI,CACN;AAACa,GAAA,GAJWD,QAA6B;AAM1C,OAAO,MAAME,WAAgC,GAAGA,CAAC;EAAEzB,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBAC5EH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAA2J;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClK,CACN;AAACe,GAAA,GAJWD,WAAgC;AAM7C,OAAO,MAAME,QAA6B,GAAGA,CAAC;EAAE3B,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBACzEH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAAoF;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC3F,CACN;AAACiB,GAAA,GAJWD,QAA6B;AAM1C,OAAO,MAAME,UAA+B,GAAGA,CAAC;EAAE7B,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBAC3EH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAA41B;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACn2B,CACN;AAACmB,GAAA,GAJWD,UAA+B;AAM5C,OAAO,MAAME,YAAiC,GAAGA,CAAC;EAAE/B,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBAC7EH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,gBAC3FR,OAAA;IAAMS,CAAC,EAAC;EAAsG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAChHb,OAAA;IAAMS,CAAC,EAAC;EAAqC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC5C,CACN;AAACqB,GAAA,GALWD,YAAiC;AAO9C,OAAO,MAAME,YAAiC,GAAGA,CAAC;EAAEjC,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBAC7EH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAAqN;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC5N,CACN;AAACuB,IAAA,GAJWD,YAAiC;AAM9C,OAAO,MAAME,QAA6B,GAAGA,CAAC;EAAEnC,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBACzEH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAAqC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC5C,CACN;AAACyB,IAAA,GAJWD,QAA6B;AAM1C,OAAO,MAAME,QAA6B,GAAGA,CAAC;EAAErC,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBACzEH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAAwH;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC/H,CACN;AAAC2B,IAAA,GAJWD,QAA6B;AAM1C,OAAO,MAAME,QAA6B,GAAGA,CAAC;EAAEvC,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBACzEH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAA6M;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACpN,CACN;AAAC6B,IAAA,GAJWD,QAA6B;AAM1C,OAAO,MAAME,QAA6B,GAAGA,CAAC;EAAEzC,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,kBACzEH,OAAA;EAAKI,KAAK,EAAED,IAAK;EAACE,MAAM,EAAEF,IAAK;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACL,SAAS,EAAEA,SAAU;EAAAM,QAAA,eAC3FR,OAAA;IAAMS,CAAC,EAAC;EAA4J;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACnK,CACN;AAAC+B,IAAA,GAJWD,QAA6B;AAAA,IAAA7B,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA;AAAAC,YAAA,CAAA/B,EAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAT,IAAA;AAAAS,YAAA,CAAAP,IAAA;AAAAO,YAAA,CAAAL,IAAA;AAAAK,YAAA,CAAAH,IAAA;AAAAG,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}