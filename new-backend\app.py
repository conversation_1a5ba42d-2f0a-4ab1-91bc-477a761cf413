#!/usr/bin/env python3
"""
SHACK Game Backend - Versão Reconstruída
Sistema de hacking online com Supabase
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import jwt
import hashlib
import uuid
from datetime import datetime, timezone, timedelta
from functools import wraps

# Importar cliente Supabase
from supabase import create_client, Client

# Configuração da aplicação
app = Flask(__name__)
CORS(app)

# Configurações
JWT_SECRET = os.environ.get('SECRET_KEY', 'shack-game-secret-key-2024-csrf-protection-enabled')
SUPABASE_URL = os.environ.get('SUPABASE_URL', 'https://eavukvjzvxjohgvkwvmu.supabase.co')
SUPABASE_KEY = os.environ.get('SUPABASE_ANON_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVhdnVrdmp6dnhqb2hndmt3dm11Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE1MTQ0MTMsImV4cCI6MjA2NzA5MDQxM30.UYAGF1OEhoTTCD6jEkprsKmr0j441-UaHvK2F4ZIZHk')

# Inicializar <PERSON>base
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Utilitários
def log(message):
    """Log com timestamp"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")

def generate_jwt_token(user_data):
    """Gera token JWT para o usuário"""
    payload = {
        'uid': user_data['uid'],
        'nick': user_data.get('nick', ''),
        'email': user_data.get('email', ''),
        'exp': datetime.now(timezone.utc) + timedelta(hours=24)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

def hash_password(password):
    """Hash da senha usando SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def token_required(f):
    """Decorator para verificar token JWT"""
    @wraps(f)
    def decorated(*args, **kwargs):
        log(f"Verificando token para {request.endpoint}")
        
        token = None
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer TOKEN
                log(f"Token extraído: {token[:50]}...")
            except IndexError:
                log("Erro ao extrair token do header")
                return jsonify({'sucesso': False, 'mensagem': 'Token inválido'}), 401
        
        if not token:
            log("Token não fornecido")
            return jsonify({'sucesso': False, 'mensagem': 'Token não fornecido'}), 401
        
        try:
            data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            current_user_uid = data['uid']
            log(f"Token válido para UID: {current_user_uid}")
        except jwt.ExpiredSignatureError:
            log("Token expirado")
            return jsonify({'sucesso': False, 'mensagem': 'Token expirado'}), 401
        except jwt.InvalidTokenError as e:
            log(f"Token inválido: {e}")
            return jsonify({'sucesso': False, 'mensagem': 'Token inválido'}), 401
        
        return f(current_user_uid, *args, **kwargs)
    
    return decorated

# Rotas da API

@app.route('/')
def home():
    """Rota de status"""
    return jsonify({
        "status": "OK",
        "message": "SHACK Game Backend funcionando!",
        "version": "2.0",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/api/health')
def health():
    """Health check"""
    try:
        # Testar conexão com Supabase
        result = supabase.table('jogadores').select('count').execute()
        return jsonify({
            "status": "healthy",
            "database": "connected",
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        log(f"Erro no health check: {e}")
        return jsonify({
            "status": "unhealthy",
            "database": "disconnected",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/auth/register', methods=['POST'])
def register():
    """Registro de novo usuário"""
    log("Recebendo requisição de registro...")
    
    try:
        data = request.get_json()
        nick = data.get('nick', '').strip()
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        
        log(f"Tentativa de registro: {nick} ({email})")
        
        # Validações
        if not nick or len(nick) < 3:
            return jsonify({'sucesso': False, 'mensagem': 'Nick deve ter pelo menos 3 caracteres'}), 400
        
        if not email or '@' not in email:
            return jsonify({'sucesso': False, 'mensagem': 'Email inválido'}), 400
        
        if not password or len(password) < 6:
            return jsonify({'sucesso': False, 'mensagem': 'Senha deve ter pelo menos 6 caracteres'}), 400
        
        # Verificar se email já existe
        existing_user = supabase.table('jogadores').select('*').eq('email', email).execute()
        if existing_user.data:
            log(f"Email já existe: {email}")
            return jsonify({'sucesso': False, 'mensagem': 'Email já cadastrado'}), 400
        
        # Verificar se nick já existe
        existing_nick = supabase.table('jogadores').select('*').eq('nick', nick).execute()
        if existing_nick.data:
            log(f"Nick já existe: {nick}")
            return jsonify({'sucesso': False, 'mensagem': 'Nick já está em uso'}), 400
        
        # Criar novo usuário
        new_uid = str(uuid.uuid4())
        password_hash = hash_password(password)
        
        user_data = {
            'uid': new_uid,
            'nick': nick,
            'email': email,
            'password_hash': password_hash,
            'ip': request.remote_addr or '127.0.0.1',
            'nivel': 1,
            'xp': 0,
            'dinheiro': 10,
            'shack': 0,
            'antivirus': 1,
            'bankguard': 1,
            'bruteforce': 1,
            'cpu': 1,
            'firewall': 1,
            'malware_kit': 1,
            'proxyvpn': 1,
            'mineradora': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'last_login': datetime.now(timezone.utc).isoformat()
        }
        
        # Inserir no Supabase
        result = supabase.table('jogadores').insert(user_data).execute()
        
        if result.data:
            log(f"Usuário criado com sucesso: {nick}")
            
            # Gerar token JWT
            token = generate_jwt_token(user_data)
            
            return jsonify({
                'sucesso': True,
                'mensagem': 'Conta criada com sucesso!',
                'user': {
                    'uid': new_uid,
                    'nick': nick,
                    'email': email
                },
                'token': token
            })
        else:
            log("Erro ao inserir usuário no banco")
            return jsonify({'sucesso': False, 'mensagem': 'Erro interno do servidor'}), 500
            
    except Exception as e:
        log(f"Erro no registro: {e}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno do servidor'}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """Login de usuário"""
    log("Recebendo requisição de login...")
    
    try:
        data = request.get_json()
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        
        log(f"Tentativa de login: {email}")
        
        # Validações
        if not email or not password:
            return jsonify({'sucesso': False, 'mensagem': 'Email e senha são obrigatórios'}), 400
        
        # Buscar usuário no Supabase
        user_result = supabase.table('jogadores').select('*').eq('email', email).execute()
        
        if not user_result.data:
            log(f"Usuário não encontrado: {email}")
            return jsonify({'sucesso': False, 'mensagem': 'Email ou senha incorretos'}), 401
        
        user = user_result.data[0]
        password_hash = hash_password(password)
        
        if user['password_hash'] != password_hash:
            log(f"Senha incorreta para: {email}")
            return jsonify({'sucesso': False, 'mensagem': 'Email ou senha incorretos'}), 401
        
        # Atualizar último login
        supabase.table('jogadores').update({
            'last_login': datetime.now(timezone.utc).isoformat(),
            'ip': request.remote_addr or '127.0.0.1'
        }).eq('uid', user['uid']).execute()
        
        log(f"Login bem-sucedido: {user['nick']}")
        
        # Gerar token JWT
        token = generate_jwt_token(user)
        
        return jsonify({
            'sucesso': True,
            'mensagem': 'Login realizado com sucesso!',
            'user': {
                'uid': user['uid'],
                'nick': user['nick'],
                'email': user['email']
            },
            'token': token
        })
        
    except Exception as e:
        log(f"Erro no login: {e}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno do servidor'}), 500

@app.route('/api/jogador', methods=['GET'])
@token_required
def get_player(current_user_uid):
    """Obter dados do jogador"""
    log(f"Buscando dados do jogador: {current_user_uid}")

    try:
        # Buscar jogador no Supabase
        result = supabase.table('jogadores').select('*').eq('uid', current_user_uid).execute()

        if not result.data:
            log(f"Jogador não encontrado: {current_user_uid}")
            return jsonify({'sucesso': False, 'mensagem': 'Jogador não encontrado'}), 404

        player = result.data[0]
        log(f"Dados do jogador carregados: {player['nick']}")

        # Remover dados sensíveis
        player_data = {k: v for k, v in player.items() if k != 'password_hash'}

        return jsonify({
            'sucesso': True,
            'jogador': player_data
        })

    except Exception as e:
        log(f"Erro ao buscar jogador: {e}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno do servidor'}), 500

@app.route('/api/upgrade', methods=['POST'])
@token_required
def upgrade_app(current_user_uid):
    """Fazer upgrade de aplicativo"""
    log(f"Requisição de upgrade do jogador: {current_user_uid}")

    try:
        data = request.get_json()
        app_name = data.get('app_name', '').strip()
        quantity = data.get('quantity', 1)

        if not app_name:
            return jsonify({'sucesso': False, 'mensagem': 'Nome do app é obrigatório'}), 400

        # Buscar jogador atual
        result = supabase.table('jogadores').select('*').eq('uid', current_user_uid).execute()
        if not result.data:
            return jsonify({'sucesso': False, 'mensagem': 'Jogador não encontrado'}), 404

        player = result.data[0]

        # Verificar se o app existe
        valid_apps = ['antivirus', 'bankguard', 'bruteforce', 'cpu', 'firewall', 'malware_kit', 'proxyvpn', 'mineradora']
        if app_name not in valid_apps:
            return jsonify({'sucesso': False, 'mensagem': 'Aplicativo inválido'}), 400

        current_level = player.get(app_name, 1)
        new_level = current_level + quantity

        # Calcular custo do upgrade
        base_cost = 1
        total_cost = 0
        total_shack_cost = 0

        for level in range(current_level, new_level):
            cost = int(base_cost * (1.1 ** level))
            if level >= 10:
                # Níveis 10+ custam dinheiro + shack
                total_cost += cost
                total_shack_cost += max(1, cost // 10)
            else:
                # Níveis 1-9 custam só dinheiro
                total_cost += cost

        # Verificar se tem recursos suficientes
        if player['dinheiro'] < total_cost:
            return jsonify({'sucesso': False, 'mensagem': f'Dinheiro insuficiente. Necessário: ${total_cost}'}), 400

        if total_shack_cost > 0 and player.get('shack', 0) < total_shack_cost:
            return jsonify({'sucesso': False, 'mensagem': f'Shack insuficiente. Necessário: {total_shack_cost}⛏️'}), 400

        # Realizar upgrade
        new_money = player['dinheiro'] - total_cost
        new_shack = player.get('shack', 0) - total_shack_cost

        # Calcular XP ganho (10 XP por nível)
        xp_gained = quantity * 10
        new_xp = player['xp'] + xp_gained

        # Verificar se subiu de nível
        new_player_level = player['nivel']
        while new_xp >= (new_player_level * 100):
            new_xp -= (new_player_level * 100)
            new_player_level += 1

        # Atualizar no banco
        update_data = {
            app_name: new_level,
            'dinheiro': new_money,
            'shack': new_shack,
            'xp': new_xp,
            'nivel': new_player_level
        }

        supabase.table('jogadores').update(update_data).eq('uid', current_user_uid).execute()

        log(f"Upgrade realizado: {player['nick']} - {app_name} nível {new_level}")

        return jsonify({
            'sucesso': True,
            'mensagem': f'{app_name.title()} atualizado para nível {new_level}!',
            'nivel_novo': new_level,
            'custo_dinheiro': total_cost,
            'custo_shack': total_shack_cost,
            'xp_ganho': xp_gained,
            'novo_nivel_jogador': new_player_level
        })

    except Exception as e:
        log(f"Erro no upgrade: {e}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno do servidor'}), 500

if __name__ == '__main__':
    log("=== SHACK GAME BACKEND v2.0 ===")
    log("Iniciando servidor Flask...")
    log(f"Supabase URL: {SUPABASE_URL}")
    log("Servidor rodando em: http://localhost:5000")
    log("===================================")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
