from flask import request, abort
import bleach
import re
import validators

class InputValidation:
    """
    Class for handling input validation to prevent injection attacks
    """
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize with Flask app"""
        self.app = app
        
        # Register a before_request handler to validate all incoming data
        @app.before_request
        def validate_request():
            # Skip validation for GET requests
            if request.method == 'GET':
                return None
                
            # Validate form data
            if request.form:
                for key, value in request.form.items():
                    if not self._is_safe_input(key, value):
                        print(f"⚠️ Input validation failed for form field: {key}")
                        abort(400, description="Invalid input detected")
            
            # Validate JSON data
            if request.is_json:
                json_data = request.get_json(silent=True)
                if json_data and isinstance(json_data, dict):
                    if not self._validate_json_data(json_data):
                        print(f"⚠️ Input validation failed for JSON data")
                        abort(400, description="Invalid input detected in JSON")
        
        print("✅ Input validation middleware initialized")
    
    def _is_safe_input(self, key, value):
        """Check if an input string is safe"""
        if not isinstance(value, str):
            return True
            
        # Strip dangerous HTML/scripts
        cleaned = bleach.clean(value, strip=True)
        
        # Check if cleaning changed the value significantly
        if len(value) > 0 and len(cleaned) < len(value) * 0.8:
            print(f"⚠️ Potentially malicious HTML detected in {key}")
            return False
            
        # Check for common SQL injection patterns
        sql_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|UNION|CREATE)\b.*\bFROM\b)",
            r"--.*$",
            r"/\*.*\*/",
            r";\s*$"
        ]
        
        for pattern in sql_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                print(f"⚠️ Potential SQL injection detected in {key}")
                return False
                
        return True
    
    def _validate_json_data(self, data, depth=0):
        """Recursively validate JSON data"""
        # Prevent deep recursion
        if depth > 5:
            return True
            
        if isinstance(data, dict):
            for key, value in data.items():
                # Validate the key
                if not self._is_safe_input("json_key", str(key)):
                    return False
                    
                # Validate the value based on its type
                if isinstance(value, str):
                    if not self._is_safe_input(f"json_{key}", value):
                        return False
                elif isinstance(value, (dict, list)):
                    if not self._validate_json_data(value, depth + 1):
                        return False
        
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, (dict, list)):
                    if not self._validate_json_data(item, depth + 1):
                        return False
                elif isinstance(item, str):
                    if not self._is_safe_input("json_array_item", item):
                        return False
        
        return True
    
    def sanitize_string(self, value):
        """Sanitize a string for safe display"""
        if not isinstance(value, str):
            return value
        return bleach.clean(value, strip=True)
    
    def validate_email(self, email):
        """Validate email format"""
        return validators.email(email)
    
    def validate_url(self, url):
        """Validate URL format"""
        return validators.url(url)