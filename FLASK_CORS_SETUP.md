# 🔧 Configuração CORS para Flask

## 📋 **Problema**
<PERSON>act (localhost:3000) não consegue se comunicar com o Flask (localhost:5000) devido ao CORS.

## 🛠️ **Solução**

### **1. Instalar Flask-CORS**
```bash
pip install flask-cors
```

### **2. Configurar CORS no seu arquivo principal do Flask**

**Opção A - Configuração Simples:**
```python
from flask import Flask
from flask_cors import CORS

app = Flask(__name__)
CORS(app, origins=["http://localhost:3000"])

# Suas rotas aqui...
```

**Opção B - Configuração Avançada (Recomendada):**
```python
from flask import Flask
from cors_config import configure_cors

app = Flask(__name__)
app = configure_cors(app)  # Usar o arquivo cors_config.py

# Suas rotas aqui...
```

### **3. Verificar se as rotas de API existem**

Certifique-se de que estas rotas estão implementadas no Flask:

```python
# Autenticação
@app.route('/api/auth/login', methods=['POST'])
def login():
    # Implementar login
    pass

@app.route('/api/auth/register', methods=['POST'])
def register():
    # Implementar registro
    pass

@app.route('/api/auth/verify', methods=['GET'])
def verify_token():
    # Verificar token
    pass

# Dados do jogador
@app.route('/api/jogador', methods=['GET'])
def get_player_data():
    # Retornar dados do jogador
    pass

# Chat
@app.route('/api/chat/messages', methods=['GET'])
def get_chat_messages():
    # Retornar mensagens do chat
    pass

@app.route('/api/chat/send', methods=['POST'])
def send_chat_message():
    # Enviar mensagem
    pass

# Scanner
@app.route('/api/scan', methods=['GET'])
def scan_targets():
    # Escanear alvos
    pass

# Transferência
@app.route('/api/transferir', methods=['POST'])
def transfer_money():
    # Transferir dinheiro
    pass
```

### **4. Formato de Resposta Esperado**

O React espera respostas no formato:
```python
{
    "sucesso": True,  # ou False
    "mensagem": "Mensagem de sucesso/erro",
    "data": {},  # dados específicos
    # outros campos específicos...
}
```

### **5. Exemplo de Rota de Login**
```python
@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        # Validar credenciais
        user = validate_user(email, password)
        
        if user:
            token = generate_token(user)
            return jsonify({
                "sucesso": True,
                "mensagem": "Login realizado com sucesso",
                "user": {
                    "uid": user.uid,
                    "nick": user.nick,
                    "email": user.email
                },
                "token": token
            })
        else:
            return jsonify({
                "sucesso": False,
                "mensagem": "Credenciais inválidas"
            }), 401
            
    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro interno: {str(e)}"
        }), 500
```

### **6. Testar a Configuração**

1. **Reiniciar o Flask** após adicionar CORS
2. **Verificar no console do React** se não há mais erros de CORS
3. **Testar login** para verificar se a comunicação funciona

### **7. Problemas Comuns**

**Erro: "ECONNREFUSED"**
- Certifique-se de que o Flask está rodando na porta 5000
- Verifique se não há firewall bloqueando

**Erro: "404 Not Found"**
- Verifique se as rotas estão implementadas corretamente
- Confirme os métodos HTTP (GET, POST, etc.)

**Erro: "CORS policy"**
- Certifique-se de que flask-cors está instalado
- Verifique se a configuração CORS está correta

## ✅ **Verificação Final**

Se tudo estiver funcionando:
- ✅ React inicia sem erros de TypeScript
- ✅ Não há erros de CORS no console
- ✅ Login funciona corretamente
- ✅ Dados do jogador carregam
- ✅ Chat funciona (se implementado)

**Agora o React e Flask estão se comunicando perfeitamente!** 🎉
