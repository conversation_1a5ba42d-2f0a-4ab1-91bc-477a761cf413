-- SQL para adicionar coluna alvo_dados na tabela bruteforce_ataques
-- Execute este SQL no Supabase SQL Editor

-- Adicionar coluna para armazenar dados completos do alvo (para acesso bancário)
ALTER TABLE bruteforce_ataques 
ADD COLUMN IF NOT EXISTS alvo_dados JSONB;

-- Adicionar coluna para confirmação de sucesso
ALTER TABLE bruteforce_ataques 
ADD COLUMN IF NOT EXISTS sucesso_confirmado BOOLEAN DEFAULT FALSE;

-- Coment<PERSON>rio sobre as novas colunas
COMMENT ON COLUMN bruteforce_ataques.alvo_dados IS 'Dados completos do alvo em JSON para acesso bancário após bruteforce bem-sucedido';
COMMENT ON COLUMN bruteforce_ataques.sucesso_confirmado IS 'Confirma se o ataque foi bem-sucedido e processado';

-- Atualizar registros existentes
UPDATE bruteforce_ataques 
SET sucesso_confirmado = CASE 
    WHEN status = 'sucesso' THEN TRUE 
    ELSE FALSE 
END
WHERE sucesso_confirmado IS NULL;
