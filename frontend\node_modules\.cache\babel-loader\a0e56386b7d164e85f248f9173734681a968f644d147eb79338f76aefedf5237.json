{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\GameMainPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\n\n// Layout principal do jogo - Estilo celular antigo\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameMainPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    loadPlayerData,\n    hasPlayerData\n  } = usePlayer();\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('GameMainPage - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 1,\n    dinheiro: 0,\n    ip: '**************'\n  };\n  const handleIconClick = route => {\n    navigate(route);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-bold\",\n              children: \"S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold\",\n              children: (user === null || user === void 0 ? void 0 : user.nick) || 'Ivo77'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400\",\n              children: [\"IP: \", playerData.ip]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-bold text-blue-400\",\n            children: [\"N\\xEDvel \", playerData.nivel]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-green-400 font-semibold\",\n            children: [\"$\", playerData.dinheiro || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-6 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-3 gap-6 max-w-2xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/scanner'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/apps'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCBB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Apps\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/upgrades'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Upgrades\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/chat'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCE7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/shop'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Loja\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/terminal'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCBB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Terminal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/logs'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/ranking'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83C\\uDFC6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Ranking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/bank'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83C\\uDFE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Banco\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/mining'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\u26CF\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Minera\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-cyan-800 rounded-lg p-4 text-center border border-cyan-600 hover:bg-cyan-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/terminal'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCBB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-cyan-200\",\n            children: \"Terminal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/supporters'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\u2764\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Supporters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-around items-center max-w-md mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/scanner'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/chat'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-blue-400 hover:text-blue-300 transition-colors\",\n          onClick: () => handleIconClick('/game'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83C\\uDFE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-semibold\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/terminal'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83D\\uDCBB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Terminal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/config'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Config\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(GameMainPage, \"Gzh4cCW8/G5MAMmfC9xUDiJ/DA0=\", false, function () {\n  return [useNavigate, useSimpleAuth, usePlayer];\n});\n_c = GameMainPage;\nexport default GameMainPage;\nvar _c;\n$RefreshReg$(_c, \"GameMainPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useSimpleAuth", "usePlayer", "jsxDEV", "_jsxDEV", "GameMainPage", "_s", "navigate", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "<PERSON><PERSON><PERSON>", "ip", "handleIconClick", "route", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/GameMainPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\n\n// Layout principal do jogo - Estilo celular antigo\nconst GameMainPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('GameMainPage - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 1,\n    dinheiro: 0,\n    ip: '**************'\n  };\n\n  const handleIconClick = (route: string) => {\n    navigate(route);\n  };\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-xs font-bold\">S</span>\n            </div>\n            <div>\n              <div className=\"text-lg font-bold\">{user?.nick || 'Ivo77'}</div>\n              <div className=\"text-xs text-gray-400\">IP: {playerData.ip}</div>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-lg font-bold text-blue-400\">Nível {playerData.nivel}</div>\n            <div className=\"text-sm text-green-400 font-semibold\">\n              ${playerData.dinheiro || 0}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Grid principal de ícones */}\n      <div className=\"flex-1 p-6 overflow-y-auto\">\n        <div className=\"grid grid-cols-3 gap-6 max-w-2xl mx-auto\">\n          {/* Primeira linha */}\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/scanner')}\n          >\n            <div className=\"text-3xl mb-2\">🔍</div>\n            <span className=\"text-sm font-medium text-gray-200\">Scan</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/apps')}\n          >\n            <div className=\"text-3xl mb-2\">💻</div>\n            <span className=\"text-sm font-medium text-gray-200\">Apps</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/upgrades')}\n          >\n            <div className=\"text-3xl mb-2\">⚙️</div>\n            <span className=\"text-sm font-medium text-gray-200\">Upgrades</span>\n          </div>\n\n          {/* Segunda linha */}\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/chat')}\n          >\n            <div className=\"text-3xl mb-2\">📧</div>\n            <span className=\"text-sm font-medium text-gray-200\">Chat</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/shop')}\n          >\n            <div className=\"text-3xl mb-2\">🛒</div>\n            <span className=\"text-sm font-medium text-gray-200\">Loja</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/terminal')}\n          >\n            <div className=\"text-3xl mb-2\">💻</div>\n            <span className=\"text-sm font-medium text-gray-200\">Terminal</span>\n          </div>\n\n          {/* Terceira linha */}\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/logs')}\n          >\n            <div className=\"text-3xl mb-2\">📊</div>\n            <span className=\"text-sm font-medium text-gray-200\">Logs</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/ranking')}\n          >\n            <div className=\"text-3xl mb-2\">🏆</div>\n            <span className=\"text-sm font-medium text-gray-200\">Ranking</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/bank')}\n          >\n            <div className=\"text-3xl mb-2\">🏦</div>\n            <span className=\"text-sm font-medium text-gray-200\">Banco</span>\n          </div>\n\n          {/* Quarta linha */}\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/mining')}\n          >\n            <div className=\"text-3xl mb-2\">⛏️</div>\n            <span className=\"text-sm font-medium text-gray-200\">Mineração</span>\n          </div>\n\n          <div\n            className=\"bg-cyan-800 rounded-lg p-4 text-center border border-cyan-600 hover:bg-cyan-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/terminal')}\n          >\n            <div className=\"text-3xl mb-2\">💻</div>\n            <span className=\"text-sm font-medium text-cyan-200\">Terminal</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/supporters')}\n          >\n            <div className=\"text-3xl mb-2\">❤️</div>\n            <span className=\"text-sm font-medium text-gray-200\">Supporters</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer com botões de navegação - Layout de celular */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-around items-center max-w-md mx-auto\">\n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/scanner')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">🔍</span>\n            </div>\n            <span className=\"text-xs\">Scan</span>\n          </button>\n\n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/chat')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">💬</span>\n            </div>\n            <span className=\"text-xs\">Chat</span>\n          </button>\n\n          {/* Botão Home no centro */}\n          <button\n            className=\"flex flex-col items-center space-y-1 text-blue-400 hover:text-blue-300 transition-colors\"\n            onClick={() => handleIconClick('/game')}\n          >\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-lg\">🏠</span>\n            </div>\n            <span className=\"text-xs font-semibold\">Home</span>\n          </button>\n\n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/terminal')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">💻</span>\n            </div>\n            <span className=\"text-xs\">Terminal</span>\n          </button>\n\n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/config')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">⚙️</span>\n            </div>\n            <span className=\"text-xs\">Config</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GameMainPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAK,CAAC,GAAGP,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEQ,aAAa;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGV,SAAS,CAAC,CAAC;EAErFH,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,aAAa,IAAI,CAACF,eAAe,EAAE;MACtCG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DH,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACC,aAAa,EAAEF,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAEpD;EACA,MAAMI,UAAU,GAAGN,aAAa,IAAI;IAClCO,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,eAAe,GAAIC,KAAa,IAAK;IACzCd,QAAQ,CAACc,KAAK,CAAC;EACjB,CAAC;EAED,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DnB,OAAA;MAAKkB,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEnB,OAAA;QAAKkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnB,OAAA;UAAKkB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CnB,OAAA;YAAKkB,SAAS,EAAC,kEAAkE;YAAAC,QAAA,eAC/EnB,OAAA;cAAMkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNvB,OAAA;YAAAmB,QAAA,gBACEnB,OAAA;cAAKkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,KAAI;YAAO;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEvB,OAAA;cAAKkB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,MAAI,EAACR,UAAU,CAACI,EAAE;YAAA;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvB,OAAA;UAAKkB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBnB,OAAA;YAAKkB,SAAS,EAAC,iCAAiC;YAAAC,QAAA,GAAC,WAAM,EAACR,UAAU,CAACE,KAAK;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/EvB,OAAA;YAAKkB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,GAAC,GACnD,EAACR,UAAU,CAACG,QAAQ,IAAI,CAAC;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCnB,OAAA;QAAKkB,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBAEvDnB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,eAAe,CAAE;UAAAG,QAAA,gBAEhDnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAGNvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAGNvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,eAAe,CAAE;UAAAG,QAAA,gBAEhDnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAGNvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,cAAc,CAAE;UAAAG,QAAA,gBAE/CnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,kBAAkB,CAAE;UAAAG,QAAA,gBAEnDnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEnB,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEnB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,eAAe,CAAE;UAAAG,QAAA,gBAEhDnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAGTvB,OAAA;UACEkB,SAAS,EAAC,0FAA0F;UACpGO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,OAAO,CAAE;UAAAG,QAAA,gBAExCnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,cAAc,CAAE;UAAAG,QAAA,gBAE/CnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CAjNID,YAAsB;EAAA,QACTL,WAAW,EACXC,aAAa,EAC4CC,SAAS;AAAA;AAAA4B,EAAA,GAH/EzB,YAAsB;AAmN5B,eAAeA,YAAY;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}