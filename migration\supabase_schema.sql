-- Schema para o jogo SHACK no Supabase
-- Execute este SQL no painel do Supabase (SQL Editor)

-- Extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tabela de usuários/jogadores
CREATE TABLE usuarios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    uid VARCHAR(255) UNIQUE NOT NULL, -- Firebase UID
    nick VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    ip VARCHAR(15) NOT NULL,
    
    -- Recursos do jogador
    dinheiro BIGINT DEFAULT 1000,
    shack BIGINT DEFAULT 100,
    
    -- Equipamentos
    cpu INTEGER DEFAULT 1,
    firewall INTEGER DEFAULT 1,
    antivirus INTEGER DEFAULT 1,
    malware_kit INTEGER DEFAULT 1,
    
    -- Progressão
    nivel INTEGER DEFAULT 1,
    xp BIGINT DEFAULT 0,
    
    -- <PERSON><PERSON><PERSON>
    nivel_mineradora INTEGER DEFAULT 1,
    ultimo_recurso_coletado_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Grupos e torneios
    grupo_id UUID,
    deface_points_individual INTEGER DEFAULT 0,
    tournament_points_individual INTEGER DEFAULT 0,
    last_deface_timestamp BIGINT DEFAULT 0,
    
    -- Habilidades NFT (JSONB para flexibilidade)
    habilidades_adquiridas JSONB DEFAULT '{}',
    
    -- Logs e histórico (JSONB para arrays)
    historico JSONB DEFAULT '[]',
    log JSONB DEFAULT '[]',
    
    -- Metadados
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE
);

-- Tabela de grupos
CREATE TABLE grupos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nome VARCHAR(100) UNIQUE NOT NULL,
    lider_uid VARCHAR(255) NOT NULL,
    
    -- Membros (array de UIDs)
    membros JSONB DEFAULT '[]',
    max_membros INTEGER DEFAULT 5,
    
    -- Pontuação
    deface_points INTEGER DEFAULT 0,
    tournament_points INTEGER DEFAULT 0,
    
    -- Metadados
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Tabela de habilidades NFT
CREATE TABLE habilidades_nft (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    skill_id VARCHAR(100) UNIQUE NOT NULL, -- ID único da habilidade
    nome VARCHAR(200) NOT NULL,
    descricao TEXT NOT NULL,
    efeito DECIMAL(5,3) NOT NULL, -- Valor do efeito (ex: 0.05 para 5%)
    preco_shack INTEGER NOT NULL,
    estoque_maximo INTEGER NOT NULL,
    estoque_atual INTEGER NOT NULL,
    
    -- Metadados
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Tabela de notícias
CREATE TABLE noticias (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    priority BOOLEAN DEFAULT FALSE,
    
    -- Metadados
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Tabela de logs de atividade (para auditoria)
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_uid VARCHAR(255),
    action VARCHAR(100) NOT NULL,
    details JSONB,
    ip_address VARCHAR(15),
    user_agent TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de vítimas (para o sistema de exploit)
CREATE TABLE vitimas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ip VARCHAR(15) UNIQUE NOT NULL,
    firewall INTEGER DEFAULT 1,
    antivirus INTEGER DEFAULT 1,
    dinheiro BIGINT DEFAULT 1000,
    
    -- Metadados
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Índices para performance
CREATE INDEX idx_usuarios_uid ON usuarios(uid);
CREATE INDEX idx_usuarios_nick ON usuarios(nick);
CREATE INDEX idx_usuarios_grupo_id ON usuarios(grupo_id);
CREATE INDEX idx_usuarios_nivel ON usuarios(nivel);
CREATE INDEX idx_grupos_nome ON grupos(nome);
CREATE INDEX idx_grupos_lider ON grupos(lider_uid);
CREATE INDEX idx_habilidades_skill_id ON habilidades_nft(skill_id);
CREATE INDEX idx_activity_logs_user_uid ON activity_logs(user_uid);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);
CREATE INDEX idx_vitimas_ip ON vitimas(ip);

-- Foreign Keys
ALTER TABLE usuarios ADD CONSTRAINT fk_usuarios_grupo 
    FOREIGN KEY (grupo_id) REFERENCES grupos(id);

-- Triggers para updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_usuarios_updated_at BEFORE UPDATE ON usuarios
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_grupos_updated_at BEFORE UPDATE ON grupos
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_habilidades_nft_updated_at BEFORE UPDATE ON habilidades_nft
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vitimas_updated_at BEFORE UPDATE ON vitimas
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Inserir algumas vítimas padrão para o sistema de exploit
INSERT INTO vitimas (ip, firewall, antivirus, dinheiro) VALUES
('*************', 1, 1, 5000),
('*************', 2, 1, 7500),
('*************', 1, 2, 6000),
('*************', 3, 2, 10000),
('*************', 2, 3, 8500);

-- Inserir habilidades NFT padrão
INSERT INTO habilidades_nft (skill_id, nome, descricao, efeito, preco_shack, estoque_maximo, estoque_atual) VALUES
('the_killer', 'The Killer', 'Aumenta o poder do seu firewall em 5%', 0.05, 2500, 5, 5),
('firewall_reverso', 'Firewall Reverso', 'Ignora 5% do firewall adversário em ataques.', 0.05, 2000, 5, 5);

COMMIT;
