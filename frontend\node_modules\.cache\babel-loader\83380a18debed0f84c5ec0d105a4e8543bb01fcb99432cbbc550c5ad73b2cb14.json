{"ast": null, "code": "// Mock API Service para desenvolvimento sem Flask\n// Simula as respostas da API real\n\nclass MockApiService {\n  constructor() {\n    this.delay = ms => new Promise(resolve => setTimeout(resolve, ms));\n  }\n  // Simular delay de rede\n  async simulateNetworkDelay() {\n    await this.delay(Math.random() * 1000 + 500); // 500-1500ms\n  }\n\n  // === AUTENTICAÇÃO ===\n  async login(credentials) {\n    await this.simulateNetworkDelay();\n    console.log('MockAPI - Login:', credentials);\n\n    // Simular validação\n    if (!credentials.email || !credentials.password) {\n      return {\n        sucesso: false,\n        mensagem: 'Email e senha são obrigatórios'\n      };\n    }\n\n    // Simular usuário válido\n    const mockUser = {\n      uid: 'mock-user-' + Date.now(),\n      nick: credentials.email.split('@')[0] || 'Player',\n      email: credentials.email\n    };\n    const mockToken = 'mock-jwt-token-' + Date.now();\n    return {\n      sucesso: true,\n      mensagem: 'Login realizado com sucesso',\n      user: mockUser,\n      token: mockToken\n    };\n  }\n  async register(userData) {\n    await this.simulateNetworkDelay();\n    console.log('MockAPI - Register:', userData);\n\n    // Simular validação\n    if (!userData.email || !userData.password || !userData.nick) {\n      return {\n        sucesso: false,\n        mensagem: 'Todos os campos são obrigatórios'\n      };\n    }\n\n    // Simular criação de usuário\n    const mockUser = {\n      uid: 'mock-user-' + Date.now(),\n      nick: userData.nick,\n      email: userData.email\n    };\n    const mockToken = 'mock-jwt-token-' + Date.now();\n    return {\n      sucesso: true,\n      mensagem: 'Conta criada com sucesso',\n      user: mockUser,\n      token: mockToken\n    };\n  }\n  async verifyToken() {\n    await this.simulateNetworkDelay();\n    console.log('MockAPI - Verificando token...');\n\n    // Simular token sempre válido para desenvolvimento\n    return true;\n  }\n\n  // === DADOS DO JOGADOR ===\n  async getPlayerData() {\n    await this.simulateNetworkDelay();\n    console.log('MockAPI - Carregando dados do jogador...');\n    const mockPlayerData = {\n      uid: 'mock-player-123',\n      nick: 'TestPlayer',\n      email: '<EMAIL>',\n      pontos: 1250,\n      nivel: 15,\n      conquistas: 42,\n      ranking: 7,\n      grupo: 'Alpha',\n      ultimo_acesso: new Date().toISOString(),\n      // Propriedades do jogo\n      cpu: 5,\n      ram: 4,\n      firewall: 3,\n      antivirus: 2,\n      malware_kit: 3,\n      bruteforce: 2,\n      bankguard: 1,\n      proxyvpn: 1,\n      dinheiro: 50000,\n      ip: '*************'\n    };\n    return {\n      sucesso: true,\n      mensagem: 'Dados carregados com sucesso',\n      dados: mockPlayerData\n    };\n  }\n\n  // === CHAT ===\n  async getChatMessages() {\n    await this.simulateNetworkDelay();\n    const mockMessages = [{\n      id: 1,\n      usuario: 'Player1',\n      mensagem: 'Olá pessoal!',\n      timestamp: new Date(Date.now() - 300000).toISOString(),\n      tipo: 'global'\n    }, {\n      id: 2,\n      usuario: 'Player2',\n      mensagem: 'Como estão?',\n      timestamp: new Date(Date.now() - 180000).toISOString(),\n      tipo: 'global'\n    }, {\n      id: 3,\n      usuario: 'TestPlayer',\n      mensagem: 'Tudo bem por aqui!',\n      timestamp: new Date(Date.now() - 60000).toISOString(),\n      tipo: 'global'\n    }];\n    return {\n      sucesso: true,\n      mensagem: 'Mensagens carregadas',\n      dados: mockMessages\n    };\n  }\n  async sendChatMessage(message) {\n    await this.simulateNetworkDelay();\n    console.log('MockAPI - Enviando mensagem:', message);\n    return {\n      sucesso: true,\n      mensagem: 'Mensagem enviada com sucesso'\n    };\n  }\n\n  // === SCANNER ===\n  async scanTarget(target) {\n    await this.simulateNetworkDelay();\n    console.log('MockAPI - Escaneando alvo:', target);\n\n    // Simular resultado de scan\n    const mockScanResult = {\n      alvo: target,\n      vulnerabilidades: Math.floor(Math.random() * 5) + 1,\n      dificuldade: ['Fácil', 'Médio', 'Difícil'][Math.floor(Math.random() * 3)],\n      pontos_potenciais: Math.floor(Math.random() * 500) + 100,\n      tempo_estimado: Math.floor(Math.random() * 30) + 5 + ' minutos'\n    };\n    return {\n      sucesso: true,\n      mensagem: 'Scan realizado com sucesso',\n      dados: mockScanResult\n    };\n  }\n\n  // === LOJA ===\n  async getShopItems() {\n    await this.simulateNetworkDelay();\n    const mockItems = [{\n      id: 1,\n      nome: 'Scanner Avançado',\n      descricao: 'Melhora a precisão dos scans',\n      preco: 500,\n      tipo: 'ferramenta'\n    }, {\n      id: 2,\n      nome: 'Boost de XP',\n      descricao: 'Dobra a experiência por 1 hora',\n      preco: 200,\n      tipo: 'boost'\n    }, {\n      id: 3,\n      nome: 'Proteção Extra',\n      descricao: 'Reduz danos recebidos',\n      preco: 750,\n      tipo: 'defesa'\n    }];\n    return {\n      sucesso: true,\n      mensagem: 'Itens da loja carregados',\n      dados: mockItems\n    };\n  }\n}\n\n// Instância singleton\nconst mockApiService = new MockApiService();\nexport default mockApiService;", "map": {"version": 3, "names": ["MockApiService", "constructor", "delay", "ms", "Promise", "resolve", "setTimeout", "simulateNetworkDelay", "Math", "random", "login", "credentials", "console", "log", "email", "password", "sucesso", "mensagem", "mockUser", "uid", "Date", "now", "nick", "split", "mockToken", "user", "token", "register", "userData", "verifyToken", "getPlayerData", "mockPlayerData", "pontos", "nivel", "conquistas", "ranking", "grupo", "ultimo_acesso", "toISOString", "cpu", "ram", "firewall", "antivirus", "malware_kit", "bruteforce", "bankguard", "proxyvpn", "<PERSON><PERSON><PERSON>", "ip", "dados", "getChatMessages", "mockMessages", "id", "usuario", "timestamp", "tipo", "sendChatMessage", "message", "scanTarget", "target", "mockScanResult", "alvo", "vulnerabilidades", "floor", "dificuldade", "pontos_potenciais", "tempo_estimado", "getShopItems", "mockItems", "nome", "descricao", "preco", "mockApiService"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/mockApi.ts"], "sourcesContent": ["// Mock API Service para desenvolvimento sem Flask\n// Simula as respostas da API real\n\nexport interface ApiResponse<T = any> {\n  sucesso: boolean;\n  mensagem: string;\n  dados?: T;\n  user?: any;\n  token?: string;\n}\n\nexport interface PlayerData {\n  uid: string;\n  nick: string;\n  email: string;\n  pontos: number;\n  nivel: number;\n  conquistas: number;\n  ranking: number;\n  grupo?: string;\n  ultimo_acesso: string;\n  // Propriedades do jogo\n  cpu?: number;\n  ram?: number;\n  firewall?: number;\n  antivirus?: number;\n  malware_kit?: number;\n  bruteforce?: number;\n  bankguard?: number;\n  proxyvpn?: number;\n  dinheiro?: number;\n  ip?: string;\n  // Propriedades adicionais\n  shack?: number;\n  banco_saldo?: number;\n  banco_nivel?: number;\n  nivel_mineradora?: number;\n}\n\nclass MockApiService {\n  private delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\n\n  // Simular delay de rede\n  private async simulateNetworkDelay(): Promise<void> {\n    await this.delay(Math.random() * 1000 + 500); // 500-1500ms\n  }\n\n  // === AUTENTICAÇÃO ===\n  async login(credentials: { email: string; password: string }): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Login:', credentials);\n    \n    // Simular validação\n    if (!credentials.email || !credentials.password) {\n      return {\n        sucesso: false,\n        mensagem: 'Email e senha são obrigatórios'\n      };\n    }\n\n    // Simular usuário válido\n    const mockUser = {\n      uid: 'mock-user-' + Date.now(),\n      nick: credentials.email.split('@')[0] || 'Player',\n      email: credentials.email\n    };\n\n    const mockToken = 'mock-jwt-token-' + Date.now();\n\n    return {\n      sucesso: true,\n      mensagem: 'Login realizado com sucesso',\n      user: mockUser,\n      token: mockToken\n    };\n  }\n\n  async register(userData: { email: string; password: string; nick: string }): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Register:', userData);\n    \n    // Simular validação\n    if (!userData.email || !userData.password || !userData.nick) {\n      return {\n        sucesso: false,\n        mensagem: 'Todos os campos são obrigatórios'\n      };\n    }\n\n    // Simular criação de usuário\n    const mockUser = {\n      uid: 'mock-user-' + Date.now(),\n      nick: userData.nick,\n      email: userData.email\n    };\n\n    const mockToken = 'mock-jwt-token-' + Date.now();\n\n    return {\n      sucesso: true,\n      mensagem: 'Conta criada com sucesso',\n      user: mockUser,\n      token: mockToken\n    };\n  }\n\n  async verifyToken(): Promise<boolean> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Verificando token...');\n    \n    // Simular token sempre válido para desenvolvimento\n    return true;\n  }\n\n  // === DADOS DO JOGADOR ===\n  async getPlayerData(): Promise<ApiResponse<PlayerData>> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Carregando dados do jogador...');\n    \n    const mockPlayerData: PlayerData = {\n      uid: 'mock-player-123',\n      nick: 'TestPlayer',\n      email: '<EMAIL>',\n      pontos: 1250,\n      nivel: 15,\n      conquistas: 42,\n      ranking: 7,\n      grupo: 'Alpha',\n      ultimo_acesso: new Date().toISOString(),\n      // Propriedades do jogo\n      cpu: 5,\n      ram: 4,\n      firewall: 3,\n      antivirus: 2,\n      malware_kit: 3,\n      bruteforce: 2,\n      bankguard: 1,\n      proxyvpn: 1,\n      dinheiro: 50000,\n      ip: '*************'\n    };\n\n    return {\n      sucesso: true,\n      mensagem: 'Dados carregados com sucesso',\n      dados: mockPlayerData\n    };\n  }\n\n  // === CHAT ===\n  async getChatMessages(): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    const mockMessages = [\n      {\n        id: 1,\n        usuario: 'Player1',\n        mensagem: 'Olá pessoal!',\n        timestamp: new Date(Date.now() - 300000).toISOString(),\n        tipo: 'global'\n      },\n      {\n        id: 2,\n        usuario: 'Player2',\n        mensagem: 'Como estão?',\n        timestamp: new Date(Date.now() - 180000).toISOString(),\n        tipo: 'global'\n      },\n      {\n        id: 3,\n        usuario: 'TestPlayer',\n        mensagem: 'Tudo bem por aqui!',\n        timestamp: new Date(Date.now() - 60000).toISOString(),\n        tipo: 'global'\n      }\n    ];\n\n    return {\n      sucesso: true,\n      mensagem: 'Mensagens carregadas',\n      dados: mockMessages\n    };\n  }\n\n  async sendChatMessage(message: { mensagem: string; tipo: string }): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Enviando mensagem:', message);\n    \n    return {\n      sucesso: true,\n      mensagem: 'Mensagem enviada com sucesso'\n    };\n  }\n\n  // === SCANNER ===\n  async scanTarget(target: string): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Escaneando alvo:', target);\n    \n    // Simular resultado de scan\n    const mockScanResult = {\n      alvo: target,\n      vulnerabilidades: Math.floor(Math.random() * 5) + 1,\n      dificuldade: ['Fácil', 'Médio', 'Difícil'][Math.floor(Math.random() * 3)],\n      pontos_potenciais: Math.floor(Math.random() * 500) + 100,\n      tempo_estimado: Math.floor(Math.random() * 30) + 5 + ' minutos'\n    };\n\n    return {\n      sucesso: true,\n      mensagem: 'Scan realizado com sucesso',\n      dados: mockScanResult\n    };\n  }\n\n  // === LOJA ===\n  async getShopItems(): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    const mockItems = [\n      {\n        id: 1,\n        nome: 'Scanner Avançado',\n        descricao: 'Melhora a precisão dos scans',\n        preco: 500,\n        tipo: 'ferramenta'\n      },\n      {\n        id: 2,\n        nome: 'Boost de XP',\n        descricao: 'Dobra a experiência por 1 hora',\n        preco: 200,\n        tipo: 'boost'\n      },\n      {\n        id: 3,\n        nome: 'Proteção Extra',\n        descricao: 'Reduz danos recebidos',\n        preco: 750,\n        tipo: 'defesa'\n      }\n    ];\n\n    return {\n      sucesso: true,\n      mensagem: 'Itens da loja carregados',\n      dados: mockItems\n    };\n  }\n}\n\n// Instância singleton\nconst mockApiService = new MockApiService();\nexport default mockApiService;\n"], "mappings": "AAAA;AACA;;AAsCA,MAAMA,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,KAAK,GAAIC,EAAU,IAAK,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC,CAAC;EAAA;EAE/E;EACA,MAAcI,oBAAoBA,CAAA,EAAkB;IAClD,MAAM,IAAI,CAACL,KAAK,CAACM,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;EAChD;;EAEA;EACA,MAAMC,KAAKA,CAACC,WAAgD,EAAwB;IAClF,MAAM,IAAI,CAACJ,oBAAoB,CAAC,CAAC;IAEjCK,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,WAAW,CAAC;;IAE5C;IACA,IAAI,CAACA,WAAW,CAACG,KAAK,IAAI,CAACH,WAAW,CAACI,QAAQ,EAAE;MAC/C,OAAO;QACLC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE;MACZ,CAAC;IACH;;IAEA;IACA,MAAMC,QAAQ,GAAG;MACfC,GAAG,EAAE,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC9BC,IAAI,EAAEX,WAAW,CAACG,KAAK,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ;MACjDT,KAAK,EAAEH,WAAW,CAACG;IACrB,CAAC;IAED,MAAMU,SAAS,GAAG,iBAAiB,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC;IAEhD,OAAO;MACLL,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,6BAA6B;MACvCQ,IAAI,EAAEP,QAAQ;MACdQ,KAAK,EAAEF;IACT,CAAC;EACH;EAEA,MAAMG,QAAQA,CAACC,QAA2D,EAAwB;IAChG,MAAM,IAAI,CAACrB,oBAAoB,CAAC,CAAC;IAEjCK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,QAAQ,CAAC;;IAE5C;IACA,IAAI,CAACA,QAAQ,CAACd,KAAK,IAAI,CAACc,QAAQ,CAACb,QAAQ,IAAI,CAACa,QAAQ,CAACN,IAAI,EAAE;MAC3D,OAAO;QACLN,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE;MACZ,CAAC;IACH;;IAEA;IACA,MAAMC,QAAQ,GAAG;MACfC,GAAG,EAAE,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC9BC,IAAI,EAAEM,QAAQ,CAACN,IAAI;MACnBR,KAAK,EAAEc,QAAQ,CAACd;IAClB,CAAC;IAED,MAAMU,SAAS,GAAG,iBAAiB,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC;IAEhD,OAAO;MACLL,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,0BAA0B;MACpCQ,IAAI,EAAEP,QAAQ;MACdQ,KAAK,EAAEF;IACT,CAAC;EACH;EAEA,MAAMK,WAAWA,CAAA,EAAqB;IACpC,MAAM,IAAI,CAACtB,oBAAoB,CAAC,CAAC;IAEjCK,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;IAE7C;IACA,OAAO,IAAI;EACb;;EAEA;EACA,MAAMiB,aAAaA,CAAA,EAAqC;IACtD,MAAM,IAAI,CAACvB,oBAAoB,CAAC,CAAC;IAEjCK,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IAEvD,MAAMkB,cAA0B,GAAG;MACjCZ,GAAG,EAAE,iBAAiB;MACtBG,IAAI,EAAE,YAAY;MAClBR,KAAK,EAAE,kBAAkB;MACzBkB,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,OAAO;MACdC,aAAa,EAAE,IAAIjB,IAAI,CAAC,CAAC,CAACkB,WAAW,CAAC,CAAC;MACvC;MACAC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,KAAK;MACfC,EAAE,EAAE;IACN,CAAC;IAED,OAAO;MACLhC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,8BAA8B;MACxCgC,KAAK,EAAElB;IACT,CAAC;EACH;;EAEA;EACA,MAAMmB,eAAeA,CAAA,EAAyB;IAC5C,MAAM,IAAI,CAAC3C,oBAAoB,CAAC,CAAC;IAEjC,MAAM4C,YAAY,GAAG,CACnB;MACEC,EAAE,EAAE,CAAC;MACLC,OAAO,EAAE,SAAS;MAClBpC,QAAQ,EAAE,cAAc;MACxBqC,SAAS,EAAE,IAAIlC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACiB,WAAW,CAAC,CAAC;MACtDiB,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLC,OAAO,EAAE,SAAS;MAClBpC,QAAQ,EAAE,aAAa;MACvBqC,SAAS,EAAE,IAAIlC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACiB,WAAW,CAAC,CAAC;MACtDiB,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLC,OAAO,EAAE,YAAY;MACrBpC,QAAQ,EAAE,oBAAoB;MAC9BqC,SAAS,EAAE,IAAIlC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAACiB,WAAW,CAAC,CAAC;MACrDiB,IAAI,EAAE;IACR,CAAC,CACF;IAED,OAAO;MACLvC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,sBAAsB;MAChCgC,KAAK,EAAEE;IACT,CAAC;EACH;EAEA,MAAMK,eAAeA,CAACC,OAA2C,EAAwB;IACvF,MAAM,IAAI,CAAClD,oBAAoB,CAAC,CAAC;IAEjCK,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE4C,OAAO,CAAC;IAEpD,OAAO;MACLzC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;IACZ,CAAC;EACH;;EAEA;EACA,MAAMyC,UAAUA,CAACC,MAAc,EAAwB;IACrD,MAAM,IAAI,CAACpD,oBAAoB,CAAC,CAAC;IAEjCK,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE8C,MAAM,CAAC;;IAEjD;IACA,MAAMC,cAAc,GAAG;MACrBC,IAAI,EAAEF,MAAM;MACZG,gBAAgB,EAAEtD,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACnDuD,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,CAACxD,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;MACzEwD,iBAAiB,EAAEzD,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;MACxDyD,cAAc,EAAE1D,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG;IACvD,CAAC;IAED,OAAO;MACLO,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,4BAA4B;MACtCgC,KAAK,EAAEW;IACT,CAAC;EACH;;EAEA;EACA,MAAMO,YAAYA,CAAA,EAAyB;IACzC,MAAM,IAAI,CAAC5D,oBAAoB,CAAC,CAAC;IAEjC,MAAM6D,SAAS,GAAG,CAChB;MACEhB,EAAE,EAAE,CAAC;MACLiB,IAAI,EAAE,kBAAkB;MACxBC,SAAS,EAAE,8BAA8B;MACzCC,KAAK,EAAE,GAAG;MACVhB,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLiB,IAAI,EAAE,aAAa;MACnBC,SAAS,EAAE,gCAAgC;MAC3CC,KAAK,EAAE,GAAG;MACVhB,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLiB,IAAI,EAAE,gBAAgB;MACtBC,SAAS,EAAE,uBAAuB;MAClCC,KAAK,EAAE,GAAG;MACVhB,IAAI,EAAE;IACR,CAAC,CACF;IAED,OAAO;MACLvC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,0BAA0B;MACpCgC,KAAK,EAAEmB;IACT,CAAC;EACH;AACF;;AAEA;AACA,MAAMI,cAAc,GAAG,IAAIxE,cAAc,CAAC,CAAC;AAC3C,eAAewE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}