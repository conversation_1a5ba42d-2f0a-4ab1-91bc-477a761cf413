{"ast": null, "code": "\"use client\";\n\n// src/useSuspenseInfiniteQuery.ts\nimport { InfiniteQueryObserver, skipToken } from \"@tanstack/query-core\";\nimport { useBaseQuery } from \"./useBaseQuery.js\";\nimport { defaultThrowOnError } from \"./suspense.js\";\nfunction useSuspenseInfiniteQuery(options, queryClient) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (options.queryFn === skipToken) {\n      console.error(\"skipToken is not allowed for useSuspenseInfiniteQuery\");\n    }\n  }\n  return useBaseQuery({\n    ...options,\n    enabled: true,\n    suspense: true,\n    throwOnError: defaultThrowOnError\n  }, InfiniteQueryObserver, queryClient);\n}\nexport { useSuspenseInfiniteQuery };", "map": {"version": 3, "names": ["InfiniteQueryObserver", "skipToken", "useBaseQuery", "defaultThrowOnError", "useSuspenseInfiniteQuery", "options", "queryClient", "process", "env", "NODE_ENV", "queryFn", "console", "error", "enabled", "suspense", "throwOnError"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\react-query\\src\\useSuspenseInfiniteQuery.ts"], "sourcesContent": ["'use client'\nimport { InfiniteQueryObserver, skipToken } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport { defaultThrowOnError } from './suspense'\nimport type {\n  DefaultError,\n  InfiniteData,\n  InfiniteQueryObserverSuccessResult,\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type {\n  UseSuspenseInfiniteQueryOptions,\n  UseSuspenseInfiniteQueryResult,\n} from './types'\n\nexport function useSuspenseInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UseSuspenseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseSuspenseInfiniteQueryResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if ((options.queryFn as any) === skipToken) {\n      console.error('skipToken is not allowed for useSuspenseInfiniteQuery')\n    }\n  }\n\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      suspense: true,\n      throwOnError: defaultThrowOnError,\n    },\n    InfiniteQueryObserver as typeof QueryObserver,\n    queryClient,\n  ) as InfiniteQueryObserverSuccessResult<TData, TError>\n}\n"], "mappings": ";;;AACA,SAASA,qBAAA,EAAuBC,SAAA,QAAiB;AACjD,SAASC,YAAA,QAAoB;AAC7B,SAASC,mBAAA,QAA2B;AAc7B,SAASC,yBAOdC,OAAA,EAOAC,WAAA,EAC+C;EAC/C,IAAIC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAKJ,OAAA,CAAQK,OAAA,KAAoBT,SAAA,EAAW;MAC1CU,OAAA,CAAQC,KAAA,CAAM,uDAAuD;IACvE;EACF;EAEA,OAAOV,YAAA,CACL;IACE,GAAGG,OAAA;IACHQ,OAAA,EAAS;IACTC,QAAA,EAAU;IACVC,YAAA,EAAcZ;EAChB,GACAH,qBAAA,EACAM,WACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}