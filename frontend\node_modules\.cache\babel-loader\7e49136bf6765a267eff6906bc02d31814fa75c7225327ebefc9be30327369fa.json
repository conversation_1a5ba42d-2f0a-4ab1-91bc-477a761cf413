{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\GameHomePage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useHackGameStore } from '../stores/hackGameStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameHomePage = () => {\n  _s();\n  var _notifications$find;\n  const {\n    player,\n    playerApps,\n    currentScreen,\n    setCurrentScreen,\n    notifications\n  } = useHackGameStore();\n  if (!player) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-full text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-4xl mb-4\",\n          children: \"\\uD83D\\uDCF1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Carregando...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this);\n  }\n  const unreadNotifications = notifications.filter(n => !n.read).length;\n\n  // Calcular total de níveis dos apps\n  const totalAppLevels = Object.values(playerApps).reduce((sum, level) => sum + level, 0);\n  const maxAppLevels = Object.keys(playerApps).length * 10; // 7 apps * 10 níveis máximos\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-full bg-gradient-to-br from-gray-900 to-black text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold\",\n            children: player.nick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400 font-mono\",\n            children: player.ip\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm font-semibold text-blue-400\",\n            children: [\"N\\xEDvel \", player.level]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-green-400\",\n            children: [\"$\", player.cash.toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between text-xs text-gray-400 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"XP: \", player.xp]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [player.xpToNextLevel, \" para pr\\xF3ximo n\\xEDvel\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full bg-gray-700 rounded-full h-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500\",\n            style: {\n              width: `${Math.max(10, 100 - player.xpToNextLevel / (player.xp + player.xpToNextLevel) * 100)}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentScreen('apps'),\n          className: \"flex flex-col items-center p-3 rounded-2xl bg-gradient-to-br from-purple-600/20 to-blue-600/20 border border-purple-500/30 hover:from-purple-600/30 hover:to-blue-600/30 transition-all duration-200 hover:scale-105 active:scale-95 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium text-center leading-tight\",\n            children: \"Upgrades\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-1 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-purple-400\",\n              children: totalAppLevels\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-500\",\n              children: \"/\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-500\",\n              children: maxAppLevels\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -top-1 -right-1 w-5 h-5 bg-purple-600 rounded-full flex items-center justify-center border-2 border-gray-900\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-bold text-white\",\n              children: Math.floor(totalAppLevels / maxAppLevels * 100)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentScreen('scanner'),\n          className: \"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium text-center leading-tight\",\n            children: \"Scanner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Rede\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentScreen('terminal'),\n          className: \"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCBB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium text-center leading-tight\",\n            children: \"Terminal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Hack\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentScreen('profile'),\n          className: \"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDC64\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium text-center leading-tight\",\n            children: \"Perfil\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Stats\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentScreen('settings'),\n          className: \"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium text-center leading-tight\",\n            children: \"Config\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Sistema\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentScreen('shop'),\n          className: \"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium text-center leading-tight\",\n            children: \"Loja\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Compras\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentScreen('ranking'),\n          className: \"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83C\\uDFC6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium text-center leading-tight\",\n            children: \"Ranking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Top\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentScreen('logs'),\n          className: \"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium text-center leading-tight\",\n            children: \"Logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Hist\\xF3rico\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentScreen('chat'),\n          className: \"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium text-center leading-tight\",\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Global\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), unreadNotifications > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 p-3 bg-blue-900/30 border border-blue-700/50 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              children: \"Notifica\\xE7\\xF5es\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs bg-blue-600 px-2 py-1 rounded-full\",\n            children: unreadNotifications\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-300 mt-1\",\n          children: ((_notifications$find = notifications.find(n => !n.read)) === null || _notifications$find === void 0 ? void 0 : _notifications$find.message) || 'Novas atualizações disponíveis'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-t border-gray-700\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center text-xs text-gray-400\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"SHACK OS v2.1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(GameHomePage, \"CvyFJuhjOISH/U7jba5OEILMKOk=\", false, function () {\n  return [useHackGameStore];\n});\n_c = GameHomePage;\nexport default GameHomePage;\nvar _c;\n$RefreshReg$(_c, \"GameHomePage\");", "map": {"version": 3, "names": ["React", "useHackGameStore", "jsxDEV", "_jsxDEV", "GameHomePage", "_s", "_notifications$find", "player", "playerApps", "currentScreen", "setCurrentScreen", "notifications", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "unreadNotifications", "filter", "n", "read", "length", "totalAppLevels", "Object", "values", "reduce", "sum", "level", "maxAppLevels", "keys", "nick", "ip", "cash", "toLocaleString", "xp", "xpToNextLevel", "style", "width", "Math", "max", "onClick", "floor", "find", "message", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/GameHomePage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useHackGameStore } from '../stores/hackGameStore';\nimport { GAME_APPS } from '../types/game';\n\nconst GameHomePage: React.FC = () => {\n  const { \n    player, \n    playerApps, \n    currentScreen, \n    setCurrentScreen,\n    notifications \n  } = useHackGameStore();\n\n  if (!player) {\n    return (\n      <div className=\"flex items-center justify-center h-full text-white\">\n        <div className=\"text-center\">\n          <div className=\"text-4xl mb-4\">📱</div>\n          <p>Carregando...</p>\n        </div>\n      </div>\n    );\n  }\n\n  const unreadNotifications = notifications.filter(n => !n.read).length;\n\n  // Calcular total de níveis dos apps\n  const totalAppLevels = Object.values(playerApps).reduce((sum, level) => sum + level, 0);\n  const maxAppLevels = Object.keys(playerApps).length * 10; // 7 apps * 10 níveis máximos\n\n  return (\n    <div className=\"h-full bg-gradient-to-br from-gray-900 to-black text-white flex flex-col\">\n      {/* Header com informações do player */}\n      <div className=\"p-4 border-b border-gray-700\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <div>\n            <h1 className=\"text-lg font-bold\">{player.nick}</h1>\n            <p className=\"text-xs text-gray-400 font-mono\">{player.ip}</p>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-sm font-semibold text-blue-400\">Nível {player.level}</div>\n            <div className=\"text-xs text-green-400\">${player.cash.toLocaleString()}</div>\n          </div>\n        </div>\n        \n        {/* Barra de XP */}\n        <div className=\"mt-2\">\n          <div className=\"flex justify-between text-xs text-gray-400 mb-1\">\n            <span>XP: {player.xp}</span>\n            <span>{player.xpToNextLevel} para próximo nível</span>\n          </div>\n          <div className=\"w-full bg-gray-700 rounded-full h-2\">\n            <div \n              className=\"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500\"\n              style={{ \n                width: `${Math.max(10, 100 - (player.xpToNextLevel / (player.xp + player.xpToNextLevel)) * 100)}%` \n              }}\n            ></div>\n          </div>\n        </div>\n      </div>\n\n      {/* Grid de aplicativos */}\n      <div className=\"flex-1 p-4\">\n        <div className=\"grid grid-cols-3 gap-4\">\n          {/* Upgrades */}\n          <button\n            onClick={() => setCurrentScreen('apps')}\n            className=\"flex flex-col items-center p-3 rounded-2xl bg-gradient-to-br from-purple-600/20 to-blue-600/20 border border-purple-500/30 hover:from-purple-600/30 hover:to-blue-600/30 transition-all duration-200 hover:scale-105 active:scale-95 relative\"\n          >\n            <div className=\"text-3xl mb-2\">⚙️</div>\n            <span className=\"text-xs font-medium text-center leading-tight\">Upgrades</span>\n            <div className=\"flex items-center space-x-1 mt-1\">\n              <span className=\"text-xs text-purple-400\">{totalAppLevels}</span>\n              <span className=\"text-xs text-gray-500\">/</span>\n              <span className=\"text-xs text-gray-500\">{maxAppLevels}</span>\n            </div>\n            {/* Indicador de progresso */}\n            <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-purple-600 rounded-full flex items-center justify-center border-2 border-gray-900\">\n              <span className=\"text-xs font-bold text-white\">{Math.floor((totalAppLevels / maxAppLevels) * 100)}</span>\n            </div>\n          </button>\n\n          {/* Scanner */}\n          <button\n            onClick={() => setCurrentScreen('scanner')}\n            className=\"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\"\n          >\n            <div className=\"text-3xl mb-2\">🔍</div>\n            <span className=\"text-xs font-medium text-center leading-tight\">Scanner</span>\n            <span className=\"text-xs text-gray-400\">Rede</span>\n          </button>\n\n          {/* Terminal */}\n          <button\n            onClick={() => setCurrentScreen('terminal')}\n            className=\"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\"\n          >\n            <div className=\"text-3xl mb-2\">💻</div>\n            <span className=\"text-xs font-medium text-center leading-tight\">Terminal</span>\n            <span className=\"text-xs text-gray-400\">Hack</span>\n          </button>\n\n          {/* Perfil */}\n          <button\n            onClick={() => setCurrentScreen('profile')}\n            className=\"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\"\n          >\n            <div className=\"text-3xl mb-2\">👤</div>\n            <span className=\"text-xs font-medium text-center leading-tight\">Perfil</span>\n            <span className=\"text-xs text-gray-400\">Stats</span>\n          </button>\n\n          {/* Configurações */}\n          <button\n            onClick={() => setCurrentScreen('settings')}\n            className=\"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\"\n          >\n            <div className=\"text-3xl mb-2\">⚙️</div>\n            <span className=\"text-xs font-medium text-center leading-tight\">Config</span>\n            <span className=\"text-xs text-gray-400\">Sistema</span>\n          </button>\n\n          {/* Loja */}\n          <button\n            onClick={() => setCurrentScreen('shop')}\n            className=\"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\"\n          >\n            <div className=\"text-3xl mb-2\">🛒</div>\n            <span className=\"text-xs font-medium text-center leading-tight\">Loja</span>\n            <span className=\"text-xs text-gray-400\">Compras</span>\n          </button>\n\n          {/* Ranking */}\n          <button\n            onClick={() => setCurrentScreen('ranking')}\n            className=\"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\"\n          >\n            <div className=\"text-3xl mb-2\">🏆</div>\n            <span className=\"text-xs font-medium text-center leading-tight\">Ranking</span>\n            <span className=\"text-xs text-gray-400\">Top</span>\n          </button>\n\n          {/* Logs */}\n          <button\n            onClick={() => setCurrentScreen('logs')}\n            className=\"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\"\n          >\n            <div className=\"text-3xl mb-2\">📊</div>\n            <span className=\"text-xs font-medium text-center leading-tight\">Logs</span>\n            <span className=\"text-xs text-gray-400\">Histórico</span>\n          </button>\n\n          {/* Chat */}\n          <button\n            onClick={() => setCurrentScreen('chat')}\n            className=\"flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95\"\n          >\n            <div className=\"text-3xl mb-2\">💬</div>\n            <span className=\"text-xs font-medium text-center leading-tight\">Chat</span>\n            <span className=\"text-xs text-gray-400\">Global</span>\n          </button>\n        </div>\n\n        {/* Notificações recentes */}\n        {unreadNotifications > 0 && (\n          <div className=\"mt-6 p-3 bg-blue-900/30 border border-blue-700/50 rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm font-medium\">Notificações</span>\n              </div>\n              <span className=\"text-xs bg-blue-600 px-2 py-1 rounded-full\">{unreadNotifications}</span>\n            </div>\n            <p className=\"text-xs text-gray-300 mt-1\">\n              {notifications.find(n => !n.read)?.message || 'Novas atualizações disponíveis'}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Footer com informações do sistema */}\n      <div className=\"p-4 border-t border-gray-700\">\n        <div className=\"flex justify-between items-center text-xs text-gray-400\">\n          <span>SHACK OS v2.1</span>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n            <span>Online</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GameHomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3D,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA;EACnC,MAAM;IACJC,MAAM;IACNC,UAAU;IACVC,aAAa;IACbC,gBAAgB;IAChBC;EACF,CAAC,GAAGV,gBAAgB,CAAC,CAAC;EAEtB,IAAI,CAACM,MAAM,EAAE;IACX,oBACEJ,OAAA;MAAKS,SAAS,EAAC,oDAAoD;MAAAC,QAAA,eACjEV,OAAA;QAAKS,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BV,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCd,OAAA;UAAAU,QAAA,EAAG;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMC,mBAAmB,GAAGP,aAAa,CAACQ,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,CAAC,CAACC,MAAM;;EAErE;EACA,MAAMC,cAAc,GAAGC,MAAM,CAACC,MAAM,CAACjB,UAAU,CAAC,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;EACvF,MAAMC,YAAY,GAAGL,MAAM,CAACM,IAAI,CAACtB,UAAU,CAAC,CAACc,MAAM,GAAG,EAAE,CAAC,CAAC;;EAE1D,oBACEnB,OAAA;IAAKS,SAAS,EAAC,0EAA0E;IAAAC,QAAA,gBAEvFV,OAAA;MAAKS,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CV,OAAA;QAAKS,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDV,OAAA;UAAAU,QAAA,gBACEV,OAAA;YAAIS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAEN,MAAM,CAACwB;UAAI;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpDd,OAAA;YAAGS,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAEN,MAAM,CAACyB;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACNd,OAAA;UAAKS,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBV,OAAA;YAAKS,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,WAAM,EAACN,MAAM,CAACqB,KAAK;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/Ed,OAAA;YAAKS,SAAS,EAAC,wBAAwB;YAAAC,QAAA,GAAC,GAAC,EAACN,MAAM,CAAC0B,IAAI,CAACC,cAAc,CAAC,CAAC;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNd,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBV,OAAA;UAAKS,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DV,OAAA;YAAAU,QAAA,GAAM,MAAI,EAACN,MAAM,CAAC4B,EAAE;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5Bd,OAAA;YAAAU,QAAA,GAAON,MAAM,CAAC6B,aAAa,EAAC,2BAAmB;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNd,OAAA;UAAKS,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDV,OAAA;YACES,SAAS,EAAC,2FAA2F;YACrGyB,KAAK,EAAE;cACLC,KAAK,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAIjC,MAAM,CAAC6B,aAAa,IAAI7B,MAAM,CAAC4B,EAAE,GAAG5B,MAAM,CAAC6B,aAAa,CAAC,GAAI,GAAG,CAAC;YACjG;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNd,OAAA;MAAKS,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBV,OAAA;QAAKS,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAErCV,OAAA;UACEsC,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC,MAAM,CAAE;UACxCE,SAAS,EAAC,+OAA+O;UAAAC,QAAA,gBAEzPV,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCd,OAAA;YAAMS,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/Ed,OAAA;YAAKS,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CV,OAAA;cAAMS,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEU;YAAc;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjEd,OAAA;cAAMS,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDd,OAAA;cAAMS,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEgB;YAAY;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,uHAAuH;YAAAC,QAAA,eACpIV,OAAA;cAAMS,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAE0B,IAAI,CAACG,KAAK,CAAEnB,cAAc,GAAGM,YAAY,GAAI,GAAG;YAAC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGTd,OAAA;UACEsC,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC,SAAS,CAAE;UAC3CE,SAAS,EAAC,4IAA4I;UAAAC,QAAA,gBAEtJV,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCd,OAAA;YAAMS,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9Ed,OAAA;YAAMS,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAGTd,OAAA;UACEsC,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC,UAAU,CAAE;UAC5CE,SAAS,EAAC,4IAA4I;UAAAC,QAAA,gBAEtJV,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCd,OAAA;YAAMS,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/Ed,OAAA;YAAMS,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAGTd,OAAA;UACEsC,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC,SAAS,CAAE;UAC3CE,SAAS,EAAC,4IAA4I;UAAAC,QAAA,gBAEtJV,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCd,OAAA;YAAMS,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7Ed,OAAA;YAAMS,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eAGTd,OAAA;UACEsC,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC,UAAU,CAAE;UAC5CE,SAAS,EAAC,4IAA4I;UAAAC,QAAA,gBAEtJV,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCd,OAAA;YAAMS,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7Ed,OAAA;YAAMS,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAGTd,OAAA;UACEsC,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC,MAAM,CAAE;UACxCE,SAAS,EAAC,4IAA4I;UAAAC,QAAA,gBAEtJV,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCd,OAAA;YAAMS,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3Ed,OAAA;YAAMS,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAGTd,OAAA;UACEsC,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC,SAAS,CAAE;UAC3CE,SAAS,EAAC,4IAA4I;UAAAC,QAAA,gBAEtJV,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCd,OAAA;YAAMS,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9Ed,OAAA;YAAMS,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAGTd,OAAA;UACEsC,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC,MAAM,CAAE;UACxCE,SAAS,EAAC,4IAA4I;UAAAC,QAAA,gBAEtJV,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCd,OAAA;YAAMS,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3Ed,OAAA;YAAMS,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eAGTd,OAAA;UACEsC,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC,MAAM,CAAE;UACxCE,SAAS,EAAC,4IAA4I;UAAAC,QAAA,gBAEtJV,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCd,OAAA;YAAMS,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3Ed,OAAA;YAAMS,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLC,mBAAmB,GAAG,CAAC,iBACtBf,OAAA;QAAKS,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3EV,OAAA;UAAKS,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDV,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CV,OAAA;cAAKS,SAAS,EAAC;YAAgD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtEd,OAAA;cAAMS,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNd,OAAA;YAAMS,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAEK;UAAmB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF,CAAC,eACNd,OAAA;UAAGS,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACtC,EAAAP,mBAAA,GAAAK,aAAa,CAACgC,IAAI,CAACvB,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,CAAC,cAAAf,mBAAA,uBAAhCA,mBAAA,CAAkCsC,OAAO,KAAI;QAAgC;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNd,OAAA;MAAKS,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CV,OAAA;QAAKS,SAAS,EAAC,yDAAyD;QAAAC,QAAA,gBACtEV,OAAA;UAAAU,QAAA,EAAM;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1Bd,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CV,OAAA;YAAKS,SAAS,EAAC;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvEd,OAAA;YAAAU,QAAA,EAAM;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACZ,EAAA,CA7LID,YAAsB;EAAA,QAOtBH,gBAAgB;AAAA;AAAA4C,EAAA,GAPhBzC,YAAsB;AA+L5B,eAAeA,YAAY;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}