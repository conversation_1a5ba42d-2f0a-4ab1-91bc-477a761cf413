#!/usr/bin/env python3
"""
Backend Flask simplificado para o SHACK Game
Funciona com dados mock mas com API real
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import jwt
import datetime
import os
import uuid
import hashlib
from datetime import timezone, timedelta
from functools import wraps

app = Flask(__name__)
CORS(app)

# Configurações
JWT_SECRET = os.environ.get('SECRET_KEY', 'shack-game-secret-key-2024-csrf-protection-enabled')

# "Banco de dados" em memória (mock)
users_db = {
    '<EMAIL>': {
        'uid': '6c85a9cf-760c-4e6b-86f0-e74d57e61ce4',
        'nick': 'zakedev',
        'email': '<EMAIL>',
        'password_hash': hashlib.sha256('teste123'.encode()).hexdigest(),
        'ip': '*************',
        'nivel': 5,
        'xp': 250,
        'dinheiro': 150,
        'shack': 25,
        'antivirus': 3,
        'bankguard': 2,
        'bruteforce': 4,
        'cpu': 2,
        'firewall': 3,
        'malware_kit': 2,
        'proxyvpn': 1,
        'created_at': '2024-01-01T00:00:00Z',
        'last_login': datetime.datetime.now(timezone.utc).isoformat()
    }
}

def generate_jwt_token(user_data):
    """Gera token JWT para o usuário"""
    payload = {
        'uid': user_data['uid'],
        'nick': user_data['nick'],
        'exp': datetime.datetime.now(timezone.utc) + timedelta(hours=24)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

def token_required(f):
    """Decorator para verificar token JWT"""
    @wraps(f)
    def decorated(*args, **kwargs):
        print(f"[DEBUG] Verificando token para {request.endpoint}")
        
        token = None
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer TOKEN
                print(f"[DEBUG] Token extraído: {token[:50]}...")
            except IndexError:
                return jsonify({'sucesso': False, 'mensagem': 'Token inválido'}), 401
        
        if not token:
            print("[DEBUG] Token não fornecido")
            return jsonify({'sucesso': False, 'mensagem': 'Token não fornecido'}), 401
        
        try:
            data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            current_user_uid = data['uid']
            print(f"[DEBUG] Token válido para UID: {current_user_uid}")
        except jwt.ExpiredSignatureError:
            print("[DEBUG] Token expirado")
            return jsonify({'sucesso': False, 'mensagem': 'Token expirado'}), 401
        except jwt.InvalidTokenError as e:
            print(f"[DEBUG] Token inválido: {e}")
            return jsonify({'sucesso': False, 'mensagem': 'Token inválido'}), 401
        
        return f(current_user_uid, *args, **kwargs)
    
    return decorated

@app.route('/')
def home():
    return jsonify({"status": "OK", "message": "SHACK Backend funcionando!"})

@app.route('/api/auth/login', methods=['POST'])
def login():
    print("[DEBUG] Recebendo requisição de login...")
    
    data = request.get_json()
    email = data.get('email')
    password = data.get('password')
    
    print(f"[DEBUG] Email: {email}")
    
    # Verificar se usuário existe
    if email not in users_db:
        print("[DEBUG] Usuário não encontrado")
        return jsonify({'sucesso': False, 'mensagem': 'Usuário não encontrado'}), 401
    
    user = users_db[email]
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    
    if user['password_hash'] != password_hash:
        print("[DEBUG] Senha incorreta")
        return jsonify({'sucesso': False, 'mensagem': 'Senha incorreta'}), 401
    
    # Gerar token JWT
    token = generate_jwt_token(user)
    print(f"[DEBUG] Login bem-sucedido, token gerado: {token[:50]}...")
    
    # Atualizar último login
    user['last_login'] = datetime.datetime.now(timezone.utc).isoformat()
    
    return jsonify({
        'sucesso': True,
        'user': {
            'uid': user['uid'],
            'nick': user['nick'],
            'email': user['email']
        },
        'token': token
    })

@app.route('/api/auth/register', methods=['POST'])
def register():
    print("[DEBUG] Recebendo requisição de registro...")
    
    data = request.get_json()
    nick = data.get('nick')
    email = data.get('email')
    password = data.get('password')
    
    print(f"[DEBUG] Registro: {nick}, {email}")
    
    # Verificar se usuário já existe
    if email in users_db:
        print("[DEBUG] Email já existe")
        return jsonify({'sucesso': False, 'mensagem': 'Email já cadastrado'}), 400
    
    # Criar novo usuário
    new_uid = str(uuid.uuid4())
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    
    new_user = {
        'uid': new_uid,
        'nick': nick,
        'email': email,
        'password_hash': password_hash,
        'ip': f'192.168.1.{len(users_db) + 100}',
        'nivel': 1,
        'xp': 0,
        'dinheiro': 10,
        'shack': 0,
        'antivirus': 1,
        'bankguard': 1,
        'bruteforce': 1,
        'cpu': 1,
        'firewall': 1,
        'malware_kit': 1,
        'proxyvpn': 1,
        'created_at': datetime.datetime.now(timezone.utc).isoformat(),
        'last_login': datetime.datetime.now(timezone.utc).isoformat()
    }
    
    users_db[email] = new_user
    
    # Gerar token JWT
    token = generate_jwt_token(new_user)
    print(f"[DEBUG] Registro bem-sucedido, token gerado: {token[:50]}...")
    
    return jsonify({
        'sucesso': True,
        'user': {
            'uid': new_user['uid'],
            'nick': new_user['nick'],
            'email': new_user['email']
        },
        'token': token
    })

@app.route('/api/jogador', methods=['GET'])
@token_required
def get_player(current_user_uid):
    print(f"[DEBUG] Buscando dados do jogador para UID: {current_user_uid}")
    
    # Encontrar usuário pelo UID
    user = None
    for email, user_data in users_db.items():
        if user_data['uid'] == current_user_uid:
            user = user_data
            break
    
    if not user:
        print("[DEBUG] Usuário não encontrado")
        return jsonify({'sucesso': False, 'mensagem': 'Usuário não encontrado'}), 404
    
    print(f"[DEBUG] Dados do jogador encontrados: {user['nick']}")
    
    return jsonify({
        'sucesso': True,
        'jogador': {
            'uid': user['uid'],
            'nick': user['nick'],
            'email': user['email'],
            'ip': user['ip'],
            'nivel': user['nivel'],
            'xp': user['xp'],
            'dinheiro': user['dinheiro'],
            'shack': user.get('shack', 0),
            'antivirus': user['antivirus'],
            'bankguard': user['bankguard'],
            'bruteforce': user['bruteforce'],
            'cpu': user['cpu'],
            'firewall': user['firewall'],
            'malware_kit': user['malware_kit'],
            'proxyvpn': user['proxyvpn'],
            'created_at': user['created_at'],
            'last_login': user['last_login']
        }
    })

if __name__ == '__main__':
    print("=== SHACK BACKEND SIMPLIFICADO ===")
    print("Iniciando servidor Flask...")
    print("URL: http://localhost:5000")
    print("Usuário de teste:")
    print("  Email: <EMAIL>")
    print("  Senha: teste123")
    print("===================================")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
