{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleLoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleLoginPage = () => {\n  _s();\n  const {\n    isLoading,\n    error,\n    login,\n    register,\n    simulateLogin,\n    isAuthenticated,\n    user,\n    clearError\n  } = useSimpleAuth();\n  const [isRegisterMode, setIsRegisterMode] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    nick: ''\n  });\n  console.log('SimpleLoginPage - Estado:', {\n    isLoading,\n    isAuthenticated,\n    user: !!user\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Limpar erro quando usuário digita\n    if (error) clearError();\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (isRegisterMode) {\n      if (!formData.email || !formData.password || !formData.nick) {\n        return;\n      }\n      await register({\n        email: formData.email,\n        password: formData.password,\n        nick: formData.nick\n      });\n    } else {\n      if (!formData.email || !formData.password) {\n        return;\n      }\n      await login({\n        email: formData.email,\n        password: formData.password\n      });\n    }\n  };\n  const toggleMode = () => {\n    setIsRegisterMode(!isRegisterMode);\n    setFormData({\n      email: '',\n      password: '',\n      nick: ''\n    });\n    clearError();\n  };\n\n  // Se já está autenticado, mostrar sucesso e aguardar carregamento\n  if (isAuthenticated && user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-green-400 mb-4\",\n          children: \"Login Realizado!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-300 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Bem-vindo, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: user.nick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 27\n            }, this), \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: [\"UID: \", user.uid]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: [\"Email: \", user.email]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-green-400 mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-green-300\",\n            children: \"\\uD83C\\uDFAE Carregando o jogo...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card max-w-md w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-2\",\n          children: \"\\uD83C\\uDFAE SHACK Web Game\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-muted\",\n          children: \"Login Simplificado (Modo Teste)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 text-red-100 p-4 rounded-lg mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold mb-6\",\n            children: isRegisterMode ? '📝 Criar Conta' : '🔐 Fazer Login'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium mb-2\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\",\n                placeholder: \"<EMAIL>\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), isRegisterMode && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium mb-2\",\n                children: \"Nick\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"nick\",\n                value: formData.nick,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\",\n                placeholder: \"Seu nick no jogo\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium mb-2\",\n                children: \"Senha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\",\n                placeholder: \"Sua senha\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isLoading,\n              className: `w-full py-3 px-4 rounded-lg font-medium transition-colors ${isLoading ? 'bg-gray-600 text-gray-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'}`,\n              children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), isRegisterMode ? 'Criando conta...' : 'Fazendo login...']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this) : isRegisterMode ? '✨ Criar Conta' : '🚀 Entrar'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: toggleMode,\n              className: \"text-blue-400 hover:text-blue-300 text-sm\",\n              children: isRegisterMode ? 'Já tem conta? Fazer login' : 'Não tem conta? Criar uma'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card bg-bg-tertiary\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"\\uD83E\\uDDEA Teste R\\xE1pido\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-text-muted text-sm mb-4\",\n            children: \"Para testar rapidamente sem preencher formul\\xE1rio:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: simulateLogin,\n            disabled: isLoading,\n            className: \"w-full btn-secondary\",\n            children: \"\\u26A1 Login Instant\\xE2neo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card bg-yellow-900 border-yellow-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-yellow-100 mb-2\",\n            children: \"\\u2139\\uFE0F Modo Desenvolvimento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-yellow-200 text-sm\",\n            children: \"Usando API mock para desenvolvimento. Os dados n\\xE3o s\\xE3o persistidos.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 text-center text-text-muted text-xs\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Vers\\xE3o de teste - React funcionando \\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleLoginPage, \"roLM9JythJepd9gq3OU2o3qxVa4=\", false, function () {\n  return [useSimpleAuth];\n});\n_c = SimpleLoginPage;\nexport default SimpleLoginPage;\nvar _c;\n$RefreshReg$(_c, \"SimpleLoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useSimpleAuth", "jsxDEV", "_jsxDEV", "SimpleLoginPage", "_s", "isLoading", "error", "login", "register", "simulateLogin", "isAuthenticated", "user", "clearError", "isRegisterMode", "setIsRegisterMode", "formData", "setFormData", "email", "password", "nick", "console", "log", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "toggleMode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "uid", "onSubmit", "type", "onChange", "placeholder", "required", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleLoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\n\nconst SimpleLoginPage: React.FC = () => {\n  const { isLoading, error, login, register, simulateLogin, isAuthenticated, user, clearError } = useSimpleAuth();\n\n  const [isRegisterMode, setIsRegisterMode] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    nick: ''\n  });\n\n  console.log('SimpleLoginPage - Estado:', { isLoading, isAuthenticated, user: !!user });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Limpar erro quando usuário digita\n    if (error) clearError();\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (isRegisterMode) {\n      if (!formData.email || !formData.password || !formData.nick) {\n        return;\n      }\n      await register({\n        email: formData.email,\n        password: formData.password,\n        nick: formData.nick\n      });\n    } else {\n      if (!formData.email || !formData.password) {\n        return;\n      }\n      await login({\n        email: formData.email,\n        password: formData.password\n      });\n    }\n  };\n\n  const toggleMode = () => {\n    setIsRegisterMode(!isRegisterMode);\n    setFormData({ email: '', password: '', nick: '' });\n    clearError();\n  };\n\n  // Se já está autenticado, mostrar sucesso e aguardar carregamento\n  if (isAuthenticated && user) {\n    return (\n      <div className=\"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">✅</div>\n          <h1 className=\"text-2xl font-bold text-green-400 mb-4\">\n            Login Realizado!\n          </h1>\n          <div className=\"text-gray-300 mb-6\">\n            <p>Bem-vindo, <strong>{user.nick}</strong>!</p>\n            <p className=\"text-sm\">UID: {user.uid}</p>\n            <p className=\"text-sm\">Email: {user.email}</p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-green-400 mx-auto mb-2\"></div>\n            <p className=\"text-sm text-green-300\">\n              🎮 Carregando o jogo...\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\">\n      <div className=\"card max-w-md w-full\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold mb-2\">\n            🎮 SHACK Web Game\n          </h1>\n          <p className=\"text-text-muted\">\n            Login Simplificado (Modo Teste)\n          </p>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900 border border-red-500 text-red-100 p-4 rounded-lg mb-6\">\n            <p className=\"text-sm\">{error}</p>\n          </div>\n        )}\n\n        <div className=\"space-y-6\">\n          {/* Formulário de Login/Registro */}\n          <div className=\"card\">\n            <h3 className=\"text-xl font-semibold mb-6\">\n              {isRegisterMode ? '📝 Criar Conta' : '🔐 Fazer Login'}\n            </h3>\n\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">\n                  Email\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                />\n              </div>\n\n              {isRegisterMode && (\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    Nick\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"nick\"\n                    value={formData.nick}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\"\n                    placeholder=\"Seu nick no jogo\"\n                    required\n                  />\n                </div>\n              )}\n\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">\n                  Senha\n                </label>\n                <input\n                  type=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\"\n                  placeholder=\"Sua senha\"\n                  required\n                />\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${\n                  isLoading\n                    ? 'bg-gray-600 text-gray-300 cursor-not-allowed'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white'\n                }`}\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                    {isRegisterMode ? 'Criando conta...' : 'Fazendo login...'}\n                  </div>\n                ) : (\n                  isRegisterMode ? '✨ Criar Conta' : '🚀 Entrar'\n                )}\n              </button>\n            </form>\n\n            <div className=\"mt-6 text-center\">\n              <button\n                onClick={toggleMode}\n                className=\"text-blue-400 hover:text-blue-300 text-sm\"\n              >\n                {isRegisterMode\n                  ? 'Já tem conta? Fazer login'\n                  : 'Não tem conta? Criar uma'\n                }\n              </button>\n            </div>\n          </div>\n\n          {/* Botão de teste rápido */}\n          <div className=\"card bg-bg-tertiary\">\n            <h3 className=\"text-lg font-semibold mb-4\">\n              🧪 Teste Rápido\n            </h3>\n            <p className=\"text-text-muted text-sm mb-4\">\n              Para testar rapidamente sem preencher formulário:\n            </p>\n\n            <button\n              onClick={simulateLogin}\n              disabled={isLoading}\n              className=\"w-full btn-secondary\"\n            >\n              ⚡ Login Instantâneo\n            </button>\n          </div>\n\n          <div className=\"card bg-yellow-900 border-yellow-500\">\n            <h3 className=\"text-lg font-semibold text-yellow-100 mb-2\">\n              ℹ️ Modo Desenvolvimento\n            </h3>\n            <p className=\"text-yellow-200 text-sm\">\n              Usando API mock para desenvolvimento. Os dados não são persistidos.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"mt-8 text-center text-text-muted text-xs\">\n          <p>Versão de teste - React funcionando ✅</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleLoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC,SAAS;IAAEC,KAAK;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,aAAa;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGZ,aAAa,CAAC,CAAC;EAE/G,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;EAEFC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;IAAEhB,SAAS;IAAEK,eAAe;IAAEC,IAAI,EAAE,CAAC,CAACA;EAAK,CAAC,CAAC;EAEtF,MAAMW,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCV,WAAW,CAACW,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAInB,KAAK,EAAEM,UAAU,CAAC,CAAC;EACzB,CAAC;EAED,MAAMgB,YAAY,GAAG,MAAOL,CAAkB,IAAK;IACjDA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,IAAIhB,cAAc,EAAE;MAClB,IAAI,CAACE,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,QAAQ,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;QAC3D;MACF;MACA,MAAMX,QAAQ,CAAC;QACbS,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BC,IAAI,EAAEJ,QAAQ,CAACI;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACJ,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;QACzC;MACF;MACA,MAAMX,KAAK,CAAC;QACVU,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvBhB,iBAAiB,CAAC,CAACD,cAAc,CAAC;IAClCG,WAAW,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC;IAClDP,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA,IAAIF,eAAe,IAAIC,IAAI,EAAE;IAC3B,oBACET,OAAA;MAAK6B,SAAS,EAAC,mHAAmH;MAAAC,QAAA,eAChI9B,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAK6B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtClC,OAAA;UAAI6B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAK6B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC9B,OAAA;YAAA8B,QAAA,GAAG,aAAW,eAAA9B,OAAA;cAAA8B,QAAA,EAASrB,IAAI,CAACQ;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/ClC,OAAA;YAAG6B,SAAS,EAAC,SAAS;YAAAC,QAAA,GAAC,OAAK,EAACrB,IAAI,CAAC0B,GAAG;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1ClC,OAAA;YAAG6B,SAAS,EAAC,SAAS;YAAAC,QAAA,GAAC,SAAO,EAACrB,IAAI,CAACM,KAAK;UAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNlC,OAAA;UAAK6B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9B,OAAA;YAAK6B,SAAS,EAAC;UAA4E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClGlC,OAAA;YAAG6B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAEtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAK6B,SAAS,EAAC,+EAA+E;IAAAC,QAAA,eAC5F9B,OAAA;MAAK6B,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnC9B,OAAA;QAAK6B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9B,OAAA;UAAI6B,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAG6B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEL9B,KAAK,iBACJJ,OAAA;QAAK6B,SAAS,EAAC,mEAAmE;QAAAC,QAAA,eAChF9B,OAAA;UAAG6B,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAE1B;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CACN,eAEDlC,OAAA;QAAK6B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAExB9B,OAAA;UAAK6B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB9B,OAAA;YAAI6B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACvCnB,cAAc,GAAG,gBAAgB,GAAG;UAAgB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAELlC,OAAA;YAAMoC,QAAQ,EAAEV,YAAa;YAACG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjD9B,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAO6B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlC,OAAA;gBACEqC,IAAI,EAAC,OAAO;gBACZf,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAEV,QAAQ,CAACE,KAAM;gBACtBuB,QAAQ,EAAElB,iBAAkB;gBAC5BS,SAAS,EAAC,kHAAkH;gBAC5HU,WAAW,EAAC,eAAe;gBAC3BC,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELvB,cAAc,iBACbX,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAO6B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlC,OAAA;gBACEqC,IAAI,EAAC,MAAM;gBACXf,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAEV,QAAQ,CAACI,IAAK;gBACrBqB,QAAQ,EAAElB,iBAAkB;gBAC5BS,SAAS,EAAC,kHAAkH;gBAC5HU,WAAW,EAAC,kBAAkB;gBAC9BC,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDlC,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAO6B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlC,OAAA;gBACEqC,IAAI,EAAC,UAAU;gBACff,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEV,QAAQ,CAACG,QAAS;gBACzBsB,QAAQ,EAAElB,iBAAkB;gBAC5BS,SAAS,EAAC,kHAAkH;gBAC5HU,WAAW,EAAC,WAAW;gBACvBC,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlC,OAAA;cACEqC,IAAI,EAAC,QAAQ;cACbI,QAAQ,EAAEtC,SAAU;cACpB0B,SAAS,EAAE,6DACT1B,SAAS,GACL,8CAA8C,GAC9C,0CAA0C,EAC7C;cAAA2B,QAAA,EAEF3B,SAAS,gBACRH,OAAA;gBAAK6B,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C9B,OAAA;kBAAK6B,SAAS,EAAC;gBAAgE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACrFvB,cAAc,GAAG,kBAAkB,GAAG,kBAAkB;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,GAENvB,cAAc,GAAG,eAAe,GAAG;YACpC;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEPlC,OAAA;YAAK6B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B9B,OAAA;cACE0C,OAAO,EAAEd,UAAW;cACpBC,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAEpDnB,cAAc,GACX,2BAA2B,GAC3B;YAA0B;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAExB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC9B,OAAA;YAAI6B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlC,OAAA;YAAG6B,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJlC,OAAA;YACE0C,OAAO,EAAEnC,aAAc;YACvBkC,QAAQ,EAAEtC,SAAU;YACpB0B,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EACjC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENlC,OAAA;UAAK6B,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACnD9B,OAAA;YAAI6B,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAE3D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlC,OAAA;YAAG6B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlC,OAAA;QAAK6B,SAAS,EAAC,0CAA0C;QAAAC,QAAA,eACvD9B,OAAA;UAAA8B,QAAA,EAAG;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CAxNID,eAAyB;EAAA,QACmEH,aAAa;AAAA;AAAA6C,EAAA,GADzG1C,eAAyB;AA0N/B,eAAeA,eAAe;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}