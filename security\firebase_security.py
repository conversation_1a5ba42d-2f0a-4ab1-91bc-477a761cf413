import re
from functools import wraps
from flask import request, abort, g
import firebase_admin
from firebase_admin import auth, firestore

class FirebaseSecurity:
    """
    Security class for Firebase authentication and authorization
    """
    
    def __init__(self, app=None):
        self.app = app
        self.firebase_app = None
        
        # Default role definitions
        self.roles = {
            'admin': {'level': 100, 'description': 'Administrator with full access'},
            'moderator': {'level': 50, 'description': 'Moderator with limited admin access'},
            'user': {'level': 10, 'description': 'Regular authenticated user'},
            'guest': {'level': 1, 'description': 'Unauthenticated guest'}
        }
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize with Flask app"""
        self.app = app
        
        # Try to get the Firebase app
        try:
            self.firebase_app = firebase_admin.get_app()
        except ValueError:
            # Firebase app not initialized yet
            pass
            
        print("✅ Firebase security middleware initialized")
    
    def verify_firebase_token(self, id_token):
        """Verify the Firebase ID token"""
        if not self.firebase_app:
            print("⚠️ Firebase app not initialized")
            return None
            
        try:
            # Verify the ID token
            decoded_token = auth.verify_id_token(id_token, app=self.firebase_app)
            return decoded_token
        except Exception as e:
            print(f"⚠️ Firebase token verification error: {str(e)}")
            return None
    
    def token_required(self, f):
        """Decorator to require a valid Firebase token for a route"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Get token from request
            token = None
            auth_header = request.headers.get('Authorization')
            
            if auth_header:
                # Extract token from "Bearer <token>"
                match = re.match(r'^Bearer\s+(.*)$', auth_header)
                if match:
                    token = match.group(1)
            
            if not token:
                print("⚠️ No token provided")
                abort(401, description="Authentication required")
            
            # Verify the token
            decoded_token = self.verify_firebase_token(token)
            if not decoded_token:
                abort(401, description="Invalid or expired token")
            
            # Store user info in Flask's g object for the request
            g.user = {
                'uid': decoded_token['uid'],
                'email': decoded_token.get('email', ''),
                'claims': decoded_token.get('claims', {})
            }
            
            return f(*args, **kwargs)
        return decorated_function
    
    def role_required(self, min_role):
        """
        Decorator to require a minimum role level for a route
        
        Usage: @firebase_security.role_required('admin')
        """
        def decorator(f):
            @wraps(f)
            @self.token_required
            def decorated_function(*args, **kwargs):
                # Check if user has required role level
                user_role = g.user.get('claims', {}).get('role', 'user')
                user_level = self.roles.get(user_role, {}).get('level', self.roles['user']['level'])
                
                required_level = self.roles.get(min_role, {}).get('level', 100)
                
                if user_level < required_level:
                    print(f"⚠️ User {g.user.get('uid')} with role {user_role} attempted to access {request.path} requiring {min_role}")
                    abort(403, description="Insufficient permissions")
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def set_user_role(self, uid, role):
        """Set a user's role in Firebase Auth custom claims"""
        if not self.firebase_app:
            print("⚠️ Firebase app not initialized")
            return False
            
        if role not in self.roles:
            print(f"⚠️ Invalid role: {role}")
            return False
            
        try:
            # Set custom user claims
            auth.set_custom_user_claims(uid, {'role': role}, app=self.firebase_app)
            print(f"✅ Role '{role}' set for user {uid}")
            return True
        except Exception as e:
            print(f"⚠️ Error setting user role: {str(e)}")
            return False
    
    def get_user_role(self, uid):
        """Get a user's role from Firebase Auth"""
        if not self.firebase_app:
            print("⚠️ Firebase app not initialized")
            return 'user'  # Default to regular user
            
        try:
            # Get the user record
            user = auth.get_user(uid, app=self.firebase_app)
            if user.custom_claims and 'role' in user.custom_claims:
                return user.custom_claims['role']
            return 'user'  # Default role
        except Exception as e:
            print(f"⚠️ Error getting user role: {str(e)}")
            return 'user'  # Default to regular user
    
    def secure_firestore_rules(self):
        """
        Print security rules for Firestore
        
        Note: These rules should be manually copied to the Firebase Console
        """
        rules = """
// Firestore security rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Base rule - deny all by default
    match /{document=**} {
      allow read, write: if false;
    }
    
    // User data - users can read/write only their own data
    match /users/{userId} {
      allow read: if request.auth != null && (request.auth.uid == userId || 
                 request.auth.token.role == 'admin' || 
                 request.auth.token.role == 'moderator');
      allow write: if request.auth != null && (request.auth.uid == userId || 
                  request.auth.token.role == 'admin');
    }
    
    // Skills data - users can read but only admins can write
    match /habilidades/{skillId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.token.role == 'admin';
    }
    
    // Game data - read for all, write for admins
    match /game_data/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.token.role == 'admin';
    }
    
    // Public data - anyone can read, only admins can write
    match /public/{document} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.role == 'admin';
    }
  }
}
        """
        print("===== RECOMMENDED FIRESTORE SECURITY RULES =====")
        print(rules)
        print("=============================================")
        print("Copy these rules to your Firebase Console > Firestore > Rules")
        
        return rules