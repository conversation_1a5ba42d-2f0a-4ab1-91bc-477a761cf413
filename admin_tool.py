#!/usr/bin/env python3
"""
Script utilitário para marcar um usuário como administrador
"""

from game import models
import sys

def marcar_admin(uid):
    """Marcar usuário como admin"""
    try:
        success = models.marcar_usuario_como_admin(uid)
        if success:
            print(f"✅ Usuário {uid} marcado como administrador com sucesso!")
            return True
        else:
            print(f"❌ Erro ao marcar usuário {uid} como administrador")
            return False
    except Exception as e:
        print(f"❌ Erro: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Uso: python admin_tool.py <UID_DO_USUARIO>")
        print("Exemplo: python admin_tool.py abc123def456")
        sys.exit(1)
    
    uid = sys.argv[1]
    print(f"Marcando usuário {uid} como administrador...")
    marcar_admin(uid)
