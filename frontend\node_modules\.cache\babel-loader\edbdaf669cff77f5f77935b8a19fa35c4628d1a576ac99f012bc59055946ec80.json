{"ast": null, "code": "// src/index.ts\nexport * from \"@tanstack/query-core\";\nexport * from \"./types.js\";\nimport { useQueries } from \"./useQueries.js\";\nimport { useQuery } from \"./useQuery.js\";\nimport { useSuspenseQuery } from \"./useSuspenseQuery.js\";\nimport { useSuspenseInfiniteQuery } from \"./useSuspenseInfiniteQuery.js\";\nimport { useSuspenseQueries } from \"./useSuspenseQueries.js\";\nimport { usePrefetchQuery } from \"./usePrefetchQuery.js\";\nimport { usePrefetchInfiniteQuery } from \"./usePrefetchInfiniteQuery.js\";\nimport { queryOptions } from \"./queryOptions.js\";\nimport { infiniteQueryOptions } from \"./infiniteQueryOptions.js\";\nimport { QueryClientContext, QueryClientProvider, useQueryClient } from \"./QueryClientProvider.js\";\nimport { HydrationBoundary } from \"./HydrationBoundary.js\";\nimport { QueryErrorResetBoundary, useQueryErrorResetBoundary } from \"./QueryErrorResetBoundary.js\";\nimport { useIsFetching } from \"./useIsFetching.js\";\nimport { useIsMutating, useMutationState } from \"./useMutationState.js\";\nimport { useMutation } from \"./useMutation.js\";\nimport { mutationOptions } from \"./mutationOptions.js\";\nimport { useInfiniteQuery } from \"./useInfiniteQuery.js\";\nimport { useIsRestoring, IsRestoringProvider } from \"./IsRestoringProvider.js\";\nexport { HydrationBoundary, IsRestoringProvider, QueryClientContext, QueryClientProvider, QueryErrorResetBoundary, infiniteQueryOptions, mutationOptions, queryOptions, useInfiniteQuery, useIsFetching, useIsMutating, useIsRestoring, useMutation, useMutationState, usePrefetchInfiniteQuery, usePrefetchQuery, useQueries, useQuery, useQueryClient, useQueryErrorResetBoundary, useSuspenseInfiniteQuery, useSuspenseQueries, useSuspenseQuery };", "map": {"version": 3, "names": ["useQueries", "useQuery", "useSuspenseQuery", "useSuspenseInfiniteQuery", "useSuspenseQueries", "usePrefetchQuery", "usePrefetchInfiniteQuery", "queryOptions", "infiniteQueryOptions", "QueryClientContext", "QueryClientProvider", "useQueryClient", "HydrationBoundary", "QueryErrorResetBoundary", "useQueryErrorResetBoundary", "useIsFetching", "useIsMutating", "useMutationState", "useMutation", "mutationOptions", "useInfiniteQuery", "useIsRestoring", "IsRestoringProvider"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\react-query\\src\\index.ts"], "sourcesContent": ["/* istanbul ignore file */\n\n// Re-export core\nexport * from '@tanstack/query-core'\n\n// React Query\nexport * from './types'\nexport { useQueries } from './useQueries'\nexport type { QueriesResults, QueriesOptions } from './useQueries'\nexport { useQuery } from './useQuery'\nexport { useSuspenseQuery } from './useSuspenseQuery'\nexport { useSuspenseInfiniteQuery } from './useSuspenseInfiniteQuery'\nexport { useSuspenseQueries } from './useSuspenseQueries'\nexport type {\n  SuspenseQueriesResults,\n  SuspenseQueriesOptions,\n} from './useSuspenseQueries'\nexport { usePrefetchQuery } from './usePrefetchQuery'\nexport { usePrefetchInfiniteQuery } from './usePrefetchInfiniteQuery'\nexport { queryOptions } from './queryOptions'\nexport type {\n  DefinedInitialDataOptions,\n  UndefinedInitialDataOptions,\n  UnusedSkipTokenOptions,\n} from './queryOptions'\nexport { infiniteQueryOptions } from './infiniteQueryOptions'\nexport type {\n  DefinedInitialDataInfiniteOptions,\n  UndefinedInitialDataInfiniteOptions,\n  UnusedSkipTokenInfiniteOptions,\n} from './infiniteQueryOptions'\nexport {\n  QueryClientContext,\n  QueryClientProvider,\n  useQueryClient,\n} from './QueryClientProvider'\nexport type { QueryClientProviderProps } from './QueryClientProvider'\nexport type { QueryErrorResetBoundaryProps } from './QueryErrorResetBoundary'\nexport { HydrationBoundary } from './HydrationBoundary'\nexport type { HydrationBoundaryProps } from './HydrationBoundary'\nexport type {\n  QueryErrorClearResetFunction,\n  QueryErrorIsResetFunction,\n  QueryErrorResetBoundaryFunction,\n  QueryErrorResetFunction,\n} from './QueryErrorResetBoundary'\nexport {\n  QueryErrorResetBoundary,\n  useQueryErrorResetBoundary,\n} from './QueryErrorResetBoundary'\nexport { useIsFetching } from './useIsFetching'\nexport { useIsMutating, useMutationState } from './useMutationState'\nexport { useMutation } from './useMutation'\nexport { mutationOptions } from './mutationOptions'\nexport { useInfiniteQuery } from './useInfiniteQuery'\nexport { useIsRestoring, IsRestoringProvider } from './IsRestoringProvider'\n"], "mappings": ";AAGA,cAAc;AAGd,cAAc;AACd,SAASA,UAAA,QAAkB;AAE3B,SAASC,QAAA,QAAgB;AACzB,SAASC,gBAAA,QAAwB;AACjC,SAASC,wBAAA,QAAgC;AACzC,SAASC,kBAAA,QAA0B;AAKnC,SAASC,gBAAA,QAAwB;AACjC,SAASC,wBAAA,QAAgC;AACzC,SAASC,YAAA,QAAoB;AAM7B,SAASC,oBAAA,QAA4B;AAMrC,SACEC,kBAAA,EACAC,mBAAA,EACAC,cAAA,QACK;AAGP,SAASC,iBAAA,QAAyB;AAQlC,SACEC,uBAAA,EACAC,0BAAA,QACK;AACP,SAASC,aAAA,QAAqB;AAC9B,SAASC,aAAA,EAAeC,gBAAA,QAAwB;AAChD,SAASC,WAAA,QAAmB;AAC5B,SAASC,eAAA,QAAuB;AAChC,SAASC,gBAAA,QAAwB;AACjC,SAASC,cAAA,EAAgBC,mBAAA,QAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}