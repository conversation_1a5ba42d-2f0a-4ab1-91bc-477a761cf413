// Tipos do sistema de jogo

export interface Player {
  uid: string;
  nick: string;
  email: string;
  ip: string;
  level: number;
  xp: number;
  xpToNextLevel: number;
  cash: number;
  createdAt: string;
  lastLogin: string;
}

export interface GameApp {
  id: string;
  name: string;
  description: string;
  icon: string;
  level: number;
  maxLevel: number;
  upgradeCost: number;
  xpReward: number;
  category: 'security' | 'attack' | 'utility';
}

export interface PlayerApps {
  antivirus: number;
  bankguard: number;
  bruteforce: number;
  sdk: number;
  firewall: number;
  malware_kit: number;
  proxyvpn: number;
}

export interface GameStats {
  totalPlayers: number;
  onlinePlayers: number;
  totalHacks: number;
  totalMoney: number;
}

export interface HackTarget {
  uid: string;
  nick: string;
  ip: string;
  level: number;
  cash: number;
  apps: PlayerApps;
  isOnline: boolean;
  lastSeen: string;
}

export interface HackAttempt {
  id: string;
  attackerIp: string;
  targetIp: string;
  type: 'scan' | 'exploit' | 'bruteforce';
  status: 'pending' | 'success' | 'failed';
  timestamp: string;
  reward?: number;
}

export interface GameNotification {
  id: string;
  type: 'hack' | 'upgrade' | 'level' | 'system';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Configurações do jogo
export const GAME_CONFIG = {
  // Sistema de XP
  BASE_XP_PER_LEVEL: 100,
  XP_MULTIPLIER: 1.5,
  
  // Apps iniciais
  INITIAL_APPS: {
    antivirus: 1,
    bankguard: 1,
    bruteforce: 1,
    sdk: 1,
    firewall: 1,
    malware_kit: 1,
    proxyvpn: 1,
  } as PlayerApps,
  
  // Custos de upgrade
  BASE_UPGRADE_COST: 1, // Custo base de $1
  UPGRADE_COST_MULTIPLIER: 1.1, // Aumenta 1.1x a cada upgrade
  
  // XP por upgrade
  BASE_XP_REWARD: 25,
  XP_REWARD_MULTIPLIER: 1.2,
  
  // Limites
  MAX_APP_LEVEL: 10,
  MAX_PLAYER_LEVEL: 100,
  
  // Dinheiro inicial
  INITIAL_CASH: 10, // Reduzido para tornar o jogo mais desafiador
};

// Definições dos aplicativos
export const GAME_APPS: Record<keyof PlayerApps, Omit<GameApp, 'level' | 'upgradeCost' | 'xpReward'>> = {
  antivirus: {
    id: 'antivirus',
    name: 'AntiVirus',
    description: 'Protege contra malware e ataques',
    icon: '🛡️',
    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,
    category: 'security',
  },
  bankguard: {
    id: 'bankguard',
    name: 'BankGuard',
    description: 'Protege transações bancárias',
    icon: '🏦',
    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,
    category: 'security',
  },
  bruteforce: {
    id: 'bruteforce',
    name: 'BruteForce',
    description: 'Quebra senhas e sistemas',
    icon: '🔨',
    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,
    category: 'attack',
  },
  sdk: {
    id: 'sdk',
    name: 'SDK',
    description: 'Kit de desenvolvimento de software',
    icon: '⚙️',
    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,
    category: 'utility',
  },
  firewall: {
    id: 'firewall',
    name: 'Firewall',
    description: 'Bloqueia conexões maliciosas',
    icon: '🔥',
    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,
    category: 'security',
  },
  malware_kit: {
    id: 'malware_kit',
    name: 'Malware Kit',
    description: 'Cria e distribui malware',
    icon: '🦠',
    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,
    category: 'attack',
  },
  proxyvpn: {
    id: 'proxyvpn',
    name: 'ProxyVPN',
    description: 'Oculta identidade e localização',
    icon: '🌐',
    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,
    category: 'utility',
  },
};

// Funções utilitárias
export const calculateXpForLevel = (level: number): number => {
  return Math.floor(GAME_CONFIG.BASE_XP_PER_LEVEL * Math.pow(GAME_CONFIG.XP_MULTIPLIER, level - 1));
};

export const calculateUpgradeCost = (currentLevel: number): number => {
  // Custo para ir do nível atual para o próximo nível
  return Math.ceil(GAME_CONFIG.BASE_UPGRADE_COST * Math.pow(GAME_CONFIG.UPGRADE_COST_MULTIPLIER, currentLevel));
};

export const calculateXpReward = (appLevel: number): number => {
  return Math.floor(GAME_CONFIG.BASE_XP_REWARD * Math.pow(GAME_CONFIG.XP_REWARD_MULTIPLIER, appLevel - 1));
};

export const generateRandomIP = (): string => {
  const segments = [];
  for (let i = 0; i < 4; i++) {
    segments.push(Math.floor(Math.random() * 256));
  }
  return segments.join('.');
};

export const getPlayerLevel = (totalXp: number): number => {
  let level = 1;
  let xpRequired = 0;
  
  while (level <= GAME_CONFIG.MAX_PLAYER_LEVEL) {
    const xpForThisLevel = calculateXpForLevel(level);
    if (totalXp < xpRequired + xpForThisLevel) {
      break;
    }
    xpRequired += xpForThisLevel;
    level++;
  }
  
  return level;
};

export const getXpForNextLevel = (totalXp: number): number => {
  const currentLevel = getPlayerLevel(totalXp);
  let xpRequired = 0;
  
  for (let i = 1; i < currentLevel; i++) {
    xpRequired += calculateXpForLevel(i);
  }
  
  const xpForCurrentLevel = calculateXpForLevel(currentLevel);
  const xpInCurrentLevel = totalXp - xpRequired;
  
  return xpForCurrentLevel - xpInCurrentLevel;
};
