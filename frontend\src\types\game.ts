// Tipos do sistema de jogo

export interface Player {
  uid: string;
  nick: string;
  email: string;
  ip: string;
  level: number;
  xp: number;
  xpToNextLevel: number;
  cash: number;
  shack: number; // Nova moeda para upgrades avançados
  createdAt: string;
  lastLogin: string;
}

export interface GameApp {
  id: string;
  name: string;
  description: string;
  icon: string;
  level: number;
  maxLevel: number;
  upgradeCost: number;
  xpReward: number;
  category: 'security' | 'attack' | 'utility';
}

export interface PlayerApps {
  antivirus: number;
  bankguard: number;
  bruteforce: number;
  sdk: number;
  firewall: number;
  malware_kit: number;
  proxyvpn: number;
  mineradora: number; // Novo app para gerar dinheiro e shack
}

export interface GameStats {
  totalPlayers: number;
  onlinePlayers: number;
  totalHacks: number;
  totalMoney: number;
}

export interface HackTarget {
  uid: string;
  nick: string;
  ip: string;
  level: number;
  cash: number;
  apps: PlayerApps;
  isOnline: boolean;
  lastSeen: string;
}

export interface HackAttempt {
  id: string;
  attackerIp: string;
  targetIp: string;
  type: 'scan' | 'exploit' | 'bruteforce';
  status: 'pending' | 'success' | 'failed';
  timestamp: string;
  reward?: number;
}

export interface GameNotification {
  id: string;
  type: 'hack' | 'upgrade' | 'level' | 'system';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Configurações do jogo
export const GAME_CONFIG = {
  // Sistema de XP
  BASE_XP_PER_LEVEL: 100,
  XP_MULTIPLIER: 1.5,
  
  // Apps iniciais
  INITIAL_APPS: {
    antivirus: 1,
    bankguard: 1,
    bruteforce: 1,
    sdk: 1,
    firewall: 1,
    malware_kit: 1,
    proxyvpn: 1,
    mineradora: 1, // Novo app para gerar dinheiro e shack
  } as PlayerApps,
  
  // Custos de upgrade
  BASE_UPGRADE_COST: 1, // Custo base de $1
  UPGRADE_COST_MULTIPLIER: 1.1, // Aumenta 1.1x a cada upgrade
  
  // XP por upgrade
  BASE_XP_REWARD: 25,
  XP_REWARD_MULTIPLIER: 1.2,
  
  // Limites
  MAX_PLAYER_LEVEL: 100,
  DUAL_CURRENCY_LEVEL: 10, // A partir do nível 10, upgrades custam dinheiro + shack
  
  // Valores iniciais
  INITIAL_CASH: 10, // Dinheiro inicial
  INITIAL_SHACK: 0, // Shack inicial (nova moeda)

  // Configurações da nova moeda SHACK
  BASE_SHACK_COST: 1, // Custo base em shack para upgrades 10+
  SHACK_COST_MULTIPLIER: 1.2, // Multiplicador para custo em shack
};

// Definições dos aplicativos
export const GAME_APPS: Record<keyof PlayerApps, Omit<GameApp, 'level' | 'upgradeCost' | 'xpReward'>> = {
  antivirus: {
    id: 'antivirus',
    name: 'AntiVirus',
    description: 'Protege contra malware e ataques',
    icon: '🛡️',
    maxLevel: Infinity, // Sem limite
    category: 'security',
  },
  bankguard: {
    id: 'bankguard',
    name: 'BankGuard',
    description: 'Protege transações bancárias',
    icon: '🏦',
    maxLevel: Infinity, // Sem limite
    category: 'security',
  },
  bruteforce: {
    id: 'bruteforce',
    name: 'BruteForce',
    description: 'Quebra senhas e sistemas',
    icon: '🔨',
    maxLevel: Infinity, // Sem limite
    category: 'attack',
  },
  sdk: {
    id: 'sdk',
    name: 'SDK',
    description: 'Kit de desenvolvimento de software',
    icon: '⚙️',
    maxLevel: Infinity, // Sem limite
    category: 'utility',
  },
  firewall: {
    id: 'firewall',
    name: 'Firewall',
    description: 'Bloqueia conexões maliciosas',
    icon: '🔥',
    maxLevel: Infinity, // Sem limite
    category: 'security',
  },
  malware_kit: {
    id: 'malware_kit',
    name: 'Malware Kit',
    description: 'Cria e distribui malware',
    icon: '🦠',
    maxLevel: Infinity, // Sem limite
    category: 'attack',
  },
  proxyvpn: {
    id: 'proxyvpn',
    name: 'ProxyVPN',
    description: 'Oculta identidade e localização',
    icon: '🌐',
    maxLevel: Infinity, // Sem limite
    category: 'utility',
  },
  mineradora: {
    id: 'mineradora',
    name: 'Mineradora',
    description: 'Gera dinheiro e shack automaticamente',
    icon: '⛏️',
    maxLevel: Infinity, // Sem limite
    category: 'utility',
  },
};

// Funções utilitárias
export const calculateXpForLevel = (level: number): number => {
  return Math.floor(GAME_CONFIG.BASE_XP_PER_LEVEL * Math.pow(GAME_CONFIG.XP_MULTIPLIER, level - 1));
};

export const calculateUpgradeCost = (currentLevel: number): { cash: number; shack: number } => {
  // Custo em dinheiro (sempre presente)
  const cashCost = Math.ceil(GAME_CONFIG.BASE_UPGRADE_COST * Math.pow(GAME_CONFIG.UPGRADE_COST_MULTIPLIER, currentLevel));

  // Custo em shack (apenas a partir do nível 10)
  const shackCost = currentLevel >= GAME_CONFIG.DUAL_CURRENCY_LEVEL
    ? Math.ceil(GAME_CONFIG.BASE_SHACK_COST * Math.pow(GAME_CONFIG.SHACK_COST_MULTIPLIER, currentLevel - GAME_CONFIG.DUAL_CURRENCY_LEVEL))
    : 0;

  return { cash: cashCost, shack: shackCost };
};

export const calculateXpReward = (appLevel: number): number => {
  return Math.floor(GAME_CONFIG.BASE_XP_REWARD * Math.pow(GAME_CONFIG.XP_REWARD_MULTIPLIER, appLevel - 1));
};

// Funções para a Mineradora
export const calculateMineradoraCashGeneration = (level: number): number => {
  // Gera dinheiro por hora baseado no nível
  return Math.floor(level * 0.5); // $0.5 por hora por nível
};

export const calculateMineradoraShackGeneration = (level: number): number => {
  // Gera shack por hora baseado no nível (apenas a partir do nível 5)
  return level >= 5 ? Math.floor((level - 4) * 0.1) : 0; // 0.1 shack por hora por nível acima de 4
};

export const calculateMineradoraProduction = (level: number, hoursOffline: number = 1): { cash: number; shack: number } => {
  const cashPerHour = calculateMineradoraCashGeneration(level);
  const shackPerHour = calculateMineradoraShackGeneration(level);

  return {
    cash: Math.floor(cashPerHour * hoursOffline),
    shack: Math.floor(shackPerHour * hoursOffline),
  };
};

export const generateRandomIP = (): string => {
  const segments = [];
  for (let i = 0; i < 4; i++) {
    segments.push(Math.floor(Math.random() * 256));
  }
  return segments.join('.');
};

export const getPlayerLevel = (totalXp: number): number => {
  let level = 1;
  let xpRequired = 0;
  
  while (level <= GAME_CONFIG.MAX_PLAYER_LEVEL) {
    const xpForThisLevel = calculateXpForLevel(level);
    if (totalXp < xpRequired + xpForThisLevel) {
      break;
    }
    xpRequired += xpForThisLevel;
    level++;
  }
  
  return level;
};

export const getXpForNextLevel = (totalXp: number): number => {
  const currentLevel = getPlayerLevel(totalXp);
  let xpRequired = 0;
  
  for (let i = 1; i < currentLevel; i++) {
    xpRequired += calculateXpForLevel(i);
  }
  
  const xpForCurrentLevel = calculateXpForLevel(currentLevel);
  const xpInCurrentLevel = totalXp - xpRequired;
  
  return xpForCurrentLevel - xpInCurrentLevel;
};
