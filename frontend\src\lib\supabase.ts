import { createClient } from '@supabase/supabase-js';

// Configurações do Supabase
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Tipos para o banco de dados
export interface Player {
  id: string;
  nick: string;
  email: string;
  ip: string;
  level: number;
  xp: number;
  cash: number;
  created_at: string;
  updated_at: string;
  last_login: string;
}

export interface PlayerApps {
  id: string;
  player_id: string;
  antivirus: number;
  bankguard: number;
  bruteforce: number;
  sdk: number;
  firewall: number;
  malwarekit: number;
  proxyvpn: number;
  updated_at: string;
}

export interface GameNotification {
  id: string;
  player_id: string;
  type: 'system' | 'upgrade' | 'level' | 'hack' | 'transfer';
  title: string;
  message: string;
  read: boolean;
  created_at: string;
}

export interface HackTarget {
  id: string;
  player_id: string;
  target_ip: string;
  target_nick: string;
  target_level: number;
  target_cash: number;
  firewall_level: number;
  last_seen: string;
}

export interface HackLog {
  id: string;
  attacker_id: string;
  target_id: string;
  success: boolean;
  amount_stolen: number;
  timestamp: string;
}

export interface ChatMessage {
  id: string;
  player_id: string;
  player_nick: string;
  message: string;
  created_at: string;
}

// Funções utilitárias para o Supabase
export const supabaseUtils = {
  // Verificar conexão
  async testConnection() {
    try {
      const { data, error } = await supabase.from('players').select('count').limit(1);
      return { success: !error, data, error };
    } catch (error) {
      return { success: false, error };
    }
  },

  // Gerar IP aleatório único
  generateUniqueIP: async (): Promise<string> => {
    let attempts = 0;
    const maxAttempts = 100;

    while (attempts < maxAttempts) {
      const ip = `${Math.floor(Math.random() * 255) + 1}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
      
      const { data } = await supabase
        .from('players')
        .select('id')
        .eq('ip', ip)
        .limit(1);

      if (!data || data.length === 0) {
        return ip;
      }
      
      attempts++;
    }

    // Fallback se não conseguir gerar IP único
    return `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
  },

  // Verificar se nick já existe
  async checkNickExists(nick: string): Promise<boolean> {
    const { data } = await supabase
      .from('players')
      .select('id')
      .eq('nick', nick)
      .limit(1);

    return data ? data.length > 0 : false;
  },

  // Verificar se email já existe
  async checkEmailExists(email: string): Promise<boolean> {
    const { data } = await supabase
      .from('players')
      .select('id')
      .eq('email', email)
      .limit(1);

    return data ? data.length > 0 : false;
  }
};
