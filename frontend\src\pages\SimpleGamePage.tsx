import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { useSimpleAuth } from '../stores/simpleAuthStore';
import { usePlayer } from '../stores/playerStore';
import { useChat } from '../stores/chatStore';
import gameApi from '../services/gameApi';
import GameMainPage from './GameMainPage';

// Componente do Dashboard Simplificado
const SimpleDashboard: React.FC = () => {
  const { user } = useSimpleAuth();
  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();

  useEffect(() => {
    if (!hasPlayerData && !isLoadingPlayer) {
      console.log('SimpleDashboard - Carregando dados do jogador...');
      loadPlayerData();
    }
  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);

  // Usar dados do player se disponível, senão usar dados mockados
  const playerData = currentPlayer || {
    pontos: 1250,
    nivel: 15,
    conquistas: 42,
    ranking: 7
  };

  return (
    <div className="p-4">
      {/* Header compacto */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold cyber-text">
              Terminal Principal
            </h1>
            <p className="text-sm text-text-muted">
              Operador: <span className="text-cyber-primary">{user?.nick || 'Jogador'}</span>
            </p>
          </div>
          {isLoadingPlayer && (
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary"></div>
          )}
        </div>
      </div>

      {/* Stats do Jogador - Layout Celular */}
      <div className="space-y-4">
        <div className="cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary">
            <h2 className="text-lg font-semibold mb-4 cyber-text">📊 Status do Sistema</h2>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-bg-tertiary rounded-lg p-3 border border-blue-500">
                <div className="text-center">
                  <div className="text-xl font-bold text-blue-400">
                    {isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()}
                  </div>
                  <div className="text-xs text-blue-300">PONTOS</div>
                </div>
              </div>
              <div className="bg-bg-tertiary rounded-lg p-3 border border-green-500">
                <div className="text-center">
                  <div className="text-xl font-bold text-green-400">
                    {isLoadingPlayer ? '...' : playerData.nivel}
                  </div>
                  <div className="text-xs text-green-300">NÍVEL</div>
                </div>
              </div>
              <div className="bg-bg-tertiary rounded-lg p-3 border border-purple-500">
                <div className="text-center">
                  <div className="text-xl font-bold text-purple-400">
                    {isLoadingPlayer ? '...' : playerData.conquistas}
                  </div>
                  <div className="text-xs text-purple-300">CONQUISTAS</div>
                </div>
              </div>
              <div className="bg-bg-tertiary rounded-lg p-3 border border-orange-500">
                <div className="text-center">
                  <div className="text-xl font-bold text-orange-400">
                    {isLoadingPlayer ? '...' : playerData.ranking}
                  </div>
                  <div className="text-xs text-orange-300">RANKING</div>
                </div>
              </div>
            </div>
        </div>

        {/* Ações Rápidas - Estilo Celular */}
        <div className="cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary">
            <h2 className="text-lg font-semibold mb-4 cyber-text">⚡ Acesso Rápido</h2>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => window.location.href = '/game/scanner'}
                className="btn-cyber text-sm py-3"
              >
                <div className="text-lg mb-1">🔍</div>
                <div>Scanner</div>
              </button>
              <button
                onClick={() => window.location.href = '/game/chat'}
                className="btn-cyber text-sm py-3"
              >
                <div className="text-lg mb-1">💬</div>
                <div>Chat</div>
              </button>
              <button className="btn-cyber text-sm py-3">
                <div className="text-lg mb-1">🏆</div>
                <div>Loja</div>
              </button>
              <button className="btn-cyber text-sm py-3">
                <div className="text-lg mb-1">⚙️</div>
                <div>Config</div>
              </button>
            </div>
        </div>

        {/* Log de Atividades - Estilo Terminal */}
        <div className="cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary">
            <h2 className="text-lg font-semibold mb-4 cyber-text">📈 Log do Sistema</h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3">
                    <span className="text-white font-bold text-sm">+</span>
                  </div>
                  <div>
                    <div className="font-medium text-sm">Pontos ganhos</div>
                    <div className="text-xs text-text-muted">02:15:33</div>
                  </div>
                </div>
                <div className="text-green-400 font-bold text-sm">+150</div>
              </div>

              <div className="flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3">
                    <span className="text-white font-bold text-sm">🏆</span>
                  </div>
                  <div>
                    <div className="font-medium text-sm">Nova conquista</div>
                    <div className="text-xs text-text-muted">01:22:15</div>
                  </div>
                </div>
                <div className="text-blue-400 font-bold text-sm">HACK</div>
              </div>

              <div className="flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3">
                    <span className="text-white font-bold text-sm">↑</span>
                  </div>
                  <div>
                    <div className="font-medium text-sm">Level UP</div>
                    <div className="text-xs text-text-muted">00:45:22</div>
                  </div>
                </div>
                <div className="text-purple-400 font-bold text-sm">LV.15</div>
              </div>
            </div>
        </div>
      </div>
    </div>
  );
};

// Componente Scanner Completo
const SimpleScanner: React.FC = () => {
  const { user } = useSimpleAuth();
  const { currentPlayer } = usePlayer();
  const [isScanning, setIsScanning] = React.useState(false);
  const [scanTargets, setScanTargets] = React.useState<any[]>([]);
  const [scanError, setScanError] = React.useState<string | null>(null);
  const [specificIP, setSpecificIP] = React.useState('');
  const [isAdvancedScan, setIsAdvancedScan] = React.useState(false);
  const [connectionStatus, setConnectionStatus] = React.useState<string>('Não testado');
  const [loginStatus, setLoginStatus] = React.useState<string>('Não logado');

  const handleQuickScan = async () => {
    setIsScanning(true);
    setScanError(null);

    try {
      console.log('🔍 Iniciando scan rápido...');
      const data = await gameApi.scanTargets();

      if (data.sucesso && data.alvos) {
        setScanTargets(data.alvos);
        console.log('✅ Scan realizado com sucesso:', data.alvos);
      } else {
        setScanError(data.mensagem || 'Erro ao escanear alvos');
        console.log('❌ Erro no scan:', data.mensagem);
      }
    } catch (error: any) {
      console.error('❌ Erro no scan:', error);
      setScanError(error.message || 'Erro de conexão com o servidor');
    } finally {
      setIsScanning(false);
    }
  };

  const handleAdvancedScan = async () => {
    if (!specificIP.trim()) {
      setScanError('Digite um IP válido');
      return;
    }

    setIsAdvancedScan(true);
    setScanError(null);

    try {
      console.log(`🎯 Iniciando scan avançado para IP: ${specificIP}`);
      const data = await gameApi.scanSpecificIP(specificIP);

      if (data.sucesso && data.alvo) {
        setScanTargets([data.alvo]);
        console.log('✅ Scan avançado realizado:', data.alvo);
      } else {
        setScanError(data.mensagem || 'IP não encontrado');
        console.log('❌ IP não encontrado:', data.mensagem);
      }
    } catch (error: any) {
      console.error('❌ Erro no scan avançado:', error);
      setScanError(error.message || 'Erro de conexão com o servidor');
    } finally {
      setIsAdvancedScan(false);
    }
  };

  const handleExploit = async (target: any) => {
    try {
      console.log(`⚔️ Iniciando exploit contra: ${target.nick} (${target.ip})`);
      const data = await gameApi.exploitTarget(target.ip);

      if (data.sucesso) {
        alert(`✅ Exploit bem-sucedido! ${data.mensagem}`);
        console.log('✅ Exploit realizado com sucesso:', data);
        // Recarregar dados do jogador se necessário
      } else {
        alert(`❌ Exploit falhou: ${data.mensagem}`);
        console.log('❌ Exploit falhou:', data.mensagem);
      }
    } catch (error: any) {
      console.error('❌ Erro no exploit:', error);
      alert(`❌ Erro no exploit: ${error.message}`);
    }
  };

  const testConnection = async () => {
    try {
      setConnectionStatus('Testando...');
      const result = await gameApi.testConnection();

      if (result.success) {
        setConnectionStatus('✅ Conectado');
        console.log('✅ Conexão com backend OK:', result.data);
      } else {
        setConnectionStatus('❌ Erro de conexão');
        console.log('❌ Erro de conexão:', result.error);
      }
    } catch (error) {
      setConnectionStatus('❌ Falha na conexão');
      console.error('❌ Falha no teste de conexão:', error);
    }
  };

  const quickLogin = async () => {
    try {
      setLoginStatus('Fazendo login...');
      const result = await gameApi.quickLogin();

      if (result.sucesso) {
        setLoginStatus('✅ Logado');
        console.log('✅ Login realizado:', result);
      } else {
        setLoginStatus('❌ Erro no login');
        console.log('❌ Erro no login:', result);
      }
    } catch (error) {
      setLoginStatus('❌ Falha no login');
      console.error('❌ Falha no login:', error);
    }
  };

  const playerCPU = currentPlayer?.cpu || 1;

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* Header com informações do scanner */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => window.history.back()}
              className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600"
            >
              <span className="text-lg">←</span>
            </button>
            <div>
              <h1 className="text-lg font-bold">🔍 Scanner</h1>
              <p className="text-xs text-gray-400">Sistema de Reconhecimento</p>
            </div>
          </div>
          <div className="text-right">
            <div className="space-y-1">
              <button
                onClick={testConnection}
                className="text-xs bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded block w-full"
              >
                Testar
              </button>
              <button
                onClick={quickLogin}
                className="text-xs bg-green-600 hover:bg-green-700 px-2 py-1 rounded block w-full"
              >
                Login
              </button>
              <p className="text-xs">{connectionStatus}</p>
              <p className="text-xs">{loginStatus}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {/* Scan Rápido */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold mb-3 text-white">⚡ Scan Rápido</h3>
          <p className="text-sm text-gray-400 mb-4">Encontra alvos aleatórios na rede</p>
          <button
            onClick={handleQuickScan}
            disabled={isScanning}
            className="w-full py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold"
          >
            {isScanning ? 'Escaneando...' : 'Iniciar Scan Rápido'}
          </button>
        </div>

        {/* Scan Avançado */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold mb-3 text-white">🎯 Scan Avançado</h3>
          <p className="text-sm text-gray-400 mb-4">Busca por IP específico</p>
          <div className="flex space-x-2">
            <input
              type="text"
              value={specificIP}
              onChange={(e) => setSpecificIP(e.target.value)}
              placeholder="*************"
              className="flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white font-mono text-sm"
            />
            <button
              onClick={handleAdvancedScan}
              disabled={isAdvancedScan}
              className="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold"
            >
              {isAdvancedScan ? '...' : 'Scan'}
            </button>
          </div>
        </div>

        {/* Erro */}
        {scanError && (
          <div className="bg-red-900 border border-red-500 rounded-lg p-3">
            <p className="text-red-300 text-sm font-mono">❌ {scanError}</p>
          </div>
        )}

        {/* Resultados */}
        {scanTargets.length > 0 && (
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
            <h3 className="text-lg font-semibold mb-4 text-white">📊 Alvos Encontrados</h3>
            <div className="space-y-3">
              {scanTargets.map((target, index) => {
                const canExploit = playerCPU > (target.firewall || 1);

                return (
                  <div key={index} className="bg-gray-700 rounded-lg p-4 border border-gray-600">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-bold text-blue-400">{target.nick}</h4>
                        <p className="text-xs text-gray-400 font-mono">IP: {target.ip}</p>
                        <p className="text-xs text-gray-400">Nível: {target.nivel}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-xs text-gray-400">Firewall: {target.firewall || 1}</p>
                        <p className="text-xs text-gray-400">Dinheiro: ${(target.dinheiro || 0).toLocaleString()}</p>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div className="text-xs">
                        <span className={`px-2 py-1 rounded ${
                          canExploit ? 'bg-green-600 text-green-100' : 'bg-red-600 text-red-100'
                        }`}>
                          {canExploit ? 'VULNERÁVEL' : 'PROTEGIDO'}
                        </span>
                      </div>

                      <button
                        onClick={() => handleExploit(target)}
                        disabled={!canExploit}
                        className={`px-4 py-2 rounded font-semibold text-sm ${
                          canExploit
                            ? 'bg-green-600 hover:bg-green-700 text-white'
                            : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        {canExploit ? 'EXPLOITAR' : 'CPU INSUFICIENTE'}
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Info do Jogador */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-sm font-semibold mb-2 text-white">💻 Seu Sistema</h3>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>CPU: <span className="text-blue-400">{playerCPU}</span></div>
            <div>Status: <span className="text-green-400">ONLINE</span></div>
          </div>
        </div>
      </div>

      {/* Footer com botão de voltar */}
      <div className="bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0">
        <div className="flex justify-center">
          <button
            onClick={() => window.history.back()}
            className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
          >
            <span className="text-lg">←</span>
            <span className="text-sm">Voltar</span>
          </button>
        </div>
      </div>
    </div>
  );
};

const SimpleChat: React.FC = () => {
  const { messages, isLoading: isLoadingMessages, loadMessages, sendMessage, isSending } = useChat();
  const [newMessage, setNewMessage] = React.useState('');

  useEffect(() => {
    if (messages.length === 0 && !isLoadingMessages) {
      console.log('SimpleChat - Carregando mensagens...');
      loadMessages();
    }
  }, [messages.length, isLoadingMessages, loadMessages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newMessage.trim() && !isSending) {
      await sendMessage(newMessage.trim());
      setNewMessage('');
    }
  };

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* Header do chat */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => window.history.back()}
            className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600"
          >
            <span className="text-lg">←</span>
          </button>
          <div>
            <h1 className="text-lg font-bold">💬 Chat Global</h1>
            <p className="text-xs text-gray-400">Canal Global - Criptografado</p>
          </div>
        </div>
      </div>

      {/* Área de mensagens */}
      <div className="flex-1 p-4 overflow-hidden">
        <div className="bg-black rounded-lg p-3 h-full overflow-y-auto mb-4 border border-gray-600">
          {isLoadingMessages ? (
            <div className="text-center text-blue-400">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400 mx-auto mb-2"></div>
              <span className="text-xs font-mono">CARREGANDO DADOS...</span>
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center text-blue-400">
              <div className="text-2xl mb-2">💬</div>
              <p className="text-xs font-mono">CANAL VAZIO - AGUARDANDO TRANSMISSÃO</p>
            </div>
          ) : (
            <div className="space-y-2">
              {messages.map((message) => (
                <div key={message.id} className="border-l-2 border-blue-400 pl-3 py-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-blue-400 font-mono text-xs">
                      [{new Date(message.timestamp).toLocaleTimeString()}]
                    </span>
                    <span className="text-green-400 font-mono text-xs font-bold">
                      {message.usuario}:
                    </span>
                  </div>
                  <p className="text-white font-mono text-sm pl-2">{message.mensagem}</p>
                </div>
              ))}
            </div>
          )}
        </div>

      </div>

      {/* Formulário de envio */}
      <div className="bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0">
        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="> Digite sua mensagem..."
            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded font-mono text-white text-sm focus:outline-none focus:border-blue-400 placeholder-gray-400"
            disabled={isSending}
          />
          <button
            type="submit"
            disabled={!newMessage.trim() || isSending}
            className={`px-4 py-2 rounded font-mono text-sm transition-all ${
              !newMessage.trim() || isSending
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700 font-bold'
            }`}
          >
            {isSending ? '...' : 'ENVIAR'}
          </button>
        </form>
      </div>
    </div>
  );
};

// Navegação Estilo Celular/Jogo
const SimpleNavigation: React.FC = () => {
  const currentPath = window.location.pathname;

  return (
    <div className="bg-bg-primary">
      {/* Header estilo celular */}
      <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center">
              <span className="text-black font-bold text-lg">S</span>
            </div>
            <div>
              <h1 className="text-xl font-bold cyber-text">SHACK</h1>
              <p className="text-xs text-text-muted">Web Terminal</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="status-online"></div>
            <span className="text-xs text-green-400">ONLINE</span>
          </div>
        </div>
      </div>

      {/* Menu de navegação estilo celular */}
      <div className="bg-bg-secondary border-b border-cyber-primary">
        <div className="flex">
          <a
            href="/game"
            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${
              currentPath === '/game' || currentPath === '/game/'
                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'
                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'
            }`}
          >
            <div className="text-lg mb-1">🏠</div>
            <div>Dashboard</div>
          </a>
          <a
            href="/game/scanner"
            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${
              currentPath.includes('/scanner')
                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'
                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'
            }`}
          >
            <div className="text-lg mb-1">🔍</div>
            <div>Scanner</div>
          </a>
          <a
            href="/game/chat"
            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${
              currentPath.includes('/chat')
                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'
                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'
            }`}
          >
            <div className="text-lg mb-1">💬</div>
            <div>Chat</div>
          </a>
        </div>
      </div>
    </div>
  );
};

// Página Principal do Jogo Simplificada
const SimpleGamePage: React.FC = () => {
  console.log('SimpleGamePage - Renderizando...');

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Routes>
        <Route path="/" element={<GameMainPage />} />
        <Route path="/scanner" element={<SimpleScanner />} />
        <Route path="/chat" element={<SimpleChat />} />
        <Route path="*" element={<GameMainPage />} />
      </Routes>
    </div>
  );
};

export default SimpleGamePage;
