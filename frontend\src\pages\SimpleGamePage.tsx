import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { useSimpleAuth } from '../stores/simpleAuthStore';
import { usePlayer } from '../stores/playerStore';
import { useChat } from '../stores/chatStore';

// Componente do Dashboard Simplificado
const SimpleDashboard: React.FC = () => {
  const { user } = useSimpleAuth();
  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();

  useEffect(() => {
    if (!hasPlayerData && !isLoadingPlayer) {
      console.log('SimpleDashboard - Carregando dados do jogador...');
      loadPlayerData();
    }
  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);

  // Usar dados do player se disponível, senão usar dados mockados
  const playerData = currentPlayer || {
    pontos: 1250,
    nivel: 15,
    conquistas: 42,
    ranking: 7
  };

  return (
    <div className="p-4">
      {/* Header compacto */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold cyber-text">
              Terminal Principal
            </h1>
            <p className="text-sm text-text-muted">
              Operador: <span className="text-cyber-primary">{user?.nick || 'Jogador'}</span>
            </p>
          </div>
          {isLoadingPlayer && (
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary"></div>
          )}
        </div>
      </div>

        {/* Stats do Jogador - Layout Celular */}
        <div className="space-y-4">
          <div className="cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary">
            <h2 className="text-lg font-semibold mb-4 cyber-text">📊 Status do Sistema</h2>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-bg-tertiary rounded-lg p-3 border border-blue-500">
                <div className="text-center">
                  <div className="text-xl font-bold text-blue-400">
                    {isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()}
                  </div>
                  <div className="text-xs text-blue-300">PONTOS</div>
                </div>
              </div>
              <div className="bg-bg-tertiary rounded-lg p-3 border border-green-500">
                <div className="text-center">
                  <div className="text-xl font-bold text-green-400">
                    {isLoadingPlayer ? '...' : playerData.nivel}
                  </div>
                  <div className="text-xs text-green-300">NÍVEL</div>
                </div>
              </div>
              <div className="bg-bg-tertiary rounded-lg p-3 border border-purple-500">
                <div className="text-center">
                  <div className="text-xl font-bold text-purple-400">
                    {isLoadingPlayer ? '...' : playerData.conquistas}
                  </div>
                  <div className="text-xs text-purple-300">CONQUISTAS</div>
                </div>
              </div>
              <div className="bg-bg-tertiary rounded-lg p-3 border border-orange-500">
                <div className="text-center">
                  <div className="text-xl font-bold text-orange-400">
                    {isLoadingPlayer ? '...' : playerData.ranking}
                  </div>
                  <div className="text-xs text-orange-300">RANKING</div>
                </div>
              </div>
            </div>
          </div>

          {/* Ações Rápidas - Estilo Celular */}
          <div className="cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary">
            <h2 className="text-lg font-semibold mb-4 cyber-text">⚡ Acesso Rápido</h2>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => window.location.href = '/game/scanner'}
                className="btn-cyber text-sm py-3"
              >
                <div className="text-lg mb-1">🔍</div>
                Scanner
              </button>
              <button
                onClick={() => window.location.href = '/game/chat'}
                className="btn-cyber text-sm py-3"
              >
                <div className="text-lg mb-1">💬</div>
                Chat
              </button>
              <button className="btn-cyber text-sm py-3">
                <div className="text-lg mb-1">🏆</div>
                Loja
              </button>
              <button className="btn-cyber text-sm py-3">
                <div className="text-lg mb-1">⚙️</div>
                Config
              </button>
            </div>
          </div>

          {/* Log de Atividades - Estilo Terminal */}
          <div className="cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary">
            <h2 className="text-lg font-semibold mb-4 cyber-text">📈 Log do Sistema</h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3">
                    <span className="text-white font-bold text-sm">+</span>
                  </div>
                  <div>
                    <div className="font-medium text-sm">Pontos ganhos</div>
                    <div className="text-xs text-text-muted">02:15:33</div>
                  </div>
                </div>
                <div className="text-green-400 font-bold text-sm">+150</div>
              </div>

              <div className="flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3">
                    <span className="text-white font-bold text-sm">🏆</span>
                  </div>
                  <div>
                    <div className="font-medium text-sm">Nova conquista</div>
                    <div className="text-xs text-text-muted">01:22:15</div>
                  </div>
                </div>
                <div className="text-blue-400 font-bold text-sm">HACK</div>
              </div>

              <div className="flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3">
                    <span className="text-white font-bold text-sm">↑</span>
                  </div>
                  <div>
                    <div className="font-medium text-sm">Level UP</div>
                    <div className="text-xs text-text-muted">00:45:22</div>
                  </div>
                </div>
                <div className="text-purple-400 font-bold text-sm">LV.15</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Componentes de placeholder para outras páginas
const SimpleScanner: React.FC = () => (
  <div className="p-8">
    <div className="max-w-4xl mx-auto">
      <div className="card text-center">
        <h1 className="text-3xl font-bold mb-4">🔍 Scanner</h1>
        <p className="text-text-muted mb-6">Funcionalidade do scanner será implementada aqui.</p>
        <div className="bg-bg-tertiary p-8 rounded-lg">
          <div className="text-6xl mb-4">🚧</div>
          <p>Em desenvolvimento...</p>
        </div>
      </div>
    </div>
  </div>
);

const SimpleChat: React.FC = () => {
  const { messages, isLoading: isLoadingMessages, loadMessages, sendMessage, isSending } = useChat();
  const [newMessage, setNewMessage] = React.useState('');

  useEffect(() => {
    if (messages.length === 0 && !isLoadingMessages) {
      console.log('SimpleChat - Carregando mensagens...');
      loadMessages();
    }
  }, [messages.length, isLoadingMessages, loadMessages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newMessage.trim() && !isSending) {
      await sendMessage(newMessage.trim());
      setNewMessage('');
    }
  };

  return (
    <div className="p-4">
      <div className="cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary">
        <div className="p-4 border-b border-cyber-primary">
          <h1 className="text-xl font-bold cyber-text">💬 Terminal de Chat</h1>
          <p className="text-xs text-text-muted">Canal Global - Criptografado</p>
        </div>

        {/* Área de mensagens - Estilo Terminal */}
        <div className="p-4">
          <div className="bg-black rounded-lg p-3 h-80 overflow-y-auto mb-4 border border-cyber-primary">
            {isLoadingMessages ? (
              <div className="text-center text-cyber-primary">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary mx-auto mb-2"></div>
                <span className="text-xs font-mono">CARREGANDO DADOS...</span>
              </div>
            ) : messages.length === 0 ? (
              <div className="text-center text-cyber-primary">
                <div className="text-2xl mb-2">💬</div>
                <p className="text-xs font-mono">CANAL VAZIO - AGUARDANDO TRANSMISSÃO</p>
              </div>
            ) : (
              <div className="space-y-2">
                {messages.map((message) => (
                  <div key={message.id} className="border-l-2 border-cyber-primary pl-3 py-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-cyber-primary font-mono text-xs">
                        [{new Date(message.timestamp).toLocaleTimeString()}]
                      </span>
                      <span className="text-blue-400 font-mono text-xs font-bold">
                        {message.usuario}:
                      </span>
                    </div>
                    <p className="text-white font-mono text-sm pl-2">{message.mensagem}</p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Formulário de envio - Estilo Terminal */}
          <form onSubmit={handleSendMessage} className="flex space-x-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="> Digite comando..."
              className="flex-1 px-3 py-2 bg-black border border-cyber-primary rounded font-mono text-cyber-primary text-sm focus:outline-none focus:border-cyber-secondary placeholder-gray-500"
              disabled={isSending}
            />
            <button
              type="submit"
              disabled={!newMessage.trim() || isSending}
              className={`px-4 py-2 rounded font-mono text-sm transition-all ${
                !newMessage.trim() || isSending
                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                  : 'bg-cyber-primary text-black hover:bg-cyber-secondary font-bold'
              }`}
            >
              {isSending ? '...' : 'SEND'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

// Navegação Estilo Celular/Jogo
const SimpleNavigation: React.FC = () => {
  const currentPath = window.location.pathname;

  return (
    <div className="bg-bg-primary">
      {/* Header estilo celular */}
      <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center">
              <span className="text-black font-bold text-lg">S</span>
            </div>
            <div>
              <h1 className="text-xl font-bold cyber-text">SHACK</h1>
              <p className="text-xs text-text-muted">Web Terminal</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="status-online"></div>
            <span className="text-xs text-green-400">ONLINE</span>
          </div>
        </div>
      </div>

      {/* Menu de navegação estilo celular */}
      <div className="bg-bg-secondary border-b border-cyber-primary">
        <div className="flex">
          <a
            href="/game"
            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${
              currentPath === '/game' || currentPath === '/game/'
                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'
                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'
            }`}
          >
            <div className="text-lg mb-1">🏠</div>
            Dashboard
          </a>
          <a
            href="/game/scanner"
            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${
              currentPath.includes('/scanner')
                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'
                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'
            }`}
          >
            <div className="text-lg mb-1">🔍</div>
            Scanner
          </a>
          <a
            href="/game/chat"
            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${
              currentPath.includes('/chat')
                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'
                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'
            }`}
          >
            <div className="text-lg mb-1">💬</div>
            Chat
          </a>
        </div>
      </div>
    </div>
  );
};

// Página Principal do Jogo Simplificada
const SimpleGamePage: React.FC = () => {
  console.log('SimpleGamePage - Renderizando...');

  return (
    <div className="min-h-screen bg-bg-primary text-text-primary">
      {/* Container estilo celular */}
      <div className="max-w-md mx-auto bg-bg-primary min-h-screen relative">
        {/* Simulação de tela de celular */}
        <div className="bg-gradient-to-b from-bg-primary to-bg-secondary min-h-screen">
          <SimpleNavigation />

          {/* Conteúdo principal */}
          <div className="pb-4">
            <Routes>
              <Route path="/" element={<SimpleDashboard />} />
              <Route path="/scanner" element={<SimpleScanner />} />
              <Route path="/chat" element={<SimpleChat />} />
              <Route path="*" element={<SimpleDashboard />} />
            </Routes>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleGamePage;
