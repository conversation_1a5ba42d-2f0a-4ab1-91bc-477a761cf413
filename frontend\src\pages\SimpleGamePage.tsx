import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { useSimpleAuth } from '../stores/simpleAuthStore';
import { usePlayer } from '../stores/playerStore';
import { useChat } from '../stores/chatStore';

// Componente do Dashboard Simplificado
const SimpleDashboard: React.FC = () => {
  const { user } = useSimpleAuth();
  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();

  useEffect(() => {
    if (!hasPlayerData && !isLoadingPlayer) {
      console.log('SimpleDashboard - Carregando dados do jogador...');
      loadPlayerData();
    }
  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);

  // Usar dados do player se disponível, senão usar dados mockados
  const playerData = currentPlayer || {
    pontos: 1250,
    nivel: 15,
    conquistas: 42,
    ranking: 7
  };

  return (
    <div className="p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-2">
            🎮 Dashboard do Jogo
          </h1>
          <p className="text-text-muted">
            Bem-vindo de volta, <strong>{user?.nick || 'Jogador'}</strong>!
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Stats do Jogador */}
          <div className="lg:col-span-2">
            <div className="card">
              <h2 className="text-2xl font-semibold mb-6">📊 Estatísticas</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="card bg-blue-900 border-blue-500">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-100">
                      {isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()}
                    </div>
                    <div className="text-sm text-blue-300">Pontos</div>
                  </div>
                </div>
                <div className="card bg-green-900 border-green-500">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-100">
                      {isLoadingPlayer ? '...' : playerData.nivel}
                    </div>
                    <div className="text-sm text-green-300">Nível</div>
                  </div>
                </div>
                <div className="card bg-purple-900 border-purple-500">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-100">
                      {isLoadingPlayer ? '...' : playerData.conquistas}
                    </div>
                    <div className="text-sm text-purple-300">Conquistas</div>
                  </div>
                </div>
                <div className="card bg-orange-900 border-orange-500">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-100">
                      {isLoadingPlayer ? '...' : playerData.ranking}
                    </div>
                    <div className="text-sm text-orange-300">Ranking</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Ações Rápidas */}
          <div>
            <div className="card">
              <h2 className="text-xl font-semibold mb-4">⚡ Ações Rápidas</h2>
              <div className="space-y-3">
                <button className="w-full btn-primary">
                  🔍 Scanner
                </button>
                <button className="w-full btn-secondary">
                  💬 Chat
                </button>
                <button className="w-full btn-accent">
                  🏆 Loja
                </button>
                <button className="w-full btn-outline">
                  ⚙️ Configurações
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Atividade Recente */}
        <div className="mt-8">
          <div className="card">
            <h2 className="text-2xl font-semibold mb-6">📈 Atividade Recente</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-bg-tertiary rounded-lg">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white font-bold">+</span>
                  </div>
                  <div>
                    <div className="font-medium">Pontos ganhos</div>
                    <div className="text-sm text-text-muted">Há 2 horas</div>
                  </div>
                </div>
                <div className="text-green-400 font-bold">+150 pts</div>
              </div>

              <div className="flex items-center justify-between p-4 bg-bg-tertiary rounded-lg">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white font-bold">🏆</span>
                  </div>
                  <div>
                    <div className="font-medium">Nova conquista</div>
                    <div className="text-sm text-text-muted">Há 1 dia</div>
                  </div>
                </div>
                <div className="text-blue-400 font-bold">Explorador</div>
              </div>

              <div className="flex items-center justify-between p-4 bg-bg-tertiary rounded-lg">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white font-bold">⬆</span>
                  </div>
                  <div>
                    <div className="font-medium">Subiu de nível</div>
                    <div className="text-sm text-text-muted">Há 3 dias</div>
                  </div>
                </div>
                <div className="text-purple-400 font-bold">Nível 15</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Componentes de placeholder para outras páginas
const SimpleScanner: React.FC = () => (
  <div className="p-8">
    <div className="max-w-4xl mx-auto">
      <div className="card text-center">
        <h1 className="text-3xl font-bold mb-4">🔍 Scanner</h1>
        <p className="text-text-muted mb-6">Funcionalidade do scanner será implementada aqui.</p>
        <div className="bg-bg-tertiary p-8 rounded-lg">
          <div className="text-6xl mb-4">🚧</div>
          <p>Em desenvolvimento...</p>
        </div>
      </div>
    </div>
  </div>
);

const SimpleChat: React.FC = () => {
  const { messages, isLoadingMessages, loadMessages, sendMessage, isSending } = useChat();
  const [newMessage, setNewMessage] = React.useState('');

  useEffect(() => {
    if (messages.length === 0 && !isLoadingMessages) {
      console.log('SimpleChat - Carregando mensagens...');
      loadMessages();
    }
  }, [messages.length, isLoadingMessages, loadMessages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newMessage.trim() && !isSending) {
      await sendMessage(newMessage.trim());
      setNewMessage('');
    }
  };

  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto">
        <div className="card">
          <h1 className="text-3xl font-bold mb-6">💬 Chat Global</h1>

          {/* Área de mensagens */}
          <div className="bg-bg-tertiary rounded-lg p-4 h-96 overflow-y-auto mb-4">
            {isLoadingMessages ? (
              <div className="text-center text-text-muted">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                Carregando mensagens...
              </div>
            ) : messages.length === 0 ? (
              <div className="text-center text-text-muted">
                <div className="text-4xl mb-2">💬</div>
                <p>Nenhuma mensagem ainda. Seja o primeiro a falar!</p>
              </div>
            ) : (
              <div className="space-y-3">
                {messages.map((message) => (
                  <div key={message.id} className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      {message.usuario.charAt(0).toUpperCase()}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-sm">{message.usuario}</span>
                        <span className="text-xs text-text-muted">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-sm">{message.mensagem}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Formulário de envio */}
          <form onSubmit={handleSendMessage} className="flex space-x-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Digite sua mensagem..."
              className="flex-1 px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500"
              disabled={isSending}
            />
            <button
              type="submit"
              disabled={!newMessage.trim() || isSending}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                !newMessage.trim() || isSending
                  ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isSending ? '...' : 'Enviar'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

// Navegação Simples
const SimpleNavigation: React.FC = () => {
  return (
    <nav className="bg-bg-secondary border-b border-border-primary">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-8">
            <div className="text-xl font-bold">🎮 SHACK</div>
            <div className="flex space-x-4">
              <a href="/game" className="nav-link">Dashboard</a>
              <a href="/game/scanner" className="nav-link">Scanner</a>
              <a href="/game/chat" className="nav-link">Chat</a>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-text-muted">Modo Teste</span>
          </div>
        </div>
      </div>
    </nav>
  );
};

// Página Principal do Jogo Simplificada
const SimpleGamePage: React.FC = () => {
  console.log('SimpleGamePage - Renderizando...');

  return (
    <div className="min-h-screen bg-bg-primary text-text-primary">
      <SimpleNavigation />
      
      <Routes>
        <Route path="/" element={<SimpleDashboard />} />
        <Route path="/scanner" element={<SimpleScanner />} />
        <Route path="/chat" element={<SimpleChat />} />
        <Route path="*" element={<SimpleDashboard />} />
      </Routes>
    </div>
  );
};

export default SimpleGamePage;
