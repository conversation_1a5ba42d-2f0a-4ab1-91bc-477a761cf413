-- ==================== SHACK GAME DATABASE SCHEMA ====================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ==================== PLAYERS TABLE ====================
CREATE TABLE players (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nick VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    ip VARCHAR(15) UNIQUE NOT NULL,
    level INTEGER DEFAULT 1 NOT NULL,
    xp INTEGER DEFAULT 0 NOT NULL,
    cash INTEGER DEFAULT 1000 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for players
CREATE INDEX idx_players_nick ON players(nick);
CREATE INDEX idx_players_email ON players(email);
CREATE INDEX idx_players_ip ON players(ip);
CREATE INDEX idx_players_level ON players(level);
CREATE INDEX idx_players_last_login ON players(last_login);

-- ==================== PLAYER APPS TABLE ====================
CREATE TABLE player_apps (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    player_id UUID REFERENCES players(id) ON DELETE CASCADE,
    antivirus INTEGER DEFAULT 1 NOT NULL,
    bankguard INTEGER DEFAULT 1 NOT NULL,
    bruteforce INTEGER DEFAULT 1 NOT NULL,
    sdk INTEGER DEFAULT 1 NOT NULL,
    firewall INTEGER DEFAULT 1 NOT NULL,
    malwarekit INTEGER DEFAULT 1 NOT NULL,
    proxyvpn INTEGER DEFAULT 1 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(player_id)
);

-- Index for player_apps
CREATE INDEX idx_player_apps_player_id ON player_apps(player_id);

-- ==================== NOTIFICATIONS TABLE ====================
CREATE TABLE notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    player_id UUID REFERENCES players(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('system', 'upgrade', 'level', 'hack', 'transfer')),
    title VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for notifications
CREATE INDEX idx_notifications_player_id ON notifications(player_id);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_read ON notifications(read);

-- ==================== CHAT MESSAGES TABLE ====================
CREATE TABLE chat_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    player_id UUID REFERENCES players(id) ON DELETE CASCADE,
    player_nick VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for chat_messages
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_chat_messages_player_id ON chat_messages(player_id);

-- ==================== HACK LOGS TABLE ====================
CREATE TABLE hack_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    attacker_id UUID REFERENCES players(id) ON DELETE CASCADE,
    target_id UUID REFERENCES players(id) ON DELETE CASCADE,
    success BOOLEAN NOT NULL,
    amount_stolen INTEGER DEFAULT 0,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for hack_logs
CREATE INDEX idx_hack_logs_attacker_id ON hack_logs(attacker_id);
CREATE INDEX idx_hack_logs_target_id ON hack_logs(target_id);
CREATE INDEX idx_hack_logs_timestamp ON hack_logs(timestamp);

-- ==================== SCAN TARGETS TABLE ====================
CREATE TABLE scan_targets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    player_id UUID REFERENCES players(id) ON DELETE CASCADE,
    target_ip VARCHAR(15) NOT NULL,
    target_nick VARCHAR(20) NOT NULL,
    target_level INTEGER NOT NULL,
    target_cash INTEGER NOT NULL,
    firewall_level INTEGER NOT NULL,
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for scan_targets
CREATE INDEX idx_scan_targets_player_id ON scan_targets(player_id);
CREATE INDEX idx_scan_targets_target_ip ON scan_targets(target_ip);
CREATE INDEX idx_scan_targets_last_seen ON scan_targets(last_seen);

-- ==================== FUNCTIONS ====================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_players_updated_at BEFORE UPDATE ON players
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_player_apps_updated_at BEFORE UPDATE ON player_apps
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==================== ROW LEVEL SECURITY ====================

-- Enable RLS on all tables
ALTER TABLE players ENABLE ROW LEVEL SECURITY;
ALTER TABLE player_apps ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE hack_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE scan_targets ENABLE ROW LEVEL SECURITY;

-- Policies for players (players can only see/edit their own data)
CREATE POLICY "Players can view their own data" ON players
    FOR SELECT USING (true); -- Allow reading all players for ranking/scanning

CREATE POLICY "Players can update their own data" ON players
    FOR UPDATE USING (auth.uid()::text = id::text);

-- Policies for player_apps
CREATE POLICY "Players can view their own apps" ON player_apps
    FOR SELECT USING (auth.uid()::text = player_id::text);

CREATE POLICY "Players can update their own apps" ON player_apps
    FOR UPDATE USING (auth.uid()::text = player_id::text);

-- Policies for notifications
CREATE POLICY "Players can view their own notifications" ON notifications
    FOR SELECT USING (auth.uid()::text = player_id::text);

CREATE POLICY "Players can update their own notifications" ON notifications
    FOR UPDATE USING (auth.uid()::text = player_id::text);

-- Policies for chat_messages (everyone can read, only owner can insert)
CREATE POLICY "Anyone can view chat messages" ON chat_messages
    FOR SELECT USING (true);

CREATE POLICY "Players can insert their own messages" ON chat_messages
    FOR INSERT WITH CHECK (auth.uid()::text = player_id::text);

-- Policies for hack_logs
CREATE POLICY "Players can view hack logs involving them" ON hack_logs
    FOR SELECT USING (
        auth.uid()::text = attacker_id::text OR 
        auth.uid()::text = target_id::text
    );

CREATE POLICY "Players can insert hack logs as attacker" ON hack_logs
    FOR INSERT WITH CHECK (auth.uid()::text = attacker_id::text);

-- Policies for scan_targets
CREATE POLICY "Players can view their own scan targets" ON scan_targets
    FOR SELECT USING (auth.uid()::text = player_id::text);

CREATE POLICY "Players can manage their own scan targets" ON scan_targets
    FOR ALL USING (auth.uid()::text = player_id::text);

-- ==================== SAMPLE DATA ====================

-- Insert sample players for testing
INSERT INTO players (nick, email, ip, level, xp, cash) VALUES
('Admin', '<EMAIL>', '***********', 50, 50000, 100000),
('Hacker01', '<EMAIL>', '********', 15, 2250, 5000),
('NoobPlayer', '<EMAIL>', '**********', 3, 150, 800);

-- Insert corresponding apps for sample players
INSERT INTO player_apps (player_id, antivirus, bankguard, bruteforce, sdk, firewall, malwarekit, proxyvpn)
SELECT id, 10, 10, 10, 10, 10, 10, 10 FROM players WHERE nick = 'Admin';

INSERT INTO player_apps (player_id, antivirus, bankguard, bruteforce, sdk, firewall, malwarekit, proxyvpn)
SELECT id, 5, 4, 6, 3, 5, 4, 3 FROM players WHERE nick = 'Hacker01';

INSERT INTO player_apps (player_id, antivirus, bankguard, bruteforce, sdk, firewall, malwarekit, proxyvpn)
SELECT id, 2, 1, 2, 1, 1, 1, 2 FROM players WHERE nick = 'NoobPlayer';

-- Insert sample chat messages
INSERT INTO chat_messages (player_id, player_nick, message)
SELECT id, nick, 'Bem-vindos ao SHACK! 🔒' FROM players WHERE nick = 'Admin';

INSERT INTO chat_messages (player_id, player_nick, message)
SELECT id, nick, 'Alguém quer fazer uma batalha?' FROM players WHERE nick = 'Hacker01';

INSERT INTO chat_messages (player_id, player_nick, message)
SELECT id, nick, 'Como faço upgrade nos apps?' FROM players WHERE nick = 'NoobPlayer';
