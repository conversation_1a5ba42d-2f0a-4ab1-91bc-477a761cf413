{"ast": null, "code": "var _s = $RefreshSig$();\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport apiService from '../services/api';\nexport const useAuthStore = create()(persist((set, get) => ({\n  // Estado inicial\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n  // Ação de login\n  login: async credentials => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const response = await apiService.login(credentials);\n      if (response.sucesso && response.user && response.token) {\n        // Salvar no localStorage (será persistido pelo zustand)\n        set({\n          user: response.user,\n          token: response.token,\n          isAuthenticated: true,\n          isLoading: false,\n          error: null\n        });\n\n        // Também salvar no localStorage para compatibilidade com código existente\n        localStorage.setItem('shack_token', response.token);\n        localStorage.setItem('shack_user', JSON.stringify(response.user));\n      } else {\n        throw new Error(response.mensagem || 'Erro desconhecido no login');\n      }\n    } catch (error) {\n      set({\n        isLoading: false,\n        error: error.message || 'Erro ao fazer login',\n        isAuthenticated: false,\n        user: null,\n        token: null\n      });\n      throw error;\n    }\n  },\n  // Ação de registro\n  register: async userData => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const response = await apiService.register(userData);\n      if (response.sucesso && response.user && response.token) {\n        set({\n          user: response.user,\n          token: response.token,\n          isAuthenticated: true,\n          isLoading: false,\n          error: null\n        });\n\n        // Também salvar no localStorage para compatibilidade\n        localStorage.setItem('shack_token', response.token);\n        localStorage.setItem('shack_user', JSON.stringify(response.user));\n      } else {\n        throw new Error(response.mensagem || 'Erro desconhecido no registro');\n      }\n    } catch (error) {\n      set({\n        isLoading: false,\n        error: error.message || 'Erro ao criar conta',\n        isAuthenticated: false,\n        user: null,\n        token: null\n      });\n      throw error;\n    }\n  },\n  // Ação de logout\n  logout: () => {\n    // Limpar localStorage\n    localStorage.removeItem('shack_token');\n    localStorage.removeItem('shack_user');\n\n    // Limpar estado\n    set({\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null\n    });\n  },\n  // Verificar autenticação\n  checkAuth: async () => {\n    const {\n      token,\n      isLoading\n    } = get();\n\n    // Evitar múltiplas verificações simultâneas\n    if (isLoading) {\n      console.log('AuthStore - Verificação já em andamento, ignorando...');\n      return;\n    }\n    if (!token) {\n      console.log('AuthStore - Sem token, definindo como não autenticado');\n      set({\n        isAuthenticated: false,\n        isLoading: false\n      });\n      return;\n    }\n    console.log('AuthStore - Verificando token...');\n    set({\n      isLoading: true\n    });\n    try {\n      const isValid = await apiService.verifyToken();\n      if (isValid) {\n        console.log('AuthStore - Token válido');\n        set({\n          isAuthenticated: true,\n          isLoading: false\n        });\n      } else {\n        console.log('AuthStore - Token inválido, fazendo logout');\n        // Token inválido, fazer logout\n        get().logout();\n      }\n    } catch (error) {\n      // Erro na verificação, fazer logout\n      console.error('AuthStore - Erro ao verificar autenticação:', error);\n      get().logout();\n    } finally {\n      set({\n        isLoading: false\n      });\n    }\n  },\n  // Limpar erro\n  clearError: () => {\n    set({\n      error: null\n    });\n  },\n  // Definir loading\n  setLoading: loading => {\n    set({\n      isLoading: loading\n    });\n  }\n}), {\n  name: 'shack-auth',\n  // Nome da chave no localStorage\n  partialize: state => ({\n    user: state.user,\n    token: state.token,\n    isAuthenticated: state.isAuthenticated\n  })\n}));\n\n// Hook personalizado para usar autenticação\nexport const useAuth = () => {\n  _s();\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    register,\n    logout,\n    checkAuth,\n    clearError,\n    setLoading\n  } = useAuthStore();\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    // Ações\n    login,\n    register,\n    logout,\n    checkAuth,\n    clearError,\n    setLoading,\n    // Computed\n    isLoggedIn: isAuthenticated && !!user\n  };\n};\n_s(useAuth, \"kseNHy2yAeZe5/otvhljWCLKbIM=\", false, function () {\n  return [useAuthStore];\n});", "map": {"version": 3, "names": ["create", "persist", "apiService", "useAuthStore", "set", "get", "user", "token", "isAuthenticated", "isLoading", "error", "login", "credentials", "response", "sucesso", "localStorage", "setItem", "JSON", "stringify", "Error", "mensagem", "message", "register", "userData", "logout", "removeItem", "checkAuth", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "verifyToken", "clearError", "setLoading", "loading", "name", "partialize", "state", "useAuth", "_s", "isLoggedIn"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/authStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport apiService from '../services/api';\n\nexport interface User {\n  uid: string;\n  nick: string;\n  email: string;\n}\n\ninterface AuthState {\n  // Estado\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n\n  // Ações\n  login: (credentials: { email: string; password: string }) => Promise<void>;\n  register: (userData: { email: string; password: string; nick: string }) => Promise<void>;\n  logout: () => void;\n  checkAuth: () => Promise<void>;\n  clearError: () => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      // Estado inicial\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // Ação de login\n      login: async (credentials) => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          const response = await apiService.login(credentials);\n          \n          if (response.sucesso && response.user && response.token) {\n            // Salvar no localStorage (será persistido pelo zustand)\n            set({\n              user: response.user,\n              token: response.token,\n              isAuthenticated: true,\n              isLoading: false,\n              error: null,\n            });\n\n            // Também salvar no localStorage para compatibilidade com código existente\n            localStorage.setItem('shack_token', response.token);\n            localStorage.setItem('shack_user', JSON.stringify(response.user));\n          } else {\n            throw new Error(response.mensagem || 'Erro desconhecido no login');\n          }\n        } catch (error: any) {\n          set({\n            isLoading: false,\n            error: error.message || 'Erro ao fazer login',\n            isAuthenticated: false,\n            user: null,\n            token: null,\n          });\n          throw error;\n        }\n      },\n\n      // Ação de registro\n      register: async (userData) => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          const response = await apiService.register(userData);\n          \n          if (response.sucesso && response.user && response.token) {\n            set({\n              user: response.user,\n              token: response.token,\n              isAuthenticated: true,\n              isLoading: false,\n              error: null,\n            });\n\n            // Também salvar no localStorage para compatibilidade\n            localStorage.setItem('shack_token', response.token);\n            localStorage.setItem('shack_user', JSON.stringify(response.user));\n          } else {\n            throw new Error(response.mensagem || 'Erro desconhecido no registro');\n          }\n        } catch (error: any) {\n          set({\n            isLoading: false,\n            error: error.message || 'Erro ao criar conta',\n            isAuthenticated: false,\n            user: null,\n            token: null,\n          });\n          throw error;\n        }\n      },\n\n      // Ação de logout\n      logout: () => {\n        // Limpar localStorage\n        localStorage.removeItem('shack_token');\n        localStorage.removeItem('shack_user');\n        \n        // Limpar estado\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: null,\n        });\n      },\n\n      // Verificar autenticação\n      checkAuth: async () => {\n        const { token, isLoading } = get();\n\n        // Evitar múltiplas verificações simultâneas\n        if (isLoading) {\n          console.log('AuthStore - Verificação já em andamento, ignorando...');\n          return;\n        }\n\n        if (!token) {\n          console.log('AuthStore - Sem token, definindo como não autenticado');\n          set({ isAuthenticated: false, isLoading: false });\n          return;\n        }\n\n        console.log('AuthStore - Verificando token...');\n        set({ isLoading: true });\n\n        try {\n          const isValid = await apiService.verifyToken();\n\n          if (isValid) {\n            console.log('AuthStore - Token válido');\n            set({ isAuthenticated: true, isLoading: false });\n          } else {\n            console.log('AuthStore - Token inválido, fazendo logout');\n            // Token inválido, fazer logout\n            get().logout();\n          }\n        } catch (error) {\n          // Erro na verificação, fazer logout\n          console.error('AuthStore - Erro ao verificar autenticação:', error);\n          get().logout();\n        } finally {\n          set({ isLoading: false });\n        }\n      },\n\n      // Limpar erro\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // Definir loading\n      setLoading: (loading) => {\n        set({ isLoading: loading });\n      },\n    }),\n    {\n      name: 'shack-auth', // Nome da chave no localStorage\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// Hook personalizado para usar autenticação\nexport const useAuth = () => {\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    register,\n    logout,\n    checkAuth,\n    clearError,\n    setLoading,\n  } = useAuthStore();\n\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    \n    // Ações\n    login,\n    register,\n    logout,\n    checkAuth,\n    clearError,\n    setLoading,\n    \n    // Computed\n    isLoggedIn: isAuthenticated && !!user,\n  };\n};\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,UAAU,MAAM,iBAAiB;AAyBxC,OAAO,MAAMC,YAAY,GAAGH,MAAM,CAAY,CAAC,CAC7CC,OAAO,CACL,CAACG,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEX;EACAC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5BR,GAAG,CAAC;MAAEK,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMX,UAAU,CAACS,KAAK,CAACC,WAAW,CAAC;MAEpD,IAAIC,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACP,IAAI,IAAIO,QAAQ,CAACN,KAAK,EAAE;QACvD;QACAH,GAAG,CAAC;UACFE,IAAI,EAAEO,QAAQ,CAACP,IAAI;UACnBC,KAAK,EAAEM,QAAQ,CAACN,KAAK;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;;QAEF;QACAK,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEH,QAAQ,CAACN,KAAK,CAAC;QACnDQ,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACL,QAAQ,CAACP,IAAI,CAAC,CAAC;MACnE,CAAC,MAAM;QACL,MAAM,IAAIa,KAAK,CAACN,QAAQ,CAACO,QAAQ,IAAI,4BAA4B,CAAC;MACpE;IACF,CAAC,CAAC,OAAOV,KAAU,EAAE;MACnBN,GAAG,CAAC;QACFK,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEA,KAAK,CAACW,OAAO,IAAI,qBAAqB;QAC7Cb,eAAe,EAAE,KAAK;QACtBF,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,MAAMG,KAAK;IACb;EACF,CAAC;EAED;EACAY,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5BnB,GAAG,CAAC;MAAEK,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMX,UAAU,CAACoB,QAAQ,CAACC,QAAQ,CAAC;MAEpD,IAAIV,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACP,IAAI,IAAIO,QAAQ,CAACN,KAAK,EAAE;QACvDH,GAAG,CAAC;UACFE,IAAI,EAAEO,QAAQ,CAACP,IAAI;UACnBC,KAAK,EAAEM,QAAQ,CAACN,KAAK;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;;QAEF;QACAK,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEH,QAAQ,CAACN,KAAK,CAAC;QACnDQ,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACL,QAAQ,CAACP,IAAI,CAAC,CAAC;MACnE,CAAC,MAAM;QACL,MAAM,IAAIa,KAAK,CAACN,QAAQ,CAACO,QAAQ,IAAI,+BAA+B,CAAC;MACvE;IACF,CAAC,CAAC,OAAOV,KAAU,EAAE;MACnBN,GAAG,CAAC;QACFK,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEA,KAAK,CAACW,OAAO,IAAI,qBAAqB;QAC7Cb,eAAe,EAAE,KAAK;QACtBF,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,MAAMG,KAAK;IACb;EACF,CAAC;EAED;EACAc,MAAM,EAAEA,CAAA,KAAM;IACZ;IACAT,YAAY,CAACU,UAAU,CAAC,aAAa,CAAC;IACtCV,YAAY,CAACU,UAAU,CAAC,YAAY,CAAC;;IAErC;IACArB,GAAG,CAAC;MACFE,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE,KAAK;MACtBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED;EACAgB,SAAS,EAAE,MAAAA,CAAA,KAAY;IACrB,MAAM;MAAEnB,KAAK;MAAEE;IAAU,CAAC,GAAGJ,GAAG,CAAC,CAAC;;IAElC;IACA,IAAII,SAAS,EAAE;MACbkB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpE;IACF;IAEA,IAAI,CAACrB,KAAK,EAAE;MACVoB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpExB,GAAG,CAAC;QAAEI,eAAe,EAAE,KAAK;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MACjD;IACF;IAEAkB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CxB,GAAG,CAAC;MAAEK,SAAS,EAAE;IAAK,CAAC,CAAC;IAExB,IAAI;MACF,MAAMoB,OAAO,GAAG,MAAM3B,UAAU,CAAC4B,WAAW,CAAC,CAAC;MAE9C,IAAID,OAAO,EAAE;QACXF,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCxB,GAAG,CAAC;UAAEI,eAAe,EAAE,IAAI;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC;MAClD,CAAC,MAAM;QACLkB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzD;QACAvB,GAAG,CAAC,CAAC,CAACmB,MAAM,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOd,KAAK,EAAE;MACd;MACAiB,OAAO,CAACjB,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnEL,GAAG,CAAC,CAAC,CAACmB,MAAM,CAAC,CAAC;IAChB,CAAC,SAAS;MACRpB,GAAG,CAAC;QAAEK,SAAS,EAAE;MAAM,CAAC,CAAC;IAC3B;EACF,CAAC;EAED;EACAsB,UAAU,EAAEA,CAAA,KAAM;IAChB3B,GAAG,CAAC;MAAEM,KAAK,EAAE;IAAK,CAAC,CAAC;EACtB,CAAC;EAED;EACAsB,UAAU,EAAGC,OAAO,IAAK;IACvB7B,GAAG,CAAC;MAAEK,SAAS,EAAEwB;IAAQ,CAAC,CAAC;EAC7B;AACF,CAAC,CAAC,EACF;EACEC,IAAI,EAAE,YAAY;EAAE;EACpBC,UAAU,EAAGC,KAAK,KAAM;IACtB9B,IAAI,EAAE8B,KAAK,CAAC9B,IAAI;IAChBC,KAAK,EAAE6B,KAAK,CAAC7B,KAAK;IAClBC,eAAe,EAAE4B,KAAK,CAAC5B;EACzB,CAAC;AACH,CACF,CACF,CAAC;;AAED;AACA,OAAO,MAAM6B,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IACJhC,IAAI;IACJC,KAAK;IACLC,eAAe;IACfC,SAAS;IACTC,KAAK;IACLC,KAAK;IACLW,QAAQ;IACRE,MAAM;IACNE,SAAS;IACTK,UAAU;IACVC;EACF,CAAC,GAAG7B,YAAY,CAAC,CAAC;EAElB,OAAO;IACL;IACAG,IAAI;IACJC,KAAK;IACLC,eAAe;IACfC,SAAS;IACTC,KAAK;IAEL;IACAC,KAAK;IACLW,QAAQ;IACRE,MAAM;IACNE,SAAS;IACTK,UAAU;IACVC,UAAU;IAEV;IACAO,UAAU,EAAE/B,eAAe,IAAI,CAAC,CAACF;EACnC,CAAC;AACH,CAAC;AAACgC,EAAA,CAlCWD,OAAO;EAAA,QAadlC,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}