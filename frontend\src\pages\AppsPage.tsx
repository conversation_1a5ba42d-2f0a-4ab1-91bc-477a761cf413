import React, { useState } from 'react';
import { useAuth } from '../stores/authStore';
import { usePlayer } from '../stores/playerStore';
import GameFooter from '../components/common/GameFooter';
import { ConfigIcon, SkillsIcon, SecurityIcon, TerminalIcon } from '../components/ui/GameIcons';

const AppsPage: React.FC = () => {
  const { user } = useAuth();
  const { currentPlayer } = usePlayer();
  const [selectedApp, setSelectedApp] = useState<string | null>(null);

  const apps = [
    {
      id: 'upgrades',
      name: 'Upgrades',
      icon: SkillsIcon,
      color: 'text-pink-400',
      description: 'Melhore suas habilidades e equipamentos'
    },
    {
      id: 'security',
      name: 'Seguran<PERSON>',
      icon: SecurityIcon,
      color: 'text-red-400',
      description: 'Configure firewall e proteções'
    },
    {
      id: 'terminal',
      name: 'Terminal',
      icon: TerminalIcon,
      color: 'text-cyan-400',
      description: 'Acesso ao terminal do sistema'
    },
    {
      id: 'config',
      name: 'Configurações',
      icon: ConfigIcon,
      color: 'text-gray-400',
      description: 'Configurações do sistema'
    }
  ];

  const handleAppClick = (appId: string) => {
    if (appId === 'upgrades') {
      // Redirecionar para a página de upgrades
      window.location.href = '/game/upgrades';
    } else if (appId === 'terminal') {
      window.location.href = '/game/terminal';
    } else if (appId === 'config') {
      window.location.href = '/game/config';
    } else {
      setSelectedApp(appId);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-gray-700/50 p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => window.history.back()}
              className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors"
            >
              <span className="text-lg">←</span>
            </button>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Apps
              </h1>
              <p className="text-xs text-gray-400">Aplicativos do Sistema</p>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-1 text-green-400 text-xs">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-4 overflow-y-auto">
        {!selectedApp ? (
          <div className="space-y-6">
            {/* Grid de aplicativos */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50">
              <h2 className="text-lg font-semibold mb-4 text-white">Aplicativos Disponíveis</h2>
              <div className="grid grid-cols-2 gap-4">
                {apps.map((app) => {
                  const IconComponent = app.icon;
                  return (
                    <button
                      key={app.id}
                      onClick={() => handleAppClick(app.id)}
                      className="flex flex-col items-center p-6 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
                    >
                      <div className={`w-16 h-16 flex items-center justify-center mb-3 ${app.color} group-hover:scale-110 transition-transform`}>
                        <IconComponent size={48} />
                      </div>
                      <h3 className="text-sm font-semibold text-white mb-1">{app.name}</h3>
                      <p className="text-xs text-gray-400 text-center">{app.description}</p>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Informações do sistema */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50">
              <h2 className="text-lg font-semibold mb-4 text-white">Informações do Sistema</h2>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Usuário:</span>
                  <span className="text-white ml-2">{user?.nick || 'Desconhecido'}</span>
                </div>
                <div>
                  <span className="text-gray-400">Nível:</span>
                  <span className="text-blue-400 ml-2">{currentPlayer?.nivel || 1}</span>
                </div>
                <div>
                  <span className="text-gray-400">IP:</span>
                  <span className="text-cyan-400 ml-2 font-mono">{currentPlayer?.ip || '127.0.0.1'}</span>
                </div>
                <div>
                  <span className="text-gray-400">Status:</span>
                  <span className="text-green-400 ml-2">Online</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-white">
                {apps.find(app => app.id === selectedApp)?.name}
              </h2>
              <button
                onClick={() => setSelectedApp(null)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            </div>
            <div className="text-center py-12">
              <p className="text-gray-400">Aplicativo em desenvolvimento...</p>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <GameFooter currentPage="apps" />
    </div>
  );
};

export default AppsPage;
