import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../stores/authStore';
import LoadingSpinner from '../common/LoadingSpinner';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { isAuthenticated, isLoading, checkAuth, user } = useAuth();

  useEffect(() => {
    console.log('AuthGuard - Estado:', { isAuthenticated, isLoading, user: !!user });

    // Verificar autenticação ao montar o componente
    if (!isAuthenticated && !isLoading) {
      console.log('AuthGuard - Verificando autenticação...');
      checkAuth();
    }
  }, [isAuthenticated, isLoading, checkAuth]);

  // Mostrar loading enquanto verifica autenticação
  if (isLoading) {
    console.log('AuthGuard - Mostrando loading...');
    return (
      <LoadingSpinner
        fullScreen
        size="lg"
        text="Verificando autenticação..."
        color="primary"
      />
    );
  }

  // Redirecionar para login se não autenticado
  if (!isAuthenticated || !user) {
    console.log('AuthGuard - Redirecionando para login...');
    return <Navigate to="/login" replace />;
  }

  // Renderizar children se autenticado
  console.log('AuthGuard - Usuário autenticado, renderizando children...');
  return <>{children}</>;
};

export default AuthGuard;
