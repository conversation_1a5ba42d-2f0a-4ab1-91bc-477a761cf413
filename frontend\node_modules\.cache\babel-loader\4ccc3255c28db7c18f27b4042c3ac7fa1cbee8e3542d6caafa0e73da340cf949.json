{"ast": null, "code": "// src/thenable.ts\nimport { noop } from \"./utils.js\";\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {});\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = value => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = reason => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then(result => {\n    data = result;\n    return result;\n  }, noop)?.catch(noop);\n  if (data !== void 0) {\n    return {\n      data\n    };\n  }\n  return void 0;\n}\nexport { pendingThenable, tryResolveSync };", "map": {"version": 3, "names": ["noop", "pendingThenable", "resolve", "reject", "thenable", "Promise", "_resolve", "_reject", "status", "catch", "finalize", "data", "Object", "assign", "value", "reason", "tryResolveSync", "promise", "then", "result"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\query-core\\src\\thenable.ts"], "sourcesContent": ["/**\n * Thenable types which matches <PERSON><PERSON>'s types for promises\n *\n * <PERSON><PERSON> seemingly uses `.status`, `.value` and `.reason` properties on a promises to optimistically unwrap data from promises\n *\n * @see https://github.com/facebook/react/blob/main/packages/shared/ReactTypes.js#L112-L138\n * @see https://github.com/facebook/react/blob/4f604941569d2e8947ce1460a0b2997e835f37b9/packages/react-debug-tools/src/ReactDebugHooks.js#L224-L227\n */\n\nimport { noop } from './utils'\n\ninterface Fulfilled<T> {\n  status: 'fulfilled'\n  value: T\n}\ninterface Rejected {\n  status: 'rejected'\n  reason: unknown\n}\ninterface Pending<T> {\n  status: 'pending'\n\n  /**\n   * Resolve the promise with a value.\n   * Will remove the `resolve` and `reject` properties from the promise.\n   */\n  resolve: (value: T) => void\n  /**\n   * Reject the promise with a reason.\n   * Will remove the `resolve` and `reject` properties from the promise.\n   */\n  reject: (reason: unknown) => void\n}\n\nexport type FulfilledThenable<T> = Promise<T> & Fulfilled<T>\nexport type RejectedThenable<T> = Promise<T> & Rejected\nexport type PendingThenable<T> = Promise<T> & Pending<T>\n\nexport type Thenable<T> =\n  | FulfilledThenable<T>\n  | RejectedThenable<T>\n  | PendingThenable<T>\n\nexport function pendingThenable<T>(): PendingThenable<T> {\n  let resolve: Pending<T>['resolve']\n  let reject: Pending<T>['reject']\n  // this could use `Promise.withResolvers()` in the future\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve\n    reject = _reject\n  }) as PendingThenable<T>\n\n  thenable.status = 'pending'\n  thenable.catch(() => {\n    // prevent unhandled rejection errors\n  })\n\n  function finalize(data: Fulfilled<T> | Rejected) {\n    Object.assign(thenable, data)\n\n    // clear pending props props to avoid calling them twice\n    delete (thenable as Partial<PendingThenable<T>>).resolve\n    delete (thenable as Partial<PendingThenable<T>>).reject\n  }\n\n  thenable.resolve = (value) => {\n    finalize({\n      status: 'fulfilled',\n      value,\n    })\n\n    resolve(value)\n  }\n  thenable.reject = (reason) => {\n    finalize({\n      status: 'rejected',\n      reason,\n    })\n\n    reject(reason)\n  }\n\n  return thenable\n}\n\n/**\n * This function takes a Promise-like input and detects whether the data\n * is synchronously available or not.\n *\n * It does not inspect .status, .value or .reason properties of the promise,\n * as those are not always available, and the .status of React's promises\n * should not be considered part of the public API.\n */\nexport function tryResolveSync(promise: Promise<unknown> | Thenable<unknown>) {\n  let data: unknown\n\n  promise\n    .then((result) => {\n      data = result\n      return result\n    }, noop)\n    // .catch can be unavailable on certain kinds of thenable's\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    ?.catch(noop)\n\n  if (data !== undefined) {\n    return { data }\n  }\n\n  return undefined\n}\n"], "mappings": ";AASA,SAASA,IAAA,QAAY;AAkCd,SAASC,gBAAA,EAAyC;EACvD,IAAIC,OAAA;EACJ,IAAIC,MAAA;EAEJ,MAAMC,QAAA,GAAW,IAAIC,OAAA,CAAQ,CAACC,QAAA,EAAUC,OAAA,KAAY;IAClDL,OAAA,GAAUI,QAAA;IACVH,MAAA,GAASI,OAAA;EACX,CAAC;EAEDH,QAAA,CAASI,MAAA,GAAS;EAClBJ,QAAA,CAASK,KAAA,CAAM,MAAM,CAErB,CAAC;EAED,SAASC,SAASC,IAAA,EAA+B;IAC/CC,MAAA,CAAOC,MAAA,CAAOT,QAAA,EAAUO,IAAI;IAG5B,OAAQP,QAAA,CAAyCF,OAAA;IACjD,OAAQE,QAAA,CAAyCD,MAAA;EACnD;EAEAC,QAAA,CAASF,OAAA,GAAWY,KAAA,IAAU;IAC5BJ,QAAA,CAAS;MACPF,MAAA,EAAQ;MACRM;IACF,CAAC;IAEDZ,OAAA,CAAQY,KAAK;EACf;EACAV,QAAA,CAASD,MAAA,GAAUY,MAAA,IAAW;IAC5BL,QAAA,CAAS;MACPF,MAAA,EAAQ;MACRO;IACF,CAAC;IAEDZ,MAAA,CAAOY,MAAM;EACf;EAEA,OAAOX,QAAA;AACT;AAUO,SAASY,eAAeC,OAAA,EAA+C;EAC5E,IAAIN,IAAA;EAEJM,OAAA,CACGC,IAAA,CAAMC,MAAA,IAAW;IAChBR,IAAA,GAAOQ,MAAA;IACP,OAAOA,MAAA;EACT,GAAGnB,IAAI,GAGLS,KAAA,CAAMT,IAAI;EAEd,IAAIW,IAAA,KAAS,QAAW;IACtB,OAAO;MAAEA;IAAK;EAChB;EAEA,OAAO;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}