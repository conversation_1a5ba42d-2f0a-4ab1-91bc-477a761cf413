{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\GameSetupPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useHackGameStore } from '../stores/hackGameStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameSetupPage = () => {\n  _s();\n  const {\n    initializePlayer,\n    error,\n    isLoading\n  } = useHackGameStore();\n  const [nick, setNick] = useState('');\n  const [email, setEmail] = useState('');\n  const [isCreating, setIsCreating] = useState(false);\n  const handleCreatePlayer = async () => {\n    if (!nick.trim() || !email.trim()) {\n      alert('Por favor, preencha todos os campos');\n      return;\n    }\n    if (nick.length < 3 || nick.length > 20) {\n      alert('O nick deve ter entre 3 e 20 caracteres');\n      return;\n    }\n    if (!email.includes('@')) {\n      alert('Por favor, insira um email válido');\n      return;\n    }\n    setIsCreating(true);\n    try {\n      const success = await initializePlayer(nick.trim(), email.trim());\n      if (!success) {\n        // O erro já foi definido no store\n        setIsCreating(false);\n      }\n      // Se success for true, o jogador foi criado e a tela mudará automaticamente\n    } catch (error) {\n      alert('Erro ao criar jogador. Tente novamente.');\n      setIsCreating(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-6xl mb-4\",\n        children: \"\\uD83D\\uDD12\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold mb-2\",\n        children: \"SHACK\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-400\",\n        children: \"Simulador de Hacking\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 px-6 py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800/50 rounded-2xl p-6 border border-gray-700/50\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-6 text-center\",\n          children: \"Criar Novo Hacker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Nome do Hacker\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: nick,\n              onChange: e => setNick(e.target.value),\n              placeholder: \"Digite seu nick\",\n              maxLength: 20,\n              className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-400 mt-1\",\n              children: \"3-20 caracteres\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: email,\n              onChange: e => setEmail(e.target.value),\n              placeholder: \"<EMAIL>\",\n              className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCreatePlayer,\n          disabled: isCreating || !nick.trim() || !email.trim(),\n          className: \"w-full mt-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors\",\n          children: isCreating ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Criando...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this) : 'Iniciar Jornada'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold mb-2 flex items-center\",\n            children: \"\\uD83C\\uDFAF Como Jogar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"text-sm text-gray-300 space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Fa\\xE7a upgrades nos seus aplicativos\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Ganhe XP e suba de n\\xEDvel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Escaneie a rede em busca de alvos\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Invada sistemas e ganhe dinheiro\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold mb-2 flex items-center\",\n            children: \"\\uD83D\\uDEE0\\uFE0F Aplicativos Iniciais\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-2 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83D\\uDEE1\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"AntiVirus\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83C\\uDFE6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"BankGuard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83D\\uDD28\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"BruteForce\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u2699\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"SDK\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83D\\uDD25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Firewall\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83E\\uDDA0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Malware Kit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83C\\uDF10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"ProxyVPN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 py-2 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500\",\n        children: \"SHACK v1.0 - Simulador de Hacking Educacional\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(GameSetupPage, \"sx+gTWoxds8yQpu5ygPbJFxZGD4=\", false, function () {\n  return [useHackGameStore];\n});\n_c = GameSetupPage;\nexport default GameSetupPage;\nvar _c;\n$RefreshReg$(_c, \"GameSetupPage\");", "map": {"version": 3, "names": ["React", "useState", "useHackGameStore", "jsxDEV", "_jsxDEV", "GameSetupPage", "_s", "initializePlayer", "error", "isLoading", "nick", "<PERSON><PERSON><PERSON>", "email", "setEmail", "isCreating", "setIsCreating", "handleCreatePlayer", "trim", "alert", "length", "includes", "success", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/GameSetupPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useHackGameStore } from '../stores/hackGameStore';\n\nconst GameSetupPage: React.FC = () => {\n  const { initializePlayer, error, isLoading } = useHackGameStore();\n  const [nick, setNick] = useState('');\n  const [email, setEmail] = useState('');\n  const [isCreating, setIsCreating] = useState(false);\n\n  const handleCreatePlayer = async () => {\n    if (!nick.trim() || !email.trim()) {\n      alert('Por favor, preencha todos os campos');\n      return;\n    }\n\n    if (nick.length < 3 || nick.length > 20) {\n      alert('O nick deve ter entre 3 e 20 caracteres');\n      return;\n    }\n\n    if (!email.includes('@')) {\n      alert('Por favor, insira um email válido');\n      return;\n    }\n\n    setIsCreating(true);\n\n    try {\n      const success = await initializePlayer(nick.trim(), email.trim());\n\n      if (!success) {\n        // O erro já foi definido no store\n        setIsCreating(false);\n      }\n      // Se success for true, o jogador foi criado e a tela mudará automaticamente\n    } catch (error) {\n      alert('Erro ao criar jogador. Tente novamente.');\n      setIsCreating(false);\n    }\n  };\n\n  return (\n    <div className=\"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"p-6 text-center\">\n        <div className=\"text-6xl mb-4\">🔒</div>\n        <h1 className=\"text-2xl font-bold mb-2\">SHACK</h1>\n        <p className=\"text-sm text-gray-400\">Simulador de Hacking</p>\n      </div>\n\n      {/* Formulário */}\n      <div className=\"flex-1 px-6 py-4\">\n        <div className=\"bg-gray-800/50 rounded-2xl p-6 border border-gray-700/50\">\n          <h2 className=\"text-lg font-semibold mb-6 text-center\">Criar Novo Hacker</h2>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Nome do Hacker\n              </label>\n              <input\n                type=\"text\"\n                value={nick}\n                onChange={(e) => setNick(e.target.value)}\n                placeholder=\"Digite seu nick\"\n                maxLength={20}\n                className=\"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none\"\n              />\n              <p className=\"text-xs text-gray-400 mt-1\">3-20 caracteres</p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Email\n              </label>\n              <input\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                placeholder=\"<EMAIL>\"\n                className=\"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none\"\n              />\n            </div>\n          </div>\n\n          <button\n            onClick={handleCreatePlayer}\n            disabled={isCreating || !nick.trim() || !email.trim()}\n            className=\"w-full mt-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors\"\n          >\n            {isCreating ? (\n              <div className=\"flex items-center justify-center space-x-2\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                <span>Criando...</span>\n              </div>\n            ) : (\n              'Iniciar Jornada'\n            )}\n          </button>\n        </div>\n\n        {/* Informações do jogo */}\n        <div className=\"mt-6 space-y-4\">\n          <div className=\"bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\">\n            <h3 className=\"font-semibold mb-2 flex items-center\">\n              🎯 Como Jogar\n            </h3>\n            <ul className=\"text-sm text-gray-300 space-y-1\">\n              <li>• Faça upgrades nos seus aplicativos</li>\n              <li>• Ganhe XP e suba de nível</li>\n              <li>• Escaneie a rede em busca de alvos</li>\n              <li>• Invada sistemas e ganhe dinheiro</li>\n            </ul>\n          </div>\n\n          <div className=\"bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\">\n            <h3 className=\"font-semibold mb-2 flex items-center\">\n              🛠️ Aplicativos Iniciais\n            </h3>\n            <div className=\"grid grid-cols-2 gap-2 text-xs\">\n              <div className=\"flex items-center space-x-2\">\n                <span>🛡️</span>\n                <span>AntiVirus</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span>🏦</span>\n                <span>BankGuard</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span>🔨</span>\n                <span>BruteForce</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span>⚙️</span>\n                <span>SDK</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span>🔥</span>\n                <span>Firewall</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span>🦠</span>\n                <span>Malware Kit</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span>🌐</span>\n                <span>ProxyVPN</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"px-4 py-2 text-center\">\n        <p className=\"text-xs text-gray-500\">\n          SHACK v1.0 - Simulador de Hacking Educacional\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default GameSetupPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,gBAAgB,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC,gBAAgB;IAAEC,KAAK;IAAEC;EAAU,CAAC,GAAGP,gBAAgB,CAAC,CAAC;EACjE,MAAM,CAACQ,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMe,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,CAAC,IAAI,CAACL,KAAK,CAACK,IAAI,CAAC,CAAC,EAAE;MACjCC,KAAK,CAAC,qCAAqC,CAAC;MAC5C;IACF;IAEA,IAAIR,IAAI,CAACS,MAAM,GAAG,CAAC,IAAIT,IAAI,CAACS,MAAM,GAAG,EAAE,EAAE;MACvCD,KAAK,CAAC,yCAAyC,CAAC;MAChD;IACF;IAEA,IAAI,CAACN,KAAK,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;MACxBF,KAAK,CAAC,mCAAmC,CAAC;MAC1C;IACF;IAEAH,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF,MAAMM,OAAO,GAAG,MAAMd,gBAAgB,CAACG,IAAI,CAACO,IAAI,CAAC,CAAC,EAAEL,KAAK,CAACK,IAAI,CAAC,CAAC,CAAC;MAEjE,IAAI,CAACI,OAAO,EAAE;QACZ;QACAN,aAAa,CAAC,KAAK,CAAC;MACtB;MACA;IACF,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdU,KAAK,CAAC,yCAAyC,CAAC;MAChDH,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,oBACEX,OAAA;IAAKkB,SAAS,EAAC,uFAAuF;IAAAC,QAAA,gBAEpGnB,OAAA;MAAKkB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnB,OAAA;QAAKkB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvCvB,OAAA;QAAIkB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClDvB,OAAA;QAAGkB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BnB,OAAA;QAAKkB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEnB,OAAA;UAAIkB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE7EvB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAAmB,QAAA,gBACEnB,OAAA;cAAOkB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvB,OAAA;cACEwB,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEnB,IAAK;cACZoB,QAAQ,EAAGC,CAAC,IAAKpB,OAAO,CAACoB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACzCI,WAAW,EAAC,iBAAiB;cAC7BC,SAAS,EAAE,EAAG;cACdZ,SAAS,EAAC;YAAmI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9I,CAAC,eACFvB,OAAA;cAAGkB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAENvB,OAAA;YAAAmB,QAAA,gBACEnB,OAAA;cAAOkB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvB,OAAA;cACEwB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEjB,KAAM;cACbkB,QAAQ,EAAGC,CAAC,IAAKlB,QAAQ,CAACkB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC1CI,WAAW,EAAC,eAAe;cAC3BX,SAAS,EAAC;YAAmI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvB,OAAA;UACE+B,OAAO,EAAEnB,kBAAmB;UAC5BoB,QAAQ,EAAEtB,UAAU,IAAI,CAACJ,IAAI,CAACO,IAAI,CAAC,CAAC,IAAI,CAACL,KAAK,CAACK,IAAI,CAAC,CAAE;UACtDK,SAAS,EAAC,2HAA2H;UAAAC,QAAA,EAEpIT,UAAU,gBACTV,OAAA;YAAKkB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDnB,OAAA;cAAKkB,SAAS,EAAC;YAA2D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjFvB,OAAA;cAAAmB,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,GAEN;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BnB,OAAA;UAAKkB,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtEnB,OAAA;YAAIkB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAErD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvB,OAAA;YAAIkB,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC7CnB,OAAA;cAAAmB,QAAA,EAAI;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7CvB,OAAA;cAAAmB,QAAA,EAAI;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnCvB,OAAA;cAAAmB,QAAA,EAAI;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CvB,OAAA;cAAAmB,QAAA,EAAI;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENvB,OAAA;UAAKkB,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtEnB,OAAA;YAAIkB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAErD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvB,OAAA;YAAKkB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CnB,OAAA;cAAKkB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CnB,OAAA;gBAAAmB,QAAA,EAAM;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChBvB,OAAA;gBAAAmB,QAAA,EAAM;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNvB,OAAA;cAAKkB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CnB,OAAA;gBAAAmB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACfvB,OAAA;gBAAAmB,QAAA,EAAM;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNvB,OAAA;cAAKkB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CnB,OAAA;gBAAAmB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACfvB,OAAA;gBAAAmB,QAAA,EAAM;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACNvB,OAAA;cAAKkB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CnB,OAAA;gBAAAmB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACfvB,OAAA;gBAAAmB,QAAA,EAAM;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACNvB,OAAA;cAAKkB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CnB,OAAA;gBAAAmB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACfvB,OAAA;gBAAAmB,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACNvB,OAAA;cAAKkB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CnB,OAAA;gBAAAmB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACfvB,OAAA;gBAAAmB,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNvB,OAAA;cAAKkB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CnB,OAAA;gBAAAmB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACfvB,OAAA;gBAAAmB,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCnB,OAAA;QAAGkB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CA9JID,aAAuB;EAAA,QACoBH,gBAAgB;AAAA;AAAAmC,EAAA,GAD3DhC,aAAuB;AAgK7B,eAAeA,aAAa;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}