{"ast": null, "code": "// Serviço para APIs do jogo que se conecta ao Flask backend\nclass GameApiService {\n  constructor() {\n    this.baseURL = 'http://localhost:5000';\n  }\n  async makeRequest(endpoint, options = {}) {\n    const defaultHeaders = {\n      'Content-Type': 'application/json'\n    };\n    const config = {\n      ...options,\n      credentials: 'include',\n      // Importante para sessões Flask\n      headers: {\n        ...defaultHeaders,\n        ...options.headers\n      }\n    };\n    try {\n      console.log(`🌐 Fazendo requisição para: ${this.baseURL}${endpoint}`);\n      const response = await fetch(`${this.baseURL}${endpoint}`, config);\n      console.log(`📡 Resposta recebida: ${response.status} ${response.statusText}`);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n      const data = await response.json();\n      console.log('📦 Dados recebidos:', data);\n      return data;\n    } catch (error) {\n      console.error(`❌ Erro na requisição para ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  // === AUTENTICAÇÃO ===\n  async login(credentials) {\n    return this.makeRequest('/api/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials)\n    });\n  }\n  async register(userData) {\n    return this.makeRequest('/api/auth/register', {\n      method: 'POST',\n      body: JSON.stringify(userData)\n    });\n  }\n  async checkAuth() {\n    return this.makeRequest('/api/auth/check');\n  }\n  async logout() {\n    return this.makeRequest('/api/auth/logout', {\n      method: 'POST'\n    });\n  }\n  async verifySession() {\n    return this.makeRequest('/api/auth/verify');\n  }\n\n  // === MÉTODO DE TESTE RÁPIDO ===\n  async quickLogin() {\n    try {\n      // Tenta fazer login com usuário de teste\n      console.log('🔐 Tentando login com usuário de teste...');\n      const loginResult = await this.login({\n        email: '<EMAIL>',\n        password: 'test123'\n      });\n      if (loginResult.sucesso) {\n        console.log('✅ Login realizado com sucesso:', loginResult);\n        return loginResult;\n      } else {\n        // Se login falhou, tenta criar usuário\n        console.log('👤 Usuário não existe, criando...');\n        const registerResult = await this.register({\n          email: '<EMAIL>',\n          password: 'test123',\n          nick: 'TestPlayer'\n        });\n        if (registerResult.sucesso) {\n          console.log('✅ Usuário criado e logado:', registerResult);\n          return registerResult;\n        } else {\n          throw new Error(registerResult.mensagem || 'Erro ao criar usuário');\n        }\n      }\n    } catch (error) {\n      console.error('❌ Erro no login rápido:', error);\n      throw error;\n    }\n  }\n\n  // === SCANNER ===\n  async scanTargets() {\n    return this.makeRequest('/api/scan');\n  }\n  async scanSpecificIP(ip) {\n    return this.makeRequest(`/api/scan/ip/${ip}`);\n  }\n\n  // === EXPLOITS ===\n  async exploitTarget(ip) {\n    return this.makeRequest(`/api/alvo/${ip}/exploit`, {\n      method: 'POST'\n    });\n  }\n\n  // === DADOS DO JOGADOR ===\n  async getPlayerData() {\n    return this.makeRequest('/api/jogador');\n  }\n\n  // === CHAT ===\n  async getChatMessages() {\n    return this.makeRequest('/api/chat/mensagens');\n  }\n  async sendChatMessage(message) {\n    return this.makeRequest('/api/chat/enviar', {\n      method: 'POST',\n      body: JSON.stringify({\n        mensagem: message\n      })\n    });\n  }\n\n  // === TRANSFERÊNCIAS ===\n  async transferMoney(targetNick, amount, description) {\n    return this.makeRequest('/api/transferir', {\n      method: 'POST',\n      body: JSON.stringify({\n        destinatario_nick: targetNick,\n        valor: amount,\n        descricao: description || 'Transferência via React'\n      })\n    });\n  }\n  async forceTransfer(targetUid, percentage) {\n    return this.makeRequest(`/api/alvo/${targetUid}/transferir`, {\n      method: 'POST',\n      body: JSON.stringify({\n        porcentagem: percentage\n      })\n    });\n  }\n  async getTransferHistory() {\n    return this.makeRequest('/api/transferencias/historico');\n  }\n\n  // === LOJA ===\n  async getShopItems() {\n    return this.makeRequest('/api/shop/items');\n  }\n  async buyShopItem(itemId) {\n    return this.makeRequest('/api/shop/comprar', {\n      method: 'POST',\n      body: JSON.stringify({\n        item_id: itemId\n      })\n    });\n  }\n\n  // === UPGRADES ===\n  async upgradeItem(item, quantity = 1) {\n    return this.makeRequest('/api/upgrade', {\n      method: 'POST',\n      body: JSON.stringify({\n        item: item,\n        quantidade: quantity\n      })\n    });\n  }\n  async getUpgradeCosts(item, currentLevel, quantity = 1) {\n    return this.makeRequest(`/api/upgrade/custo?item=${item}&nivel=${currentLevel}&quantidade=${quantity}`);\n  }\n  async getUpgradeInfo() {\n    return this.makeRequest('/api/upgrade/info');\n  }\n\n  // === RANKING ===\n  async getRanking() {\n    return this.makeRequest('/api/ranking');\n  }\n\n  // === LOGS ===\n  async getLogs() {\n    return this.makeRequest('/api/logs');\n  }\n  async getActivityLogs(limit = 50) {\n    return this.makeRequest(`/api/logs/atividades?limit=${limit}`);\n  }\n  async getAttackLogs() {\n    return this.makeRequest('/api/logs/ataques');\n  }\n\n  // === ECONOMIA E MINERAÇÃO ===\n  async collectResources() {\n    return this.makeRequest('/api/mineracao/coletar', {\n      method: 'POST'\n    });\n  }\n  async getMiningInfo() {\n    return this.makeRequest('/api/mineracao/info');\n  }\n  async upgradeMining() {\n    return this.makeRequest('/api/mineracao/upgrade', {\n      method: 'POST'\n    });\n  }\n\n  // === GRUPOS E TORNEIOS ===\n  async getGroupInfo() {\n    return this.makeRequest('/api/grupo/info');\n  }\n  async joinGroup(groupName) {\n    return this.makeRequest('/api/grupo/entrar', {\n      method: 'POST',\n      body: JSON.stringify({\n        nome_grupo: groupName\n      })\n    });\n  }\n  async leaveGroup() {\n    return this.makeRequest('/api/grupo/sair', {\n      method: 'POST'\n    });\n  }\n  async getTournamentInfo() {\n    return this.makeRequest('/api/torneio/info');\n  }\n  async getTournamentAppOfDay() {\n    return this.makeRequest('/api/torneio/app-do-dia');\n  }\n\n  // === MERCADO NEGRO ===\n  async getBlackMarketItems() {\n    return this.makeRequest('/api/mercado-negro/items');\n  }\n  async buyBlackMarketItem(itemId) {\n    return this.makeRequest('/api/mercado-negro/comprar', {\n      method: 'POST',\n      body: JSON.stringify({\n        item_id: itemId\n      })\n    });\n  }\n  async getPlayerRanking(uid) {\n    return this.makeRequest(`/api/ranking/jogador/${uid}`);\n  }\n\n  // === CONEXÕES ATIVAS ===\n  async getActiveConnections() {\n    return this.makeRequest('/api/conexoes/ativas');\n  }\n  async disconnectConnection(connectionId) {\n    return this.makeRequest(`/api/conexoes/${connectionId}/desconectar`, {\n      method: 'POST'\n    });\n  }\n\n  // === MINERAÇÃO ===\n  async getMiningStatus() {\n    return this.makeRequest('/api/mineracao/status');\n  }\n  async collectShacks() {\n    return this.makeRequest('/api/mineracao/coletar-shacks', {\n      method: 'POST'\n    });\n  }\n  async upgradeMiner() {\n    return this.makeRequest('/api/mineracao/upgrade', {\n      method: 'POST'\n    });\n  }\n\n  // === DEFACE SYSTEM ===\n  async performDeface(targetNick) {\n    return this.makeRequest('/api/deface', {\n      method: 'POST',\n      body: JSON.stringify({\n        alvo_nick: targetNick\n      })\n    });\n  }\n  async getDefaceRanking() {\n    return this.makeRequest('/api/deface/ranking');\n  }\n\n  // === HABILIDADES NFT ===\n  async getNFTSkills() {\n    return this.makeRequest('/api/habilidades/disponiveis');\n  }\n  async buyNFTSkill(skillId) {\n    return this.makeRequest('/api/habilidades/comprar', {\n      method: 'POST',\n      body: JSON.stringify({\n        habilidade_id: skillId\n      })\n    });\n  }\n\n  // === CONFIGURAÇÕES ===\n  async changePassword(data) {\n    return this.makeRequest('/api/auth/change-password', {\n      method: 'POST',\n      body: JSON.stringify(data)\n    });\n  }\n  async updateProfile(data) {\n    return this.makeRequest('/api/profile/update', {\n      method: 'POST',\n      body: JSON.stringify(data)\n    });\n  }\n\n  // === NOTÍCIAS ===\n  async getNews() {\n    return this.makeRequest('/api/news');\n  }\n\n  // === TERMINAL/BRUTEFORCE ===\n  async executeBruteforce(target, wordlist) {\n    return this.makeRequest('/api/bruteforce/execute', {\n      method: 'POST',\n      body: JSON.stringify({\n        target,\n        wordlist\n      })\n    });\n  }\n  async getBruteforceStatus() {\n    return this.makeRequest('/api/bruteforce/status');\n  }\n\n  // === SISTEMA BANCÁRIO ===\n  async getBankAccount() {\n    return this.makeRequest('/api/banco/conta');\n  }\n  async bankDeposit(amount) {\n    return this.makeRequest('/api/banco/depositar', {\n      method: 'POST',\n      body: JSON.stringify({\n        valor: amount\n      })\n    });\n  }\n  async bankWithdraw(amount) {\n    return this.makeRequest('/api/banco/sacar', {\n      method: 'POST',\n      body: JSON.stringify({\n        valor: amount\n      })\n    });\n  }\n  async getBankTransactions() {\n    return this.makeRequest('/api/banco/transacoes');\n  }\n  async collectBankInterest() {\n    return this.makeRequest('/api/banco/coletar-juros', {\n      method: 'POST'\n    });\n  }\n\n  // === TESTE DE CONECTIVIDADE ===\n  async testConnection() {\n    try {\n      const response = await fetch(`${this.baseURL}/`);\n      const data = await response.json();\n      return {\n        success: true,\n        data\n      };\n    } catch (error) {\n      console.error('❌ Erro de conectividade:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  }\n}\nexport const gameApi = new GameApiService();\nexport default gameApi;", "map": {"version": 3, "names": ["GameApiService", "constructor", "baseURL", "makeRequest", "endpoint", "options", "defaultHeaders", "config", "credentials", "headers", "console", "log", "response", "fetch", "status", "statusText", "ok", "Error", "data", "json", "error", "login", "method", "body", "JSON", "stringify", "register", "userData", "checkAuth", "logout", "verifySession", "quickLogin", "loginResult", "email", "password", "sucesso", "registerResult", "nick", "mensagem", "scanTargets", "scanSpecificIP", "ip", "exploitTarget", "getPlayerData", "getChatMessages", "sendChatMessage", "message", "transferMoney", "targetNick", "amount", "description", "destinatario_nick", "valor", "descricao", "forceTransfer", "targetUid", "percentage", "porcentagem", "getTransferHistory", "getShopItems", "buyShopItem", "itemId", "item_id", "upgradeItem", "item", "quantity", "quantidade", "getUpgradeCosts", "currentLevel", "getUpgradeInfo", "getRanking", "getLogs", "getActivityLogs", "limit", "getAttackLogs", "collectResources", "getMiningInfo", "upgradeMining", "getGroupInfo", "joinGroup", "groupName", "nome_grupo", "leaveGroup", "getTournamentInfo", "getTournamentAppOfDay", "getBlackMarketItems", "buyBlackMarketItem", "getPlayerRanking", "uid", "getActiveConnections", "disconnectConnection", "connectionId", "getMiningStatus", "collectShacks", "upgradeMiner", "performDeface", "alvo_nick", "getDefaceRanking", "getNFTSkills", "buyNFTSkill", "skillId", "habilidade_id", "changePassword", "updateProfile", "getNews", "executeBruteforce", "target", "wordlist", "getBruteforceStatus", "getBankAccount", "bankDeposit", "bankWithdraw", "getBankTransactions", "collectBankInterest", "testConnection", "success", "gameApi"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/gameApi.ts"], "sourcesContent": ["// Serviço para APIs do jogo que se conecta ao Flask backend\nclass GameApiService {\n  private baseURL = 'http://localhost:5000';\n  \n  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {\n    const defaultHeaders = {\n      'Content-Type': 'application/json',\n    };\n\n    const config: RequestInit = {\n      ...options,\n      credentials: 'include', // Importante para sessões Flask\n      headers: {\n        ...defaultHeaders,\n        ...options.headers,\n      },\n    };\n\n    try {\n      console.log(`🌐 Fazendo requisição para: ${this.baseURL}${endpoint}`);\n      \n      const response = await fetch(`${this.baseURL}${endpoint}`, config);\n      \n      console.log(`📡 Resposta recebida: ${response.status} ${response.statusText}`);\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      console.log('📦 Dados recebidos:', data);\n      \n      return data;\n    } catch (error) {\n      console.error(`❌ Erro na requisição para ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  // === AUTENTICAÇÃO ===\n  async login(credentials: { email: string; password: string }) {\n    return this.makeRequest('/api/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    });\n  }\n\n  async register(userData: { email: string; password: string; nick: string }) {\n    return this.makeRequest('/api/auth/register', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async checkAuth() {\n    return this.makeRequest('/api/auth/check');\n  }\n\n  async logout() {\n    return this.makeRequest('/api/auth/logout', {\n      method: 'POST',\n    });\n  }\n\n  async verifySession() {\n    return this.makeRequest('/api/auth/verify');\n  }\n\n  // === MÉTODO DE TESTE RÁPIDO ===\n  async quickLogin() {\n    try {\n      // Tenta fazer login com usuário de teste\n      console.log('🔐 Tentando login com usuário de teste...');\n\n      const loginResult = await this.login({\n        email: '<EMAIL>',\n        password: 'test123'\n      });\n\n      if (loginResult.sucesso) {\n        console.log('✅ Login realizado com sucesso:', loginResult);\n        return loginResult;\n      } else {\n        // Se login falhou, tenta criar usuário\n        console.log('👤 Usuário não existe, criando...');\n\n        const registerResult = await this.register({\n          email: '<EMAIL>',\n          password: 'test123',\n          nick: 'TestPlayer'\n        });\n\n        if (registerResult.sucesso) {\n          console.log('✅ Usuário criado e logado:', registerResult);\n          return registerResult;\n        } else {\n          throw new Error(registerResult.mensagem || 'Erro ao criar usuário');\n        }\n      }\n    } catch (error) {\n      console.error('❌ Erro no login rápido:', error);\n      throw error;\n    }\n  }\n\n  // === SCANNER ===\n  async scanTargets() {\n    return this.makeRequest('/api/scan');\n  }\n\n  async scanSpecificIP(ip: string) {\n    return this.makeRequest(`/api/scan/ip/${ip}`);\n  }\n\n  // === EXPLOITS ===\n  async exploitTarget(ip: string) {\n    return this.makeRequest(`/api/alvo/${ip}/exploit`, {\n      method: 'POST',\n    });\n  }\n\n  // === DADOS DO JOGADOR ===\n  async getPlayerData() {\n    return this.makeRequest('/api/jogador');\n  }\n\n  // === CHAT ===\n  async getChatMessages() {\n    return this.makeRequest('/api/chat/mensagens');\n  }\n\n  async sendChatMessage(message: string) {\n    return this.makeRequest('/api/chat/enviar', {\n      method: 'POST',\n      body: JSON.stringify({ mensagem: message }),\n    });\n  }\n\n  // === TRANSFERÊNCIAS ===\n  async transferMoney(targetNick: string, amount: number, description?: string) {\n    return this.makeRequest('/api/transferir', {\n      method: 'POST',\n      body: JSON.stringify({\n        destinatario_nick: targetNick,\n        valor: amount,\n        descricao: description || 'Transferência via React',\n      }),\n    });\n  }\n\n  async forceTransfer(targetUid: string, percentage: number) {\n    return this.makeRequest(`/api/alvo/${targetUid}/transferir`, {\n      method: 'POST',\n      body: JSON.stringify({\n        porcentagem: percentage,\n      }),\n    });\n  }\n\n  async getTransferHistory() {\n    return this.makeRequest('/api/transferencias/historico');\n  }\n\n  // === LOJA ===\n  async getShopItems() {\n    return this.makeRequest('/api/shop/items');\n  }\n\n  async buyShopItem(itemId: string) {\n    return this.makeRequest('/api/shop/comprar', {\n      method: 'POST',\n      body: JSON.stringify({ item_id: itemId }),\n    });\n  }\n\n  // === UPGRADES ===\n  async upgradeItem(item: string, quantity: number = 1) {\n    return this.makeRequest('/api/upgrade', {\n      method: 'POST',\n      body: JSON.stringify({\n        item: item,\n        quantidade: quantity,\n      }),\n    });\n  }\n\n  async getUpgradeCosts(item: string, currentLevel: number, quantity: number = 1) {\n    return this.makeRequest(`/api/upgrade/custo?item=${item}&nivel=${currentLevel}&quantidade=${quantity}`);\n  }\n\n  async getUpgradeInfo() {\n    return this.makeRequest('/api/upgrade/info');\n  }\n\n  // === RANKING ===\n  async getRanking() {\n    return this.makeRequest('/api/ranking');\n  }\n\n  // === LOGS ===\n  async getLogs() {\n    return this.makeRequest('/api/logs');\n  }\n\n  async getActivityLogs(limit: number = 50) {\n    return this.makeRequest(`/api/logs/atividades?limit=${limit}`);\n  }\n\n  async getAttackLogs() {\n    return this.makeRequest('/api/logs/ataques');\n  }\n\n  // === ECONOMIA E MINERAÇÃO ===\n  async collectResources() {\n    return this.makeRequest('/api/mineracao/coletar', {\n      method: 'POST',\n    });\n  }\n\n  async getMiningInfo() {\n    return this.makeRequest('/api/mineracao/info');\n  }\n\n  async upgradeMining() {\n    return this.makeRequest('/api/mineracao/upgrade', {\n      method: 'POST',\n    });\n  }\n\n  // === GRUPOS E TORNEIOS ===\n  async getGroupInfo() {\n    return this.makeRequest('/api/grupo/info');\n  }\n\n  async joinGroup(groupName: string) {\n    return this.makeRequest('/api/grupo/entrar', {\n      method: 'POST',\n      body: JSON.stringify({ nome_grupo: groupName }),\n    });\n  }\n\n  async leaveGroup() {\n    return this.makeRequest('/api/grupo/sair', {\n      method: 'POST',\n    });\n  }\n\n  async getTournamentInfo() {\n    return this.makeRequest('/api/torneio/info');\n  }\n\n  async getTournamentAppOfDay() {\n    return this.makeRequest('/api/torneio/app-do-dia');\n  }\n\n  // === MERCADO NEGRO ===\n  async getBlackMarketItems() {\n    return this.makeRequest('/api/mercado-negro/items');\n  }\n\n  async buyBlackMarketItem(itemId: string) {\n    return this.makeRequest('/api/mercado-negro/comprar', {\n      method: 'POST',\n      body: JSON.stringify({ item_id: itemId }),\n    });\n  }\n\n  async getPlayerRanking(uid: string) {\n    return this.makeRequest(`/api/ranking/jogador/${uid}`);\n  }\n\n  // === CONEXÕES ATIVAS ===\n  async getActiveConnections() {\n    return this.makeRequest('/api/conexoes/ativas');\n  }\n\n  async disconnectConnection(connectionId: string) {\n    return this.makeRequest(`/api/conexoes/${connectionId}/desconectar`, {\n      method: 'POST',\n    });\n  }\n\n  // === MINERAÇÃO ===\n  async getMiningStatus() {\n    return this.makeRequest('/api/mineracao/status');\n  }\n\n  async collectShacks() {\n    return this.makeRequest('/api/mineracao/coletar-shacks', {\n      method: 'POST',\n    });\n  }\n\n  async upgradeMiner() {\n    return this.makeRequest('/api/mineracao/upgrade', {\n      method: 'POST',\n    });\n  }\n\n  // === DEFACE SYSTEM ===\n  async performDeface(targetNick: string) {\n    return this.makeRequest('/api/deface', {\n      method: 'POST',\n      body: JSON.stringify({ alvo_nick: targetNick }),\n    });\n  }\n\n  async getDefaceRanking() {\n    return this.makeRequest('/api/deface/ranking');\n  }\n\n  // === HABILIDADES NFT ===\n  async getNFTSkills() {\n    return this.makeRequest('/api/habilidades/disponiveis');\n  }\n\n  async buyNFTSkill(skillId: string) {\n    return this.makeRequest('/api/habilidades/comprar', {\n      method: 'POST',\n      body: JSON.stringify({ habilidade_id: skillId }),\n    });\n  }\n\n  // === CONFIGURAÇÕES ===\n  async changePassword(data: { currentPassword: string; newPassword: string }) {\n    return this.makeRequest('/api/auth/change-password', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async updateProfile(data: { nick?: string; email?: string }) {\n    return this.makeRequest('/api/profile/update', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  // === NOTÍCIAS ===\n  async getNews() {\n    return this.makeRequest('/api/news');\n  }\n\n  // === TERMINAL/BRUTEFORCE ===\n  async executeBruteforce(target: string, wordlist: string) {\n    return this.makeRequest('/api/bruteforce/execute', {\n      method: 'POST',\n      body: JSON.stringify({ target, wordlist }),\n    });\n  }\n\n  async getBruteforceStatus() {\n    return this.makeRequest('/api/bruteforce/status');\n  }\n\n  // === SISTEMA BANCÁRIO ===\n  async getBankAccount() {\n    return this.makeRequest('/api/banco/conta');\n  }\n\n  async bankDeposit(amount: number) {\n    return this.makeRequest('/api/banco/depositar', {\n      method: 'POST',\n      body: JSON.stringify({ valor: amount }),\n    });\n  }\n\n  async bankWithdraw(amount: number) {\n    return this.makeRequest('/api/banco/sacar', {\n      method: 'POST',\n      body: JSON.stringify({ valor: amount }),\n    });\n  }\n\n  async getBankTransactions() {\n    return this.makeRequest('/api/banco/transacoes');\n  }\n\n  async collectBankInterest() {\n    return this.makeRequest('/api/banco/coletar-juros', {\n      method: 'POST',\n    });\n  }\n\n  // === TESTE DE CONECTIVIDADE ===\n  async testConnection() {\n    try {\n      const response = await fetch(`${this.baseURL}/`);\n      const data = await response.json();\n      return { success: true, data };\n    } catch (error) {\n      console.error('❌ Erro de conectividade:', error);\n      return { success: false, error };\n    }\n  }\n}\n\nexport const gameApi = new GameApiService();\nexport default gameApi;\n"], "mappings": "AAAA;AACA,MAAMA,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,OAAO,GAAG,uBAAuB;EAAA;EAEzC,MAAcC,WAAWA,CAACC,QAAgB,EAAEC,OAAoB,GAAG,CAAC,CAAC,EAAgB;IACnF,MAAMC,cAAc,GAAG;MACrB,cAAc,EAAE;IAClB,CAAC;IAED,MAAMC,MAAmB,GAAG;MAC1B,GAAGF,OAAO;MACVG,WAAW,EAAE,SAAS;MAAE;MACxBC,OAAO,EAAE;QACP,GAAGH,cAAc;QACjB,GAAGD,OAAO,CAACI;MACb;IACF,CAAC;IAED,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,+BAA+B,IAAI,CAACT,OAAO,GAAGE,QAAQ,EAAE,CAAC;MAErE,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACX,OAAO,GAAGE,QAAQ,EAAE,EAAEG,MAAM,CAAC;MAElEG,OAAO,CAACC,GAAG,CAAC,yBAAyBC,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACG,UAAU,EAAE,CAAC;MAE9E,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,QAAQL,QAAQ,CAACE,MAAM,KAAKF,QAAQ,CAACG,UAAU,EAAE,CAAC;MACpE;MAEA,MAAMG,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClCT,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,IAAI,CAAC;MAExC,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,6BAA6BhB,QAAQ,GAAG,EAAEgB,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMC,KAAKA,CAACb,WAAgD,EAAE;IAC5D,OAAO,IAAI,CAACL,WAAW,CAAC,iBAAiB,EAAE;MACzCmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjB,WAAW;IAClC,CAAC,CAAC;EACJ;EAEA,MAAMkB,QAAQA,CAACC,QAA2D,EAAE;IAC1E,OAAO,IAAI,CAACxB,WAAW,CAAC,oBAAoB,EAAE;MAC5CmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,QAAQ;IAC/B,CAAC,CAAC;EACJ;EAEA,MAAMC,SAASA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACzB,WAAW,CAAC,iBAAiB,CAAC;EAC5C;EAEA,MAAM0B,MAAMA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC1B,WAAW,CAAC,kBAAkB,EAAE;MAC1CmB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EAEA,MAAMQ,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC3B,WAAW,CAAC,kBAAkB,CAAC;EAC7C;;EAEA;EACA,MAAM4B,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF;MACArB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAExD,MAAMqB,WAAW,GAAG,MAAM,IAAI,CAACX,KAAK,CAAC;QACnCY,KAAK,EAAE,eAAe;QACtBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAIF,WAAW,CAACG,OAAO,EAAE;QACvBzB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqB,WAAW,CAAC;QAC1D,OAAOA,WAAW;MACpB,CAAC,MAAM;QACL;QACAtB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAEhD,MAAMyB,cAAc,GAAG,MAAM,IAAI,CAACV,QAAQ,CAAC;UACzCO,KAAK,EAAE,eAAe;UACtBC,QAAQ,EAAE,SAAS;UACnBG,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAID,cAAc,CAACD,OAAO,EAAE;UAC1BzB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyB,cAAc,CAAC;UACzD,OAAOA,cAAc;QACvB,CAAC,MAAM;UACL,MAAM,IAAInB,KAAK,CAACmB,cAAc,CAACE,QAAQ,IAAI,uBAAuB,CAAC;QACrE;MACF;IACF,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMmB,WAAWA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACpC,WAAW,CAAC,WAAW,CAAC;EACtC;EAEA,MAAMqC,cAAcA,CAACC,EAAU,EAAE;IAC/B,OAAO,IAAI,CAACtC,WAAW,CAAC,gBAAgBsC,EAAE,EAAE,CAAC;EAC/C;;EAEA;EACA,MAAMC,aAAaA,CAACD,EAAU,EAAE;IAC9B,OAAO,IAAI,CAACtC,WAAW,CAAC,aAAasC,EAAE,UAAU,EAAE;MACjDnB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMqB,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACxC,WAAW,CAAC,cAAc,CAAC;EACzC;;EAEA;EACA,MAAMyC,eAAeA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACzC,WAAW,CAAC,qBAAqB,CAAC;EAChD;EAEA,MAAM0C,eAAeA,CAACC,OAAe,EAAE;IACrC,OAAO,IAAI,CAAC3C,WAAW,CAAC,kBAAkB,EAAE;MAC1CmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEa,QAAQ,EAAEQ;MAAQ,CAAC;IAC5C,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,aAAaA,CAACC,UAAkB,EAAEC,MAAc,EAAEC,WAAoB,EAAE;IAC5E,OAAO,IAAI,CAAC/C,WAAW,CAAC,iBAAiB,EAAE;MACzCmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnB0B,iBAAiB,EAAEH,UAAU;QAC7BI,KAAK,EAAEH,MAAM;QACbI,SAAS,EAAEH,WAAW,IAAI;MAC5B,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,MAAMI,aAAaA,CAACC,SAAiB,EAAEC,UAAkB,EAAE;IACzD,OAAO,IAAI,CAACrD,WAAW,CAAC,aAAaoD,SAAS,aAAa,EAAE;MAC3DjC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBgC,WAAW,EAAED;MACf,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,MAAME,kBAAkBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACvD,WAAW,CAAC,+BAA+B,CAAC;EAC1D;;EAEA;EACA,MAAMwD,YAAYA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACxD,WAAW,CAAC,iBAAiB,CAAC;EAC5C;EAEA,MAAMyD,WAAWA,CAACC,MAAc,EAAE;IAChC,OAAO,IAAI,CAAC1D,WAAW,CAAC,mBAAmB,EAAE;MAC3CmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEqC,OAAO,EAAED;MAAO,CAAC;IAC1C,CAAC,CAAC;EACJ;;EAEA;EACA,MAAME,WAAWA,CAACC,IAAY,EAAEC,QAAgB,GAAG,CAAC,EAAE;IACpD,OAAO,IAAI,CAAC9D,WAAW,CAAC,cAAc,EAAE;MACtCmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBuC,IAAI,EAAEA,IAAI;QACVE,UAAU,EAAED;MACd,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,MAAME,eAAeA,CAACH,IAAY,EAAEI,YAAoB,EAAEH,QAAgB,GAAG,CAAC,EAAE;IAC9E,OAAO,IAAI,CAAC9D,WAAW,CAAC,2BAA2B6D,IAAI,UAAUI,YAAY,eAAeH,QAAQ,EAAE,CAAC;EACzG;EAEA,MAAMI,cAAcA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAClE,WAAW,CAAC,mBAAmB,CAAC;EAC9C;;EAEA;EACA,MAAMmE,UAAUA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACnE,WAAW,CAAC,cAAc,CAAC;EACzC;;EAEA;EACA,MAAMoE,OAAOA,CAAA,EAAG;IACd,OAAO,IAAI,CAACpE,WAAW,CAAC,WAAW,CAAC;EACtC;EAEA,MAAMqE,eAAeA,CAACC,KAAa,GAAG,EAAE,EAAE;IACxC,OAAO,IAAI,CAACtE,WAAW,CAAC,8BAA8BsE,KAAK,EAAE,CAAC;EAChE;EAEA,MAAMC,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACvE,WAAW,CAAC,mBAAmB,CAAC;EAC9C;;EAEA;EACA,MAAMwE,gBAAgBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACxE,WAAW,CAAC,wBAAwB,EAAE;MAChDmB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EAEA,MAAMsD,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACzE,WAAW,CAAC,qBAAqB,CAAC;EAChD;EAEA,MAAM0E,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC1E,WAAW,CAAC,wBAAwB,EAAE;MAChDmB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMwD,YAAYA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC3E,WAAW,CAAC,iBAAiB,CAAC;EAC5C;EAEA,MAAM4E,SAASA,CAACC,SAAiB,EAAE;IACjC,OAAO,IAAI,CAAC7E,WAAW,CAAC,mBAAmB,EAAE;MAC3CmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEwD,UAAU,EAAED;MAAU,CAAC;IAChD,CAAC,CAAC;EACJ;EAEA,MAAME,UAAUA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC/E,WAAW,CAAC,iBAAiB,EAAE;MACzCmB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EAEA,MAAM6D,iBAAiBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAAChF,WAAW,CAAC,mBAAmB,CAAC;EAC9C;EAEA,MAAMiF,qBAAqBA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAACjF,WAAW,CAAC,yBAAyB,CAAC;EACpD;;EAEA;EACA,MAAMkF,mBAAmBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAAClF,WAAW,CAAC,0BAA0B,CAAC;EACrD;EAEA,MAAMmF,kBAAkBA,CAACzB,MAAc,EAAE;IACvC,OAAO,IAAI,CAAC1D,WAAW,CAAC,4BAA4B,EAAE;MACpDmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEqC,OAAO,EAAED;MAAO,CAAC;IAC1C,CAAC,CAAC;EACJ;EAEA,MAAM0B,gBAAgBA,CAACC,GAAW,EAAE;IAClC,OAAO,IAAI,CAACrF,WAAW,CAAC,wBAAwBqF,GAAG,EAAE,CAAC;EACxD;;EAEA;EACA,MAAMC,oBAAoBA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAACtF,WAAW,CAAC,sBAAsB,CAAC;EACjD;EAEA,MAAMuF,oBAAoBA,CAACC,YAAoB,EAAE;IAC/C,OAAO,IAAI,CAACxF,WAAW,CAAC,iBAAiBwF,YAAY,cAAc,EAAE;MACnErE,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMsE,eAAeA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACzF,WAAW,CAAC,uBAAuB,CAAC;EAClD;EAEA,MAAM0F,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC1F,WAAW,CAAC,+BAA+B,EAAE;MACvDmB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EAEA,MAAMwE,YAAYA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC3F,WAAW,CAAC,wBAAwB,EAAE;MAChDmB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMyE,aAAaA,CAAC/C,UAAkB,EAAE;IACtC,OAAO,IAAI,CAAC7C,WAAW,CAAC,aAAa,EAAE;MACrCmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEuE,SAAS,EAAEhD;MAAW,CAAC;IAChD,CAAC,CAAC;EACJ;EAEA,MAAMiD,gBAAgBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAC9F,WAAW,CAAC,qBAAqB,CAAC;EAChD;;EAEA;EACA,MAAM+F,YAAYA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC/F,WAAW,CAAC,8BAA8B,CAAC;EACzD;EAEA,MAAMgG,WAAWA,CAACC,OAAe,EAAE;IACjC,OAAO,IAAI,CAACjG,WAAW,CAAC,0BAA0B,EAAE;MAClDmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE4E,aAAa,EAAED;MAAQ,CAAC;IACjD,CAAC,CAAC;EACJ;;EAEA;EACA,MAAME,cAAcA,CAACpF,IAAsD,EAAE;IAC3E,OAAO,IAAI,CAACf,WAAW,CAAC,2BAA2B,EAAE;MACnDmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,IAAI;IAC3B,CAAC,CAAC;EACJ;EAEA,MAAMqF,aAAaA,CAACrF,IAAuC,EAAE;IAC3D,OAAO,IAAI,CAACf,WAAW,CAAC,qBAAqB,EAAE;MAC7CmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,IAAI;IAC3B,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMsF,OAAOA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrG,WAAW,CAAC,WAAW,CAAC;EACtC;;EAEA;EACA,MAAMsG,iBAAiBA,CAACC,MAAc,EAAEC,QAAgB,EAAE;IACxD,OAAO,IAAI,CAACxG,WAAW,CAAC,yBAAyB,EAAE;MACjDmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEiF,MAAM;QAAEC;MAAS,CAAC;IAC3C,CAAC,CAAC;EACJ;EAEA,MAAMC,mBAAmBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACzG,WAAW,CAAC,wBAAwB,CAAC;EACnD;;EAEA;EACA,MAAM0G,cAAcA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC1G,WAAW,CAAC,kBAAkB,CAAC;EAC7C;EAEA,MAAM2G,WAAWA,CAAC7D,MAAc,EAAE;IAChC,OAAO,IAAI,CAAC9C,WAAW,CAAC,sBAAsB,EAAE;MAC9CmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE2B,KAAK,EAAEH;MAAO,CAAC;IACxC,CAAC,CAAC;EACJ;EAEA,MAAM8D,YAAYA,CAAC9D,MAAc,EAAE;IACjC,OAAO,IAAI,CAAC9C,WAAW,CAAC,kBAAkB,EAAE;MAC1CmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE2B,KAAK,EAAEH;MAAO,CAAC;IACxC,CAAC,CAAC;EACJ;EAEA,MAAM+D,mBAAmBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAAC7G,WAAW,CAAC,uBAAuB,CAAC;EAClD;EAEA,MAAM8G,mBAAmBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAAC9G,WAAW,CAAC,0BAA0B,EAAE;MAClDmB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;EACA,MAAM4F,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAMtG,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACX,OAAO,GAAG,CAAC;MAChD,MAAMgB,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC,OAAO;QAAEgG,OAAO,EAAE,IAAI;QAAEjG;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QAAE+F,OAAO,EAAE,KAAK;QAAE/F;MAAM,CAAC;IAClC;EACF;AACF;AAEA,OAAO,MAAMgG,OAAO,GAAG,IAAIpH,cAAc,CAAC,CAAC;AAC3C,eAAeoH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}