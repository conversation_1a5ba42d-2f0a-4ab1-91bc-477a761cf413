{"ast": null, "code": "import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { GAME_CONFIG, calculateUpgradeCost } from '../types/game';\nimport { backendService } from '../services/backendService';\nconst initialPlayerApps = {\n  ...GAME_CONFIG.INITIAL_APPS\n};\nexport const useHackGameStore = create()(persist((set, get) => ({\n  // Initial state\n  player: null,\n  playerApps: initialPlayerApps,\n  isOnline: false,\n  notifications: [],\n  availableTargets: [],\n  hackHistory: [],\n  currentScreen: 'home',\n  isLoading: false,\n  error: null,\n  // Actions\n  loginPlayer: async (email, password) => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const result = await backendService.login(email, password);\n      if (result.success) {\n        // Carregar dados do jogador após login\n        const success = await get().loadPlayer();\n        return success;\n      }\n      set({\n        error: result.error || 'Erro ao fazer login',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  registerPlayer: async (nick, email, password) => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const result = await backendService.register(nick, email, password);\n      if (result.success) {\n        set({\n          isLoading: false\n        });\n        return true;\n      }\n      set({\n        error: result.error || 'Erro ao registrar jogador',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  loadPlayer: async () => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const result = await backendService.getPlayer();\n      if (result.success && result.player) {\n        const player = backendService.convertBackendPlayer(result.player);\n        const playerApps = backendService.convertBackendApps(result.player);\n        set({\n          player,\n          playerApps,\n          isOnline: true,\n          isLoading: false\n        });\n        return true;\n      }\n      set({\n        error: result.error || 'Erro ao carregar jogador',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  upgradeApp: async appId => {\n    const state = get();\n    const {\n      player,\n      playerApps\n    } = state;\n    if (!player) return false;\n    const currentLevel = playerApps[appId];\n    const upgradeCost = calculateUpgradeCost(currentLevel);\n\n    // Verificar se tem dinheiro suficiente\n    if (player.cash < upgradeCost) {\n      set({\n        error: 'Dinheiro insuficiente para upgrade!'\n      });\n      return false;\n    }\n\n    // Verificar se não atingiu o nível máximo\n    if (currentLevel >= GAME_CONFIG.MAX_APP_LEVEL) {\n      set({\n        error: 'App já está no nível máximo!'\n      });\n      return false;\n    }\n    try {\n      set({\n        isLoading: true\n      });\n\n      // Fazer upgrade no backend\n      const result = await backendService.upgradeApp(appId, 1);\n      if (result.success && result.data) {\n        // Recarregar dados do jogador para ter os valores atualizados\n        await get().loadPlayer();\n\n        // Adicionar notificação local\n        get().addNotification({\n          type: 'upgrade',\n          title: 'Upgrade Concluído!',\n          message: result.data.mensagem || `${appId} foi atualizado!`\n        });\n\n        // Verificar se subiu de nível\n        if (result.data.level_up) {\n          get().addNotification({\n            type: 'level',\n            title: 'Level Up!',\n            message: `Parabéns! Você atingiu o nível ${result.data.nivel_jogador}!`\n          });\n        }\n        set({\n          isLoading: false\n        });\n        return true;\n      }\n      set({\n        error: result.error || 'Erro ao realizar upgrade',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  setCurrentScreen: screen => {\n    set({\n      currentScreen: screen\n    });\n  },\n  loadNotifications: async () => {\n    const state = get();\n    if (!state.player) return;\n    try {\n      const result = await gameService.getNotifications(state.player.id);\n      if (result.success && result.notifications) {\n        set({\n          notifications: result.notifications\n        });\n      }\n    } catch (error) {\n      set({\n        error: error.message\n      });\n    }\n  },\n  addNotification: async notification => {\n    const state = get();\n    if (!state.player) return;\n    try {\n      const result = await gameService.createNotification(state.player.id, notification);\n      if (result.success) {\n        // Recarregar notificações\n        await get().loadNotifications();\n      }\n    } catch (error) {\n      set({\n        error: error.message\n      });\n    }\n  },\n  markNotificationRead: async id => {\n    try {\n      const result = await gameService.markNotificationRead(id);\n      if (result.success) {\n        set(state => ({\n          notifications: state.notifications.map(notif => notif.id === id ? {\n            ...notif,\n            read: true\n          } : notif)\n        }));\n      }\n    } catch (error) {\n      set({\n        error: error.message\n      });\n    }\n  },\n  clearNotifications: () => {\n    set({\n      notifications: []\n    });\n  },\n  setError: error => {\n    set({\n      error\n    });\n    if (error) {\n      // Limpar erro após 5 segundos\n      setTimeout(() => {\n        set({\n          error: null\n        });\n      }, 5000);\n    }\n  },\n  setLoading: loading => {\n    set({\n      isLoading: loading\n    });\n  },\n  syncWithServer: async () => {\n    const state = get();\n    if (!state.player) return;\n    try {\n      set({\n        isLoading: true\n      });\n\n      // Recarregar dados do jogador\n      await get().loadPlayer(state.player.id);\n      set({\n        isLoading: false\n      });\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n    }\n  },\n  reset: () => {\n    set({\n      player: null,\n      playerApps: initialPlayerApps,\n      isOnline: false,\n      notifications: [],\n      availableTargets: [],\n      hackHistory: [],\n      currentScreen: 'home',\n      isLoading: false,\n      error: null\n    });\n  }\n}), {\n  name: 'shack-game-storage',\n  partialize: state => ({\n    player: state.player,\n    currentScreen: state.currentScreen\n  }),\n  // Recarregar dados do servidor quando a store for hidratada\n  onRehydrateStorage: () => state => {\n    var _state$player;\n    if (state !== null && state !== void 0 && (_state$player = state.player) !== null && _state$player !== void 0 && _state$player.id) {\n      // Recarregar dados do servidor em background\n      setTimeout(() => {\n        state.syncWithServer();\n      }, 1000);\n    }\n  }\n}));", "map": {"version": 3, "names": ["create", "persist", "GAME_CONFIG", "calculateUpgradeCost", "backendService", "initialPlayerApps", "INITIAL_APPS", "useHackGameStore", "set", "get", "player", "playerApps", "isOnline", "notifications", "availableTargets", "hack<PERSON><PERSON><PERSON>", "currentScreen", "isLoading", "error", "loginPlayer", "email", "password", "result", "login", "success", "loadPlayer", "message", "registerPlayer", "nick", "register", "getPlayer", "convertBackendPlayer", "convertBackendApps", "upgradeApp", "appId", "state", "currentLevel", "upgradeCost", "cash", "MAX_APP_LEVEL", "data", "addNotification", "type", "title", "mensagem", "level_up", "nivel_jogador", "setCurrentScreen", "screen", "loadNotifications", "gameService", "getNotifications", "id", "notification", "createNotification", "markNotificationRead", "map", "notif", "read", "clearNotifications", "setError", "setTimeout", "setLoading", "loading", "syncWithServer", "reset", "name", "partialize", "onRehydrateStorage", "_state$player"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/hackGameStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport {\n  GAME_CONFIG,\n  getPlayerLevel,\n  getXpForNextLevel,\n  calculateUpgradeCost,\n  calculateXpReward,\n  Player,\n  PlayerApps,\n  GameNotification,\n  HackTarget\n} from '../types/game';\nimport { backendService, BackendPlayer } from '../services/backendService';\n\ninterface GameState {\n  // Player data\n  player: Player | null;\n  playerApps: PlayerApps;\n  \n  // Game state\n  isOnline: boolean;\n  notifications: GameNotification[];\n  \n  // Targets and hacking\n  availableTargets: HackTarget[];\n  hackHistory: any[];\n  \n  // UI state\n  currentScreen: 'home' | 'apps' | 'scanner' | 'terminal' | 'profile' | 'settings' | 'shop' | 'ranking' | 'logs' | 'chat';\n  isLoading: boolean;\n  error: string | null;\n  \n  // Actions\n  loginPlayer: (email: string, password: string) => Promise<boolean>;\n  registerPlayer: (nick: string, email: string, password: string) => Promise<boolean>;\n  loadPlayer: () => Promise<boolean>;\n  upgradeApp: (appId: keyof PlayerApps) => Promise<boolean>;\n  setCurrentScreen: (screen: GameState['currentScreen']) => void;\n  addNotification: (notification: Omit<GameNotification, 'id' | 'timestamp'>) => void;\n  markNotificationRead: (id: string) => void;\n  clearNotifications: () => void;\n  setError: (error: string | null) => void;\n  setLoading: (loading: boolean) => void;\n  syncWithServer: () => Promise<void>;\n  logout: () => void;\n  reset: () => void;\n}\n\nconst initialPlayerApps: PlayerApps = { ...GAME_CONFIG.INITIAL_APPS };\n\nexport const useHackGameStore = create<GameState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      player: null,\n      playerApps: initialPlayerApps,\n      isOnline: false,\n      notifications: [],\n      availableTargets: [],\n      hackHistory: [],\n      currentScreen: 'home',\n      isLoading: false,\n      error: null,\n\n      // Actions\n      loginPlayer: async (email: string, password: string) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const result = await backendService.login(email, password);\n\n          if (result.success) {\n            // Carregar dados do jogador após login\n            const success = await get().loadPlayer();\n            return success;\n          }\n\n          set({ error: result.error || 'Erro ao fazer login', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      registerPlayer: async (nick: string, email: string, password: string) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const result = await backendService.register(nick, email, password);\n\n          if (result.success) {\n            set({ isLoading: false });\n            return true;\n          }\n\n          set({ error: result.error || 'Erro ao registrar jogador', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      loadPlayer: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const result = await backendService.getPlayer();\n\n          if (result.success && result.player) {\n            const player = backendService.convertBackendPlayer(result.player);\n            const playerApps = backendService.convertBackendApps(result.player);\n\n            set({\n              player,\n              playerApps,\n              isOnline: true,\n              isLoading: false,\n            });\n\n            return true;\n          }\n\n          set({ error: result.error || 'Erro ao carregar jogador', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      upgradeApp: async (appId: keyof PlayerApps) => {\n        const state = get();\n        const { player, playerApps } = state;\n\n        if (!player) return false;\n\n        const currentLevel = playerApps[appId];\n        const upgradeCost = calculateUpgradeCost(currentLevel);\n\n        // Verificar se tem dinheiro suficiente\n        if (player.cash < upgradeCost) {\n          set({ error: 'Dinheiro insuficiente para upgrade!' });\n          return false;\n        }\n\n        // Verificar se não atingiu o nível máximo\n        if (currentLevel >= GAME_CONFIG.MAX_APP_LEVEL) {\n          set({ error: 'App já está no nível máximo!' });\n          return false;\n        }\n\n        try {\n          set({ isLoading: true });\n\n          // Fazer upgrade no backend\n          const result = await backendService.upgradeApp(appId, 1);\n\n          if (result.success && result.data) {\n            // Recarregar dados do jogador para ter os valores atualizados\n            await get().loadPlayer();\n\n            // Adicionar notificação local\n            get().addNotification({\n              type: 'upgrade',\n              title: 'Upgrade Concluído!',\n              message: result.data.mensagem || `${appId} foi atualizado!`,\n            });\n\n            // Verificar se subiu de nível\n            if (result.data.level_up) {\n              get().addNotification({\n                type: 'level',\n                title: 'Level Up!',\n                message: `Parabéns! Você atingiu o nível ${result.data.nivel_jogador}!`,\n              });\n            }\n\n            set({ isLoading: false });\n            return true;\n          }\n\n          set({ error: result.error || 'Erro ao realizar upgrade', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n\n\n      setCurrentScreen: (screen: GameState['currentScreen']) => {\n        set({ currentScreen: screen });\n      },\n\n      loadNotifications: async () => {\n        const state = get();\n        if (!state.player) return;\n\n        try {\n          const result = await gameService.getNotifications(state.player.id);\n\n          if (result.success && result.notifications) {\n            set({ notifications: result.notifications });\n          }\n        } catch (error: any) {\n          set({ error: error.message });\n        }\n      },\n\n      addNotification: async (notification: Omit<GameNotification, 'id' | 'player_id' | 'created_at' | 'read'>) => {\n        const state = get();\n        if (!state.player) return;\n\n        try {\n          const result = await gameService.createNotification(state.player.id, notification);\n\n          if (result.success) {\n            // Recarregar notificações\n            await get().loadNotifications();\n          }\n        } catch (error: any) {\n          set({ error: error.message });\n        }\n      },\n\n      markNotificationRead: async (id: string) => {\n        try {\n          const result = await gameService.markNotificationRead(id);\n\n          if (result.success) {\n            set((state) => ({\n              notifications: state.notifications.map(notif =>\n                notif.id === id ? { ...notif, read: true } : notif\n              )\n            }));\n          }\n        } catch (error: any) {\n          set({ error: error.message });\n        }\n      },\n\n      clearNotifications: () => {\n        set({ notifications: [] });\n      },\n\n      setError: (error: string | null) => {\n        set({ error });\n        if (error) {\n          // Limpar erro após 5 segundos\n          setTimeout(() => {\n            set({ error: null });\n          }, 5000);\n        }\n      },\n\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n\n      syncWithServer: async () => {\n        const state = get();\n        if (!state.player) return;\n\n        try {\n          set({ isLoading: true });\n\n          // Recarregar dados do jogador\n          await get().loadPlayer(state.player.id);\n\n          set({ isLoading: false });\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n        }\n      },\n\n      reset: () => {\n        set({\n          player: null,\n          playerApps: initialPlayerApps,\n          isOnline: false,\n          notifications: [],\n          availableTargets: [],\n          hackHistory: [],\n          currentScreen: 'home',\n          isLoading: false,\n          error: null,\n        });\n      },\n    }),\n    {\n      name: 'shack-game-storage',\n      partialize: (state) => ({\n        player: state.player,\n        currentScreen: state.currentScreen,\n      }),\n      // Recarregar dados do servidor quando a store for hidratada\n      onRehydrateStorage: () => (state) => {\n        if (state?.player?.id) {\n          // Recarregar dados do servidor em background\n          setTimeout(() => {\n            state.syncWithServer();\n          }, 1000);\n        }\n      },\n    }\n  )\n);\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SACEC,WAAW,EAGXC,oBAAoB,QAMf,eAAe;AACtB,SAASC,cAAc,QAAuB,4BAA4B;AAoC1E,MAAMC,iBAA6B,GAAG;EAAE,GAAGH,WAAW,CAACI;AAAa,CAAC;AAErE,OAAO,MAAMC,gBAAgB,GAAGP,MAAM,CAAY,CAAC,CACjDC,OAAO,CACL,CAACO,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAEN,iBAAiB;EAC7BO,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE,EAAE;EACjBC,gBAAgB,EAAE,EAAE;EACpBC,WAAW,EAAE,EAAE;EACfC,aAAa,EAAE,MAAM;EACrBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEX;EACAC,WAAW,EAAE,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAK;IACtDb,GAAG,CAAC;MAAES,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMlB,cAAc,CAACmB,KAAK,CAACH,KAAK,EAAEC,QAAQ,CAAC;MAE1D,IAAIC,MAAM,CAACE,OAAO,EAAE;QAClB;QACA,MAAMA,OAAO,GAAG,MAAMf,GAAG,CAAC,CAAC,CAACgB,UAAU,CAAC,CAAC;QACxC,OAAOD,OAAO;MAChB;MAEAhB,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI,qBAAqB;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MACvE,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ,OAAO;QAAET,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAEDU,cAAc,EAAE,MAAAA,CAAOC,IAAY,EAAER,KAAa,EAAEC,QAAgB,KAAK;IACvEb,GAAG,CAAC;MAAES,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMlB,cAAc,CAACyB,QAAQ,CAACD,IAAI,EAAER,KAAK,EAAEC,QAAQ,CAAC;MAEnE,IAAIC,MAAM,CAACE,OAAO,EAAE;QAClBhB,GAAG,CAAC;UAAES,SAAS,EAAE;QAAM,CAAC,CAAC;QACzB,OAAO,IAAI;MACb;MAEAT,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI,2BAA2B;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MAC7E,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ,OAAO;QAAET,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAEDQ,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtBjB,GAAG,CAAC;MAAES,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMlB,cAAc,CAAC0B,SAAS,CAAC,CAAC;MAE/C,IAAIR,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACZ,MAAM,EAAE;QACnC,MAAMA,MAAM,GAAGN,cAAc,CAAC2B,oBAAoB,CAACT,MAAM,CAACZ,MAAM,CAAC;QACjE,MAAMC,UAAU,GAAGP,cAAc,CAAC4B,kBAAkB,CAACV,MAAM,CAACZ,MAAM,CAAC;QAEnEF,GAAG,CAAC;UACFE,MAAM;UACNC,UAAU;UACVC,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;QACb,CAAC,CAAC;QAEF,OAAO,IAAI;MACb;MAEAT,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI,0BAA0B;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MAC5E,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ,OAAO;QAAET,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAEDgB,UAAU,EAAE,MAAOC,KAAuB,IAAK;IAC7C,MAAMC,KAAK,GAAG1B,GAAG,CAAC,CAAC;IACnB,MAAM;MAAEC,MAAM;MAAEC;IAAW,CAAC,GAAGwB,KAAK;IAEpC,IAAI,CAACzB,MAAM,EAAE,OAAO,KAAK;IAEzB,MAAM0B,YAAY,GAAGzB,UAAU,CAACuB,KAAK,CAAC;IACtC,MAAMG,WAAW,GAAGlC,oBAAoB,CAACiC,YAAY,CAAC;;IAEtD;IACA,IAAI1B,MAAM,CAAC4B,IAAI,GAAGD,WAAW,EAAE;MAC7B7B,GAAG,CAAC;QAAEU,KAAK,EAAE;MAAsC,CAAC,CAAC;MACrD,OAAO,KAAK;IACd;;IAEA;IACA,IAAIkB,YAAY,IAAIlC,WAAW,CAACqC,aAAa,EAAE;MAC7C/B,GAAG,CAAC;QAAEU,KAAK,EAAE;MAA+B,CAAC,CAAC;MAC9C,OAAO,KAAK;IACd;IAEA,IAAI;MACFV,GAAG,CAAC;QAAES,SAAS,EAAE;MAAK,CAAC,CAAC;;MAExB;MACA,MAAMK,MAAM,GAAG,MAAMlB,cAAc,CAAC6B,UAAU,CAACC,KAAK,EAAE,CAAC,CAAC;MAExD,IAAIZ,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACkB,IAAI,EAAE;QACjC;QACA,MAAM/B,GAAG,CAAC,CAAC,CAACgB,UAAU,CAAC,CAAC;;QAExB;QACAhB,GAAG,CAAC,CAAC,CAACgC,eAAe,CAAC;UACpBC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,oBAAoB;UAC3BjB,OAAO,EAAEJ,MAAM,CAACkB,IAAI,CAACI,QAAQ,IAAI,GAAGV,KAAK;QAC3C,CAAC,CAAC;;QAEF;QACA,IAAIZ,MAAM,CAACkB,IAAI,CAACK,QAAQ,EAAE;UACxBpC,GAAG,CAAC,CAAC,CAACgC,eAAe,CAAC;YACpBC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,WAAW;YAClBjB,OAAO,EAAE,kCAAkCJ,MAAM,CAACkB,IAAI,CAACM,aAAa;UACtE,CAAC,CAAC;QACJ;QAEAtC,GAAG,CAAC;UAAES,SAAS,EAAE;QAAM,CAAC,CAAC;QACzB,OAAO,IAAI;MACb;MAEAT,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI,0BAA0B;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MAC5E,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ,OAAO;QAAET,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAID8B,gBAAgB,EAAGC,MAAkC,IAAK;IACxDxC,GAAG,CAAC;MAAEQ,aAAa,EAAEgC;IAAO,CAAC,CAAC;EAChC,CAAC;EAEDC,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,MAAMd,KAAK,GAAG1B,GAAG,CAAC,CAAC;IACnB,IAAI,CAAC0B,KAAK,CAACzB,MAAM,EAAE;IAEnB,IAAI;MACF,MAAMY,MAAM,GAAG,MAAM4B,WAAW,CAACC,gBAAgB,CAAChB,KAAK,CAACzB,MAAM,CAAC0C,EAAE,CAAC;MAElE,IAAI9B,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACT,aAAa,EAAE;QAC1CL,GAAG,CAAC;UAAEK,aAAa,EAAES,MAAM,CAACT;QAAc,CAAC,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOK,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ;MAAQ,CAAC,CAAC;IAC/B;EACF,CAAC;EAEDe,eAAe,EAAE,MAAOY,YAAgF,IAAK;IAC3G,MAAMlB,KAAK,GAAG1B,GAAG,CAAC,CAAC;IACnB,IAAI,CAAC0B,KAAK,CAACzB,MAAM,EAAE;IAEnB,IAAI;MACF,MAAMY,MAAM,GAAG,MAAM4B,WAAW,CAACI,kBAAkB,CAACnB,KAAK,CAACzB,MAAM,CAAC0C,EAAE,EAAEC,YAAY,CAAC;MAElF,IAAI/B,MAAM,CAACE,OAAO,EAAE;QAClB;QACA,MAAMf,GAAG,CAAC,CAAC,CAACwC,iBAAiB,CAAC,CAAC;MACjC;IACF,CAAC,CAAC,OAAO/B,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ;MAAQ,CAAC,CAAC;IAC/B;EACF,CAAC;EAED6B,oBAAoB,EAAE,MAAOH,EAAU,IAAK;IAC1C,IAAI;MACF,MAAM9B,MAAM,GAAG,MAAM4B,WAAW,CAACK,oBAAoB,CAACH,EAAE,CAAC;MAEzD,IAAI9B,MAAM,CAACE,OAAO,EAAE;QAClBhB,GAAG,CAAE2B,KAAK,KAAM;UACdtB,aAAa,EAAEsB,KAAK,CAACtB,aAAa,CAAC2C,GAAG,CAACC,KAAK,IAC1CA,KAAK,CAACL,EAAE,KAAKA,EAAE,GAAG;YAAE,GAAGK,KAAK;YAAEC,IAAI,EAAE;UAAK,CAAC,GAAGD,KAC/C;QACF,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOvC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ;MAAQ,CAAC,CAAC;IAC/B;EACF,CAAC;EAEDiC,kBAAkB,EAAEA,CAAA,KAAM;IACxBnD,GAAG,CAAC;MAAEK,aAAa,EAAE;IAAG,CAAC,CAAC;EAC5B,CAAC;EAED+C,QAAQ,EAAG1C,KAAoB,IAAK;IAClCV,GAAG,CAAC;MAAEU;IAAM,CAAC,CAAC;IACd,IAAIA,KAAK,EAAE;MACT;MACA2C,UAAU,CAAC,MAAM;QACfrD,GAAG,CAAC;UAAEU,KAAK,EAAE;QAAK,CAAC,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED4C,UAAU,EAAGC,OAAgB,IAAK;IAChCvD,GAAG,CAAC;MAAES,SAAS,EAAE8C;IAAQ,CAAC,CAAC;EAC7B,CAAC;EAEDC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,MAAM7B,KAAK,GAAG1B,GAAG,CAAC,CAAC;IACnB,IAAI,CAAC0B,KAAK,CAACzB,MAAM,EAAE;IAEnB,IAAI;MACFF,GAAG,CAAC;QAAES,SAAS,EAAE;MAAK,CAAC,CAAC;;MAExB;MACA,MAAMR,GAAG,CAAC,CAAC,CAACgB,UAAU,CAACU,KAAK,CAACzB,MAAM,CAAC0C,EAAE,CAAC;MAEvC5C,GAAG,CAAC;QAAES,SAAS,EAAE;MAAM,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ,OAAO;QAAET,SAAS,EAAE;MAAM,CAAC,CAAC;IACjD;EACF,CAAC;EAEDgD,KAAK,EAAEA,CAAA,KAAM;IACXzD,GAAG,CAAC;MACFE,MAAM,EAAE,IAAI;MACZC,UAAU,EAAEN,iBAAiB;MAC7BO,QAAQ,EAAE,KAAK;MACfC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,EAAE;MACpBC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,EACF;EACEgD,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAGhC,KAAK,KAAM;IACtBzB,MAAM,EAAEyB,KAAK,CAACzB,MAAM;IACpBM,aAAa,EAAEmB,KAAK,CAACnB;EACvB,CAAC,CAAC;EACF;EACAoD,kBAAkB,EAAEA,CAAA,KAAOjC,KAAK,IAAK;IAAA,IAAAkC,aAAA;IACnC,IAAIlC,KAAK,aAALA,KAAK,gBAAAkC,aAAA,GAALlC,KAAK,CAAEzB,MAAM,cAAA2D,aAAA,eAAbA,aAAA,CAAejB,EAAE,EAAE;MACrB;MACAS,UAAU,CAAC,MAAM;QACf1B,KAAK,CAAC6B,cAAc,CAAC,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC;IACV;EACF;AACF,CACF,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}