{"ast": null, "code": "\"use client\";\n\n// src/useMutation.ts\nimport * as React from \"react\";\nimport { MutationObserver, noop, notify<PERSON><PERSON><PERSON>, shouldThrowError } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nfunction useMutation(options, queryClient) {\n  const client = useQueryClient(queryClient);\n  const [observer] = React.useState(() => new MutationObserver(client, options));\n  React.useEffect(() => {\n    observer.setOptions(options);\n  }, [observer, options]);\n  const result = React.useSyncExternalStore(React.useCallback(onStoreChange => observer.subscribe(notifyManager.batchCalls(onStoreChange)), [observer]), () => observer.getCurrentResult(), () => observer.getCurrentResult());\n  const mutate = React.useCallback((variables, mutateOptions) => {\n    observer.mutate(variables, mutateOptions).catch(noop);\n  }, [observer]);\n  if (result.error && shouldThrowError(observer.options.throwOnError, [result.error])) {\n    throw result.error;\n  }\n  return {\n    ...result,\n    mutate,\n    mutateAsync: result.mutate\n  };\n}\nexport { useMutation };", "map": {"version": 3, "names": ["React", "MutationObserver", "noop", "notify<PERSON><PERSON>ger", "shouldThrowError", "useQueryClient", "useMutation", "options", "queryClient", "client", "observer", "useState", "useEffect", "setOptions", "result", "useSyncExternalStore", "useCallback", "onStoreChange", "subscribe", "batchCalls", "getCurrentResult", "mutate", "variables", "mutateOptions", "catch", "error", "throwOnError", "mutateAsync"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\react-query\\src\\useMutation.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport {\n  MutationObserver,\n  noop,\n  notifyManager,\n  shouldThrowError,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\nimport type { DefaultError, QueryClient } from '@tanstack/query-core'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n  queryClient?: QueryClient,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const client = useQueryClient(queryClient)\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        client,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.throwOnError, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AACvB,SACEC,gBAAA,EACAC,IAAA,EACAC,aAAA,EACAC,gBAAA,QACK;AACP,SAASC,cAAA,QAAsB;AAUxB,SAASC,YAMdC,OAAA,EACAC,WAAA,EACwD;EACxD,MAAMC,MAAA,GAASJ,cAAA,CAAeG,WAAW;EAEzC,MAAM,CAACE,QAAQ,IAAUV,KAAA,CAAAW,QAAA,CACvB,MACE,IAAIV,gBAAA,CACFQ,MAAA,EACAF,OACF,CACJ;EAEMP,KAAA,CAAAY,SAAA,CAAU,MAAM;IACpBF,QAAA,CAASG,UAAA,CAAWN,OAAO;EAC7B,GAAG,CAACG,QAAA,EAAUH,OAAO,CAAC;EAEtB,MAAMO,MAAA,GAAed,KAAA,CAAAe,oBAAA,CACbf,KAAA,CAAAgB,WAAA,CACHC,aAAA,IACCP,QAAA,CAASQ,SAAA,CAAUf,aAAA,CAAcgB,UAAA,CAAWF,aAAa,CAAC,GAC5D,CAACP,QAAQ,CACX,GACA,MAAMA,QAAA,CAASU,gBAAA,CAAiB,GAChC,MAAMV,QAAA,CAASU,gBAAA,CAAiB,CAClC;EAEA,MAAMC,MAAA,GAAerB,KAAA,CAAAgB,WAAA,CAGnB,CAACM,SAAA,EAAWC,aAAA,KAAkB;IAC5Bb,QAAA,CAASW,MAAA,CAAOC,SAAA,EAAWC,aAAa,EAAEC,KAAA,CAAMtB,IAAI;EACtD,GACA,CAACQ,QAAQ,CACX;EAEA,IACEI,MAAA,CAAOW,KAAA,IACPrB,gBAAA,CAAiBM,QAAA,CAASH,OAAA,CAAQmB,YAAA,EAAc,CAACZ,MAAA,CAAOW,KAAK,CAAC,GAC9D;IACA,MAAMX,MAAA,CAAOW,KAAA;EACf;EAEA,OAAO;IAAE,GAAGX,MAAA;IAAQO,MAAA;IAAQM,WAAA,EAAab,MAAA,CAAOO;EAAO;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}