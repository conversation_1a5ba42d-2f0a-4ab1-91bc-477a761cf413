{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\UpgradePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UpgradePage = () => {\n  _s();\n  var _currentPlayer$dinhei;\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    currentPlayer,\n    loadPlayerData\n  } = usePlayer();\n  const [upgradeItems, setUpgradeItems] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [upgradeError, setUpgradeError] = useState(null);\n  const [upgradeSuccess, setUpgradeSuccess] = useState(null);\n  const [upgradingItem, setUpgradingItem] = useState(null);\n  useEffect(() => {\n    if (isAuthenticated && currentPlayer) {\n      loadUpgradeItems();\n    }\n  }, [isAuthenticated, currentPlayer]);\n  const loadUpgradeItems = () => {\n    if (!currentPlayer) return;\n    const items = [{\n      name: 'cpu',\n      displayName: 'CPU',\n      icon: '🖥️',\n      description: 'Aumenta poder de processamento para exploits',\n      currentLevel: currentPlayer.cpu || 1,\n      maxLevel: 100,\n      cost: calculateUpgradeCost('cpu', currentPlayer.cpu || 1)\n    }, {\n      name: 'ram',\n      displayName: 'RAM',\n      icon: '💾',\n      description: 'Melhora performance do sistema',\n      currentLevel: currentPlayer.ram || 1,\n      maxLevel: 100,\n      cost: calculateUpgradeCost('ram', currentPlayer.ram || 1)\n    }, {\n      name: 'firewall',\n      displayName: 'Firewall',\n      icon: '🛡️',\n      description: 'Protege contra ataques externos',\n      currentLevel: currentPlayer.firewall || 1,\n      maxLevel: 100,\n      cost: calculateUpgradeCost('firewall', currentPlayer.firewall || 1)\n    }, {\n      name: 'antivirus',\n      displayName: 'Antivírus',\n      icon: '🦠',\n      description: 'Detecta e remove malware',\n      currentLevel: currentPlayer.antivirus || 1,\n      maxLevel: 100,\n      cost: calculateUpgradeCost('antivirus', currentPlayer.antivirus || 1)\n    }, {\n      name: 'malware_kit',\n      displayName: 'Malware Kit',\n      icon: '🔧',\n      description: 'Ferramentas para criar malware',\n      currentLevel: currentPlayer.malware_kit || 1,\n      maxLevel: 100,\n      cost: calculateUpgradeCost('malware_kit', currentPlayer.malware_kit || 1)\n    }, {\n      name: 'bruteforce',\n      displayName: 'BruteForce',\n      icon: '🔨',\n      description: 'Força bruta para quebrar senhas',\n      currentLevel: currentPlayer.bruteforce || 1,\n      maxLevel: 100,\n      cost: calculateUpgradeCost('bruteforce', currentPlayer.bruteforce || 1)\n    }, {\n      name: 'bankguard',\n      displayName: 'BankGuard',\n      icon: '🏦',\n      description: 'Protege transferências bancárias',\n      currentLevel: currentPlayer.bankguard || 1,\n      maxLevel: 100,\n      cost: calculateUpgradeCost('bankguard', currentPlayer.bankguard || 1)\n    }, {\n      name: 'proxyvpn',\n      displayName: 'ProxyVPN',\n      icon: '🌐',\n      description: 'Oculta identidade online',\n      currentLevel: currentPlayer.proxyvpn || 1,\n      maxLevel: 100,\n      cost: calculateUpgradeCost('proxyvpn', currentPlayer.proxyvpn || 1)\n    }];\n    setUpgradeItems(items);\n  };\n  const calculateUpgradeCost = (item, currentLevel) => {\n    // Fórmula básica de custo: base * level^1.5\n    const baseCost = 1000;\n    const dinheiro = Math.floor(baseCost * Math.pow(currentLevel, 1.5));\n    const shacks = Math.floor(dinheiro * 0.1); // 10% do custo em shacks\n\n    return {\n      dinheiro,\n      shacks\n    };\n  };\n  const handleUpgrade = async item => {\n    if (!currentPlayer) return;\n\n    // Verificar se tem recursos suficientes\n    if (((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.dinheiro) || 0) < item.cost.dinheiro) {\n      setUpgradeError('Dinheiro insuficiente');\n      return;\n    }\n    if (item.cost.shacks && ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.shack) || 0) < item.cost.shacks) {\n      setUpgradeError('Shacks insuficientes');\n      return;\n    }\n    if (item.currentLevel >= item.maxLevel) {\n      setUpgradeError('Item já está no nível máximo');\n      return;\n    }\n    setUpgradingItem(item.name);\n    setUpgradeError(null);\n    setUpgradeSuccess(null);\n    try {\n      const response = await gameApi.upgradeItem(item.name, 1);\n      if (response.sucesso) {\n        setUpgradeSuccess(`${item.displayName} atualizado para nível ${item.currentLevel + 1}!`);\n\n        // Recarregar dados do jogador\n        await loadPlayerData();\n\n        // Atualizar lista de upgrades\n        setTimeout(() => {\n          loadUpgradeItems();\n        }, 500);\n      } else {\n        setUpgradeError(response.mensagem || 'Erro no upgrade');\n      }\n    } catch (error) {\n      setUpgradeError(error.message || 'Erro de conexão');\n    } finally {\n      setUpgradingItem(null);\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-900 text-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"\\uD83D\\uDD12 Acesso Negado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Voc\\xEA precisa estar logado para acessar os upgrades\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold\",\n            children: \"\\uD83D\\uDD27 Upgrades\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Melhorias do Sistema\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-2 text-white\",\n          children: \"\\uD83D\\uDCB0 Recursos Dispon\\xEDveis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl font-bold text-green-400\",\n              children: [\"$\", (currentPlayer === null || currentPlayer === void 0 ? void 0 : (_currentPlayer$dinhei = currentPlayer.dinheiro) === null || _currentPlayer$dinhei === void 0 ? void 0 : _currentPlayer$dinhei.toLocaleString()) || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400\",\n              children: \"Dinheiro\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl font-bold text-blue-400\",\n              children: ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.shack) || 0).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400\",\n              children: \"Shacks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), upgradeError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-300 text-sm\",\n          children: [\"\\u274C \", upgradeError]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this), upgradeSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-900 border border-green-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-300 text-sm\",\n          children: [\"\\u2705 \", upgradeSuccess]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: upgradeItems.map(item => {\n          const canAfford = currentPlayer && currentPlayer.dinheiro >= item.cost.dinheiro && (!item.cost.shacks || currentPlayer.shack >= item.cost.shacks);\n          const isMaxLevel = item.currentLevel >= item.maxLevel;\n          const isUpgrading = upgradingItem === item.name;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl\",\n                  children: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-white\",\n                    children: item.displayName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400\",\n                    children: item.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-400\",\n                    children: [\"N\\xEDvel \", item.currentLevel, \"/\", item.maxLevel]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-300\",\n                  children: [\"$\", item.cost.dinheiro.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), item.cost.shacks && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-blue-400\",\n                  children: [item.cost.shacks, \" Shacks\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-700 rounded-full h-2 mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-600 h-2 rounded-full\",\n                style: {\n                  width: `${item.currentLevel / item.maxLevel * 100}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleUpgrade(item),\n              disabled: !canAfford || isMaxLevel || isUpgrading,\n              className: `w-full py-2 rounded-lg font-semibold text-sm ${isMaxLevel ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : canAfford && !isUpgrading ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-gray-600 text-gray-400 cursor-not-allowed'}`,\n              children: isUpgrading ? 'Atualizando...' : isMaxLevel ? 'Nível Máximo' : canAfford ? 'Atualizar' : 'Recursos Insuficientes'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, item.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"Voltar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this);\n};\n_s(UpgradePage, \"87suUtGn8PbD+dMett8AMwYpHhM=\", false, function () {\n  return [useAuth, usePlayer];\n});\n_c = UpgradePage;\nexport default UpgradePage;\nvar _c;\n$RefreshReg$(_c, \"UpgradePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "usePlayer", "gameApi", "jsxDEV", "_jsxDEV", "UpgradePage", "_s", "_currentPlayer$dinhei", "user", "isAuthenticated", "currentPlayer", "loadPlayerData", "upgradeItems", "setUpgradeItems", "isLoading", "setIsLoading", "upgradeError", "setUpgradeError", "upgradeSuccess", "setUpgradeSuccess", "upgradingItem", "setUpgradingItem", "loadUpgradeItems", "items", "name", "displayName", "icon", "description", "currentLevel", "cpu", "maxLevel", "cost", "calculateUpgradeCost", "ram", "firewall", "antivirus", "malware_kit", "bruteforce", "bankguard", "proxyvpn", "item", "baseCost", "<PERSON><PERSON><PERSON>", "Math", "floor", "pow", "shacks", "handleUpgrade", "shack", "response", "upgradeItem", "sucesso", "setTimeout", "mensagem", "error", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "history", "back", "toLocaleString", "map", "can<PERSON>fford", "isMaxLevel", "isUpgrading", "style", "width", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/UpgradePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\n\ninterface UpgradeItem {\n  name: string;\n  displayName: string;\n  icon: string;\n  description: string;\n  currentLevel: number;\n  maxLevel: number;\n  cost: {\n    dinheiro: number;\n    shacks?: number;\n  };\n}\n\nconst UpgradePage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const { currentPlayer, loadPlayerData } = usePlayer();\n  \n  const [upgradeItems, setUpgradeItems] = useState<UpgradeItem[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [upgradeError, setUpgradeError] = useState<string | null>(null);\n  const [upgradeSuccess, setUpgradeSuccess] = useState<string | null>(null);\n  const [upgradingItem, setUpgradingItem] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (isAuthenticated && currentPlayer) {\n      loadUpgradeItems();\n    }\n  }, [isAuthenticated, currentPlayer]);\n\n  const loadUpgradeItems = () => {\n    if (!currentPlayer) return;\n\n    const items: UpgradeItem[] = [\n      {\n        name: 'cpu',\n        displayName: 'CPU',\n        icon: '🖥️',\n        description: 'Aumenta poder de processamento para exploits',\n        currentLevel: currentPlayer.cpu || 1,\n        maxLevel: 100,\n        cost: calculateUpgradeCost('cpu', currentPlayer.cpu || 1),\n      },\n      {\n        name: 'ram',\n        displayName: 'RAM',\n        icon: '💾',\n        description: 'Melhora performance do sistema',\n        currentLevel: currentPlayer.ram || 1,\n        maxLevel: 100,\n        cost: calculateUpgradeCost('ram', currentPlayer.ram || 1),\n      },\n      {\n        name: 'firewall',\n        displayName: 'Firewall',\n        icon: '🛡️',\n        description: 'Protege contra ataques externos',\n        currentLevel: currentPlayer.firewall || 1,\n        maxLevel: 100,\n        cost: calculateUpgradeCost('firewall', currentPlayer.firewall || 1),\n      },\n      {\n        name: 'antivirus',\n        displayName: 'Antivírus',\n        icon: '🦠',\n        description: 'Detecta e remove malware',\n        currentLevel: currentPlayer.antivirus || 1,\n        maxLevel: 100,\n        cost: calculateUpgradeCost('antivirus', currentPlayer.antivirus || 1),\n      },\n      {\n        name: 'malware_kit',\n        displayName: 'Malware Kit',\n        icon: '🔧',\n        description: 'Ferramentas para criar malware',\n        currentLevel: currentPlayer.malware_kit || 1,\n        maxLevel: 100,\n        cost: calculateUpgradeCost('malware_kit', currentPlayer.malware_kit || 1),\n      },\n      {\n        name: 'bruteforce',\n        displayName: 'BruteForce',\n        icon: '🔨',\n        description: 'Força bruta para quebrar senhas',\n        currentLevel: currentPlayer.bruteforce || 1,\n        maxLevel: 100,\n        cost: calculateUpgradeCost('bruteforce', currentPlayer.bruteforce || 1),\n      },\n      {\n        name: 'bankguard',\n        displayName: 'BankGuard',\n        icon: '🏦',\n        description: 'Protege transferências bancárias',\n        currentLevel: currentPlayer.bankguard || 1,\n        maxLevel: 100,\n        cost: calculateUpgradeCost('bankguard', currentPlayer.bankguard || 1),\n      },\n      {\n        name: 'proxyvpn',\n        displayName: 'ProxyVPN',\n        icon: '🌐',\n        description: 'Oculta identidade online',\n        currentLevel: currentPlayer.proxyvpn || 1,\n        maxLevel: 100,\n        cost: calculateUpgradeCost('proxyvpn', currentPlayer.proxyvpn || 1),\n      },\n    ];\n\n    setUpgradeItems(items);\n  };\n\n  const calculateUpgradeCost = (item: string, currentLevel: number) => {\n    // Fórmula básica de custo: base * level^1.5\n    const baseCost = 1000;\n    const dinheiro = Math.floor(baseCost * Math.pow(currentLevel, 1.5));\n    const shacks = Math.floor(dinheiro * 0.1); // 10% do custo em shacks\n    \n    return { dinheiro, shacks };\n  };\n\n  const handleUpgrade = async (item: UpgradeItem) => {\n    if (!currentPlayer) return;\n\n    // Verificar se tem recursos suficientes\n    if ((currentPlayer?.dinheiro || 0) < item.cost.dinheiro) {\n      setUpgradeError('Dinheiro insuficiente');\n      return;\n    }\n\n    if (item.cost.shacks && (currentPlayer?.shack || 0) < item.cost.shacks) {\n      setUpgradeError('Shacks insuficientes');\n      return;\n    }\n\n    if (item.currentLevel >= item.maxLevel) {\n      setUpgradeError('Item já está no nível máximo');\n      return;\n    }\n\n    setUpgradingItem(item.name);\n    setUpgradeError(null);\n    setUpgradeSuccess(null);\n\n    try {\n      const response = await gameApi.upgradeItem(item.name, 1);\n      \n      if (response.sucesso) {\n        setUpgradeSuccess(`${item.displayName} atualizado para nível ${item.currentLevel + 1}!`);\n        \n        // Recarregar dados do jogador\n        await loadPlayerData();\n        \n        // Atualizar lista de upgrades\n        setTimeout(() => {\n          loadUpgradeItems();\n        }, 500);\n      } else {\n        setUpgradeError(response.mensagem || 'Erro no upgrade');\n      }\n    } catch (error: any) {\n      setUpgradeError(error.message || 'Erro de conexão');\n    } finally {\n      setUpgradingItem(null);\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"h-screen bg-gray-900 text-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-4\">🔒 Acesso Negado</h1>\n          <p className=\"text-gray-400\">Você precisa estar logado para acessar os upgrades</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n          >\n            <span className=\"text-lg\">←</span>\n          </button>\n          <div>\n            <h1 className=\"text-lg font-bold\">🔧 Upgrades</h1>\n            <p className=\"text-xs text-gray-400\">Melhorias do Sistema</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-4\">\n        {/* Recursos disponíveis */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-2 text-white\">💰 Recursos Disponíveis</h3>\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"text-center\">\n              <div className=\"text-xl font-bold text-green-400\">\n                ${currentPlayer?.dinheiro?.toLocaleString() || '0'}\n              </div>\n              <div className=\"text-xs text-gray-400\">Dinheiro</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-xl font-bold text-blue-400\">\n                {(currentPlayer?.shack || 0).toLocaleString()}\n              </div>\n              <div className=\"text-xs text-gray-400\">Shacks</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Mensagens de erro/sucesso */}\n        {upgradeError && (\n          <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n            <p className=\"text-red-300 text-sm\">❌ {upgradeError}</p>\n          </div>\n        )}\n\n        {upgradeSuccess && (\n          <div className=\"bg-green-900 border border-green-500 rounded-lg p-3\">\n            <p className=\"text-green-300 text-sm\">✅ {upgradeSuccess}</p>\n          </div>\n        )}\n\n        {/* Lista de upgrades */}\n        <div className=\"space-y-3\">\n          {upgradeItems.map((item) => {\n            const canAfford = currentPlayer && \n              currentPlayer.dinheiro >= item.cost.dinheiro &&\n              (!item.cost.shacks || currentPlayer.shack >= item.cost.shacks);\n            \n            const isMaxLevel = item.currentLevel >= item.maxLevel;\n            const isUpgrading = upgradingItem === item.name;\n\n            return (\n              <div key={item.name} className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n                <div className=\"flex justify-between items-start mb-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"text-2xl\">{item.icon}</div>\n                    <div>\n                      <h4 className=\"font-bold text-white\">{item.displayName}</h4>\n                      <p className=\"text-xs text-gray-400\">{item.description}</p>\n                      <div className=\"text-sm text-blue-400\">\n                        Nível {item.currentLevel}/{item.maxLevel}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm text-gray-300\">\n                      ${item.cost.dinheiro.toLocaleString()}\n                    </div>\n                    {item.cost.shacks && (\n                      <div className=\"text-xs text-blue-400\">\n                        {item.cost.shacks} Shacks\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Barra de progresso */}\n                <div className=\"w-full bg-gray-700 rounded-full h-2 mb-3\">\n                  <div \n                    className=\"bg-blue-600 h-2 rounded-full\" \n                    style={{ width: `${(item.currentLevel / item.maxLevel) * 100}%` }}\n                  ></div>\n                </div>\n\n                <button\n                  onClick={() => handleUpgrade(item)}\n                  disabled={!canAfford || isMaxLevel || isUpgrading}\n                  className={`w-full py-2 rounded-lg font-semibold text-sm ${\n                    isMaxLevel\n                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                      : canAfford && !isUpgrading\n                      ? 'bg-blue-600 hover:bg-blue-700 text-white'\n                      : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                  }`}\n                >\n                  {isUpgrading ? 'Atualizando...' : \n                   isMaxLevel ? 'Nível Máximo' :\n                   canAfford ? 'Atualizar' : 'Recursos Insuficientes'}\n                </button>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-center\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\"\n          >\n            <span className=\"text-lg\">←</span>\n            <span className=\"text-sm\">Voltar</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UpgradePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAe1C,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEU,aAAa;IAAEC;EAAe,CAAC,GAAGV,SAAS,CAAC,CAAC;EAErD,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EAEvEC,SAAS,CAAC,MAAM;IACd,IAAIU,eAAe,IAAIC,aAAa,EAAE;MACpCY,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACb,eAAe,EAAEC,aAAa,CAAC,CAAC;EAEpC,MAAMY,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACZ,aAAa,EAAE;IAEpB,MAAMa,KAAoB,GAAG,CAC3B;MACEC,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,KAAK;MAClBC,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,8CAA8C;MAC3DC,YAAY,EAAElB,aAAa,CAACmB,GAAG,IAAI,CAAC;MACpCC,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAEC,oBAAoB,CAAC,KAAK,EAAEtB,aAAa,CAACmB,GAAG,IAAI,CAAC;IAC1D,CAAC,EACD;MACEL,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,KAAK;MAClBC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,gCAAgC;MAC7CC,YAAY,EAAElB,aAAa,CAACuB,GAAG,IAAI,CAAC;MACpCH,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAEC,oBAAoB,CAAC,KAAK,EAAEtB,aAAa,CAACuB,GAAG,IAAI,CAAC;IAC1D,CAAC,EACD;MACET,IAAI,EAAE,UAAU;MAChBC,WAAW,EAAE,UAAU;MACvBC,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,iCAAiC;MAC9CC,YAAY,EAAElB,aAAa,CAACwB,QAAQ,IAAI,CAAC;MACzCJ,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAEC,oBAAoB,CAAC,UAAU,EAAEtB,aAAa,CAACwB,QAAQ,IAAI,CAAC;IACpE,CAAC,EACD;MACEV,IAAI,EAAE,WAAW;MACjBC,WAAW,EAAE,WAAW;MACxBC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,0BAA0B;MACvCC,YAAY,EAAElB,aAAa,CAACyB,SAAS,IAAI,CAAC;MAC1CL,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAEC,oBAAoB,CAAC,WAAW,EAAEtB,aAAa,CAACyB,SAAS,IAAI,CAAC;IACtE,CAAC,EACD;MACEX,IAAI,EAAE,aAAa;MACnBC,WAAW,EAAE,aAAa;MAC1BC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,gCAAgC;MAC7CC,YAAY,EAAElB,aAAa,CAAC0B,WAAW,IAAI,CAAC;MAC5CN,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAEC,oBAAoB,CAAC,aAAa,EAAEtB,aAAa,CAAC0B,WAAW,IAAI,CAAC;IAC1E,CAAC,EACD;MACEZ,IAAI,EAAE,YAAY;MAClBC,WAAW,EAAE,YAAY;MACzBC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,iCAAiC;MAC9CC,YAAY,EAAElB,aAAa,CAAC2B,UAAU,IAAI,CAAC;MAC3CP,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAEC,oBAAoB,CAAC,YAAY,EAAEtB,aAAa,CAAC2B,UAAU,IAAI,CAAC;IACxE,CAAC,EACD;MACEb,IAAI,EAAE,WAAW;MACjBC,WAAW,EAAE,WAAW;MACxBC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,kCAAkC;MAC/CC,YAAY,EAAElB,aAAa,CAAC4B,SAAS,IAAI,CAAC;MAC1CR,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAEC,oBAAoB,CAAC,WAAW,EAAEtB,aAAa,CAAC4B,SAAS,IAAI,CAAC;IACtE,CAAC,EACD;MACEd,IAAI,EAAE,UAAU;MAChBC,WAAW,EAAE,UAAU;MACvBC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,0BAA0B;MACvCC,YAAY,EAAElB,aAAa,CAAC6B,QAAQ,IAAI,CAAC;MACzCT,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAEC,oBAAoB,CAAC,UAAU,EAAEtB,aAAa,CAAC6B,QAAQ,IAAI,CAAC;IACpE,CAAC,CACF;IAED1B,eAAe,CAACU,KAAK,CAAC;EACxB,CAAC;EAED,MAAMS,oBAAoB,GAAGA,CAACQ,IAAY,EAAEZ,YAAoB,KAAK;IACnE;IACA,MAAMa,QAAQ,GAAG,IAAI;IACrB,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAGE,IAAI,CAACE,GAAG,CAACjB,YAAY,EAAE,GAAG,CAAC,CAAC;IACnE,MAAMkB,MAAM,GAAGH,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC;;IAE3C,OAAO;MAAEA,QAAQ;MAAEI;IAAO,CAAC;EAC7B,CAAC;EAED,MAAMC,aAAa,GAAG,MAAOP,IAAiB,IAAK;IACjD,IAAI,CAAC9B,aAAa,EAAE;;IAEpB;IACA,IAAI,CAAC,CAAAA,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgC,QAAQ,KAAI,CAAC,IAAIF,IAAI,CAACT,IAAI,CAACW,QAAQ,EAAE;MACvDzB,eAAe,CAAC,uBAAuB,CAAC;MACxC;IACF;IAEA,IAAIuB,IAAI,CAACT,IAAI,CAACe,MAAM,IAAI,CAAC,CAAApC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsC,KAAK,KAAI,CAAC,IAAIR,IAAI,CAACT,IAAI,CAACe,MAAM,EAAE;MACtE7B,eAAe,CAAC,sBAAsB,CAAC;MACvC;IACF;IAEA,IAAIuB,IAAI,CAACZ,YAAY,IAAIY,IAAI,CAACV,QAAQ,EAAE;MACtCb,eAAe,CAAC,8BAA8B,CAAC;MAC/C;IACF;IAEAI,gBAAgB,CAACmB,IAAI,CAAChB,IAAI,CAAC;IAC3BP,eAAe,CAAC,IAAI,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MACF,MAAM8B,QAAQ,GAAG,MAAM/C,OAAO,CAACgD,WAAW,CAACV,IAAI,CAAChB,IAAI,EAAE,CAAC,CAAC;MAExD,IAAIyB,QAAQ,CAACE,OAAO,EAAE;QACpBhC,iBAAiB,CAAC,GAAGqB,IAAI,CAACf,WAAW,0BAA0Be,IAAI,CAACZ,YAAY,GAAG,CAAC,GAAG,CAAC;;QAExF;QACA,MAAMjB,cAAc,CAAC,CAAC;;QAEtB;QACAyC,UAAU,CAAC,MAAM;UACf9B,gBAAgB,CAAC,CAAC;QACpB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLL,eAAe,CAACgC,QAAQ,CAACI,QAAQ,IAAI,iBAAiB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBrC,eAAe,CAACqC,KAAK,CAACC,OAAO,IAAI,iBAAiB,CAAC;IACrD,CAAC,SAAS;MACRlC,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,IAAI,CAACZ,eAAe,EAAE;IACpB,oBACEL,OAAA;MAAKoD,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/ErD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAIoD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DzD,OAAA;UAAGoD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEzD,OAAA;IAAKoD,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DrD,OAAA;MAAKoD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrErD,OAAA;QAAKoD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CrD,OAAA;UACE0D,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCT,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7FrD,OAAA;YAAMoD,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACTzD,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAIoD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDzD,OAAA;YAAGoD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzD,OAAA;MAAKoD,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBAEnDrD,OAAA;QAAKoD,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChErD,OAAA;UAAIoD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFzD,OAAA;UAAKoD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCrD,OAAA;YAAKoD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrD,OAAA;cAAKoD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,GAC/C,EAAC,CAAA/C,aAAa,aAAbA,aAAa,wBAAAH,qBAAA,GAAbG,aAAa,CAAEgC,QAAQ,cAAAnC,qBAAA,uBAAvBA,qBAAA,CAAyB2D,cAAc,CAAC,CAAC,KAAI,GAAG;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNzD,OAAA;YAAKoD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrD,OAAA;cAAKoD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC7C,CAAC,CAAA/C,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsC,KAAK,KAAI,CAAC,EAAEkB,cAAc,CAAC;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL7C,YAAY,iBACXZ,OAAA;QAAKoD,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DrD,OAAA;UAAGoD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,SAAE,EAACzC,YAAY;QAAA;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CACN,EAEA3C,cAAc,iBACbd,OAAA;QAAKoD,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClErD,OAAA;UAAGoD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,SAAE,EAACvC,cAAc;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CACN,eAGDzD,OAAA;QAAKoD,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB7C,YAAY,CAACuD,GAAG,CAAE3B,IAAI,IAAK;UAC1B,MAAM4B,SAAS,GAAG1D,aAAa,IAC7BA,aAAa,CAACgC,QAAQ,IAAIF,IAAI,CAACT,IAAI,CAACW,QAAQ,KAC3C,CAACF,IAAI,CAACT,IAAI,CAACe,MAAM,IAAIpC,aAAa,CAACsC,KAAK,IAAIR,IAAI,CAACT,IAAI,CAACe,MAAM,CAAC;UAEhE,MAAMuB,UAAU,GAAG7B,IAAI,CAACZ,YAAY,IAAIY,IAAI,CAACV,QAAQ;UACrD,MAAMwC,WAAW,GAAGlD,aAAa,KAAKoB,IAAI,CAAChB,IAAI;UAE/C,oBACEpB,OAAA;YAAqBoD,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChFrD,OAAA;cAAKoD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDrD,OAAA;gBAAKoD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CrD,OAAA;kBAAKoD,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAEjB,IAAI,CAACd;gBAAI;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CzD,OAAA;kBAAAqD,QAAA,gBACErD,OAAA;oBAAIoD,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAEjB,IAAI,CAACf;kBAAW;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DzD,OAAA;oBAAGoD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEjB,IAAI,CAACb;kBAAW;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3DzD,OAAA;oBAAKoD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,WAC/B,EAACjB,IAAI,CAACZ,YAAY,EAAC,GAAC,EAACY,IAAI,CAACV,QAAQ;kBAAA;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAKoD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBrD,OAAA;kBAAKoD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,GACpC,EAACjB,IAAI,CAACT,IAAI,CAACW,QAAQ,CAACwB,cAAc,CAAC,CAAC;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,EACLrB,IAAI,CAACT,IAAI,CAACe,MAAM,iBACf1C,OAAA;kBAAKoD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACnCjB,IAAI,CAACT,IAAI,CAACe,MAAM,EAAC,SACpB;gBAAA;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNzD,OAAA;cAAKoD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,eACvDrD,OAAA;gBACEoD,SAAS,EAAC,8BAA8B;gBACxCe,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAIhC,IAAI,CAACZ,YAAY,GAAGY,IAAI,CAACV,QAAQ,GAAI,GAAG;gBAAI;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENzD,OAAA;cACE0D,OAAO,EAAEA,CAAA,KAAMf,aAAa,CAACP,IAAI,CAAE;cACnCiC,QAAQ,EAAE,CAACL,SAAS,IAAIC,UAAU,IAAIC,WAAY;cAClDd,SAAS,EAAE,gDACTa,UAAU,GACN,8CAA8C,GAC9CD,SAAS,IAAI,CAACE,WAAW,GACzB,0CAA0C,GAC1C,8CAA8C,EACjD;cAAAb,QAAA,EAEFa,WAAW,GAAG,gBAAgB,GAC9BD,UAAU,GAAG,cAAc,GAC3BD,SAAS,GAAG,WAAW,GAAG;YAAwB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA,GA9CDrB,IAAI,CAAChB,IAAI;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+Cd,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzD,OAAA;MAAKoD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrErD,OAAA;QAAKoD,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClCrD,OAAA;UACE0D,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCT,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExFrD,OAAA;YAAMoD,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCzD,OAAA;YAAMoD,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CArSID,WAAqB;EAAA,QACSL,OAAO,EACCC,SAAS;AAAA;AAAAyE,EAAA,GAF/CrE,WAAqB;AAuS3B,eAAeA,WAAW;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}