{"ast": null, "code": "import axios from 'axios';\n\n// Configuração do cliente HTTP para o backend Flask\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Interceptor para adicionar token de autenticação\napiClient.interceptors.request.use(config => {\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Interceptor para tratar respostas\napiClient.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expirado ou inválido\n    localStorage.removeItem('auth_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport class BackendService {\n  // ==================== AUTENTICAÇÃO ====================\n\n  async login(email, password) {\n    try {\n      const response = await apiClient.post('/api/auth/login', {\n        email,\n        password\n      });\n      if (response.data.sucesso) {\n        const token = response.data.token;\n        localStorage.setItem('auth_token', token);\n        return {\n          success: true,\n          token\n        };\n      }\n      return {\n        success: false,\n        error: response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.mensagem) || 'Erro de conexão'\n      };\n    }\n  }\n  async register(nick, email, password) {\n    try {\n      const response = await apiClient.post('/api/auth/register', {\n        nick,\n        email,\n        password\n      });\n      return {\n        success: response.data.sucesso,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      return {\n        success: false,\n        error: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.mensagem) || 'Erro de conexão'\n      };\n    }\n  }\n\n  // ==================== JOGADOR ====================\n\n  async getPlayer() {\n    try {\n      const response = await apiClient.get('/api/jogador');\n      if (response.data.sucesso) {\n        return {\n          success: true,\n          player: response.data.jogador\n        };\n      }\n      return {\n        success: false,\n        error: response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      return {\n        success: false,\n        error: ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.mensagem) || 'Erro ao carregar jogador'\n      };\n    }\n  }\n\n  // ==================== UPGRADES ====================\n\n  async upgradeApp(appName, quantity = 1) {\n    try {\n      // Mapear nomes dos apps do React para o backend\n      const appMapping = {\n        antivirus: 'antivirus',\n        bankguard: 'bankguard',\n        bruteforce: 'bruteforce',\n        sdk: 'cpu',\n        // SDK no React = CPU no backend\n        firewall: 'firewall',\n        malwarekit: 'malware_kit',\n        proxyvpn: 'proxyvpn',\n        mineradora: 'mineradora' // Novo app\n      };\n      const backendAppName = appMapping[appName] || appName;\n      const response = await apiClient.post('/api/appstore/comprar', {\n        item: backendAppName,\n        quantidade: quantity\n      });\n      return {\n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      return {\n        success: false,\n        error: ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.mensagem) || 'Erro ao fazer upgrade'\n      };\n    }\n  }\n\n  // ==================== SCANNER ====================\n\n  async scanTargets() {\n    try {\n      const response = await apiClient.get('/api/scan');\n      return {\n        success: response.data.sucesso,\n        targets: response.data.alvos || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      return {\n        success: false,\n        error: ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.mensagem) || 'Erro ao escanear'\n      };\n    }\n  }\n\n  // ==================== CHAT ====================\n\n  async getChatMessages() {\n    try {\n      const response = await apiClient.get('/api/chat/messages');\n      return {\n        success: response.data.sucesso,\n        messages: response.data.mensagens || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      return {\n        success: false,\n        error: ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.mensagem) || 'Erro ao carregar chat'\n      };\n    }\n  }\n  async sendChatMessage(message) {\n    try {\n      const response = await apiClient.post('/api/chat/send', {\n        mensagem: message\n      });\n      return {\n        success: response.data.sucesso,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      return {\n        success: false,\n        error: ((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.mensagem) || 'Erro ao enviar mensagem'\n      };\n    }\n  }\n\n  // ==================== HACKING ====================\n\n  async exploitTarget(targetData) {\n    try {\n      const response = await apiClient.post('/api/transferir', targetData);\n      return {\n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response9, _error$response9$data;\n      return {\n        success: false,\n        error: ((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.mensagem) || 'Erro ao exploitar alvo'\n      };\n    }\n  }\n\n  // ==================== UTILITÁRIOS ====================\n\n  async testConnection() {\n    try {\n      const response = await apiClient.get('/api/status');\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Erro de conexão com o servidor'\n      };\n    }\n  }\n\n  // Converter jogador do backend para formato do React\n  convertBackendPlayer(backendPlayer) {\n    return {\n      uid: backendPlayer.uid,\n      nick: backendPlayer.nick,\n      email: backendPlayer.email,\n      ip: backendPlayer.ip,\n      level: backendPlayer.nivel,\n      xp: backendPlayer.xp,\n      xpToNextLevel: 100,\n      // Será calculado depois\n      cash: backendPlayer.dinheiro,\n      shack: backendPlayer.shack || 0,\n      // Nova moeda\n      createdAt: backendPlayer.created_at,\n      lastLogin: backendPlayer.last_login\n    };\n  }\n\n  // Converter apps do backend para formato do React\n  convertBackendApps(backendPlayer) {\n    return {\n      antivirus: backendPlayer.antivirus || 1,\n      bankguard: backendPlayer.bankguard || 1,\n      bruteforce: backendPlayer.bruteforce || 1,\n      sdk: backendPlayer.cpu || 1,\n      // CPU no backend = SDK no React\n      firewall: backendPlayer.firewall || 1,\n      malwarekit: backendPlayer.malware_kit || 1,\n      proxyvpn: backendPlayer.proxyvpn || 1,\n      mineradora: backendPlayer.mineradora || 1 // Novo app\n    };\n  }\n}\nexport const backendService = new BackendService();", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "apiClient", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "response", "error", "_error$response", "status", "removeItem", "window", "location", "href", "Promise", "reject", "BackendService", "login", "email", "password", "post", "data", "sucesso", "setItem", "success", "mensagem", "_error$response2", "_error$response2$data", "register", "nick", "undefined", "_error$response3", "_error$response3$data", "getPlayer", "get", "player", "jogador", "_error$response4", "_error$response4$data", "upgradeApp", "appName", "quantity", "appMapping", "antivirus", "bankguard", "bruteforce", "sdk", "firewall", "malwarekit", "proxyvpn", "mineradora", "backendAppName", "item", "quantidade", "_error$response5", "_error$response5$data", "scanTargets", "targets", "alvos", "_error$response6", "_error$response6$data", "getChatMessages", "messages", "mensagens", "_error$response7", "_error$response7$data", "sendChatMessage", "message", "_error$response8", "_error$response8$data", "exploitTarget", "targetData", "_error$response9", "_error$response9$data", "testConnection", "convertBackendPlayer", "backendPlayer", "uid", "ip", "level", "nivel", "xp", "xpToNextLevel", "cash", "<PERSON><PERSON><PERSON>", "shack", "createdAt", "created_at", "lastLogin", "last_login", "convertBackendApps", "cpu", "malware_kit", "backendService"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/backendService.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Configuração do cliente HTTP para o backend Flask\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Interceptor para adicionar token de autenticação\napiClient.interceptors.request.use((config) => {\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Interceptor para tratar respostas\napiClient.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expirado ou inválido\n      localStorage.removeItem('auth_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport interface BackendPlayer {\n  uid: string;\n  nick: string;\n  email: string;\n  ip: string;\n  nivel: number;\n  xp: number;\n  dinheiro: number;\n  // Apps\n  cpu: number;\n  firewall: number;\n  antivirus: number;\n  malware_kit: number;\n  bruteforce: number;\n  bankguard: number;\n  proxyvpn: number;\n  // Timestamps\n  created_at: string;\n  last_login: string;\n}\n\nexport interface UpgradeResponse {\n  sucesso: boolean;\n  mensagem: string;\n  nivel_novo?: number;\n  custo_dinheiro?: number;\n  custo_shacks?: number;\n  xp_ganho?: number;\n  nivel_jogador?: number;\n  level_up?: boolean;\n}\n\nexport class BackendService {\n  // ==================== AUTENTICAÇÃO ====================\n  \n  async login(email: string, password: string): Promise<{ success: boolean; token?: string; error?: string }> {\n    try {\n      const response = await apiClient.post('/api/auth/login', {\n        email,\n        password,\n      });\n\n      if (response.data.sucesso) {\n        const token = response.data.token;\n        localStorage.setItem('auth_token', token);\n        return { success: true, token };\n      }\n\n      return { success: false, error: response.data.mensagem };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro de conexão' };\n    }\n  }\n\n  async register(nick: string, email: string, password: string): Promise<{ success: boolean; error?: string }> {\n    try {\n      const response = await apiClient.post('/api/auth/register', {\n        nick,\n        email,\n        password,\n      });\n\n      return { \n        success: response.data.sucesso,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro de conexão' };\n    }\n  }\n\n  // ==================== JOGADOR ====================\n\n  async getPlayer(): Promise<{ success: boolean; player?: BackendPlayer; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/jogador');\n      \n      if (response.data.sucesso) {\n        return { success: true, player: response.data.jogador };\n      }\n\n      return { success: false, error: response.data.mensagem };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao carregar jogador' };\n    }\n  }\n\n  // ==================== UPGRADES ====================\n\n  async upgradeApp(appName: string, quantity: number = 1): Promise<{ success: boolean; data?: UpgradeResponse; error?: string }> {\n    try {\n      // Mapear nomes dos apps do React para o backend\n      const appMapping: { [key: string]: string } = {\n        antivirus: 'antivirus',\n        bankguard: 'bankguard',\n        bruteforce: 'bruteforce',\n        sdk: 'cpu', // SDK no React = CPU no backend\n        firewall: 'firewall',\n        malwarekit: 'malware_kit',\n        proxyvpn: 'proxyvpn',\n        mineradora: 'mineradora', // Novo app\n      };\n\n      const backendAppName = appMapping[appName] || appName;\n\n      const response = await apiClient.post('/api/appstore/comprar', {\n        item: backendAppName,\n        quantidade: quantity,\n      });\n\n      return { \n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao fazer upgrade' };\n    }\n  }\n\n  // ==================== SCANNER ====================\n\n  async scanTargets(): Promise<{ success: boolean; targets?: any[]; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/scan');\n      \n      return { \n        success: response.data.sucesso,\n        targets: response.data.alvos || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao escanear' };\n    }\n  }\n\n  // ==================== CHAT ====================\n\n  async getChatMessages(): Promise<{ success: boolean; messages?: any[]; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/chat/messages');\n      \n      return { \n        success: response.data.sucesso,\n        messages: response.data.mensagens || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao carregar chat' };\n    }\n  }\n\n  async sendChatMessage(message: string): Promise<{ success: boolean; error?: string }> {\n    try {\n      const response = await apiClient.post('/api/chat/send', {\n        mensagem: message,\n      });\n\n      return { \n        success: response.data.sucesso,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao enviar mensagem' };\n    }\n  }\n\n  // ==================== HACKING ====================\n\n  async exploitTarget(targetData: any): Promise<{ success: boolean; data?: any; error?: string }> {\n    try {\n      const response = await apiClient.post('/api/transferir', targetData);\n      \n      return { \n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao exploitar alvo' };\n    }\n  }\n\n  // ==================== UTILITÁRIOS ====================\n\n  async testConnection(): Promise<{ success: boolean; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/status');\n      return { success: true };\n    } catch (error: any) {\n      return { success: false, error: 'Erro de conexão com o servidor' };\n    }\n  }\n\n  // Converter jogador do backend para formato do React\n  convertBackendPlayer(backendPlayer: BackendPlayer): any {\n    return {\n      uid: backendPlayer.uid,\n      nick: backendPlayer.nick,\n      email: backendPlayer.email,\n      ip: backendPlayer.ip,\n      level: backendPlayer.nivel,\n      xp: backendPlayer.xp,\n      xpToNextLevel: 100, // Será calculado depois\n      cash: backendPlayer.dinheiro,\n      shack: (backendPlayer as any).shack || 0, // Nova moeda\n      createdAt: backendPlayer.created_at,\n      lastLogin: backendPlayer.last_login,\n    };\n  }\n\n  // Converter apps do backend para formato do React\n  convertBackendApps(backendPlayer: BackendPlayer) {\n    return {\n      antivirus: backendPlayer.antivirus || 1,\n      bankguard: backendPlayer.bankguard || 1,\n      bruteforce: backendPlayer.bruteforce || 1,\n      sdk: backendPlayer.cpu || 1, // CPU no backend = SDK no React\n      firewall: backendPlayer.firewall || 1,\n      malwarekit: backendPlayer.malware_kit || 1,\n      proxyvpn: backendPlayer.proxyvpn || 1,\n      mineradora: (backendPlayer as any).mineradora || 1, // Novo app\n    };\n  }\n}\n\nexport const backendService = new BackendService();\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,SAAS,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC7BC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,SAAS,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EAC7C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;;AAEF;AACAR,SAAS,CAACK,YAAY,CAACQ,QAAQ,CAACN,GAAG,CAChCM,QAAQ,IAAKA,QAAQ,EACrBC,KAAK,IAAK;EAAA,IAAAC,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAD,KAAK,CAACD,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAN,YAAY,CAACO,UAAU,CAAC,YAAY,CAAC;IACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOC,OAAO,CAACC,MAAM,CAACR,KAAK,CAAC;AAC9B,CACF,CAAC;AAkCD,OAAO,MAAMS,cAAc,CAAC;EAC1B;;EAEA,MAAMC,KAAKA,CAACC,KAAa,EAAEC,QAAgB,EAAiE;IAC1G,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMb,SAAS,CAAC2B,IAAI,CAAC,iBAAiB,EAAE;QACvDF,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,IAAIb,QAAQ,CAACe,IAAI,CAACC,OAAO,EAAE;QACzB,MAAMpB,KAAK,GAAGI,QAAQ,CAACe,IAAI,CAACnB,KAAK;QACjCC,YAAY,CAACoB,OAAO,CAAC,YAAY,EAAErB,KAAK,CAAC;QACzC,OAAO;UAAEsB,OAAO,EAAE,IAAI;UAAEtB;QAAM,CAAC;MACjC;MAEA,OAAO;QAAEsB,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAED,QAAQ,CAACe,IAAI,CAACI;MAAS,CAAC;IAC1D,CAAC,CAAC,OAAOlB,KAAU,EAAE;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAE,EAAAmB,gBAAA,GAAAnB,KAAK,CAACD,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBL,IAAI,cAAAM,qBAAA,uBAApBA,qBAAA,CAAsBF,QAAQ,KAAI;MAAkB,CAAC;IACvF;EACF;EAEA,MAAMG,QAAQA,CAACC,IAAY,EAAEX,KAAa,EAAEC,QAAgB,EAAiD;IAC3G,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMb,SAAS,CAAC2B,IAAI,CAAC,oBAAoB,EAAE;QAC1DS,IAAI;QACJX,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,OAAO;QACLK,OAAO,EAAElB,QAAQ,CAACe,IAAI,CAACC,OAAO;QAC9Bf,KAAK,EAAED,QAAQ,CAACe,IAAI,CAACC,OAAO,GAAGQ,SAAS,GAAGxB,QAAQ,CAACe,IAAI,CAACI;MAC3D,CAAC;IACH,CAAC,CAAC,OAAOlB,KAAU,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAER,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAE,EAAAwB,gBAAA,GAAAxB,KAAK,CAACD,QAAQ,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,uBAApBA,qBAAA,CAAsBP,QAAQ,KAAI;MAAkB,CAAC;IACvF;EACF;;EAEA;;EAEA,MAAMQ,SAASA,CAAA,EAA0E;IACvF,IAAI;MACF,MAAM3B,QAAQ,GAAG,MAAMb,SAAS,CAACyC,GAAG,CAAC,cAAc,CAAC;MAEpD,IAAI5B,QAAQ,CAACe,IAAI,CAACC,OAAO,EAAE;QACzB,OAAO;UAAEE,OAAO,EAAE,IAAI;UAAEW,MAAM,EAAE7B,QAAQ,CAACe,IAAI,CAACe;QAAQ,CAAC;MACzD;MAEA,OAAO;QAAEZ,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAED,QAAQ,CAACe,IAAI,CAACI;MAAS,CAAC;IAC1D,CAAC,CAAC,OAAOlB,KAAU,EAAE;MAAA,IAAA8B,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEd,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAE,EAAA8B,gBAAA,GAAA9B,KAAK,CAACD,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBb,QAAQ,KAAI;MAA2B,CAAC;IAChG;EACF;;EAEA;;EAEA,MAAMc,UAAUA,CAACC,OAAe,EAAEC,QAAgB,GAAG,CAAC,EAAyE;IAC7H,IAAI;MACF;MACA,MAAMC,UAAqC,GAAG;QAC5CC,SAAS,EAAE,WAAW;QACtBC,SAAS,EAAE,WAAW;QACtBC,UAAU,EAAE,YAAY;QACxBC,GAAG,EAAE,KAAK;QAAE;QACZC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,aAAa;QACzBC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,YAAY,CAAE;MAC5B,CAAC;MAED,MAAMC,cAAc,GAAGT,UAAU,CAACF,OAAO,CAAC,IAAIA,OAAO;MAErD,MAAMlC,QAAQ,GAAG,MAAMb,SAAS,CAAC2B,IAAI,CAAC,uBAAuB,EAAE;QAC7DgC,IAAI,EAAED,cAAc;QACpBE,UAAU,EAAEZ;MACd,CAAC,CAAC;MAEF,OAAO;QACLjB,OAAO,EAAElB,QAAQ,CAACe,IAAI,CAACC,OAAO;QAC9BD,IAAI,EAAEf,QAAQ,CAACe,IAAI;QACnBd,KAAK,EAAED,QAAQ,CAACe,IAAI,CAACC,OAAO,GAAGQ,SAAS,GAAGxB,QAAQ,CAACe,IAAI,CAACI;MAC3D,CAAC;IACH,CAAC,CAAC,OAAOlB,KAAU,EAAE;MAAA,IAAA+C,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAE/B,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAE,EAAA+C,gBAAA,GAAA/C,KAAK,CAACD,QAAQ,cAAAgD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjC,IAAI,cAAAkC,qBAAA,uBAApBA,qBAAA,CAAsB9B,QAAQ,KAAI;MAAwB,CAAC;IAC7F;EACF;;EAEA;;EAEA,MAAM+B,WAAWA,CAAA,EAAmE;IAClF,IAAI;MACF,MAAMlD,QAAQ,GAAG,MAAMb,SAAS,CAACyC,GAAG,CAAC,WAAW,CAAC;MAEjD,OAAO;QACLV,OAAO,EAAElB,QAAQ,CAACe,IAAI,CAACC,OAAO;QAC9BmC,OAAO,EAAEnD,QAAQ,CAACe,IAAI,CAACqC,KAAK,IAAI,EAAE;QAClCnD,KAAK,EAAED,QAAQ,CAACe,IAAI,CAACC,OAAO,GAAGQ,SAAS,GAAGxB,QAAQ,CAACe,IAAI,CAACI;MAC3D,CAAC;IACH,CAAC,CAAC,OAAOlB,KAAU,EAAE;MAAA,IAAAoD,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEpC,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAE,EAAAoD,gBAAA,GAAApD,KAAK,CAACD,QAAQ,cAAAqD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtC,IAAI,cAAAuC,qBAAA,uBAApBA,qBAAA,CAAsBnC,QAAQ,KAAI;MAAmB,CAAC;IACxF;EACF;;EAEA;;EAEA,MAAMoC,eAAeA,CAAA,EAAoE;IACvF,IAAI;MACF,MAAMvD,QAAQ,GAAG,MAAMb,SAAS,CAACyC,GAAG,CAAC,oBAAoB,CAAC;MAE1D,OAAO;QACLV,OAAO,EAAElB,QAAQ,CAACe,IAAI,CAACC,OAAO;QAC9BwC,QAAQ,EAAExD,QAAQ,CAACe,IAAI,CAAC0C,SAAS,IAAI,EAAE;QACvCxD,KAAK,EAAED,QAAQ,CAACe,IAAI,CAACC,OAAO,GAAGQ,SAAS,GAAGxB,QAAQ,CAACe,IAAI,CAACI;MAC3D,CAAC;IACH,CAAC,CAAC,OAAOlB,KAAU,EAAE;MAAA,IAAAyD,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEzC,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAE,EAAAyD,gBAAA,GAAAzD,KAAK,CAACD,QAAQ,cAAA0D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3C,IAAI,cAAA4C,qBAAA,uBAApBA,qBAAA,CAAsBxC,QAAQ,KAAI;MAAwB,CAAC;IAC7F;EACF;EAEA,MAAMyC,eAAeA,CAACC,OAAe,EAAiD;IACpF,IAAI;MACF,MAAM7D,QAAQ,GAAG,MAAMb,SAAS,CAAC2B,IAAI,CAAC,gBAAgB,EAAE;QACtDK,QAAQ,EAAE0C;MACZ,CAAC,CAAC;MAEF,OAAO;QACL3C,OAAO,EAAElB,QAAQ,CAACe,IAAI,CAACC,OAAO;QAC9Bf,KAAK,EAAED,QAAQ,CAACe,IAAI,CAACC,OAAO,GAAGQ,SAAS,GAAGxB,QAAQ,CAACe,IAAI,CAACI;MAC3D,CAAC;IACH,CAAC,CAAC,OAAOlB,KAAU,EAAE;MAAA,IAAA6D,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAE7C,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAE,EAAA6D,gBAAA,GAAA7D,KAAK,CAACD,QAAQ,cAAA8D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/C,IAAI,cAAAgD,qBAAA,uBAApBA,qBAAA,CAAsB5C,QAAQ,KAAI;MAA0B,CAAC;IAC/F;EACF;;EAEA;;EAEA,MAAM6C,aAAaA,CAACC,UAAe,EAA6D;IAC9F,IAAI;MACF,MAAMjE,QAAQ,GAAG,MAAMb,SAAS,CAAC2B,IAAI,CAAC,iBAAiB,EAAEmD,UAAU,CAAC;MAEpE,OAAO;QACL/C,OAAO,EAAElB,QAAQ,CAACe,IAAI,CAACC,OAAO;QAC9BD,IAAI,EAAEf,QAAQ,CAACe,IAAI;QACnBd,KAAK,EAAED,QAAQ,CAACe,IAAI,CAACC,OAAO,GAAGQ,SAAS,GAAGxB,QAAQ,CAACe,IAAI,CAACI;MAC3D,CAAC;IACH,CAAC,CAAC,OAAOlB,KAAU,EAAE;MAAA,IAAAiE,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEjD,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAE,EAAAiE,gBAAA,GAAAjE,KAAK,CAACD,QAAQ,cAAAkE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnD,IAAI,cAAAoD,qBAAA,uBAApBA,qBAAA,CAAsBhD,QAAQ,KAAI;MAAyB,CAAC;IAC9F;EACF;;EAEA;;EAEA,MAAMiD,cAAcA,CAAA,EAAkD;IACpE,IAAI;MACF,MAAMpE,QAAQ,GAAG,MAAMb,SAAS,CAACyC,GAAG,CAAC,aAAa,CAAC;MACnD,OAAO;QAAEV,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOjB,KAAU,EAAE;MACnB,OAAO;QAAEiB,OAAO,EAAE,KAAK;QAAEjB,KAAK,EAAE;MAAiC,CAAC;IACpE;EACF;;EAEA;EACAoE,oBAAoBA,CAACC,aAA4B,EAAO;IACtD,OAAO;MACLC,GAAG,EAAED,aAAa,CAACC,GAAG;MACtBhD,IAAI,EAAE+C,aAAa,CAAC/C,IAAI;MACxBX,KAAK,EAAE0D,aAAa,CAAC1D,KAAK;MAC1B4D,EAAE,EAAEF,aAAa,CAACE,EAAE;MACpBC,KAAK,EAAEH,aAAa,CAACI,KAAK;MAC1BC,EAAE,EAAEL,aAAa,CAACK,EAAE;MACpBC,aAAa,EAAE,GAAG;MAAE;MACpBC,IAAI,EAAEP,aAAa,CAACQ,QAAQ;MAC5BC,KAAK,EAAGT,aAAa,CAASS,KAAK,IAAI,CAAC;MAAE;MAC1CC,SAAS,EAAEV,aAAa,CAACW,UAAU;MACnCC,SAAS,EAAEZ,aAAa,CAACa;IAC3B,CAAC;EACH;;EAEA;EACAC,kBAAkBA,CAACd,aAA4B,EAAE;IAC/C,OAAO;MACLjC,SAAS,EAAEiC,aAAa,CAACjC,SAAS,IAAI,CAAC;MACvCC,SAAS,EAAEgC,aAAa,CAAChC,SAAS,IAAI,CAAC;MACvCC,UAAU,EAAE+B,aAAa,CAAC/B,UAAU,IAAI,CAAC;MACzCC,GAAG,EAAE8B,aAAa,CAACe,GAAG,IAAI,CAAC;MAAE;MAC7B5C,QAAQ,EAAE6B,aAAa,CAAC7B,QAAQ,IAAI,CAAC;MACrCC,UAAU,EAAE4B,aAAa,CAACgB,WAAW,IAAI,CAAC;MAC1C3C,QAAQ,EAAE2B,aAAa,CAAC3B,QAAQ,IAAI,CAAC;MACrCC,UAAU,EAAG0B,aAAa,CAAS1B,UAAU,IAAI,CAAC,CAAE;IACtD,CAAC;EACH;AACF;AAEA,OAAO,MAAM2C,cAAc,GAAG,IAAI7E,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}