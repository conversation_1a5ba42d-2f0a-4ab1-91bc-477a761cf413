{"ast": null, "code": "import React,{useEffect}from'react';import{Routes,Route}from'react-router-dom';import{useSimpleAuth}from'../stores/simpleAuthStore';import{usePlayer}from'../stores/playerStore';import{useChat}from'../stores/chatStore';// Componente do Dashboard Simplificado\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SimpleDashboard=()=>{const{user}=useSimpleAuth();const{currentPlayer,isLoadingPlayer,loadPlayerData,hasPlayerData}=usePlayer();useEffect(()=>{if(!hasPlayerData&&!isLoadingPlayer){console.log('SimpleDashboard - Carregando dados do jogador...');loadPlayerData();}},[hasPlayerData,isLoadingPlayer,loadPlayerData]);// Usar dados do player se disponível, senão usar dados mockados\nconst playerData=currentPlayer||{pontos:1250,nivel:15,conquistas:42,ranking:7};return/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold cyber-text\",children:\"Terminal Principal\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-text-muted\",children:[\"Operador: \",/*#__PURE__*/_jsx(\"span\",{className:\"text-cyber-primary\",children:(user===null||user===void 0?void 0:user.nick)||'Jogador'})]})]}),isLoadingPlayer&&/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold mb-4 cyber-text\",children:\"\\uD83D\\uDCCA Status do Sistema\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-bg-tertiary rounded-lg p-3 border border-blue-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold text-blue-400\",children:isLoadingPlayer?'...':playerData.pontos.toLocaleString()}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-blue-300\",children:\"PONTOS\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-bg-tertiary rounded-lg p-3 border border-green-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold text-green-400\",children:isLoadingPlayer?'...':playerData.nivel}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-green-300\",children:\"N\\xCDVEL\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-bg-tertiary rounded-lg p-3 border border-purple-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold text-purple-400\",children:isLoadingPlayer?'...':playerData.conquistas}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-purple-300\",children:\"CONQUISTAS\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-bg-tertiary rounded-lg p-3 border border-orange-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold text-orange-400\",children:isLoadingPlayer?'...':playerData.ranking}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-orange-300\",children:\"RANKING\"})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold mb-4 cyber-text\",children:\"\\u26A1 Acesso R\\xE1pido\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-3\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>window.location.href='/game/scanner',className:\"btn-cyber text-sm py-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83D\\uDD0D\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Scanner\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>window.location.href='/game/chat',className:\"btn-cyber text-sm py-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83D\\uDCAC\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Chat\"})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"btn-cyber text-sm py-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83C\\uDFC6\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Loja\"})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"btn-cyber text-sm py-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\u2699\\uFE0F\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Config\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold mb-4 cyber-text\",children:\"\\uD83D\\uDCC8 Log do Sistema\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-sm\",children:\"+\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:\"Pontos ganhos\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-text-muted\",children:\"02:15:33\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-green-400 font-bold text-sm\",children:\"+150\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-sm\",children:\"\\uD83C\\uDFC6\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:\"Nova conquista\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-text-muted\",children:\"01:22:15\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-blue-400 font-bold text-sm\",children:\"HACK\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-sm\",children:\"\\u2191\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:\"Level UP\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-text-muted\",children:\"00:45:22\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-purple-400 font-bold text-sm\",children:\"LV.15\"})]})]})]})]})]});};// Componentes de placeholder para outras páginas\nconst SimpleScanner=()=>/*#__PURE__*/_jsx(\"div\",{className:\"p-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-4xl mx-auto\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold mb-4\",children:\"\\uD83D\\uDD0D Scanner\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-muted mb-6\",children:\"Funcionalidade do scanner ser\\xE1 implementada aqui.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-bg-tertiary p-8 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-6xl mb-4\",children:\"\\uD83D\\uDEA7\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Em desenvolvimento...\"})]})]})})});const SimpleChat=()=>{const{messages,isLoading:isLoadingMessages,loadMessages,sendMessage,isSending}=useChat();const[newMessage,setNewMessage]=React.useState('');useEffect(()=>{if(messages.length===0&&!isLoadingMessages){console.log('SimpleChat - Carregando mensagens...');loadMessages();}},[messages.length,isLoadingMessages,loadMessages]);const handleSendMessage=async e=>{e.preventDefault();if(newMessage.trim()&&!isSending){await sendMessage(newMessage.trim());setNewMessage('');}};return/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border-b border-cyber-primary\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-xl font-bold cyber-text\",children:\"\\uD83D\\uDCAC Terminal de Chat\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-text-muted\",children:\"Canal Global - Criptografado\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-black rounded-lg p-3 h-80 overflow-y-auto mb-4 border border-cyber-primary\",children:isLoadingMessages?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-cyber-primary\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary mx-auto mb-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-mono\",children:\"CARREGANDO DADOS...\"})]}):messages.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-cyber-primary\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl mb-2\",children:\"\\uD83D\\uDCAC\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs font-mono\",children:\"CANAL VAZIO - AGUARDANDO TRANSMISS\\xC3O\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:messages.map(message=>/*#__PURE__*/_jsxs(\"div\",{className:\"border-l-2 border-cyber-primary pl-3 py-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-1\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-cyber-primary font-mono text-xs\",children:[\"[\",new Date(message.timestamp).toLocaleTimeString(),\"]\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-blue-400 font-mono text-xs font-bold\",children:[message.usuario,\":\"]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-mono text-sm pl-2\",children:message.mensagem})]},message.id))})}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSendMessage,className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:newMessage,onChange:e=>setNewMessage(e.target.value),placeholder:\"> Digite comando...\",className:\"flex-1 px-3 py-2 bg-black border border-cyber-primary rounded font-mono text-cyber-primary text-sm focus:outline-none focus:border-cyber-secondary placeholder-gray-500\",disabled:isSending}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:!newMessage.trim()||isSending,className:`px-4 py-2 rounded font-mono text-sm transition-all ${!newMessage.trim()||isSending?'bg-gray-800 text-gray-500 cursor-not-allowed':'bg-cyber-primary text-black hover:bg-cyber-secondary font-bold'}`,children:isSending?'...':'SEND'})]})]})]})});};// Navegação Estilo Celular/Jogo\nconst SimpleNavigation=()=>{const currentPath=window.location.pathname;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-bg-primary\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-900 to-purple-900 p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-black font-bold text-lg\",children:\"S\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-xl font-bold cyber-text\",children:\"SHACK\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-text-muted\",children:\"Web Terminal\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"status-online\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-green-400\",children:\"ONLINE\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-bg-secondary border-b border-cyber-primary\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsxs(\"a\",{href:\"/game\",className:`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath==='/game'||currentPath==='/game/'?'bg-cyber-primary text-black border-b-2 border-cyber-primary':'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83C\\uDFE0\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Dashboard\"})]}),/*#__PURE__*/_jsxs(\"a\",{href:\"/game/scanner\",className:`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath.includes('/scanner')?'bg-cyber-primary text-black border-b-2 border-cyber-primary':'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83D\\uDD0D\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Scanner\"})]}),/*#__PURE__*/_jsxs(\"a\",{href:\"/game/chat\",className:`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath.includes('/chat')?'bg-cyber-primary text-black border-b-2 border-cyber-primary':'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83D\\uDCAC\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Chat\"})]})]})})]});};// Página Principal do Jogo Simplificada\nconst SimpleGamePage=()=>{console.log('SimpleGamePage - Renderizando...');return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-bg-primary text-text-primary\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-md mx-auto bg-bg-primary min-h-screen relative\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-b from-bg-primary to-bg-secondary min-h-screen\",children:[/*#__PURE__*/_jsx(SimpleNavigation,{}),/*#__PURE__*/_jsx(\"div\",{className:\"pb-4\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(SimpleDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/scanner\",element:/*#__PURE__*/_jsx(SimpleScanner,{})}),/*#__PURE__*/_jsx(Route,{path:\"/chat\",element:/*#__PURE__*/_jsx(SimpleChat,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(SimpleDashboard,{})})]})})]})})});};export default SimpleGamePage;", "map": {"version": 3, "names": ["React", "useEffect", "Routes", "Route", "useSimpleAuth", "usePlayer", "useChat", "jsx", "_jsx", "jsxs", "_jsxs", "SimpleDashboard", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "conquistas", "ranking", "className", "children", "nick", "toLocaleString", "onClick", "window", "location", "href", "SimpleScanner", "SimpleChat", "messages", "isLoading", "isLoadingMessages", "loadMessages", "sendMessage", "isSending", "newMessage", "setNewMessage", "useState", "length", "handleSendMessage", "e", "preventDefault", "trim", "map", "message", "Date", "timestamp", "toLocaleTimeString", "usuario", "mensagem", "id", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "SimpleNavigation", "currentPath", "pathname", "includes", "SimpleGamePage", "path", "element"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleGamePage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useChat } from '../stores/chatStore';\n\n// Componente do Dashboard Simplificado\nconst SimpleDashboard: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n\n  return (\n    <div className=\"p-4\">\n      {/* Header compacto */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold cyber-text\">\n              Terminal Principal\n            </h1>\n            <p className=\"text-sm text-text-muted\">\n              Operador: <span className=\"text-cyber-primary\">{user?.nick || 'Jogador'}</span>\n            </p>\n          </div>\n          {isLoadingPlayer && (\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary\"></div>\n          )}\n        </div>\n      </div>\n\n      {/* Stats do Jogador - Layout Celular */}\n      <div className=\"space-y-4\">\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">📊 Status do Sistema</h2>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-blue-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-blue-400\">\n                    {isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()}\n                  </div>\n                  <div className=\"text-xs text-blue-300\">PONTOS</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-green-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-green-400\">\n                    {isLoadingPlayer ? '...' : playerData.nivel}\n                  </div>\n                  <div className=\"text-xs text-green-300\">NÍVEL</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-purple-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-purple-400\">\n                    {isLoadingPlayer ? '...' : playerData.conquistas}\n                  </div>\n                  <div className=\"text-xs text-purple-300\">CONQUISTAS</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-orange-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-orange-400\">\n                    {isLoadingPlayer ? '...' : playerData.ranking}\n                  </div>\n                  <div className=\"text-xs text-orange-300\">RANKING</div>\n                </div>\n              </div>\n            </div>\n        </div>\n\n        {/* Ações Rápidas - Estilo Celular */}\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">⚡ Acesso Rápido</h2>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <button\n                onClick={() => window.location.href = '/game/scanner'}\n                className=\"btn-cyber text-sm py-3\"\n              >\n                <div className=\"text-lg mb-1\">🔍</div>\n                <div>Scanner</div>\n              </button>\n              <button\n                onClick={() => window.location.href = '/game/chat'}\n                className=\"btn-cyber text-sm py-3\"\n              >\n                <div className=\"text-lg mb-1\">💬</div>\n                <div>Chat</div>\n              </button>\n              <button className=\"btn-cyber text-sm py-3\">\n                <div className=\"text-lg mb-1\">🏆</div>\n                <div>Loja</div>\n              </button>\n              <button className=\"btn-cyber text-sm py-3\">\n                <div className=\"text-lg mb-1\">⚙️</div>\n                <div>Config</div>\n              </button>\n            </div>\n        </div>\n\n        {/* Log de Atividades - Estilo Terminal */}\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">📈 Log do Sistema</h2>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">+</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Pontos ganhos</div>\n                    <div className=\"text-xs text-text-muted\">02:15:33</div>\n                  </div>\n                </div>\n                <div className=\"text-green-400 font-bold text-sm\">+150</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">🏆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Nova conquista</div>\n                    <div className=\"text-xs text-text-muted\">01:22:15</div>\n                  </div>\n                </div>\n                <div className=\"text-blue-400 font-bold text-sm\">HACK</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">↑</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Level UP</div>\n                    <div className=\"text-xs text-text-muted\">00:45:22</div>\n                  </div>\n                </div>\n                <div className=\"text-purple-400 font-bold text-sm\">LV.15</div>\n              </div>\n            </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Componentes de placeholder para outras páginas\nconst SimpleScanner: React.FC = () => (\n  <div className=\"p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"card text-center\">\n        <h1 className=\"text-3xl font-bold mb-4\">🔍 Scanner</h1>\n        <p className=\"text-text-muted mb-6\">Funcionalidade do scanner será implementada aqui.</p>\n        <div className=\"bg-bg-tertiary p-8 rounded-lg\">\n          <div className=\"text-6xl mb-4\">🚧</div>\n          <p>Em desenvolvimento...</p>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nconst SimpleChat: React.FC = () => {\n  const { messages, isLoading: isLoadingMessages, loadMessages, sendMessage, isSending } = useChat();\n  const [newMessage, setNewMessage] = React.useState('');\n\n  useEffect(() => {\n    if (messages.length === 0 && !isLoadingMessages) {\n      console.log('SimpleChat - Carregando mensagens...');\n      loadMessages();\n    }\n  }, [messages.length, isLoadingMessages, loadMessages]);\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (newMessage.trim() && !isSending) {\n      await sendMessage(newMessage.trim());\n      setNewMessage('');\n    }\n  };\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary\">\n        <div className=\"p-4 border-b border-cyber-primary\">\n          <h1 className=\"text-xl font-bold cyber-text\">💬 Terminal de Chat</h1>\n          <p className=\"text-xs text-text-muted\">Canal Global - Criptografado</p>\n        </div>\n\n        {/* Área de mensagens - Estilo Terminal */}\n        <div className=\"p-4\">\n          <div className=\"bg-black rounded-lg p-3 h-80 overflow-y-auto mb-4 border border-cyber-primary\">\n            {isLoadingMessages ? (\n              <div className=\"text-center text-cyber-primary\">\n                <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary mx-auto mb-2\"></div>\n                <span className=\"text-xs font-mono\">CARREGANDO DADOS...</span>\n              </div>\n            ) : messages.length === 0 ? (\n              <div className=\"text-center text-cyber-primary\">\n                <div className=\"text-2xl mb-2\">💬</div>\n                <p className=\"text-xs font-mono\">CANAL VAZIO - AGUARDANDO TRANSMISSÃO</p>\n              </div>\n            ) : (\n              <div className=\"space-y-2\">\n                {messages.map((message) => (\n                  <div key={message.id} className=\"border-l-2 border-cyber-primary pl-3 py-1\">\n                    <div className=\"flex items-center space-x-2 mb-1\">\n                      <span className=\"text-cyber-primary font-mono text-xs\">\n                        [{new Date(message.timestamp).toLocaleTimeString()}]\n                      </span>\n                      <span className=\"text-blue-400 font-mono text-xs font-bold\">\n                        {message.usuario}:\n                      </span>\n                    </div>\n                    <p className=\"text-white font-mono text-sm pl-2\">{message.mensagem}</p>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Formulário de envio - Estilo Terminal */}\n          <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n            <input\n              type=\"text\"\n              value={newMessage}\n              onChange={(e) => setNewMessage(e.target.value)}\n              placeholder=\"> Digite comando...\"\n              className=\"flex-1 px-3 py-2 bg-black border border-cyber-primary rounded font-mono text-cyber-primary text-sm focus:outline-none focus:border-cyber-secondary placeholder-gray-500\"\n              disabled={isSending}\n            />\n            <button\n              type=\"submit\"\n              disabled={!newMessage.trim() || isSending}\n              className={`px-4 py-2 rounded font-mono text-sm transition-all ${\n                !newMessage.trim() || isSending\n                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'\n                  : 'bg-cyber-primary text-black hover:bg-cyber-secondary font-bold'\n              }`}\n            >\n              {isSending ? '...' : 'SEND'}\n            </button>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Navegação Estilo Celular/Jogo\nconst SimpleNavigation: React.FC = () => {\n  const currentPath = window.location.pathname;\n\n  return (\n    <div className=\"bg-bg-primary\">\n      {/* Header estilo celular */}\n      <div className=\"bg-gradient-to-r from-blue-900 to-purple-900 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-black font-bold text-lg\">S</span>\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold cyber-text\">SHACK</h1>\n              <p className=\"text-xs text-text-muted\">Web Terminal</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"status-online\"></div>\n            <span className=\"text-xs text-green-400\">ONLINE</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Menu de navegação estilo celular */}\n      <div className=\"bg-bg-secondary border-b border-cyber-primary\">\n        <div className=\"flex\">\n          <a\n            href=\"/game\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath === '/game' || currentPath === '/game/'\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">🏠</div>\n            <div>Dashboard</div>\n          </a>\n          <a\n            href=\"/game/scanner\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath.includes('/scanner')\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">🔍</div>\n            <div>Scanner</div>\n          </a>\n          <a\n            href=\"/game/chat\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath.includes('/chat')\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">💬</div>\n            <div>Chat</div>\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Página Principal do Jogo Simplificada\nconst SimpleGamePage: React.FC = () => {\n  console.log('SimpleGamePage - Renderizando...');\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary\">\n      {/* Container estilo celular */}\n      <div className=\"max-w-md mx-auto bg-bg-primary min-h-screen relative\">\n        {/* Simulação de tela de celular */}\n        <div className=\"bg-gradient-to-b from-bg-primary to-bg-secondary min-h-screen\">\n          <SimpleNavigation />\n\n          {/* Conteúdo principal */}\n          <div className=\"pb-4\">\n            <Routes>\n              <Route path=\"/\" element={<SimpleDashboard />} />\n              <Route path=\"/scanner\" element={<SimpleScanner />} />\n              <Route path=\"/chat\" element={<SimpleChat />} />\n              <Route path=\"*\" element={<SimpleDashboard />} />\n            </Routes>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleGamePage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CAChD,OAASC,aAAa,KAAQ,2BAA2B,CACzD,OAASC,SAAS,KAAQ,uBAAuB,CACjD,OAASC,OAAO,KAAQ,qBAAqB,CAE7C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAAEC,IAAK,CAAC,CAAGR,aAAa,CAAC,CAAC,CAChC,KAAM,CAAES,aAAa,CAAEC,eAAe,CAAEC,cAAc,CAAEC,aAAc,CAAC,CAAGX,SAAS,CAAC,CAAC,CAErFJ,SAAS,CAAC,IAAM,CACd,GAAI,CAACe,aAAa,EAAI,CAACF,eAAe,CAAE,CACtCG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC,CAC/DH,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAACC,aAAa,CAAEF,eAAe,CAAEC,cAAc,CAAC,CAAC,CAEpD;AACA,KAAM,CAAAI,UAAU,CAAGN,aAAa,EAAI,CAClCO,MAAM,CAAE,IAAI,CACZC,KAAK,CAAE,EAAE,CACTC,UAAU,CAAE,EAAE,CACdC,OAAO,CAAE,CACX,CAAC,CAED,mBACEb,KAAA,QAAKc,SAAS,CAAC,KAAK,CAAAC,QAAA,eAElBjB,IAAA,QAAKgB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBf,KAAA,QAAKc,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDf,KAAA,QAAAe,QAAA,eACEjB,IAAA,OAAIgB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,oBAE9C,CAAI,CAAC,cACLf,KAAA,MAAGc,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EAAC,YAC3B,cAAAjB,IAAA,SAAMgB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAE,CAAAb,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEc,IAAI,GAAI,SAAS,CAAO,CAAC,EAC9E,CAAC,EACD,CAAC,CACLZ,eAAe,eACdN,IAAA,QAAKgB,SAAS,CAAC,mEAAmE,CAAM,CACzF,EACE,CAAC,CACH,CAAC,cAGNd,KAAA,QAAKc,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBf,KAAA,QAAKc,SAAS,CAAC,+EAA+E,CAAAC,QAAA,eAC1FjB,IAAA,OAAIgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,gCAAoB,CAAI,CAAC,cAC/Ef,KAAA,QAAKc,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCjB,IAAA,QAAKgB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cACnEf,KAAA,QAAKc,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjB,IAAA,QAAKgB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC7CX,eAAe,CAAG,KAAK,CAAGK,UAAU,CAACC,MAAM,CAACO,cAAc,CAAC,CAAC,CAC1D,CAAC,cACNnB,IAAA,QAAKgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,QAAM,CAAK,CAAC,EAChD,CAAC,CACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,uDAAuD,CAAAC,QAAA,cACpEf,KAAA,QAAKc,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjB,IAAA,QAAKgB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC9CX,eAAe,CAAG,KAAK,CAAGK,UAAU,CAACE,KAAK,CACxC,CAAC,cACNb,IAAA,QAAKgB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,UAAK,CAAK,CAAC,EAChD,CAAC,CACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cACrEf,KAAA,QAAKc,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjB,IAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/CX,eAAe,CAAG,KAAK,CAAGK,UAAU,CAACG,UAAU,CAC7C,CAAC,cACNd,IAAA,QAAKgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,YAAU,CAAK,CAAC,EACtD,CAAC,CACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cACrEf,KAAA,QAAKc,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjB,IAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/CX,eAAe,CAAG,KAAK,CAAGK,UAAU,CAACI,OAAO,CAC1C,CAAC,cACNf,IAAA,QAAKgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,SAAO,CAAK,CAAC,EACnD,CAAC,CACH,CAAC,EACH,CAAC,EACL,CAAC,cAGNf,KAAA,QAAKc,SAAS,CAAC,+EAA+E,CAAAC,QAAA,eAC1FjB,IAAA,OAAIgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,yBAAe,CAAI,CAAC,cAC1Ef,KAAA,QAAKc,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCf,KAAA,WACEkB,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,eAAgB,CACtDP,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAElCjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,SAAO,CAAK,CAAC,EACZ,CAAC,cACTf,KAAA,WACEkB,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,YAAa,CACnDP,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAElCjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,MAAI,CAAK,CAAC,EACT,CAAC,cACTf,KAAA,WAAQc,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACxCjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,MAAI,CAAK,CAAC,EACT,CAAC,cACTf,KAAA,WAAQc,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACxCjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,QAAM,CAAK,CAAC,EACX,CAAC,EACN,CAAC,EACL,CAAC,cAGNf,KAAA,QAAKc,SAAS,CAAC,+EAA+E,CAAAC,QAAA,eAC1FjB,IAAA,OAAIgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,6BAAiB,CAAI,CAAC,cAC5Ef,KAAA,QAAKc,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBf,KAAA,QAAKc,SAAS,CAAC,qFAAqF,CAAAC,QAAA,eAClGf,KAAA,QAAKc,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjB,IAAA,QAAKgB,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cACjFjB,IAAA,SAAMgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,CACpD,CAAC,cACNf,KAAA,QAAAe,QAAA,eACEjB,IAAA,QAAKgB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,eAAa,CAAK,CAAC,cACxDjB,IAAA,QAAKgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,EACpD,CAAC,EACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,MAAI,CAAK,CAAC,EACzD,CAAC,cAENf,KAAA,QAAKc,SAAS,CAAC,oFAAoF,CAAAC,QAAA,eACjGf,KAAA,QAAKc,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjB,IAAA,QAAKgB,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChFjB,IAAA,SAAMgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,CACrD,CAAC,cACNf,KAAA,QAAAe,QAAA,eACEjB,IAAA,QAAKgB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,gBAAc,CAAK,CAAC,cACzDjB,IAAA,QAAKgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,EACpD,CAAC,EACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAC,MAAI,CAAK,CAAC,EACxD,CAAC,cAENf,KAAA,QAAKc,SAAS,CAAC,sFAAsF,CAAAC,QAAA,eACnGf,KAAA,QAAKc,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjB,IAAA,QAAKgB,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cAClFjB,IAAA,SAAMgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,CACpD,CAAC,cACNf,KAAA,QAAAe,QAAA,eACEjB,IAAA,QAAKgB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,cACnDjB,IAAA,QAAKgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,EACpD,CAAC,EACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,OAAK,CAAK,CAAC,EAC3D,CAAC,EACH,CAAC,EACL,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAO,aAAuB,CAAGA,CAAA,gBAC9BxB,IAAA,QAAKgB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBjB,IAAA,QAAKgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCf,KAAA,QAAKc,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BjB,IAAA,OAAIgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,sBAAU,CAAI,CAAC,cACvDjB,IAAA,MAAGgB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,sDAAiD,CAAG,CAAC,cACzFf,KAAA,QAAKc,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CjB,IAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACvCjB,IAAA,MAAAiB,QAAA,CAAG,uBAAqB,CAAG,CAAC,EACzB,CAAC,EACH,CAAC,CACH,CAAC,CACH,CACN,CAED,KAAM,CAAAQ,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAEC,QAAQ,CAAEC,SAAS,CAAEC,iBAAiB,CAAEC,YAAY,CAAEC,WAAW,CAAEC,SAAU,CAAC,CAAGjC,OAAO,CAAC,CAAC,CAClG,KAAM,CAACkC,UAAU,CAAEC,aAAa,CAAC,CAAGzC,KAAK,CAAC0C,QAAQ,CAAC,EAAE,CAAC,CAEtDzC,SAAS,CAAC,IAAM,CACd,GAAIiC,QAAQ,CAACS,MAAM,GAAK,CAAC,EAAI,CAACP,iBAAiB,CAAE,CAC/CnB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC,CACnDmB,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAAE,CAACH,QAAQ,CAACS,MAAM,CAAEP,iBAAiB,CAAEC,YAAY,CAAC,CAAC,CAEtD,KAAM,CAAAO,iBAAiB,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACtDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAIN,UAAU,CAACO,IAAI,CAAC,CAAC,EAAI,CAACR,SAAS,CAAE,CACnC,KAAM,CAAAD,WAAW,CAACE,UAAU,CAACO,IAAI,CAAC,CAAC,CAAC,CACpCN,aAAa,CAAC,EAAE,CAAC,CACnB,CACF,CAAC,CAED,mBACEjC,IAAA,QAAKgB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBf,KAAA,QAAKc,SAAS,CAAC,2EAA2E,CAAAC,QAAA,eACxFf,KAAA,QAAKc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDjB,IAAA,OAAIgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,+BAAmB,CAAI,CAAC,cACrEjB,IAAA,MAAGgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,8BAA4B,CAAG,CAAC,EACpE,CAAC,cAGNf,KAAA,QAAKc,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBjB,IAAA,QAAKgB,SAAS,CAAC,+EAA+E,CAAAC,QAAA,CAC3FW,iBAAiB,cAChB1B,KAAA,QAAKc,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CjB,IAAA,QAAKgB,SAAS,CAAC,gFAAgF,CAAM,CAAC,cACtGhB,IAAA,SAAMgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,EAC3D,CAAC,CACJS,QAAQ,CAACS,MAAM,GAAK,CAAC,cACvBjC,KAAA,QAAKc,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CjB,IAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACvCjB,IAAA,MAAGgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,yCAAoC,CAAG,CAAC,EACtE,CAAC,cAENjB,IAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBS,QAAQ,CAACc,GAAG,CAAEC,OAAO,eACpBvC,KAAA,QAAsBc,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACzEf,KAAA,QAAKc,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/Cf,KAAA,SAAMc,SAAS,CAAC,sCAAsC,CAAAC,QAAA,EAAC,GACpD,CAAC,GAAI,CAAAyB,IAAI,CAACD,OAAO,CAACE,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAC,GACrD,EAAM,CAAC,cACP1C,KAAA,SAAMc,SAAS,CAAC,2CAA2C,CAAAC,QAAA,EACxDwB,OAAO,CAACI,OAAO,CAAC,GACnB,EAAM,CAAC,EACJ,CAAC,cACN7C,IAAA,MAAGgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEwB,OAAO,CAACK,QAAQ,CAAI,CAAC,GAT/DL,OAAO,CAACM,EAUb,CACN,CAAC,CACC,CACN,CACE,CAAC,cAGN7C,KAAA,SAAM8C,QAAQ,CAAEZ,iBAAkB,CAACpB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC3DjB,IAAA,UACEiD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAElB,UAAW,CAClBmB,QAAQ,CAAGd,CAAC,EAAKJ,aAAa,CAACI,CAAC,CAACe,MAAM,CAACF,KAAK,CAAE,CAC/CG,WAAW,CAAC,qBAAqB,CACjCrC,SAAS,CAAC,yKAAyK,CACnLsC,QAAQ,CAAEvB,SAAU,CACrB,CAAC,cACF/B,IAAA,WACEiD,IAAI,CAAC,QAAQ,CACbK,QAAQ,CAAE,CAACtB,UAAU,CAACO,IAAI,CAAC,CAAC,EAAIR,SAAU,CAC1Cf,SAAS,CAAE,sDACT,CAACgB,UAAU,CAACO,IAAI,CAAC,CAAC,EAAIR,SAAS,CAC3B,8CAA8C,CAC9C,gEAAgE,EACnE,CAAAd,QAAA,CAEFc,SAAS,CAAG,KAAK,CAAG,MAAM,CACrB,CAAC,EACL,CAAC,EACJ,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAwB,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAAAC,WAAW,CAAGnC,MAAM,CAACC,QAAQ,CAACmC,QAAQ,CAE5C,mBACEvD,KAAA,QAAKc,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE5BjB,IAAA,QAAKgB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAC/Df,KAAA,QAAKc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDf,KAAA,QAAKc,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CjB,IAAA,QAAKgB,SAAS,CAAC,wEAAwE,CAAAC,QAAA,cACrFjB,IAAA,SAAMgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,CACpD,CAAC,cACNf,KAAA,QAAAe,QAAA,eACEjB,IAAA,OAAIgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,cACvDjB,IAAA,MAAGgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,EACpD,CAAC,EACH,CAAC,cACNf,KAAA,QAAKc,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CjB,IAAA,QAAKgB,SAAS,CAAC,eAAe,CAAM,CAAC,cACrChB,IAAA,SAAMgB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,EACnD,CAAC,EACH,CAAC,CACH,CAAC,cAGNjB,IAAA,QAAKgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5Df,KAAA,QAAKc,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBf,KAAA,MACEqB,IAAI,CAAC,OAAO,CACZP,SAAS,CAAE,mEACTwC,WAAW,GAAK,OAAO,EAAIA,WAAW,GAAK,QAAQ,CAC/C,6DAA6D,CAC7D,+DAA+D,EAClE,CAAAvC,QAAA,eAEHjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,WAAS,CAAK,CAAC,EACnB,CAAC,cACJf,KAAA,MACEqB,IAAI,CAAC,eAAe,CACpBP,SAAS,CAAE,mEACTwC,WAAW,CAACE,QAAQ,CAAC,UAAU,CAAC,CAC5B,6DAA6D,CAC7D,+DAA+D,EAClE,CAAAzC,QAAA,eAEHjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,SAAO,CAAK,CAAC,EACjB,CAAC,cACJf,KAAA,MACEqB,IAAI,CAAC,YAAY,CACjBP,SAAS,CAAE,mEACTwC,WAAW,CAACE,QAAQ,CAAC,OAAO,CAAC,CACzB,6DAA6D,CAC7D,+DAA+D,EAClE,CAAAzC,QAAA,eAEHjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,MAAI,CAAK,CAAC,EACd,CAAC,EACD,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA0C,cAAwB,CAAGA,CAAA,GAAM,CACrClD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAE/C,mBACEV,IAAA,QAAKgB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,cAE3DjB,IAAA,QAAKgB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cAEnEf,KAAA,QAAKc,SAAS,CAAC,+DAA+D,CAAAC,QAAA,eAC5EjB,IAAA,CAACuD,gBAAgB,GAAE,CAAC,cAGpBvD,IAAA,QAAKgB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBf,KAAA,CAACR,MAAM,EAAAuB,QAAA,eACLjB,IAAA,CAACL,KAAK,EAACiE,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE7D,IAAA,CAACG,eAAe,GAAE,CAAE,CAAE,CAAC,cAChDH,IAAA,CAACL,KAAK,EAACiE,IAAI,CAAC,UAAU,CAACC,OAAO,cAAE7D,IAAA,CAACwB,aAAa,GAAE,CAAE,CAAE,CAAC,cACrDxB,IAAA,CAACL,KAAK,EAACiE,IAAI,CAAC,OAAO,CAACC,OAAO,cAAE7D,IAAA,CAACyB,UAAU,GAAE,CAAE,CAAE,CAAC,cAC/CzB,IAAA,CAACL,KAAK,EAACiE,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE7D,IAAA,CAACG,eAAe,GAAE,CAAE,CAAE,CAAC,EAC1C,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAwD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}