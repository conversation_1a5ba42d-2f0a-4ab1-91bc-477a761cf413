import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  GAME_CONFIG,
  getPlayerLevel,
  getXpForNextLevel,
  calculateUpgradeCost,
  calculateXpReward,
  calculateMineradoraProduction,
  Player,
  PlayerApps,
  GameNotification,
  HackTarget
} from '../types/game';
import { backendService, BackendPlayer } from '../services/backendService';

interface GameState {
  // Player data
  player: Player | null;
  playerApps: PlayerApps;
  
  // Game state
  isOnline: boolean;
  notifications: GameNotification[];
  
  // Targets and hacking
  availableTargets: HackTarget[];
  hackHistory: any[];
  
  // UI state
  currentScreen: 'home' | 'apps' | 'scanner' | 'terminal' | 'profile' | 'settings' | 'shop' | 'ranking' | 'logs' | 'chat';
  isLoading: boolean;
  error: string | null;
  
  // Actions
  loginPlayer: (email: string, password: string) => Promise<boolean>;
  registerPlayer: (nick: string, email: string, password: string) => Promise<boolean>;
  loadPlayer: () => Promise<boolean>;
  upgradeApp: (appId: keyof PlayerApps) => Promise<boolean>;
  collectMineradoraProduction: () => void;
  setCurrentScreen: (screen: GameState['currentScreen']) => void;
  addNotification: (notification: Omit<GameNotification, 'id' | 'timestamp'>) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
  syncWithServer: () => Promise<void>;
  logout: () => void;
  reset: () => void;
}

const initialPlayerApps: PlayerApps = {
  ...GAME_CONFIG.INITIAL_APPS,
  mineradora: 1, // Garantir que a mineradora está incluída
};

export const useHackGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      // Initial state
      player: null,
      playerApps: initialPlayerApps,
      isOnline: false,
      notifications: [],
      availableTargets: [],
      hackHistory: [],
      currentScreen: 'home',
      isLoading: false,
      error: null,

      // Actions
      loginPlayer: async (email: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          const result = await backendService.login(email, password);

          if (result.success) {
            // Carregar dados do jogador após login
            const success = await get().loadPlayer();
            return success;
          }

          set({ error: result.error || 'Erro ao fazer login', isLoading: false });
          return false;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          return false;
        }
      },

      registerPlayer: async (nick: string, email: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          const result = await backendService.register(nick, email, password);

          if (result.success) {
            set({ isLoading: false });
            return true;
          }

          set({ error: result.error || 'Erro ao registrar jogador', isLoading: false });
          return false;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          return false;
        }
      },

      loadPlayer: async () => {
        set({ isLoading: true, error: null });

        try {
          const result = await backendService.getPlayer();

          if (result.success && result.player) {
            const player = backendService.convertBackendPlayer(result.player);
            const playerApps = backendService.convertBackendApps(result.player);

            set({
              player,
              playerApps,
              isOnline: true,
              isLoading: false,
            });

            return true;
          }

          set({ error: result.error || 'Erro ao carregar jogador', isLoading: false });
          return false;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          return false;
        }
      },

      upgradeApp: async (appId: keyof PlayerApps) => {
        const state = get();
        const { player, playerApps } = state;

        if (!player) return false;

        const currentLevel = playerApps[appId];
        const upgradeCost = calculateUpgradeCost(currentLevel);

        // Verificar se tem dinheiro suficiente
        if (player.cash < upgradeCost.cash) {
          set({ error: 'Dinheiro insuficiente para upgrade!' });
          return false;
        }

        // Verificar se tem shack suficiente (para upgrades nível 10+)
        if (upgradeCost.shack > 0 && (player.shack || 0) < upgradeCost.shack) {
          set({ error: 'Shack insuficiente para upgrade!' });
          return false;
        }

        try {
          set({ isLoading: true });

          // Fazer upgrade no backend
          const result = await backendService.upgradeApp(appId, 1);

          if (result.success && result.data) {
            // Recarregar dados do jogador para ter os valores atualizados
            await get().loadPlayer();

            // Adicionar notificação local
            const costMessage = upgradeCost.shack > 0
              ? `Custo: $${upgradeCost.cash} + ${upgradeCost.shack} shack`
              : `Custo: $${upgradeCost.cash}`;

            get().addNotification({
              type: 'upgrade',
              title: 'Upgrade Concluído!',
              message: `${appId} foi atualizado para nível ${currentLevel + 1}! ${costMessage}`,
            });

            // Verificar se subiu de nível
            if (result.data.level_up) {
              get().addNotification({
                type: 'level',
                title: 'Level Up!',
                message: `Parabéns! Você atingiu o nível ${result.data.nivel_jogador}!`,
              });
            }

            set({ isLoading: false });
            return true;
          }

          set({ error: result.error || 'Erro ao realizar upgrade', isLoading: false });
          return false;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          return false;
        }
      },

      collectMineradoraProduction: () => {
        const state = get();
        if (!state.player || !state.playerApps.mineradora) return;

        const mineradoraLevel = state.playerApps.mineradora;
        const production = calculateMineradoraProduction(mineradoraLevel, 1); // 1 hora de produção

        if (production.cash > 0 || production.shack > 0) {
          set((state) => ({
            player: state.player ? {
              ...state.player,
              cash: state.player.cash + production.cash,
              shack: (state.player.shack || 0) + production.shack,
            } : null
          }));

          // Adicionar notificação
          get().addNotification({
            type: 'system',
            title: 'Mineradora Coletada!',
            message: `+$${production.cash}${production.shack > 0 ? ` +${production.shack} ⛏️` : ''}`,
          });
        }
      },

      setCurrentScreen: (screen: GameState['currentScreen']) => {
        set({ currentScreen: screen });
      },

      addNotification: (notification: Omit<GameNotification, 'id' | 'timestamp'>) => {
        const newNotification: GameNotification = {
          ...notification,
          id: `notif_${Date.now()}_${Math.random()}`,
          timestamp: new Date().toISOString(),
        };

        set((state) => ({
          notifications: [newNotification, ...state.notifications].slice(0, 50) // Manter apenas 50 notificações
        }));
      },

      markNotificationRead: (id: string) => {
        set((state) => ({
          notifications: state.notifications.map(notif =>
            notif.id === id ? { ...notif, read: true } : notif
          )
        }));
      },

      clearNotifications: () => {
        set({ notifications: [] });
      },

      setError: (error: string | null) => {
        set({ error });
        if (error) {
          // Limpar erro após 5 segundos
          setTimeout(() => {
            set({ error: null });
          }, 5000);
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      syncWithServer: async () => {
        try {
          set({ isLoading: true });

          // Recarregar dados do jogador
          await get().loadPlayer();

          set({ isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      logout: () => {
        localStorage.removeItem('auth_token');
        get().reset();
      },

      reset: () => {
        set({
          player: null,
          playerApps: initialPlayerApps,
          isOnline: false,
          notifications: [],
          availableTargets: [],
          hackHistory: [],
          currentScreen: 'home',
          isLoading: false,
          error: null,
        });
      },
    }),
    {
      name: 'shack-game-storage',
      partialize: (state) => ({
        currentScreen: state.currentScreen,
        notifications: state.notifications,
      }),
      // Recarregar dados do servidor quando a store for hidratada
      onRehydrateStorage: () => (state) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          // Recarregar dados do servidor em background
          setTimeout(() => {
            state?.syncWithServer();
          }, 1000);
        }
      },
    }
  )
);
