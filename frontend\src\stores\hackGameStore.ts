import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  GAME_CONFIG,
  getPlayerLevel,
  getXpForNextLevel,
  calculateUpgradeCost,
  calculateXpReward
} from '../types/game';
import { gameService } from '../services/gameService';
import { Player, PlayerApps, GameNotification, HackTarget } from '../lib/supabase';

interface GameState {
  // Player data
  player: Player | null;
  playerApps: PlayerApps;
  
  // Game state
  isOnline: boolean;
  notifications: GameNotification[];
  
  // Targets and hacking
  availableTargets: HackTarget[];
  hackHistory: any[];
  
  // UI state
  currentScreen: 'home' | 'apps' | 'scanner' | 'terminal' | 'profile' | 'settings' | 'shop' | 'ranking' | 'logs' | 'chat';
  isLoading: boolean;
  error: string | null;
  
  // Actions
  initializePlayer: (nick: string, email: string) => Promise<boolean>;
  loadPlayer: (playerId: string) => Promise<boolean>;
  updatePlayerApps: (apps: Partial<PlayerApps>) => Promise<boolean>;
  upgradeApp: (appId: keyof PlayerApps) => Promise<boolean>;
  addXp: (amount: number) => Promise<void>;
  addCash: (amount: number) => Promise<void>;
  spendCash: (amount: number) => Promise<boolean>;
  setCurrentScreen: (screen: GameState['currentScreen']) => void;
  loadNotifications: () => Promise<void>;
  addNotification: (notification: Omit<GameNotification, 'id' | 'player_id' | 'created_at' | 'read'>) => Promise<void>;
  markNotificationRead: (id: string) => Promise<void>;
  clearNotifications: () => void;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
  syncWithServer: () => Promise<void>;
  reset: () => void;
}

const initialPlayerApps: PlayerApps = { ...GAME_CONFIG.INITIAL_APPS };

export const useHackGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      // Initial state
      player: null,
      playerApps: initialPlayerApps,
      isOnline: false,
      notifications: [],
      availableTargets: [],
      hackHistory: [],
      currentScreen: 'home',
      isLoading: false,
      error: null,

      // Actions
      initializePlayer: async (nick: string, email: string) => {
        set({ isLoading: true, error: null });

        try {
          const result = await gameService.createPlayer(nick, email);

          if (result.success && result.player) {
            const appsResult = await gameService.getPlayerApps(result.player.id);

            if (appsResult.success && appsResult.apps) {
              set({
                player: result.player,
                playerApps: {
                  antivirus: appsResult.apps.antivirus,
                  bankguard: appsResult.apps.bankguard,
                  bruteforce: appsResult.apps.bruteforce,
                  sdk: appsResult.apps.sdk,
                  firewall: appsResult.apps.firewall,
                  malwarekit: appsResult.apps.malwarekit,
                  proxyvpn: appsResult.apps.proxyvpn,
                },
                isOnline: true,
                isLoading: false,
              });

              // Carregar notificações
              await get().loadNotifications();
              return true;
            }
          }

          set({ error: result.error || 'Erro ao criar jogador', isLoading: false });
          return false;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          return false;
        }
      },

      loadPlayer: async (playerId: string) => {
        set({ isLoading: true, error: null });

        try {
          const [playerResult, appsResult] = await Promise.all([
            gameService.getPlayer(playerId),
            gameService.getPlayerApps(playerId)
          ]);

          if (playerResult.success && playerResult.player && appsResult.success && appsResult.apps) {
            set({
              player: playerResult.player,
              playerApps: {
                antivirus: appsResult.apps.antivirus,
                bankguard: appsResult.apps.bankguard,
                bruteforce: appsResult.apps.bruteforce,
                sdk: appsResult.apps.sdk,
                firewall: appsResult.apps.firewall,
                malwarekit: appsResult.apps.malwarekit,
                proxyvpn: appsResult.apps.proxyvpn,
              },
              isOnline: true,
              isLoading: false,
            });

            // Carregar notificações
            await get().loadNotifications();
            return true;
          }

          set({ error: 'Erro ao carregar jogador', isLoading: false });
          return false;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          return false;
        }
      },

      updatePlayerApps: async (apps: Partial<PlayerApps>) => {
        const state = get();
        if (!state.player) return false;

        try {
          const result = await gameService.updatePlayerApps(state.player.id, apps);

          if (result.success) {
            set((state) => ({
              playerApps: { ...state.playerApps, ...apps }
            }));
            return true;
          }

          set({ error: result.error || 'Erro ao atualizar apps' });
          return false;
        } catch (error: any) {
          set({ error: error.message });
          return false;
        }
      },

      upgradeApp: async (appId: keyof PlayerApps) => {
        const state = get();
        const { player, playerApps } = state;

        if (!player) return false;

        const currentLevel = playerApps[appId];
        const upgradeCost = calculateUpgradeCost(currentLevel);

        // Verificar se tem dinheiro suficiente
        if (player.cash < upgradeCost) {
          set({ error: 'Dinheiro insuficiente para upgrade!' });
          return false;
        }

        // Verificar se não atingiu o nível máximo
        if (currentLevel >= GAME_CONFIG.MAX_APP_LEVEL) {
          set({ error: 'App já está no nível máximo!' });
          return false;
        }

        try {
          // Realizar upgrade no servidor
          const newLevel = currentLevel + 1;
          const newCash = player.cash - upgradeCost;
          const xpReward = calculateXpReward(currentLevel);
          const newXp = player.xp + xpReward;
          const newPlayerLevel = getPlayerLevel(newXp);

          // Atualizar apps
          const appsUpdate = { [appId]: newLevel };
          const appsResult = await gameService.updatePlayerApps(player.id, appsUpdate);

          // Atualizar player
          const playerUpdate = {
            cash: newCash,
            xp: newXp,
            level: newPlayerLevel
          };
          const playerResult = await gameService.updatePlayer(player.id, playerUpdate);

          if (appsResult.success && playerResult.success) {
            // Atualizar estado local
            set((state) => ({
              player: {
                ...state.player!,
                cash: newCash,
                xp: newXp,
                level: newPlayerLevel,
              },
              playerApps: {
                ...state.playerApps,
                [appId]: newLevel,
              },
              error: null,
            }));

            // Adicionar notificação
            await get().addNotification({
              type: 'upgrade',
              title: 'Upgrade Concluído!',
              message: `${appId} foi atualizado para nível ${newLevel}. +${xpReward} XP`,
            });

            // Verificar se subiu de nível
            if (newPlayerLevel > player.level) {
              await get().addNotification({
                type: 'level',
                title: 'Level Up!',
                message: `Parabéns! Você atingiu o nível ${newPlayerLevel}!`,
              });
            }

            return true;
          }

          set({ error: 'Erro ao realizar upgrade' });
          return false;
        } catch (error: any) {
          set({ error: error.message });
          return false;
        }
      },

      addXp: async (amount: number) => {
        const state = get();
        if (!state.player) return;

        try {
          const newTotalXp = state.player.xp + amount;
          const newLevel = getPlayerLevel(newTotalXp);
          const leveledUp = newLevel > state.player.level;

          const result = await gameService.updatePlayer(state.player.id, {
            xp: newTotalXp,
            level: newLevel,
          });

          if (result.success) {
            set((state) => ({
              player: state.player ? {
                ...state.player,
                xp: newTotalXp,
                level: newLevel,
              } : null
            }));

            // Se subiu de nível, adicionar notificação
            if (leveledUp) {
              await get().addNotification({
                type: 'level',
                title: 'Level Up!',
                message: `Parabéns! Você atingiu o nível ${newLevel}!`,
              });
            }
          }
        } catch (error: any) {
          set({ error: error.message });
        }
      },

      addCash: async (amount: number) => {
        const state = get();
        if (!state.player) return;

        try {
          const newCash = state.player.cash + amount;
          const result = await gameService.updatePlayer(state.player.id, { cash: newCash });

          if (result.success) {
            set((state) => ({
              player: state.player ? {
                ...state.player,
                cash: newCash
              } : null
            }));
          }
        } catch (error: any) {
          set({ error: error.message });
        }
      },

      spendCash: async (amount: number) => {
        const state = get();
        if (!state.player || state.player.cash < amount) {
          return false;
        }

        try {
          const newCash = state.player.cash - amount;
          const result = await gameService.updatePlayer(state.player.id, { cash: newCash });

          if (result.success) {
            set((state) => ({
              player: state.player ? {
                ...state.player,
                cash: newCash
              } : null
            }));
            return true;
          }

          return false;
        } catch (error: any) {
          set({ error: error.message });
          return false;
        }
      },

      setCurrentScreen: (screen: GameState['currentScreen']) => {
        set({ currentScreen: screen });
      },

      loadNotifications: async () => {
        const state = get();
        if (!state.player) return;

        try {
          const result = await gameService.getNotifications(state.player.id);

          if (result.success && result.notifications) {
            set({ notifications: result.notifications });
          }
        } catch (error: any) {
          set({ error: error.message });
        }
      },

      addNotification: async (notification: Omit<GameNotification, 'id' | 'player_id' | 'created_at' | 'read'>) => {
        const state = get();
        if (!state.player) return;

        try {
          const result = await gameService.createNotification(state.player.id, notification);

          if (result.success) {
            // Recarregar notificações
            await get().loadNotifications();
          }
        } catch (error: any) {
          set({ error: error.message });
        }
      },

      markNotificationRead: async (id: string) => {
        try {
          const result = await gameService.markNotificationRead(id);

          if (result.success) {
            set((state) => ({
              notifications: state.notifications.map(notif =>
                notif.id === id ? { ...notif, read: true } : notif
              )
            }));
          }
        } catch (error: any) {
          set({ error: error.message });
        }
      },

      clearNotifications: () => {
        set({ notifications: [] });
      },

      setError: (error: string | null) => {
        set({ error });
        if (error) {
          // Limpar erro após 5 segundos
          setTimeout(() => {
            set({ error: null });
          }, 5000);
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      syncWithServer: async () => {
        const state = get();
        if (!state.player) return;

        try {
          set({ isLoading: true });

          // Recarregar dados do jogador
          await get().loadPlayer(state.player.id);

          set({ isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      reset: () => {
        set({
          player: null,
          playerApps: initialPlayerApps,
          isOnline: false,
          notifications: [],
          availableTargets: [],
          hackHistory: [],
          currentScreen: 'home',
          isLoading: false,
          error: null,
        });
      },
    }),
    {
      name: 'shack-game-storage',
      partialize: (state) => ({
        player: state.player,
        currentScreen: state.currentScreen,
      }),
      // Recarregar dados do servidor quando a store for hidratada
      onRehydrateStorage: () => (state) => {
        if (state?.player?.id) {
          // Recarregar dados do servidor em background
          setTimeout(() => {
            state.syncWithServer();
          }, 1000);
        }
      },
    }
  )
);
