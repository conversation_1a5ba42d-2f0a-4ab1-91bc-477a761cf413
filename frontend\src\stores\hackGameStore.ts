import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  <PERSON>, 
  PlayerApps, 
  GameNotification, 
  HackTarget,
  GAME_CONFIG,
  generateRandomIP,
  getPlayerLevel,
  getXpForNextLevel,
  calculateUpgradeCost,
  calculateXpReward
} from '../types/game';

interface GameState {
  // Player data
  player: Player | null;
  playerApps: PlayerApps;
  
  // Game state
  isOnline: boolean;
  notifications: GameNotification[];
  
  // Targets and hacking
  availableTargets: HackTarget[];
  hackHistory: any[];
  
  // UI state
  currentScreen: 'home' | 'apps' | 'scanner' | 'terminal' | 'profile' | 'settings';
  isLoading: boolean;
  error: string | null;
  
  // Actions
  initializePlayer: (nick: string, email: string) => void;
  updatePlayerApps: (apps: Partial<PlayerApps>) => void;
  upgradeApp: (appId: keyof PlayerApps) => boolean;
  addXp: (amount: number) => void;
  addCash: (amount: number) => void;
  spendCash: (amount: number) => boolean;
  setCurrentScreen: (screen: GameState['currentScreen']) => void;
  addNotification: (notification: Omit<GameNotification, 'id' | 'timestamp'>) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
  reset: () => void;
}

const initialPlayerApps: PlayerApps = { ...GAME_CONFIG.INITIAL_APPS };

export const useHackGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      // Initial state
      player: null,
      playerApps: initialPlayerApps,
      isOnline: false,
      notifications: [],
      availableTargets: [],
      hackHistory: [],
      currentScreen: 'home',
      isLoading: false,
      error: null,

      // Actions
      initializePlayer: (nick: string, email: string) => {
        const newPlayer: Player = {
          uid: `player_${Date.now()}`,
          nick,
          email,
          ip: generateRandomIP(),
          level: 1,
          xp: 0,
          xpToNextLevel: GAME_CONFIG.BASE_XP_PER_LEVEL,
          cash: GAME_CONFIG.INITIAL_CASH,
          createdAt: new Date().toISOString(),
          lastLogin: new Date().toISOString(),
        };

        set({
          player: newPlayer,
          playerApps: { ...GAME_CONFIG.INITIAL_APPS },
          isOnline: true,
          notifications: [{
            id: `notif_${Date.now()}`,
            type: 'system',
            title: 'Bem-vindo ao SHACK!',
            message: `Seu IP é ${newPlayer.ip}. Comece fazendo upgrades nos seus apps!`,
            timestamp: new Date().toISOString(),
            read: false,
          }],
        });
      },

      updatePlayerApps: (apps: Partial<PlayerApps>) => {
        set((state) => ({
          playerApps: { ...state.playerApps, ...apps }
        }));
      },

      upgradeApp: (appId: keyof PlayerApps) => {
        const state = get();
        const { player, playerApps } = state;
        
        if (!player) return false;

        const currentLevel = playerApps[appId];
        const upgradeCost = calculateUpgradeCost(currentLevel);
        
        // Verificar se tem dinheiro suficiente
        if (player.cash < upgradeCost) {
          set({ error: 'Dinheiro insuficiente para upgrade!' });
          return false;
        }

        // Verificar se não atingiu o nível máximo
        if (currentLevel >= GAME_CONFIG.MAX_APP_LEVEL) {
          set({ error: 'App já está no nível máximo!' });
          return false;
        }

        // Realizar upgrade
        const xpReward = calculateXpReward(currentLevel);
        const newApps = { ...playerApps, [appId]: currentLevel + 1 };
        
        set((state) => {
          const newPlayer = {
            ...state.player!,
            cash: state.player!.cash - upgradeCost,
          };
          
          return {
            player: newPlayer,
            playerApps: newApps,
            error: null,
          };
        });

        // Adicionar XP
        get().addXp(xpReward);

        // Adicionar notificação
        get().addNotification({
          type: 'upgrade',
          title: 'Upgrade Concluído!',
          message: `${appId} foi atualizado para nível ${currentLevel + 1}. +${xpReward} XP`,
          read: false,
        });

        return true;
      },

      addXp: (amount: number) => {
        set((state) => {
          if (!state.player) return state;

          const newTotalXp = state.player.xp + amount;
          const newLevel = getPlayerLevel(newTotalXp);
          const xpToNext = getXpForNextLevel(newTotalXp);
          
          const leveledUp = newLevel > state.player.level;
          
          const updatedPlayer = {
            ...state.player,
            xp: newTotalXp,
            level: newLevel,
            xpToNextLevel: xpToNext,
          };

          // Se subiu de nível, adicionar notificação
          if (leveledUp) {
            setTimeout(() => {
              get().addNotification({
                type: 'level',
                title: 'Level Up!',
                message: `Parabéns! Você atingiu o nível ${newLevel}!`,
                read: false,
              });
            }, 100);
          }

          return { player: updatedPlayer };
        });
      },

      addCash: (amount: number) => {
        set((state) => ({
          player: state.player ? {
            ...state.player,
            cash: state.player.cash + amount
          } : null
        }));
      },

      spendCash: (amount: number) => {
        const state = get();
        if (!state.player || state.player.cash < amount) {
          return false;
        }

        set((state) => ({
          player: state.player ? {
            ...state.player,
            cash: state.player.cash - amount
          } : null
        }));

        return true;
      },

      setCurrentScreen: (screen: GameState['currentScreen']) => {
        set({ currentScreen: screen });
      },

      addNotification: (notification: Omit<GameNotification, 'id' | 'timestamp'>) => {
        const newNotification: GameNotification = {
          ...notification,
          id: `notif_${Date.now()}_${Math.random()}`,
          timestamp: new Date().toISOString(),
        };

        set((state) => ({
          notifications: [newNotification, ...state.notifications].slice(0, 50) // Manter apenas 50 notificações
        }));
      },

      markNotificationRead: (id: string) => {
        set((state) => ({
          notifications: state.notifications.map(notif =>
            notif.id === id ? { ...notif, read: true } : notif
          )
        }));
      },

      clearNotifications: () => {
        set({ notifications: [] });
      },

      setError: (error: string | null) => {
        set({ error });
        if (error) {
          // Limpar erro após 5 segundos
          setTimeout(() => {
            set({ error: null });
          }, 5000);
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      reset: () => {
        set({
          player: null,
          playerApps: initialPlayerApps,
          isOnline: false,
          notifications: [],
          availableTargets: [],
          hackHistory: [],
          currentScreen: 'home',
          isLoading: false,
          error: null,
        });
      },
    }),
    {
      name: 'shack-game-storage',
      partialize: (state) => ({
        player: state.player,
        playerApps: state.playerApps,
        notifications: state.notifications,
        hackHistory: state.hackHistory,
      }),
    }
  )
);
