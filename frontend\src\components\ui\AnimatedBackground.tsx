import React from 'react';

interface AnimatedBackgroundProps {
  variant?: 'matrix' | 'particles' | 'grid' | 'waves';
  className?: string;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  variant = 'matrix',
  className = '',
}) => {
  const renderMatrix = () => (
    <div className="absolute inset-0 overflow-hidden opacity-10">
      <div className="matrix-bg">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="matrix-column"
            style={{
              left: `${i * 5}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${3 + Math.random() * 2}s`,
            }}
          >
            {Array.from({ length: 20 }).map((_, j) => (
              <span
                key={j}
                className="matrix-char"
                style={{
                  animationDelay: `${j * 0.1}s`,
                }}
              >
                {String.fromCharCode(0x30A0 + Math.random() * 96)}
              </span>
            ))}
          </div>
        ))}
      </div>
      <style dangerouslySetInnerHTML={{
        __html: `
        .matrix-bg {
          position: relative;
          width: 100%;
          height: 100%;
        }
        .matrix-column {
          position: absolute;
          top: -100%;
          width: 20px;
          height: 200%;
          animation: matrix-fall linear infinite;
          font-family: 'Courier New', monospace;
          font-size: 14px;
          color: #00ff41;
          display: flex;
          flex-direction: column;
        }
        .matrix-char {
          opacity: 0;
          animation: matrix-fade 0.5s ease-in-out infinite alternate;
        }
        @keyframes matrix-fall {
          to {
            transform: translateY(100vh);
          }
        }
        @keyframes matrix-fade {
          to {
            opacity: 1;
          }
        }
        `
      }} />
    </div>
  );

  const renderParticles = () => (
    <div className="absolute inset-0 overflow-hidden opacity-20">
      {Array.from({ length: 50 }).map((_, i) => (
        <div
          key={i}
          className="absolute w-1 h-1 bg-blue-400 rounded-full animate-pulse"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 3}s`,
            animationDuration: `${2 + Math.random() * 2}s`,
          }}
        />
      ))}
    </div>
  );

  const renderGrid = () => (
    <div className="absolute inset-0 opacity-5">
      <div
        className="w-full h-full"
        style={{
          backgroundImage: `
            linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),
            linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
        }}
      />
    </div>
  );

  const renderWaves = () => (
    <div className="absolute inset-0 overflow-hidden opacity-10">
      <svg
        className="absolute bottom-0 w-full h-32"
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
      >
        <path
          d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
          opacity=".25"
          className="fill-blue-500 animate-pulse"
        />
        <path
          d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
          opacity=".5"
          className="fill-blue-600 animate-pulse"
          style={{ animationDelay: '1s' }}
        />
        <path
          d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
          className="fill-blue-700 animate-pulse"
          style={{ animationDelay: '2s' }}
        />
      </svg>
    </div>
  );

  const backgrounds = {
    matrix: renderMatrix,
    particles: renderParticles,
    grid: renderGrid,
    waves: renderWaves,
  };

  return (
    <div className={`absolute inset-0 pointer-events-none ${className}`}>
      {backgrounds[variant]()}
    </div>
  );
};

export default AnimatedBackground;
