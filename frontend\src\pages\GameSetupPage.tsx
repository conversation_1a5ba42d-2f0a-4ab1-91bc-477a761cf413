import React, { useState } from 'react';
import { useHackGameStore } from '../stores/hackGameStore';

const GameSetupPage: React.FC = () => {
  const { initializePlayer, error, isLoading } = useHackGameStore();
  const [nick, setNick] = useState('');
  const [email, setEmail] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const handleCreatePlayer = async () => {
    if (!nick.trim() || !email.trim()) {
      alert('Por favor, preencha todos os campos');
      return;
    }

    if (nick.length < 3 || nick.length > 20) {
      alert('O nick deve ter entre 3 e 20 caracteres');
      return;
    }

    if (!email.includes('@')) {
      alert('Por favor, insira um email válido');
      return;
    }

    setIsCreating(true);

    try {
      const success = await initializePlayer(nick.trim(), email.trim());

      if (!success) {
        // O erro já foi definido no store
        setIsCreating(false);
      }
      // Se success for true, o jogador foi criado e a tela mudará automaticamente
    } catch (error) {
      alert('Erro ao criar jogador. Tente novamente.');
      setIsCreating(false);
    }
  };

  return (
    <div className="h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col">
      {/* Header */}
      <div className="p-6 text-center">
        <div className="text-6xl mb-4">🔒</div>
        <h1 className="text-2xl font-bold mb-2">SHACK</h1>
        <p className="text-sm text-gray-400">Simulador de Hacking</p>
      </div>

      {/* Formulário */}
      <div className="flex-1 px-6 py-4">
        <div className="bg-gray-800/50 rounded-2xl p-6 border border-gray-700/50">
          <h2 className="text-lg font-semibold mb-6 text-center">Criar Novo Hacker</h2>
          
          {/* Erro */}
          {error && (
            <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg">
              <p className="text-red-300 text-sm">{error}</p>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Nome do Hacker
              </label>
              <input
                type="text"
                value={nick}
                onChange={(e) => setNick(e.target.value)}
                placeholder="Digite seu nick"
                maxLength={20}
                disabled={isCreating || isLoading}
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50"
              />
              <p className="text-xs text-gray-400 mt-1">3-20 caracteres</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Email
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                disabled={isCreating || isLoading}
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50"
              />
            </div>
          </div>

          <button
            onClick={handleCreatePlayer}
            disabled={isCreating || isLoading || !nick.trim() || !email.trim()}
            className="w-full mt-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors"
          >
            {(isCreating || isLoading) ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Criando jogador...</span>
              </div>
            ) : (
              'Iniciar Jornada'
            )}
          </button>
        </div>

        {/* Informações do jogo */}
        <div className="mt-6 space-y-4">
          <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700/30">
            <h3 className="font-semibold mb-2 flex items-center">
              🎯 Como Jogar
            </h3>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Faça upgrades nos seus aplicativos</li>
              <li>• Ganhe XP e suba de nível</li>
              <li>• Escaneie a rede em busca de alvos</li>
              <li>• Invada sistemas e ganhe dinheiro</li>
            </ul>
          </div>

          <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700/30">
            <h3 className="font-semibold mb-2 flex items-center">
              🛠️ Aplicativos Iniciais
            </h3>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center space-x-2">
                <span>🛡️</span>
                <span>AntiVirus</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>🏦</span>
                <span>BankGuard</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>🔨</span>
                <span>BruteForce</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>⚙️</span>
                <span>SDK</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>🔥</span>
                <span>Firewall</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>🦠</span>
                <span>Malware Kit</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>🌐</span>
                <span>ProxyVPN</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="px-4 py-2 text-center">
        <p className="text-xs text-gray-500">
          SHACK v1.0 - Simulador de Hacking Educacional
        </p>
      </div>
    </div>
  );
};

export default GameSetupPage;
