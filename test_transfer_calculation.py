#!/usr/bin/env python3
"""
Teste específico para verificar os cálculos de transferência
"""

def test_transfer_calculation():
    """Simula o cálculo de transferência para verificar o problema"""
    
    print("=== TESTE DE CÁLCULO DE TRANSFERÊNCIA ===")
    
    # Cenário do problema reportado
    saldo_alvo = 1000
    porcentagem = 80
    
    print(f"Saldo do alvo: ${saldo_alvo}")
    print(f"Porcentagem: {porcentagem}%")
    print()
    
    # Cálculo base
    quantia_base = int(saldo_alvo * (porcentagem / 100))
    print(f"1. Quantia base (80% de ${saldo_alvo}): ${quantia_base}")
    
    # Simulando diferentes níveis de apps
    scenarios = [
        {"bankguard": 1, "bruteforce": 1, "desc": "Sem modificadores"},
        {"bankguard": 1, "bruteforce": 2, "desc": "BruteForce nível 2"},
        {"bankguard": 1, "bruteforce": 3, "desc": "BruteForce nível 3"},
        {"bankguard": 2, "bruteforce": 1, "desc": "BankGuard nível 2"},
        {"bankguard": 2, "bruteforce": 2, "desc": "Ambos nível 2"},
    ]
    
    print("\n=== CENÁRIOS DE TESTE ===")
    
    for i, scenario in enumerate(scenarios, 1):
        bankguard_nivel = scenario["bankguard"]
        bruteforce_nivel = scenario["bruteforce"]
        
        print(f"\n{i}. {scenario['desc']}:")
        print(f"   BankGuard: nível {bankguard_nivel}")
        print(f"   BruteForce: nível {bruteforce_nivel}")
        
        # Cálculo ANTIGO (problemático)
        print("\n   CÁLCULO ANTIGO:")
        reducao_bankguard_old = 1 - (bankguard_nivel - 1) * 0.03
        quantia_bankguard_old = int(quantia_base * reducao_bankguard_old)
        
        bonus_bruteforce_old = 1 + (bruteforce_nivel - 1) * 0.05
        quantia_final_old = int(quantia_bankguard_old * bonus_bruteforce_old)
        
        print(f"   - Após BankGuard: ${quantia_bankguard_old} (fator: {reducao_bankguard_old:.3f})")
        print(f"   - Após BruteForce: ${quantia_final_old} (fator: {bonus_bruteforce_old:.3f})")
        print(f"   - RESULTADO ANTIGO: ${min(quantia_final_old, saldo_alvo)}")
        
        # Cálculo NOVO (corrigido)
        print("\n   CÁLCULO NOVO:")
        reducao_bankguard_new = max(0.5, 1 - (bankguard_nivel - 1) * 0.03)
        quantia_bankguard_new = int(quantia_base * reducao_bankguard_new)
        
        bonus_bruteforce_new = min(1.5, 1 + (bruteforce_nivel - 1) * 0.05)
        quantia_bruteforce_new = int(quantia_bankguard_new * bonus_bruteforce_new)
        
        limite_porcentagem = int(saldo_alvo * (porcentagem / 100))
        quantia_final_new = min(quantia_bruteforce_new, limite_porcentagem, saldo_alvo)
        
        print(f"   - Após BankGuard: ${quantia_bankguard_new} (fator: {reducao_bankguard_new:.3f})")
        print(f"   - Após BruteForce: ${quantia_bruteforce_new} (fator: {bonus_bruteforce_new:.3f})")
        print(f"   - Limite porcentagem: ${limite_porcentagem}")
        print(f"   - RESULTADO NOVO: ${quantia_final_new}")
        
        # Comparação
        diferenca = quantia_final_new - min(quantia_final_old, saldo_alvo)
        if diferenca != 0:
            print(f"   - DIFERENÇA: {diferenca:+d}")
        
        # Verificar se excede a porcentagem original
        porcentagem_real_old = (min(quantia_final_old, saldo_alvo) / saldo_alvo) * 100
        porcentagem_real_new = (quantia_final_new / saldo_alvo) * 100
        
        print(f"   - % real antiga: {porcentagem_real_old:.1f}%")
        print(f"   - % real nova: {porcentagem_real_new:.1f}%")
        
        if porcentagem_real_old > porcentagem:
            print(f"   ⚠️  PROBLEMA: Excedeu {porcentagem}%!")
        if porcentagem_real_new <= porcentagem:
            print(f"   ✅ CORRIGIDO: Dentro do limite de {porcentagem}%")

def main():
    test_transfer_calculation()
    
    print("\n" + "="*60)
    print("CONCLUSÃO:")
    print("- O problema era que BruteForce podia aumentar além da % original")
    print("- A correção limita o resultado ao máximo da % especificada")
    print("- Agora 80% nunca resultará em mais de 80% do saldo")

if __name__ == "__main__":
    main()
