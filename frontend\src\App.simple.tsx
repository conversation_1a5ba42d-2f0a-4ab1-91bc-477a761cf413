import React from 'react';
import './styles/globals.css';

function App() {
  return (
    <div className="min-h-screen bg-bg-primary text-text-primary p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8">
          🎮 SHACK Web Game
        </h1>
        
        <div className="card text-center">
          <h2 className="text-2xl font-semibold mb-4">
            React App Funcionando!
          </h2>
          <p className="text-text-secondary mb-6">
            Se você está vendo esta mensagem, o React está funcionando corretamente.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="card bg-green-900 border-green-500">
              <h3 className="text-lg font-semibold text-green-100 mb-2">
                ✅ React Carregado
              </h3>
              <p className="text-green-200 text-sm">
                Aplicação React inicializada com sucesso
              </p>
            </div>
            
            <div className="card bg-blue-900 border-blue-500">
              <h3 className="text-lg font-semibold text-blue-100 mb-2">
                🎨 Tailwind CSS
              </h3>
              <p className="text-blue-200 text-sm">
                Estilos carregados e funcionando
              </p>
            </div>
          </div>
          
          <div className="mt-6">
            <button 
              onClick={() => alert('React funcionando!')}
              className="btn-primary"
            >
              Testar Interação
            </button>
          </div>
        </div>
        
        <div className="mt-8 text-center text-text-muted">
          <p>Próximo passo: Ativar autenticação e componentes completos</p>
        </div>
      </div>
    </div>
  );
}

export default App;
