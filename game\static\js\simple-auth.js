/**
 * Sistema de autenticação simples SEM Firebase
 * Para uso com backend Supabase
 */

class SimpleAuth {
    constructor() {
        this.currentUser = null;
        this.token = null;
        this.callbacks = [];
        
        // Recupera sessão salva
        this.loadSession();
    }
    
    // Salva sessão no localStorage
    saveSession(user, token) {
        localStorage.setItem('shack_user', JSON.stringify(user));
        localStorage.setItem('shack_token', token);
        this.currentUser = user;
        this.token = token;
        this.notifyCallbacks(user);
    }
    
    // Carrega sessão do localStorage
    loadSession() {
        try {
            const savedUser = localStorage.getItem('shack_user');
            const savedToken = localStorage.getItem('shack_token');
            
            if (savedUser && savedToken) {
                this.currentUser = JSON.parse(savedUser);
                this.token = savedToken;
                this.notifyCallbacks(this.currentUser);
            }
        } catch (e) {
            // console.log removido
            this.logout();
        }
    }
    
    // Remove sessão
    clearSession() {
        localStorage.removeItem('shack_user');
        localStorage.removeItem('shack_token');
        this.currentUser = null;
        this.token = null;
        this.notifyCallbacks(null);
    }
    
    // Adiciona callback para mudanças de estado
    onAuthStateChanged(callback) {
        this.callbacks.push(callback);
        // Chama imediatamente com estado atual
        callback(this.currentUser);
    }
    
    // Notifica todos os callbacks
    notifyCallbacks(user) {
        this.callbacks.forEach(callback => {
            try {
                callback(user);
            } catch (e) {
                // console.error removido
            }
        });
    }
    
    // Retorna token atual
    async getIdToken() {
        return this.token;
    }
    
    // Retorna sessão atual
    getSession() {
        return {
            user: this.currentUser,
            token: this.token
        };
    }
    
    // Login simples
    async login(email, password) {
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, password })
            });
            
            const result = await response.json();
            
            if (result.sucesso) {
                this.saveSession(result.user, result.token);
                this.notifyAuthStateChange(); // Notifica mudança de estado
                return { user: result.user };
            } else {
                throw new Error(result.mensagem || 'Erro no login');
            }
        } catch (error) {
            // console.error removido
            throw error;
        }
    }
    
    // Registro simples
    async register(email, password, nick) {
        try {
            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, password, nick })
            });
            
            const result = await response.json();
            
            if (result.sucesso) {
                this.saveSession(result.user, result.token);
                this.notifyAuthStateChange(); // Notifica mudança de estado
                return { user: result.user };
            } else {
                throw new Error(result.mensagem || 'Erro no registro');
            }
        } catch (error) {
            // console.error removido
            throw error;
        }
    }
    
    // Logout simples
    logout() {
        try {
            // Limpa sessão local
            localStorage.removeItem('shack_user');
            localStorage.removeItem('shack_token');
            this.currentUser = null;
            this.token = null;
            
            // Chama endpoint de logout se disponível
            fetch('/api/auth/logout', { method: 'POST' }).catch(() => {
                // Ignora erros do logout no servidor
            });
            
            // Notifica callbacks e mudança de estado
            this.notifyCallbacks(null);
            this.notifyAuthStateChange();
            
        } catch (error) {
            // console.error removido
        }
    }
    
    // Método para notificar mudanças no estado de autenticação
    notifyAuthStateChange() {
        // Dispara evento personalizado que o main.js pode escutar
        if (typeof window !== 'undefined' && window.checkAuthState) {
            setTimeout(() => window.checkAuthState(), 100);
        }
    }
}

// Instância global
const auth = new SimpleAuth();

// Funções auxiliares para compatibilidade
async function loginUser(email, password) {
    return await auth.login(email, password);
}

async function registerUser(email, password, nick) {
    return await auth.register(email, password, nick);
}

// Torna disponível globalmente
window.auth = auth;
window.loginUser = loginUser;
window.registerUser = registerUser;
