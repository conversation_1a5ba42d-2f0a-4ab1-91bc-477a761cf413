import React from 'react';

interface ModernCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'gradient' | 'neon';
  hover?: boolean;
  glow?: boolean;
  onClick?: () => void;
}

const ModernCard: React.FC<ModernCardProps> = ({
  children,
  className = '',
  variant = 'default',
  hover = true,
  glow = false,
  onClick,
}) => {
  const baseClasses = 'rounded-xl transition-all duration-300 ease-in-out';
  
  const variantClasses = {
    default: 'bg-gray-800/80 backdrop-blur-sm border border-gray-700/50',
    glass: 'bg-white/5 backdrop-blur-md border border-white/10',
    gradient: 'bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm border border-blue-500/20',
    neon: 'bg-gray-900/90 backdrop-blur-sm border border-cyan-500/50 shadow-lg shadow-cyan-500/20',
  };

  const hoverClasses = hover ? {
    default: 'hover:bg-gray-800/90 hover:border-gray-600/70 hover:shadow-lg hover:shadow-gray-900/20 hover:-translate-y-1',
    glass: 'hover:bg-white/10 hover:border-white/20 hover:shadow-xl hover:shadow-white/5 hover:-translate-y-1',
    gradient: 'hover:from-blue-900/30 hover:to-purple-900/30 hover:border-blue-400/40 hover:shadow-xl hover:shadow-blue-500/10 hover:-translate-y-1',
    neon: 'hover:border-cyan-400/70 hover:shadow-xl hover:shadow-cyan-500/30 hover:-translate-y-1',
  } : {};

  const glowClasses = glow ? {
    default: 'shadow-lg shadow-gray-500/10',
    glass: 'shadow-xl shadow-white/5',
    gradient: 'shadow-xl shadow-blue-500/20',
    neon: 'shadow-xl shadow-cyan-500/30',
  } : {};

  const classes = [
    baseClasses,
    variantClasses[variant],
    hover ? hoverClasses[variant] : '',
    glow ? glowClasses[variant] : '',
    onClick ? 'cursor-pointer' : '',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className={classes} onClick={onClick}>
      {children}
    </div>
  );
};

export default ModernCard;
