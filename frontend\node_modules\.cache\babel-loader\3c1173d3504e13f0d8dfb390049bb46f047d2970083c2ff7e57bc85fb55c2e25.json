{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\InvadedPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useGameSystem } from '../hooks/useGameSystem';\nimport GameFooter from '../components/common/GameFooter';\nimport { TerminalIcon, BankIcon } from '../components/ui/GameIcons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InvadedPage = () => {\n  _s();\n  var _location$state, _target$dinheiro, _target$dinheiro2;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const target = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.target;\n  const {\n    activeConnections,\n    activeBruteforces,\n    startBruteforce,\n    performBankTransfer,\n    isTransferring,\n    transferError\n  } = useGameSystem();\n  const [selectedPercentage, setSelectedPercentage] = useState(null);\n  const [showTransferModal, setShowTransferModal] = useState(false);\n  useEffect(() => {\n    if (!target) {\n      navigate('/game/scanner');\n    }\n  }, [target, navigate]);\n  if (!target) {\n    return null;\n  }\n  const connection = activeConnections.find(conn => conn.targetIp === target.ip);\n  const bruteforceStatus = activeBruteforces.get(target.ip);\n  const canAccessBank = (connection === null || connection === void 0 ? void 0 : connection.bankAccess) || false;\n  const handleStartBruteforce = async () => {\n    if (!connection) return;\n    try {\n      await startBruteforce(connection.id, target.ip);\n    } catch (error) {\n      console.error('Erro ao iniciar bruteforce:', error);\n    }\n  };\n  const handleBankAccess = () => {\n    if (canAccessBank) {\n      setShowTransferModal(true);\n    } else {\n      alert('Execute um ataque BruteForce primeiro para quebrar a segurança bancária.');\n    }\n  };\n  const handleTransfer = async () => {\n    if (!selectedPercentage) {\n      alert('Selecione uma porcentagem primeiro');\n      return;\n    }\n    try {\n      await performBankTransfer(target.ip, selectedPercentage);\n      setShowTransferModal(false);\n      setSelectedPercentage(null);\n      alert('Transferência realizada com sucesso!');\n    } catch (error) {\n      console.error('Erro na transferência:', error);\n    }\n  };\n  const calculateTransferAmount = percentage => {\n    return Math.floor(target.dinheiro * percentage / 100);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-gray-700/50 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/game/scanner'),\n            className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\u2190\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent\",\n              children: \"Sistema Invadido\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-400\",\n              children: \"Acesso Remoto Ativo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-1 text-green-400 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Conectado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"Informa\\xE7\\xF5es do Sistema\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"Usu\\xE1rio:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-semibold\",\n              children: target.nick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"IP:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-cyan-400 font-mono\",\n              children: target.ip\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"N\\xEDvel:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-400 font-semibold\",\n              children: target.nivel\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"Saldo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-green-400 font-semibold\",\n              children: [\"$\", ((_target$dinheiro = target.dinheiro) === null || _target$dinheiro === void 0 ? void 0 : _target$dinheiro.toLocaleString()) || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-3 gap-2 text-xs\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-700/50 rounded p-2 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400\",\n              children: \"Firewall\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-red-400 font-semibold\",\n              children: target.firewall || 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-700/50 rounded p-2 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400\",\n              children: \"CPU\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-400 font-semibold\",\n              children: target.cpu || 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-700/50 rounded p-2 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400\",\n              children: \"RAM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-purple-400 font-semibold\",\n              children: target.ram || 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), bruteforceStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-br from-yellow-900/50 to-orange-900/50 backdrop-blur-sm rounded-xl p-6 border border-yellow-700/50 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-yellow-400\",\n          children: \"Ataque BruteForce em Andamento\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"Progresso:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-yellow-400\",\n              children: [bruteforceStatus.progress.toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-700 rounded-full h-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-yellow-500 to-orange-500 h-3 rounded-full transition-all duration-1000\",\n              style: {\n                width: `${bruteforceStatus.progress}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"Tempo restante:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-yellow-400\",\n              children: [bruteforceStatus.timeRemaining, \"s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleStartBruteforce,\n          disabled: !!bruteforceStatus || canAccessBank,\n          className: `w-full p-4 rounded-xl border transition-all ${bruteforceStatus ? 'bg-yellow-900/20 border-yellow-700/50 text-yellow-400' : canAccessBank ? 'bg-green-900/20 border-green-700/50 text-green-400' : 'bg-gray-800/50 border-gray-600/50 hover:border-cyan-500/50 text-white hover:bg-gray-700/50'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(TerminalIcon, {\n              size: 24,\n              className: bruteforceStatus ? 'text-yellow-400' : canAccessBank ? 'text-green-400' : 'text-cyan-400'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-semibold\",\n                children: bruteforceStatus ? 'BruteForce em Andamento' : canAccessBank ? 'BruteForce Concluído' : 'Iniciar BruteForce'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-400\",\n                children: bruteforceStatus ? 'Quebrando segurança bancária...' : canAccessBank ? 'Segurança bancária quebrada' : 'Quebrar segurança do sistema bancário'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBankAccess,\n          disabled: !canAccessBank,\n          className: `w-full p-4 rounded-xl border transition-all ${canAccessBank ? 'bg-green-800/50 border-green-600/50 hover:border-green-500/50 text-white hover:bg-green-700/50' : 'bg-gray-800/20 border-gray-700/50 text-gray-500 cursor-not-allowed'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(BankIcon, {\n              size: 24,\n              className: canAccessBank ? 'text-green-400' : 'text-gray-500'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-semibold\",\n                children: canAccessBank ? 'Acessar Banco' : 'Banco Bloqueado'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-400\",\n                children: canAccessBank ? 'Realizar transferências bancárias' : 'Execute BruteForce primeiro'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), showTransferModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-xl p-6 w-full max-w-md border border-gray-700\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"Transfer\\xEAncia Banc\\xE1ria\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400 mb-2\",\n            children: [\"Saldo dispon\\xEDvel: $\", (_target$dinheiro2 = target.dinheiro) === null || _target$dinheiro2 === void 0 ? void 0 : _target$dinheiro2.toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-4 gap-2\",\n            children: [20, 40, 60, 80].map(percentage => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedPercentage(percentage),\n              className: `py-2 px-3 rounded-lg text-sm font-semibold transition-all ${selectedPercentage === percentage ? 'bg-green-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,\n              children: [percentage, \"%\"]\n            }, percentage, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this), selectedPercentage && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 p-3 bg-gray-700/50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400\",\n              children: \"Valor a transferir:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold text-green-400\",\n              children: [\"$\", calculateTransferAmount(selectedPercentage).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this), transferError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-300 text-sm\",\n            children: transferError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowTransferModal(false);\n              setSelectedPercentage(null);\n            },\n            className: \"flex-1 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg\",\n            children: \"Cancelar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleTransfer,\n            disabled: !selectedPercentage || isTransferring,\n            className: \"flex-1 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg\",\n            children: isTransferring ? 'Transferindo...' : 'Confirmar'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(GameFooter, {\n      currentPage: \"scanner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(InvadedPage, \"XFcZme6M9D18yBiKQSXiVQTmKJ0=\", false, function () {\n  return [useNavigate, useLocation, useGameSystem];\n});\n_c = InvadedPage;\nexport default InvadedPage;\nvar _c;\n$RefreshReg$(_c, \"InvadedPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "useGameSystem", "GameFooter", "TerminalIcon", "BankIcon", "jsxDEV", "_jsxDEV", "InvadedPage", "_s", "_location$state", "_target$dinheiro", "_target$dinheiro2", "navigate", "location", "target", "state", "activeConnections", "activeBruteforces", "startBruteforce", "performBankTransfer", "isTransferring", "transferError", "selectedPercentage", "setSelectedPercentage", "showTransferModal", "setShowTransferModal", "connection", "find", "conn", "targetIp", "ip", "bruteforceStatus", "get", "canAccessBank", "bankAccess", "handleStartBruteforce", "id", "error", "console", "handleBankAccess", "alert", "handleTransfer", "calculateTransferAmount", "percentage", "Math", "floor", "<PERSON><PERSON><PERSON>", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "nivel", "toLocaleString", "firewall", "cpu", "ram", "progress", "toFixed", "style", "width", "timeRemaining", "disabled", "size", "map", "currentPage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/InvadedPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useGameSystem } from '../hooks/useGameSystem';\nimport GameFooter from '../components/common/GameFooter';\nimport { TerminalIcon, BankIcon, TransferIcon, SecurityIcon } from '../components/ui/GameIcons';\n\nconst InvadedPage: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const target = location.state?.target;\n  \n  const {\n    activeConnections,\n    activeBruteforces,\n    startBruteforce,\n    performBankTransfer,\n    isTransferring,\n    transferError\n  } = useGameSystem();\n\n  const [selectedPercentage, setSelectedPercentage] = useState<number | null>(null);\n  const [showTransferModal, setShowTransferModal] = useState(false);\n\n  useEffect(() => {\n    if (!target) {\n      navigate('/game/scanner');\n    }\n  }, [target, navigate]);\n\n  if (!target) {\n    return null;\n  }\n\n  const connection = activeConnections.find(conn => conn.targetIp === target.ip);\n  const bruteforceStatus = activeBruteforces.get(target.ip);\n  const canAccessBank = connection?.bankAccess || false;\n\n  const handleStartBruteforce = async () => {\n    if (!connection) return;\n    \n    try {\n      await startBruteforce(connection.id, target.ip);\n    } catch (error) {\n      console.error('Erro ao iniciar bruteforce:', error);\n    }\n  };\n\n  const handleBankAccess = () => {\n    if (canAccessBank) {\n      setShowTransferModal(true);\n    } else {\n      alert('Execute um ataque BruteForce primeiro para quebrar a segurança bancária.');\n    }\n  };\n\n  const handleTransfer = async () => {\n    if (!selectedPercentage) {\n      alert('Selecione uma porcentagem primeiro');\n      return;\n    }\n\n    try {\n      await performBankTransfer(target.ip, selectedPercentage);\n      setShowTransferModal(false);\n      setSelectedPercentage(null);\n      alert('Transferência realizada com sucesso!');\n    } catch (error) {\n      console.error('Erro na transferência:', error);\n    }\n  };\n\n  const calculateTransferAmount = (percentage: number) => {\n    return Math.floor((target.dinheiro * percentage) / 100);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-gray-700/50 p-4 flex-shrink-0\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={() => navigate('/game/scanner')}\n              className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors\"\n            >\n              <span className=\"text-lg\">←</span>\n            </button>\n            <div>\n              <h1 className=\"text-xl font-bold bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent\">\n                Sistema Invadido\n              </h1>\n              <p className=\"text-xs text-gray-400\">Acesso Remoto Ativo</p>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"flex items-center space-x-1 text-green-400 text-xs\">\n              <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n              <span>Conectado</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Informações do Alvo */}\n      <div className=\"p-4\">\n        <div className=\"bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 mb-6\">\n          <h2 className=\"text-lg font-semibold mb-4 text-white\">Informações do Sistema</h2>\n          \n          <div className=\"grid grid-cols-2 gap-4 mb-4\">\n            <div>\n              <span className=\"text-gray-400 text-sm\">Usuário:</span>\n              <div className=\"text-white font-semibold\">{target.nick}</div>\n            </div>\n            <div>\n              <span className=\"text-gray-400 text-sm\">IP:</span>\n              <div className=\"text-cyan-400 font-mono\">{target.ip}</div>\n            </div>\n            <div>\n              <span className=\"text-gray-400 text-sm\">Nível:</span>\n              <div className=\"text-blue-400 font-semibold\">{target.nivel}</div>\n            </div>\n            <div>\n              <span className=\"text-gray-400 text-sm\">Saldo:</span>\n              <div className=\"text-green-400 font-semibold\">${target.dinheiro?.toLocaleString() || '0'}</div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-3 gap-2 text-xs\">\n            <div className=\"bg-gray-700/50 rounded p-2 text-center\">\n              <div className=\"text-gray-400\">Firewall</div>\n              <div className=\"text-red-400 font-semibold\">{target.firewall || 1}</div>\n            </div>\n            <div className=\"bg-gray-700/50 rounded p-2 text-center\">\n              <div className=\"text-gray-400\">CPU</div>\n              <div className=\"text-blue-400 font-semibold\">{target.cpu || 1}</div>\n            </div>\n            <div className=\"bg-gray-700/50 rounded p-2 text-center\">\n              <div className=\"text-gray-400\">RAM</div>\n              <div className=\"text-purple-400 font-semibold\">{target.ram || 1}</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Status do Bruteforce */}\n        {bruteforceStatus && (\n          <div className=\"bg-gradient-to-br from-yellow-900/50 to-orange-900/50 backdrop-blur-sm rounded-xl p-6 border border-yellow-700/50 mb-6\">\n            <h3 className=\"text-lg font-semibold mb-4 text-yellow-400\">Ataque BruteForce em Andamento</h3>\n            \n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between text-sm\">\n                <span className=\"text-gray-400\">Progresso:</span>\n                <span className=\"text-yellow-400\">{bruteforceStatus.progress.toFixed(1)}%</span>\n              </div>\n              \n              <div className=\"w-full bg-gray-700 rounded-full h-3\">\n                <div \n                  className=\"bg-gradient-to-r from-yellow-500 to-orange-500 h-3 rounded-full transition-all duration-1000\"\n                  style={{ width: `${bruteforceStatus.progress}%` }}\n                ></div>\n              </div>\n              \n              <div className=\"flex justify-between text-sm\">\n                <span className=\"text-gray-400\">Tempo restante:</span>\n                <span className=\"text-yellow-400\">{bruteforceStatus.timeRemaining}s</span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Ações Disponíveis */}\n        <div className=\"space-y-4\">\n          {/* Terminal/Bruteforce */}\n          <button\n            onClick={handleStartBruteforce}\n            disabled={!!bruteforceStatus || canAccessBank}\n            className={`w-full p-4 rounded-xl border transition-all ${\n              bruteforceStatus\n                ? 'bg-yellow-900/20 border-yellow-700/50 text-yellow-400'\n                : canAccessBank\n                ? 'bg-green-900/20 border-green-700/50 text-green-400'\n                : 'bg-gray-800/50 border-gray-600/50 hover:border-cyan-500/50 text-white hover:bg-gray-700/50'\n            }`}\n          >\n            <div className=\"flex items-center space-x-3\">\n              <TerminalIcon size={24} className={bruteforceStatus ? 'text-yellow-400' : canAccessBank ? 'text-green-400' : 'text-cyan-400'} />\n              <div className=\"text-left\">\n                <div className=\"font-semibold\">\n                  {bruteforceStatus ? 'BruteForce em Andamento' : canAccessBank ? 'BruteForce Concluído' : 'Iniciar BruteForce'}\n                </div>\n                <div className=\"text-sm text-gray-400\">\n                  {bruteforceStatus ? 'Quebrando segurança bancária...' : canAccessBank ? 'Segurança bancária quebrada' : 'Quebrar segurança do sistema bancário'}\n                </div>\n              </div>\n            </div>\n          </button>\n\n          {/* Banco */}\n          <button\n            onClick={handleBankAccess}\n            disabled={!canAccessBank}\n            className={`w-full p-4 rounded-xl border transition-all ${\n              canAccessBank\n                ? 'bg-green-800/50 border-green-600/50 hover:border-green-500/50 text-white hover:bg-green-700/50'\n                : 'bg-gray-800/20 border-gray-700/50 text-gray-500 cursor-not-allowed'\n            }`}\n          >\n            <div className=\"flex items-center space-x-3\">\n              <BankIcon size={24} className={canAccessBank ? 'text-green-400' : 'text-gray-500'} />\n              <div className=\"text-left\">\n                <div className=\"font-semibold\">\n                  {canAccessBank ? 'Acessar Banco' : 'Banco Bloqueado'}\n                </div>\n                <div className=\"text-sm text-gray-400\">\n                  {canAccessBank ? 'Realizar transferências bancárias' : 'Execute BruteForce primeiro'}\n                </div>\n              </div>\n            </div>\n          </button>\n        </div>\n      </div>\n\n      {/* Modal de Transferência */}\n      {showTransferModal && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-gray-800 rounded-xl p-6 w-full max-w-md border border-gray-700\">\n            <h3 className=\"text-lg font-semibold mb-4 text-white\">Transferência Bancária</h3>\n            \n            <div className=\"mb-4\">\n              <p className=\"text-sm text-gray-400 mb-2\">Saldo disponível: ${target.dinheiro?.toLocaleString()}</p>\n              \n              <div className=\"grid grid-cols-4 gap-2\">\n                {[20, 40, 60, 80].map(percentage => (\n                  <button\n                    key={percentage}\n                    onClick={() => setSelectedPercentage(percentage)}\n                    className={`py-2 px-3 rounded-lg text-sm font-semibold transition-all ${\n                      selectedPercentage === percentage\n                        ? 'bg-green-600 text-white'\n                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n                    }`}\n                  >\n                    {percentage}%\n                  </button>\n                ))}\n              </div>\n              \n              {selectedPercentage && (\n                <div className=\"mt-3 p-3 bg-gray-700/50 rounded-lg\">\n                  <p className=\"text-sm text-gray-400\">Valor a transferir:</p>\n                  <p className=\"text-lg font-semibold text-green-400\">\n                    ${calculateTransferAmount(selectedPercentage).toLocaleString()}\n                  </p>\n                </div>\n              )}\n            </div>\n\n            {transferError && (\n              <div className=\"mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg\">\n                <p className=\"text-red-300 text-sm\">{transferError}</p>\n              </div>\n            )}\n\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={() => {\n                  setShowTransferModal(false);\n                  setSelectedPercentage(null);\n                }}\n                className=\"flex-1 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg\"\n              >\n                Cancelar\n              </button>\n              <button\n                onClick={handleTransfer}\n                disabled={!selectedPercentage || isTransferring}\n                className=\"flex-1 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg\"\n              >\n                {isTransferring ? 'Transferindo...' : 'Confirmar'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Footer */}\n      <GameFooter currentPage=\"scanner\" />\n    </div>\n  );\n};\n\nexport default InvadedPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAOC,UAAU,MAAM,iCAAiC;AACxD,SAASC,YAAY,EAAEC,QAAQ,QAAoC,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhG,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,iBAAA;EAClC,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,MAAM,IAAAL,eAAA,GAAGI,QAAQ,CAACE,KAAK,cAAAN,eAAA,uBAAdA,eAAA,CAAgBK,MAAM;EAErC,MAAM;IACJE,iBAAiB;IACjBC,iBAAiB;IACjBC,eAAe;IACfC,mBAAmB;IACnBC,cAAc;IACdC;EACF,CAAC,GAAGpB,aAAa,CAAC,CAAC;EAEnB,MAAM,CAACqB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EACjF,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAEjEC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgB,MAAM,EAAE;MACXF,QAAQ,CAAC,eAAe,CAAC;IAC3B;EACF,CAAC,EAAE,CAACE,MAAM,EAAEF,QAAQ,CAAC,CAAC;EAEtB,IAAI,CAACE,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAEA,MAAMY,UAAU,GAAGV,iBAAiB,CAACW,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKf,MAAM,CAACgB,EAAE,CAAC;EAC9E,MAAMC,gBAAgB,GAAGd,iBAAiB,CAACe,GAAG,CAAClB,MAAM,CAACgB,EAAE,CAAC;EACzD,MAAMG,aAAa,GAAG,CAAAP,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEQ,UAAU,KAAI,KAAK;EAErD,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACT,UAAU,EAAE;IAEjB,IAAI;MACF,MAAMR,eAAe,CAACQ,UAAU,CAACU,EAAE,EAAEtB,MAAM,CAACgB,EAAE,CAAC;IACjD,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIN,aAAa,EAAE;MACjBR,oBAAoB,CAAC,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLe,KAAK,CAAC,0EAA0E,CAAC;IACnF;EACF,CAAC;EAED,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACnB,kBAAkB,EAAE;MACvBkB,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA,IAAI;MACF,MAAMrB,mBAAmB,CAACL,MAAM,CAACgB,EAAE,EAAER,kBAAkB,CAAC;MACxDG,oBAAoB,CAAC,KAAK,CAAC;MAC3BF,qBAAqB,CAAC,IAAI,CAAC;MAC3BiB,KAAK,CAAC,sCAAsC,CAAC;IAC/C,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMK,uBAAuB,GAAIC,UAAkB,IAAK;IACtD,OAAOC,IAAI,CAACC,KAAK,CAAE/B,MAAM,CAACgC,QAAQ,GAAGH,UAAU,GAAI,GAAG,CAAC;EACzD,CAAC;EAED,oBACErC,OAAA;IAAKyC,SAAS,EAAC,gGAAgG;IAAAC,QAAA,gBAE7G1C,OAAA;MAAKyC,SAAS,EAAC,iHAAiH;MAAAC,QAAA,eAC9H1C,OAAA;QAAKyC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD1C,OAAA;UAAKyC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C1C,OAAA;YACE2C,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,eAAe,CAAE;YACzCmC,SAAS,EAAC,qGAAqG;YAAAC,QAAA,eAE/G1C,OAAA;cAAMyC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACT/C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAIyC,SAAS,EAAC,6FAA6F;cAAAC,QAAA,EAAC;YAE5G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/C,OAAA;cAAGyC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/C,OAAA;UAAKyC,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzB1C,OAAA;YAAKyC,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjE1C,OAAA;cAAKyC,SAAS,EAAC;YAAiD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvE/C,OAAA;cAAA0C,QAAA,EAAM;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA;MAAKyC,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClB1C,OAAA;QAAKyC,SAAS,EAAC,kHAAkH;QAAAC,QAAA,gBAC/H1C,OAAA;UAAIyC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEjF/C,OAAA;UAAKyC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C1C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAMyC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvD/C,OAAA;cAAKyC,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAElC,MAAM,CAACwC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACN/C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAMyC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD/C,OAAA;cAAKyC,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAElC,MAAM,CAACgB;YAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACN/C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAMyC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrD/C,OAAA;cAAKyC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAElC,MAAM,CAACyC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACN/C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAMyC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrD/C,OAAA;cAAKyC,SAAS,EAAC,8BAA8B;cAAAC,QAAA,GAAC,GAAC,EAAC,EAAAtC,gBAAA,GAAAI,MAAM,CAACgC,QAAQ,cAAApC,gBAAA,uBAAfA,gBAAA,CAAiB8C,cAAc,CAAC,CAAC,KAAI,GAAG;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UAAKyC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C1C,OAAA;YAAKyC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD1C,OAAA;cAAKyC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7C/C,OAAA;cAAKyC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAElC,MAAM,CAAC2C,QAAQ,IAAI;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACN/C,OAAA;YAAKyC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD1C,OAAA;cAAKyC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxC/C,OAAA;cAAKyC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAElC,MAAM,CAAC4C,GAAG,IAAI;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACN/C,OAAA;YAAKyC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD1C,OAAA;cAAKyC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxC/C,OAAA;cAAKyC,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAElC,MAAM,CAAC6C,GAAG,IAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLtB,gBAAgB,iBACfzB,OAAA;QAAKyC,SAAS,EAAC,wHAAwH;QAAAC,QAAA,gBACrI1C,OAAA;UAAIyC,SAAS,EAAC,4CAA4C;UAAAC,QAAA,EAAC;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE9F/C,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1C,OAAA;YAAKyC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3C1C,OAAA;cAAMyC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjD/C,OAAA;cAAMyC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAEjB,gBAAgB,CAAC6B,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eAEN/C,OAAA;YAAKyC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClD1C,OAAA;cACEyC,SAAS,EAAC,8FAA8F;cACxGe,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAGhC,gBAAgB,CAAC6B,QAAQ;cAAI;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN/C,OAAA;YAAKyC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3C1C,OAAA;cAAMyC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtD/C,OAAA;cAAMyC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAEjB,gBAAgB,CAACiC,aAAa,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD/C,OAAA;QAAKyC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAExB1C,OAAA;UACE2C,OAAO,EAAEd,qBAAsB;UAC/B8B,QAAQ,EAAE,CAAC,CAAClC,gBAAgB,IAAIE,aAAc;UAC9Cc,SAAS,EAAE,+CACThB,gBAAgB,GACZ,uDAAuD,GACvDE,aAAa,GACb,oDAAoD,GACpD,4FAA4F,EAC/F;UAAAe,QAAA,eAEH1C,OAAA;YAAKyC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C1C,OAAA,CAACH,YAAY;cAAC+D,IAAI,EAAE,EAAG;cAACnB,SAAS,EAAEhB,gBAAgB,GAAG,iBAAiB,GAAGE,aAAa,GAAG,gBAAgB,GAAG;YAAgB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChI/C,OAAA;cAAKyC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1C,OAAA;gBAAKyC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3BjB,gBAAgB,GAAG,yBAAyB,GAAGE,aAAa,GAAG,sBAAsB,GAAG;cAAoB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC,eACN/C,OAAA;gBAAKyC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCjB,gBAAgB,GAAG,iCAAiC,GAAGE,aAAa,GAAG,6BAA6B,GAAG;cAAuC;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5I,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGT/C,OAAA;UACE2C,OAAO,EAAEV,gBAAiB;UAC1B0B,QAAQ,EAAE,CAAChC,aAAc;UACzBc,SAAS,EAAE,+CACTd,aAAa,GACT,gGAAgG,GAChG,oEAAoE,EACvE;UAAAe,QAAA,eAEH1C,OAAA;YAAKyC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C1C,OAAA,CAACF,QAAQ;cAAC8D,IAAI,EAAE,EAAG;cAACnB,SAAS,EAAEd,aAAa,GAAG,gBAAgB,GAAG;YAAgB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrF/C,OAAA;cAAKyC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1C,OAAA;gBAAKyC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3Bf,aAAa,GAAG,eAAe,GAAG;cAAiB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN/C,OAAA;gBAAKyC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCf,aAAa,GAAG,mCAAmC,GAAG;cAA6B;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7B,iBAAiB,iBAChBlB,OAAA;MAAKyC,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClF1C,OAAA;QAAKyC,SAAS,EAAC,mEAAmE;QAAAC,QAAA,gBAChF1C,OAAA;UAAIyC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEjF/C,OAAA;UAAKyC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB1C,OAAA;YAAGyC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GAAC,wBAAmB,GAAArC,iBAAA,GAACG,MAAM,CAACgC,QAAQ,cAAAnC,iBAAA,uBAAfA,iBAAA,CAAiB6C,cAAc,CAAC,CAAC;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpG/C,OAAA;YAAKyC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EACpC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAACmB,GAAG,CAACxB,UAAU,iBAC9BrC,OAAA;cAEE2C,OAAO,EAAEA,CAAA,KAAM1B,qBAAqB,CAACoB,UAAU,CAAE;cACjDI,SAAS,EAAE,6DACTzB,kBAAkB,KAAKqB,UAAU,GAC7B,yBAAyB,GACzB,6CAA6C,EAChD;cAAAK,QAAA,GAEFL,UAAU,EAAC,GACd;YAAA,GATOA,UAAU;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAST,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAEL/B,kBAAkB,iBACjBhB,OAAA;YAAKyC,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACjD1C,OAAA;cAAGyC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5D/C,OAAA;cAAGyC,SAAS,EAAC,sCAAsC;cAAAC,QAAA,GAAC,GACjD,EAACN,uBAAuB,CAACpB,kBAAkB,CAAC,CAACkC,cAAc,CAAC,CAAC;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAELhC,aAAa,iBACZf,OAAA;UAAKyC,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eACtE1C,OAAA;YAAGyC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAE3B;UAAa;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CACN,eAED/C,OAAA;UAAKyC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1C,OAAA;YACE2C,OAAO,EAAEA,CAAA,KAAM;cACbxB,oBAAoB,CAAC,KAAK,CAAC;cAC3BF,qBAAqB,CAAC,IAAI,CAAC;YAC7B,CAAE;YACFwB,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAC5E;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/C,OAAA;YACE2C,OAAO,EAAER,cAAe;YACxBwB,QAAQ,EAAE,CAAC3C,kBAAkB,IAAIF,cAAe;YAChD2B,SAAS,EAAC,wFAAwF;YAAAC,QAAA,EAEjG5B,cAAc,GAAG,iBAAiB,GAAG;UAAW;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD/C,OAAA,CAACJ,UAAU;MAACkE,WAAW,EAAC;IAAS;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjC,CAAC;AAEV,CAAC;AAAC7C,EAAA,CA1RID,WAAqB;EAAA,QACRR,WAAW,EACXC,WAAW,EAUxBC,aAAa;AAAA;AAAAoE,EAAA,GAZb9D,WAAqB;AA4R3B,eAAeA,WAAW;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}