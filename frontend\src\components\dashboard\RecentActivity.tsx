import React from 'react';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  ShieldExclamationIcon,
  CpuChipIcon,
  UserIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface ActivityItem {
  id: string;
  type: 'money_gained' | 'money_lost' | 'attack' | 'defense' | 'upgrade' | 'login';
  title: string;
  description: string;
  amount?: number;
  timestamp: string;
  severity?: 'low' | 'medium' | 'high';
}

interface RecentActivityProps {
  activities?: ActivityItem[];
  isLoading?: boolean;
}

// Dados de exemplo - em produção viriam da API
const mockActivities: ActivityItem[] = [
  {
    id: '1',
    type: 'money_gained',
    title: 'Exploit Realizado',
    description: 'Você exploitou ************* e ganhou dinheiro',
    amount: 1500,
    timestamp: '2024-01-15T10:30:00Z',
    severity: 'low'
  },
  {
    id: '2',
    type: 'attack',
    title: 'Tentativa de Invasão',
    description: 'Alguém tentou exploitar seu sistema',
    timestamp: '2024-01-15T09:15:00Z',
    severity: 'medium'
  },
  {
    id: '3',
    type: 'upgrade',
    title: 'Upgrade Realizado',
    description: 'CPU atualizado para nível 5',
    timestamp: '2024-01-15T08:45:00Z',
    severity: 'low'
  },
  {
    id: '4',
    type: 'money_lost',
    title: 'Sistema Exploitado',
    description: 'Você perdeu dinheiro em um ataque',
    amount: 800,
    timestamp: '2024-01-15T07:20:00Z',
    severity: 'high'
  },
  {
    id: '5',
    type: 'defense',
    title: 'Ataque Bloqueado',
    description: 'Seu firewall bloqueou uma tentativa de invasão',
    timestamp: '2024-01-15T06:10:00Z',
    severity: 'low'
  }
];

const RecentActivity: React.FC<RecentActivityProps> = ({ 
  activities = mockActivities, 
  isLoading = false 
}) => {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'money_gained':
        return <ArrowUpIcon className="h-5 w-5 text-green-400" />;
      case 'money_lost':
        return <ArrowDownIcon className="h-5 w-5 text-red-400" />;
      case 'attack':
        return <ShieldExclamationIcon className="h-5 w-5 text-orange-400" />;
      case 'defense':
        return <ShieldExclamationIcon className="h-5 w-5 text-blue-400" />;
      case 'upgrade':
        return <CpuChipIcon className="h-5 w-5 text-purple-400" />;
      case 'login':
        return <UserIcon className="h-5 w-5 text-gray-400" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getActivityColor = (type: string, severity?: string) => {
    if (severity === 'high') return 'border-l-red-500';
    if (severity === 'medium') return 'border-l-orange-500';
    
    switch (type) {
      case 'money_gained':
        return 'border-l-green-500';
      case 'money_lost':
        return 'border-l-red-500';
      case 'attack':
        return 'border-l-orange-500';
      case 'defense':
        return 'border-l-blue-500';
      case 'upgrade':
        return 'border-l-purple-500';
      default:
        return 'border-l-gray-500';
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d`;
  };

  const formatMoney = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="card">
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          Atividade Recente
        </h3>
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex items-center space-x-3 p-3">
                <div className="w-10 h-10 bg-bg-tertiary rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-bg-tertiary rounded w-3/4" />
                  <div className="h-3 bg-bg-tertiary rounded w-1/2" />
                </div>
                <div className="h-3 bg-bg-tertiary rounded w-8" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Atividade Recente
        </h3>
        <button className="text-accent-blue hover:text-primary-light text-sm font-medium transition-colors">
          Ver todas
        </button>
      </div>

      <div className="space-y-1">
        {activities.length === 0 ? (
          <div className="text-center py-8">
            <ClockIcon className="h-12 w-12 text-text-muted mx-auto mb-3" />
            <p className="text-text-muted">Nenhuma atividade recente</p>
          </div>
        ) : (
          activities.map((activity) => (
            <div
              key={activity.id}
              className={`
                flex items-center space-x-3 p-3 rounded-lg border-l-4 
                bg-bg-tertiary hover:bg-bg-primary transition-colors cursor-pointer
                ${getActivityColor(activity.type, activity.severity)}
              `}
            >
              {/* Ícone */}
              <div className="flex-shrink-0 w-10 h-10 bg-bg-secondary rounded-full flex items-center justify-center">
                {getActivityIcon(activity.type)}
              </div>

              {/* Conteúdo */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-text-primary truncate">
                    {activity.title}
                  </p>
                  <span className="text-xs text-text-muted flex-shrink-0 ml-2">
                    {formatTime(activity.timestamp)}
                  </span>
                </div>
                
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-text-muted truncate">
                    {activity.description}
                  </p>
                  
                  {activity.amount && (
                    <span className={`
                      text-xs font-mono font-bold flex-shrink-0 ml-2
                      ${activity.type === 'money_gained' ? 'text-green-400' : 'text-red-400'}
                    `}>
                      {activity.type === 'money_gained' ? '+' : '-'}
                      {formatMoney(activity.amount)}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Resumo de estatísticas */}
      <div className="mt-4 pt-4 border-t border-border-color">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-green-400">
              {activities.filter(a => a.type === 'money_gained').length}
            </div>
            <div className="text-xs text-text-muted">Ganhos</div>
          </div>
          
          <div>
            <div className="text-lg font-bold text-orange-400">
              {activities.filter(a => a.type === 'attack').length}
            </div>
            <div className="text-xs text-text-muted">Ataques</div>
          </div>
          
          <div>
            <div className="text-lg font-bold text-blue-400">
              {activities.filter(a => a.type === 'defense').length}
            </div>
            <div className="text-xs text-text-muted">Defesas</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecentActivity;
