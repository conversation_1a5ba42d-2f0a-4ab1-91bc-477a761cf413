{"version": 3, "file": "static/css/main.879b3e34.css", "mappings": "AAAA,mDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,qDAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,iCAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAAd,4OAAc,CAAd,eAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,cAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,kWAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,uBAAc,CAAd,0GAAc,CAAd,wGAAc,CAAd,mGAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,gDAAc,CAAd,8CAAc,CAAd,kBAAc,CAAd,2CAAc,CAAd,6VAAc,CAAd,uQAAc,CAAd,sCAAc,CAAd,2BAAc,CAAd,2BAAc,CAAd,oBAAc,CAAd,gCAAc,CAAd,wBAAc,CAAd,qEAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,0BAAc,CAAd,kEAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,oBAAc,CAAd,gBAAc,CAAd,aAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,WAAc,CAAd,SAAc,CAAd,gCAAc,CAAd,wBAAc,CAAd,wBAAc,CAAd,gBAAc,CAAd,qBAAc,CAAd,UAAc,CAAd,+BAAc,CAAd,+BAAc,CAAd,oFAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,uBAAc,CAAd,0GAAc,CAAd,wGAAc,CAAd,sGAAc,CAAd,kBAAc,CAAd,0EAAc,CAAd,uBAAc,CAAd,qDAAc,CAAd,kBAAc,CAAd,mTAAc,CAAd,qEAAc,EAAd,uMAAc,CAAd,kEAAc,EAAd,gMAAc,CAAd,mRAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,2EAAc,EAAd,wHAAc,CAAd,oFAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,eAAc,CAAd,cAAc,CAAd,iBAAc,CAAd,6BAAc,CAAd,8CAAc,CAAd,yCAAc,CAEd,wCAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,qBAAmB,CAAnB,cAAmB,CAAnB,gBAAmB,CAAnB,qBAAmB,CAAnB,mBAAmB,CAAnB,YAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,qBAAmB,CAAnB,8BAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,yCAAmB,CAAnB,mOAAmB,CAAnB,wCAAmB,CAAnB,mCAAmB,CAAnB,uNAAmB,CAAnB,iDAAmB,CAAnB,0CAAmB,CAAnB,+CAAmB,CAAnB,sCAAmB,CAAnB,8BAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,4CAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,yCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,qEAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,qCAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,0CAAmB,CAAnB,6BAAmB,CAAnB,gDAAmB,CAAnB,iDAAmB,CAAnB,wBAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,gCAAmB,CAAnB,8BAAmB,CAAnB,gCAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,0CAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,2CAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,mCAAmB,CAAnB,iBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,yBAAmB,CAAnB,4DAAmB,CAAnB,wCAAmB,CAAnB,yBAAmB,CAAnB,6DAAmB,CAAnB,yCAAmB,CAAnB,yBAAmB,CAAnB,2DAAmB,CAAnB,0CAAmB,CAAnB,yBAAmB,CAAnB,4DAAmB,CAAnB,0CAAmB,CAAnB,yBAAmB,CAAnB,4DAAmB,CAAnB,uCAAmB,CAAnB,yBAAmB,CAAnB,2DAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,kDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,mCAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,mCAAmB,CAAnB,sFAAmB,CAAnB,6FAAmB,CAAnB,qFAAmB,CAAnB,4EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,8EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,2EAAmB,CAAnB,yGAAmB,CAAnB,sEAAmB,CAAnB,wEAAmB,CAAnB,uEAAmB,CAAnB,sEAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,8CAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,4CAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,aAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,mCAAmB,CAAnB,oCAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,sCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,yCAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,uCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,sCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,wCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,sCAAmB,CAAnB,6DAAmB,CAAnB,aAAmB,CAAnB,sDAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,2EAAmB,CAAnB,kGAAmB,CAAnB,8BAAmB,CAAnB,kMAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,qIAAmB,CAAnB,kDAAmB,CAAnB,wEAAmB,CAAnB,kDAAmB,CAAnB,4EAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,+DAAmB,CAGnB,MACE,iBAAkB,CAClB,sBAAuB,CACvB,uBAAwB,CACxB,mBAAoB,CACpB,gBAAiB,CACjB,oBAAqB,CACrB,sBAAuB,CACvB,qBAAsB,CACtB,sBAAuB,CACvB,wBAAyB,CACzB,oBAAqB,CACrB,gBAAiB,CACjB,kBAAgC,CAChC,qBAAmC,CACnC,uBAAwB,CACxB,yBAA0B,CAC1B,sBAAuB,CACvB,oBACF,CAGA,EACE,qBACF,CAEA,KAEE,kBAA6B,CAA7B,4BAA6B,CAC7B,aAA0B,CAA1B,yBAA0B,CAF1B,qDAA4D,CAI5D,QAAS,CADT,iBAAkB,CAElB,SACF,CAGA,YAOE,gEAA6G,CAN7G,UAAW,CAKX,WAAY,CAFZ,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAGX,UACF,CAGA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,iBACE,MACE,SAAU,CACV,kBACF,CACA,IACE,UAAY,CACZ,oBACF,CACF,CAEA,gBACE,GACE,6DAA8F,CAA9F,2FACF,CACA,GACE,8DAA+F,CAA/F,4FACF,CACF,CAGA,cACE,+BACF,CAEA,YACE,aAA2B,CAA3B,0BAA2B,CAE3B,iCAAqC,CADrC,4BAAuC,CAAvC,sCAEF,CAEA,cACE,wBAAsC,CAAtC,qCAAsC,CACtC,6BACF,CAEA,eAKE,2BAA4B,CAF5B,wBAAyB,CACzB,iBAAkB,CAFlB,WAAY,CADZ,UAKF,CAGA,oBACE,SACF,CAEA,0BACE,kBAA+B,CAA/B,8BACF,CAEA,0BACE,kBAA0B,CAA1B,yBAA0B,CAC1B,iBACF,CAEA,gCACE,kBAAgC,CAAhC,+BACF,CAIE,8BAAkH,CAAlH,mBAAkH,CAAlH,wBAAkH,CAAlH,sDAAkH,CAAlH,mBAAkH,CAAlH,UAAkH,CAAlH,+CAAkH,CAAlH,eAAkH,CAAlH,kBAAkH,CAAlH,iHAAkH,CAAlH,kDAAkH,CAAlH,oCAAkH,CAAlH,wBAAkH,CAAlH,sDAAkH,CAIlH,oCAAwJ,CAAxJ,iBAAwJ,CAAxJ,mBAAwJ,CAAxJ,wBAAwJ,CAAxJ,qDAAwJ,CAAxJ,oBAAwJ,CAAxJ,qDAAwJ,CAAxJ,mBAAwJ,CAAxJ,gBAAwJ,CAAxJ,aAAwJ,CAAxJ,+CAAwJ,CAAxJ,eAAwJ,CAAxJ,kBAAwJ,CAAxJ,iHAAwJ,CAAxJ,kDAAwJ,CAAxJ,sCAAwJ,CAAxJ,wBAAwJ,CAAxJ,qDAAwJ,CAIxJ,gCAAgL,CAAhL,mBAAgL,CAAhL,wBAAgL,CAAhL,oBAAgL,CAAhL,qDAAgL,CAAhL,oBAAgL,CAAhL,gBAAgL,CAAhL,aAAgL,CAAhL,4CAAgL,CAAhL,iCAAgL,CAAhL,eAAgL,CAAhL,kBAAgL,CAAhL,+CAAgL,CAAhL,kDAAgL,CAAhL,kCAAgL,CAAhL,mBAAgL,CAAhL,wBAAgL,CAAhL,qDAAgL,CAAhL,UAAgL,CAAhL,yCAAgL,CADlL,WAGE,6BAA0C,CAD1C,4BAAuC,CAAvC,sCAEF,CAEA,iBACE,6BACF,CAQE,kBAJA,qBAA0E,CAA1E,iBAA0E,CAA1E,+DAA0E,CAA1E,iGAA0E,CAA1E,wBAA0E,CAA1E,qDAA0E,CAA1E,oBAA0E,CAA1E,qDAA0E,CAA1E,mBAA0E,CAA1E,gBAA0E,CAA1E,+CAA0E,CAA1E,+GAI2E,CAA3E,mCAA2E,CAA3E,uBAA2E,CAA3E,kDAA2E,CAA3E,uCAA2E,CAA3E,gEAA2E,CAA3E,kGAA2E,CAA3E,oBAA2E,CAA3E,uDAA2E,CAA3E,+CAA2E,CAA3E,kGAA2E,CAK3E,oCAAwK,CAAxK,iBAAwK,CAAxK,mBAAwK,CAAxK,wBAAwK,CAAxK,qDAAwK,CAAxK,oBAAwK,CAAxK,qDAAwK,CAAxK,mBAAwK,CAAxK,gBAAwK,CAAxK,aAAwK,CAAxK,+CAAwK,CAAxK,cAAwK,CAAxK,iHAAwK,CAAxK,kDAAwK,CAAxK,UAAwK,CAAxK,0CAAwK,CAAxK,oBAAwK,CAAxK,uDAAwK,CAAxK,uBAAwK,CAAxK,kBAAwK,CAKxK,6EAAiE,CAAjE,iGAAiE,CAAjE,mBAAiE,CAAjE,+CAAiE,CAAjE,kHAAiE,CAAjE,YAAiE,CAAjE,cAAiE,CAAjE,UAAiE,CAAjE,QAAiE,CAAjE,UAAiE,CAIjE,8CAA2C,CAA3C,sDAA2C,CAI3C,0CAJA,iBAA2C,CAA3C,mBAA2C,CAA3C,+DAA2C,CAA3C,iGAA2C,CAA3C,mBAA2C,CAA3C,+CAA2C,CAA3C,6GAA2C,CAA3C,+CAA2C,CAA3C,eAA2C,CAA3C,YAA2C,CAA3C,cAA2C,CAA3C,UAA2C,CAA3C,QAA2C,CAA3C,UAIyC,CAAzC,4CAAyC,CAAzC,sDAAyC,CAIzC,oCAA0C,CAA1C,mBAA0C,CAA1C,+DAA0C,CAA1C,iGAA0C,CAA1C,wBAA0C,CAA1C,sDAA0C,CAA1C,mBAA0C,CAA1C,+CAA0C,CAA1C,6GAA0C,CAA1C,+CAA0C,CAA1C,eAA0C,CAA1C,YAA0C,CAA1C,cAA0C,CAA1C,UAA0C,CAA1C,QAA0C,CAA1C,UAA0C,CAK1C,0CAA0D,EAA1D,8BAA0D,CAA1D,iCAA0D,CAA1D,uBAA0D,CAA1D,oBAA0D,CAA1D,2EAA0D,CAlL5D,yCAoLA,CApLA,iBAoLA,CApLA,6LAoLA,CApLA,6CAoLA,CApLA,wBAoLA,CApLA,qDAoLA,CApLA,+CAoLA,CApLA,wBAoLA,CApLA,qDAoLA,CApLA,8CAoLA,CApLA,wBAoLA,CApLA,qDAoLA,CApLA,2CAoLA,CApLA,wBAoLA,CApLA,sDAoLA,CApLA,2CAoLA,CApLA,wBAoLA,CApLA,sDAoLA,CApLA,kDAoLA,CApLA,wBAoLA,CApLA,sDAoLA,CApLA,2CAoLA,CApLA,wBAoLA,CApLA,qDAoLA,CApLA,4CAoLA,CApLA,wBAoLA,CApLA,sDAoLA,CApLA,4CAoLA,CApLA,wBAoLA,CApLA,sDAoLA,CApLA,6CAoLA,CApLA,wBAoLA,CApLA,sDAoLA,CApLA,+CAoLA,CApLA,wBAoLA,CApLA,sDAoLA,CApLA,6CAoLA,CApLA,wBAoLA,CApLA,uDAoLA,CApLA,0CAoLA,CApLA,wBAoLA,CApLA,sDAoLA,CApLA,wCAoLA,CApLA,qBAoLA,CApLA,wDAoLA,CApLA,6CAoLA,CApLA,wBAoLA,CApLA,qDAoLA,CApLA,+CAoLA,CApLA,+CAoLA,CApLA,aAoLA,CApLA,+CAoLA,CApLA,oDAoLA,CApLA,aAoLA,CApLA,4CAoLA,CApLA,oDAoLA,CApLA,aAoLA,CApLA,8CAoLA,CApLA,8CAoLA,CApLA,aAoLA,CApLA,+CAoLA,CApLA,mDAoLA,CApLA,aAoLA,CApLA,+CAoLA,CApLA,uFAoLA,CApLA,iGAoLA,CApLA,+CAoLA,CApLA,kGAoLA,CApLA,oFAoLA,CApLA,oBAoLA,CApLA,uDAoLA,CApLA,0DAoLA,CApLA,oBAoLA,CApLA,sDAoLA,CApLA,kDAoLA,CApLA,oBAoLA,CApLA,sDAoLA,CApLA,kDAoLA,CApLA,kBAoLA,CApLA,yCAoLA,CApLA,gBAoLA,CApLA,6LAoLA,CApLA,yDAoLA,CApLA,yCAoLA,CApLA,gEAoLA,CApLA,aAoLA,CApLA,+CAoLA,CApLA,iDAoLA,CApLA,sBAoLA,CApLA,wBAoLA,EApLA,gDAoLA,CApLA,wBAoLA,CApLA,wBAoLA,CApLA,sBAoLA,CApLA,6BAoLA,CApLA,qBAoLA,CApLA,6BAoLA,CApLA,8DAoLA,CApLA,8DAoLA,CApLA,qBAoLA,CApLA,0BAoLA,EApLA,mEAoLA,CApLA,6BAoLA,CApLA,8DAoLA,CApLA,8DAoLA,EApLA,uDAoLA", "sources": ["styles/globals.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* === TEMA MODERNO PRETO E AZUL === */\n:root {\n  --primary: #2563eb;\n  --primary-dark: #1d4ed8;\n  --primary-light: #3b82f6;\n  --secondary: #64748b;\n  --accent: #0ea5e9;\n  --bg-primary: #0f172a;\n  --bg-secondary: #1e293b;\n  --bg-tertiary: #334155;\n  --text-primary: #f8fafc;\n  --text-secondary: #cbd5e1;\n  --text-muted: #94a3b8;\n  --border: #334155;\n  --shadow: rgba(37, 99, 235, 0.1);\n  --shadow-lg: rgba(37, 99, 235, 0.2);\n  --cyber-primary: #00ff41;\n  --cyber-secondary: #0080ff;\n  --cyber-accent: #ff0080;\n  --cyber-glow: #00ff41;\n}\n\n/* Reset e Base */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  overflow-x: hidden;\n  margin: 0;\n  padding: 0;\n}\n\n/* Gradient de fundo moderno */\nbody::before {\n  content: '';\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, rgba(0, 123, 255, 0.03) 0%, transparent 50%, rgba(13, 17, 23, 0.05) 100%);\n  z-index: -1;\n}\n\n/* Animações personalizadas */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.5;\n    transform: scale(1.1);\n  }\n}\n\n@keyframes glow {\n  0% {\n    text-shadow: 0 0 5px var(--cyber-glow), 0 0 10px var(--cyber-glow), 0 0 15px var(--cyber-glow);\n  }\n  100% {\n    text-shadow: 0 0 10px var(--cyber-glow), 0 0 20px var(--cyber-glow), 0 0 30px var(--cyber-glow);\n  }\n}\n\n/* Componentes customizados */\n.game-section {\n  animation: fadeInUp 0.5s ease-out;\n}\n\n.cyber-text {\n  color: var(--cyber-primary);\n  text-shadow: 0 0 10px var(--cyber-glow);\n  font-family: 'Courier New', monospace;\n}\n\n.cyber-border {\n  border: 1px solid var(--cyber-primary);\n  box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);\n}\n\n.status-online {\n  width: 12px;\n  height: 12px;\n  background-color: #10b981;\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n}\n\n/* Scrollbar personalizada */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: var(--bg-secondary);\n}\n\n::-webkit-scrollbar-thumb {\n  background: var(--primary);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: var(--primary-light);\n}\n\n/* Botões personalizados */\n.btn-primary {\n  @apply bg-primary hover:bg-primary-dark text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;\n}\n\n.btn-secondary {\n  @apply bg-bg-secondary hover:bg-bg-tertiary text-text-primary border border-border-color font-medium py-2 px-4 rounded-lg transition-colors duration-200;\n}\n\n.btn-cyber {\n  @apply bg-transparent border-2 border-cyber-primary text-cyber-primary hover:bg-cyber-primary hover:text-black font-mono font-bold py-2 px-4 rounded transition-all duration-300;\n  text-shadow: 0 0 10px var(--cyber-glow);\n  box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);\n}\n\n.btn-cyber:hover {\n  box-shadow: 0 0 20px rgba(0, 255, 65, 0.6);\n}\n\n/* Cards personalizados */\n.card {\n  @apply bg-bg-secondary border border-border-color rounded-lg p-4 shadow-lg;\n}\n\n.card-hover {\n  @apply card hover:border-accent hover:shadow-xl transition-all duration-300;\n}\n\n/* Inputs personalizados */\n.input-primary {\n  @apply w-full p-3 rounded-lg bg-surface-elevated text-text-primary border border-border-color focus:border-accent-blue focus:outline-none transition-colors duration-200;\n}\n\n/* Notificações */\n.notification {\n  @apply fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm;\n}\n\n.notification-success {\n  @apply notification bg-green-600 text-white;\n}\n\n.notification-error {\n  @apply notification bg-red-600 text-white;\n}\n\n.notification-info {\n  @apply notification bg-blue-600 text-white;\n}\n\n/* Loading spinner */\n.spinner {\n  @apply animate-spin rounded-full border-b-2 border-primary;\n}\n"], "names": [], "sourceRoot": ""}