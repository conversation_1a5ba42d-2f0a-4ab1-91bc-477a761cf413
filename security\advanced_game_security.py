from flask import request, abort, g
import time
import json
import hmac
import hashlib
import random
from functools import wraps
from collections import defaultdict
import threading

class AdvancedGameSecurity:
    """
    Sistema avançado de segurança específico para o jogo SHACK
    Complementa o GameSecurity básico com recursos anti-cheat
    """
    
    def __init__(self, app=None):
        self.app = app
        
        # Rastreamento de ações por usuário
        self.user_actions = defaultdict(list)
        self.user_cooldowns = defaultdict(dict)
        self.suspicious_users = set()
        
        # Configurações de limite
        self.action_limits = {
            'attack': {'count': 10, 'per_seconds': 300},    # 10 ataques por 5 minutos
            'purchase': {'count': 20, 'per_seconds': 60},   # 20 compras por minuto
            'transfer': {'count': 5, 'per_seconds': 180},   # 5 transferências por 3 minutos
            'deface': {'count': 3, 'per_seconds': 600},     # 3 defacements por 10 minutos
        }
        
        # Cooldowns específicos (em segundos)
        self.cooldowns = {
            'attack': 10,      # 10 segundos entre ataques
            'deface': 30,      # 30 segundos entre defacements
            'big_purchase': 5, # 5 segundos entre compras grandes
        }
        
        # Lock para thread safety
        self.lock = threading.Lock()
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize with Flask app"""
        self.app = app
        print("✅ Advanced game security initialized")
        
        # Cleanup thread
        cleanup_thread = threading.Thread(target=self._cleanup_old_data, daemon=True)
        cleanup_thread.start()
    
    def verify_game_action(self, action_type, cooldown_check=True):
        """
        Decorator para verificar se uma ação do jogo é válida
        
        Usage: @game_security.verify_game_action('attack')
        """
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                # Obter UID do usuário (assumindo que está em g.user ou args)
                user_id = None
                if hasattr(g, 'user') and g.user:
                    user_id = g.user.get('uid')
                elif args and len(args) > 0:
                    user_id = args[0]  # Primeiro argumento é geralmente o UID
                
                if not user_id:
                    print("⚠️ Game Security: User ID not found")
                    abort(400, description="User authentication required")
                
                # Verificar se o usuário está em cooldown
                if cooldown_check and not self._check_cooldown(user_id, action_type):
                    print(f"⚠️ Game Security: User {user_id} is in cooldown for {action_type}")
                    abort(429, description=f"Action '{action_type}' is in cooldown")
                
                # Verificar limites de ação
                if not self._check_action_limit(user_id, action_type):
                    print(f"⚠️ Game Security: User {user_id} exceeded action limit for {action_type}")
                    abort(429, description=f"Too many '{action_type}' actions")
                
                # Verificar se o usuário está marcado como suspeito
                if user_id in self.suspicious_users:
                    print(f"⚠️ Game Security: Suspicious user {user_id} attempting {action_type}")
                    # Pode retornar erro ou aplicar verificações adicionais
                
                # Registrar a ação
                self._record_action(user_id, action_type)
                
                # Set cooldown if specified
                if cooldown_check and action_type in self.cooldowns:
                    self._set_cooldown(user_id, action_type)
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def validate_game_transaction(self, min_amount=None, max_amount=None, require_positive=True):
        """
        Decorator para validar transações financeiras do jogo
        
        Usage: @game_security.validate_game_transaction(min_amount=1, max_amount=1000000)
        """
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                # Obter dados da requisição
                data = None
                if request.is_json:
                    data = request.get_json(silent=True)
                elif request.form:
                    data = request.form.to_dict()
                
                if data:
                    # Validar valores monetários
                    for key in ['quantidade', 'valor', 'preco', 'porcentagem']:
                        if key in data:
                            value = data[key]
                            
                            # Verificar se é número
                            if not isinstance(value, (int, float)):
                                try:
                                    value = float(value)
                                    data[key] = value
                                except (ValueError, TypeError):
                                    print(f"⚠️ Game Security: Invalid numeric value for {key}: {value}")
                                    abort(400, description=f"Invalid numeric value for {key}")
                            
                            # Verificar se é positivo (se requerido)
                            if require_positive and value <= 0:
                                print(f"⚠️ Game Security: Negative or zero value for {key}: {value}")
                                abort(400, description=f"Value for {key} must be positive")
                            
                            # Verificar limites
                            if min_amount is not None and value < min_amount:
                                print(f"⚠️ Game Security: Value {value} below minimum {min_amount}")
                                abort(400, description=f"Value below minimum allowed ({min_amount})")
                            
                            if max_amount is not None and value > max_amount:
                                print(f"⚠️ Game Security: Value {value} above maximum {max_amount}")
                                abort(400, description=f"Value above maximum allowed ({max_amount})")
                    
                    # Detectar tentativas de valores extremos
                    for key, value in data.items():
                        if isinstance(value, (int, float)):
                            if value > 999999999:  # 1 bilhão
                                self._flag_suspicious_behavior(
                                    g.user.get('uid') if hasattr(g, 'user') else 'unknown',
                                    f"Extreme value attempted: {key}={value}"
                                )
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def detect_cheat_patterns(self):
        """
        Decorator para detectar padrões de trapaça
        
        Usage: @game_security.detect_cheat_patterns()
        """
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                user_id = None
                if hasattr(g, 'user') and g.user:
                    user_id = g.user.get('uid')
                elif args and len(args) > 0:
                    user_id = args[0]
                
                if user_id:
                    # Verificar padrões suspeitos
                    self._analyze_user_behavior(user_id)
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def _check_cooldown(self, user_id, action_type):
        """Verificar se o usuário está em cooldown para uma ação"""
        with self.lock:
            if user_id not in self.user_cooldowns:
                return True
            
            cooldown_end = self.user_cooldowns[user_id].get(action_type, 0)
            return time.time() > cooldown_end
    
    def _set_cooldown(self, user_id, action_type):
        """Definir cooldown para uma ação"""
        with self.lock:
            if user_id not in self.user_cooldowns:
                self.user_cooldowns[user_id] = {}
            
            cooldown_duration = self.cooldowns.get(action_type, 0)
            self.user_cooldowns[user_id][action_type] = time.time() + cooldown_duration
    
    def _check_action_limit(self, user_id, action_type):
        """Verificar se o usuário excedeu o limite de ações"""
        if action_type not in self.action_limits:
            return True
        
        with self.lock:
            limit_config = self.action_limits[action_type]
            current_time = time.time()
            window_start = current_time - limit_config['per_seconds']
            
            # Obter ações do usuário no período
            user_actions = self.user_actions.get(user_id, [])
            recent_actions = [
                action for action in user_actions 
                if action['time'] > window_start and action['type'] == action_type
            ]
            
            return len(recent_actions) < limit_config['count']
    
    def _record_action(self, user_id, action_type):
        """Registrar uma ação do usuário"""
        with self.lock:
            action_record = {
                'type': action_type,
                'time': time.time(),
                'ip': request.remote_addr if request else 'unknown'
            }
            
            self.user_actions[user_id].append(action_record)
            
            # Limitar histórico de ações (manter apenas as últimas 100)
            if len(self.user_actions[user_id]) > 100:
                self.user_actions[user_id] = self.user_actions[user_id][-100:]
    
    def _analyze_user_behavior(self, user_id):
        """Analisar comportamento do usuário para detectar padrões suspeitos"""
        if user_id not in self.user_actions:
            return
        
        actions = self.user_actions[user_id]
        current_time = time.time()
        
        # Verificar ações muito rápidas (possível bot)
        recent_actions = [a for a in actions if current_time - a['time'] < 60]  # Último minuto
        
        if len(recent_actions) > 30:  # Mais de 30 ações por minuto
            self._flag_suspicious_behavior(user_id, "Extremely high action frequency")
        
        # Verificar padrões temporais suspeitos (ações em intervalos muito regulares)
        if len(recent_actions) > 10:
            intervals = []
            for i in range(1, len(recent_actions)):
                interval = recent_actions[i]['time'] - recent_actions[i-1]['time']
                intervals.append(interval)
            
            # Se todos os intervalos são quase idênticos, pode ser um bot
            if len(set(round(interval, 1) for interval in intervals)) == 1:
                self._flag_suspicious_behavior(user_id, "Robotic timing patterns")
    
    def _flag_suspicious_behavior(self, user_id, reason):
        """Marcar um usuário como suspeito"""
        self.suspicious_users.add(user_id)
        print(f"⚠️ SECURITY ALERT: User {user_id} flagged for suspicious behavior: {reason}")
        
        # Log to activity monitor if available
        if self.app and hasattr(self.app, 'activity_monitor'):
            self.app.activity_monitor.log_unauthorized_access(
                f"Suspicious behavior: {reason}",
                request.remote_addr if request else None
            )
    
    def _cleanup_old_data(self):
        """Limpar dados antigos periodicamente"""
        while True:
            time.sleep(300)  # Executar a cada 5 minutos
            
            try:
                current_time = time.time()
                with self.lock:
                    # Limpar ações antigas (manter apenas últimas 24 horas)
                    cutoff_time = current_time - 86400
                    
                    for user_id in list(self.user_actions.keys()):
                        self.user_actions[user_id] = [
                            action for action in self.user_actions[user_id]
                            if action['time'] > cutoff_time
                        ]
                        
                        # Remover listas vazias
                        if not self.user_actions[user_id]:
                            del self.user_actions[user_id]
                    
                    # Limpar cooldowns expirados
                    for user_id in list(self.user_cooldowns.keys()):
                        self.user_cooldowns[user_id] = {
                            action: end_time
                            for action, end_time in self.user_cooldowns[user_id].items()
                            if end_time > current_time
                        }
                        
                        if not self.user_cooldowns[user_id]:
                            del self.user_cooldowns[user_id]
                            
            except Exception as e:
                print(f"Error in game security cleanup: {str(e)}")
    
    def get_user_stats(self, user_id):
        """Obter estatísticas de um usuário para análise"""
        with self.lock:
            actions = self.user_actions.get(user_id, [])
            cooldowns = self.user_cooldowns.get(user_id, {})
            
            # Calcular estatísticas
            current_time = time.time()
            recent_actions = [a for a in actions if current_time - a['time'] < 3600]  # Última hora
            
            action_counts = defaultdict(int)
            for action in recent_actions:
                action_counts[action['type']] += 1
            
            return {
                'total_actions_last_hour': len(recent_actions),
                'action_breakdown': dict(action_counts),
                'is_suspicious': user_id in self.suspicious_users,
                'active_cooldowns': {
                    action: max(0, end_time - current_time)
                    for action, end_time in cooldowns.items()
                    if end_time > current_time
                }
            }
    
    def reset_user_suspicion(self, user_id):
        """Remover flag de suspeito de um usuário (para administradores)"""
        self.suspicious_users.discard(user_id)
        print(f"✅ User {user_id} cleared from suspicious list")
