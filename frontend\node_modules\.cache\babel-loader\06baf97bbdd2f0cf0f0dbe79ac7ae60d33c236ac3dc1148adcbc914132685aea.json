{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../../stores/authStore';\nimport LoadingSpinner from '../common/LoadingSpinner';\nimport { Fragment as _Fragment, jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthGuard = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading,\n    user\n  } = useAuth();\n  console.log('AuthGuard - Estado:', {\n    isAuthenticated,\n    isLoading,\n    user: !!user\n  });\n\n  // MODO TEMPORÁRIO: Simular usuário autenticado para teste\n  const MODO_TESTE = true;\n  if (MODO_TESTE) {\n    console.log('AuthGuard - MODO TESTE: Permitindo acesso sem autenticação');\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: children\n    }, void 0, false);\n  }\n\n  // Mostrar loading enquanto verifica autenticação\n  if (isLoading) {\n    console.log('AuthGuard - Mostrando loading...');\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      fullScreen: true,\n      size: \"lg\",\n      text: \"Verificando autentica\\xE7\\xE3o...\",\n      color: \"primary\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirecionar para login se não autenticado\n  if (!isAuthenticated || !user) {\n    console.log('AuthGuard - Redirecionando para login...');\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Renderizar children se autenticado\n  console.log('AuthGuard - Usuário autenticado, renderizando children...');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(AuthGuard, \"dRMuwJKhnJOfZ5vJGRuol568QeA=\", false, function () {\n  return [useAuth];\n});\n_c = AuthGuard;\nexport default AuthGuard;\nvar _c;\n$RefreshReg$(_c, \"AuthGuard\");", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "LoadingSpinner", "Fragment", "_Fragment", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "children", "_s", "isAuthenticated", "isLoading", "user", "console", "log", "MODO_TESTE", "fullScreen", "size", "text", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/auth/AuthGuard.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../../stores/authStore';\nimport LoadingSpinner from '../common/LoadingSpinner';\n\ninterface AuthGuardProps {\n  children: React.ReactNode;\n}\n\nconst AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {\n  const { isAuthenticated, isLoading, user } = useAuth();\n\n  console.log('AuthGuard - Estado:', { isAuthenticated, isLoading, user: !!user });\n\n  // MODO TEMPORÁRIO: Simular usuário autenticado para teste\n  const MODO_TESTE = true;\n\n  if (MODO_TESTE) {\n    console.log('AuthGuard - MODO TESTE: Permitindo acesso sem autenticação');\n    return <>{children}</>;\n  }\n\n  // Mostrar loading enquanto verifica autenticação\n  if (isLoading) {\n    console.log('AuthGuard - Mostrando loading...');\n    return (\n      <LoadingSpinner\n        fullScreen\n        size=\"lg\"\n        text=\"Verificando autenticação...\"\n        color=\"primary\"\n      />\n    );\n  }\n\n  // Redirecionar para login se não autenticado\n  if (!isAuthenticated || !user) {\n    console.log('AuthGuard - Redirecionando para login...');\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // Renderizar children se autenticado\n  console.log('AuthGuard - Usuário autenticado, renderizando children...');\n  return <>{children}</>;\n};\n\nexport default AuthGuard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,cAAc,MAAM,0BAA0B;AAAC,SAAAC,QAAA,IAAAC,SAAA,EAAAC,MAAA,IAAAC,OAAA;AAMtD,MAAMC,SAAmC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAM;IAAEC,eAAe;IAAEC,SAAS;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAEtDY,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;IAAEJ,eAAe;IAAEC,SAAS;IAAEC,IAAI,EAAE,CAAC,CAACA;EAAK,CAAC,CAAC;;EAEhF;EACA,MAAMG,UAAU,GAAG,IAAI;EAEvB,IAAIA,UAAU,EAAE;IACdF,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzE,oBAAOR,OAAA,CAAAF,SAAA;MAAAI,QAAA,EAAGA;IAAQ,gBAAG,CAAC;EACxB;;EAEA;EACA,IAAIG,SAAS,EAAE;IACbE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,oBACER,OAAA,CAACJ,cAAc;MACbc,UAAU;MACVC,IAAI,EAAC,IAAI;MACTC,IAAI,EAAC,mCAA6B;MAClCC,KAAK,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAEN;;EAEA;EACA,IAAI,CAACb,eAAe,IAAI,CAACE,IAAI,EAAE;IAC7BC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvD,oBAAOR,OAAA,CAACN,QAAQ;MAACwB,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;;EAEA;EACAV,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;EACxE,oBAAOR,OAAA,CAAAF,SAAA;IAAAI,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACC,EAAA,CAnCIF,SAAmC;EAAA,QACMN,OAAO;AAAA;AAAAyB,EAAA,GADhDnB,SAAmC;AAqCzC,eAAeA,SAAS;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}