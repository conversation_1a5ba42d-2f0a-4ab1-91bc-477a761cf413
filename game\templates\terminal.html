{% extends "base.html" %}

{% block content %}
<!-- <PERSON><PERSON> Terminal -->
<div id="terminal-section" class="game-section">
    <div class="h-full overflow-y-auto custom-scrollbar">
        <div class="max-w-4xl mx-auto p-6 pb-20 space-y-6">
            <!-- Cabeçalho do Terminal -->
            <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                <h2 class="text-2xl font-bold text-primary-text mb-4 flex items-center gap-3">
                    <svg class="w-8 h-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 7.5l3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0021 18V6a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 6v12a2.25 2.25 0 002.25 2.25z" />
                    </svg>
                    Terminal de Bruteforce
                </h2>
                <p class="text-secondary-text mb-6">Execute bruteforce em conexões ativas estabelecidas via exploit</p>
                
                <!-- Status das Conexões -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                        <h3 class="text-lg font-semibold text-primary-text mb-2">� Status das Conexões</h3>
                        <p class="text-3xl font-bold text-blue-400" id="conexoes-status-display">0/5</p>
                        <p class="text-sm text-secondary-text mt-1" id="conexoes-status-text">conexões ativas</p>
                    </div>
                    <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                        <h3 class="text-lg font-semibold text-primary-text mb-2">Gerenciar Conexões</h3>
                        <button id="btn-fechar-todas-conexoes" 
                                onclick="fecharTodasConexoes()" 
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                                disabled>
                            🔌 Fechar Todas
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Lista de Conexões Ativas -->
            <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                <h3 class="text-xl font-semibold text-primary-text mb-4">Conexões Estabelecidas</h3>
                <div id="conexoes-container">
                    <div class="text-center text-secondary-text py-8">
                        <svg class="w-16 h-16 mx-auto mb-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-16.5 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 002.25-2.25V6.75a2.25 2.25 0 00-2.25-2.25H6.75A2.25 2.25 0 004.5 6.75v10.5a2.25 2.25 0 002.25 2.25z" />
                        </svg>
                        <p>Nenhuma conexão ativa</p>
                        <p class="text-sm">Execute exploits bem-sucedidos para estabelecer conexões</p>
                    </div>
                </div>
            </div>
            
            <!-- Histórico de Bruteforce -->
            <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                <h3 class="text-xl font-semibold text-primary-text mb-4">Histórico de Ataques</h3>
                <div id="historico-bruteforce" class="space-y-3 max-h-64 overflow-y-auto">
                    <div class="text-center text-secondary-text py-4">
                        <p>Nenhum ataque realizado ainda</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Renderizar seção Terminal
async function renderTerminalSection() {
    const container = document.getElementById('terminal-section');
    if (!container) return;
    
    try {
        // Carregar conexões ativas
        await carregarConexoesTerminal();
        
        // Atualizar a cada 10 segundos
        setInterval(carregarConexoesTerminal, 10000);
        
    } catch (error) {
        console.error('Erro ao renderizar terminal:', error);
    }
}

// Carregar conexões do terminal
async function carregarConexoesTerminal() {
    try {
        const response = await fetchAPI('/api/conexoes/status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ uid: window.user_id })
        });
        
        if (response.sucesso) {
            const conexoes = response.conexoes || [];
            const totalConexoes = response.total_conexoes || 0;
            const limiteConexoes = response.limite_conexoes || 5;
            const noLimite = response.no_limite || false;
            
            // Atualizar status das conexões
            document.getElementById('conexoes-status-display').textContent = `${totalConexoes}/${limiteConexoes}`;
            document.getElementById('conexoes-status-text').textContent = 'conexões ativas';
            
            // Atualizar cor do status baseado no limite
            const statusDisplay = document.getElementById('conexoes-status-display');
            if (noLimite) {
                statusDisplay.className = 'text-3xl font-bold text-red-400';
            } else if (totalConexoes >= 3) {
                statusDisplay.className = 'text-3xl font-bold text-yellow-400';
            } else {
                statusDisplay.className = 'text-3xl font-bold text-blue-400';
            }
            
            // Habilitar/desabilitar botão de fechar todas
            const btnFecharTodas = document.getElementById('btn-fechar-todas-conexoes');
            btnFecharTodas.disabled = totalConexoes === 0;
            
            // Renderizar conexões
            renderConexoes(conexoes);
            
            // Comunicar status para outras seções (desabilitar exploit se necessário)
            if (window.updateExploitButtonStatus) {
                window.updateExploitButtonStatus(noLimite);
            }
        } else {
            console.error('Erro ao carregar conexões:', response.mensagem);
        }
    } catch (error) {
        console.error('Erro na requisição:', error);
    }
}

// Fechar todas as conexões
async function fecharTodasConexoes() {
    try {
        const button = document.getElementById('btn-fechar-todas-conexoes');
        const originalText = button.innerHTML;
        
        // Mostrar loading
        button.innerHTML = 'Fechando...';
        button.disabled = true;
        
        const response = await fetchAPI('/api/conexoes/fechar-todas', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ uid: window.user_id })
        });
        
        if (response.sucesso) {
            showNotification(`${response.mensagem}`, 'success');
            
            // Recarregar conexões
            await carregarConexoesTerminal();
        } else {
            showNotification(`${response.mensagem}`, 'error');
        }
        
        // Restaurar botão
        button.innerHTML = originalText;
        button.disabled = false;
        
    } catch (error) {
        console.error('Erro ao fechar conexões:', error);
        showNotification('Erro interno do servidor', 'error');
        
        // Restaurar botão
        const button = document.getElementById('btn-fechar-todas-conexoes');
        button.innerHTML = '🔌 Fechar Todas';
        button.disabled = false;
    }
}

// Fechar conexão específica
async function fecharConexaoEspecifica(alvo_ip) {
    try {
        const response = await fetchAPI('/api/conexoes/fechar', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                uid: window.user_id,
                alvo_ip: alvo_ip
            })
        });
        
        if (response.sucesso) {
            showNotification(`${response.mensagem}`, 'success');
            
            // Recarregar conexões
            await carregarConexoesTerminal();
        } else {
            showNotification(`${response.mensagem}`, 'error');
        }
        
    } catch (error) {
        console.error('Erro ao fechar conexão:', error);
        showNotification('Erro interno do servidor', 'error');
    }
}

// Renderizar lista de conexões
function renderConexoes(conexoes) {
    const container = document.getElementById('conexoes-container');
    
    if (conexoes.length === 0) {
        container.innerHTML = `
            <div class="text-center text-secondary-text py-8">
                <svg class="w-16 h-16 mx-auto mb-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-16.5 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 002.25-2.25V6.75a2.25 2.25 0 00-2.25-2.25H6.75A2.25 2.25 0 004.5 6.75v10.5a2.25 2.25 0 002.25 2.25z" />
                </svg>
                <p>Nenhuma conexão ativa</p>
                <p class="text-sm">Execute exploits bem-sucedidos para estabelecer conexões</p>
            </div>
        `;
        return;
    }
    
    const conexoesHTML = conexoes.map(conexao => `
        <div class="bg-surface-default border border-border-color rounded-lg p-4 hover:border-green-400 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <div>
                        <h4 class="font-semibold text-primary-text">${conexao.nick}</h4>
                        <p class="text-sm text-secondary-text font-mono">${conexao.ip}</p>
                        <p class="text-xs text-blue-400">Expira em ${conexao.tempo_expiracao || '30min'}</p>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="text-right text-sm">
                        <div class="text-secondary-text">BankGuard: <span class="text-orange-400">${conexao.bankguard}</span></div>
                        <div class="text-secondary-text">Dinheiro: <span class="text-green-400">$${conexao.dinheiro.toLocaleString()}</span></div>
                        ${conexao.bruteforce_ativo ? '<div class="text-yellow-400 text-xs">Bruteforce ativo</div>' : ''}
                        ${conexao.banco_liberado ? '<div class="text-green-400 text-xs">Banco liberado</div>' : ''}
                    </div>
                    <div class="flex flex-col gap-2">
                        <button onclick="executarBruteforce('${conexao.ip}')" 
                                class="bg-red-600 hover:bg-red-700 text-white px-3 py-1.5 rounded-lg font-semibold transition-all duration-300 hover:scale-105 text-sm"
                                ${conexao.bruteforce_ativo ? 'disabled style="opacity: 0.5; cursor: not-allowed;"' : ''}>
                            ${conexao.bruteforce_ativo ? 'Executando' : '🔨 Bruteforce'}
                        </button>
                        <button onclick="fecharConexaoEspecifica('${conexao.ip}')" 
                                class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1.5 rounded-lg font-semibold transition-all duration-300 hover:scale-105 text-sm">
                            Fechar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = conexoesHTML;
}

// Executar bruteforce
async function executarBruteforce(alvo_ip) {
    try {
        const button = event.target;
        const originalText = button.innerHTML;
        
        // Mostrar loading
        button.innerHTML = 'Executando...';
        button.disabled = true;
        
        const response = await fetchAPI('/api/terminal/bruteforce', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ alvo_ip })
        });
        
        if (response.sucesso) {
            showNotification(`Bruteforce bem-sucedido! Roubou $${response.quantia_roubada}`, 'success');
            
            // Adicionar ao histórico
            adicionarAoHistorico({
                tipo: 'sucesso',
                alvo_ip,
                quantia: response.quantia_roubada,
                tempo: response.tempo_execucao,
                timestamp: Date.now()
            });
            
            // Recarregar conexões
            await carregarConexoesTerminal();
        } else {
            showNotification(`${response.mensagem}`, 'error');
            
            // Adicionar falha ao histórico
            adicionarAoHistorico({
                tipo: 'falha',
                alvo_ip,
                mensagem: response.mensagem,
                tempo: response.tempo_execucao || 0,
                timestamp: Date.now()
            });
        }
        
        // Restaurar botão
        button.innerHTML = originalText;
        button.disabled = false;
        
    } catch (error) {
        console.error('Erro no bruteforce:', error);
        showNotification('Erro interno do servidor', 'error');
        
        // Restaurar botão
        const button = event.target;
        button.innerHTML = '🔨 Bruteforce';
        button.disabled = false;
    }
}

// Adicionar ao histórico
function adicionarAoHistorico(entrada) {
    const container = document.getElementById('historico-bruteforce');
    const timestamp = new Date(entrada.timestamp).toLocaleTimeString();
    
    let icone, cor, mensagem;
    
    if (entrada.tipo === 'sucesso') {
        icone = '';
        cor = 'text-green-400';
        mensagem = `Bruteforce em ${entrada.alvo_ip} - Roubou $${entrada.quantia.toLocaleString()} em ${entrada.tempo}s`;
    } else {
        icone = '';
        cor = 'text-red-400';
        mensagem = `Bruteforce em ${entrada.alvo_ip} falhou - ${entrada.mensagem}`;
    }
    
    const entradaHTML = `
        <div class="flex items-center gap-3 p-3 bg-surface-hover rounded-lg">
            <span class="text-xl">${icone}</span>
            <div class="flex-1">
                <p class="${cor} font-medium">${mensagem}</p>
                <p class="text-xs text-secondary-text">${timestamp}</p>
            </div>
        </div>
    `;
    
    // Se for a primeira entrada, limpar mensagem padrão
    if (container.children.length === 1 && container.children[0].textContent.includes('Nenhum ataque')) {
        container.innerHTML = '';
    }
    
    container.insertAdjacentHTML('afterbegin', entradaHTML);
    
    // Manter apenas últimas 10 entradas
    while (container.children.length > 10) {
        container.removeChild(container.lastChild);
    }
}

// Tornar função global
window.renderTerminalSection = renderTerminalSection;
window.executarBruteforce = executarBruteforce;
</script>
{% endblock %}
