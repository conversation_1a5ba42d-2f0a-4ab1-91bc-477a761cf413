import React, { useState } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../stores/authStore';
import LoginForm from '../components/auth/LoginForm';
import RegisterForm from '../components/auth/RegisterForm';

const LoginPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const [isLoginMode, setIsLoginMode] = useState(true);

  // Redirecionar se já estiver logado
  if (isAuthenticated && user) {
    return <Navigate to="/game" replace />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      {/* Background com gradiente */}
      <div className="absolute inset-0 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary" />
      
      {/* Efeitos visuais de fundo */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary opacity-10 rounded-full blur-3xl" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent opacity-10 rounded-full blur-3xl" />
      </div>

      {/* Conteúdo principal */}
      <div className="relative z-10 w-full max-w-md">
        {/* Header da página */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <span className="text-blue-500 text-2xl">●</span>
            <span className="font-bold text-3xl text-text-primary">SHACK</span>
            <span className="text-blue-400 text-2xl">WEB</span>
            <span className="text-blue-300 text-2xl">●</span>
          </div>
          <p className="text-text-secondary text-sm">
            Versão React - Sistema de Hacking
          </p>
        </div>

        {/* Formulários */}
        <div className="animate-fadeInUp">
          {isLoginMode ? (
            <LoginForm onSwitchToRegister={() => setIsLoginMode(false)} />
          ) : (
            <RegisterForm onSwitchToLogin={() => setIsLoginMode(true)} />
          )}
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-text-muted text-xs">
          <p>© 2024 SHACK Web Game - Todos os direitos reservados</p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
