// Serviço para APIs do jogo que se conecta ao Flask backend
class GameApiService {
  private baseURL = 'http://localhost:5000';
  
  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };

    const config: RequestInit = {
      ...options,
      credentials: 'include', // Importante para sessões Flask
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      console.log(`🌐 Fazendo requisição para: ${this.baseURL}${endpoint}`);
      
      const response = await fetch(`${this.baseURL}${endpoint}`, config);
      
      console.log(`📡 Resposta recebida: ${response.status} ${response.statusText}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📦 Dados recebidos:', data);
      
      return data;
    } catch (error) {
      console.error(`❌ Erro na requisição para ${endpoint}:`, error);
      throw error;
    }
  }

  // === AUTENTICAÇÃO ===
  async login(credentials: { email: string; password: string }) {
    return this.makeRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(userData: { email: string; password: string; nick: string }) {
    return this.makeRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async checkAuth() {
    return this.makeRequest('/api/auth/check');
  }

  async logout() {
    return this.makeRequest('/api/auth/logout', {
      method: 'POST',
    });
  }

  async verifySession() {
    return this.makeRequest('/api/auth/verify');
  }

  // === MÉTODO DE TESTE RÁPIDO ===
  async quickLogin() {
    try {
      // Tenta fazer login com usuário de teste
      console.log('🔐 Tentando login com usuário de teste...');

      const loginResult = await this.login({
        email: '<EMAIL>',
        password: 'test123'
      });

      if (loginResult.sucesso) {
        console.log('✅ Login realizado com sucesso:', loginResult);
        return loginResult;
      } else {
        // Se login falhou, tenta criar usuário
        console.log('👤 Usuário não existe, criando...');

        const registerResult = await this.register({
          email: '<EMAIL>',
          password: 'test123',
          nick: 'TestPlayer'
        });

        if (registerResult.sucesso) {
          console.log('✅ Usuário criado e logado:', registerResult);
          return registerResult;
        } else {
          throw new Error(registerResult.mensagem || 'Erro ao criar usuário');
        }
      }
    } catch (error) {
      console.error('❌ Erro no login rápido:', error);
      throw error;
    }
  }

  // === SCANNER ===
  async scanTargets() {
    return this.makeRequest('/api/scan');
  }

  async scanSpecificIP(ip: string) {
    return this.makeRequest(`/api/scan/ip/${ip}`);
  }

  // === EXPLOITS ===
  async exploitTarget(ip: string) {
    return this.makeRequest(`/api/alvo/${ip}/exploit`, {
      method: 'POST',
    });
  }

  // === DADOS DO JOGADOR ===
  async getPlayerData() {
    return this.makeRequest('/api/jogador');
  }

  // === CHAT ===
  async getChatMessages() {
    return this.makeRequest('/api/chat/mensagens');
  }

  async sendChatMessage(message: string) {
    return this.makeRequest('/api/chat/enviar', {
      method: 'POST',
      body: JSON.stringify({ mensagem: message }),
    });
  }

  // === TRANSFERÊNCIAS ===
  async transferMoney(targetNick: string, amount: number, description?: string) {
    return this.makeRequest('/api/transferir', {
      method: 'POST',
      body: JSON.stringify({
        destinatario_nick: targetNick,
        valor: amount,
        descricao: description || 'Transferência via React',
      }),
    });
  }

  async forceTransfer(targetUid: string, percentage: number) {
    return this.makeRequest(`/api/alvo/${targetUid}/transferir`, {
      method: 'POST',
      body: JSON.stringify({
        porcentagem: percentage,
      }),
    });
  }

  async getTransferHistory() {
    return this.makeRequest('/api/transferencias/historico');
  }

  // === LOJA ===
  async getShopItems() {
    return this.makeRequest('/api/shop/items');
  }

  async buyShopItem(itemId: string) {
    return this.makeRequest('/api/shop/comprar', {
      method: 'POST',
      body: JSON.stringify({ item_id: itemId }),
    });
  }

  // === UPGRADES ===
  async upgradeItem(item: string, quantity: number = 1) {
    return this.makeRequest('/api/upgrade', {
      method: 'POST',
      body: JSON.stringify({
        item: item,
        quantidade: quantity,
      }),
    });
  }

  async getUpgradeCosts(item: string, currentLevel: number, quantity: number = 1) {
    return this.makeRequest(`/api/upgrade/custo?item=${item}&nivel=${currentLevel}&quantidade=${quantity}`);
  }

  async getUpgradeInfo() {
    return this.makeRequest('/api/upgrade/info');
  }

  // === RANKING ===
  async getRanking() {
    return this.makeRequest('/api/ranking');
  }

  // === LOGS ===
  async getLogs() {
    return this.makeRequest('/api/logs');
  }

  async getActivityLogs(limit: number = 50) {
    return this.makeRequest(`/api/logs/atividades?limit=${limit}`);
  }

  async getAttackLogs() {
    return this.makeRequest('/api/logs/ataques');
  }

  // === ECONOMIA E MINERAÇÃO ===
  async collectResources() {
    return this.makeRequest('/api/mineracao/coletar', {
      method: 'POST',
    });
  }

  async getMiningInfo() {
    return this.makeRequest('/api/mineracao/info');
  }

  async upgradeMining() {
    return this.makeRequest('/api/mineracao/upgrade', {
      method: 'POST',
    });
  }

  // === GRUPOS E TORNEIOS ===
  async getGroupInfo() {
    return this.makeRequest('/api/grupo/info');
  }

  async joinGroup(groupName: string) {
    return this.makeRequest('/api/grupo/entrar', {
      method: 'POST',
      body: JSON.stringify({ nome_grupo: groupName }),
    });
  }

  async leaveGroup() {
    return this.makeRequest('/api/grupo/sair', {
      method: 'POST',
    });
  }

  async getTournamentInfo() {
    return this.makeRequest('/api/torneio/info');
  }

  async getTournamentAppOfDay() {
    return this.makeRequest('/api/torneio/app-do-dia');
  }

  // === MERCADO NEGRO ===
  async getBlackMarketItems() {
    return this.makeRequest('/api/mercado-negro/items');
  }

  async buyBlackMarketItem(itemId: string) {
    return this.makeRequest('/api/mercado-negro/comprar', {
      method: 'POST',
      body: JSON.stringify({ item_id: itemId }),
    });
  }

  async getPlayerRanking(uid: string) {
    return this.makeRequest(`/api/ranking/jogador/${uid}`);
  }

  // === CONEXÕES ATIVAS ===
  async getActiveConnections() {
    return this.makeRequest('/api/conexoes/ativas');
  }

  async disconnectConnection(connectionId: string) {
    return this.makeRequest(`/api/conexoes/${connectionId}/desconectar`, {
      method: 'POST',
    });
  }

  // === MINERAÇÃO ===
  async getMiningStatus() {
    return this.makeRequest('/api/mineracao/status');
  }

  async collectShacks() {
    return this.makeRequest('/api/mineracao/coletar-shacks', {
      method: 'POST',
    });
  }

  async upgradeMiner() {
    return this.makeRequest('/api/mineracao/upgrade', {
      method: 'POST',
    });
  }

  // === DEFACE SYSTEM ===
  async performDeface(targetNick: string) {
    return this.makeRequest('/api/deface', {
      method: 'POST',
      body: JSON.stringify({ alvo_nick: targetNick }),
    });
  }

  async getDefaceRanking() {
    return this.makeRequest('/api/deface/ranking');
  }

  // === HABILIDADES NFT ===
  async getNFTSkills() {
    return this.makeRequest('/api/habilidades/disponiveis');
  }

  async buyNFTSkill(skillId: string) {
    return this.makeRequest('/api/habilidades/comprar', {
      method: 'POST',
      body: JSON.stringify({ habilidade_id: skillId }),
    });
  }

  // === CONFIGURAÇÕES ===
  async changePassword(data: { currentPassword: string; newPassword: string }) {
    return this.makeRequest('/api/auth/change-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateProfile(data: { nick?: string; email?: string }) {
    return this.makeRequest('/api/profile/update', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // === NOTÍCIAS ===
  async getNews() {
    return this.makeRequest('/api/news');
  }

  // === TERMINAL/BRUTEFORCE ===
  async executeBruteforce(target: string, wordlist: string) {
    return this.makeRequest('/api/bruteforce/execute', {
      method: 'POST',
      body: JSON.stringify({ target, wordlist }),
    });
  }

  async getBruteforceStatus() {
    return this.makeRequest('/api/bruteforce/status');
  }

  // === SISTEMA BANCÁRIO ===
  async getBankAccount() {
    return this.makeRequest('/api/banco/conta');
  }

  async bankDeposit(amount: number) {
    return this.makeRequest('/api/banco/depositar', {
      method: 'POST',
      body: JSON.stringify({ valor: amount }),
    });
  }

  async bankWithdraw(amount: number) {
    return this.makeRequest('/api/banco/sacar', {
      method: 'POST',
      body: JSON.stringify({ valor: amount }),
    });
  }

  async getBankTransactions() {
    return this.makeRequest('/api/banco/transacoes');
  }

  async collectBankInterest() {
    return this.makeRequest('/api/banco/coletar-juros', {
      method: 'POST',
    });
  }

  // === TESTE DE CONECTIVIDADE ===
  async testConnection() {
    try {
      const response = await fetch(`${this.baseURL}/`);
      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error('❌ Erro de conectividade:', error);
      return { success: false, error };
    }
  }
}

export const gameApi = new GameApiService();
export default gameApi;
