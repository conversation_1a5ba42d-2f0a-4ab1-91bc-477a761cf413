// Serviço para APIs do jogo que se conecta ao Flask backend
class GameApiService {
  private baseURL = 'http://localhost:5000';
  
  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };

    const config: RequestInit = {
      ...options,
      credentials: 'include', // Importante para sessões Flask
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      console.log(`🌐 Fazendo requisição para: ${this.baseURL}${endpoint}`);
      
      const response = await fetch(`${this.baseURL}${endpoint}`, config);
      
      console.log(`📡 Resposta recebida: ${response.status} ${response.statusText}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📦 Dados recebidos:', data);
      
      return data;
    } catch (error) {
      console.error(`❌ Erro na requisição para ${endpoint}:`, error);
      throw error;
    }
  }

  // === AUTENTICAÇÃO ===
  async login(credentials: { email: string; password: string }) {
    return this.makeRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(userData: { email: string; password: string; nick: string }) {
    return this.makeRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async checkAuth() {
    return this.makeRequest('/api/auth/check');
  }

  // === MÉTODO DE TESTE RÁPIDO ===
  async quickLogin() {
    try {
      // Tenta fazer login com usuário de teste
      console.log('🔐 Tentando login com usuário de teste...');

      const loginResult = await this.login({
        email: '<EMAIL>',
        password: 'test123'
      });

      if (loginResult.sucesso) {
        console.log('✅ Login realizado com sucesso:', loginResult);
        return loginResult;
      } else {
        // Se login falhou, tenta criar usuário
        console.log('👤 Usuário não existe, criando...');

        const registerResult = await this.register({
          email: '<EMAIL>',
          password: 'test123',
          nick: 'TestPlayer'
        });

        if (registerResult.sucesso) {
          console.log('✅ Usuário criado e logado:', registerResult);
          return registerResult;
        } else {
          throw new Error(registerResult.mensagem || 'Erro ao criar usuário');
        }
      }
    } catch (error) {
      console.error('❌ Erro no login rápido:', error);
      throw error;
    }
  }

  // === SCANNER ===
  async scanTargets() {
    return this.makeRequest('/api/scan');
  }

  async scanSpecificIP(ip: string) {
    return this.makeRequest(`/api/scan/ip/${ip}`);
  }

  // === EXPLOITS ===
  async exploitTarget(ip: string) {
    return this.makeRequest(`/api/alvo/${ip}/exploit`, {
      method: 'POST',
    });
  }

  // === DADOS DO JOGADOR ===
  async getPlayerData() {
    return this.makeRequest('/api/jogador');
  }

  // === CHAT ===
  async getChatMessages() {
    return this.makeRequest('/api/chat/mensagens');
  }

  async sendChatMessage(message: string) {
    return this.makeRequest('/api/chat/enviar', {
      method: 'POST',
      body: JSON.stringify({ mensagem: message }),
    });
  }

  // === TRANSFERÊNCIAS ===
  async transferPoints(targetNick: string, amount: number) {
    return this.makeRequest('/api/transferir', {
      method: 'POST',
      body: JSON.stringify({
        alvo_nick: targetNick,
        quantidade: amount,
      }),
    });
  }

  // === LOJA ===
  async getShopItems() {
    return this.makeRequest('/api/shop/items');
  }

  async buyShopItem(itemId: string) {
    return this.makeRequest('/api/shop/comprar', {
      method: 'POST',
      body: JSON.stringify({ item_id: itemId }),
    });
  }

  // === UPGRADES ===
  async upgradeItem(item: string, quantity: number = 1) {
    return this.makeRequest('/api/upgrade', {
      method: 'POST',
      body: JSON.stringify({
        item: item,
        quantidade: quantity,
      }),
    });
  }

  // === RANKING ===
  async getRanking() {
    return this.makeRequest('/api/ranking');
  }

  // === LOGS ===
  async getLogs() {
    return this.makeRequest('/api/logs');
  }

  // === TESTE DE CONECTIVIDADE ===
  async testConnection() {
    try {
      const response = await fetch(`${this.baseURL}/`);
      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error('❌ Erro de conectividade:', error);
      return { success: false, error };
    }
  }
}

export const gameApi = new GameApiService();
export default gameApi;
