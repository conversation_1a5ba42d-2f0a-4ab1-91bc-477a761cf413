import React from 'react';
import { useHackGameStore } from '../stores/hackGameStore';
import { GAME_APPS } from '../types/game';

const GameHomePage: React.FC = () => {
  const { 
    player, 
    playerApps, 
    currentScreen, 
    setCurrentScreen,
    notifications 
  } = useHackGameStore();

  if (!player) {
    return (
      <div className="flex items-center justify-center h-full text-white">
        <div className="text-center">
          <div className="text-4xl mb-4">📱</div>
          <p>Carregando...</p>
        </div>
      </div>
    );
  }

  const unreadNotifications = notifications.filter(n => !n.read).length;

  // Calcular total de níveis dos apps
  const totalAppLevels = Object.values(playerApps).reduce((sum, level) => sum + level, 0);
  const maxAppLevels = Object.keys(playerApps).length * 10; // 7 apps * 10 níveis máximos

  return (
    <div className="h-full bg-gradient-to-br from-gray-900 to-black text-white flex flex-col">
      {/* Header com informações do player */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <div>
            <h1 className="text-lg font-bold">{player.nick}</h1>
            <p className="text-xs text-gray-400 font-mono">{player.ip}</p>
          </div>
          <div className="text-right">
            <div className="text-sm font-semibold text-blue-400">Nível {player.level}</div>
            <div className="text-xs text-green-400">${player.cash.toLocaleString()}</div>
          </div>
        </div>
        
        {/* Barra de XP */}
        <div className="mt-2">
          <div className="flex justify-between text-xs text-gray-400 mb-1">
            <span>XP: {player.xp}</span>
            <span>{player.xpToNextLevel} para próximo nível</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500"
              style={{ 
                width: `${Math.max(10, 100 - (player.xpToNextLevel / (player.xp + player.xpToNextLevel)) * 100)}%` 
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Grid de aplicativos */}
      <div className="flex-1 p-4">
        <div className="grid grid-cols-3 gap-4">
          {/* Upgrades */}
          <button
            onClick={() => setCurrentScreen('apps')}
            className="flex flex-col items-center p-3 rounded-2xl bg-gradient-to-br from-purple-600/20 to-blue-600/20 border border-purple-500/30 hover:from-purple-600/30 hover:to-blue-600/30 transition-all duration-200 hover:scale-105 active:scale-95 relative"
          >
            <div className="text-3xl mb-2">⚙️</div>
            <span className="text-xs font-medium text-center leading-tight">Upgrades</span>
            <div className="flex items-center space-x-1 mt-1">
              <span className="text-xs text-purple-400">{totalAppLevels}</span>
              <span className="text-xs text-gray-500">/</span>
              <span className="text-xs text-gray-500">{maxAppLevels}</span>
            </div>
            {/* Indicador de progresso */}
            <div className="absolute -top-1 -right-1 w-5 h-5 bg-purple-600 rounded-full flex items-center justify-center border-2 border-gray-900">
              <span className="text-xs font-bold text-white">{Math.floor((totalAppLevels / maxAppLevels) * 100)}</span>
            </div>
          </button>

          {/* Scanner */}
          <button
            onClick={() => setCurrentScreen('scanner')}
            className="flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95"
          >
            <div className="text-3xl mb-2">🔍</div>
            <span className="text-xs font-medium text-center leading-tight">Scanner</span>
            <span className="text-xs text-gray-400">Rede</span>
          </button>

          {/* Terminal */}
          <button
            onClick={() => setCurrentScreen('terminal')}
            className="flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95"
          >
            <div className="text-3xl mb-2">💻</div>
            <span className="text-xs font-medium text-center leading-tight">Terminal</span>
            <span className="text-xs text-gray-400">Hack</span>
          </button>

          {/* Perfil */}
          <button
            onClick={() => setCurrentScreen('profile')}
            className="flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95"
          >
            <div className="text-3xl mb-2">👤</div>
            <span className="text-xs font-medium text-center leading-tight">Perfil</span>
            <span className="text-xs text-gray-400">Stats</span>
          </button>

          {/* Configurações */}
          <button
            onClick={() => setCurrentScreen('settings')}
            className="flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95"
          >
            <div className="text-3xl mb-2">⚙️</div>
            <span className="text-xs font-medium text-center leading-tight">Config</span>
            <span className="text-xs text-gray-400">Sistema</span>
          </button>

          {/* Loja */}
          <button
            onClick={() => setCurrentScreen('shop')}
            className="flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95"
          >
            <div className="text-3xl mb-2">🛒</div>
            <span className="text-xs font-medium text-center leading-tight">Loja</span>
            <span className="text-xs text-gray-400">Compras</span>
          </button>

          {/* Ranking */}
          <button
            onClick={() => setCurrentScreen('ranking')}
            className="flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95"
          >
            <div className="text-3xl mb-2">🏆</div>
            <span className="text-xs font-medium text-center leading-tight">Ranking</span>
            <span className="text-xs text-gray-400">Top</span>
          </button>

          {/* Logs */}
          <button
            onClick={() => setCurrentScreen('logs')}
            className="flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95"
          >
            <div className="text-3xl mb-2">📊</div>
            <span className="text-xs font-medium text-center leading-tight">Logs</span>
            <span className="text-xs text-gray-400">Histórico</span>
          </button>

          {/* Chat */}
          <button
            onClick={() => setCurrentScreen('chat')}
            className="flex flex-col items-center p-3 rounded-2xl bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 hover:scale-105 active:scale-95"
          >
            <div className="text-3xl mb-2">💬</div>
            <span className="text-xs font-medium text-center leading-tight">Chat</span>
            <span className="text-xs text-gray-400">Global</span>
          </button>
        </div>

        {/* Notificações recentes */}
        {unreadNotifications > 0 && (
          <div className="mt-6 p-3 bg-blue-900/30 border border-blue-700/50 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Notificações</span>
              </div>
              <span className="text-xs bg-blue-600 px-2 py-1 rounded-full">{unreadNotifications}</span>
            </div>
            <p className="text-xs text-gray-300 mt-1">
              {notifications.find(n => !n.read)?.message || 'Novas atualizações disponíveis'}
            </p>
          </div>
        )}
      </div>

      {/* Footer com informações do sistema */}
      <div className="px-4 py-2 border-t border-gray-700">
        <div className="flex justify-between items-center text-xs text-gray-400">
          <span>SHACK OS v2.1</span>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>Online</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameHomePage;
