{"ast": null, "code": "var _s = $RefreshSig$();\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nexport const useSimpleAuthStore = create()(persist((set, get) => ({\n  // Estado inicial\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n  // Login com API mock\n  login: async credentials => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      console.log('SimpleAuth - Fazendo login...');\n      const response = await mockApiService.login(credentials);\n      if (response.sucesso && response.user && response.token) {\n        set({\n          user: response.user,\n          token: response.token,\n          isAuthenticated: true,\n          isLoading: false,\n          error: null\n        });\n        console.log('SimpleAuth - Login realizado com sucesso!');\n      } else {\n        set({\n          isLoading: false,\n          error: response.mensagem || 'Erro no login'\n        });\n      }\n    } catch (error) {\n      console.error('SimpleAuth - Erro no login:', error);\n      set({\n        isLoading: false,\n        error: 'Erro de conexão'\n      });\n    }\n  },\n  // Registro com API mock\n  register: async userData => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      console.log('SimpleAuth - Criando conta...');\n      const response = await mockApiService.register(userData);\n      if (response.sucesso && response.user && response.token) {\n        set({\n          user: response.user,\n          token: response.token,\n          isAuthenticated: true,\n          isLoading: false,\n          error: null\n        });\n        console.log('SimpleAuth - Conta criada com sucesso!');\n      } else {\n        set({\n          isLoading: false,\n          error: response.mensagem || 'Erro no registro'\n        });\n      }\n    } catch (error) {\n      console.error('SimpleAuth - Erro no registro:', error);\n      set({\n        isLoading: false,\n        error: 'Erro de conexão'\n      });\n    }\n  },\n  // Logout\n  logout: () => {\n    console.log('SimpleAuth - Fazendo logout');\n    set({\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null\n    });\n  },\n  // Verificar autenticação\n  checkAuth: async () => {\n    const {\n      token,\n      isLoading\n    } = get();\n    if (isLoading) {\n      console.log('SimpleAuth - Verificação já em andamento');\n      return;\n    }\n    if (!token) {\n      console.log('SimpleAuth - Sem token');\n      set({\n        isAuthenticated: false,\n        isLoading: false\n      });\n      return;\n    }\n    console.log('SimpleAuth - Verificando token...');\n    set({\n      isLoading: true\n    });\n    try {\n      const isValid = await mockApiService.verifyToken();\n      if (isValid) {\n        console.log('SimpleAuth - Token válido');\n        set({\n          isAuthenticated: true,\n          isLoading: false\n        });\n      } else {\n        console.log('SimpleAuth - Token inválido');\n        get().logout();\n      }\n    } catch (error) {\n      console.error('SimpleAuth - Erro na verificação:', error);\n      get().logout();\n    }\n  },\n  // Limpar erro\n  clearError: () => {\n    set({\n      error: null\n    });\n  },\n  // Simulação de login para teste\n  simulateLogin: () => {\n    console.log('SimpleAuth - Simulando login...');\n    set({\n      isLoading: true\n    });\n\n    // Simular delay de API\n    setTimeout(() => {\n      const mockUser = {\n        uid: 'test-user-123',\n        nick: 'TestPlayer',\n        email: '<EMAIL>'\n      };\n      const mockToken = 'mock-jwt-token-123';\n      set({\n        user: mockUser,\n        token: mockToken,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      });\n      console.log('SimpleAuth - Login simulado com sucesso!');\n    }, 1000);\n  }\n}), {\n  name: 'simple-auth-storage',\n  partialize: state => ({\n    user: state.user,\n    token: state.token,\n    isAuthenticated: state.isAuthenticated\n  })\n}));\n\n// Hook simplificado\nexport const useSimpleAuth = () => {\n  _s();\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    register,\n    logout,\n    clearError,\n    checkAuth,\n    simulateLogin\n  } = useSimpleAuthStore();\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    // Ações\n    login,\n    register,\n    logout,\n    clearError,\n    checkAuth,\n    simulateLogin,\n    // Computed\n    isLoggedIn: isAuthenticated && !!user\n  };\n};\n_s(useSimpleAuth, \"36Bk4VLrS/KDxHWPVaagtmOhgfc=\", false, function () {\n  return [useSimpleAuthStore];\n});", "map": {"version": 3, "names": ["create", "persist", "useSimpleAuthStore", "set", "get", "user", "token", "isAuthenticated", "isLoading", "error", "login", "credentials", "console", "log", "response", "mockApiService", "sucesso", "mensagem", "register", "userData", "logout", "checkAuth", "<PERSON><PERSON><PERSON><PERSON>", "verifyToken", "clearError", "simulateLogin", "setTimeout", "mockUser", "uid", "nick", "email", "mockToken", "name", "partialize", "state", "useSimpleAuth", "_s", "isLoggedIn"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/simpleAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport backendService from '../services/backendService';\n\nexport interface User {\n  uid: string;\n  nick: string;\n  email: string;\n}\n\ninterface SimpleAuthState {\n  // Estado\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n\n  // Ações com API mock\n  login: (credentials: { email: string; password: string }) => Promise<void>;\n  register: (userData: { email: string; password: string; nick: string }) => Promise<void>;\n  logout: () => void;\n  clearError: () => void;\n  checkAuth: () => Promise<void>;\n\n  // Simulação de login para teste\n  simulateLogin: () => void;\n}\n\nexport const useSimpleAuthStore = create<SimpleAuthState>()(\n  persist(\n    (set, get) => ({\n      // Estado inicial\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // Login com API mock\n      login: async (credentials) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          console.log('SimpleAuth - Fazendo login...');\n          const response = await mockApiService.login(credentials);\n\n          if (response.sucesso && response.user && response.token) {\n            set({\n              user: response.user,\n              token: response.token,\n              isAuthenticated: true,\n              isLoading: false,\n              error: null,\n            });\n            console.log('SimpleAuth - Login realizado com sucesso!');\n          } else {\n            set({\n              isLoading: false,\n              error: response.mensagem || 'Erro no login',\n            });\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro no login:', error);\n          set({\n            isLoading: false,\n            error: 'Erro de conexão',\n          });\n        }\n      },\n\n      // Registro com API mock\n      register: async (userData) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          console.log('SimpleAuth - Criando conta...');\n          const response = await mockApiService.register(userData);\n\n          if (response.sucesso && response.user && response.token) {\n            set({\n              user: response.user,\n              token: response.token,\n              isAuthenticated: true,\n              isLoading: false,\n              error: null,\n            });\n            console.log('SimpleAuth - Conta criada com sucesso!');\n          } else {\n            set({\n              isLoading: false,\n              error: response.mensagem || 'Erro no registro',\n            });\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro no registro:', error);\n          set({\n            isLoading: false,\n            error: 'Erro de conexão',\n          });\n        }\n      },\n\n      // Logout\n      logout: () => {\n        console.log('SimpleAuth - Fazendo logout');\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: null,\n        });\n      },\n\n      // Verificar autenticação\n      checkAuth: async () => {\n        const { token, isLoading } = get();\n\n        if (isLoading) {\n          console.log('SimpleAuth - Verificação já em andamento');\n          return;\n        }\n\n        if (!token) {\n          console.log('SimpleAuth - Sem token');\n          set({ isAuthenticated: false, isLoading: false });\n          return;\n        }\n\n        console.log('SimpleAuth - Verificando token...');\n        set({ isLoading: true });\n\n        try {\n          const isValid = await mockApiService.verifyToken();\n\n          if (isValid) {\n            console.log('SimpleAuth - Token válido');\n            set({ isAuthenticated: true, isLoading: false });\n          } else {\n            console.log('SimpleAuth - Token inválido');\n            get().logout();\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro na verificação:', error);\n          get().logout();\n        }\n      },\n\n      // Limpar erro\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // Simulação de login para teste\n      simulateLogin: () => {\n        console.log('SimpleAuth - Simulando login...');\n        set({ isLoading: true });\n        \n        // Simular delay de API\n        setTimeout(() => {\n          const mockUser: User = {\n            uid: 'test-user-123',\n            nick: 'TestPlayer',\n            email: '<EMAIL>'\n          };\n          \n          const mockToken = 'mock-jwt-token-123';\n          \n          set({\n            user: mockUser,\n            token: mockToken,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n          \n          console.log('SimpleAuth - Login simulado com sucesso!');\n        }, 1000);\n      },\n    }),\n    {\n      name: 'simple-auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// Hook simplificado\nexport const useSimpleAuth = () => {\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    register,\n    logout,\n    clearError,\n    checkAuth,\n    simulateLogin,\n  } = useSimpleAuthStore();\n\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n\n    // Ações\n    login,\n    register,\n    logout,\n    clearError,\n    checkAuth,\n    simulateLogin,\n\n    // Computed\n    isLoggedIn: isAuthenticated && !!user,\n  };\n};\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AA4B5C,OAAO,MAAMC,kBAAkB,GAAGF,MAAM,CAAkB,CAAC,CACzDC,OAAO,CACL,CAACE,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEX;EACAC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5BR,GAAG,CAAC;MAAEK,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACFG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,MAAMC,QAAQ,GAAG,MAAMC,cAAc,CAACL,KAAK,CAACC,WAAW,CAAC;MAExD,IAAIG,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACT,IAAI,IAAIS,QAAQ,CAACR,KAAK,EAAE;QACvDH,GAAG,CAAC;UACFE,IAAI,EAAES,QAAQ,CAACT,IAAI;UACnBC,KAAK,EAAEQ,QAAQ,CAACR,KAAK;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;QACFG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAC1D,CAAC,MAAM;QACLV,GAAG,CAAC;UACFK,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAEK,QAAQ,CAACG,QAAQ,IAAI;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDN,GAAG,CAAC;QACFK,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAS,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5BhB,GAAG,CAAC;MAAEK,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACFG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,MAAMC,QAAQ,GAAG,MAAMC,cAAc,CAACG,QAAQ,CAACC,QAAQ,CAAC;MAExD,IAAIL,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACT,IAAI,IAAIS,QAAQ,CAACR,KAAK,EAAE;QACvDH,GAAG,CAAC;UACFE,IAAI,EAAES,QAAQ,CAACT,IAAI;UACnBC,KAAK,EAAEQ,QAAQ,CAACR,KAAK;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;QACFG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD,CAAC,MAAM;QACLV,GAAG,CAAC;UACFK,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAEK,QAAQ,CAACG,QAAQ,IAAI;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDN,GAAG,CAAC;QACFK,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAW,MAAM,EAAEA,CAAA,KAAM;IACZR,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1CV,GAAG,CAAC;MACFE,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE,KAAK;MACtBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED;EACAY,SAAS,EAAE,MAAAA,CAAA,KAAY;IACrB,MAAM;MAAEf,KAAK;MAAEE;IAAU,CAAC,GAAGJ,GAAG,CAAC,CAAC;IAElC,IAAII,SAAS,EAAE;MACbI,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD;IACF;IAEA,IAAI,CAACP,KAAK,EAAE;MACVM,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrCV,GAAG,CAAC;QAAEI,eAAe,EAAE,KAAK;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MACjD;IACF;IAEAI,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDV,GAAG,CAAC;MAAEK,SAAS,EAAE;IAAK,CAAC,CAAC;IAExB,IAAI;MACF,MAAMc,OAAO,GAAG,MAAMP,cAAc,CAACQ,WAAW,CAAC,CAAC;MAElD,IAAID,OAAO,EAAE;QACXV,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCV,GAAG,CAAC;UAAEI,eAAe,EAAE,IAAI;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC;MAClD,CAAC,MAAM;QACLI,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CT,GAAG,CAAC,CAAC,CAACgB,MAAM,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDL,GAAG,CAAC,CAAC,CAACgB,MAAM,CAAC,CAAC;IAChB;EACF,CAAC;EAED;EACAI,UAAU,EAAEA,CAAA,KAAM;IAChBrB,GAAG,CAAC;MAAEM,KAAK,EAAE;IAAK,CAAC,CAAC;EACtB,CAAC;EAED;EACAgB,aAAa,EAAEA,CAAA,KAAM;IACnBb,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CV,GAAG,CAAC;MAAEK,SAAS,EAAE;IAAK,CAAC,CAAC;;IAExB;IACAkB,UAAU,CAAC,MAAM;MACf,MAAMC,QAAc,GAAG;QACrBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE;MACT,CAAC;MAED,MAAMC,SAAS,GAAG,oBAAoB;MAEtC5B,GAAG,CAAC;QACFE,IAAI,EAAEsB,QAAQ;QACdrB,KAAK,EAAEyB,SAAS;QAChBxB,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;MAEFG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC,CAAC,EACF;EACEmB,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAGC,KAAK,KAAM;IACtB7B,IAAI,EAAE6B,KAAK,CAAC7B,IAAI;IAChBC,KAAK,EAAE4B,KAAK,CAAC5B,KAAK;IAClBC,eAAe,EAAE2B,KAAK,CAAC3B;EACzB,CAAC;AACH,CACF,CACF,CAAC;;AAED;AACA,OAAO,MAAM4B,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IACJ/B,IAAI;IACJC,KAAK;IACLC,eAAe;IACfC,SAAS;IACTC,KAAK;IACLC,KAAK;IACLQ,QAAQ;IACRE,MAAM;IACNI,UAAU;IACVH,SAAS;IACTI;EACF,CAAC,GAAGvB,kBAAkB,CAAC,CAAC;EAExB,OAAO;IACL;IACAG,IAAI;IACJC,KAAK;IACLC,eAAe;IACfC,SAAS;IACTC,KAAK;IAEL;IACAC,KAAK;IACLQ,QAAQ;IACRE,MAAM;IACNI,UAAU;IACVH,SAAS;IACTI,aAAa;IAEb;IACAY,UAAU,EAAE9B,eAAe,IAAI,CAAC,CAACF;EACnC,CAAC;AACH,CAAC;AAAC+B,EAAA,CAlCWD,aAAa;EAAA,QAapBjC,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}