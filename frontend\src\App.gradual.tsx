import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import './styles/globals.css';

// Componente de teste simples
const TestPage = ({ title, color }: { title: string; color: string }) => (
  <div className="min-h-screen bg-bg-primary text-text-primary p-8">
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <Link to="/" className="text-accent-blue hover:text-primary-light">
          ← Voltar ao início
        </Link>
      </div>
      
      <div className={`card border-${color}-500`}>
        <h1 className="text-3xl font-bold mb-4">{title}</h1>
        <p className="text-text-secondary">
          Esta é a página de teste: {title}
        </p>
      </div>
    </div>
  </div>
);

// Página inicial
const HomePage = () => (
  <div className="min-h-screen bg-bg-primary text-text-primary p-8">
    <div className="max-w-4xl mx-auto">
      <h1 className="text-4xl font-bold text-center mb-8">
        🎮 SHACK Web Game - Teste Gradual
      </h1>
      
      <div className="card text-center">
        <h2 className="text-2xl font-semibold mb-6">
          React Router Funcionando!
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Link to="/login" className="card bg-blue-900 border-blue-500 hover:bg-blue-800 transition-colors">
            <h3 className="text-lg font-semibold text-blue-100 mb-2">
              🔐 Teste Login
            </h3>
            <p className="text-blue-200 text-sm">
              Testar página de login
            </p>
          </Link>
          
          <Link to="/game" className="card bg-green-900 border-green-500 hover:bg-green-800 transition-colors">
            <h3 className="text-lg font-semibold text-green-100 mb-2">
              🎮 Teste Game
            </h3>
            <p className="text-green-200 text-sm">
              Testar página do jogo
            </p>
          </Link>
        </div>
        
        <div className="text-text-muted">
          <p>✅ React funcionando</p>
          <p>✅ React Router funcionando</p>
          <p>✅ Tailwind CSS funcionando</p>
        </div>
      </div>
    </div>
  </div>
);

function App() {
  console.log('App Gradual - Renderizando...');
  
  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/login" element={<TestPage title="Página de Login" color="blue" />} />
        <Route path="/game" element={<TestPage title="Página do Jogo" color="green" />} />
        <Route path="*" element={<TestPage title="Página 404" color="red" />} />
      </Routes>
    </Router>
  );
}

export default App;
