import React from 'react';
import { ChatMessage as ChatMessageType } from '../../stores/chatStore';
import { useSimpleAuth } from '../../stores/simpleAuthStore';

interface ChatMessageProps {
  message: ChatMessageType;
  isOwn?: boolean;
  showAvatar?: boolean;
  showTimestamp?: boolean;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  isOwn = false,
  showAvatar = true,
  showTimestamp = true,
}) => {
  const { user } = useSimpleAuth();

  // Verificar se a mensagem é do usuário atual
  const isOwnMessage = message.usuario === user?.nick;
  
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getAvatarColor = (nick: string) => {
    // Gerar cor baseada no nick
    const colors = [
      'bg-red-500',
      'bg-blue-500',
      'bg-green-500',
      'bg-yellow-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-teal-500',
    ];
    
    let hash = 0;
    for (let i = 0; i < nick.length; i++) {
      hash = nick.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  };

  return (
    <div className={`flex items-start space-x-3 p-3 hover:bg-bg-tertiary transition-colors ${
      isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''
    }`}>
      {/* Avatar */}
      {showAvatar && (
        <div className={`
          flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold
          ${getAvatarColor(message.usuario)}
        `}>
          {message.usuario.charAt(0).toUpperCase()}
        </div>
      )}

      {/* Conteúdo da mensagem */}
      <div className={`flex-1 min-w-0 ${isOwnMessage ? 'text-right' : ''}`}>
        {/* Header com nick e timestamp */}
        <div className={`flex items-center space-x-2 mb-1 ${
          isOwnMessage ? 'justify-end' : 'justify-start'
        }`}>
          <span className={`text-sm font-medium ${
            isOwnMessage ? 'text-accent-blue' : 'text-text-primary'
          }`}>
            {isOwnMessage ? 'Você' : message.usuario}
          </span>
          
          {showTimestamp && (
            <span className="text-xs text-text-muted">
              {formatTime(message.timestamp)}
            </span>
          )}
        </div>

        {/* Mensagem */}
        <div className={`
          inline-block max-w-xs md:max-w-md lg:max-w-lg xl:max-w-xl
          px-3 py-2 rounded-lg text-sm break-words
          ${isOwnMessage
            ? 'bg-accent-blue text-white rounded-br-sm'
            : 'bg-bg-secondary text-text-primary rounded-bl-sm border border-border-color'
          }
        `}>
          {message.mensagem}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
