-- Schema para tabela de transferências entre jogadores
-- Execute este SQL no Supabase

-- Tabela de transferências entre jogadores
CREATE TABLE IF NOT EXISTS transferencias (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- <PERSON><PERSON> da transferência
    remetente_uid VARCHAR(255) NOT NULL,
    remetente_nick VARCHAR(100) NOT NULL,
    remetente_ip VARCHAR(15) NOT NULL,
    
    destinatario_uid VARCHAR(255) NOT NULL,
    destinatario_nick VARCHAR(100) NOT NULL,
    destinatario_ip VARCHAR(15) NOT NULL,
    
    valor BIGINT NOT NULL,
    descricao TEXT,
    
    -- Tipo de transferência
    tipo VARCHAR(50) DEFAULT 'transferencia' CHECK (tipo IN ('transferencia', 'pagamento', 'premio', 'taxa')),
    
    -- Status da transferência
    status VARCHAR(20) DEFAULT 'concluida' CHECK (status IN ('pendente', 'concluida', 'cancelada', 'falhada')),
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_transferencias_remetente ON transferencias(remetente_uid);
CREATE INDEX IF NOT EXISTS idx_transferencias_destinatario ON transferencias(destinatario_uid);
CREATE INDEX IF NOT EXISTS idx_transferencias_created_at ON transferencias(created_at);
CREATE INDEX IF NOT EXISTS idx_transferencias_status ON transferencias(status);

-- Foreign keys
ALTER TABLE transferencias 
ADD CONSTRAINT IF NOT EXISTS fk_transferencias_remetente 
FOREIGN KEY (remetente_uid) REFERENCES usuarios(uid) ON DELETE CASCADE;

ALTER TABLE transferencias 
ADD CONSTRAINT IF NOT EXISTS fk_transferencias_destinatario 
FOREIGN KEY (destinatario_uid) REFERENCES usuarios(uid) ON DELETE CASCADE;

-- Trigger para updated_at
CREATE TRIGGER IF NOT EXISTS update_transferencias_updated_at 
BEFORE UPDATE ON transferencias
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
