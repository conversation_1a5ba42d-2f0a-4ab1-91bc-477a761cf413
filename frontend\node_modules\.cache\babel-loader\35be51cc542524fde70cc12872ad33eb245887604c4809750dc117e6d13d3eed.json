{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleGamePage.tsx\",\n  _s = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useChat } from '../stores/chatStore';\nimport gameApi from '../services/gameApi';\nimport GameMainPage from './GameMainPage';\nimport TransferPage from './TransferPage';\nimport UpgradePage from './UpgradePage';\nimport ShopPage from './ShopPage';\nimport RankingPage from './RankingPage';\nimport LogsPage from './LogsPage';\nimport ConfigPage from './ConfigPage';\nimport MiningPage from './MiningPage';\nimport TerminalPage from './TerminalPage';\nimport BankPage from './BankPage';\nimport GameFooter from '../components/common/GameFooter';\n\n// Componente do Dashboard Simplificado\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleDashboard = () => {\n  _s();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    loadPlayerData,\n    hasPlayerData\n  } = usePlayer();\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold cyber-text\",\n            children: \"Terminal Principal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-text-muted\",\n            children: [\"Operador: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-cyber-primary\",\n              children: (user === null || user === void 0 ? void 0 : user.nick) || 'Jogador'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), isLoadingPlayer && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 cyber-text\",\n          children: \"\\uD83D\\uDCCA Status do Sistema\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-blue-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-blue-400\",\n                children: isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-blue-300\",\n                children: \"PONTOS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-green-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-green-400\",\n                children: isLoadingPlayer ? '...' : playerData.nivel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-green-300\",\n                children: \"N\\xCDVEL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-purple-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-purple-400\",\n                children: isLoadingPlayer ? '...' : playerData.conquistas\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-purple-300\",\n                children: \"CONQUISTAS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-orange-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-orange-400\",\n                children: isLoadingPlayer ? '...' : playerData.ranking\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-orange-300\",\n                children: \"RANKING\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 cyber-text\",\n          children: \"\\u26A1 Acesso R\\xE1pido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.href = '/game/scanner',\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.href = '/game/chat',\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Loja\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Config\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 cyber-text\",\n          children: \"\\uD83D\\uDCC8 Log do Sistema\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-sm\",\n                  children: \"+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Pontos ganhos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-text-muted\",\n                  children: \"02:15:33\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-green-400 font-bold text-sm\",\n              children: \"+150\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-sm\",\n                  children: \"\\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Nova conquista\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-text-muted\",\n                  children: \"01:22:15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-400 font-bold text-sm\",\n              children: \"HACK\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-sm\",\n                  children: \"\\u2191\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Level UP\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-text-muted\",\n                  children: \"00:45:22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-purple-400 font-bold text-sm\",\n              children: \"LV.15\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n\n// Componente Scanner Completo\n_s(SimpleDashboard, \"4ZBWbfZBu+lAD/wW1WhGuHvBS6I=\", false, function () {\n  return [useSimpleAuth, usePlayer];\n});\n_c = SimpleDashboard;\nconst SimpleScanner = () => {\n  _s3();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer\n  } = usePlayer();\n  const [isScanning, setIsScanning] = React.useState(false);\n  const [scanTargets, setScanTargets] = React.useState([]);\n  const [scanError, setScanError] = React.useState(null);\n  const [specificIP, setSpecificIP] = React.useState('');\n  const [isAdvancedScan, setIsAdvancedScan] = React.useState(false);\n  const [connectionStatus, setConnectionStatus] = React.useState('Não testado');\n  const [loginStatus, setLoginStatus] = React.useState('Não logado');\n  const handleQuickScan = async () => {\n    setIsScanning(true);\n    setScanError(null);\n    try {\n      console.log('🔍 Iniciando scan rápido...');\n      const data = await gameApi.scanTargets();\n      if (data.sucesso && data.alvos) {\n        setScanTargets(data.alvos);\n        console.log('✅ Scan realizado com sucesso:', data.alvos);\n      } else {\n        setScanError(data.mensagem || 'Erro ao escanear alvos');\n        console.log('❌ Erro no scan:', data.mensagem);\n      }\n    } catch (error) {\n      console.error('❌ Erro no scan:', error);\n      setScanError(error.message || 'Erro de conexão com o servidor');\n    } finally {\n      setIsScanning(false);\n    }\n  };\n  const handleAdvancedScan = async () => {\n    if (!specificIP.trim()) {\n      setScanError('Digite um IP válido');\n      return;\n    }\n    setIsAdvancedScan(true);\n    setScanError(null);\n    try {\n      console.log(`🎯 Iniciando scan avançado para IP: ${specificIP}`);\n      const data = await gameApi.scanSpecificIP(specificIP);\n      if (data.sucesso && data.alvo) {\n        setScanTargets([data.alvo]);\n        console.log('✅ Scan avançado realizado:', data.alvo);\n      } else {\n        setScanError(data.mensagem || 'IP não encontrado');\n        console.log('❌ IP não encontrado:', data.mensagem);\n      }\n    } catch (error) {\n      console.error('❌ Erro no scan avançado:', error);\n      setScanError(error.message || 'Erro de conexão com o servidor');\n    } finally {\n      setIsAdvancedScan(false);\n    }\n  };\n  const handleExploit = async target => {\n    try {\n      console.log(`⚔️ Iniciando exploit contra: ${target.nick} (${target.ip})`);\n      const data = await gameApi.exploitTarget(target.ip);\n      if (data.sucesso) {\n        console.log('✅ Exploit realizado com sucesso:', data);\n\n        // Mostrar opções de transferência se o exploit foi bem-sucedido\n        if (data.conexao_criada) {\n          var _target$dinheiro;\n          const transferAmount = prompt(`Exploit bem-sucedido em ${target.nick}!\\n\\n` + `Dinheiro disponível: $${((_target$dinheiro = target.dinheiro) === null || _target$dinheiro === void 0 ? void 0 : _target$dinheiro.toLocaleString()) || '0'}\\n\\n` + `Digite a porcentagem para transferir (20-80%):`);\n          if (transferAmount) {\n            const percentage = parseInt(transferAmount);\n            if (percentage >= 20 && percentage <= 80) {\n              try {\n                const transferResponse = await gameApi.forceTransfer(target.uid || target.ip, percentage);\n                if (transferResponse.sucesso) {\n                  alert(`Transferência realizada! ${transferResponse.mensagem}`);\n                } else {\n                  alert(`Erro na transferência: ${transferResponse.mensagem}`);\n                }\n              } catch (error) {\n                alert('Erro na transferência');\n              }\n            } else {\n              alert('Porcentagem deve estar entre 20% e 80%');\n            }\n          }\n        } else {\n          alert(`✅ Exploit bem-sucedido! ${data.mensagem}`);\n        }\n\n        // Recarregar lista de alvos\n        handleQuickScan();\n      } else {\n        alert(`❌ Exploit falhou: ${data.mensagem}`);\n        console.log('❌ Exploit falhou:', data.mensagem);\n      }\n    } catch (error) {\n      console.error('❌ Erro no exploit:', error);\n      alert(`❌ Erro no exploit: ${error.message}`);\n    }\n  };\n  const testConnection = async () => {\n    try {\n      setConnectionStatus('Testando...');\n      const result = await gameApi.testConnection();\n      if (result.success) {\n        setConnectionStatus('✅ Conectado');\n        console.log('✅ Conexão com backend OK:', result.data);\n      } else {\n        setConnectionStatus('❌ Erro de conexão');\n        console.log('❌ Erro de conexão:', result.error);\n      }\n    } catch (error) {\n      setConnectionStatus('❌ Falha na conexão');\n      console.error('❌ Falha no teste de conexão:', error);\n    }\n  };\n  const quickLogin = async () => {\n    try {\n      var _s2 = $RefreshSig$();\n      setLoginStatus('Fazendo login...');\n      const {\n        quickLogin: doQuickLogin\n      } = await _s2(import('../stores/authStore').then(_s2(m => {\n        _s2();\n        return m.useAuth();\n      }, \"aXa0DhOnbpb+WuJfaBQuXhXHp4U=\", false, function () {\n        return [m.useAuth];\n      })), \"aXa0DhOnbpb+WuJfaBQuXhXHp4U=\", true);\n      const success = await doQuickLogin();\n      if (success) {\n        setLoginStatus('✅ Logado');\n        console.log('✅ Login realizado com sucesso');\n      } else {\n        setLoginStatus('❌ Erro no login');\n        console.log('❌ Erro no login');\n      }\n    } catch (error) {\n      setLoginStatus('❌ Falha no login');\n      console.error('❌ Falha no login:', error);\n    }\n  };\n  const playerCPU = (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.cpu) || 1;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.history.back(),\n            className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\u2190\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-lg font-bold\",\n              children: \"\\uD83D\\uDD0D Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-400\",\n              children: \"Sistema de Reconhecimento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: testConnection,\n              className: \"text-xs bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded block w-full\",\n              children: \"Testar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: quickLogin,\n              className: \"text-xs bg-green-600 hover:bg-green-700 px-2 py-1 rounded block w-full\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs\",\n              children: connectionStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs\",\n              children: loginStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-3 text-white\",\n          children: \"\\u26A1 Scan R\\xE1pido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 mb-4\",\n          children: \"Encontra alvos aleat\\xF3rios na rede\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleQuickScan,\n          disabled: isScanning,\n          className: \"w-full py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n          children: isScanning ? 'Escaneando...' : 'Iniciar Scan Rápido'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-3 text-white\",\n          children: \"\\uD83C\\uDFAF Scan Avan\\xE7ado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 mb-4\",\n          children: \"Busca por IP espec\\xEDfico\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: specificIP,\n            onChange: e => setSpecificIP(e.target.value),\n            placeholder: \"*************\",\n            className: \"flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white font-mono text-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAdvancedScan,\n            disabled: isAdvancedScan,\n            className: \"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n            children: isAdvancedScan ? '...' : 'Scan'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this), scanError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-300 text-sm font-mono\",\n          children: [\"\\u274C \", scanError]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 11\n      }, this), scanTargets.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"\\uD83D\\uDCCA Alvos Encontrados\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: scanTargets.map((target, index) => {\n            const canExploit = playerCPU > (target.firewall || 1);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-700 rounded-lg p-4 border border-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-blue-400\",\n                    children: target.nick\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400 font-mono\",\n                    children: [\"IP: \", target.ip]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400\",\n                    children: [\"N\\xEDvel: \", target.nivel]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400\",\n                    children: [\"Firewall: \", target.firewall || 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400\",\n                    children: [\"Dinheiro: $\", (target.dinheiro || 0).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded ${canExploit ? 'bg-green-600 text-green-100' : 'bg-red-600 text-red-100'}`,\n                    children: canExploit ? 'VULNERÁVEL' : 'PROTEGIDO'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleExploit(target),\n                  disabled: !canExploit,\n                  className: `px-4 py-2 rounded font-semibold text-sm ${canExploit ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-600 text-gray-400 cursor-not-allowed'}`,\n                  children: canExploit ? 'EXPLOITAR' : 'CPU INSUFICIENTE'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-semibold mb-2 text-white\",\n          children: \"\\uD83D\\uDCBB Seu Sistema\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-2 text-xs\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"CPU: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-400\",\n              children: playerCPU\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"Status: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-400\",\n              children: \"ONLINE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 26\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(GameFooter, {\n      currentPage: \"scanner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 328,\n    columnNumber: 5\n  }, this);\n};\n_s3(SimpleScanner, \"I2Y3tVnn6VOWAAFfKefWQeN+/ao=\", false, function () {\n  return [useSimpleAuth, usePlayer];\n});\n_c2 = SimpleScanner;\nconst SimpleChat = () => {\n  _s4();\n  const {\n    messages,\n    isLoading: isLoadingMessages,\n    loadMessages,\n    sendMessage,\n    isSending\n  } = useChat();\n  const [newMessage, setNewMessage] = React.useState('');\n  useEffect(() => {\n    if (messages.length === 0 && !isLoadingMessages) {\n      console.log('SimpleChat - Carregando mensagens...');\n      loadMessages();\n    }\n  }, [messages.length, isLoadingMessages, loadMessages]);\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    if (newMessage.trim() && !isSending) {\n      await sendMessage(newMessage.trim());\n      setNewMessage('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold\",\n            children: \"\\uD83D\\uDCAC Chat Global\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Canal Global - Criptografado\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-black rounded-lg p-3 h-full overflow-y-auto mb-4 border border-gray-600\",\n        children: isLoadingMessages ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-blue-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400 mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-mono\",\n            children: \"CARREGANDO DADOS...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this) : messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-blue-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl mb-2\",\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs font-mono\",\n            children: \"CANAL VAZIO - AGUARDANDO TRANSMISS\\xC3O\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-l-2 border-blue-400 pl-3 py-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-400 font-mono text-xs\",\n                children: [\"[\", new Date(message.timestamp).toLocaleTimeString(), \"]\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-400 font-mono text-xs font-bold\",\n                children: [message.usuario, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white font-mono text-sm pl-2\",\n              children: message.mensagem\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 19\n            }, this)]\n          }, message.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSendMessage,\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: newMessage,\n          onChange: e => setNewMessage(e.target.value),\n          placeholder: \"> Digite sua mensagem...\",\n          className: \"flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded font-mono text-white text-sm focus:outline-none focus:border-blue-400 placeholder-gray-400\",\n          disabled: isSending\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: !newMessage.trim() || isSending,\n          className: `px-4 py-2 rounded font-mono text-sm transition-all ${!newMessage.trim() || isSending ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700 font-bold'}`,\n          children: isSending ? '...' : 'ENVIAR'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(GameFooter, {\n      currentPage: \"chat\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 495,\n    columnNumber: 5\n  }, this);\n};\n\n// Navegação Estilo Celular/Jogo\n_s4(SimpleChat, \"OOvOc6vVu05UbQBZJwuAFiCSFbw=\", false, function () {\n  return [useChat];\n});\n_c3 = SimpleChat;\nconst SimpleNavigation = () => {\n  const currentPath = window.location.pathname;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-bg-primary\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-900 to-purple-900 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-black font-bold text-lg\",\n              children: \"S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold cyber-text\",\n              children: \"SHACK\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-text-muted\",\n              children: \"Web Terminal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"status-online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-green-400\",\n            children: \"ONLINE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-bg-secondary border-b border-cyber-primary\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/game\",\n          className: `flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath === '/game' || currentPath === '/game/' ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary' : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg mb-1\",\n            children: \"\\uD83C\\uDFE0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/game/scanner\",\n          className: `flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath.includes('/scanner') ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary' : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg mb-1\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Scanner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/game/chat\",\n          className: `flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath.includes('/chat') ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary' : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg mb-1\",\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 582,\n    columnNumber: 5\n  }, this);\n};\n\n// Página Principal do Jogo Simplificada\n_c4 = SimpleNavigation;\nconst SimpleGamePage = () => {\n  console.log('SimpleGamePage - Renderizando...');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(GameMainPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/scanner\",\n        element: /*#__PURE__*/_jsxDEV(SimpleScanner, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 652,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/chat\",\n        element: /*#__PURE__*/_jsxDEV(SimpleChat, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 653,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/transfer\",\n        element: /*#__PURE__*/_jsxDEV(TransferPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/upgrades\",\n        element: /*#__PURE__*/_jsxDEV(UpgradePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/shop\",\n        element: /*#__PURE__*/_jsxDEV(ShopPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/ranking\",\n        element: /*#__PURE__*/_jsxDEV(RankingPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/logs\",\n        element: /*#__PURE__*/_jsxDEV(LogsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/config\",\n        element: /*#__PURE__*/_jsxDEV(ConfigPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/mining\",\n        element: /*#__PURE__*/_jsxDEV(MiningPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 660,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/terminal\",\n        element: /*#__PURE__*/_jsxDEV(TerminalPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/bank\",\n        element: /*#__PURE__*/_jsxDEV(BankPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(GameMainPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 663,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 650,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 649,\n    columnNumber: 5\n  }, this);\n};\n_c5 = SimpleGamePage;\nexport default SimpleGamePage;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"SimpleDashboard\");\n$RefreshReg$(_c2, \"SimpleScanner\");\n$RefreshReg$(_c3, \"SimpleChat\");\n$RefreshReg$(_c4, \"SimpleNavigation\");\n$RefreshReg$(_c5, \"SimpleGamePage\");", "map": {"version": 3, "names": ["React", "useEffect", "Routes", "Route", "useSimpleAuth", "usePlayer", "useChat", "gameApi", "GameMainPage", "TransferPage", "UpgradePage", "ShopPage", "RankingPage", "LogsPage", "ConfigPage", "MiningPage", "TerminalPage", "BankPage", "GameFooter", "jsxDEV", "_jsxDEV", "SimpleDashboard", "_s", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "conquistas", "ranking", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "toLocaleString", "onClick", "window", "location", "href", "_c", "SimpleScanner", "_s3", "isScanning", "setIsScanning", "useState", "scanTargets", "setScanTargets", "scanError", "setScanError", "specificIP", "setSpecificIP", "isAdvancedScan", "setIsAdvancedScan", "connectionStatus", "setConnectionStatus", "loginStatus", "setLoginStatus", "handleQuickScan", "data", "sucesso", "alvos", "mensagem", "error", "message", "handleAdvancedScan", "trim", "scanSpecificIP", "alvo", "handleExploit", "target", "ip", "exploitTarget", "conexao_criada", "_target$dinheiro", "transferAmount", "prompt", "<PERSON><PERSON><PERSON>", "percentage", "parseInt", "transferResponse", "forceTransfer", "uid", "alert", "testConnection", "result", "success", "quickLogin", "_s2", "$RefreshSig$", "doQuickLogin", "then", "m", "useAuth", "playerCPU", "cpu", "history", "back", "disabled", "type", "value", "onChange", "e", "placeholder", "length", "map", "index", "canExploit", "firewall", "currentPage", "_c2", "SimpleChat", "_s4", "messages", "isLoading", "isLoadingMessages", "loadMessages", "sendMessage", "isSending", "newMessage", "setNewMessage", "handleSendMessage", "preventDefault", "Date", "timestamp", "toLocaleTimeString", "usuario", "id", "onSubmit", "_c3", "SimpleNavigation", "currentPath", "pathname", "includes", "_c4", "SimpleGamePage", "path", "element", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleGamePage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useChat } from '../stores/chatStore';\nimport gameApi from '../services/gameApi';\nimport GameMainPage from './GameMainPage';\nimport TransferPage from './TransferPage';\nimport UpgradePage from './UpgradePage';\nimport ShopPage from './ShopPage';\nimport RankingPage from './RankingPage';\nimport LogsPage from './LogsPage';\nimport ConfigPage from './ConfigPage';\nimport MiningPage from './MiningPage';\nimport TerminalPage from './TerminalPage';\nimport BankPage from './BankPage';\nimport GameFooter from '../components/common/GameFooter';\n\n// Componente do Dashboard Simplificado\nconst SimpleDashboard: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n\n  return (\n    <div className=\"p-4\">\n      {/* Header compacto */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold cyber-text\">\n              Terminal Principal\n            </h1>\n            <p className=\"text-sm text-text-muted\">\n              Operador: <span className=\"text-cyber-primary\">{user?.nick || 'Jogador'}</span>\n            </p>\n          </div>\n          {isLoadingPlayer && (\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary\"></div>\n          )}\n        </div>\n      </div>\n\n      {/* Stats do Jogador - Layout Celular */}\n      <div className=\"space-y-4\">\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">📊 Status do Sistema</h2>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-blue-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-blue-400\">\n                    {isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()}\n                  </div>\n                  <div className=\"text-xs text-blue-300\">PONTOS</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-green-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-green-400\">\n                    {isLoadingPlayer ? '...' : playerData.nivel}\n                  </div>\n                  <div className=\"text-xs text-green-300\">NÍVEL</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-purple-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-purple-400\">\n                    {isLoadingPlayer ? '...' : playerData.conquistas}\n                  </div>\n                  <div className=\"text-xs text-purple-300\">CONQUISTAS</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-orange-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-orange-400\">\n                    {isLoadingPlayer ? '...' : playerData.ranking}\n                  </div>\n                  <div className=\"text-xs text-orange-300\">RANKING</div>\n                </div>\n              </div>\n            </div>\n        </div>\n\n        {/* Ações Rápidas - Estilo Celular */}\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">⚡ Acesso Rápido</h2>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <button\n                onClick={() => window.location.href = '/game/scanner'}\n                className=\"btn-cyber text-sm py-3\"\n              >\n                <div className=\"text-lg mb-1\">🔍</div>\n                <div>Scanner</div>\n              </button>\n              <button\n                onClick={() => window.location.href = '/game/chat'}\n                className=\"btn-cyber text-sm py-3\"\n              >\n                <div className=\"text-lg mb-1\">💬</div>\n                <div>Chat</div>\n              </button>\n              <button className=\"btn-cyber text-sm py-3\">\n                <div className=\"text-lg mb-1\">🏆</div>\n                <div>Loja</div>\n              </button>\n              <button className=\"btn-cyber text-sm py-3\">\n                <div className=\"text-lg mb-1\">⚙️</div>\n                <div>Config</div>\n              </button>\n            </div>\n        </div>\n\n        {/* Log de Atividades - Estilo Terminal */}\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">📈 Log do Sistema</h2>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">+</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Pontos ganhos</div>\n                    <div className=\"text-xs text-text-muted\">02:15:33</div>\n                  </div>\n                </div>\n                <div className=\"text-green-400 font-bold text-sm\">+150</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">🏆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Nova conquista</div>\n                    <div className=\"text-xs text-text-muted\">01:22:15</div>\n                  </div>\n                </div>\n                <div className=\"text-blue-400 font-bold text-sm\">HACK</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">↑</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Level UP</div>\n                    <div className=\"text-xs text-text-muted\">00:45:22</div>\n                  </div>\n                </div>\n                <div className=\"text-purple-400 font-bold text-sm\">LV.15</div>\n              </div>\n            </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Componente Scanner Completo\nconst SimpleScanner: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer } = usePlayer();\n  const [isScanning, setIsScanning] = React.useState(false);\n  const [scanTargets, setScanTargets] = React.useState<any[]>([]);\n  const [scanError, setScanError] = React.useState<string | null>(null);\n  const [specificIP, setSpecificIP] = React.useState('');\n  const [isAdvancedScan, setIsAdvancedScan] = React.useState(false);\n  const [connectionStatus, setConnectionStatus] = React.useState<string>('Não testado');\n  const [loginStatus, setLoginStatus] = React.useState<string>('Não logado');\n\n  const handleQuickScan = async () => {\n    setIsScanning(true);\n    setScanError(null);\n\n    try {\n      console.log('🔍 Iniciando scan rápido...');\n      const data = await gameApi.scanTargets();\n\n      if (data.sucesso && data.alvos) {\n        setScanTargets(data.alvos);\n        console.log('✅ Scan realizado com sucesso:', data.alvos);\n      } else {\n        setScanError(data.mensagem || 'Erro ao escanear alvos');\n        console.log('❌ Erro no scan:', data.mensagem);\n      }\n    } catch (error: any) {\n      console.error('❌ Erro no scan:', error);\n      setScanError(error.message || 'Erro de conexão com o servidor');\n    } finally {\n      setIsScanning(false);\n    }\n  };\n\n  const handleAdvancedScan = async () => {\n    if (!specificIP.trim()) {\n      setScanError('Digite um IP válido');\n      return;\n    }\n\n    setIsAdvancedScan(true);\n    setScanError(null);\n\n    try {\n      console.log(`🎯 Iniciando scan avançado para IP: ${specificIP}`);\n      const data = await gameApi.scanSpecificIP(specificIP);\n\n      if (data.sucesso && data.alvo) {\n        setScanTargets([data.alvo]);\n        console.log('✅ Scan avançado realizado:', data.alvo);\n      } else {\n        setScanError(data.mensagem || 'IP não encontrado');\n        console.log('❌ IP não encontrado:', data.mensagem);\n      }\n    } catch (error: any) {\n      console.error('❌ Erro no scan avançado:', error);\n      setScanError(error.message || 'Erro de conexão com o servidor');\n    } finally {\n      setIsAdvancedScan(false);\n    }\n  };\n\n  const handleExploit = async (target: any) => {\n    try {\n      console.log(`⚔️ Iniciando exploit contra: ${target.nick} (${target.ip})`);\n      const data = await gameApi.exploitTarget(target.ip);\n\n      if (data.sucesso) {\n        console.log('✅ Exploit realizado com sucesso:', data);\n\n        // Mostrar opções de transferência se o exploit foi bem-sucedido\n        if (data.conexao_criada) {\n          const transferAmount = prompt(\n            `Exploit bem-sucedido em ${target.nick}!\\n\\n` +\n            `Dinheiro disponível: $${target.dinheiro?.toLocaleString() || '0'}\\n\\n` +\n            `Digite a porcentagem para transferir (20-80%):`\n          );\n\n          if (transferAmount) {\n            const percentage = parseInt(transferAmount);\n            if (percentage >= 20 && percentage <= 80) {\n              try {\n                const transferResponse = await gameApi.forceTransfer(target.uid || target.ip, percentage);\n                if (transferResponse.sucesso) {\n                  alert(`Transferência realizada! ${transferResponse.mensagem}`);\n                } else {\n                  alert(`Erro na transferência: ${transferResponse.mensagem}`);\n                }\n              } catch (error) {\n                alert('Erro na transferência');\n              }\n            } else {\n              alert('Porcentagem deve estar entre 20% e 80%');\n            }\n          }\n        } else {\n          alert(`✅ Exploit bem-sucedido! ${data.mensagem}`);\n        }\n\n        // Recarregar lista de alvos\n        handleQuickScan();\n      } else {\n        alert(`❌ Exploit falhou: ${data.mensagem}`);\n        console.log('❌ Exploit falhou:', data.mensagem);\n      }\n    } catch (error: any) {\n      console.error('❌ Erro no exploit:', error);\n      alert(`❌ Erro no exploit: ${error.message}`);\n    }\n  };\n\n  const testConnection = async () => {\n    try {\n      setConnectionStatus('Testando...');\n      const result = await gameApi.testConnection();\n\n      if (result.success) {\n        setConnectionStatus('✅ Conectado');\n        console.log('✅ Conexão com backend OK:', result.data);\n      } else {\n        setConnectionStatus('❌ Erro de conexão');\n        console.log('❌ Erro de conexão:', result.error);\n      }\n    } catch (error) {\n      setConnectionStatus('❌ Falha na conexão');\n      console.error('❌ Falha no teste de conexão:', error);\n    }\n  };\n\n  const quickLogin = async () => {\n    try {\n      setLoginStatus('Fazendo login...');\n      const { quickLogin: doQuickLogin } = await import('../stores/authStore').then(m => m.useAuth());\n      const success = await doQuickLogin();\n\n      if (success) {\n        setLoginStatus('✅ Logado');\n        console.log('✅ Login realizado com sucesso');\n      } else {\n        setLoginStatus('❌ Erro no login');\n        console.log('❌ Erro no login');\n      }\n    } catch (error) {\n      setLoginStatus('❌ Falha no login');\n      console.error('❌ Falha no login:', error);\n    }\n  };\n\n  const playerCPU = currentPlayer?.cpu || 1;\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header com informações do scanner */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => window.history.back()}\n              className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n            >\n              <span className=\"text-lg\">←</span>\n            </button>\n            <div>\n              <h1 className=\"text-lg font-bold\">🔍 Scanner</h1>\n              <p className=\"text-xs text-gray-400\">Sistema de Reconhecimento</p>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"space-y-1\">\n              <button\n                onClick={testConnection}\n                className=\"text-xs bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded block w-full\"\n              >\n                Testar\n              </button>\n              <button\n                onClick={quickLogin}\n                className=\"text-xs bg-green-600 hover:bg-green-700 px-2 py-1 rounded block w-full\"\n              >\n                Login\n              </button>\n              <p className=\"text-xs\">{connectionStatus}</p>\n              <p className=\"text-xs\">{loginStatus}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-4\">\n        {/* Scan Rápido */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-3 text-white\">⚡ Scan Rápido</h3>\n          <p className=\"text-sm text-gray-400 mb-4\">Encontra alvos aleatórios na rede</p>\n          <button\n            onClick={handleQuickScan}\n            disabled={isScanning}\n            className=\"w-full py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n          >\n            {isScanning ? 'Escaneando...' : 'Iniciar Scan Rápido'}\n          </button>\n        </div>\n\n        {/* Scan Avançado */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-3 text-white\">🎯 Scan Avançado</h3>\n          <p className=\"text-sm text-gray-400 mb-4\">Busca por IP específico</p>\n          <div className=\"flex space-x-2\">\n            <input\n              type=\"text\"\n              value={specificIP}\n              onChange={(e) => setSpecificIP(e.target.value)}\n              placeholder=\"*************\"\n              className=\"flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white font-mono text-sm\"\n            />\n            <button\n              onClick={handleAdvancedScan}\n              disabled={isAdvancedScan}\n              className=\"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n            >\n              {isAdvancedScan ? '...' : 'Scan'}\n            </button>\n          </div>\n        </div>\n\n        {/* Erro */}\n        {scanError && (\n          <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n            <p className=\"text-red-300 text-sm font-mono\">❌ {scanError}</p>\n          </div>\n        )}\n\n        {/* Resultados */}\n        {scanTargets.length > 0 && (\n          <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n            <h3 className=\"text-lg font-semibold mb-4 text-white\">📊 Alvos Encontrados</h3>\n            <div className=\"space-y-3\">\n              {scanTargets.map((target, index) => {\n                const canExploit = playerCPU > (target.firewall || 1);\n\n                return (\n                  <div key={index} className=\"bg-gray-700 rounded-lg p-4 border border-gray-600\">\n                    <div className=\"flex justify-between items-start mb-3\">\n                      <div>\n                        <h4 className=\"font-bold text-blue-400\">{target.nick}</h4>\n                        <p className=\"text-xs text-gray-400 font-mono\">IP: {target.ip}</p>\n                        <p className=\"text-xs text-gray-400\">Nível: {target.nivel}</p>\n                      </div>\n                      <div className=\"text-right\">\n                        <p className=\"text-xs text-gray-400\">Firewall: {target.firewall || 1}</p>\n                        <p className=\"text-xs text-gray-400\">Dinheiro: ${(target.dinheiro || 0).toLocaleString()}</p>\n                      </div>\n                    </div>\n\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"text-xs\">\n                        <span className={`px-2 py-1 rounded ${\n                          canExploit ? 'bg-green-600 text-green-100' : 'bg-red-600 text-red-100'\n                        }`}>\n                          {canExploit ? 'VULNERÁVEL' : 'PROTEGIDO'}\n                        </span>\n                      </div>\n\n                      <button\n                        onClick={() => handleExploit(target)}\n                        disabled={!canExploit}\n                        className={`px-4 py-2 rounded font-semibold text-sm ${\n                          canExploit\n                            ? 'bg-green-600 hover:bg-green-700 text-white'\n                            : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                        }`}\n                      >\n                        {canExploit ? 'EXPLOITAR' : 'CPU INSUFICIENTE'}\n                      </button>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        )}\n\n        {/* Info do Jogador */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-sm font-semibold mb-2 text-white\">💻 Seu Sistema</h3>\n          <div className=\"grid grid-cols-2 gap-2 text-xs\">\n            <div>CPU: <span className=\"text-blue-400\">{playerCPU}</span></div>\n            <div>Status: <span className=\"text-green-400\">ONLINE</span></div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer com navegação */}\n      <GameFooter currentPage=\"scanner\" />\n    </div>\n  );\n};\n\nconst SimpleChat: React.FC = () => {\n  const { messages, isLoading: isLoadingMessages, loadMessages, sendMessage, isSending } = useChat();\n  const [newMessage, setNewMessage] = React.useState('');\n\n  useEffect(() => {\n    if (messages.length === 0 && !isLoadingMessages) {\n      console.log('SimpleChat - Carregando mensagens...');\n      loadMessages();\n    }\n  }, [messages.length, isLoadingMessages, loadMessages]);\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (newMessage.trim() && !isSending) {\n      await sendMessage(newMessage.trim());\n      setNewMessage('');\n    }\n  };\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header do chat */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => window.history.back()}\n            className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n          >\n            <span className=\"text-lg\">←</span>\n          </button>\n          <div>\n            <h1 className=\"text-lg font-bold\">💬 Chat Global</h1>\n            <p className=\"text-xs text-gray-400\">Canal Global - Criptografado</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Área de mensagens */}\n      <div className=\"flex-1 p-4 overflow-hidden\">\n        <div className=\"bg-black rounded-lg p-3 h-full overflow-y-auto mb-4 border border-gray-600\">\n          {isLoadingMessages ? (\n            <div className=\"text-center text-blue-400\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400 mx-auto mb-2\"></div>\n              <span className=\"text-xs font-mono\">CARREGANDO DADOS...</span>\n            </div>\n          ) : messages.length === 0 ? (\n            <div className=\"text-center text-blue-400\">\n              <div className=\"text-2xl mb-2\">💬</div>\n              <p className=\"text-xs font-mono\">CANAL VAZIO - AGUARDANDO TRANSMISSÃO</p>\n            </div>\n          ) : (\n            <div className=\"space-y-2\">\n              {messages.map((message) => (\n                <div key={message.id} className=\"border-l-2 border-blue-400 pl-3 py-1\">\n                  <div className=\"flex items-center space-x-2 mb-1\">\n                    <span className=\"text-blue-400 font-mono text-xs\">\n                      [{new Date(message.timestamp).toLocaleTimeString()}]\n                    </span>\n                    <span className=\"text-green-400 font-mono text-xs font-bold\">\n                      {message.usuario}:\n                    </span>\n                  </div>\n                  <p className=\"text-white font-mono text-sm pl-2\">{message.mensagem}</p>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n      </div>\n\n      {/* Formulário de envio */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n          <input\n            type=\"text\"\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            placeholder=\"> Digite sua mensagem...\"\n            className=\"flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded font-mono text-white text-sm focus:outline-none focus:border-blue-400 placeholder-gray-400\"\n            disabled={isSending}\n          />\n          <button\n            type=\"submit\"\n            disabled={!newMessage.trim() || isSending}\n            className={`px-4 py-2 rounded font-mono text-sm transition-all ${\n              !newMessage.trim() || isSending\n                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                : 'bg-blue-600 text-white hover:bg-blue-700 font-bold'\n            }`}\n          >\n            {isSending ? '...' : 'ENVIAR'}\n          </button>\n        </form>\n      </div>\n\n      {/* Footer com navegação */}\n      <GameFooter currentPage=\"chat\" />\n    </div>\n  );\n};\n\n// Navegação Estilo Celular/Jogo\nconst SimpleNavigation: React.FC = () => {\n  const currentPath = window.location.pathname;\n\n  return (\n    <div className=\"bg-bg-primary\">\n      {/* Header estilo celular */}\n      <div className=\"bg-gradient-to-r from-blue-900 to-purple-900 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-black font-bold text-lg\">S</span>\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold cyber-text\">SHACK</h1>\n              <p className=\"text-xs text-text-muted\">Web Terminal</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"status-online\"></div>\n            <span className=\"text-xs text-green-400\">ONLINE</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Menu de navegação estilo celular */}\n      <div className=\"bg-bg-secondary border-b border-cyber-primary\">\n        <div className=\"flex\">\n          <a\n            href=\"/game\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath === '/game' || currentPath === '/game/'\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">🏠</div>\n            <div>Dashboard</div>\n          </a>\n          <a\n            href=\"/game/scanner\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath.includes('/scanner')\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">🔍</div>\n            <div>Scanner</div>\n          </a>\n          <a\n            href=\"/game/chat\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath.includes('/chat')\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">💬</div>\n            <div>Chat</div>\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Página Principal do Jogo Simplificada\nconst SimpleGamePage: React.FC = () => {\n  console.log('SimpleGamePage - Renderizando...');\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white\">\n      <Routes>\n        <Route path=\"/\" element={<GameMainPage />} />\n        <Route path=\"/scanner\" element={<SimpleScanner />} />\n        <Route path=\"/chat\" element={<SimpleChat />} />\n        <Route path=\"/transfer\" element={<TransferPage />} />\n        <Route path=\"/upgrades\" element={<UpgradePage />} />\n        <Route path=\"/shop\" element={<ShopPage />} />\n        <Route path=\"/ranking\" element={<RankingPage />} />\n        <Route path=\"/logs\" element={<LogsPage />} />\n        <Route path=\"/config\" element={<ConfigPage />} />\n        <Route path=\"/mining\" element={<MiningPage />} />\n        <Route path=\"/terminal\" element={<TerminalPage />} />\n        <Route path=\"/bank\" element={<BankPage />} />\n        <Route path=\"*\" element={<GameMainPage />} />\n      </Routes>\n    </div>\n  );\n};\n\nexport default SimpleGamePage;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,iCAAiC;;AAExD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGnB,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEoB,aAAa;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGtB,SAAS,CAAC,CAAC;EAErFJ,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0B,aAAa,IAAI,CAACF,eAAe,EAAE;MACtCG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/DH,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACC,aAAa,EAAEF,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAEpD;EACA,MAAMI,UAAU,GAAGN,aAAa,IAAI;IAClCO,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE;EACX,CAAC;EAED,oBACEd,OAAA;IAAKe,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElBhB,OAAA;MAAKe,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBhB,OAAA;QAAKe,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDhB,OAAA;UAAAgB,QAAA,gBACEhB,OAAA;YAAIe,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpB,OAAA;YAAGe,SAAS,EAAC,yBAAyB;YAAAC,QAAA,GAAC,YAC3B,eAAAhB,OAAA;cAAMe,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAE,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,KAAI;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLf,eAAe,iBACdL,OAAA;UAAKe,SAAS,EAAC;QAAmE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACzF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBhB,OAAA;QAAKe,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC1FhB,OAAA;UAAIe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EpB,OAAA;UAAKe,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrChB,OAAA;YAAKe,SAAS,EAAC,sDAAsD;YAAAC,QAAA,eACnEhB,OAAA;cAAKe,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhB,OAAA;gBAAKe,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC7CX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACC,MAAM,CAACW,cAAc,CAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpB,OAAA;YAAKe,SAAS,EAAC,uDAAuD;YAAAC,QAAA,eACpEhB,OAAA;cAAKe,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhB,OAAA;gBAAKe,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9CX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACE;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpB,OAAA;YAAKe,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrEhB,OAAA;cAAKe,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhB,OAAA;gBAAKe,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/CX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACG;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpB,OAAA;YAAKe,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrEhB,OAAA;cAAKe,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhB,OAAA;gBAAKe,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/CX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACI;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNpB,OAAA;QAAKe,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC1FhB,OAAA;UAAIe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EpB,OAAA;UAAKe,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrChB,OAAA;YACEuB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,eAAgB;YACtDX,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAElChB,OAAA;cAAKe,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCpB,OAAA;cAAAgB,QAAA,EAAK;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACTpB,OAAA;YACEuB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAa;YACnDX,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAElChB,OAAA;cAAKe,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCpB,OAAA;cAAAgB,QAAA,EAAK;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACTpB,OAAA;YAAQe,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACxChB,OAAA;cAAKe,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCpB,OAAA;cAAAgB,QAAA,EAAK;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACTpB,OAAA;YAAQe,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACxChB,OAAA;cAAKe,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCpB,OAAA;cAAAgB,QAAA,EAAK;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNpB,OAAA;QAAKe,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC1FhB,OAAA;UAAIe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EpB,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhB,OAAA;YAAKe,SAAS,EAAC,qFAAqF;YAAAC,QAAA,gBAClGhB,OAAA;cAAKe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChB,OAAA;gBAAKe,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,eACjFhB,OAAA;kBAAMe,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNpB,OAAA;gBAAAgB,QAAA,gBACEhB,OAAA;kBAAKe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDpB,OAAA;kBAAKe,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAENpB,OAAA;YAAKe,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGhB,OAAA;cAAKe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChB,OAAA;gBAAKe,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAChFhB,OAAA;kBAAMe,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNpB,OAAA;gBAAAgB,QAAA,gBACEhB,OAAA;kBAAKe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzDpB,OAAA;kBAAKe,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAENpB,OAAA;YAAKe,SAAS,EAAC,sFAAsF;YAAAC,QAAA,gBACnGhB,OAAA;cAAKe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChB,OAAA;gBAAKe,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,eAClFhB,OAAA;kBAAMe,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNpB,OAAA;gBAAAgB,QAAA,gBACEhB,OAAA;kBAAKe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDpB,OAAA;kBAAKe,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAlB,EAAA,CA5JMD,eAAyB;EAAA,QACZjB,aAAa,EAC4CC,SAAS;AAAA;AAAA0C,EAAA,GAF/E1B,eAAyB;AA6J/B,MAAM2B,aAAuB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpC,MAAM;IAAE1B;EAAK,CAAC,GAAGnB,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEoB;EAAc,CAAC,GAAGnB,SAAS,CAAC,CAAC;EACrC,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAGnD,KAAK,CAACoD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtD,KAAK,CAACoD,QAAQ,CAAQ,EAAE,CAAC;EAC/D,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAGxD,KAAK,CAACoD,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAG1D,KAAK,CAACoD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACO,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,KAAK,CAACoD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACS,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9D,KAAK,CAACoD,QAAQ,CAAS,aAAa,CAAC;EACrF,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGhE,KAAK,CAACoD,QAAQ,CAAS,YAAY,CAAC;EAE1E,MAAMa,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCd,aAAa,CAAC,IAAI,CAAC;IACnBK,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF5B,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,MAAMqC,IAAI,GAAG,MAAM3D,OAAO,CAAC8C,WAAW,CAAC,CAAC;MAExC,IAAIa,IAAI,CAACC,OAAO,IAAID,IAAI,CAACE,KAAK,EAAE;QAC9Bd,cAAc,CAACY,IAAI,CAACE,KAAK,CAAC;QAC1BxC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEqC,IAAI,CAACE,KAAK,CAAC;MAC1D,CAAC,MAAM;QACLZ,YAAY,CAACU,IAAI,CAACG,QAAQ,IAAI,wBAAwB,CAAC;QACvDzC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqC,IAAI,CAACG,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB1C,OAAO,CAAC0C,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCd,YAAY,CAACc,KAAK,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACjE,CAAC,SAAS;MACRpB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMqB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACf,UAAU,CAACgB,IAAI,CAAC,CAAC,EAAE;MACtBjB,YAAY,CAAC,qBAAqB,CAAC;MACnC;IACF;IAEAI,iBAAiB,CAAC,IAAI,CAAC;IACvBJ,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF5B,OAAO,CAACC,GAAG,CAAC,uCAAuC4B,UAAU,EAAE,CAAC;MAChE,MAAMS,IAAI,GAAG,MAAM3D,OAAO,CAACmE,cAAc,CAACjB,UAAU,CAAC;MAErD,IAAIS,IAAI,CAACC,OAAO,IAAID,IAAI,CAACS,IAAI,EAAE;QAC7BrB,cAAc,CAAC,CAACY,IAAI,CAACS,IAAI,CAAC,CAAC;QAC3B/C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqC,IAAI,CAACS,IAAI,CAAC;MACtD,CAAC,MAAM;QACLnB,YAAY,CAACU,IAAI,CAACG,QAAQ,IAAI,mBAAmB,CAAC;QAClDzC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEqC,IAAI,CAACG,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB1C,OAAO,CAAC0C,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDd,YAAY,CAACc,KAAK,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACjE,CAAC,SAAS;MACRX,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMgB,aAAa,GAAG,MAAOC,MAAW,IAAK;IAC3C,IAAI;MACFjD,OAAO,CAACC,GAAG,CAAC,gCAAgCgD,MAAM,CAACpC,IAAI,KAAKoC,MAAM,CAACC,EAAE,GAAG,CAAC;MACzE,MAAMZ,IAAI,GAAG,MAAM3D,OAAO,CAACwE,aAAa,CAACF,MAAM,CAACC,EAAE,CAAC;MAEnD,IAAIZ,IAAI,CAACC,OAAO,EAAE;QAChBvC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEqC,IAAI,CAAC;;QAErD;QACA,IAAIA,IAAI,CAACc,cAAc,EAAE;UAAA,IAAAC,gBAAA;UACvB,MAAMC,cAAc,GAAGC,MAAM,CAC3B,2BAA2BN,MAAM,CAACpC,IAAI,OAAO,GAC7C,yBAAyB,EAAAwC,gBAAA,GAAAJ,MAAM,CAACO,QAAQ,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBvC,cAAc,CAAC,CAAC,KAAI,GAAG,MAAM,GACvE,gDACF,CAAC;UAED,IAAIwC,cAAc,EAAE;YAClB,MAAMG,UAAU,GAAGC,QAAQ,CAACJ,cAAc,CAAC;YAC3C,IAAIG,UAAU,IAAI,EAAE,IAAIA,UAAU,IAAI,EAAE,EAAE;cACxC,IAAI;gBACF,MAAME,gBAAgB,GAAG,MAAMhF,OAAO,CAACiF,aAAa,CAACX,MAAM,CAACY,GAAG,IAAIZ,MAAM,CAACC,EAAE,EAAEO,UAAU,CAAC;gBACzF,IAAIE,gBAAgB,CAACpB,OAAO,EAAE;kBAC5BuB,KAAK,CAAC,4BAA4BH,gBAAgB,CAAClB,QAAQ,EAAE,CAAC;gBAChE,CAAC,MAAM;kBACLqB,KAAK,CAAC,0BAA0BH,gBAAgB,CAAClB,QAAQ,EAAE,CAAC;gBAC9D;cACF,CAAC,CAAC,OAAOC,KAAK,EAAE;gBACdoB,KAAK,CAAC,uBAAuB,CAAC;cAChC;YACF,CAAC,MAAM;cACLA,KAAK,CAAC,wCAAwC,CAAC;YACjD;UACF;QACF,CAAC,MAAM;UACLA,KAAK,CAAC,2BAA2BxB,IAAI,CAACG,QAAQ,EAAE,CAAC;QACnD;;QAEA;QACAJ,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACLyB,KAAK,CAAC,qBAAqBxB,IAAI,CAACG,QAAQ,EAAE,CAAC;QAC3CzC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEqC,IAAI,CAACG,QAAQ,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB1C,OAAO,CAAC0C,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CoB,KAAK,CAAC,sBAAsBpB,KAAK,CAACC,OAAO,EAAE,CAAC;IAC9C;EACF,CAAC;EAED,MAAMoB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF7B,mBAAmB,CAAC,aAAa,CAAC;MAClC,MAAM8B,MAAM,GAAG,MAAMrF,OAAO,CAACoF,cAAc,CAAC,CAAC;MAE7C,IAAIC,MAAM,CAACC,OAAO,EAAE;QAClB/B,mBAAmB,CAAC,aAAa,CAAC;QAClClC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE+D,MAAM,CAAC1B,IAAI,CAAC;MACvD,CAAC,MAAM;QACLJ,mBAAmB,CAAC,mBAAmB,CAAC;QACxClC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE+D,MAAM,CAACtB,KAAK,CAAC;MACjD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdR,mBAAmB,CAAC,oBAAoB,CAAC;MACzClC,OAAO,CAAC0C,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMwB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MAAA,IAAAC,GAAA,GAAAC,YAAA;MACFhC,cAAc,CAAC,kBAAkB,CAAC;MAClC,MAAM;QAAE8B,UAAU,EAAEG;MAAa,CAAC,GAAG,MAAAF,GAAA,CAAM,MAAM,CAAC,qBAAqB,CAAC,CAACG,IAAI,CAAAH,GAAA,CAACI,CAAC;QAAAJ,GAAA;QAAA,OAAII,CAAC,CAACC,OAAO,CAAC,CAAC;MAAA;QAAA,QAAXD,CAAC,CAACC,OAAO;MAAA,EAAE,CAAC;MAC/F,MAAMP,OAAO,GAAG,MAAMI,YAAY,CAAC,CAAC;MAEpC,IAAIJ,OAAO,EAAE;QACX7B,cAAc,CAAC,UAAU,CAAC;QAC1BpC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC9C,CAAC,MAAM;QACLmC,cAAc,CAAC,iBAAiB,CAAC;QACjCpC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAChC;IACF,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdN,cAAc,CAAC,kBAAkB,CAAC;MAClCpC,OAAO,CAAC0C,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C;EACF,CAAC;EAED,MAAM+B,SAAS,GAAG,CAAA7E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8E,GAAG,KAAI,CAAC;EAEzC,oBACElF,OAAA;IAAKe,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DhB,OAAA;MAAKe,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEhB,OAAA;QAAKe,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDhB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChB,OAAA;YACEuB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAAC2D,OAAO,CAACC,IAAI,CAAC,CAAE;YACrCrE,SAAS,EAAC,mFAAmF;YAAAC,QAAA,eAE7FhB,OAAA;cAAMe,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACTpB,OAAA;YAAAgB,QAAA,gBACEhB,OAAA;cAAIe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDpB,OAAA;cAAGe,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA;UAAKe,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBhB,OAAA;YAAKe,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhB,OAAA;cACEuB,OAAO,EAAEgD,cAAe;cACxBxD,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EACjF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpB,OAAA;cACEuB,OAAO,EAAEmD,UAAW;cACpB3D,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EACnF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpB,OAAA;cAAGe,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEyB;YAAgB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7CpB,OAAA;cAAGe,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAE2B;YAAW;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBAEnDhB,OAAA;QAAKe,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEhB,OAAA;UAAIe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEpB,OAAA;UAAGe,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/EpB,OAAA;UACEuB,OAAO,EAAEsB,eAAgB;UACzBwC,QAAQ,EAAEvD,UAAW;UACrBf,SAAS,EAAC,oGAAoG;UAAAC,QAAA,EAE7Gc,UAAU,GAAG,eAAe,GAAG;QAAqB;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNpB,OAAA;QAAKe,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEhB,OAAA;UAAIe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EpB,OAAA;UAAGe,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrEpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhB,OAAA;YACEsF,IAAI,EAAC,MAAM;YACXC,KAAK,EAAElD,UAAW;YAClBmD,QAAQ,EAAGC,CAAC,IAAKnD,aAAa,CAACmD,CAAC,CAAChC,MAAM,CAAC8B,KAAK,CAAE;YAC/CG,WAAW,EAAC,eAAe;YAC3B3E,SAAS,EAAC;UAA0F;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,eACFpB,OAAA;YACEuB,OAAO,EAAE6B,kBAAmB;YAC5BiC,QAAQ,EAAE9C,cAAe;YACzBxB,SAAS,EAAC,oGAAoG;YAAAC,QAAA,EAE7GuB,cAAc,GAAG,KAAK,GAAG;UAAM;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLe,SAAS,iBACRnC,OAAA;QAAKe,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DhB,OAAA;UAAGe,SAAS,EAAC,gCAAgC;UAAAC,QAAA,GAAC,SAAE,EAACmB,SAAS;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CACN,EAGAa,WAAW,CAAC0D,MAAM,GAAG,CAAC,iBACrB3F,OAAA;QAAKe,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEhB,OAAA;UAAIe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EpB,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBiB,WAAW,CAAC2D,GAAG,CAAC,CAACnC,MAAM,EAAEoC,KAAK,KAAK;YAClC,MAAMC,UAAU,GAAGb,SAAS,IAAIxB,MAAM,CAACsC,QAAQ,IAAI,CAAC,CAAC;YAErD,oBACE/F,OAAA;cAAiBe,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAC5EhB,OAAA;gBAAKe,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDhB,OAAA;kBAAAgB,QAAA,gBACEhB,OAAA;oBAAIe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAEyC,MAAM,CAACpC;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1DpB,OAAA;oBAAGe,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAAC,MAAI,EAACyC,MAAM,CAACC,EAAE;kBAAA;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClEpB,OAAA;oBAAGe,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,YAAO,EAACyC,MAAM,CAAC7C,KAAK;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACNpB,OAAA;kBAAKe,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhB,OAAA;oBAAGe,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,YAAU,EAACyC,MAAM,CAACsC,QAAQ,IAAI,CAAC;kBAAA;oBAAA9E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzEpB,OAAA;oBAAGe,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,aAAW,EAAC,CAACyC,MAAM,CAACO,QAAQ,IAAI,CAAC,EAAE1C,cAAc,CAAC,CAAC;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENpB,OAAA;gBAAKe,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDhB,OAAA;kBAAKe,SAAS,EAAC,SAAS;kBAAAC,QAAA,eACtBhB,OAAA;oBAAMe,SAAS,EAAE,qBACf+E,UAAU,GAAG,6BAA6B,GAAG,yBAAyB,EACrE;oBAAA9E,QAAA,EACA8E,UAAU,GAAG,YAAY,GAAG;kBAAW;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENpB,OAAA;kBACEuB,OAAO,EAAEA,CAAA,KAAMiC,aAAa,CAACC,MAAM,CAAE;kBACrC4B,QAAQ,EAAE,CAACS,UAAW;kBACtB/E,SAAS,EAAE,2CACT+E,UAAU,GACN,4CAA4C,GAC5C,8CAA8C,EACjD;kBAAA9E,QAAA,EAEF8E,UAAU,GAAG,WAAW,GAAG;gBAAkB;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAjCEyE,KAAK;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDpB,OAAA;QAAKe,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEhB,OAAA;UAAIe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEpB,OAAA;UAAKe,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7ChB,OAAA;YAAAgB,QAAA,GAAK,OAAK,eAAAhB,OAAA;cAAMe,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEiE;YAAS;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClEpB,OAAA;YAAAgB,QAAA,GAAK,UAAQ,eAAAhB,OAAA;cAAMe,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpB,OAAA,CAACF,UAAU;MAACkG,WAAW,EAAC;IAAS;MAAA/E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjC,CAAC;AAEV,CAAC;AAACS,GAAA,CAxSID,aAAuB;EAAA,QACV5C,aAAa,EACJC,SAAS;AAAA;AAAAgH,GAAA,GAF/BrE,aAAuB;AA0S7B,MAAMsE,UAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM;IAAEC,QAAQ;IAAEC,SAAS,EAAEC,iBAAiB;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGvH,OAAO,CAAC,CAAC;EAClG,MAAM,CAACwH,UAAU,EAAEC,aAAa,CAAC,GAAG/H,KAAK,CAACoD,QAAQ,CAAC,EAAE,CAAC;EAEtDnD,SAAS,CAAC,MAAM;IACd,IAAIuH,QAAQ,CAACT,MAAM,KAAK,CAAC,IAAI,CAACW,iBAAiB,EAAE;MAC/C9F,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD8F,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACH,QAAQ,CAACT,MAAM,EAAEW,iBAAiB,EAAEC,YAAY,CAAC,CAAC;EAEtD,MAAMK,iBAAiB,GAAG,MAAOnB,CAAkB,IAAK;IACtDA,CAAC,CAACoB,cAAc,CAAC,CAAC;IAClB,IAAIH,UAAU,CAACrD,IAAI,CAAC,CAAC,IAAI,CAACoD,SAAS,EAAE;MACnC,MAAMD,WAAW,CAACE,UAAU,CAACrD,IAAI,CAAC,CAAC,CAAC;MACpCsD,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,oBACE3G,OAAA;IAAKe,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DhB,OAAA;MAAKe,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEhB,OAAA;QAAKe,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ChB,OAAA;UACEuB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAAC2D,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCrE,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7FhB,OAAA;YAAMe,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACTpB,OAAA;UAAAgB,QAAA,gBACEhB,OAAA;YAAIe,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDpB,OAAA;YAAGe,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzChB,OAAA;QAAKe,SAAS,EAAC,4EAA4E;QAAAC,QAAA,EACxFsF,iBAAiB,gBAChBtG,OAAA;UAAKe,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxChB,OAAA;YAAKe,SAAS,EAAC;UAA2E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjGpB,OAAA;YAAMe,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,GACJgF,QAAQ,CAACT,MAAM,KAAK,CAAC,gBACvB3F,OAAA;UAAKe,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxChB,OAAA;YAAKe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCpB,OAAA;YAAGe,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,gBAENpB,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBoF,QAAQ,CAACR,GAAG,CAAEzC,OAAO,iBACpBnD,OAAA;YAAsBe,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACpEhB,OAAA;cAAKe,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/ChB,OAAA;gBAAMe,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAAC,GAC/C,EAAC,IAAI8F,IAAI,CAAC3D,OAAO,CAAC4D,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAC,GACrD;cAAA;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPpB,OAAA;gBAAMe,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,GACzDmC,OAAO,CAAC8D,OAAO,EAAC,GACnB;cAAA;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNpB,OAAA;cAAGe,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEmC,OAAO,CAACF;YAAQ;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAT/D+B,OAAO,CAAC+D,EAAE;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEhB,OAAA;QAAMmH,QAAQ,EAAEP,iBAAkB;QAAC7F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3DhB,OAAA;UACEsF,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEmB,UAAW;UAClBlB,QAAQ,EAAGC,CAAC,IAAKkB,aAAa,CAAClB,CAAC,CAAChC,MAAM,CAAC8B,KAAK,CAAE;UAC/CG,WAAW,EAAC,0BAA0B;UACtC3E,SAAS,EAAC,wJAAwJ;UAClKsE,QAAQ,EAAEoB;QAAU;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACFpB,OAAA;UACEsF,IAAI,EAAC,QAAQ;UACbD,QAAQ,EAAE,CAACqB,UAAU,CAACrD,IAAI,CAAC,CAAC,IAAIoD,SAAU;UAC1C1F,SAAS,EAAE,sDACT,CAAC2F,UAAU,CAACrD,IAAI,CAAC,CAAC,IAAIoD,SAAS,GAC3B,8CAA8C,GAC9C,oDAAoD,EACvD;UAAAzF,QAAA,EAEFyF,SAAS,GAAG,KAAK,GAAG;QAAQ;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNpB,OAAA,CAACF,UAAU;MAACkG,WAAW,EAAC;IAAM;MAAA/E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9B,CAAC;AAEV,CAAC;;AAED;AAAA+E,GAAA,CAtGMD,UAAoB;EAAA,QACiEhH,OAAO;AAAA;AAAAkI,GAAA,GAD5FlB,UAAoB;AAuG1B,MAAMmB,gBAA0B,GAAGA,CAAA,KAAM;EACvC,MAAMC,WAAW,GAAG9F,MAAM,CAACC,QAAQ,CAAC8F,QAAQ;EAE5C,oBACEvH,OAAA;IAAKe,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BhB,OAAA;MAAKe,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eAC/DhB,OAAA;QAAKe,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDhB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChB,OAAA;YAAKe,SAAS,EAAC,wEAAwE;YAAAC,QAAA,eACrFhB,OAAA;cAAMe,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNpB,OAAA;YAAAgB,QAAA,gBACEhB,OAAA;cAAIe,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDpB,OAAA;cAAGe,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChB,OAAA;YAAKe,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrCpB,OAAA;YAAMe,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DhB,OAAA;QAAKe,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhB,OAAA;UACE0B,IAAI,EAAC,OAAO;UACZX,SAAS,EAAE,mEACTuG,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,QAAQ,GAC/C,6DAA6D,GAC7D,+DAA+D,EAClE;UAAAtG,QAAA,gBAEHhB,OAAA;YAAKe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCpB,OAAA;YAAAgB,QAAA,EAAK;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACJpB,OAAA;UACE0B,IAAI,EAAC,eAAe;UACpBX,SAAS,EAAE,mEACTuG,WAAW,CAACE,QAAQ,CAAC,UAAU,CAAC,GAC5B,6DAA6D,GAC7D,+DAA+D,EAClE;UAAAxG,QAAA,gBAEHhB,OAAA;YAAKe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCpB,OAAA;YAAAgB,QAAA,EAAK;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACJpB,OAAA;UACE0B,IAAI,EAAC,YAAY;UACjBX,SAAS,EAAE,mEACTuG,WAAW,CAACE,QAAQ,CAAC,OAAO,CAAC,GACzB,6DAA6D,GAC7D,+DAA+D,EAClE;UAAAxG,QAAA,gBAEHhB,OAAA;YAAKe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCpB,OAAA;YAAAgB,QAAA,EAAK;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAqG,GAAA,GAlEMJ,gBAA0B;AAmEhC,MAAMK,cAAwB,GAAGA,CAAA,KAAM;EACrClH,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAE/C,oBACET,OAAA;IAAKe,SAAS,EAAC,qCAAqC;IAAAC,QAAA,eAClDhB,OAAA,CAAClB,MAAM;MAAAkC,QAAA,gBACLhB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,GAAG;QAACC,OAAO,eAAE5H,OAAA,CAACZ,YAAY;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,UAAU;QAACC,OAAO,eAAE5H,OAAA,CAAC4B,aAAa;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,OAAO;QAACC,OAAO,eAAE5H,OAAA,CAACkG,UAAU;UAAAjF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,WAAW;QAACC,OAAO,eAAE5H,OAAA,CAACX,YAAY;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,WAAW;QAACC,OAAO,eAAE5H,OAAA,CAACV,WAAW;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpDpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,OAAO;QAACC,OAAO,eAAE5H,OAAA,CAACT,QAAQ;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,UAAU;QAACC,OAAO,eAAE5H,OAAA,CAACR,WAAW;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnDpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,OAAO;QAACC,OAAO,eAAE5H,OAAA,CAACP,QAAQ;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,SAAS;QAACC,OAAO,eAAE5H,OAAA,CAACN,UAAU;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,SAAS;QAACC,OAAO,eAAE5H,OAAA,CAACL,UAAU;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,WAAW;QAACC,OAAO,eAAE5H,OAAA,CAACJ,YAAY;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,OAAO;QAACC,OAAO,eAAE5H,OAAA,CAACH,QAAQ;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CpB,OAAA,CAACjB,KAAK;QAAC4I,IAAI,EAAC,GAAG;QAACC,OAAO,eAAE5H,OAAA,CAACZ,YAAY;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACyG,GAAA,GAtBIH,cAAwB;AAwB9B,eAAeA,cAAc;AAAC,IAAA/F,EAAA,EAAAsE,GAAA,EAAAmB,GAAA,EAAAK,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAnG,EAAA;AAAAmG,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}