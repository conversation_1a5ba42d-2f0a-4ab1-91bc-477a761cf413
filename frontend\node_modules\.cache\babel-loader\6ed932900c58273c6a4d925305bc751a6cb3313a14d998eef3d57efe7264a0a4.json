{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport gameService from '../services/gameService';\nexport const useGameSystem = () => {\n  _s();\n  const [state, setState] = useState({\n    currentPlayer: null,\n    isLoadingPlayer: false,\n    playerError: null,\n    scanTargets: [],\n    isScanning: false,\n    scanError: null,\n    exploitedTarget: null,\n    isExploiting: false,\n    exploitError: null,\n    activeConnections: [],\n    isLoadingConnections: false,\n    connectionError: null,\n    activeBruteforces: new Map(),\n    isTransferring: false,\n    transferError: null\n  });\n  const updateIntervalRef = useRef(null);\n\n  // === PLAYER DATA ===\n  const loadPlayerData = useCallback(async (forceRefresh = false) => {\n    setState(prev => ({\n      ...prev,\n      isLoadingPlayer: true,\n      playerError: null\n    }));\n    try {\n      const playerData = await gameService.loadPlayerData(forceRefresh);\n      setState(prev => ({\n        ...prev,\n        currentPlayer: playerData,\n        isLoadingPlayer: false\n      }));\n      return playerData;\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        playerError: error instanceof Error ? error.message : 'Erro ao carregar dados',\n        isLoadingPlayer: false\n      }));\n      throw error;\n    }\n  }, []);\n\n  // === SCAN SYSTEM ===\n  const performQuickScan = useCallback(async () => {\n    setState(prev => ({\n      ...prev,\n      isScanning: true,\n      scanError: null\n    }));\n    try {\n      const targets = await gameService.performQuickScan();\n      setState(prev => ({\n        ...prev,\n        scanTargets: targets,\n        isScanning: false\n      }));\n      return targets;\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        scanError: error instanceof Error ? error.message : 'Erro no scan',\n        isScanning: false\n      }));\n      throw error;\n    }\n  }, []);\n  const performAdvancedScan = useCallback(async targetIp => {\n    setState(prev => ({\n      ...prev,\n      isScanning: true,\n      scanError: null\n    }));\n    try {\n      const targets = await gameService.performAdvancedScan(targetIp);\n      setState(prev => ({\n        ...prev,\n        scanTargets: targets,\n        isScanning: false\n      }));\n      return targets;\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        scanError: error instanceof Error ? error.message : 'Erro no scan',\n        isScanning: false\n      }));\n      throw error;\n    }\n  }, []);\n\n  // === EXPLOIT SYSTEM ===\n  const performExploit = useCallback(async target => {\n    setState(prev => ({\n      ...prev,\n      isExploiting: true,\n      exploitError: null\n    }));\n    try {\n      const exploitedTarget = await gameService.performExploit(target);\n      setState(prev => ({\n        ...prev,\n        exploitedTarget,\n        isExploiting: false\n      }));\n\n      // Atualizar conexões ativas\n      await loadActiveConnections();\n      return exploitedTarget;\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        exploitError: error instanceof Error ? error.message : 'Erro no exploit',\n        isExploiting: false\n      }));\n      throw error;\n    }\n  }, []);\n\n  // === TERMINAL SYSTEM ===\n  const loadActiveConnections = useCallback(async () => {\n    setState(prev => ({\n      ...prev,\n      isLoadingConnections: true,\n      connectionError: null\n    }));\n    try {\n      const connections = await gameService.getActiveConnections();\n      setState(prev => ({\n        ...prev,\n        activeConnections: connections,\n        isLoadingConnections: false\n      }));\n      return connections;\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        connectionError: error instanceof Error ? error.message : 'Erro ao carregar conexões',\n        isLoadingConnections: false\n      }));\n      throw error;\n    }\n  }, []);\n  const startBruteforce = useCallback(async (connectionId, targetIp) => {\n    try {\n      const bruteforceStatus = await gameService.startBruteforce(connectionId, targetIp);\n      setState(prev => {\n        const newBruteforces = new Map(prev.activeBruteforces);\n        newBruteforces.set(targetIp, bruteforceStatus);\n        return {\n          ...prev,\n          activeBruteforces: newBruteforces\n        };\n      });\n      return bruteforceStatus;\n    } catch (error) {\n      console.error('Erro ao iniciar bruteforce:', error);\n      throw error;\n    }\n  }, []);\n  const closeConnection = useCallback(async (connectionId, targetIp) => {\n    try {\n      await gameService.closeConnection(connectionId, targetIp);\n\n      // Atualizar estado local\n      setState(prev => {\n        var _prev$exploitedTarget;\n        const newConnections = prev.activeConnections.filter(conn => conn.targetIp !== targetIp);\n        const newBruteforces = new Map(prev.activeBruteforces);\n        newBruteforces.delete(targetIp);\n        return {\n          ...prev,\n          activeConnections: newConnections,\n          activeBruteforces: newBruteforces,\n          exploitedTarget: ((_prev$exploitedTarget = prev.exploitedTarget) === null || _prev$exploitedTarget === void 0 ? void 0 : _prev$exploitedTarget.ip) === targetIp ? null : prev.exploitedTarget\n        };\n      });\n    } catch (error) {\n      console.error('Erro ao fechar conexão:', error);\n      throw error;\n    }\n  }, []);\n\n  // === TRANSFER SYSTEM ===\n  const performBankTransfer = useCallback(async (targetIp, percentage) => {\n    setState(prev => ({\n      ...prev,\n      isTransferring: true,\n      transferError: null\n    }));\n    try {\n      const result = await gameService.performBankTransfer(targetIp, percentage);\n      setState(prev => ({\n        ...prev,\n        isTransferring: false\n      }));\n\n      // Atualizar dados do jogador após transferência\n      await loadPlayerData(true);\n      return result;\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        transferError: error instanceof Error ? error.message : 'Erro na transferência',\n        isTransferring: false\n      }));\n      throw error;\n    }\n  }, [loadPlayerData]);\n\n  // === AUTO UPDATE SYSTEM ===\n  const startAutoUpdate = useCallback(() => {\n    if (updateIntervalRef.current) {\n      clearInterval(updateIntervalRef.current);\n    }\n    updateIntervalRef.current = setInterval(async () => {\n      try {\n        // Atualizar dados do jogador silenciosamente\n        await loadPlayerData();\n\n        // Atualizar conexões ativas se houver alguma\n        if (state.activeConnections.length > 0) {\n          await loadActiveConnections();\n        }\n      } catch (error) {\n        console.error('Erro na atualização automática:', error);\n      }\n    }, 30000); // 30 segundos\n  }, [loadPlayerData, loadActiveConnections, state.activeConnections.length]);\n  const stopAutoUpdate = useCallback(() => {\n    if (updateIntervalRef.current) {\n      clearInterval(updateIntervalRef.current);\n      updateIntervalRef.current = null;\n    }\n  }, []);\n\n  // === CLEAR ERRORS ===\n  const clearErrors = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      playerError: null,\n      scanError: null,\n      exploitError: null,\n      connectionError: null,\n      transferError: null\n    }));\n  }, []);\n\n  // === EFFECTS ===\n  useEffect(() => {\n    // Carregar dados iniciais\n    loadPlayerData();\n    loadActiveConnections();\n\n    // Iniciar atualização automática\n    startAutoUpdate();\n\n    // Cleanup\n    return () => {\n      stopAutoUpdate();\n      gameService.cleanup();\n    };\n  }, []);\n\n  // Atualizar bruteforces em tempo real\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setState(prev => {\n        const newBruteforces = new Map(prev.activeBruteforces);\n        let hasChanges = false;\n        newBruteforces.forEach((bruteforce, targetIp) => {\n          if (bruteforce.isRunning && bruteforce.timeRemaining > 0) {\n            bruteforce.timeRemaining--;\n            bruteforce.progress = (bruteforce.totalTime - bruteforce.timeRemaining) / bruteforce.totalTime * 100;\n            hasChanges = true;\n            if (bruteforce.timeRemaining <= 0) {\n              bruteforce.isRunning = false;\n              // Atualizar conexão para liberar banco\n              const connectionIndex = prev.activeConnections.findIndex(conn => conn.targetIp === targetIp);\n              if (connectionIndex >= 0) {\n                prev.activeConnections[connectionIndex].bankAccess = true;\n              }\n            }\n          }\n        });\n        return hasChanges ? {\n          ...prev,\n          activeBruteforces: newBruteforces\n        } : prev;\n      });\n    }, 1000);\n    return () => clearInterval(interval);\n  }, []);\n  return {\n    // State\n    ...state,\n    // Actions\n    loadPlayerData,\n    performQuickScan,\n    performAdvancedScan,\n    performExploit,\n    loadActiveConnections,\n    startBruteforce,\n    closeConnection,\n    performBankTransfer,\n    clearErrors,\n    // Utils\n    startAutoUpdate,\n    stopAutoUpdate\n  };\n};\n_s(useGameSystem, \"mzY++jtYrhGARoMHwu3uWs0bqzg=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "gameService", "useGameSystem", "_s", "state", "setState", "currentPlayer", "isLoadingPlayer", "playerError", "scanTargets", "isScanning", "scanError", "<PERSON><PERSON>arget", "isExploiting", "exploitError", "activeConnections", "isLoadingConnections", "connectionError", "activeBruteforces", "Map", "isTransferring", "transferError", "updateIntervalRef", "loadPlayerData", "forceRefresh", "prev", "player<PERSON><PERSON>", "error", "Error", "message", "performQuickScan", "targets", "performAdvancedScan", "targetIp", "performExploit", "target", "loadActiveConnections", "connections", "getActiveConnections", "startBruteforce", "connectionId", "bruteforceStatus", "newBruteforces", "set", "console", "closeConnection", "_prev$exploitedTarget", "newConnections", "filter", "conn", "delete", "ip", "performBankTransfer", "percentage", "result", "startAutoUpdate", "current", "clearInterval", "setInterval", "length", "stopAutoUpdate", "clearErrors", "cleanup", "interval", "has<PERSON><PERSON><PERSON>", "for<PERSON>ach", "bruteforce", "isRunning", "timeRemaining", "progress", "totalTime", "connectionIndex", "findIndex", "bankAccess"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/hooks/useGameSystem.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from 'react';\nimport gameService, { ScanTarget, ExploitedTarget, ConnectionStatus, BruteforceStatus } from '../services/gameService';\n\ninterface GameSystemState {\n  // Player data\n  currentPlayer: any;\n  isLoadingPlayer: boolean;\n  playerError: string | null;\n  \n  // Scan system\n  scanTargets: ScanTarget[];\n  isScanning: boolean;\n  scanError: string | null;\n  \n  // Exploit system\n  exploitedTarget: ExploitedTarget | null;\n  isExploiting: boolean;\n  exploitError: string | null;\n  \n  // Terminal system\n  activeConnections: ConnectionStatus[];\n  isLoadingConnections: boolean;\n  connectionError: string | null;\n  \n  // Bruteforce system\n  activeBruteforces: Map<string, BruteforceStatus>;\n  \n  // Transfer system\n  isTransferring: boolean;\n  transferError: string | null;\n}\n\nexport const useGameSystem = () => {\n  const [state, setState] = useState<GameSystemState>({\n    currentPlayer: null,\n    isLoadingPlayer: false,\n    playerError: null,\n    scanTargets: [],\n    isScanning: false,\n    scanError: null,\n    exploitedTarget: null,\n    isExploiting: false,\n    exploitError: null,\n    activeConnections: [],\n    isLoadingConnections: false,\n    connectionError: null,\n    activeBruteforces: new Map(),\n    isTransferring: false,\n    transferError: null,\n  });\n\n  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);\n\n  // === PLAYER DATA ===\n  const loadPlayerData = useCallback(async (forceRefresh = false) => {\n    setState(prev => ({ ...prev, isLoadingPlayer: true, playerError: null }));\n    \n    try {\n      const playerData = await gameService.loadPlayerData(forceRefresh);\n      setState(prev => ({ \n        ...prev, \n        currentPlayer: playerData, \n        isLoadingPlayer: false \n      }));\n      return playerData;\n    } catch (error) {\n      setState(prev => ({ \n        ...prev, \n        playerError: error instanceof Error ? error.message : 'Erro ao carregar dados',\n        isLoadingPlayer: false \n      }));\n      throw error;\n    }\n  }, []);\n\n  // === SCAN SYSTEM ===\n  const performQuickScan = useCallback(async () => {\n    setState(prev => ({ ...prev, isScanning: true, scanError: null }));\n    \n    try {\n      const targets = await gameService.performQuickScan();\n      setState(prev => ({ \n        ...prev, \n        scanTargets: targets, \n        isScanning: false \n      }));\n      return targets;\n    } catch (error) {\n      setState(prev => ({ \n        ...prev, \n        scanError: error instanceof Error ? error.message : 'Erro no scan',\n        isScanning: false \n      }));\n      throw error;\n    }\n  }, []);\n\n  const performAdvancedScan = useCallback(async (targetIp: string) => {\n    setState(prev => ({ ...prev, isScanning: true, scanError: null }));\n    \n    try {\n      const targets = await gameService.performAdvancedScan(targetIp);\n      setState(prev => ({ \n        ...prev, \n        scanTargets: targets, \n        isScanning: false \n      }));\n      return targets;\n    } catch (error) {\n      setState(prev => ({ \n        ...prev, \n        scanError: error instanceof Error ? error.message : 'Erro no scan',\n        isScanning: false \n      }));\n      throw error;\n    }\n  }, []);\n\n  // === EXPLOIT SYSTEM ===\n  const performExploit = useCallback(async (target: ScanTarget) => {\n    setState(prev => ({ ...prev, isExploiting: true, exploitError: null }));\n    \n    try {\n      const exploitedTarget = await gameService.performExploit(target);\n      setState(prev => ({ \n        ...prev, \n        exploitedTarget, \n        isExploiting: false \n      }));\n      \n      // Atualizar conexões ativas\n      await loadActiveConnections();\n      \n      return exploitedTarget;\n    } catch (error) {\n      setState(prev => ({ \n        ...prev, \n        exploitError: error instanceof Error ? error.message : 'Erro no exploit',\n        isExploiting: false \n      }));\n      throw error;\n    }\n  }, []);\n\n  // === TERMINAL SYSTEM ===\n  const loadActiveConnections = useCallback(async () => {\n    setState(prev => ({ ...prev, isLoadingConnections: true, connectionError: null }));\n    \n    try {\n      const connections = await gameService.getActiveConnections();\n      setState(prev => ({ \n        ...prev, \n        activeConnections: connections, \n        isLoadingConnections: false \n      }));\n      return connections;\n    } catch (error) {\n      setState(prev => ({ \n        ...prev, \n        connectionError: error instanceof Error ? error.message : 'Erro ao carregar conexões',\n        isLoadingConnections: false \n      }));\n      throw error;\n    }\n  }, []);\n\n  const startBruteforce = useCallback(async (connectionId: string, targetIp: string) => {\n    try {\n      const bruteforceStatus = await gameService.startBruteforce(connectionId, targetIp);\n      \n      setState(prev => {\n        const newBruteforces = new Map(prev.activeBruteforces);\n        newBruteforces.set(targetIp, bruteforceStatus);\n        return { ...prev, activeBruteforces: newBruteforces };\n      });\n      \n      return bruteforceStatus;\n    } catch (error) {\n      console.error('Erro ao iniciar bruteforce:', error);\n      throw error;\n    }\n  }, []);\n\n  const closeConnection = useCallback(async (connectionId: string, targetIp: string) => {\n    try {\n      await gameService.closeConnection(connectionId, targetIp);\n      \n      // Atualizar estado local\n      setState(prev => {\n        const newConnections = prev.activeConnections.filter(conn => conn.targetIp !== targetIp);\n        const newBruteforces = new Map(prev.activeBruteforces);\n        newBruteforces.delete(targetIp);\n        \n        return {\n          ...prev,\n          activeConnections: newConnections,\n          activeBruteforces: newBruteforces,\n          exploitedTarget: prev.exploitedTarget?.ip === targetIp ? null : prev.exploitedTarget\n        };\n      });\n    } catch (error) {\n      console.error('Erro ao fechar conexão:', error);\n      throw error;\n    }\n  }, []);\n\n  // === TRANSFER SYSTEM ===\n  const performBankTransfer = useCallback(async (targetIp: string, percentage: number) => {\n    setState(prev => ({ ...prev, isTransferring: true, transferError: null }));\n    \n    try {\n      const result = await gameService.performBankTransfer(targetIp, percentage);\n      setState(prev => ({ ...prev, isTransferring: false }));\n      \n      // Atualizar dados do jogador após transferência\n      await loadPlayerData(true);\n      \n      return result;\n    } catch (error) {\n      setState(prev => ({ \n        ...prev, \n        transferError: error instanceof Error ? error.message : 'Erro na transferência',\n        isTransferring: false \n      }));\n      throw error;\n    }\n  }, [loadPlayerData]);\n\n  // === AUTO UPDATE SYSTEM ===\n  const startAutoUpdate = useCallback(() => {\n    if (updateIntervalRef.current) {\n      clearInterval(updateIntervalRef.current);\n    }\n    \n    updateIntervalRef.current = setInterval(async () => {\n      try {\n        // Atualizar dados do jogador silenciosamente\n        await loadPlayerData();\n        \n        // Atualizar conexões ativas se houver alguma\n        if (state.activeConnections.length > 0) {\n          await loadActiveConnections();\n        }\n      } catch (error) {\n        console.error('Erro na atualização automática:', error);\n      }\n    }, 30000); // 30 segundos\n  }, [loadPlayerData, loadActiveConnections, state.activeConnections.length]);\n\n  const stopAutoUpdate = useCallback(() => {\n    if (updateIntervalRef.current) {\n      clearInterval(updateIntervalRef.current);\n      updateIntervalRef.current = null;\n    }\n  }, []);\n\n  // === CLEAR ERRORS ===\n  const clearErrors = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      playerError: null,\n      scanError: null,\n      exploitError: null,\n      connectionError: null,\n      transferError: null\n    }));\n  }, []);\n\n  // === EFFECTS ===\n  useEffect(() => {\n    // Carregar dados iniciais\n    loadPlayerData();\n    loadActiveConnections();\n    \n    // Iniciar atualização automática\n    startAutoUpdate();\n    \n    // Cleanup\n    return () => {\n      stopAutoUpdate();\n      gameService.cleanup();\n    };\n  }, []);\n\n  // Atualizar bruteforces em tempo real\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setState(prev => {\n        const newBruteforces = new Map(prev.activeBruteforces);\n        let hasChanges = false;\n        \n        newBruteforces.forEach((bruteforce, targetIp) => {\n          if (bruteforce.isRunning && bruteforce.timeRemaining > 0) {\n            bruteforce.timeRemaining--;\n            bruteforce.progress = ((bruteforce.totalTime - bruteforce.timeRemaining) / bruteforce.totalTime) * 100;\n            hasChanges = true;\n            \n            if (bruteforce.timeRemaining <= 0) {\n              bruteforce.isRunning = false;\n              // Atualizar conexão para liberar banco\n              const connectionIndex = prev.activeConnections.findIndex(conn => conn.targetIp === targetIp);\n              if (connectionIndex >= 0) {\n                prev.activeConnections[connectionIndex].bankAccess = true;\n              }\n            }\n          }\n        });\n        \n        return hasChanges ? { ...prev, activeBruteforces: newBruteforces } : prev;\n      });\n    }, 1000);\n    \n    return () => clearInterval(interval);\n  }, []);\n\n  return {\n    // State\n    ...state,\n    \n    // Actions\n    loadPlayerData,\n    performQuickScan,\n    performAdvancedScan,\n    performExploit,\n    loadActiveConnections,\n    startBruteforce,\n    closeConnection,\n    performBankTransfer,\n    clearErrors,\n    \n    // Utils\n    startAutoUpdate,\n    stopAutoUpdate\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAChE,OAAOC,WAAW,MAA2E,yBAAyB;AA+BtH,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAkB;IAClDS,aAAa,EAAE,IAAI;IACnBC,eAAe,EAAE,KAAK;IACtBC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,KAAK;IACnBC,YAAY,EAAE,IAAI;IAClBC,iBAAiB,EAAE,EAAE;IACrBC,oBAAoB,EAAE,KAAK;IAC3BC,eAAe,EAAE,IAAI;IACrBC,iBAAiB,EAAE,IAAIC,GAAG,CAAC,CAAC;IAC5BC,cAAc,EAAE,KAAK;IACrBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAGtB,MAAM,CAAwB,IAAI,CAAC;;EAE7D;EACA,MAAMuB,cAAc,GAAGxB,WAAW,CAAC,OAAOyB,YAAY,GAAG,KAAK,KAAK;IACjEnB,QAAQ,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElB,eAAe,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAK,CAAC,CAAC,CAAC;IAEzE,IAAI;MACF,MAAMkB,UAAU,GAAG,MAAMzB,WAAW,CAACsB,cAAc,CAACC,YAAY,CAAC;MACjEnB,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPnB,aAAa,EAAEoB,UAAU;QACzBnB,eAAe,EAAE;MACnB,CAAC,CAAC,CAAC;MACH,OAAOmB,UAAU;IACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdtB,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPjB,WAAW,EAAEmB,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG,wBAAwB;QAC9EtB,eAAe,EAAE;MACnB,CAAC,CAAC,CAAC;MACH,MAAMoB,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,gBAAgB,GAAG/B,WAAW,CAAC,YAAY;IAC/CM,QAAQ,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEf,UAAU,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAElE,IAAI;MACF,MAAMoB,OAAO,GAAG,MAAM9B,WAAW,CAAC6B,gBAAgB,CAAC,CAAC;MACpDzB,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPhB,WAAW,EAAEsB,OAAO;QACpBrB,UAAU,EAAE;MACd,CAAC,CAAC,CAAC;MACH,OAAOqB,OAAO;IAChB,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdtB,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPd,SAAS,EAAEgB,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG,cAAc;QAClEnB,UAAU,EAAE;MACd,CAAC,CAAC,CAAC;MACH,MAAMiB,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,mBAAmB,GAAGjC,WAAW,CAAC,MAAOkC,QAAgB,IAAK;IAClE5B,QAAQ,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEf,UAAU,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAElE,IAAI;MACF,MAAMoB,OAAO,GAAG,MAAM9B,WAAW,CAAC+B,mBAAmB,CAACC,QAAQ,CAAC;MAC/D5B,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPhB,WAAW,EAAEsB,OAAO;QACpBrB,UAAU,EAAE;MACd,CAAC,CAAC,CAAC;MACH,OAAOqB,OAAO;IAChB,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdtB,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPd,SAAS,EAAEgB,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG,cAAc;QAClEnB,UAAU,EAAE;MACd,CAAC,CAAC,CAAC;MACH,MAAMiB,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,cAAc,GAAGnC,WAAW,CAAC,MAAOoC,MAAkB,IAAK;IAC/D9B,QAAQ,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEZ,YAAY,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAK,CAAC,CAAC,CAAC;IAEvE,IAAI;MACF,MAAMF,eAAe,GAAG,MAAMX,WAAW,CAACiC,cAAc,CAACC,MAAM,CAAC;MAChE9B,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPb,eAAe;QACfC,YAAY,EAAE;MAChB,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMuB,qBAAqB,CAAC,CAAC;MAE7B,OAAOxB,eAAe;IACxB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdtB,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPX,YAAY,EAAEa,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG,iBAAiB;QACxEhB,YAAY,EAAE;MAChB,CAAC,CAAC,CAAC;MACH,MAAMc,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,qBAAqB,GAAGrC,WAAW,CAAC,YAAY;IACpDM,QAAQ,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAET,oBAAoB,EAAE,IAAI;MAAEC,eAAe,EAAE;IAAK,CAAC,CAAC,CAAC;IAElF,IAAI;MACF,MAAMoB,WAAW,GAAG,MAAMpC,WAAW,CAACqC,oBAAoB,CAAC,CAAC;MAC5DjC,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPV,iBAAiB,EAAEsB,WAAW;QAC9BrB,oBAAoB,EAAE;MACxB,CAAC,CAAC,CAAC;MACH,OAAOqB,WAAW;IACpB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdtB,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPR,eAAe,EAAEU,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG,2BAA2B;QACrFb,oBAAoB,EAAE;MACxB,CAAC,CAAC,CAAC;MACH,MAAMW,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,eAAe,GAAGxC,WAAW,CAAC,OAAOyC,YAAoB,EAAEP,QAAgB,KAAK;IACpF,IAAI;MACF,MAAMQ,gBAAgB,GAAG,MAAMxC,WAAW,CAACsC,eAAe,CAACC,YAAY,EAAEP,QAAQ,CAAC;MAElF5B,QAAQ,CAACoB,IAAI,IAAI;QACf,MAAMiB,cAAc,GAAG,IAAIvB,GAAG,CAACM,IAAI,CAACP,iBAAiB,CAAC;QACtDwB,cAAc,CAACC,GAAG,CAACV,QAAQ,EAAEQ,gBAAgB,CAAC;QAC9C,OAAO;UAAE,GAAGhB,IAAI;UAAEP,iBAAiB,EAAEwB;QAAe,CAAC;MACvD,CAAC,CAAC;MAEF,OAAOD,gBAAgB;IACzB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkB,eAAe,GAAG9C,WAAW,CAAC,OAAOyC,YAAoB,EAAEP,QAAgB,KAAK;IACpF,IAAI;MACF,MAAMhC,WAAW,CAAC4C,eAAe,CAACL,YAAY,EAAEP,QAAQ,CAAC;;MAEzD;MACA5B,QAAQ,CAACoB,IAAI,IAAI;QAAA,IAAAqB,qBAAA;QACf,MAAMC,cAAc,GAAGtB,IAAI,CAACV,iBAAiB,CAACiC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAChB,QAAQ,KAAKA,QAAQ,CAAC;QACxF,MAAMS,cAAc,GAAG,IAAIvB,GAAG,CAACM,IAAI,CAACP,iBAAiB,CAAC;QACtDwB,cAAc,CAACQ,MAAM,CAACjB,QAAQ,CAAC;QAE/B,OAAO;UACL,GAAGR,IAAI;UACPV,iBAAiB,EAAEgC,cAAc;UACjC7B,iBAAiB,EAAEwB,cAAc;UACjC9B,eAAe,EAAE,EAAAkC,qBAAA,GAAArB,IAAI,CAACb,eAAe,cAAAkC,qBAAA,uBAApBA,qBAAA,CAAsBK,EAAE,MAAKlB,QAAQ,GAAG,IAAI,GAAGR,IAAI,CAACb;QACvE,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyB,mBAAmB,GAAGrD,WAAW,CAAC,OAAOkC,QAAgB,EAAEoB,UAAkB,KAAK;IACtFhD,QAAQ,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEL,cAAc,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IAE1E,IAAI;MACF,MAAMiC,MAAM,GAAG,MAAMrD,WAAW,CAACmD,mBAAmB,CAACnB,QAAQ,EAAEoB,UAAU,CAAC;MAC1EhD,QAAQ,CAACoB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,cAAc,EAAE;MAAM,CAAC,CAAC,CAAC;;MAEtD;MACA,MAAMG,cAAc,CAAC,IAAI,CAAC;MAE1B,OAAO+B,MAAM;IACf,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdtB,QAAQ,CAACoB,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPJ,aAAa,EAAEM,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG,uBAAuB;QAC/ET,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;MACH,MAAMO,KAAK;IACb;EACF,CAAC,EAAE,CAACJ,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMgC,eAAe,GAAGxD,WAAW,CAAC,MAAM;IACxC,IAAIuB,iBAAiB,CAACkC,OAAO,EAAE;MAC7BC,aAAa,CAACnC,iBAAiB,CAACkC,OAAO,CAAC;IAC1C;IAEAlC,iBAAiB,CAACkC,OAAO,GAAGE,WAAW,CAAC,YAAY;MAClD,IAAI;QACF;QACA,MAAMnC,cAAc,CAAC,CAAC;;QAEtB;QACA,IAAInB,KAAK,CAACW,iBAAiB,CAAC4C,MAAM,GAAG,CAAC,EAAE;UACtC,MAAMvB,qBAAqB,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC,OAAOT,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACb,CAAC,EAAE,CAACJ,cAAc,EAAEa,qBAAqB,EAAEhC,KAAK,CAACW,iBAAiB,CAAC4C,MAAM,CAAC,CAAC;EAE3E,MAAMC,cAAc,GAAG7D,WAAW,CAAC,MAAM;IACvC,IAAIuB,iBAAiB,CAACkC,OAAO,EAAE;MAC7BC,aAAa,CAACnC,iBAAiB,CAACkC,OAAO,CAAC;MACxClC,iBAAiB,CAACkC,OAAO,GAAG,IAAI;IAClC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,WAAW,GAAG9D,WAAW,CAAC,MAAM;IACpCM,QAAQ,CAACoB,IAAI,KAAK;MAChB,GAAGA,IAAI;MACPjB,WAAW,EAAE,IAAI;MACjBG,SAAS,EAAE,IAAI;MACfG,YAAY,EAAE,IAAI;MAClBG,eAAe,EAAE,IAAI;MACrBI,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvB,SAAS,CAAC,MAAM;IACd;IACAyB,cAAc,CAAC,CAAC;IAChBa,qBAAqB,CAAC,CAAC;;IAEvB;IACAmB,eAAe,CAAC,CAAC;;IAEjB;IACA,OAAO,MAAM;MACXK,cAAc,CAAC,CAAC;MAChB3D,WAAW,CAAC6D,OAAO,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhE,SAAS,CAAC,MAAM;IACd,MAAMiE,QAAQ,GAAGL,WAAW,CAAC,MAAM;MACjCrD,QAAQ,CAACoB,IAAI,IAAI;QACf,MAAMiB,cAAc,GAAG,IAAIvB,GAAG,CAACM,IAAI,CAACP,iBAAiB,CAAC;QACtD,IAAI8C,UAAU,GAAG,KAAK;QAEtBtB,cAAc,CAACuB,OAAO,CAAC,CAACC,UAAU,EAAEjC,QAAQ,KAAK;UAC/C,IAAIiC,UAAU,CAACC,SAAS,IAAID,UAAU,CAACE,aAAa,GAAG,CAAC,EAAE;YACxDF,UAAU,CAACE,aAAa,EAAE;YAC1BF,UAAU,CAACG,QAAQ,GAAI,CAACH,UAAU,CAACI,SAAS,GAAGJ,UAAU,CAACE,aAAa,IAAIF,UAAU,CAACI,SAAS,GAAI,GAAG;YACtGN,UAAU,GAAG,IAAI;YAEjB,IAAIE,UAAU,CAACE,aAAa,IAAI,CAAC,EAAE;cACjCF,UAAU,CAACC,SAAS,GAAG,KAAK;cAC5B;cACA,MAAMI,eAAe,GAAG9C,IAAI,CAACV,iBAAiB,CAACyD,SAAS,CAACvB,IAAI,IAAIA,IAAI,CAAChB,QAAQ,KAAKA,QAAQ,CAAC;cAC5F,IAAIsC,eAAe,IAAI,CAAC,EAAE;gBACxB9C,IAAI,CAACV,iBAAiB,CAACwD,eAAe,CAAC,CAACE,UAAU,GAAG,IAAI;cAC3D;YACF;UACF;QACF,CAAC,CAAC;QAEF,OAAOT,UAAU,GAAG;UAAE,GAAGvC,IAAI;UAAEP,iBAAiB,EAAEwB;QAAe,CAAC,GAAGjB,IAAI;MAC3E,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMgC,aAAa,CAACM,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL;IACA,GAAG3D,KAAK;IAER;IACAmB,cAAc;IACdO,gBAAgB;IAChBE,mBAAmB;IACnBE,cAAc;IACdE,qBAAqB;IACrBG,eAAe;IACfM,eAAe;IACfO,mBAAmB;IACnBS,WAAW;IAEX;IACAN,eAAe;IACfK;EACF,CAAC;AACH,CAAC;AAACzD,EAAA,CA9SWD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}