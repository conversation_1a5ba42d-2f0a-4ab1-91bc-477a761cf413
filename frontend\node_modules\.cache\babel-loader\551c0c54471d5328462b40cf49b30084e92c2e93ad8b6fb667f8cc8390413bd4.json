{"ast": null, "code": "// Serviço para APIs do jogo que se conecta ao Flask backend\nclass GameApiService {\n  constructor() {\n    this.baseURL = 'http://localhost:5000';\n  }\n  async makeRequest(endpoint, options = {}) {\n    const token = localStorage.getItem('token');\n    const defaultHeaders = {\n      'Content-Type': 'application/json',\n      ...(token && {\n        'Authorization': `Bearer ${token}`\n      })\n    };\n    const config = {\n      ...options,\n      headers: {\n        ...defaultHeaders,\n        ...options.headers\n      }\n    };\n    try {\n      console.log(`🌐 Fazendo requisição para: ${this.baseURL}${endpoint}`);\n      const response = await fetch(`${this.baseURL}${endpoint}`, config);\n      console.log(`📡 Resposta recebida: ${response.status} ${response.statusText}`);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n      const data = await response.json();\n      console.log('📦 Dados recebidos:', data);\n      return data;\n    } catch (error) {\n      console.error(`❌ Erro na requisição para ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  // === AUTENTICAÇÃO ===\n  async login(credentials) {\n    return this.makeRequest('/api/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials)\n    });\n  }\n  async checkAuth() {\n    return this.makeRequest('/api/auth/check');\n  }\n\n  // === SCANNER ===\n  async scanTargets() {\n    return this.makeRequest('/api/scan');\n  }\n  async scanSpecificIP(ip) {\n    return this.makeRequest(`/api/scan/ip/${ip}`);\n  }\n\n  // === EXPLOITS ===\n  async exploitTarget(ip) {\n    return this.makeRequest(`/api/alvo/${ip}/exploit`, {\n      method: 'POST'\n    });\n  }\n\n  // === DADOS DO JOGADOR ===\n  async getPlayerData() {\n    return this.makeRequest('/api/jogador');\n  }\n\n  // === CHAT ===\n  async getChatMessages() {\n    return this.makeRequest('/api/chat/mensagens');\n  }\n  async sendChatMessage(message) {\n    return this.makeRequest('/api/chat/enviar', {\n      method: 'POST',\n      body: JSON.stringify({\n        mensagem: message\n      })\n    });\n  }\n\n  // === TRANSFERÊNCIAS ===\n  async transferPoints(targetNick, amount) {\n    return this.makeRequest('/api/transferir', {\n      method: 'POST',\n      body: JSON.stringify({\n        alvo_nick: targetNick,\n        quantidade: amount\n      })\n    });\n  }\n\n  // === LOJA ===\n  async getShopItems() {\n    return this.makeRequest('/api/shop/items');\n  }\n  async buyShopItem(itemId) {\n    return this.makeRequest('/api/shop/comprar', {\n      method: 'POST',\n      body: JSON.stringify({\n        item_id: itemId\n      })\n    });\n  }\n\n  // === UPGRADES ===\n  async upgradeItem(item, quantity = 1) {\n    return this.makeRequest('/api/upgrade', {\n      method: 'POST',\n      body: JSON.stringify({\n        item: item,\n        quantidade: quantity\n      })\n    });\n  }\n\n  // === RANKING ===\n  async getRanking() {\n    return this.makeRequest('/api/ranking');\n  }\n\n  // === LOGS ===\n  async getLogs() {\n    return this.makeRequest('/api/logs');\n  }\n\n  // === TESTE DE CONECTIVIDADE ===\n  async testConnection() {\n    try {\n      const response = await fetch(`${this.baseURL}/`);\n      const data = await response.json();\n      return {\n        success: true,\n        data\n      };\n    } catch (error) {\n      console.error('❌ Erro de conectividade:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  }\n}\nexport const gameApi = new GameApiService();\nexport default gameApi;", "map": {"version": 3, "names": ["GameApiService", "constructor", "baseURL", "makeRequest", "endpoint", "options", "token", "localStorage", "getItem", "defaultHeaders", "config", "headers", "console", "log", "response", "fetch", "status", "statusText", "ok", "Error", "data", "json", "error", "login", "credentials", "method", "body", "JSON", "stringify", "checkAuth", "scanTargets", "scanSpecificIP", "ip", "exploitTarget", "getPlayerData", "getChatMessages", "sendChatMessage", "message", "mensagem", "transferPoints", "targetNick", "amount", "alvo_nick", "quantidade", "getShopItems", "buyShopItem", "itemId", "item_id", "upgradeItem", "item", "quantity", "getRanking", "getLogs", "testConnection", "success", "gameApi"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/gameApi.ts"], "sourcesContent": ["// Serviço para APIs do jogo que se conecta ao Flask backend\nclass GameApiService {\n  private baseURL = 'http://localhost:5000';\n  \n  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {\n    const token = localStorage.getItem('token');\n    \n    const defaultHeaders = {\n      'Content-Type': 'application/json',\n      ...(token && { 'Authorization': `Bearer ${token}` }),\n    };\n\n    const config: RequestInit = {\n      ...options,\n      headers: {\n        ...defaultHeaders,\n        ...options.headers,\n      },\n    };\n\n    try {\n      console.log(`🌐 Fazendo requisição para: ${this.baseURL}${endpoint}`);\n      \n      const response = await fetch(`${this.baseURL}${endpoint}`, config);\n      \n      console.log(`📡 Resposta recebida: ${response.status} ${response.statusText}`);\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      console.log('📦 Dados recebidos:', data);\n      \n      return data;\n    } catch (error) {\n      console.error(`❌ Erro na requisição para ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  // === AUTENTICAÇÃO ===\n  async login(credentials: { nick: string; password: string }) {\n    return this.makeRequest('/api/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    });\n  }\n\n  async checkAuth() {\n    return this.makeRequest('/api/auth/check');\n  }\n\n  // === SCANNER ===\n  async scanTargets() {\n    return this.makeRequest('/api/scan');\n  }\n\n  async scanSpecificIP(ip: string) {\n    return this.makeRequest(`/api/scan/ip/${ip}`);\n  }\n\n  // === EXPLOITS ===\n  async exploitTarget(ip: string) {\n    return this.makeRequest(`/api/alvo/${ip}/exploit`, {\n      method: 'POST',\n    });\n  }\n\n  // === DADOS DO JOGADOR ===\n  async getPlayerData() {\n    return this.makeRequest('/api/jogador');\n  }\n\n  // === CHAT ===\n  async getChatMessages() {\n    return this.makeRequest('/api/chat/mensagens');\n  }\n\n  async sendChatMessage(message: string) {\n    return this.makeRequest('/api/chat/enviar', {\n      method: 'POST',\n      body: JSON.stringify({ mensagem: message }),\n    });\n  }\n\n  // === TRANSFERÊNCIAS ===\n  async transferPoints(targetNick: string, amount: number) {\n    return this.makeRequest('/api/transferir', {\n      method: 'POST',\n      body: JSON.stringify({\n        alvo_nick: targetNick,\n        quantidade: amount,\n      }),\n    });\n  }\n\n  // === LOJA ===\n  async getShopItems() {\n    return this.makeRequest('/api/shop/items');\n  }\n\n  async buyShopItem(itemId: string) {\n    return this.makeRequest('/api/shop/comprar', {\n      method: 'POST',\n      body: JSON.stringify({ item_id: itemId }),\n    });\n  }\n\n  // === UPGRADES ===\n  async upgradeItem(item: string, quantity: number = 1) {\n    return this.makeRequest('/api/upgrade', {\n      method: 'POST',\n      body: JSON.stringify({\n        item: item,\n        quantidade: quantity,\n      }),\n    });\n  }\n\n  // === RANKING ===\n  async getRanking() {\n    return this.makeRequest('/api/ranking');\n  }\n\n  // === LOGS ===\n  async getLogs() {\n    return this.makeRequest('/api/logs');\n  }\n\n  // === TESTE DE CONECTIVIDADE ===\n  async testConnection() {\n    try {\n      const response = await fetch(`${this.baseURL}/`);\n      const data = await response.json();\n      return { success: true, data };\n    } catch (error) {\n      console.error('❌ Erro de conectividade:', error);\n      return { success: false, error };\n    }\n  }\n}\n\nexport const gameApi = new GameApiService();\nexport default gameApi;\n"], "mappings": "AAAA;AACA,MAAMA,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,OAAO,GAAG,uBAAuB;EAAA;EAEzC,MAAcC,WAAWA,CAACC,QAAgB,EAAEC,OAAoB,GAAG,CAAC,CAAC,EAAgB;IACnF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,MAAMC,cAAc,GAAG;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIH,KAAK,IAAI;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAG,CAAC;IACrD,CAAC;IAED,MAAMI,MAAmB,GAAG;MAC1B,GAAGL,OAAO;MACVM,OAAO,EAAE;QACP,GAAGF,cAAc;QACjB,GAAGJ,OAAO,CAACM;MACb;IACF,CAAC;IAED,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,+BAA+B,IAAI,CAACX,OAAO,GAAGE,QAAQ,EAAE,CAAC;MAErE,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,GAAGE,QAAQ,EAAE,EAAEM,MAAM,CAAC;MAElEE,OAAO,CAACC,GAAG,CAAC,yBAAyBC,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACG,UAAU,EAAE,CAAC;MAE9E,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,QAAQL,QAAQ,CAACE,MAAM,KAAKF,QAAQ,CAACG,UAAU,EAAE,CAAC;MACpE;MAEA,MAAMG,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClCT,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,IAAI,CAAC;MAExC,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,6BAA6BlB,QAAQ,GAAG,EAAEkB,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMC,KAAKA,CAACC,WAA+C,EAAE;IAC3D,OAAO,IAAI,CAACrB,WAAW,CAAC,iBAAiB,EAAE;MACzCsB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACJ,WAAW;IAClC,CAAC,CAAC;EACJ;EAEA,MAAMK,SAASA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC1B,WAAW,CAAC,iBAAiB,CAAC;EAC5C;;EAEA;EACA,MAAM2B,WAAWA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC3B,WAAW,CAAC,WAAW,CAAC;EACtC;EAEA,MAAM4B,cAAcA,CAACC,EAAU,EAAE;IAC/B,OAAO,IAAI,CAAC7B,WAAW,CAAC,gBAAgB6B,EAAE,EAAE,CAAC;EAC/C;;EAEA;EACA,MAAMC,aAAaA,CAACD,EAAU,EAAE;IAC9B,OAAO,IAAI,CAAC7B,WAAW,CAAC,aAAa6B,EAAE,UAAU,EAAE;MACjDP,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMS,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC/B,WAAW,CAAC,cAAc,CAAC;EACzC;;EAEA;EACA,MAAMgC,eAAeA,CAAA,EAAG;IACtB,OAAO,IAAI,CAAChC,WAAW,CAAC,qBAAqB,CAAC;EAChD;EAEA,MAAMiC,eAAeA,CAACC,OAAe,EAAE;IACrC,OAAO,IAAI,CAAClC,WAAW,CAAC,kBAAkB,EAAE;MAC1CsB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEU,QAAQ,EAAED;MAAQ,CAAC;IAC5C,CAAC,CAAC;EACJ;;EAEA;EACA,MAAME,cAAcA,CAACC,UAAkB,EAAEC,MAAc,EAAE;IACvD,OAAO,IAAI,CAACtC,WAAW,CAAC,iBAAiB,EAAE;MACzCsB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBc,SAAS,EAAEF,UAAU;QACrBG,UAAU,EAAEF;MACd,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMG,YAAYA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACzC,WAAW,CAAC,iBAAiB,CAAC;EAC5C;EAEA,MAAM0C,WAAWA,CAACC,MAAc,EAAE;IAChC,OAAO,IAAI,CAAC3C,WAAW,CAAC,mBAAmB,EAAE;MAC3CsB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEmB,OAAO,EAAED;MAAO,CAAC;IAC1C,CAAC,CAAC;EACJ;;EAEA;EACA,MAAME,WAAWA,CAACC,IAAY,EAAEC,QAAgB,GAAG,CAAC,EAAE;IACpD,OAAO,IAAI,CAAC/C,WAAW,CAAC,cAAc,EAAE;MACtCsB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBqB,IAAI,EAAEA,IAAI;QACVN,UAAU,EAAEO;MACd,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,UAAUA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAChD,WAAW,CAAC,cAAc,CAAC;EACzC;;EAEA;EACA,MAAMiD,OAAOA,CAAA,EAAG;IACd,OAAO,IAAI,CAACjD,WAAW,CAAC,WAAW,CAAC;EACtC;;EAEA;EACA,MAAMkD,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAMvC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,GAAG,CAAC;MAChD,MAAMkB,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC,OAAO;QAAEiC,OAAO,EAAE,IAAI;QAAElC;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QAAEgC,OAAO,EAAE,KAAK;QAAEhC;MAAM,CAAC;IAClC;EACF;AACF;AAEA,OAAO,MAAMiC,OAAO,GAAG,IAAIvD,cAAc,CAAC,CAAC;AAC3C,eAAeuD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}