{"ast": null, "code": "var _s = $RefreshSig$();\nimport { create } from 'zustand';\nimport mockApiService from '../services/mockApi';\nexport const usePlayerStore = create((set, get) => ({\n  // Estado inicial\n  currentPlayer: null,\n  isLoadingPlayer: false,\n  playerError: null,\n  // Carregar dados do jogador\n  loadPlayerData: async () => {\n    const {\n      isLoadingPlayer\n    } = get();\n    if (isLoadingPlayer) {\n      console.log('PlayerStore - Carregamento já em andamento');\n      return;\n    }\n    console.log('PlayerStore - Carregando dados do jogador...');\n    set({\n      isLoadingPlayer: true,\n      playerError: null\n    });\n    try {\n      const response = await mockApiService.getPlayerData();\n      if (response.sucesso && response.dados) {\n        console.log('PlayerStore - Dados carregados:', response.dados);\n        set({\n          currentPlayer: response.dados,\n          isLoadingPlayer: false,\n          playerError: null\n        });\n      } else {\n        set({\n          isLoadingPlayer: false,\n          playerError: response.mensagem || 'Erro ao carregar dados'\n        });\n      }\n    } catch (error) {\n      console.error('PlayerStore - Erro:', error);\n      set({\n        isLoadingPlayer: false,\n        playerError: 'Erro de conexão'\n      });\n    }\n  },\n  // Atualizar estatísticas do jogador\n  updatePlayerStats: stats => {\n    const {\n      currentPlayer\n    } = get();\n    if (currentPlayer) {\n      console.log('PlayerStore - Atualizando stats:', stats);\n      set({\n        currentPlayer: {\n          ...currentPlayer,\n          ...stats\n        }\n      });\n    }\n  },\n  // Limpar erro\n  clearPlayerError: () => {\n    set({\n      playerError: null\n    });\n  }\n}));\n\n// Hook personalizado\nexport const usePlayer = () => {\n  _s();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    playerError,\n    loadPlayerData,\n    clearPlayerError,\n    updatePlayerStats\n  } = usePlayerStore();\n  return {\n    // Estado\n    currentPlayer,\n    isLoadingPlayer,\n    playerError,\n    // Ações\n    loadPlayerData,\n    clearPlayerError,\n    updatePlayerStats,\n    // Computed\n    hasPlayerData: !!currentPlayer,\n    playerLevel: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.nivel) || 0,\n    playerPoints: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.pontos) || 0,\n    playerRank: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.ranking) || 0\n  };\n};\n_s(usePlayer, \"qt8/7vDsx8KsVm9JAgkhtX2MCFw=\", false, function () {\n  return [usePlayerStore];\n});", "map": {"version": 3, "names": ["create", "mockApiService", "usePlayerStore", "set", "get", "currentPlayer", "isLoadingPlayer", "playerError", "loadPlayerData", "console", "log", "response", "getPlayerData", "sucesso", "dados", "mensagem", "error", "updatePlayerStats", "stats", "clearPlayerError", "usePlayer", "_s", "hasPlayerData", "playerLevel", "nivel", "playerPoints", "pontos", "<PERSON><PERSON><PERSON><PERSON>", "ranking"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/playerStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport mockApiService, { PlayerData } from '../services/mockApi';\n\ninterface PlayerState {\n  // Estado\n  currentPlayer: PlayerData | null;\n  isLoadingPlayer: boolean;\n  playerError: string | null;\n\n  // Ações\n  loadPlayerData: () => Promise<void>;\n  clearPlayerError: () => void;\n  updatePlayerStats: (stats: Partial<PlayerData>) => void;\n}\n\nexport const usePlayerStore = create<PlayerState>((set, get) => ({\n  // Estado inicial\n  currentPlayer: null,\n  isLoadingPlayer: false,\n  playerError: null,\n\n  // Carregar dados do jogador\n  loadPlayerData: async () => {\n    const { isLoadingPlayer } = get();\n    \n    if (isLoadingPlayer) {\n      console.log('PlayerStore - Carregamento já em andamento');\n      return;\n    }\n\n    console.log('PlayerStore - Carregando dados do jogador...');\n    set({ isLoadingPlayer: true, playerError: null });\n\n    try {\n      const response = await mockApiService.getPlayerData();\n      \n      if (response.sucesso && response.dados) {\n        console.log('PlayerStore - Dados carregados:', response.dados);\n        set({\n          currentPlayer: response.dados,\n          isLoadingPlayer: false,\n          playerError: null,\n        });\n      } else {\n        set({\n          isLoadingPlayer: false,\n          playerError: response.mensagem || 'Erro ao carregar dados',\n        });\n      }\n    } catch (error) {\n      console.error('PlayerStore - Erro:', error);\n      set({\n        isLoadingPlayer: false,\n        playerError: 'Erro de conexão',\n      });\n    }\n  },\n\n  // Atualizar estatísticas do jogador\n  updatePlayerStats: (stats: Partial<PlayerData>) => {\n    const { currentPlayer } = get();\n    \n    if (currentPlayer) {\n      console.log('PlayerStore - Atualizando stats:', stats);\n      set({\n        currentPlayer: {\n          ...currentPlayer,\n          ...stats,\n        },\n      });\n    }\n  },\n\n  // Limpar erro\n  clearPlayerError: () => {\n    set({ playerError: null });\n  },\n}));\n\n// Hook personalizado\nexport const usePlayer = () => {\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    playerError,\n    loadPlayerData,\n    clearPlayerError,\n    updatePlayerStats,\n  } = usePlayerStore();\n\n  return {\n    // Estado\n    currentPlayer,\n    isLoadingPlayer,\n    playerError,\n    \n    // Ações\n    loadPlayerData,\n    clearPlayerError,\n    updatePlayerStats,\n    \n    // Computed\n    hasPlayerData: !!currentPlayer,\n    playerLevel: currentPlayer?.nivel || 0,\n    playerPoints: currentPlayer?.pontos || 0,\n    playerRank: currentPlayer?.ranking || 0,\n  };\n};\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,OAAOC,cAAc,MAAsB,qBAAqB;AAchE,OAAO,MAAMC,cAAc,GAAGF,MAAM,CAAc,CAACG,GAAG,EAAEC,GAAG,MAAM;EAC/D;EACAC,aAAa,EAAE,IAAI;EACnBC,eAAe,EAAE,KAAK;EACtBC,WAAW,EAAE,IAAI;EAEjB;EACAC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,MAAM;MAAEF;IAAgB,CAAC,GAAGF,GAAG,CAAC,CAAC;IAEjC,IAAIE,eAAe,EAAE;MACnBG,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAC3DP,GAAG,CAAC;MAAEG,eAAe,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAK,CAAC,CAAC;IAEjD,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMV,cAAc,CAACW,aAAa,CAAC,CAAC;MAErD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,KAAK,EAAE;QACtCL,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAACG,KAAK,CAAC;QAC9DX,GAAG,CAAC;UACFE,aAAa,EAAEM,QAAQ,CAACG,KAAK;UAC7BR,eAAe,EAAE,KAAK;UACtBC,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC,MAAM;QACLJ,GAAG,CAAC;UACFG,eAAe,EAAE,KAAK;UACtBC,WAAW,EAAEI,QAAQ,CAACI,QAAQ,IAAI;QACpC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3Cb,GAAG,CAAC;QACFG,eAAe,EAAE,KAAK;QACtBC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAU,iBAAiB,EAAGC,KAA0B,IAAK;IACjD,MAAM;MAAEb;IAAc,CAAC,GAAGD,GAAG,CAAC,CAAC;IAE/B,IAAIC,aAAa,EAAE;MACjBI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEQ,KAAK,CAAC;MACtDf,GAAG,CAAC;QACFE,aAAa,EAAE;UACb,GAAGA,aAAa;UAChB,GAAGa;QACL;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAC,gBAAgB,EAAEA,CAAA,KAAM;IACtBhB,GAAG,CAAC;MAAEI,WAAW,EAAE;IAAK,CAAC,CAAC;EAC5B;AACF,CAAC,CAAC,CAAC;;AAEH;AACA,OAAO,MAAMa,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IACJhB,aAAa;IACbC,eAAe;IACfC,WAAW;IACXC,cAAc;IACdW,gBAAgB;IAChBF;EACF,CAAC,GAAGf,cAAc,CAAC,CAAC;EAEpB,OAAO;IACL;IACAG,aAAa;IACbC,eAAe;IACfC,WAAW;IAEX;IACAC,cAAc;IACdW,gBAAgB;IAChBF,iBAAiB;IAEjB;IACAK,aAAa,EAAE,CAAC,CAACjB,aAAa;IAC9BkB,WAAW,EAAE,CAAAlB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEmB,KAAK,KAAI,CAAC;IACtCC,YAAY,EAAE,CAAApB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqB,MAAM,KAAI,CAAC;IACxCC,UAAU,EAAE,CAAAtB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuB,OAAO,KAAI;EACxC,CAAC;AACH,CAAC;AAACP,EAAA,CA3BWD,SAAS;EAAA,QAQhBlB,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}