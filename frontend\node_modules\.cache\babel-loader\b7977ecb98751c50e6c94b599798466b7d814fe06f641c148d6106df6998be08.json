{"ast": null, "code": "// src/index.ts\nimport { CancelledError } from \"./retryer.js\";\nimport { QueryCache } from \"./queryCache.js\";\nimport { QueryClient } from \"./queryClient.js\";\nimport { QueryObserver } from \"./queryObserver.js\";\nimport { QueriesObserver } from \"./queriesObserver.js\";\nimport { InfiniteQueryObserver } from \"./infiniteQueryObserver.js\";\nimport { MutationCache } from \"./mutationCache.js\";\nimport { MutationObserver } from \"./mutationObserver.js\";\nimport { notifyManager, defaultScheduler } from \"./notifyManager.js\";\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { hashKey, partialMatchKey, replaceEqualDeep, isServer, matchQuery, matchMutation, keepPreviousData, skipToken, noop, shouldThrowError } from \"./utils.js\";\nimport { isCancelledError } from \"./retryer.js\";\nimport { dehydrate, hydrate, defaultShouldDehydrateQuery, defaultShouldDehydrateMutation } from \"./hydration.js\";\nimport { streamedQuery } from \"./streamedQuery.js\";\nexport * from \"./types.js\";\nimport { Query } from \"./query.js\";\nimport { Mutation } from \"./mutation.js\";\nexport { CancelledError, InfiniteQueryObserver, Mutation, MutationCache, MutationObserver, QueriesObserver, Query, QueryCache, QueryClient, QueryObserver, defaultScheduler, defaultShouldDehydrateMutation, defaultShouldDehydrateQuery, dehydrate, streamedQuery as experimental_streamedQuery, focusManager, hashKey, hydrate, isCancelledError, isServer, keepPreviousData, matchMutation, matchQuery, noop, notifyManager, onlineManager, partialMatchKey, replaceEqualDeep, shouldThrowError, skipToken };", "map": {"version": 3, "names": ["CancelledError", "Query<PERSON>ache", "QueryClient", "QueryObserver", "QueriesObserver", "InfiniteQueryObserver", "MutationCache", "MutationObserver", "notify<PERSON><PERSON>ger", "defaultScheduler", "focusManager", "onlineManager", "hash<PERSON><PERSON>", "partialMatchKey", "replaceEqualDeep", "isServer", "matchQuery", "matchMutation", "keepPreviousData", "skipToken", "noop", "shouldThrowError", "isCancelledError", "dehydrate", "hydrate", "defaultShouldDehydrateQuery", "defaultShouldDehydrateMutation", "streamedQuery", "Query", "Mutation"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\query-core\\src\\index.ts"], "sourcesContent": ["/* istanbul ignore file */\n\nexport { CancelledError } from './retryer'\nexport { QueryCache } from './queryCache'\nexport type { QueryCacheNotifyEvent } from './queryCache'\nexport { QueryClient } from './queryClient'\nexport { QueryObserver } from './queryObserver'\nexport { QueriesObserver } from './queriesObserver'\nexport { InfiniteQueryObserver } from './infiniteQueryObserver'\nexport { MutationCache } from './mutationCache'\nexport type { MutationCacheNotifyEvent } from './mutationCache'\nexport { MutationObserver } from './mutationObserver'\nexport { notifyManager, defaultScheduler } from './notifyManager'\nexport { focusManager } from './focusManager'\nexport { onlineManager } from './onlineManager'\nexport {\n  hashKey,\n  partialMatchKey,\n  replaceEqualDeep,\n  isServer,\n  matchQuery,\n  matchMutation,\n  keepPreviousData,\n  skipToken,\n  noop,\n  shouldThrowError,\n} from './utils'\nexport type { MutationFilters, QueryFilters, Updater, SkipToken } from './utils'\nexport { isCancelledError } from './retryer'\nexport {\n  dehydrate,\n  hydrate,\n  defaultShouldDehydrateQuery,\n  defaultShouldDehydrateMutation,\n} from './hydration'\n\nexport { streamedQuery as experimental_streamedQuery } from './streamedQuery'\n\n// Types\nexport * from './types'\nexport type { QueryState } from './query'\nexport { Query } from './query'\nexport type { MutationState } from './mutation'\nexport { Mutation } from './mutation'\nexport type {\n  DehydrateOptions,\n  DehydratedState,\n  HydrateOptions,\n} from './hydration'\nexport type { QueriesObserverOptions } from './queriesObserver'\n"], "mappings": ";AAEA,SAASA,cAAA,QAAsB;AAC/B,SAASC,UAAA,QAAkB;AAE3B,SAASC,WAAA,QAAmB;AAC5B,SAASC,aAAA,QAAqB;AAC9B,SAASC,eAAA,QAAuB;AAChC,SAASC,qBAAA,QAA6B;AACtC,SAASC,aAAA,QAAqB;AAE9B,SAASC,gBAAA,QAAwB;AACjC,SAASC,aAAA,EAAeC,gBAAA,QAAwB;AAChD,SAASC,YAAA,QAAoB;AAC7B,SAASC,aAAA,QAAqB;AAC9B,SACEC,OAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,QAAA,EACAC,UAAA,EACAC,aAAA,EACAC,gBAAA,EACAC,SAAA,EACAC,IAAA,EACAC,gBAAA,QACK;AAEP,SAASC,gBAAA,QAAwB;AACjC,SACEC,SAAA,EACAC,OAAA,EACAC,2BAAA,EACAC,8BAAA,QACK;AAEP,SAA0BC,aAAA,QAAkC;AAG5D,cAAc;AAEd,SAASC,KAAA,QAAa;AAEtB,SAASC,QAAA,QAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}