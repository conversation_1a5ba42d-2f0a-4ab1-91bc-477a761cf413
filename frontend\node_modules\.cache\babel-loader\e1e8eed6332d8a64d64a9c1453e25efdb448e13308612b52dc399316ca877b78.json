{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\n\n// Componentes\nimport AuthGuard from './components/auth/AuthGuard';\nimport LoginPage from './pages/LoginPage';\nimport GamePage from './pages/GamePage';\nimport NotificationSystem from './components/common/NotificationSystem';\n\n// Estilos\nimport './styles/globals.css';\n\n// Configuração do React Query\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000 // 5 minutos\n    }\n  }\n});\nfunction App() {\n  console.log('App - Renderizando...');\n  return /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-bg-primary text-text-primary\",\n        children: [/*#__PURE__*/_jsxDEV(NotificationSystem, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/game/*\",\n            element: /*#__PURE__*/_jsxDEV(AuthGuard, {\n              children: /*#__PURE__*/_jsxDEV(GamePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(AuthGuard, {\n              children: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/game\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "QueryClient", "QueryClientProvider", "<PERSON><PERSON><PERSON><PERSON>", "LoginPage", "GamePage", "NotificationSystem", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "App", "console", "log", "client", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\n\n// Componentes\nimport AuthGuard from './components/auth/AuthGuard';\nimport LoginPage from './pages/LoginPage';\nimport GamePage from './pages/GamePage';\nimport NotificationSystem from './components/common/NotificationSystem';\n\n// Estilos\nimport './styles/globals.css';\n\n// Configuração do React Query\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5 minutos\n    },\n  },\n});\n\nfunction App() {\n  console.log('App - Renderizando...');\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <Router>\n        <div className=\"min-h-screen bg-bg-primary text-text-primary\">\n          {/* Sistema de notificações global */}\n          <NotificationSystem />\n\n          {/* Rotas da aplicação */}\n          <Routes>\n            {/* Rota de login */}\n            <Route path=\"/login\" element={<LoginPage />} />\n\n            {/* Rotas protegidas do jogo */}\n            <Route\n              path=\"/game/*\"\n              element={\n                <AuthGuard>\n                  <GamePage />\n                </AuthGuard>\n              }\n            />\n\n            {/* Rota raiz - redireciona para o jogo */}\n            <Route\n              path=\"/\"\n              element={\n                <AuthGuard>\n                  <Navigate to=\"/game\" replace />\n                </AuthGuard>\n              }\n            />\n\n            {/* Rota 404 - redireciona para o jogo */}\n            <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n          </Routes>\n        </div>\n      </Router>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;;AAExE;AACA,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,kBAAkB,MAAM,wCAAwC;;AAEvE;AACA,OAAO,sBAAsB;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIR,WAAW,CAAC;EAClCS,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;IAC5B;EACF;AACF,CAAC,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACbC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EAEpC,oBACET,OAAA,CAACN,mBAAmB;IAACgB,MAAM,EAAET,WAAY;IAAAU,QAAA,eACvCX,OAAA,CAACX,MAAM;MAAAsB,QAAA,eACLX,OAAA;QAAKY,SAAS,EAAC,8CAA8C;QAAAD,QAAA,gBAE3DX,OAAA,CAACF,kBAAkB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtBhB,OAAA,CAACV,MAAM;UAAAqB,QAAA,gBAELX,OAAA,CAACT,KAAK;YAAC0B,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAElB,OAAA,CAACJ,SAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG/ChB,OAAA,CAACT,KAAK;YACJ0B,IAAI,EAAC,SAAS;YACdC,OAAO,eACLlB,OAAA,CAACL,SAAS;cAAAgB,QAAA,eACRX,OAAA,CAACH,QAAQ;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACZ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFhB,OAAA,CAACT,KAAK;YACJ0B,IAAI,EAAC,GAAG;YACRC,OAAO,eACLlB,OAAA,CAACL,SAAS;cAAAgB,QAAA,eACRX,OAAA,CAACR,QAAQ;gBAAC2B,EAAE,EAAC,OAAO;gBAACC,OAAO;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UACZ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFhB,OAAA,CAACT,KAAK;YAAC0B,IAAI,EAAC,GAAG;YAACC,OAAO,eAAElB,OAAA,CAACR,QAAQ;cAAC2B,EAAE,EAAC,GAAG;cAACC,OAAO;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAE1B;AAACK,EAAA,GA1CQd,GAAG;AA4CZ,eAAeA,GAAG;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}