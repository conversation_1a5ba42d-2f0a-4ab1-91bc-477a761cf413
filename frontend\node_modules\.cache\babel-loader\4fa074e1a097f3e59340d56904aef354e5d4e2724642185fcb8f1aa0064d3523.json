{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\ShopPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ShopPage = () => {\n  _s();\n  var _currentPlayer$dinhei, _currentPlayer$shack;\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    currentPlayer,\n    loadPlayerData\n  } = usePlayer();\n  const [shopItems, setShopItems] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [isLoading, setIsLoading] = useState(false);\n  const [purchaseError, setPurchaseError] = useState(null);\n  const [purchaseSuccess, setPurchaseSuccess] = useState(null);\n  const [purchasingItem, setPurchasingItem] = useState(null);\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadShopItems();\n    }\n  }, [isAuthenticated]);\n  const loadShopItems = async () => {\n    setIsLoading(true);\n    try {\n      // Tentar carregar do backend primeiro\n      const response = await gameApi.getShopItems();\n      if (response.sucesso && response.items) {\n        setShopItems(response.items);\n      } else {\n        // Fallback para itens mockados\n        loadMockItems();\n      }\n    } catch (error) {\n      console.error('Erro ao carregar loja:', error);\n      // Fallback para itens mockados\n      loadMockItems();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const loadMockItems = () => {\n    const mockItems = [\n    // Hardware\n    {\n      id: 'cpu_upgrade',\n      name: 'cpu_upgrade',\n      displayName: 'CPU Upgrade Kit',\n      icon: '🖥️',\n      description: 'Kit para upgrade de CPU (+5 níveis)',\n      price: {\n        dinheiro: 50000\n      },\n      category: 'hardware',\n      available: true\n    }, {\n      id: 'ram_upgrade',\n      name: 'ram_upgrade',\n      displayName: 'RAM Upgrade Kit',\n      icon: '💾',\n      description: 'Kit para upgrade de RAM (+5 níveis)',\n      price: {\n        dinheiro: 40000\n      },\n      category: 'hardware',\n      available: true\n    }, {\n      id: 'firewall_pro',\n      name: 'firewall_pro',\n      displayName: 'Firewall Pro',\n      icon: '🛡️',\n      description: 'Firewall profissional (+10 níveis)',\n      price: {\n        dinheiro: 75000,\n        shacks: 50\n      },\n      category: 'software',\n      available: true\n    },\n    // Software\n    {\n      id: 'antivirus_premium',\n      name: 'antivirus_premium',\n      displayName: 'Antivírus Premium',\n      icon: '🦠',\n      description: 'Antivírus de última geração (+10 níveis)',\n      price: {\n        dinheiro: 60000,\n        shacks: 30\n      },\n      category: 'software',\n      available: true\n    }, {\n      id: 'malware_toolkit',\n      name: 'malware_toolkit',\n      displayName: 'Malware Toolkit',\n      icon: '🔧',\n      description: 'Kit completo de ferramentas malware (+15 níveis)',\n      price: {\n        dinheiro: 100000,\n        shacks: 75\n      },\n      category: 'tools',\n      available: true\n    },\n    // Tools\n    {\n      id: 'bruteforce_accelerator',\n      name: 'bruteforce_accelerator',\n      displayName: 'BruteForce Accelerator',\n      icon: '🔨',\n      description: 'Acelera ataques de força bruta (+20 níveis)',\n      price: {\n        dinheiro: 150000,\n        shacks: 100\n      },\n      category: 'tools',\n      available: true\n    }, {\n      id: 'proxy_network',\n      name: 'proxy_network',\n      displayName: 'Proxy Network',\n      icon: '🌐',\n      description: 'Rede de proxies premium (+25 níveis)',\n      price: {\n        dinheiro: 200000,\n        shacks: 150\n      },\n      category: 'tools',\n      available: true\n    },\n    // Special\n    {\n      id: 'hacker_bundle',\n      name: 'hacker_bundle',\n      displayName: 'Hacker Bundle',\n      icon: '💎',\n      description: 'Pacote completo do hacker (+50 todos os itens)',\n      price: {\n        dinheiro: 1000000,\n        shacks: 500\n      },\n      category: 'special',\n      available: true\n    }, {\n      id: 'premium_account',\n      name: 'premium_account',\n      displayName: 'Conta Premium',\n      icon: '👑',\n      description: 'Acesso premium por 30 dias (bônus +100%)',\n      price: {\n        shacks: 1000\n      },\n      category: 'special',\n      available: true\n    }];\n    setShopItems(mockItems);\n  };\n  const handlePurchase = async item => {\n    if (!currentPlayer) return;\n\n    // Verificar se tem recursos suficientes\n    if (item.price.dinheiro && ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.dinheiro) || 0) < item.price.dinheiro) {\n      setPurchaseError('Dinheiro insuficiente');\n      return;\n    }\n    if (item.price.shacks && ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.shack) || 0) < item.price.shacks) {\n      setPurchaseError('Shacks insuficientes');\n      return;\n    }\n    setPurchasingItem(item.id);\n    setPurchaseError(null);\n    setPurchaseSuccess(null);\n    try {\n      const response = await gameApi.buyShopItem(item.id);\n      if (response.sucesso) {\n        setPurchaseSuccess(`${item.displayName} comprado com sucesso!`);\n\n        // Recarregar dados do jogador\n        await loadPlayerData();\n      } else {\n        setPurchaseError(response.mensagem || 'Erro na compra');\n      }\n    } catch (error) {\n      setPurchaseError(error.message || 'Erro de conexão');\n    } finally {\n      setPurchasingItem(null);\n    }\n  };\n  const filteredItems = selectedCategory === 'all' ? shopItems : shopItems.filter(item => item.category === selectedCategory);\n  const categories = [{\n    id: 'all',\n    name: 'Todos',\n    icon: '🛍️'\n  }, {\n    id: 'hardware',\n    name: 'Hardware',\n    icon: '🖥️'\n  }, {\n    id: 'software',\n    name: 'Software',\n    icon: '💿'\n  }, {\n    id: 'tools',\n    name: 'Ferramentas',\n    icon: '🔧'\n  }, {\n    id: 'special',\n    name: 'Especiais',\n    icon: '💎'\n  }];\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-900 text-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"\\uD83D\\uDD12 Acesso Negado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Voc\\xEA precisa estar logado para acessar a loja\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold\",\n            children: \"\\uD83D\\uDED2 Loja\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Mercado Negro Digital\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-bold text-green-400\",\n            children: [\"$\", (currentPlayer === null || currentPlayer === void 0 ? void 0 : (_currentPlayer$dinhei = currentPlayer.dinheiro) === null || _currentPlayer$dinhei === void 0 ? void 0 : _currentPlayer$dinhei.toLocaleString()) || '0']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Dinheiro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-bold text-blue-400\",\n            children: (currentPlayer === null || currentPlayer === void 0 ? void 0 : (_currentPlayer$shack = currentPlayer.shack) === null || _currentPlayer$shack === void 0 ? void 0 : _currentPlayer$shack.toLocaleString()) || '0'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Shacks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2 overflow-x-auto\",\n        children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSelectedCategory(category.id),\n          className: `flex items-center space-x-1 px-3 py-2 rounded-lg text-sm whitespace-nowrap ${selectedCategory === category.id ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: category.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: category.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)]\n        }, category.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n      children: [purchaseError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-300 text-sm\",\n          children: [\"\\u274C \", purchaseError]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), purchaseSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-900 border border-green-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-300 text-sm\",\n          children: [\"\\u2705 \", purchaseSuccess]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 11\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Carregando itens...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: filteredItems.map(item => {\n          const canAfford = currentPlayer && (!item.price.dinheiro || currentPlayer.dinheiro >= item.price.dinheiro) && (!item.price.shacks || currentPlayer.shack >= item.price.shacks);\n          const isPurchasing = purchasingItem === item.id;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl\",\n                  children: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-white\",\n                    children: item.displayName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400\",\n                    children: item.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-blue-400 mt-1\",\n                    children: [\"Categoria: \", item.category]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [item.price.dinheiro && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-green-400\",\n                  children: [\"$\", item.price.dinheiro.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 25\n                }, this), item.price.shacks && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-blue-400\",\n                  children: [item.price.shacks, \" Shacks\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handlePurchase(item),\n              disabled: !canAfford || !item.available || isPurchasing,\n              className: `w-full py-2 rounded-lg font-semibold text-sm ${!item.available ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : canAfford && !isPurchasing ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-600 text-gray-400 cursor-not-allowed'}`,\n              children: isPurchasing ? 'Comprando...' : !item.available ? 'Indisponível' : canAfford ? 'Comprar' : 'Recursos Insuficientes'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 19\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"Voltar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 5\n  }, this);\n};\n_s(ShopPage, \"c/waUzbzw8ce0KuuntjAWleMoKQ=\", false, function () {\n  return [useAuth, usePlayer];\n});\n_c = ShopPage;\nexport default ShopPage;\nvar _c;\n$RefreshReg$(_c, \"ShopPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "usePlayer", "gameApi", "jsxDEV", "_jsxDEV", "ShopPage", "_s", "_currentPlayer$dinhei", "_currentPlayer$shack", "user", "isAuthenticated", "currentPlayer", "loadPlayerData", "shopItems", "setShopItems", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "isLoading", "setIsLoading", "purchaseError", "setPurchaseError", "purchaseSuccess", "setPurchaseSuccess", "purchasingItem", "setPurchasingItem", "loadShopItems", "response", "getShopItems", "sucesso", "items", "loadMockItems", "error", "console", "mockItems", "id", "name", "displayName", "icon", "description", "price", "<PERSON><PERSON><PERSON>", "category", "available", "shacks", "handlePurchase", "item", "shack", "buyShopItem", "mensagem", "message", "filteredItems", "filter", "categories", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "history", "back", "toLocaleString", "map", "can<PERSON>fford", "isPurchasing", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/ShopPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\n\ninterface ShopItem {\n  id: string;\n  name: string;\n  displayName: string;\n  icon: string;\n  description: string;\n  price: {\n    dinheiro?: number;\n    shacks?: number;\n  };\n  category: 'hardware' | 'software' | 'tools' | 'special';\n  available: boolean;\n}\n\nconst ShopPage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const { currentPlayer, loadPlayerData } = usePlayer();\n  \n  const [shopItems, setShopItems] = useState<ShopItem[]>([]);\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [isLoading, setIsLoading] = useState(false);\n  const [purchaseError, setPurchaseError] = useState<string | null>(null);\n  const [purchaseSuccess, setPurchaseSuccess] = useState<string | null>(null);\n  const [purchasingItem, setPurchasingItem] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadShopItems();\n    }\n  }, [isAuthenticated]);\n\n  const loadShopItems = async () => {\n    setIsLoading(true);\n    try {\n      // Tentar carregar do backend primeiro\n      const response = await gameApi.getShopItems();\n      if (response.sucesso && response.items) {\n        setShopItems(response.items);\n      } else {\n        // Fallback para itens mockados\n        loadMockItems();\n      }\n    } catch (error) {\n      console.error('Erro ao carregar loja:', error);\n      // Fallback para itens mockados\n      loadMockItems();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loadMockItems = () => {\n    const mockItems: ShopItem[] = [\n      // Hardware\n      {\n        id: 'cpu_upgrade',\n        name: 'cpu_upgrade',\n        displayName: 'CPU Upgrade Kit',\n        icon: '🖥️',\n        description: 'Kit para upgrade de CPU (+5 níveis)',\n        price: { dinheiro: 50000 },\n        category: 'hardware',\n        available: true,\n      },\n      {\n        id: 'ram_upgrade',\n        name: 'ram_upgrade',\n        displayName: 'RAM Upgrade Kit',\n        icon: '💾',\n        description: 'Kit para upgrade de RAM (+5 níveis)',\n        price: { dinheiro: 40000 },\n        category: 'hardware',\n        available: true,\n      },\n      {\n        id: 'firewall_pro',\n        name: 'firewall_pro',\n        displayName: 'Firewall Pro',\n        icon: '🛡️',\n        description: 'Firewall profissional (+10 níveis)',\n        price: { dinheiro: 75000, shacks: 50 },\n        category: 'software',\n        available: true,\n      },\n      \n      // Software\n      {\n        id: 'antivirus_premium',\n        name: 'antivirus_premium',\n        displayName: 'Antivírus Premium',\n        icon: '🦠',\n        description: 'Antivírus de última geração (+10 níveis)',\n        price: { dinheiro: 60000, shacks: 30 },\n        category: 'software',\n        available: true,\n      },\n      {\n        id: 'malware_toolkit',\n        name: 'malware_toolkit',\n        displayName: 'Malware Toolkit',\n        icon: '🔧',\n        description: 'Kit completo de ferramentas malware (+15 níveis)',\n        price: { dinheiro: 100000, shacks: 75 },\n        category: 'tools',\n        available: true,\n      },\n      \n      // Tools\n      {\n        id: 'bruteforce_accelerator',\n        name: 'bruteforce_accelerator',\n        displayName: 'BruteForce Accelerator',\n        icon: '🔨',\n        description: 'Acelera ataques de força bruta (+20 níveis)',\n        price: { dinheiro: 150000, shacks: 100 },\n        category: 'tools',\n        available: true,\n      },\n      {\n        id: 'proxy_network',\n        name: 'proxy_network',\n        displayName: 'Proxy Network',\n        icon: '🌐',\n        description: 'Rede de proxies premium (+25 níveis)',\n        price: { dinheiro: 200000, shacks: 150 },\n        category: 'tools',\n        available: true,\n      },\n      \n      // Special\n      {\n        id: 'hacker_bundle',\n        name: 'hacker_bundle',\n        displayName: 'Hacker Bundle',\n        icon: '💎',\n        description: 'Pacote completo do hacker (+50 todos os itens)',\n        price: { dinheiro: 1000000, shacks: 500 },\n        category: 'special',\n        available: true,\n      },\n      {\n        id: 'premium_account',\n        name: 'premium_account',\n        displayName: 'Conta Premium',\n        icon: '👑',\n        description: 'Acesso premium por 30 dias (bônus +100%)',\n        price: { shacks: 1000 },\n        category: 'special',\n        available: true,\n      },\n    ];\n\n    setShopItems(mockItems);\n  };\n\n  const handlePurchase = async (item: ShopItem) => {\n    if (!currentPlayer) return;\n\n    // Verificar se tem recursos suficientes\n    if (item.price.dinheiro && (currentPlayer?.dinheiro || 0) < item.price.dinheiro) {\n      setPurchaseError('Dinheiro insuficiente');\n      return;\n    }\n\n    if (item.price.shacks && (currentPlayer?.shack || 0) < item.price.shacks) {\n      setPurchaseError('Shacks insuficientes');\n      return;\n    }\n\n    setPurchasingItem(item.id);\n    setPurchaseError(null);\n    setPurchaseSuccess(null);\n\n    try {\n      const response = await gameApi.buyShopItem(item.id);\n      \n      if (response.sucesso) {\n        setPurchaseSuccess(`${item.displayName} comprado com sucesso!`);\n        \n        // Recarregar dados do jogador\n        await loadPlayerData();\n      } else {\n        setPurchaseError(response.mensagem || 'Erro na compra');\n      }\n    } catch (error: any) {\n      setPurchaseError(error.message || 'Erro de conexão');\n    } finally {\n      setPurchasingItem(null);\n    }\n  };\n\n  const filteredItems = selectedCategory === 'all' \n    ? shopItems \n    : shopItems.filter(item => item.category === selectedCategory);\n\n  const categories = [\n    { id: 'all', name: 'Todos', icon: '🛍️' },\n    { id: 'hardware', name: 'Hardware', icon: '🖥️' },\n    { id: 'software', name: 'Software', icon: '💿' },\n    { id: 'tools', name: 'Ferramentas', icon: '🔧' },\n    { id: 'special', name: 'Especiais', icon: '💎' },\n  ];\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"h-screen bg-gray-900 text-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-4\">🔒 Acesso Negado</h1>\n          <p className=\"text-gray-400\">Você precisa estar logado para acessar a loja</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n          >\n            <span className=\"text-lg\">←</span>\n          </button>\n          <div>\n            <h1 className=\"text-lg font-bold\">🛒 Loja</h1>\n            <p className=\"text-xs text-gray-400\">Mercado Negro Digital</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Recursos disponíveis */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div className=\"text-center\">\n            <div className=\"text-lg font-bold text-green-400\">\n              ${currentPlayer?.dinheiro?.toLocaleString() || '0'}\n            </div>\n            <div className=\"text-xs text-gray-400\">Dinheiro</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-lg font-bold text-blue-400\">\n              {currentPlayer?.shack?.toLocaleString() || '0'}\n            </div>\n            <div className=\"text-xs text-gray-400\">Shacks</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filtros de categoria */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex space-x-2 overflow-x-auto\">\n          {categories.map((category) => (\n            <button\n              key={category.id}\n              onClick={() => setSelectedCategory(category.id)}\n              className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm whitespace-nowrap ${\n                selectedCategory === category.id\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n              }`}\n            >\n              <span>{category.icon}</span>\n              <span>{category.name}</span>\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-4\">\n        {/* Mensagens de erro/sucesso */}\n        {purchaseError && (\n          <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n            <p className=\"text-red-300 text-sm\">❌ {purchaseError}</p>\n          </div>\n        )}\n\n        {purchaseSuccess && (\n          <div className=\"bg-green-900 border border-green-500 rounded-lg p-3\">\n            <p className=\"text-green-300 text-sm\">✅ {purchaseSuccess}</p>\n          </div>\n        )}\n\n        {/* Lista de itens */}\n        {isLoading ? (\n          <div className=\"text-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4\"></div>\n            <p className=\"text-gray-400\">Carregando itens...</p>\n          </div>\n        ) : (\n          <div className=\"space-y-3\">\n            {filteredItems.map((item) => {\n              const canAfford = currentPlayer && \n                (!item.price.dinheiro || currentPlayer.dinheiro >= item.price.dinheiro) &&\n                (!item.price.shacks || currentPlayer.shack >= item.price.shacks);\n              \n              const isPurchasing = purchasingItem === item.id;\n\n              return (\n                <div key={item.id} className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n                  <div className=\"flex justify-between items-start mb-3\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"text-2xl\">{item.icon}</div>\n                      <div>\n                        <h4 className=\"font-bold text-white\">{item.displayName}</h4>\n                        <p className=\"text-xs text-gray-400\">{item.description}</p>\n                        <div className=\"text-xs text-blue-400 mt-1\">\n                          Categoria: {item.category}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      {item.price.dinheiro && (\n                        <div className=\"text-sm text-green-400\">\n                          ${item.price.dinheiro.toLocaleString()}\n                        </div>\n                      )}\n                      {item.price.shacks && (\n                        <div className=\"text-xs text-blue-400\">\n                          {item.price.shacks} Shacks\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  <button\n                    onClick={() => handlePurchase(item)}\n                    disabled={!canAfford || !item.available || isPurchasing}\n                    className={`w-full py-2 rounded-lg font-semibold text-sm ${\n                      !item.available\n                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                        : canAfford && !isPurchasing\n                        ? 'bg-green-600 hover:bg-green-700 text-white'\n                        : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                    }`}\n                  >\n                    {isPurchasing ? 'Comprando...' : \n                     !item.available ? 'Indisponível' :\n                     canAfford ? 'Comprar' : 'Recursos Insuficientes'}\n                  </button>\n                </div>\n              );\n            })}\n          </div>\n        )}\n      </div>\n\n      {/* Footer */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-center\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\"\n          >\n            <span className=\"text-lg\">←</span>\n            <span className=\"text-sm\">Voltar</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ShopPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgB1C,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC/B,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEW,aAAa;IAAEC;EAAe,CAAC,GAAGX,SAAS,CAAC,CAAC;EAErD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAS,KAAK,CAAC;EACvE,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EAEzEC,SAAS,CAAC,MAAM;IACd,IAAIW,eAAe,EAAE;MACnBe,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACf,eAAe,CAAC,CAAC;EAErB,MAAMe,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCP,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF;MACA,MAAMQ,QAAQ,GAAG,MAAMxB,OAAO,CAACyB,YAAY,CAAC,CAAC;MAC7C,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,KAAK,EAAE;QACtCf,YAAY,CAACY,QAAQ,CAACG,KAAK,CAAC;MAC9B,CAAC,MAAM;QACL;QACAC,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;MACAD,aAAa,CAAC,CAAC;IACjB,CAAC,SAAS;MACRZ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMG,SAAqB,GAAG;IAC5B;IACA;MACEC,EAAE,EAAE,aAAa;MACjBC,IAAI,EAAE,aAAa;MACnBC,WAAW,EAAE,iBAAiB;MAC9BC,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,qCAAqC;MAClDC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAM,CAAC;MAC1BC,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE;IACb,CAAC,EACD;MACER,EAAE,EAAE,aAAa;MACjBC,IAAI,EAAE,aAAa;MACnBC,WAAW,EAAE,iBAAiB;MAC9BC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,qCAAqC;MAClDC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAM,CAAC;MAC1BC,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE;IACb,CAAC,EACD;MACER,EAAE,EAAE,cAAc;MAClBC,IAAI,EAAE,cAAc;MACpBC,WAAW,EAAE,cAAc;MAC3BC,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,oCAAoC;MACjDC,KAAK,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEG,MAAM,EAAE;MAAG,CAAC;MACtCF,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE;IACb,CAAC;IAED;IACA;MACER,EAAE,EAAE,mBAAmB;MACvBC,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE,mBAAmB;MAChCC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,0CAA0C;MACvDC,KAAK,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEG,MAAM,EAAE;MAAG,CAAC;MACtCF,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE;IACb,CAAC,EACD;MACER,EAAE,EAAE,iBAAiB;MACrBC,IAAI,EAAE,iBAAiB;MACvBC,WAAW,EAAE,iBAAiB;MAC9BC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,kDAAkD;MAC/DC,KAAK,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEG,MAAM,EAAE;MAAG,CAAC;MACvCF,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE;IACb,CAAC;IAED;IACA;MACER,EAAE,EAAE,wBAAwB;MAC5BC,IAAI,EAAE,wBAAwB;MAC9BC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,6CAA6C;MAC1DC,KAAK,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEG,MAAM,EAAE;MAAI,CAAC;MACxCF,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE;IACb,CAAC,EACD;MACER,EAAE,EAAE,eAAe;MACnBC,IAAI,EAAE,eAAe;MACrBC,WAAW,EAAE,eAAe;MAC5BC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,sCAAsC;MACnDC,KAAK,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEG,MAAM,EAAE;MAAI,CAAC;MACxCF,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE;IACb,CAAC;IAED;IACA;MACER,EAAE,EAAE,eAAe;MACnBC,IAAI,EAAE,eAAe;MACrBC,WAAW,EAAE,eAAe;MAC5BC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,gDAAgD;MAC7DC,KAAK,EAAE;QAAEC,QAAQ,EAAE,OAAO;QAAEG,MAAM,EAAE;MAAI,CAAC;MACzCF,QAAQ,EAAE,SAAS;MACnBC,SAAS,EAAE;IACb,CAAC,EACD;MACER,EAAE,EAAE,iBAAiB;MACrBC,IAAI,EAAE,iBAAiB;MACvBC,WAAW,EAAE,eAAe;MAC5BC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,0CAA0C;MACvDC,KAAK,EAAE;QAAEI,MAAM,EAAE;MAAK,CAAC;MACvBF,QAAQ,EAAE,SAAS;MACnBC,SAAS,EAAE;IACb,CAAC,CACF;IAED5B,YAAY,CAACmB,SAAS,CAAC;EACzB,CAAC;EAED,MAAMW,cAAc,GAAG,MAAOC,IAAc,IAAK;IAC/C,IAAI,CAAClC,aAAa,EAAE;;IAEpB;IACA,IAAIkC,IAAI,CAACN,KAAK,CAACC,QAAQ,IAAI,CAAC,CAAA7B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6B,QAAQ,KAAI,CAAC,IAAIK,IAAI,CAACN,KAAK,CAACC,QAAQ,EAAE;MAC/EpB,gBAAgB,CAAC,uBAAuB,CAAC;MACzC;IACF;IAEA,IAAIyB,IAAI,CAACN,KAAK,CAACI,MAAM,IAAI,CAAC,CAAAhC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEmC,KAAK,KAAI,CAAC,IAAID,IAAI,CAACN,KAAK,CAACI,MAAM,EAAE;MACxEvB,gBAAgB,CAAC,sBAAsB,CAAC;MACxC;IACF;IAEAI,iBAAiB,CAACqB,IAAI,CAACX,EAAE,CAAC;IAC1Bd,gBAAgB,CAAC,IAAI,CAAC;IACtBE,kBAAkB,CAAC,IAAI,CAAC;IAExB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMxB,OAAO,CAAC6C,WAAW,CAACF,IAAI,CAACX,EAAE,CAAC;MAEnD,IAAIR,QAAQ,CAACE,OAAO,EAAE;QACpBN,kBAAkB,CAAC,GAAGuB,IAAI,CAACT,WAAW,wBAAwB,CAAC;;QAE/D;QACA,MAAMxB,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLQ,gBAAgB,CAACM,QAAQ,CAACsB,QAAQ,IAAI,gBAAgB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOjB,KAAU,EAAE;MACnBX,gBAAgB,CAACW,KAAK,CAACkB,OAAO,IAAI,iBAAiB,CAAC;IACtD,CAAC,SAAS;MACRzB,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,MAAM0B,aAAa,GAAGnC,gBAAgB,KAAK,KAAK,GAC5CF,SAAS,GACTA,SAAS,CAACsC,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACJ,QAAQ,KAAK1B,gBAAgB,CAAC;EAEhE,MAAMqC,UAAU,GAAG,CACjB;IAAElB,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE,OAAO;IAAEE,IAAI,EAAE;EAAM,CAAC,EACzC;IAAEH,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,UAAU;IAAEE,IAAI,EAAE;EAAM,CAAC,EACjD;IAAEH,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,UAAU;IAAEE,IAAI,EAAE;EAAK,CAAC,EAChD;IAAEH,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,aAAa;IAAEE,IAAI,EAAE;EAAK,CAAC,EAChD;IAAEH,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,WAAW;IAAEE,IAAI,EAAE;EAAK,CAAC,CACjD;EAED,IAAI,CAAC3B,eAAe,EAAE;IACpB,oBACEN,OAAA;MAAKiD,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/ElD,OAAA;QAAKiD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlD,OAAA;UAAIiD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DtD,OAAA;UAAGiD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtD,OAAA;IAAKiD,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DlD,OAAA;MAAKiD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrElD,OAAA;QAAKiD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClD,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCT,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7FlD,OAAA;YAAMiD,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACTtD,OAAA;UAAAkD,QAAA,gBACElD,OAAA;YAAIiD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CtD,OAAA;YAAGiD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA;MAAKiD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrElD,OAAA;QAAKiD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrClD,OAAA;UAAKiD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlD,OAAA;YAAKiD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAAC,GAC/C,EAAC,CAAA3C,aAAa,aAAbA,aAAa,wBAAAJ,qBAAA,GAAbI,aAAa,CAAE6B,QAAQ,cAAAjC,qBAAA,uBAAvBA,qBAAA,CAAyBwD,cAAc,CAAC,CAAC,KAAI,GAAG;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNtD,OAAA;UAAKiD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlD,OAAA;YAAKiD,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAC7C,CAAA3C,aAAa,aAAbA,aAAa,wBAAAH,oBAAA,GAAbG,aAAa,CAAEmC,KAAK,cAAAtC,oBAAA,uBAApBA,oBAAA,CAAsBuD,cAAc,CAAC,CAAC,KAAI;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA;MAAKiD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrElD,OAAA;QAAKiD,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAC5CF,UAAU,CAACY,GAAG,CAAEvB,QAAQ,iBACvBrC,OAAA;UAEEuD,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAACyB,QAAQ,CAACP,EAAE,CAAE;UAChDmB,SAAS,EAAE,8EACTtC,gBAAgB,KAAK0B,QAAQ,CAACP,EAAE,GAC5B,wBAAwB,GACxB,6CAA6C,EAChD;UAAAoB,QAAA,gBAEHlD,OAAA;YAAAkD,QAAA,EAAOb,QAAQ,CAACJ;UAAI;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BtD,OAAA;YAAAkD,QAAA,EAAOb,QAAQ,CAACN;UAAI;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GATvBjB,QAAQ,CAACP,EAAE;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUV,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA;MAAKiD,SAAS,EAAC,sCAAsC;MAAAC,QAAA,GAElDnC,aAAa,iBACZf,OAAA;QAAKiD,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DlD,OAAA;UAAGiD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,SAAE,EAACnC,aAAa;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN,EAEArC,eAAe,iBACdjB,OAAA;QAAKiD,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClElD,OAAA;UAAGiD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,SAAE,EAACjC,eAAe;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CACN,EAGAzC,SAAS,gBACRb,OAAA;QAAKiD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlD,OAAA;UAAKiD,SAAS,EAAC;QAA2E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGtD,OAAA;UAAGiD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,gBAENtD,OAAA;QAAKiD,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBJ,aAAa,CAACc,GAAG,CAAEnB,IAAI,IAAK;UAC3B,MAAMoB,SAAS,GAAGtD,aAAa,KAC5B,CAACkC,IAAI,CAACN,KAAK,CAACC,QAAQ,IAAI7B,aAAa,CAAC6B,QAAQ,IAAIK,IAAI,CAACN,KAAK,CAACC,QAAQ,CAAC,KACtE,CAACK,IAAI,CAACN,KAAK,CAACI,MAAM,IAAIhC,aAAa,CAACmC,KAAK,IAAID,IAAI,CAACN,KAAK,CAACI,MAAM,CAAC;UAElE,MAAMuB,YAAY,GAAG3C,cAAc,KAAKsB,IAAI,CAACX,EAAE;UAE/C,oBACE9B,OAAA;YAAmBiD,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAC9ElD,OAAA;cAAKiD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlD,OAAA;gBAAKiD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClD,OAAA;kBAAKiD,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAET,IAAI,CAACR;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CtD,OAAA;kBAAAkD,QAAA,gBACElD,OAAA;oBAAIiD,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAET,IAAI,CAACT;kBAAW;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DtD,OAAA;oBAAGiD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAET,IAAI,CAACP;kBAAW;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3DtD,OAAA;oBAAKiD,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GAAC,aAC/B,EAACT,IAAI,CAACJ,QAAQ;kBAAA;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtD,OAAA;gBAAKiD,SAAS,EAAC,YAAY;gBAAAC,QAAA,GACxBT,IAAI,CAACN,KAAK,CAACC,QAAQ,iBAClBpC,OAAA;kBAAKiD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,GAAC,GACrC,EAACT,IAAI,CAACN,KAAK,CAACC,QAAQ,CAACuB,cAAc,CAAC,CAAC;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CACN,EACAb,IAAI,CAACN,KAAK,CAACI,MAAM,iBAChBvC,OAAA;kBAAKiD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACnCT,IAAI,CAACN,KAAK,CAACI,MAAM,EAAC,SACrB;gBAAA;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAMf,cAAc,CAACC,IAAI,CAAE;cACpCsB,QAAQ,EAAE,CAACF,SAAS,IAAI,CAACpB,IAAI,CAACH,SAAS,IAAIwB,YAAa;cACxDb,SAAS,EAAE,gDACT,CAACR,IAAI,CAACH,SAAS,GACX,8CAA8C,GAC9CuB,SAAS,IAAI,CAACC,YAAY,GAC1B,4CAA4C,GAC5C,8CAA8C,EACjD;cAAAZ,QAAA,EAEFY,YAAY,GAAG,cAAc,GAC7B,CAACrB,IAAI,CAACH,SAAS,GAAG,cAAc,GAChCuB,SAAS,GAAG,SAAS,GAAG;YAAwB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA,GAxCDb,IAAI,CAACX,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyCZ,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNtD,OAAA;MAAKiD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrElD,OAAA;QAAKiD,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClClD,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCT,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExFlD,OAAA;YAAMiD,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCtD,OAAA;YAAMiD,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CA7VID,QAAkB;EAAA,QACYL,OAAO,EACCC,SAAS;AAAA;AAAAmE,EAAA,GAF/C/D,QAAkB;AA+VxB,eAAeA,QAAQ;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}