{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconsole.log('🚀 SHACK Game - Iniciando aplicação React...');\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nconsole.log('🚀 SHACK Game - Renderizando App...');\nroot.render(/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 13,\n  columnNumber: 13\n}, this));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "jsxDEV", "_jsxDEV", "console", "log", "root", "createRoot", "document", "getElementById", "render", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\n\nconsole.log('🚀 SHACK Game - Iniciando aplicação React...');\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nconsole.log('🚀 SHACK Game - Renderizando App...');\nroot.render(<App />);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhDC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;AAE3D,MAAMC,IAAI,GAAGP,QAAQ,CAACQ,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDL,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;AAClDC,IAAI,CAACI,MAAM,cAACP,OAAA,CAACH,GAAG;EAAAW,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAAC,CAAC;;AAEpB;AACA;AACA;AACAb,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}