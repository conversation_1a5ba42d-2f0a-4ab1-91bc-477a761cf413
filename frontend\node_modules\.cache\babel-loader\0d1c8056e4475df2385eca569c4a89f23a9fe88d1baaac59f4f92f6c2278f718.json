{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\game\\\\MobilePhone.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useHackGameStore } from '../../stores/hackGameStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MobilePhone = ({\n  children\n}) => {\n  _s();\n  const {\n    player,\n    notifications,\n    setCurrentScreen\n  } = useHackGameStore();\n  const unreadCount = notifications.filter(n => !n.read).length;\n  const handleHomeClick = () => {\n    setCurrentScreen('home');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-[420px] h-[900px] bg-black rounded-[3rem] p-3 shadow-2xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-full bg-gray-900 rounded-[2.5rem] overflow-hidden relative flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 left-1/2 transform -translate-x-1/2 w-36 h-7 bg-black rounded-b-2xl z-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center px-6 pt-4 pb-2 text-white text-sm relative z-40 flex-shrink-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: \"9:41\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1 h-3 bg-white rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1 h-3 bg-white rounded-full opacity-60\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 38,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1 h-3 bg-white rounded-full opacity-30\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-7 h-3 border border-white rounded-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-5 h-1 bg-green-500 rounded-sm m-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-hidden\",\n            children: children\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 pb-6 pt-2 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleHomeClick,\n              className: \"w-32 h-1 bg-white rounded-full opacity-80 hover:opacity-100 transition-all duration-200 active:scale-95 shadow-sm\",\n              style: {\n                background: 'linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%)',\n                boxShadow: '0 1px 3px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.8)'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full h-full rounded-full bg-gradient-to-r from-transparent via-white/20 to-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-0 top-24 w-1 h-14 bg-gray-700 rounded-l-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-0 top-44 w-1 h-10 bg-gray-700 rounded-l-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-0 top-60 w-1 h-10 bg-gray-700 rounded-l-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-0 top-36 w-1 h-18 bg-gray-700 rounded-r-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(MobilePhone, \"jmzuN9oEsW7tEWft5a2vGfKrtI4=\", false, function () {\n  return [useHackGameStore];\n});\n_c = MobilePhone;\nexport default MobilePhone;\nvar _c;\n$RefreshReg$(_c, \"MobilePhone\");", "map": {"version": 3, "names": ["React", "useHackGameStore", "jsxDEV", "_jsxDEV", "MobilePhone", "children", "_s", "player", "notifications", "setCurrentScreen", "unreadCount", "filter", "n", "read", "length", "handleHomeClick", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "background", "boxShadow", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/game/MobilePhone.tsx"], "sourcesContent": ["import React from 'react';\nimport { useHackGameStore } from '../../stores/hackGameStore';\n\ninterface MobilePhoneProps {\n  children: React.ReactNode;\n}\n\nconst MobilePhone: React.FC<MobilePhoneProps> = ({ children }) => {\n  const { player, notifications, setCurrentScreen } = useHackGameStore();\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  const handleHomeClick = () => {\n    setCurrentScreen('home');\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black p-4\">\n      {/* Moldura do celular */}\n      <div className=\"relative\">\n        {/* Corpo do celular */}\n        <div className=\"w-[420px] h-[900px] bg-black rounded-[3rem] p-3 shadow-2xl\">\n          {/* Tela do celular */}\n          <div className=\"w-full h-full bg-gray-900 rounded-[2.5rem] overflow-hidden relative flex flex-col\">\n            {/* Notch */}\n            <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 w-36 h-7 bg-black rounded-b-2xl z-50\"></div>\n\n            {/* Status bar */}\n            <div className=\"flex justify-between items-center px-6 pt-4 pb-2 text-white text-sm relative z-40 flex-shrink-0\">\n              <div className=\"flex items-center space-x-1\">\n                <span className=\"text-sm font-medium\">9:41</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                {unreadCount > 0 && (\n                  <div className=\"w-2 h-2 bg-red-500 rounded-full animate-pulse\"></div>\n                )}\n                <div className=\"flex space-x-1\">\n                  <div className=\"w-1 h-3 bg-white rounded-full\"></div>\n                  <div className=\"w-1 h-3 bg-white rounded-full opacity-60\"></div>\n                  <div className=\"w-1 h-3 bg-white rounded-full opacity-30\"></div>\n                </div>\n                <div className=\"w-7 h-3 border border-white rounded-sm\">\n                  <div className=\"w-5 h-1 bg-green-500 rounded-sm m-0.5\"></div>\n                </div>\n              </div>\n            </div>\n\n            {/* Conteúdo da tela */}\n            <div className=\"flex-1 overflow-hidden\">\n              {children}\n            </div>\n\n            {/* Botão Home estilo iPhone */}\n            <div className=\"flex-shrink-0 pb-6 pt-2 flex justify-center\">\n              <button\n                onClick={handleHomeClick}\n                className=\"w-32 h-1 bg-white rounded-full opacity-80 hover:opacity-100 transition-all duration-200 active:scale-95 shadow-sm\"\n                style={{\n                  background: 'linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%)',\n                  boxShadow: '0 1px 3px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.8)'\n                }}\n              >\n                {/* Indicador visual sutil */}\n                <div className=\"w-full h-full rounded-full bg-gradient-to-r from-transparent via-white/20 to-transparent\"></div>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Botões laterais */}\n        <div className=\"absolute left-0 top-24 w-1 h-14 bg-gray-700 rounded-l-lg\"></div>\n        <div className=\"absolute left-0 top-44 w-1 h-10 bg-gray-700 rounded-l-lg\"></div>\n        <div className=\"absolute left-0 top-60 w-1 h-10 bg-gray-700 rounded-l-lg\"></div>\n        <div className=\"absolute right-0 top-36 w-1 h-18 bg-gray-700 rounded-r-lg\"></div>\n      </div>\n    </div>\n  );\n};\n\nexport default MobilePhone;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM9D,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM;IAAEC,MAAM;IAAEC,aAAa;IAAEC;EAAiB,CAAC,GAAGR,gBAAgB,CAAC,CAAC;EACtE,MAAMS,WAAW,GAAGF,aAAa,CAACG,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,CAAC,CAACC,MAAM;EAE7D,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BN,gBAAgB,CAAC,MAAM,CAAC;EAC1B,CAAC;EAED,oBACEN,OAAA;IAAKa,SAAS,EAAC,yGAAyG;IAAAX,QAAA,eAEtHF,OAAA;MAAKa,SAAS,EAAC,UAAU;MAAAX,QAAA,gBAEvBF,OAAA;QAAKa,SAAS,EAAC,4DAA4D;QAAAX,QAAA,eAEzEF,OAAA;UAAKa,SAAS,EAAC,mFAAmF;UAAAX,QAAA,gBAEhGF,OAAA;YAAKa,SAAS,EAAC;UAAyF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAG/GjB,OAAA;YAAKa,SAAS,EAAC,iGAAiG;YAAAX,QAAA,gBAC9GF,OAAA;cAAKa,SAAS,EAAC,6BAA6B;cAAAX,QAAA,eAC1CF,OAAA;gBAAMa,SAAS,EAAC,qBAAqB;gBAAAX,QAAA,EAAC;cAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNjB,OAAA;cAAKa,SAAS,EAAC,6BAA6B;cAAAX,QAAA,GACzCK,WAAW,GAAG,CAAC,iBACdP,OAAA;gBAAKa,SAAS,EAAC;cAA+C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACrE,eACDjB,OAAA;gBAAKa,SAAS,EAAC,gBAAgB;gBAAAX,QAAA,gBAC7BF,OAAA;kBAAKa,SAAS,EAAC;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDjB,OAAA;kBAAKa,SAAS,EAAC;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChEjB,OAAA;kBAAKa,SAAS,EAAC;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNjB,OAAA;gBAAKa,SAAS,EAAC,wCAAwC;gBAAAX,QAAA,eACrDF,OAAA;kBAAKa,SAAS,EAAC;gBAAuC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjB,OAAA;YAAKa,SAAS,EAAC,wBAAwB;YAAAX,QAAA,EACpCA;UAAQ;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNjB,OAAA;YAAKa,SAAS,EAAC,6CAA6C;YAAAX,QAAA,eAC1DF,OAAA;cACEkB,OAAO,EAAEN,eAAgB;cACzBC,SAAS,EAAC,mHAAmH;cAC7HM,KAAK,EAAE;gBACLC,UAAU,EAAE,mDAAmD;gBAC/DC,SAAS,EAAE;cACb,CAAE;cAAAnB,QAAA,eAGFF,OAAA;gBAAKa,SAAS,EAAC;cAA0F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjB,OAAA;QAAKa,SAAS,EAAC;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChFjB,OAAA;QAAKa,SAAS,EAAC;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChFjB,OAAA;QAAKa,SAAS,EAAC;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChFjB,OAAA;QAAKa,SAAS,EAAC;MAA2D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9E;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACd,EAAA,CArEIF,WAAuC;EAAA,QACSH,gBAAgB;AAAA;AAAAwB,EAAA,GADhErB,WAAuC;AAuE7C,eAAeA,WAAW;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}