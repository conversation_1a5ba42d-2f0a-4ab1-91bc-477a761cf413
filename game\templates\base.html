<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shack Web Version</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* === TEMA MODERNO PRETO E AZUL === */
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary: #64748b;
            --accent: #0ea5e9;
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #94a3b8;
            --border: #334155;
            --shadow: rgba(37, 99, 235, 0.1);
            --shadow-lg: rgba(37, 99, 235, 0.2);
        }

        /* Reset e Base */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--main-bg);
            color: var(--primary-text);
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }

        /* Gradient de fundo moderno */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.03) 0%, transparent 50%, rgba(13, 17, 23, 0.05) 100%);
            z-index: -1;
        }

        /* Seções do jogo */
        .game-section { 
            display: none; 
            animation: fadeInUp 0.5s ease-out;
        }
        
        #main-dashboard { 
            display: block; 
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Navegação ativa */
        .nav-item.active, .dashboard-btn.active {
            color: var(--accent-blue);
            background: var(--surface-elevated);
            border: 2px solid var(--accent-blue);
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Notificações modernas */
        .notification-toast {
            padding: 1rem 1.5rem;
            border-radius: 12px;
            color: var(--primary-text);
            font-weight: 500;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            border: 1px solid;
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transform: translateX(100%);
            animation: slide-in 0.5s forwards;
        }

        @keyframes slide-in {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .toast-success {
            background: rgba(34, 197, 94, 0.1);
            border-color: #22c55e;
            box-shadow: 0 4px 20px rgba(34, 197, 94, 0.2);
        }

        .toast-error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            box-shadow: 0 4px 20px rgba(239, 68, 68, 0.2);
        }

        .toast-info {
            background: rgba(0, 123, 255, 0.1);
            border-color: var(--accent-blue);
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.2);
        }

        /* Chat moderno */
        .chat-preview {
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--surface-elevated);
            border: 1px solid var(--border-color);
            border-radius: 12px;
        }
        
        .chat-preview:hover {
            background: var(--surface-hover);
            border-color: var(--accent-blue);
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);
        }
        
        .chat-full {
            max-height: 400px;
            transition: all 0.3s ease;
            background: var(--surface-elevated);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(20px);
        }
        
        .chat-closed {
            max-height: 0;
            overflow: hidden;
        }

        /* Navegação moderna */
        .nav-item {
            padding: 1.5rem 1rem !important;
            min-height: 80px;
            min-width: 80px;
            touch-action: manipulation;
            transition: all 0.3s ease;
            background: rgba(30, 41, 59, 0.95) !important; /* Background semitransparente para evitar sobreposição */
            border: 1px solid var(--border-color);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px); /* Efeito de desfoque no fundo */
            -webkit-backdrop-filter: blur(10px); /* Compatibilidade Safari */
        }

        .nav-item:hover {
            background: rgba(51, 65, 85, 0.95) !important; /* Background hover também semitransparente */
            border-color: var(--accent-blue);
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
            transform: translateY(-2px);
        }

        .nav-item.active {
            background: rgba(37, 99, 235, 0.2) !important; /* Background ativo com cor de destaque */
            border-color: var(--accent-blue);
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
            transform: translateY(-2px);
        }

        .nav-item:active {
            transform: scale(0.98);
            box-shadow: 0 2px 10px rgba(0, 123, 255, 0.2);
        }

        .nav-item svg, .nav-item div {
            pointer-events: none;
            position: relative;
            z-index: 1;
        }

        /* Header moderno */
        header {
            background: var(--surface-elevated);
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-weight: 600;
        }

        /* Temas modernos */
        .wallpaper-dark {
            background: var(--main-bg);
        }

        .wallpaper-cyber {
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%);
        }

        .wallpaper-matrix {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }

        .wallpaper-neon {
            background: linear-gradient(135deg, #0c1222 0%, #1a202c 100%);
        }

        .wallpaper-hacker {
            background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
        }

        .wallpaper-purple {
            background: linear-gradient(135deg, #1a0033 0%, #330066 50%, #4d0099 100%);
        }

        .wallpaper-option {
            cursor: pointer;
            border: 3px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .wallpaper-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--accent-blue);
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 8px;
        }

        .wallpaper-option:hover::before {
            opacity: 0.1;
        }

        .wallpaper-option:hover {
            border-color: var(--accent-blue);
            transform: scale(1.02);
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.2);
        }

        .wallpaper-option.selected {
            border-color: var(--accent-blue);
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
        }

        /* Inputs modernos */
        input, button, select, textarea {
            background: var(--surface-elevated);
            border: 1px solid var(--border-color);
            color: var(--primary-text);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: all 0.3s ease;
            border-radius: 8px;
        }

        input:focus, textarea:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            background: var(--surface-hover);
        }

        button {
            cursor: pointer;
            position: relative;
            overflow: hidden;
            font-weight: 500;
        }

        button:hover {
            border-color: var(--accent-blue);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
            transform: translateY(-1px);
            background: var(--surface-hover);
        }

        /* Scrollbar moderna */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--surface-default);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--accent-blue);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #0056b3;
        }

        /* Scrollbars personalizados para rankings */
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }

        .scrollbar-thumb-purple-400::-webkit-scrollbar-thumb {
            background: #a855f7;
            border-radius: 3px;
        }

        .scrollbar-thumb-purple-400::-webkit-scrollbar-thumb:hover {
            background: #9333ea;
        }

        .scrollbar-thumb-blue-400::-webkit-scrollbar-thumb {
            background: #60a5fa;
            border-radius: 3px;
        }

        .scrollbar-thumb-blue-400::-webkit-scrollbar-thumb:hover {
            background: #3b82f6;
        }

        .scrollbar-track-gray-700::-webkit-scrollbar-track {
            background: #374151;
            border-radius: 3px;
        }

        /* Melhoria para visibilidade do scroll em mobile */
        @media (max-width: 768px) {
            .max-h-96 {
                max-height: 20rem; /* Reduz altura em mobile */
            }
        }

        /* Indicador de scroll disponível */
        .scroll-container {
            position: relative;
        }

        .scroll-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 10px;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), transparent);
            pointer-events: none;
            z-index: 1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .scroll-container::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 10px;
            background: linear-gradient(to top, rgba(0,0,0,0.1), transparent);
            pointer-events: none;
            z-index: 1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .scroll-container.has-scroll::before,
        .scroll-container.has-scroll::after {
            opacity: 1;
        }

        /* Footer com z-index alto para evitar sobreposição */
        #footer-nav {
            z-index: 1000;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
        }

        /* Espaçamento adicional no main para o footer fixo */
        main {
            padding-bottom: 6rem; /* 24 * 0.25rem = 6rem */
        }

        /* === ANIMAÇÕES DA APP STORE === */
        
        /* Animação de carregamento rotativa para a barra de progresso */
        @keyframes loading-progress {
            0% {
                width: 0%;
                background-position: 0% 50%;
            }
            50% {
                width: 75%;
                background-position: 100% 50%;
            }
            100% {
                width: 100%;
                background-position: 200% 50%;
            }
        }

        /* Animação flutuante para os ícones de apps */
        @keyframes float-app {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-8px);
            }
        }

        /* Animação de brilho para o ícone principal */
        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        /* Container de loading específico da App Store */
        #apps-loading {
            animation: fadeInScale 0.6s ease-out;
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Efeito de brilho nos ícones de carregamento */
        #apps-loading .animate-pulse {
            animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite, float-app 2s ease-in-out infinite;
        }

        /* Melhoria da animação de bounce para os pontos */
        @keyframes bounce-smooth {
            0%, 20%, 53%, 80%, 100% {
                animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
                transform: translate3d(0, -8px, 0);
            }
            70% {
                animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
                transform: translate3d(0, -4px, 0);
            }
            90% {
                transform: translate3d(0,-1px,0);
            }
        }

        /* Aplicar animação suave aos pontos coloridos */
        #apps-loading .animate-bounce {
            animation: bounce-smooth 1.5s infinite;
        }

        /* Efeito gradiente animado para a barra de progresso */
        #apps-loading .animate-pulse.bg-gradient-to-r {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #3b82f6);
            background-size: 200% 100%;
            animation: loading-progress 2s ease-in-out infinite;
        }

        /* Transição suave para entrada dos cards */
        .app-card-enter {
            animation: slideInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(60px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Hover melhorado para os cards de upgrade */
        .upgrade-btn:hover {
            transform: scale(1.02) translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
        }

        /* Estados de loading para botões */
        .upgrade-btn.loading {
            position: relative;
            color: transparent !important;
        }

        .upgrade-btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #ffffff40;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* Animação para badge de segurança */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body class="min-h-screen flex flex-col wallpaper-dark" id="game-body">
    <!-- Header moderno -->
    <header class="p-4 text-center text-xl">
        <div class="flex items-center justify-center space-x-3">
            <span class="text-blue-500">●</span>
            <span class="font-bold text-primary-text">SHACK</span>
            <span class="text-blue-400">WEB</span>
            <span class="text-blue-300">●</span>
        </div>
        <div class="text-xs mt-1 text-secondary-text font-medium">
            Versão de desenvolvimento
        </div>
    </header>

    <!-- Chat Preview moderno -->
    <div id="chat-preview" class="border-b border-border-color" style="display: none;">
        <div class="chat-preview p-3 flex items-center justify-between">
            <div class="flex items-center gap-2">
                <span class="text-sm font-semibold text-accent-blue">Chat Global</span>
            </div>
            <div id="last-message" class="text-sm text-secondary-text flex-1 mx-3 truncate">
                Nenhuma mensagem ainda...
            </div>
            <div class="text-xs text-accent-blue">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z" />
                </svg>
            </div>
        </div>
    </div>

    <!-- Chat Full moderno -->
    <div id="chat-full" class="border-b border-border-color chat-full chat-closed" style="display: none;">
        <div class="flex items-center justify-between p-3 border-b border-border-color">
            <div class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-accent-blue">
                    <path fill-rule="evenodd" d="M10 2c-2.236 0-4.43.18-6.57.524C1.993 2.755 1 4.014 1 5.426v5.148c0 1.413.993 2.67 2.43 2.902 1.168.188 2.352.327 3.55.414.28.02.521.18.642.413l1.891 3.664a.25.25 0 00.434 0l1.891-3.664c.121-.233.362-.393.642-.413 1.198-.087 2.382-.226 3.55-.414C18.007 13.244 19 11.987 19 10.574V5.426c0-1.412-.993-2.67-2.43-2.902A41.289 41.289 0 0010 2z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm font-semibold text-accent-blue">Chat Global</span>
            </div>
            <button id="close-chat-btn" class="text-secondary-text hover:text-accent-blue text-xl font-bold transition-colors">
                ×
            </button>
        </div>
        <div id="chat-messages" class="h-64 overflow-y-auto p-3 flex flex-col gap-2 bg-surface-default">
            <!-- Mensagens aparecerão aqui -->
        </div>
        <form id="chat-form" class="p-3 border-t border-border-color bg-surface-elevated">
            <div class="flex gap-2">
                <input type="text" id="chat-input" placeholder="Digite sua mensagem..." class="flex-1 p-2 rounded bg-surface-default border border-border-color text-primary-text focus:border-accent-blue focus:outline-none" required autocomplete="off">
                <button type="submit" class="px-4 py-2 bg-accent-blue hover:bg-blue-600 text-white rounded font-medium transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-surface-elevated flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                    </svg>
                    Enviar
                </button>
            </div>
        </form>
    </div>

    <!-- Container de notificações -->
    <div id="notification-container" class="fixed top-5 right-5 z-50 flex flex-col gap-3">
    </div>

    <!-- Modal de logs moderno -->
    <div id="log-modal" class="fixed inset-0 bg-black/60 z-50 hidden items-center justify-center">
        <div class="bg-surface-elevated rounded-lg shadow-xl w-full max-w-2xl border border-border-color">
            <div class="flex justify-between items-center p-4 border-b border-border-color">
                <h3 class="text-lg font-semibold text-primary-text">Logs do Sistema</h3>
                <button id="close-log-modal-btn" class="text-gray-400 hover:text-white">&times;</button>
            </div>
            <div id="log-content" class="p-4 h-96 overflow-y-auto">
            </div>
        </div>
    </div>

    <!-- Conteúdo principal -->
    <main class="flex-1 p-4 overflow-y-auto">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer de navegação moderno -->
    <footer id="footer-nav" class="border-t border-border-color fixed bottom-0 w-full shadow-lg" style="display: none; background: rgba(15, 23, 42, 0.98); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px);">
        <div class="flex justify-evenly items-center h-20 w-full px-2">
            <!-- Botão Shop -->
            <a href="#" class="nav-item text-center rounded-lg flex flex-col items-center justify-center transition-all duration-300 hover:transform hover:scale-105 p-3" data-section-id="shop-section">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 mb-1 text-accent-blue">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
                </svg>
                <div class="text-xs text-secondary-text">Shop</div>
            </a>

            <!-- Botão Scan -->
            <a href="#" class="nav-item text-center rounded-lg flex flex-col items-center justify-center transition-all duration-300 hover:transform hover:scale-105 p-3" data-section-id="scan-section">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 mb-1 text-accent-blue">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                </svg>
                <div class="text-xs text-secondary-text">Scan</div>
            </a>

            <!-- Botão Início -->
            <a href="#" class="nav-item text-center rounded-lg flex flex-col items-center justify-center transition-all duration-300 hover:transform hover:scale-105 p-3" data-section-id="main-dashboard">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 mb-1 text-accent-blue">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                </svg>
                <div class="text-xs text-secondary-text">Home</div>
            </a>

            <!-- Botão Grupo -->
            <a href="#" class="nav-item text-center rounded-lg flex flex-col items-center justify-center transition-all duration-300 hover:transform hover:scale-105 p-3" data-section-id="group-section">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 mb-1 text-accent-blue">
                    <path fill-rule="evenodd" d="M8.25 6.75a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0ZM15.75 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM2.25 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM6.31 15.117A6.745 6.745 0 0 1 12 12a6.745 6.745 0 0 1 6.709 7.498.75.75 0 0 1-.372.568A12.696 12.696 0 0 1 12 21.75c-2.305 0-4.47-.612-6.337-1.684a.75.75 0 0 1-.372-.568 6.787 6.787 0 0 1 1.019-4.38Z" clip-rule="evenodd" />
                </svg>
                <div class="text-xs text-secondary-text">Grupo</div>
            </a>

            <!-- Botão News -->
            <a href="#" class="nav-item text-center rounded-lg flex flex-col items-center justify-center transition-all duration-300 hover:transform hover:scale-105 p-3" data-section-id="news-section">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 mb-1 text-accent-blue">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 01-2.25 2.25M16.5 7.5V18a2.25 2.25 0 002.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 002.25 2.25h6.375a4.5 4.5 0 003-1.125z" />
                </svg>
                <div class="text-xs text-secondary-text">News</div>
            </a>
        </div>
    </footer>

    <!-- Sistema de Autenticação Simples (sem Firebase) -->
    <script src="{{ url_for('static', filename='js/simple-auth.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>