body {
    background-color: #0d1117; /* Cor de fundo escura */
    color: #c9d1d9; /* Cor do texto clara */
    font-family: 'Courier New', Courier, monospace; /* Fonte de terminal */
    margin: 20px;
}

h1, h2 {
    color: #58a6ff; /* A<PERSON>l para títulos */
}

a, button, input[type="submit"] {
    background-color: #238636; /* Verde para botões e links */
    color: white;
    padding: 10px 15px;
    border: none;
    text-decoration: none;
    cursor: pointer;
    border-radius: 5px;
}

.flash {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
    background-color: #3fb950;
    color: white;
}

/* Estilos para Scanner de Alvos */
.target-card {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border: 1px solid #4b5563;
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.target-card:hover {
    border-color: #06b6d4;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(6, 182, 212, 0.2);
}

.target-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #06b6d4, #3b82f6, #8b5cf6);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.target-card:hover::before {
    opacity: 1;
}

.status-online {
    width: 12px;
    height: 12px;
    background-color: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.1);
    }
}

.refresh-button {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.refresh-button:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.refresh-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.exploit-button {
    background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    color: white;
    font-weight: 700;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.exploit-button:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #7f1d1d 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

/* Animação de loading para scan */
.scan-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #374151;
    border-top: 3px solid #06b6d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar Styles */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #4f46e5 #1f2937;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 12px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 6px;
    border: 1px solid #374151;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #4f46e5, #3730a3);
    border-radius: 6px;
    border: 2px solid #1f2937;
    box-shadow: inset 0 0 6px rgba(79, 70, 229, 0.3);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #6366f1, #4338ca);
    box-shadow: inset 0 0 8px rgba(79, 70, 229, 0.5);
}

.custom-scrollbar::-webkit-scrollbar-corner {
    background: #1f2937;
}

/* Enhanced scrollbar for ranking sections */
.ranking-scrollbar {
    scrollbar-width: auto;
    scrollbar-color: #a855f7 #1f2937;
}

.ranking-scrollbar::-webkit-scrollbar {
    width: 16px;
}

.ranking-scrollbar::-webkit-scrollbar-track {
    background: linear-gradient(180deg, #374151 0%, #1f2937 100%);
    border-radius: 10px;
    border: 2px solid #4b5563;
    margin: 6px 0;
}

.ranking-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #a855f7 0%, #7c3aed 50%, #6366f1 100%);
    border-radius: 10px;
    border: 3px solid #1f2937;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 3px 6px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(168, 85, 247, 0.3);
    transition: all 0.3s ease;
}

.ranking-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #c084fc 0%, #9333ea 50%, #8b5cf6 100%);
    border-color: #374151;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.3),
        0 0 0 2px rgba(168, 85, 247, 0.5);
    transform: scale(1.1);
}

.ranking-scrollbar::-webkit-scrollbar-corner {
    background: #1f2937;
}

/* Estilos do slider de porcentagem */
.slider {
    background: linear-gradient(to right, #4ade80 0%, #4ade80 20%, #374151 20%, #374151 100%);
}

.slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #10b981;
    cursor: pointer;
    border: 2px solid #065f46;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #10b981;
    cursor: pointer;
    border: 2px solid #065f46;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.slider::-webkit-slider-track {
    height: 8px;
    border-radius: 4px;
}

.slider::-moz-range-track {
    height: 8px;
    border-radius: 4px;
}

/* Animações para o modal bancário */
.fixed.inset-0 {
    animation: fadeIn 0.3s ease-out;
}

.fixed.inset-0 > div {
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}