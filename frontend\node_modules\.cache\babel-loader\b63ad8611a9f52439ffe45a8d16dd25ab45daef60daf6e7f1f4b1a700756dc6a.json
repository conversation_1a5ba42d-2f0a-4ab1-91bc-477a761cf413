{"ast": null, "code": "// src/mutationObserver.ts\nimport { getDefaultState } from \"./mutation.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { hashKey, shallowEqualObjects } from \"./utils.js\";\nvar MutationObserver = class extends Subscribable {\n  #client;\n  #currentResult = void 0;\n  #currentMutation;\n  #mutateOptions;\n  constructor(client, options) {\n    super();\n    this.#client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.#updateResult();\n  }\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    this.options = this.#client.defaultMutationOptions(options);\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: \"observerOptionsUpdated\",\n        mutation: this.#currentMutation,\n        observer: this\n      });\n    }\n    if (prevOptions?.mutationKey && this.options.mutationKey && hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)) {\n      this.reset();\n    } else if (this.#currentMutation?.state.status === \"pending\") {\n      this.#currentMutation.setOptions(this.options);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this);\n    }\n  }\n  onMutationUpdate(action) {\n    this.#updateResult();\n    this.#notify(action);\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  reset() {\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = void 0;\n    this.#updateResult();\n    this.#notify();\n  }\n  mutate(variables, options) {\n    this.#mutateOptions = options;\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = this.#client.getMutationCache().build(this.#client, this.options);\n    this.#currentMutation.addObserver(this);\n    return this.#currentMutation.execute(variables);\n  }\n  #updateResult() {\n    const state = this.#currentMutation?.state ?? getDefaultState();\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === \"pending\",\n      isSuccess: state.status === \"success\",\n      isError: state.status === \"error\",\n      isIdle: state.status === \"idle\",\n      mutate: this.mutate,\n      reset: this.reset\n    };\n  }\n  #notify(action) {\n    notifyManager.batch(() => {\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables;\n        const context = this.#currentResult.context;\n        if (action?.type === \"success\") {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context);\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context);\n        } else if (action?.type === \"error\") {\n          this.#mutateOptions.onError?.(action.error, variables, context);\n          this.#mutateOptions.onSettled?.(void 0, action.error, variables, context);\n        }\n      }\n      this.listeners.forEach(listener => {\n        listener(this.#currentResult);\n      });\n    });\n  }\n};\nexport { MutationObserver };", "map": {"version": 3, "names": ["getDefaultState", "notify<PERSON><PERSON>ger", "Subscribable", "hash<PERSON><PERSON>", "shallowEqualObjects", "MutationObserver", "client", "currentResult", "currentMutation", "mutateOptions", "constructor", "options", "setOptions", "bindMethods", "updateResult", "mutate", "bind", "reset", "prevOptions", "defaultMutationOptions", "getMutationCache", "notify", "type", "mutation", "observer", "<PERSON><PERSON><PERSON>", "state", "status", "onUnsubscribe", "hasListeners", "removeObserver", "onMutationUpdate", "action", "getCurrentResult", "variables", "build", "addObserver", "execute", "#updateResult", "isPending", "isSuccess", "isError", "isIdle", "#notify", "batch", "context", "onSuccess", "data", "onSettled", "onError", "error", "listeners", "for<PERSON>ach", "listener"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\node_modules\\@tanstack\\query-core\\src\\mutationObserver.ts"], "sourcesContent": ["import { getDefaultState } from './mutation'\nimport { notify<PERSON>anager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { hashKey, shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  DefaultError,\n  MutateOptions,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  #client: QueryClient\n  #currentResult: MutationObserverResult<TData, TError, TVariables, TContext> =\n    undefined!\n  #currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  #mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.#client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.#updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options as\n      | MutationObserverOptions<TData, TError, TVariables, TContext>\n      | undefined\n    this.options = this.#client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.#currentMutation,\n        observer: this,\n      })\n    }\n\n    if (\n      prevOptions?.mutationKey &&\n      this.options.mutationKey &&\n      hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)\n    ) {\n      this.reset()\n    } else if (this.#currentMutation?.state.status === 'pending') {\n      this.#currentMutation.setOptions(this.options)\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.#updateResult()\n\n    this.#notify(action)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.#currentResult\n  }\n\n  reset(): void {\n    // reset needs to remove the observer from the mutation because there is no way to \"get it back\"\n    // another mutate call will yield a new mutation!\n    this.#currentMutation?.removeObserver(this)\n    this.#currentMutation = undefined\n    this.#updateResult()\n    this.#notify()\n  }\n\n  mutate(\n    variables: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.#mutateOptions = options\n\n    this.#currentMutation?.removeObserver(this)\n\n    this.#currentMutation = this.#client\n      .getMutationCache()\n      .build(this.#client, this.options)\n\n    this.#currentMutation.addObserver(this)\n\n    return this.#currentMutation.execute(variables)\n  }\n\n  #updateResult(): void {\n    const state =\n      this.#currentMutation?.state ??\n      getDefaultState<TData, TError, TVariables, TContext>()\n\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === 'pending',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    } as MutationObserverResult<TData, TError, TVariables, TContext>\n  }\n\n  #notify(action?: Action<TData, TError, TVariables, TContext>): void {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables!\n        const context = this.#currentResult.context\n\n        if (action?.type === 'success') {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context!)\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context)\n        } else if (action?.type === 'error') {\n          this.#mutateOptions.onError?.(action.error, variables, context)\n          this.#mutateOptions.onSettled?.(\n            undefined,\n            action.error,\n            variables,\n            context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult)\n      })\n    })\n  }\n}\n"], "mappings": ";AAAA,SAASA,eAAA,QAAuB;AAChC,SAASC,aAAA,QAAqB;AAC9B,SAASC,YAAA,QAAoB;AAC7B,SAASC,OAAA,EAASC,mBAAA,QAA2B;AAkBtC,IAAMC,gBAAA,GAAN,cAKGH,YAAA,CAER;EAGA,CAAAI,MAAA;EACA,CAAAC,aAAA,GACE;EACF,CAAAC,eAAA;EACA,CAAAC,aAAA;EAEAC,YACEJ,MAAA,EACAK,OAAA,EACA;IACA,MAAM;IAEN,KAAK,CAAAL,MAAA,GAAUA,MAAA;IACf,KAAKM,UAAA,CAAWD,OAAO;IACvB,KAAKE,WAAA,CAAY;IACjB,KAAK,CAAAC,YAAA,CAAc;EACrB;EAEUD,YAAA,EAAoB;IAC5B,KAAKE,MAAA,GAAS,KAAKA,MAAA,CAAOC,IAAA,CAAK,IAAI;IACnC,KAAKC,KAAA,GAAQ,KAAKA,KAAA,CAAMD,IAAA,CAAK,IAAI;EACnC;EAEAJ,WACED,OAAA,EACA;IACA,MAAMO,WAAA,GAAc,KAAKP,OAAA;IAGzB,KAAKA,OAAA,GAAU,KAAK,CAAAL,MAAA,CAAQa,sBAAA,CAAuBR,OAAO;IAC1D,IAAI,CAACP,mBAAA,CAAoB,KAAKO,OAAA,EAASO,WAAW,GAAG;MACnD,KAAK,CAAAZ,MAAA,CAAQc,gBAAA,CAAiB,EAAEC,MAAA,CAAO;QACrCC,IAAA,EAAM;QACNC,QAAA,EAAU,KAAK,CAAAf,eAAA;QACfgB,QAAA,EAAU;MACZ,CAAC;IACH;IAEA,IACEN,WAAA,EAAaO,WAAA,IACb,KAAKd,OAAA,CAAQc,WAAA,IACbtB,OAAA,CAAQe,WAAA,CAAYO,WAAW,MAAMtB,OAAA,CAAQ,KAAKQ,OAAA,CAAQc,WAAW,GACrE;MACA,KAAKR,KAAA,CAAM;IACb,WAAW,KAAK,CAAAT,eAAA,EAAkBkB,KAAA,CAAMC,MAAA,KAAW,WAAW;MAC5D,KAAK,CAAAnB,eAAA,CAAiBI,UAAA,CAAW,KAAKD,OAAO;IAC/C;EACF;EAEUiB,cAAA,EAAsB;IAC9B,IAAI,CAAC,KAAKC,YAAA,CAAa,GAAG;MACxB,KAAK,CAAArB,eAAA,EAAkBsB,cAAA,CAAe,IAAI;IAC5C;EACF;EAEAC,iBAAiBC,MAAA,EAA2D;IAC1E,KAAK,CAAAlB,YAAA,CAAc;IAEnB,KAAK,CAAAO,MAAA,CAAQW,MAAM;EACrB;EAEAC,iBAAA,EAKE;IACA,OAAO,KAAK,CAAA1B,aAAA;EACd;EAEAU,MAAA,EAAc;IAGZ,KAAK,CAAAT,eAAA,EAAkBsB,cAAA,CAAe,IAAI;IAC1C,KAAK,CAAAtB,eAAA,GAAmB;IACxB,KAAK,CAAAM,YAAA,CAAc;IACnB,KAAK,CAAAO,MAAA,CAAQ;EACf;EAEAN,OACEmB,SAAA,EACAvB,OAAA,EACgB;IAChB,KAAK,CAAAF,aAAA,GAAiBE,OAAA;IAEtB,KAAK,CAAAH,eAAA,EAAkBsB,cAAA,CAAe,IAAI;IAE1C,KAAK,CAAAtB,eAAA,GAAmB,KAAK,CAAAF,MAAA,CAC1Bc,gBAAA,CAAiB,EACjBe,KAAA,CAAM,KAAK,CAAA7B,MAAA,EAAS,KAAKK,OAAO;IAEnC,KAAK,CAAAH,eAAA,CAAiB4B,WAAA,CAAY,IAAI;IAEtC,OAAO,KAAK,CAAA5B,eAAA,CAAiB6B,OAAA,CAAQH,SAAS;EAChD;EAEA,CAAApB,YAAAwB,CAAA,EAAsB;IACpB,MAAMZ,KAAA,GACJ,KAAK,CAAAlB,eAAA,EAAkBkB,KAAA,IACvB1B,eAAA,CAAqD;IAEvD,KAAK,CAAAO,aAAA,GAAiB;MACpB,GAAGmB,KAAA;MACHa,SAAA,EAAWb,KAAA,CAAMC,MAAA,KAAW;MAC5Ba,SAAA,EAAWd,KAAA,CAAMC,MAAA,KAAW;MAC5Bc,OAAA,EAASf,KAAA,CAAMC,MAAA,KAAW;MAC1Be,MAAA,EAAQhB,KAAA,CAAMC,MAAA,KAAW;MACzBZ,MAAA,EAAQ,KAAKA,MAAA;MACbE,KAAA,EAAO,KAAKA;IACd;EACF;EAEA,CAAAI,MAAAsB,CAAQX,MAAA,EAA4D;IAClE/B,aAAA,CAAc2C,KAAA,CAAM,MAAM;MAExB,IAAI,KAAK,CAAAnC,aAAA,IAAkB,KAAKoB,YAAA,CAAa,GAAG;QAC9C,MAAMK,SAAA,GAAY,KAAK,CAAA3B,aAAA,CAAe2B,SAAA;QACtC,MAAMW,OAAA,GAAU,KAAK,CAAAtC,aAAA,CAAesC,OAAA;QAEpC,IAAIb,MAAA,EAAQV,IAAA,KAAS,WAAW;UAC9B,KAAK,CAAAb,aAAA,CAAeqC,SAAA,GAAYd,MAAA,CAAOe,IAAA,EAAMb,SAAA,EAAWW,OAAQ;UAChE,KAAK,CAAApC,aAAA,CAAeuC,SAAA,GAAYhB,MAAA,CAAOe,IAAA,EAAM,MAAMb,SAAA,EAAWW,OAAO;QACvE,WAAWb,MAAA,EAAQV,IAAA,KAAS,SAAS;UACnC,KAAK,CAAAb,aAAA,CAAewC,OAAA,GAAUjB,MAAA,CAAOkB,KAAA,EAAOhB,SAAA,EAAWW,OAAO;UAC9D,KAAK,CAAApC,aAAA,CAAeuC,SAAA,GAClB,QACAhB,MAAA,CAAOkB,KAAA,EACPhB,SAAA,EACAW,OACF;QACF;MACF;MAGA,KAAKM,SAAA,CAAUC,OAAA,CAASC,QAAA,IAAa;QACnCA,QAAA,CAAS,KAAK,CAAA9C,aAAc;MAC9B,CAAC;IACH,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}