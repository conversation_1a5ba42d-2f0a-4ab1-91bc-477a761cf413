{"ast": null, "code": "// src/query.ts\nimport { ensureQueryFn, noop, replaceData, resolveEnabled, resolveStaleTime, skipToken, timeUntilStale } from \"./utils.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { canFetch, createRetryer, isCancelledError } from \"./retryer.js\";\nimport { Removable } from \"./removable.js\";\nvar Query = class extends Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = {\n      ...this.#defaultOptions,\n      ...options\n    };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({\n      type: \"setState\",\n      state,\n      setStateOptions\n    });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(observer => resolveEnabled(observer.options.enabled, this) !== false);\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStatic() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(observer => resolveStaleTime(observer.options.staleTime, this) === \"static\");\n    }\n    return false;\n  }\n  isStale() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(observer => observer.getCurrentResult().isStale);\n    }\n    return this.state.data === void 0 || this.state.isInvalidated;\n  }\n  isStaleByTime(staleTime = 0) {\n    if (this.state.data === void 0) {\n      return true;\n    }\n    if (staleTime === \"static\") {\n      return false;\n    }\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    return !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n    observer?.refetch({\n      cancelRefetch: false\n    });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n    observer?.refetch({\n      cancelRefetch: false\n    });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({\n        type: \"observerAdded\",\n        query: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({\n        type: \"observerRemoved\",\n        query: this,\n        observer\n      });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({\n        type: \"invalidate\"\n      });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({\n          silent: true\n        });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(`As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`);\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = object => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions);\n      const createQueryFnContext = () => {\n        const queryFnContext2 = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta\n        };\n        addSignalProperty(queryFnContext2);\n        return queryFnContext2;\n      };\n      const queryFnContext = createQueryFnContext();\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(queryFn, queryFnContext, this);\n      }\n      return queryFn(queryFnContext);\n    };\n    const createFetchContext = () => {\n      const context2 = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn\n      };\n      addSignalProperty(context2);\n      return context2;\n    };\n    const context = createFetchContext();\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({\n        type: \"fetch\",\n        meta: context.fetchOptions?.meta\n      });\n    }\n    const onError = error => {\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!isCancelledError(error)) {\n        this.#cache.config.onError?.(error, this);\n        this.#cache.config.onSettled?.(this.state.data, error, this);\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: data => {\n        if (data === void 0) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`);\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(data, this.state.error, this);\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({\n          type: \"failed\",\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.#dispatch({\n          type: \"pause\"\n        });\n      },\n      onContinue: () => {\n        this.#dispatch({\n          type: \"continue\"\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          this.#revertState = void 0;\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...(!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n        case \"error\":\n          const error = action.error;\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return {\n              ...this.#revertState,\n              fetchStatus: \"idle\"\n            };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({\n        query: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? \"fetching\" : \"paused\",\n    ...(data === void 0 && {\n      error: null,\n      status: \"pending\"\n    })\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\nexport { Query, fetchState };", "map": {"version": 3, "names": ["ensureQueryFn", "noop", "replaceData", "resolveEnabled", "resolveStaleTime", "skipToken", "timeUntilStale", "notify<PERSON><PERSON>ger", "canFetch", "createRetryer", "isCancelledError", "Removable", "Query", "initialState", "revertState", "cache", "client", "retryer", "defaultOptions", "abortSignalConsumed", "constructor", "config", "setOptions", "options", "observers", "get<PERSON><PERSON><PERSON><PERSON>ache", "query<PERSON><PERSON>", "queryHash", "getDefaultState", "state", "scheduleGc", "meta", "promise", "updateGcTime", "gcTime", "optionalRemove", "length", "fetchStatus", "remove", "setData", "newData", "data", "dispatch", "type", "dataUpdatedAt", "updatedAt", "manual", "setState", "setStateOptions", "cancel", "then", "catch", "Promise", "resolve", "destroy", "silent", "reset", "isActive", "some", "observer", "enabled", "isDisabled", "getObserversCount", "queryFn", "dataUpdateCount", "errorUpdateCount", "isStatic", "staleTime", "isStale", "getCurrentResult", "isInvalidated", "isStaleByTime", "onFocus", "find", "x", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "continue", "onOnline", "shouldFetchOnReconnect", "addObserver", "includes", "push", "clearGcTimeout", "notify", "query", "removeObserver", "filter", "revert", "cancelRetry", "invalidate", "fetch", "fetchOptions", "continueRetry", "process", "env", "NODE_ENV", "Array", "isArray", "console", "error", "abortController", "AbortController", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "fetchFn", "createQueryFnContext", "queryFnContext2", "queryFnContext", "persister", "createFetchContext", "context2", "context", "behavior", "onFetch", "fetchMeta", "onError", "onSettled", "initialPromise", "fn", "abort", "bind", "onSuccess", "Error", "onFail", "failureCount", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "canRun", "start", "#dispatch", "action", "reducer", "fetchFailureCount", "fetchFailureReason", "fetchState", "Date", "now", "status", "errorUpdatedAt", "batch", "for<PERSON>ach", "onQueryUpdate", "initialData", "hasData", "initialDataUpdatedAt"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\node_modules\\@tanstack\\query-core\\src\\query.ts"], "sourcesContent": ["import {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  skipToken,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type { QueryCache } from './queryCache'\nimport type { QueryClient } from './queryClient'\nimport type {\n  CancelOptions,\n  DefaultError,\n  FetchStatus,\n  InitialDataFunction,\n  OmitKeyof,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n  StaleTime,\n} from './types'\nimport type { QueryObserver } from './queryObserver'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  client: QueryClient\n  queryKey: TQ<PERSON>y<PERSON>ey\n  queryHash: string\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = DefaultError> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: FetchMeta | null\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  client: QueryClient\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n    query: Query,\n  ) => void\n}\n\nexport type FetchDirection = 'forward' | 'backward'\n\nexport interface FetchMeta {\n  fetchMore?: { direction: FetchDirection }\n}\n\nexport interface FetchOptions<TData = unknown> {\n  cancelRefetch?: boolean\n  meta?: FetchMeta\n  initialPromise?: Promise<TData>\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: FetchMeta\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state: QueryState<TData, TError>\n\n  #initialState: QueryState<TData, TError>\n  #revertState?: QueryState<TData, TError>\n  #cache: QueryCache\n  #client: QueryClient\n  #retryer?: Retryer<TData>\n  observers: Array<QueryObserver<any, any, any, any, any>>\n  #defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  #abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.#abortSignalConsumed = false\n    this.#defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.#client = config.client\n    this.#cache = this.#client.getQueryCache()\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.#initialState = getDefaultState(this.options)\n    this.state = config.state ?? this.#initialState\n    this.scheduleGc()\n  }\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  get promise(): Promise<TData> | undefined {\n    return this.#retryer?.promise\n  }\n\n  setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.#defaultOptions, ...options }\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.#cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.#dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.#dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.#retryer?.promise\n    this.#retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.#initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false,\n    )\n  }\n\n  isDisabled(): boolean {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive()\n    }\n    // if a query has no observers, it should still be considered disabled if it never attempted a fetch\n    return (\n      this.options.queryFn === skipToken ||\n      this.state.dataUpdateCount + this.state.errorUpdateCount === 0\n    )\n  }\n\n  isStatic(): boolean {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) =>\n          resolveStaleTime(observer.options.staleTime, this) === 'static',\n      )\n    }\n\n    return false\n  }\n\n  isStale(): boolean {\n    // check observers first, their `isStale` has the source of truth\n    // calculated with `isStaleByTime` and it takes `enabled` into account\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale,\n      )\n    }\n\n    return this.state.data === undefined || this.state.isInvalidated\n  }\n\n  isStaleByTime(staleTime: StaleTime = 0): boolean {\n    // no data is always stale\n    if (this.state.data === undefined) {\n      return true\n    }\n    // static is never stale\n    if (staleTime === 'static') {\n      return false\n    }\n    // if the query is invalidated, it is stale\n    if (this.state.isInvalidated) {\n      return true\n    }\n\n    return !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.#cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true })\n          } else {\n            this.#retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.#cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions<TQueryFnData>,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.data !== undefined && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetch\n        this.cancel({ silent: true })\n      } else if (this.#retryer) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.#retryer.continueRetry()\n        // Return current promise if we are already fetching\n        return this.#retryer.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = new AbortController()\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true\n          return abortController.signal\n        },\n      })\n    }\n\n    // Create fetch function\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions)\n\n      // Create query function context\n      const createQueryFnContext = (): QueryFunctionContext<TQueryKey> => {\n        const queryFnContext: OmitKeyof<\n          QueryFunctionContext<TQueryKey>,\n          'signal'\n        > = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta,\n        }\n        addSignalProperty(queryFnContext)\n        return queryFnContext as QueryFunctionContext<TQueryKey>\n      }\n\n      const queryFnContext = createQueryFnContext()\n\n      this.#abortSignalConsumed = false\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this as unknown as Query,\n        )\n      }\n\n      return queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const createFetchContext = (): FetchContext<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey\n    > => {\n      const context: OmitKeyof<\n        FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n        'signal'\n      > = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn,\n      }\n\n      addSignalProperty(context)\n      return context as FetchContext<TQueryFnData, TError, TData, TQueryKey>\n    }\n\n    const context = createFetchContext()\n\n    this.options.behavior?.onFetch(context, this as unknown as Query)\n\n    // Store state in case the current fetch needs to be reverted\n    this.#revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.#dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.#cache.config.onError?.(\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n      }\n\n      // Schedule query gc after fetching\n      this.scheduleGc()\n    }\n\n    // Try to fetch the data\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise as\n        | Promise<TData>\n        | undefined,\n      fn: context.fetchFn as () => Promise<TData>,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === undefined) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        try {\n          this.setData(data)\n        } catch (error) {\n          onError(error as TError)\n          return\n        }\n\n        // Notify cache callback\n        this.#cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error as any,\n          this as Query<any, any, any, any>,\n        )\n\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.#dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true,\n    })\n\n    return this.#retryer.start()\n  }\n\n  #dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null,\n          }\n        case 'success':\n          // If fetching ends successfully, we don't need revertState as a fallback anymore.\n          this.#revertState = undefined\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error\n\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate()\n      })\n\n      this.#cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nexport function fetchState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  data: TData | undefined,\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? 'fetching' : 'paused',\n    ...(data === undefined &&\n      ({\n        error: null,\n        status: 'pending',\n      } as const)),\n  } as const\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = data !== undefined\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? (initialDataUpdatedAt ?? Date.now()) : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'pending',\n    fetchStatus: 'idle',\n  }\n}\n"], "mappings": ";AAAA,SACEA,aAAA,EACAC,IAAA,EACAC,WAAA,EACAC,cAAA,EACAC,gBAAA,EACAC,SAAA,EACAC,cAAA,QACK;AACP,SAASC,aAAA,QAAqB;AAC9B,SAASC,QAAA,EAAUC,aAAA,EAAeC,gBAAA,QAAwB;AAC1D,SAASC,SAAA,QAAiB;AAmJnB,IAAMC,KAAA,GAAN,cAKGD,SAAA,CAAU;EAMlB,CAAAE,YAAA;EACA,CAAAC,WAAA;EACA,CAAAC,KAAA;EACA,CAAAC,MAAA;EACA,CAAAC,OAAA;EAEA,CAAAC,cAAA;EACA,CAAAC,mBAAA;EAEAC,YAAYC,MAAA,EAA6D;IACvE,MAAM;IAEN,KAAK,CAAAF,mBAAA,GAAuB;IAC5B,KAAK,CAAAD,cAAA,GAAkBG,MAAA,CAAOH,cAAA;IAC9B,KAAKI,UAAA,CAAWD,MAAA,CAAOE,OAAO;IAC9B,KAAKC,SAAA,GAAY,EAAC;IAClB,KAAK,CAAAR,MAAA,GAAUK,MAAA,CAAOL,MAAA;IACtB,KAAK,CAAAD,KAAA,GAAS,KAAK,CAAAC,MAAA,CAAQS,aAAA,CAAc;IACzC,KAAKC,QAAA,GAAWL,MAAA,CAAOK,QAAA;IACvB,KAAKC,SAAA,GAAYN,MAAA,CAAOM,SAAA;IACxB,KAAK,CAAAd,YAAA,GAAgBe,eAAA,CAAgB,KAAKL,OAAO;IACjD,KAAKM,KAAA,GAAQR,MAAA,CAAOQ,KAAA,IAAS,KAAK,CAAAhB,YAAA;IAClC,KAAKiB,UAAA,CAAW;EAClB;EACA,IAAIC,KAAA,EAA8B;IAChC,OAAO,KAAKR,OAAA,CAAQQ,IAAA;EACtB;EAEA,IAAIC,QAAA,EAAsC;IACxC,OAAO,KAAK,CAAAf,OAAA,EAAUe,OAAA;EACxB;EAEAV,WACEC,OAAA,EACM;IACN,KAAKA,OAAA,GAAU;MAAE,GAAG,KAAK,CAAAL,cAAA;MAAiB,GAAGK;IAAQ;IAErD,KAAKU,YAAA,CAAa,KAAKV,OAAA,CAAQW,MAAM;EACvC;EAEUC,eAAA,EAAiB;IACzB,IAAI,CAAC,KAAKX,SAAA,CAAUY,MAAA,IAAU,KAAKP,KAAA,CAAMQ,WAAA,KAAgB,QAAQ;MAC/D,KAAK,CAAAtB,KAAA,CAAOuB,MAAA,CAAO,IAAI;IACzB;EACF;EAEAC,QACEC,OAAA,EACAjB,OAAA,EACO;IACP,MAAMkB,IAAA,GAAOvC,WAAA,CAAY,KAAK2B,KAAA,CAAMY,IAAA,EAAMD,OAAA,EAAS,KAAKjB,OAAO;IAG/D,KAAK,CAAAmB,QAAA,CAAU;MACbD,IAAA;MACAE,IAAA,EAAM;MACNC,aAAA,EAAerB,OAAA,EAASsB,SAAA;MACxBC,MAAA,EAAQvB,OAAA,EAASuB;IACnB,CAAC;IAED,OAAOL,IAAA;EACT;EAEAM,SACElB,KAAA,EACAmB,eAAA,EACM;IACN,KAAK,CAAAN,QAAA,CAAU;MAAEC,IAAA,EAAM;MAAYd,KAAA;MAAOmB;IAAgB,CAAC;EAC7D;EAEAC,OAAO1B,OAAA,EAAwC;IAC7C,MAAMS,OAAA,GAAU,KAAK,CAAAf,OAAA,EAAUe,OAAA;IAC/B,KAAK,CAAAf,OAAA,EAAUgC,MAAA,CAAO1B,OAAO;IAC7B,OAAOS,OAAA,GAAUA,OAAA,CAAQkB,IAAA,CAAKjD,IAAI,EAAEkD,KAAA,CAAMlD,IAAI,IAAImD,OAAA,CAAQC,OAAA,CAAQ;EACpE;EAEAC,QAAA,EAAgB;IACd,MAAMA,OAAA,CAAQ;IAEd,KAAKL,MAAA,CAAO;MAAEM,MAAA,EAAQ;IAAK,CAAC;EAC9B;EAEAC,MAAA,EAAc;IACZ,KAAKF,OAAA,CAAQ;IACb,KAAKP,QAAA,CAAS,KAAK,CAAAlC,YAAa;EAClC;EAEA4C,SAAA,EAAoB;IAClB,OAAO,KAAKjC,SAAA,CAAUkC,IAAA,CACnBC,QAAA,IAAaxD,cAAA,CAAewD,QAAA,CAASpC,OAAA,CAAQqC,OAAA,EAAS,IAAI,MAAM,KACnE;EACF;EAEAC,WAAA,EAAsB;IACpB,IAAI,KAAKC,iBAAA,CAAkB,IAAI,GAAG;MAChC,OAAO,CAAC,KAAKL,QAAA,CAAS;IACxB;IAEA,OACE,KAAKlC,OAAA,CAAQwC,OAAA,KAAY1D,SAAA,IACzB,KAAKwB,KAAA,CAAMmC,eAAA,GAAkB,KAAKnC,KAAA,CAAMoC,gBAAA,KAAqB;EAEjE;EAEAC,SAAA,EAAoB;IAClB,IAAI,KAAKJ,iBAAA,CAAkB,IAAI,GAAG;MAChC,OAAO,KAAKtC,SAAA,CAAUkC,IAAA,CACnBC,QAAA,IACCvD,gBAAA,CAAiBuD,QAAA,CAASpC,OAAA,CAAQ4C,SAAA,EAAW,IAAI,MAAM,QAC3D;IACF;IAEA,OAAO;EACT;EAEAC,QAAA,EAAmB;IAGjB,IAAI,KAAKN,iBAAA,CAAkB,IAAI,GAAG;MAChC,OAAO,KAAKtC,SAAA,CAAUkC,IAAA,CACnBC,QAAA,IAAaA,QAAA,CAASU,gBAAA,CAAiB,EAAED,OAC5C;IACF;IAEA,OAAO,KAAKvC,KAAA,CAAMY,IAAA,KAAS,UAAa,KAAKZ,KAAA,CAAMyC,aAAA;EACrD;EAEAC,cAAcJ,SAAA,GAAuB,GAAY;IAE/C,IAAI,KAAKtC,KAAA,CAAMY,IAAA,KAAS,QAAW;MACjC,OAAO;IACT;IAEA,IAAI0B,SAAA,KAAc,UAAU;MAC1B,OAAO;IACT;IAEA,IAAI,KAAKtC,KAAA,CAAMyC,aAAA,EAAe;MAC5B,OAAO;IACT;IAEA,OAAO,CAAChE,cAAA,CAAe,KAAKuB,KAAA,CAAMe,aAAA,EAAeuB,SAAS;EAC5D;EAEAK,QAAA,EAAgB;IACd,MAAMb,QAAA,GAAW,KAAKnC,SAAA,CAAUiD,IAAA,CAAMC,CAAA,IAAMA,CAAA,CAAEC,wBAAA,CAAyB,CAAC;IAExEhB,QAAA,EAAUiB,OAAA,CAAQ;MAAEC,aAAA,EAAe;IAAM,CAAC;IAG1C,KAAK,CAAA5D,OAAA,EAAU6D,QAAA,CAAS;EAC1B;EAEAC,SAAA,EAAiB;IACf,MAAMpB,QAAA,GAAW,KAAKnC,SAAA,CAAUiD,IAAA,CAAMC,CAAA,IAAMA,CAAA,CAAEM,sBAAA,CAAuB,CAAC;IAEtErB,QAAA,EAAUiB,OAAA,CAAQ;MAAEC,aAAA,EAAe;IAAM,CAAC;IAG1C,KAAK,CAAA5D,OAAA,EAAU6D,QAAA,CAAS;EAC1B;EAEAG,YAAYtB,QAAA,EAAwD;IAClE,IAAI,CAAC,KAAKnC,SAAA,CAAU0D,QAAA,CAASvB,QAAQ,GAAG;MACtC,KAAKnC,SAAA,CAAU2D,IAAA,CAAKxB,QAAQ;MAG5B,KAAKyB,cAAA,CAAe;MAEpB,KAAK,CAAArE,KAAA,CAAOsE,MAAA,CAAO;QAAE1C,IAAA,EAAM;QAAiB2C,KAAA,EAAO;QAAM3B;MAAS,CAAC;IACrE;EACF;EAEA4B,eAAe5B,QAAA,EAAwD;IACrE,IAAI,KAAKnC,SAAA,CAAU0D,QAAA,CAASvB,QAAQ,GAAG;MACrC,KAAKnC,SAAA,GAAY,KAAKA,SAAA,CAAUgE,MAAA,CAAQd,CAAA,IAAMA,CAAA,KAAMf,QAAQ;MAE5D,IAAI,CAAC,KAAKnC,SAAA,CAAUY,MAAA,EAAQ;QAG1B,IAAI,KAAK,CAAAnB,OAAA,EAAU;UACjB,IAAI,KAAK,CAAAE,mBAAA,EAAsB;YAC7B,KAAK,CAAAF,OAAA,CAASgC,MAAA,CAAO;cAAEwC,MAAA,EAAQ;YAAK,CAAC;UACvC,OAAO;YACL,KAAK,CAAAxE,OAAA,CAASyE,WAAA,CAAY;UAC5B;QACF;QAEA,KAAK5D,UAAA,CAAW;MAClB;MAEA,KAAK,CAAAf,KAAA,CAAOsE,MAAA,CAAO;QAAE1C,IAAA,EAAM;QAAmB2C,KAAA,EAAO;QAAM3B;MAAS,CAAC;IACvE;EACF;EAEAG,kBAAA,EAA4B;IAC1B,OAAO,KAAKtC,SAAA,CAAUY,MAAA;EACxB;EAEAuD,WAAA,EAAmB;IACjB,IAAI,CAAC,KAAK9D,KAAA,CAAMyC,aAAA,EAAe;MAC7B,KAAK,CAAA5B,QAAA,CAAU;QAAEC,IAAA,EAAM;MAAa,CAAC;IACvC;EACF;EAEAiD,MACErE,OAAA,EACAsE,YAAA,EACgB;IAChB,IAAI,KAAKhE,KAAA,CAAMQ,WAAA,KAAgB,QAAQ;MACrC,IAAI,KAAKR,KAAA,CAAMY,IAAA,KAAS,UAAaoD,YAAA,EAAchB,aAAA,EAAe;QAEhE,KAAK5B,MAAA,CAAO;UAAEM,MAAA,EAAQ;QAAK,CAAC;MAC9B,WAAW,KAAK,CAAAtC,OAAA,EAAU;QAExB,KAAK,CAAAA,OAAA,CAAS6E,aAAA,CAAc;QAE5B,OAAO,KAAK,CAAA7E,OAAA,CAASe,OAAA;MACvB;IACF;IAGA,IAAIT,OAAA,EAAS;MACX,KAAKD,UAAA,CAAWC,OAAO;IACzB;IAIA,IAAI,CAAC,KAAKA,OAAA,CAAQwC,OAAA,EAAS;MACzB,MAAMJ,QAAA,GAAW,KAAKnC,SAAA,CAAUiD,IAAA,CAAMC,CAAA,IAAMA,CAAA,CAAEnD,OAAA,CAAQwC,OAAO;MAC7D,IAAIJ,QAAA,EAAU;QACZ,KAAKrC,UAAA,CAAWqC,QAAA,CAASpC,OAAO;MAClC;IACF;IAEA,IAAIwE,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,IAAI,CAACC,KAAA,CAAMC,OAAA,CAAQ,KAAK5E,OAAA,CAAQG,QAAQ,GAAG;QACzC0E,OAAA,CAAQC,KAAA,CACN,qIACF;MACF;IACF;IAEA,MAAMC,eAAA,GAAkB,IAAIC,eAAA,CAAgB;IAK5C,MAAMC,iBAAA,GAAqBC,MAAA,IAAoB;MAC7CC,MAAA,CAAOC,cAAA,CAAeF,MAAA,EAAQ,UAAU;QACtCG,UAAA,EAAY;QACZC,GAAA,EAAKA,CAAA,KAAM;UACT,KAAK,CAAA1F,mBAAA,GAAuB;UAC5B,OAAOmF,eAAA,CAAgBQ,MAAA;QACzB;MACF,CAAC;IACH;IAGA,MAAMC,OAAA,GAAUA,CAAA,KAAM;MACpB,MAAMhD,OAAA,GAAU/D,aAAA,CAAc,KAAKuB,OAAA,EAASsE,YAAY;MAGxD,MAAMmB,oBAAA,GAAuBA,CAAA,KAAuC;QAClE,MAAMC,eAAA,GAGF;UACFjG,MAAA,EAAQ,KAAK,CAAAA,MAAA;UACbU,QAAA,EAAU,KAAKA,QAAA;UACfK,IAAA,EAAM,KAAKA;QACb;QACAyE,iBAAA,CAAkBS,eAAc;QAChC,OAAOA,eAAA;MACT;MAEA,MAAMC,cAAA,GAAiBF,oBAAA,CAAqB;MAE5C,KAAK,CAAA7F,mBAAA,GAAuB;MAC5B,IAAI,KAAKI,OAAA,CAAQ4F,SAAA,EAAW;QAC1B,OAAO,KAAK5F,OAAA,CAAQ4F,SAAA,CAClBpD,OAAA,EACAmD,cAAA,EACA,IACF;MACF;MAEA,OAAOnD,OAAA,CAAQmD,cAAc;IAC/B;IAGA,MAAME,kBAAA,GAAqBA,CAAA,KAKtB;MACH,MAAMC,QAAA,GAGF;QACFxB,YAAA;QACAtE,OAAA,EAAS,KAAKA,OAAA;QACdG,QAAA,EAAU,KAAKA,QAAA;QACfV,MAAA,EAAQ,KAAK,CAAAA,MAAA;QACba,KAAA,EAAO,KAAKA,KAAA;QACZkF;MACF;MAEAP,iBAAA,CAAkBa,QAAO;MACzB,OAAOA,QAAA;IACT;IAEA,MAAMC,OAAA,GAAUF,kBAAA,CAAmB;IAEnC,KAAK7F,OAAA,CAAQgG,QAAA,EAAUC,OAAA,CAAQF,OAAA,EAAS,IAAwB;IAGhE,KAAK,CAAAxG,WAAA,GAAe,KAAKe,KAAA;IAGzB,IACE,KAAKA,KAAA,CAAMQ,WAAA,KAAgB,UAC3B,KAAKR,KAAA,CAAM4F,SAAA,KAAcH,OAAA,CAAQzB,YAAA,EAAc9D,IAAA,EAC/C;MACA,KAAK,CAAAW,QAAA,CAAU;QAAEC,IAAA,EAAM;QAASZ,IAAA,EAAMuF,OAAA,CAAQzB,YAAA,EAAc9D;MAAK,CAAC;IACpE;IAEA,MAAM2F,OAAA,GAAWrB,KAAA,IAAyC;MAExD,IAAI,EAAE3F,gBAAA,CAAiB2F,KAAK,KAAKA,KAAA,CAAM9C,MAAA,GAAS;QAC9C,KAAK,CAAAb,QAAA,CAAU;UACbC,IAAA,EAAM;UACN0D;QACF,CAAC;MACH;MAEA,IAAI,CAAC3F,gBAAA,CAAiB2F,KAAK,GAAG;QAE5B,KAAK,CAAAtF,KAAA,CAAOM,MAAA,CAAOqG,OAAA,GACjBrB,KAAA,EACA,IACF;QACA,KAAK,CAAAtF,KAAA,CAAOM,MAAA,CAAOsG,SAAA,GACjB,KAAK9F,KAAA,CAAMY,IAAA,EACX4D,KAAA,EACA,IACF;MACF;MAGA,KAAKvE,UAAA,CAAW;IAClB;IAGA,KAAK,CAAAb,OAAA,GAAWR,aAAA,CAAc;MAC5BmH,cAAA,EAAgB/B,YAAA,EAAc+B,cAAA;MAG9BC,EAAA,EAAIP,OAAA,CAAQP,OAAA;MACZe,KAAA,EAAOxB,eAAA,CAAgBwB,KAAA,CAAMC,IAAA,CAAKzB,eAAe;MACjD0B,SAAA,EAAYvF,IAAA,IAAS;QACnB,IAAIA,IAAA,KAAS,QAAW;UACtB,IAAIsD,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;YACzCG,OAAA,CAAQC,KAAA,CACN,yIAAyI,KAAK1E,SAAS,EACzJ;UACF;UACA+F,OAAA,CAAQ,IAAIO,KAAA,CAAM,GAAG,KAAKtG,SAAS,oBAAoB,CAAQ;UAC/D;QACF;QAEA,IAAI;UACF,KAAKY,OAAA,CAAQE,IAAI;QACnB,SAAS4D,KAAA,EAAO;UACdqB,OAAA,CAAQrB,KAAe;UACvB;QACF;QAGA,KAAK,CAAAtF,KAAA,CAAOM,MAAA,CAAO2G,SAAA,GAAYvF,IAAA,EAAM,IAAiC;QACtE,KAAK,CAAA1B,KAAA,CAAOM,MAAA,CAAOsG,SAAA,GACjBlF,IAAA,EACA,KAAKZ,KAAA,CAAMwE,KAAA,EACX,IACF;QAGA,KAAKvE,UAAA,CAAW;MAClB;MACA4F,OAAA;MACAQ,MAAA,EAAQA,CAACC,YAAA,EAAc9B,KAAA,KAAU;QAC/B,KAAK,CAAA3D,QAAA,CAAU;UAAEC,IAAA,EAAM;UAAUwF,YAAA;UAAc9B;QAAM,CAAC;MACxD;MACA+B,OAAA,EAASA,CAAA,KAAM;QACb,KAAK,CAAA1F,QAAA,CAAU;UAAEC,IAAA,EAAM;QAAQ,CAAC;MAClC;MACA0F,UAAA,EAAYA,CAAA,KAAM;QAChB,KAAK,CAAA3F,QAAA,CAAU;UAAEC,IAAA,EAAM;QAAW,CAAC;MACrC;MACA2F,KAAA,EAAOhB,OAAA,CAAQ/F,OAAA,CAAQ+G,KAAA;MACvBC,UAAA,EAAYjB,OAAA,CAAQ/F,OAAA,CAAQgH,UAAA;MAC5BC,WAAA,EAAalB,OAAA,CAAQ/F,OAAA,CAAQiH,WAAA;MAC7BC,MAAA,EAAQA,CAAA,KAAM;IAChB,CAAC;IAED,OAAO,KAAK,CAAAxH,OAAA,CAASyH,KAAA,CAAM;EAC7B;EAEA,CAAAhG,QAAAiG,CAAUC,MAAA,EAAqC;IAC7C,MAAMC,OAAA,GACJhH,KAAA,IAC8B;MAC9B,QAAQ+G,MAAA,CAAOjG,IAAA;QACb,KAAK;UACH,OAAO;YACL,GAAGd,KAAA;YACHiH,iBAAA,EAAmBF,MAAA,CAAOT,YAAA;YAC1BY,kBAAA,EAAoBH,MAAA,CAAOvC;UAC7B;QACF,KAAK;UACH,OAAO;YACL,GAAGxE,KAAA;YACHQ,WAAA,EAAa;UACf;QACF,KAAK;UACH,OAAO;YACL,GAAGR,KAAA;YACHQ,WAAA,EAAa;UACf;QACF,KAAK;UACH,OAAO;YACL,GAAGR,KAAA;YACH,GAAGmH,UAAA,CAAWnH,KAAA,CAAMY,IAAA,EAAM,KAAKlB,OAAO;YACtCkG,SAAA,EAAWmB,MAAA,CAAO7G,IAAA,IAAQ;UAC5B;QACF,KAAK;UAEH,KAAK,CAAAjB,WAAA,GAAe;UACpB,OAAO;YACL,GAAGe,KAAA;YACHY,IAAA,EAAMmG,MAAA,CAAOnG,IAAA;YACbuB,eAAA,EAAiBnC,KAAA,CAAMmC,eAAA,GAAkB;YACzCpB,aAAA,EAAegG,MAAA,CAAOhG,aAAA,IAAiBqG,IAAA,CAAKC,GAAA,CAAI;YAChD7C,KAAA,EAAO;YACP/B,aAAA,EAAe;YACf6E,MAAA,EAAQ;YACR,IAAI,CAACP,MAAA,CAAO9F,MAAA,IAAU;cACpBT,WAAA,EAAa;cACbyG,iBAAA,EAAmB;cACnBC,kBAAA,EAAoB;YACtB;UACF;QACF,KAAK;UACH,MAAM1C,KAAA,GAAQuC,MAAA,CAAOvC,KAAA;UAErB,IAAI3F,gBAAA,CAAiB2F,KAAK,KAAKA,KAAA,CAAMZ,MAAA,IAAU,KAAK,CAAA3E,WAAA,EAAc;YAChE,OAAO;cAAE,GAAG,KAAK,CAAAA,WAAA;cAAcuB,WAAA,EAAa;YAAO;UACrD;UAEA,OAAO;YACL,GAAGR,KAAA;YACHwE,KAAA;YACApC,gBAAA,EAAkBpC,KAAA,CAAMoC,gBAAA,GAAmB;YAC3CmF,cAAA,EAAgBH,IAAA,CAAKC,GAAA,CAAI;YACzBJ,iBAAA,EAAmBjH,KAAA,CAAMiH,iBAAA,GAAoB;YAC7CC,kBAAA,EAAoB1C,KAAA;YACpBhE,WAAA,EAAa;YACb8G,MAAA,EAAQ;UACV;QACF,KAAK;UACH,OAAO;YACL,GAAGtH,KAAA;YACHyC,aAAA,EAAe;UACjB;QACF,KAAK;UACH,OAAO;YACL,GAAGzC,KAAA;YACH,GAAG+G,MAAA,CAAO/G;UACZ;MACJ;IACF;IAEA,KAAKA,KAAA,GAAQgH,OAAA,CAAQ,KAAKhH,KAAK;IAE/BtB,aAAA,CAAc8I,KAAA,CAAM,MAAM;MACxB,KAAK7H,SAAA,CAAU8H,OAAA,CAAS3F,QAAA,IAAa;QACnCA,QAAA,CAAS4F,aAAA,CAAc;MACzB,CAAC;MAED,KAAK,CAAAxI,KAAA,CAAOsE,MAAA,CAAO;QAAEC,KAAA,EAAO;QAAM3C,IAAA,EAAM;QAAWiG;MAAO,CAAC;IAC7D,CAAC;EACH;AACF;AAEO,SAASI,WAMdvG,IAAA,EACAlB,OAAA,EACA;EACA,OAAO;IACLuH,iBAAA,EAAmB;IACnBC,kBAAA,EAAoB;IACpB1G,WAAA,EAAa7B,QAAA,CAASe,OAAA,CAAQiH,WAAW,IAAI,aAAa;IAC1D,IAAI/F,IAAA,KAAS,UACV;MACC4D,KAAA,EAAO;MACP8C,MAAA,EAAQ;IACV;EACJ;AACF;AAEA,SAASvH,gBAMPL,OAAA,EAC2B;EAC3B,MAAMkB,IAAA,GACJ,OAAOlB,OAAA,CAAQiI,WAAA,KAAgB,aAC1BjI,OAAA,CAAQiI,WAAA,CAA2C,IACpDjI,OAAA,CAAQiI,WAAA;EAEd,MAAMC,OAAA,GAAUhH,IAAA,KAAS;EAEzB,MAAMiH,oBAAA,GAAuBD,OAAA,GACzB,OAAOlI,OAAA,CAAQmI,oBAAA,KAAyB,aACrCnI,OAAA,CAAQmI,oBAAA,CAAkD,IAC3DnI,OAAA,CAAQmI,oBAAA,GACV;EAEJ,OAAO;IACLjH,IAAA;IACAuB,eAAA,EAAiB;IACjBpB,aAAA,EAAe6G,OAAA,GAAWC,oBAAA,IAAwBT,IAAA,CAAKC,GAAA,CAAI,IAAK;IAChE7C,KAAA,EAAO;IACPpC,gBAAA,EAAkB;IAClBmF,cAAA,EAAgB;IAChBN,iBAAA,EAAmB;IACnBC,kBAAA,EAAoB;IACpBtB,SAAA,EAAW;IACXnD,aAAA,EAAe;IACf6E,MAAA,EAAQM,OAAA,GAAU,YAAY;IAC9BpH,WAAA,EAAa;EACf;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}