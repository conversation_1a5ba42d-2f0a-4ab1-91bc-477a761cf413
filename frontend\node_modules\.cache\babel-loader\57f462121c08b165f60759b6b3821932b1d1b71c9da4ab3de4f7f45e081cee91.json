{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\App.query.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport './styles/globals.css';\n\n// Configuração do React Query\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000 // 5 minutos\n    }\n  }\n});\n\n// Componente de teste simples\nconst TestPage = ({\n  title,\n  description\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"min-h-screen bg-bg-primary text-text-primary p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"text-accent-blue hover:text-primary-light\",\n        children: \"\\u2190 Voltar ao in\\xEDcio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-4\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-secondary mb-4\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-900 border border-green-500 rounded p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-100 text-sm\",\n          children: \"\\u2705 React Query Provider ativo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 19,\n  columnNumber: 3\n}, this);\n\n// Página inicial\n_c = TestPage;\nconst HomePage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"min-h-screen bg-bg-primary text-text-primary p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-4xl font-bold text-center mb-8\",\n      children: \"\\uD83C\\uDFAE SHACK Web Game - Teste React Query\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-semibold mb-6\",\n        children: \"React Query Adicionado!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"card bg-blue-900 border-blue-500 hover:bg-blue-800 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-blue-100 mb-2\",\n            children: \"\\uD83D\\uDD10 Teste Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-200 text-sm\",\n            children: \"P\\xE1gina de login com React Query\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/game\",\n          className: \"card bg-green-900 border-green-500 hover:bg-green-800 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-green-100 mb-2\",\n            children: \"\\uD83C\\uDFAE Teste Game\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-green-200 text-sm\",\n            children: \"P\\xE1gina do jogo com React Query\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-bg-tertiary border border-border-color rounded p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-text-muted text-sm space-y-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2705 React funcionando\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2705 React Router funcionando\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2705 Tailwind CSS funcionando\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2705 React Query Provider ativo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 45,\n  columnNumber: 3\n}, this);\n_c2 = HomePage;\nfunction App() {\n  console.log('App Query - Renderizando com React Query...');\n  return /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(TestPage, {\n            title: \"P\\xE1gina de Login\",\n            description: \"Esta p\\xE1gina est\\xE1 envolvida pelo QueryClientProvider do React Query.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/game\",\n          element: /*#__PURE__*/_jsxDEV(TestPage, {\n            title: \"P\\xE1gina do Jogo\",\n            description: \"Esta p\\xE1gina pode usar hooks do React Query como useQuery e useMutation.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(TestPage, {\n            title: \"P\\xE1gina 404\",\n            description: \"P\\xE1gina n\\xE3o encontrada, mas React Query ainda est\\xE1 ativo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"TestPage\");\n$RefreshReg$(_c2, \"HomePage\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "QueryClient", "QueryClientProvider", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "TestPage", "title", "description", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "HomePage", "_c2", "App", "console", "log", "client", "path", "element", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/App.query.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport './styles/globals.css';\n\n// Configuração do React Query\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5 minutos\n    },\n  },\n});\n\n// Componente de teste simples\nconst TestPage = ({ title, description }: { title: string; description: string }) => (\n  <div className=\"min-h-screen bg-bg-primary text-text-primary p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"mb-6\">\n        <Link to=\"/\" className=\"text-accent-blue hover:text-primary-light\">\n          ← Voltar ao início\n        </Link>\n      </div>\n      \n      <div className=\"card\">\n        <h1 className=\"text-3xl font-bold mb-4\">{title}</h1>\n        <p className=\"text-text-secondary mb-4\">\n          {description}\n        </p>\n        \n        <div className=\"bg-green-900 border border-green-500 rounded p-4\">\n          <p className=\"text-green-100 text-sm\">\n            ✅ React Query Provider ativo\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\n// Página inicial\nconst HomePage = () => (\n  <div className=\"min-h-screen bg-bg-primary text-text-primary p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <h1 className=\"text-4xl font-bold text-center mb-8\">\n        🎮 SHACK Web Game - Teste React Query\n      </h1>\n      \n      <div className=\"card text-center\">\n        <h2 className=\"text-2xl font-semibold mb-6\">\n          React Query Adicionado!\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n          <Link to=\"/login\" className=\"card bg-blue-900 border-blue-500 hover:bg-blue-800 transition-colors\">\n            <h3 className=\"text-lg font-semibold text-blue-100 mb-2\">\n              🔐 Teste Login\n            </h3>\n            <p className=\"text-blue-200 text-sm\">\n              Página de login com React Query\n            </p>\n          </Link>\n          \n          <Link to=\"/game\" className=\"card bg-green-900 border-green-500 hover:bg-green-800 transition-colors\">\n            <h3 className=\"text-lg font-semibold text-green-100 mb-2\">\n              🎮 Teste Game\n            </h3>\n            <p className=\"text-green-200 text-sm\">\n              Página do jogo com React Query\n            </p>\n          </Link>\n        </div>\n        \n        <div className=\"bg-bg-tertiary border border-border-color rounded p-4\">\n          <div className=\"text-text-muted text-sm space-y-1\">\n            <p>✅ React funcionando</p>\n            <p>✅ React Router funcionando</p>\n            <p>✅ Tailwind CSS funcionando</p>\n            <p>✅ React Query Provider ativo</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nfunction App() {\n  console.log('App Query - Renderizando com React Query...');\n  \n  return (\n    <QueryClientProvider client={queryClient}>\n      <Router>\n        <Routes>\n          <Route path=\"/\" element={<HomePage />} />\n          <Route \n            path=\"/login\" \n            element={\n              <TestPage \n                title=\"Página de Login\" \n                description=\"Esta página está envolvida pelo QueryClientProvider do React Query.\"\n              />\n            } \n          />\n          <Route \n            path=\"/game\" \n            element={\n              <TestPage \n                title=\"Página do Jogo\" \n                description=\"Esta página pode usar hooks do React Query como useQuery e useMutation.\"\n              />\n            } \n          />\n          <Route \n            path=\"*\" \n            element={\n              <TestPage \n                title=\"Página 404\" \n                description=\"Página não encontrada, mas React Query ainda está ativo.\"\n              />\n            } \n          />\n        </Routes>\n      </Router>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;AACxE,OAAO,sBAAsB;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIJ,WAAW,CAAC;EAClCK,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;IAC5B;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAoD,CAAC,kBAC9ET,OAAA;EAAKU,SAAS,EAAC,kDAAkD;EAAAC,QAAA,eAC/DX,OAAA;IAAKU,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCX,OAAA;MAAKU,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBX,OAAA,CAACJ,IAAI;QAACgB,EAAE,EAAC,GAAG;QAACF,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAEnE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENhB,OAAA;MAAKU,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBX,OAAA;QAAIU,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAEH;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpDhB,OAAA;QAAGU,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EACpCF;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAEJhB,OAAA;QAAKU,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/DX,OAAA;UAAGU,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;;AAED;AAAAC,EAAA,GAzBMV,QAAQ;AA0Bd,MAAMW,QAAQ,GAAGA,CAAA,kBACflB,OAAA;EAAKU,SAAS,EAAC,kDAAkD;EAAAC,QAAA,eAC/DX,OAAA;IAAKU,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCX,OAAA;MAAIU,SAAS,EAAC,qCAAqC;MAAAC,QAAA,EAAC;IAEpD;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELhB,OAAA;MAAKU,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BX,OAAA;QAAIU,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAE5C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELhB,OAAA;QAAKU,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDX,OAAA,CAACJ,IAAI;UAACgB,EAAE,EAAC,QAAQ;UAACF,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBAChGX,OAAA;YAAIU,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhB,OAAA;YAAGU,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEPhB,OAAA,CAACJ,IAAI;UAACgB,EAAE,EAAC,OAAO;UAACF,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBAClGX,OAAA;YAAIU,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAE1D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhB,OAAA;YAAGU,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAEtC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENhB,OAAA;QAAKU,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpEX,OAAA;UAAKU,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDX,OAAA;YAAAW,QAAA,EAAG;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1BhB,OAAA;YAAAW,QAAA,EAAG;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjChB,OAAA;YAAAW,QAAA,EAAG;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjChB,OAAA;YAAAW,QAAA,EAAG;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACG,GAAA,GA3CID,QAAQ;AA6Cd,SAASE,GAAGA,CAAA,EAAG;EACbC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;EAE1D,oBACEtB,OAAA,CAACF,mBAAmB;IAACyB,MAAM,EAAEtB,WAAY;IAAAU,QAAA,eACvCX,OAAA,CAACP,MAAM;MAAAkB,QAAA,eACLX,OAAA,CAACN,MAAM;QAAAiB,QAAA,gBACLX,OAAA,CAACL,KAAK;UAAC6B,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEzB,OAAA,CAACkB,QAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzChB,OAAA,CAACL,KAAK;UACJ6B,IAAI,EAAC,QAAQ;UACbC,OAAO,eACLzB,OAAA,CAACO,QAAQ;YACPC,KAAK,EAAC,oBAAiB;YACvBC,WAAW,EAAC;UAAqE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFhB,OAAA,CAACL,KAAK;UACJ6B,IAAI,EAAC,OAAO;UACZC,OAAO,eACLzB,OAAA,CAACO,QAAQ;YACPC,KAAK,EAAC,mBAAgB;YACtBC,WAAW,EAAC;UAAyE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFhB,OAAA,CAACL,KAAK;UACJ6B,IAAI,EAAC,GAAG;UACRC,OAAO,eACLzB,OAAA,CAACO,QAAQ;YACPC,KAAK,EAAC,eAAY;YAClBC,WAAW,EAAC;UAA0D;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAE1B;AAACU,GAAA,GAvCQN,GAAG;AAyCZ,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}