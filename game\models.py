import os
import random
import datetime
import uuid
import json
import re
import firebase_admin
from firebase_admin import credentials, firestore, auth
from werkzeug.security import generate_password_hash, check_password_hash 
from flask import session, request 
from google.cloud.firestore_v1.base_query import FieldFilter
# --- Configuração do Firebase ---
try:
    # O caminho padrão onde o Render coloca os Secret Files
    secret_path_on_render = '/etc/secrets/firebase-credentials.json'

    # Verifica se está rodando no Render
    if os.path.exists(secret_path_on_render):
        cred = credentials.Certificate(secret_path_on_render)
        # Inicializa o app
        firebase_admin.initialize_app(cred)
        print("✅ Firebase inicializado com credenciais do Render")
    else:
        # Se não, assume que está rodando no seu PC
        local_path = r"C:\Users\<USER>\OneDrive\Área de Trabalho\SHACK 1.0\firebase-credentials.json"
        if os.path.exists(local_path):
            cred = credentials.Certificate(local_path)
            # Inicializa o app
            firebase_admin.initialize_app(cred)
            print("✅ Firebase inicializado com credenciais locais")
        else:
            print("⚠️ Arquivo de credenciais do Firebase não encontrado")
            print("🔧 Executando em modo offline/desenvolvimento")

# A PARTE QUE FALTAVA ESTÁ AQUI:
except ValueError as e:
    # Este bloco evita que o app quebre se ele for reiniciado e tentar
    # inicializar o Firebase uma segunda vez.
    print("⚠️ Firebase já inicializado ou erro de inicialização")
except Exception as e:
    print(f"⚠️ Erro na inicialização do Firebase: {e}")
    print("🔧 Executando em modo offline/desenvolvimento")

# Função para obter cliente Firestore de forma segura
def get_firestore_client():
    """Obtém cliente Firestore de forma segura"""
    try:
        return firestore.client()
    except Exception as e:
        print(f"⚠️ Erro ao conectar com Firestore: {e}")
        return None

# Inicialização segura do cliente
try:
    db = get_firestore_client()
    if db:
        print("✅ Cliente Firestore inicializado")
    else:
        print("⚠️ Cliente Firestore não disponível - modo offline")
        db = None
except Exception as e:
    print(f"⚠️ Erro ao inicializar cliente Firestore: {e}")
    db = None
# ==========================================================
#    COLE O TRECHO DE CÓDIGO EXATAMENTE AQUI
# ==========================================================
MERCADO_NEGRO_ITEMS = {
    "upgrade_miner_1": {
        "nome": "Otimização de Algoritmo",
        "descricao": "Aumenta o nível da sua Mineradora de Shack em 1, acelerando a geração de recursos.",
        "preco_shack": 500, # Preço base em Shack (calculado dinamicamente)
        "tipo": "upgrade_mineradora"
    },
    "virus_v1": {
        "nome": "Vírus 'Corruptor' v1",
        "descricao": "Aumenta temporariamente o poder do seu Malware Kit para o próximo Deface.",
        "preco_shack": 1200,
        "tipo": "boost_deface"
    }
}

# Sistema de Habilidades NFT com estoque limitado
HABILIDADES_NFT = {
    "firewall_reverso": {
        "nome": "Firewall Reverso",
        "descricao": "Ignora 5% do firewall adversário em ataques.",
        "efeito": 0.05,  # 5% de redução do firewall adversário
        "preco_shack": 2000,
        "estoque_maximo": 5,
        "estoque_atual": 5,
        "tipo": "habilidade_nft"
    }
    # Outras habilidades podem ser adicionadas aqui pela ferramenta de admin
}
# --- Funções Auxiliares de Dados (Agora com Firestore) ---

def get_user_by_nick(nick):
    """Busca um usuário pelo seu nickname."""
    users_ref = db.collection('usuarios')
    # Corrigido: Voltar a usar .where() para filtragem
    query = users_ref.where('nick', '==', nick).limit(1).stream() # <--- CORREÇÃO AQUI
    for user in query:
        return user.to_dict()
    return None

# --- Funções de Usuário e Grupo (Agora com Firebase Auth e Firestore) ---

# Em game/models.py

# RENOMEIE a função e SIMPLIFIQUE a lógica
def criar_perfil_firestore(uid, nick, email):
    """
    Cria o documento do jogador no Firestore depois que o usuário do Firebase Auth
    já foi criado pelo frontend.
    """
    # 1. Verifica se o nick já está em uso
    if get_user_by_nick(nick):
        # Se o nick já existe, o ideal seria deletar o usuário Auth recém-criado
        # para evitar contas "fantasmas".
        auth.delete_user(uid)
        return {"sucesso": False, "mensagem": "Este nickname já está em uso. Tente outro."}

    # 2. Gera um IP único (lógica que já tínhamos)
    while True:
        novo_ip = '.'.join(str(random.randint(0, 255)) for _ in range(4))
        ip_existente = db.collection('usuarios').where('ip', '==', novo_ip).limit(1).get()
        if not ip_existente:
            break
            
    # 3. Prepara os dados do jogador para o Firestore
    user_data = {
        "uid": uid, "nick": nick, "email": email, "ip": novo_ip,
        "dinheiro": 1000, "shack": 100, "cpu": 1, "firewall": 1, "antivirus": 1,
        "malware_kit": 1, "nivel": 1, "xp": 0, "historico": [], "group": None,
        "deface_points_individual": 0, "tournament_points_individual": 0, "last_deface_timestamp": 0,
        "nivel_mineradora": 1,
        "ultimo_recurso_coletado_timestamp": datetime.datetime.now(datetime.timezone.utc).timestamp(),
        
        # Sistema de Habilidades NFT
        "habilidades_adquiridas": {},
        
        "log": [] 
    }
    
    # 4. Salva o documento no Firestore usando o UID fornecido pelo frontend
    db.collection('usuarios').document(uid).set(user_data)
    return {"sucesso": True, "mensagem": "Perfil do jogador criado com sucesso!"}

# --- Funções de Jogo (Lógica permanece, acesso a dados muda) ---

def calcular_xp_necessario(nivel):
    if nivel <= 1: return 500
    return int(500 + ((nivel - 1) ** 2) * 150)

def ganhar_xp(jogador, xp_ganho):
    jogador["xp"] = jogador.get("xp", 0) + int(xp_ganho)
    xp_necessario_atual = calcular_xp_necessario(jogador["nivel"])
    
    nivel_anterior = jogador["nivel"]
    
    while jogador["xp"] >= xp_necessario_atual:
        jogador["xp"] -= xp_necessario_atual
        jogador["nivel"] += 1
        jogador.setdefault("historico", []).append(f"🏆 Subiu para o Nível {jogador['nivel']}!")
        xp_necessario_atual = calcular_xp_necessario(jogador["nivel"])
    
    return jogador

def calcular_custo_upgrade(item_nome, nivel_atual):
    custos_base = {"cpu": 15, "firewall": 15, "antivirus": 15, "malware_kit": 15}
    custo_base = custos_base.get(item_nome, 15)
    return int(custo_base * (1.25 ** (nivel_atual - 1)))

def calcular_custo_upgrade_multiplo(item_nome, nivel_atual, quantidade_upgrades):
    """Calcula o custo total e custo em Shacks para comprar múltiplos upgrades."""
    custo_total_dinheiro = 0
    custo_total_shacks = 0
    
    for i in range(quantidade_upgrades):
        nivel_upgrade = nivel_atual + i
        custo_nivel = calcular_custo_upgrade(item_nome, nivel_upgrade)
        custo_total_dinheiro += custo_nivel
        
        # A partir do nível 10, precisa de Shacks adicionalmente
        if nivel_upgrade >= 10:
            # A cada 10 níveis: 2, 4, 6, 8... Shacks
            shacks_necessarios = ((nivel_upgrade // 10) * 2)
            custo_total_shacks += shacks_necessarios
    
    return {
        "custo_dinheiro": custo_total_dinheiro,
        "custo_shacks": custo_total_shacks
    }

def calcular_xp_upgrade(nivel_atual):
    return int(50 + (nivel_atual * 25))

# --- Funções de Ação (Adaptadas para Transações do Firestore) ---

# --- Função utilitária para cooldown de roubo por IP ---
def pode_roubar_ip(jogador_db, ip_alvo):
    cooldowns = jogador_db.get('cooldown_roubo_ips', {})
    agora = datetime.datetime.now(datetime.timezone.utc).timestamp()
    cooldown = cooldowns.get(ip_alvo, 0)
    return agora >= cooldown

def atualiza_cooldown_roubo(jogador_db, ip_alvo):
    cooldowns = jogador_db.get('cooldown_roubo_ips', {})
    agora = datetime.datetime.now(datetime.timezone.utc).timestamp()
    # 1 hora = 3600 segundos
    cooldowns[ip_alvo] = agora + 3600
    return cooldowns

@firestore.transactional
def roubar_dinheiro_alvo_transaction(transaction, user_uid, ip_alvo):
    jogador_ref = db.collection('usuarios').document(user_uid)
    vitima_ref = db.collection('vitimas').document(ip_alvo)

    snapshot_jogador = jogador_ref.get(transaction=transaction)
    snapshot_vitima = vitima_ref.get(transaction=transaction)

    if not snapshot_jogador.exists or not snapshot_vitima.exists:
        return {"sucesso": False, "mensagem": "Erro ao encontrar alvo ou jogador."}

    jogador_db = snapshot_jogador.to_dict()
    vitima_alvo = snapshot_vitima.to_dict()

    # --- COOLDOWN DE ROUBO POR IP ---
    if not pode_roubar_ip(jogador_db, ip_alvo):
        cooldowns = jogador_db.get('cooldown_roubo_ips', {})
        tempo_restante = int(cooldowns[ip_alvo] - datetime.datetime.now(datetime.timezone.utc).timestamp())
        minutos = max(1, tempo_restante // 60)
        return {"sucesso": False, "mensagem": f"Cooldown ativo para este IP. Tente novamente em {minutos} minutos."}

    chance_sucesso = jogador_db.get("cpu", 1) * 10
    chance_defesa = vitima_alvo.get("firewall", 1) * 8

    if random.randint(1, 100) < (chance_sucesso - chance_defesa):
        valor_roubado = int(vitima_alvo.get("dinheiro", 0) * (random.randint(10, 40) / 100))
        if valor_roubado <= 0:
            return {"sucesso": False, "mensagem": "A conta do alvo está vazia."}
        
        xp_total = (valor_roubado // 10) + (jogador_db.get("cpu", 1) * 2)
        jogador_db = ganhar_xp(jogador_db, xp_total) 

        agora = datetime.datetime.now().strftime('%d/%m/%Y %H:%M')
        jogador_db["historico"].append(f"💰 Roubou ${{valor_roubado}} de {ip_alvo} (+{{xp_total}} XP)")
        
        # Atualiza cooldown para este IP
        cooldowns = atualiza_cooldown_roubo(jogador_db, ip_alvo)
        transaction.update(jogador_ref, {
            'dinheiro': firestore.Increment(valor_roubado),
            'xp': jogador_db['xp'],
            'nivel': jogador_db['nivel'],
            'historico': jogador_db['historico'],
            'cooldown_roubo_ips': cooldowns
        })
        
        transaction.update(vitima_ref, {
            'dinheiro': firestore.Increment(-valor_roubado),
            'log': firestore.ArrayUnion([{"data": agora, "invasor": jogador_db["nick"], "valor": valor_roubado}])
        })
        return {"sucesso": True, "mensagem": f"Roubo bem-sucedido! Você adquiriu ${{valor_roubado}}."}
    else:
        return {"sucesso": False, "mensagem": "Roubo falhou! O firewall do alvo bloqueou sua tentativa."}

def roubar_dinheiro_alvo(user_uid, ip_alvo):
    transaction = db.transaction()
    return roubar_dinheiro_alvo_transaction(transaction, user_uid, ip_alvo)


@firestore.transactional
def realizar_deface_transaction(transaction, user_uid, ip_alvo):
    jogador_ref = db.collection('usuarios').document(user_uid)
    vitima_ref = db.collection('vitimas').document(ip_alvo)

    snapshot_jogador = jogador_ref.get(transaction=transaction)
    snapshot_vitima = vitima_ref.get(transaction=transaction)

    if not snapshot_jogador.exists or not snapshot_vitima.exists:
        return {"sucesso": False, "mensagem": "Alvo inválido."}

    jogador_db = snapshot_jogador.to_dict()
    vitima_alvo = snapshot_vitima.to_dict()

    id_grupo = jogador_db.get("group")
    if not id_grupo:
        return {"sucesso": False, "mensagem": "Você precisa estar em um grupo para realizar um Deface."}

    cooldown_period = 30 * 60
    now_timestamp = datetime.datetime.now().timestamp()
    last_deface_timestamp = jogador_db.get('last_deface_timestamp', 0)
    
    if now_timestamp - last_deface_timestamp < cooldown_period:
        tempo_restante = int((cooldown_period - (now_timestamp - last_deface_timestamp)) / 60)
        return {"sucesso": False, "mensagem": f"Você está em cooldown. Tente novamente em {tempo_restante+1} minutos."}

    poder_ataque_jogador = jogador_db.get("malware_kit", 1)
    poder_defesa_vitima = vitima_alvo.get("antivirus", 1)

    if poder_ataque_jogador > poder_defesa_vitima:
        pontos_ganhos = 15 + jogador_db.get("malware_kit", 1)
        xp_ganho = pontos_ganhos * 3
        
        jogador_db = ganhar_xp(jogador_db, xp_ganho)
        jogador_db['historico'].append(f"🛡️ Deface em {ip_alvo}! +{pontos_ganhos} pts. (+{xp_ganho} XP)")
        
        transaction.update(jogador_ref, {
            'deface_points_individual': firestore.Increment(pontos_ganhos),
            'last_deface_timestamp': now_timestamp,
            'xp': jogador_db['xp'],
            'nivel': jogador_db['nivel'],
            'historico': jogador_db['historico']
        })

        grupo_ref = db.collection('grupos').document(id_grupo)
        transaction.update(grupo_ref, {'deface_points': firestore.Increment(pontos_ganhos)})

        return {"sucesso": True, "mensagem": f"Deface bem-sucedido! Você e seu grupo ganharam {pontos_ganhos} pontos. Você também ganhou {xp_ganho} XP."}
    else:
        return {"sucesso": False, "mensagem": "Ataque de Deface falhou! O Antivirus do alvo era muito forte."}


def realizar_deface(user_uid, ip_alvo):
    transaction = db.transaction()
    return realizar_deface_transaction(transaction, user_uid, ip_alvo)


# --- Funções de Usuário e Grupo (Agora com Firebase Auth e Firestore) ---

def criar_usuario(nick, email, senha):
    """Cria um usuário no Firebase Auth e um documento correspondente no Firestore."""
    print(f"DEBUG criar_usuario: Tentando criar usuário {nick} com email {email}") # Novo DEBUG
    if get_user_by_nick(nick):
        print("DEBUG criar_usuario: Nickname já em uso.") # Novo DEBUG
        return {"sucesso": False, "mensagem": "Este nickname já está em uso."}

    try:
        user_record = auth.create_user(email=email, password=senha)
        uid = user_record.uid
        print(f"DEBUG criar_usuario: Usuário criado no Firebase Auth. UID: {uid}") # Novo DEBUG

        user_data = {
            "uid": uid, "nick": nick, "email": email,
            "dinheiro": 1000, "shack": 100, "cpu": 1, "firewall": 1,
            "antivirus": 1, "malware_kit": 1, "firewall_reverso": 0,
            "nivel_mineradora": 1, "historico": [], "xp": 0, "nivel": 1,
            "group": None, "deface_points_individual": 0,
            "last_deface_timestamp": 0
        }
        print(f"DEBUG criar_usuario: Tentando criar documento Firestore para UID: {uid}") # Novo DEBUG
        db.collection('usuarios').document(uid).set(user_data)
        print(f"DEBUG criar_usuario: Documento Firestore criado com sucesso para UID: {uid}") # Novo DEBUG
        return {"sucesso": True, "uid": uid}

    except firebase_admin.auth.EmailAlreadyExistsError:
        print("DEBUG criar_usuario: Email já cadastrado no Auth.") # Novo DEBUG
        return {"sucesso": False, "mensagem": "Este e-mail já está cadastrado."}
    except Exception as e:
        print(f"ERRO CRÍTICO em criar_usuario: {e}") # <--- AQUI ESTÁ A CHAVE!
        import traceback
        traceback.print_exc() # Imprime o stack trace completo no terminal Flask
        return {"sucesso": False, "mensagem": f"Ocorreu um erro: {e}"}

@firestore.transactional
def get_jogador_com_recursos_transaction(transaction, uid):
    """
    Calcula e atualiza TODOS os recursos passivos (Shack e Dinheiro)
    gerados pela Mineradora de uma só vez.
    """
    jogador_ref = db.collection('usuarios').document(uid)
    jogador_doc = jogador_ref.get(transaction=transaction)
    if not jogador_doc.exists:
        return None

    jogador = jogador_doc.to_dict()
    now_timestamp = datetime.datetime.now(datetime.timezone.utc).timestamp()
    
    # Pega o timestamp da última coleta (agora unificado)
    last_collected = jogador.get("ultimo_recurso_coletado_timestamp", now_timestamp)
    segundos_passados = now_timestamp - last_collected

    # Se passou pelo menos um segundo, calcula e atualiza os ganhos
    if segundos_passados >= 1:
        nivel_mineradora = jogador.get("nivel_mineradora", 1)

        # 1. Calcula o Shack gerado
        shack_gerado = int(segundos_passados * calcular_geracao_shack(nivel_mineradora))
        
        # 2. Calcula o Dinheiro ($) gerado
        dinheiro_gerado = int(segundos_passados * calcular_geracao_dinheiro(nivel_mineradora))

        # 3. Prepara a atualização para o banco de dados
        updates = {
            'shack': firestore.Increment(shack_gerado),
            'dinheiro': firestore.Increment(dinheiro_gerado),
            'ultimo_recurso_coletado_timestamp': now_timestamp # Atualiza o timestamp
        }
        
        # Atualiza o documento no banco de dados
        transaction.update(jogador_ref, updates)

        # Atualiza o objeto do jogador em memória para retornar os valores corretos
        jogador['shack'] += shack_gerado
        jogador['dinheiro'] += dinheiro_gerado

    return jogador

# Adicione esta nova função de cálculo de dinheiro ao lado da de Shack
def calcular_geracao_dinheiro(nivel_mineradora):
    """Define quanto dinheiro ($) é gerado por segundo pela mineradora."""
    # Aumentado: 30 dólares/min por nível = 0.5 dólares/segundo por nível
    # Nível 1: $1,800/hora, Nível 2: $3,600/hora, etc.
    return nivel_mineradora * 0.5

def get_jogador(uid):
    """Função principal que inicia a transação para pegar os dados do jogador."""
    if not uid:
        return None
    transaction = db.transaction()
    return get_jogador_com_recursos_transaction(transaction, uid)

def criar_grupo(user_uid, nome_grupo):
    jogador_ref = db.collection('usuarios').document(user_uid)
    jogador = jogador_ref.get().to_dict()

    if jogador.get("group"): return {"sucesso": False, "mensagem": "Você já está em um grupo."}
    custo_criacao = 10000
    if jogador["dinheiro"] < custo_criacao: return {"sucesso": False, "mensagem": f"Dinheiro insuficiente. Custa ${custo_criacao}."}
    
    id_grupo = str(uuid.uuid4())[:6].upper()
    
    grupo_data = {
        "id": id_grupo, "nome": nome_grupo, "lider": jogador["nick"], 
        "membros": [jogador["nick"]], "max_membros": 5, "deface_points": 0, "tournament_points": 0
    }
    
    batch = db.batch()
    batch.update(jogador_ref, {
        "dinheiro": firestore.Increment(-custo_criacao),
        "group": id_grupo
    })
    batch.set(db.collection('grupos').document(id_grupo), grupo_data)
    batch.commit()
    
    return {"sucesso": True, "mensagem": f"Grupo '{nome_grupo}' criado com sucesso!"}

# --- Funções de Compra de Upgrades (Implementando agora) ---

@firestore.transactional
def comprar_upgrade_transaction(transaction, user_uid, item_comprado, quantidade=1):
    jogador_ref = db.collection('usuarios').document(user_uid)
    snapshot_jogador = jogador_ref.get(transaction=transaction)

    if not snapshot_jogador.exists:
        return {"sucesso": False, "mensagem": "Jogador não encontrado."}
    
    jogador_db = snapshot_jogador.to_dict()

    nivel_atual_item = jogador_db.get(item_comprado, 1) # Pega o nível atual do item, padrão 1
    
    # Calcula custos para múltiplos upgrades
    custos = calcular_custo_upgrade_multiplo(item_comprado, nivel_atual_item, quantidade)
    custo_dinheiro = custos["custo_dinheiro"]
    custo_shacks = custos["custo_shacks"]
    
    # Verifica se tem dinheiro suficiente
    if jogador_db["dinheiro"] < custo_dinheiro:
        return {"sucesso": False, "mensagem": "Dinheiro insuficiente para o upgrade."}
    
    # Verifica se tem Shacks suficientes (se necessário)
    if custo_shacks > 0 and jogador_db.get("shack", 0) < custo_shacks:
        return {"sucesso": False, "mensagem": f"Shacks insuficientes. Precisa de {custo_shacks} Shacks."}

    # Atualiza o nível do item
    novo_nivel_item = nivel_atual_item + quantidade
    xp_ganho = sum([calcular_xp_upgrade(nivel_atual_item + i + 1) for i in range(quantidade)])

    jogador_db = ganhar_xp(jogador_db, xp_ganho) # Atualiza XP e nível do jogador

    # Prepara a atualização do documento do jogador
    timestamp_atual = datetime.datetime.now(datetime.timezone.utc).timestamp()
    
    if quantidade == 1:
        log_compra = {
            "mensagem": f"💰 Comprou upgrade de {item_comprado} nível {novo_nivel_item} por $${custo_dinheiro}" + (f" + {custo_shacks} Shacks" if custo_shacks > 0 else ""),
            "timestamp": timestamp_atual,
            "tipo": "saida_dinheiro",
            "acao": f"Upgrade {item_comprado}",
            "valor": -custo_dinheiro
        }
    else:
        log_compra = {
            "mensagem": f"💰 Comprou {quantidade}x upgrades de {item_comprado} (Nível {nivel_atual_item} → {novo_nivel_item}) por $${custo_dinheiro}" + (f" + {custo_shacks} Shacks" if custo_shacks > 0 else ""),
            "timestamp": timestamp_atual,
            "tipo": "saida_dinheiro",
            "acao": f"Upgrade {item_comprado} x{quantidade}",
            "valor": -custo_dinheiro
        }
    
    # Atualiza logs
    logs_atuais = jogador_db.get('log', [])
    logs_atuais.append(log_compra)
    if len(logs_atuais) > 100:
        logs_atuais = logs_atuais[-100:]

    updates = {
        item_comprado: novo_nivel_item,
        'dinheiro': jogador_db['dinheiro'] - custo_dinheiro,
        'xp': jogador_db['xp'],
        'nivel': jogador_db['nivel'],
        'historico': jogador_db['historico'],
        'log': logs_atuais
    }
    
    # Se teve custo em Shacks, desconta também
    if custo_shacks > 0:
        updates['shack'] = jogador_db.get('shack', 0) - custo_shacks
    
    transaction.update(jogador_ref, updates)
    
    if quantidade == 1:
        return {"sucesso": True, "mensagem": f"Upgrade de {item_comprado} para Nível {novo_nivel_item} bem-sucedido! (+{xp_ganho} XP)"}
    else:
        return {"sucesso": True, "mensagem": f"{quantidade}x upgrades de {item_comprado} bem-sucedidos! Nível {nivel_atual_item} → {novo_nivel_item} (+{xp_ganho} XP)"}

def comprar_upgrade(user_uid, item_comprado, quantidade=1):
    transaction = db.transaction()
    return comprar_upgrade_transaction(transaction, user_uid, item_comprado, quantidade)


# --- Funções de Grupo (Completo) ---

@firestore.transactional
def entrar_em_grupo_transaction(transaction, user_uid, group_id):
    jogador_ref = db.collection('usuarios').document(user_uid)
    grupo_ref = db.collection('grupos').document(group_id)

    snapshot_jogador = jogador_ref.get(transaction=transaction)
    snapshot_grupo = grupo_ref.get(transaction=transaction)

    if not snapshot_jogador.exists:
        return {"sucesso": False, "mensagem": "Jogador não encontrado."}
    if not snapshot_grupo.exists:
        return {"sucesso": False, "mensagem": "Grupo não encontrado."}

    jogador_db = snapshot_jogador.to_dict()
    grupo_db = snapshot_grupo.to_dict()

    if jogador_db.get("group"):
        return {"sucesso": False, "mensagem": "Você já está em um grupo."}
    if len(grupo_db.get("membros", [])) >= grupo_db.get("max_membros", 5):
        return {"sucesso": False, "mensagem": "Grupo cheio."}

    novo_membros = grupo_db.get("membros", []) + [jogador_db["nick"]]

    transaction.update(jogador_ref, {"group": group_id})
    transaction.update(grupo_ref, {"membros": novo_membros})

    return {"sucesso": True, "mensagem": f"Você entrou no grupo '{grupo_db['nome']}'!"}

def entrar_em_grupo(user_uid, group_id):
    transaction = db.transaction()
    return entrar_em_grupo_transaction(transaction, user_uid, group_id)


@firestore.transactional
def sair_do_grupo_transaction(transaction, user_uid):
    jogador_ref = db.collection('usuarios').document(user_uid)
    snapshot_jogador = jogador_ref.get(transaction=transaction)

    if not snapshot_jogador.exists:
        return {"sucesso": False, "mensagem": "Jogador não encontrado."}
    
    jogador_db = snapshot_jogador.to_dict()
    id_grupo = jogador_db.get("group")

    if not id_grupo:
        return {"sucesso": False, "mensagem": "Você não está em nenhum grupo."}

    grupo_ref = db.collection('grupos').document(id_grupo)
    snapshot_grupo = grupo_ref.get(transaction=transaction)

    if not snapshot_grupo.exists: # Grupo não existe mais (talvez foi excluído)
        transaction.update(jogador_ref, {"group": None}) # Limpa a referência no jogador
        return {"sucesso": True, "mensagem": "Você saiu do grupo (o grupo não existia mais)."}

    grupo_db = snapshot_grupo.to_dict()
    nick_jogador = jogador_db["nick"]
    
    # Remove o jogador da lista de membros
    novo_membros = [membro for membro in grupo_db.get("membros", []) if membro != nick_jogador]
    
    # Se não sobrar ninguém, deleta o grupo
    if not novo_membros:
        transaction.delete(grupo_ref)
        transaction.update(jogador_ref, {"group": None})
        return {"sucesso": True, "mensagem": "Você saiu do grupo e o grupo foi dissolvido (último membro)."}
    
    # Se o jogador era o líder, transfere a liderança
    if grupo_db["lider"] == nick_jogador:
        novo_lider = novo_membros[0]  # Primeiro membro da lista se torna o novo líder
        transaction.update(grupo_ref, {
            "lider": novo_lider, 
            "membros": novo_membros
        })
        transaction.update(jogador_ref, {"group": None})
        return {"sucesso": True, "mensagem": f"Você saiu do grupo e {novo_lider} se tornou o novo líder."}
    else:
        # Membro comum saindo
        transaction.update(grupo_ref, {"membros": novo_membros})
        transaction.update(jogador_ref, {"group": None})
        return {"sucesso": True, "mensagem": "Você saiu do grupo com sucesso!"}


def sair_do_grupo(user_uid):
    transaction = db.transaction()
    return sair_do_grupo_transaction(transaction, user_uid)


def get_dados_completos_grupo(user_uid):
    """
    Busca os dados do grupo ao qual o jogador pertence.
    Retorna os dados do grupo se encontrado, ou mensagem de erro.
    """
    jogador_ref = db.collection('usuarios').document(user_uid)
    jogador_doc = jogador_ref.get()

    if not jogador_doc.exists:
        return {"sucesso": False, "mensagem": "Jogador não encontrado."}
    
    jogador_data = jogador_doc.to_dict()
    group_id = jogador_data.get("group")

    if not group_id:
        return {"sucesso": False, "mensagem": "Você não está em nenhum grupo."}
    
    grupo_ref = db.collection('grupos').document(group_id)
    grupo_doc = grupo_ref.get()

    if not grupo_doc.exists:
        # Se o grupo não existe mais, limpa a referência no usuário (consistência)
        jogador_ref.update({"group": None})
        return {"sucesso": False, "mensagem": "Grupo não encontrado ou foi excluído."}
    
    grupo_data = grupo_doc.to_dict()

    # Opcional: buscar detalhes dos membros se necessário
    # membros_detalhes = []
    # for nick_membro in grupo_data.get("membros", []):
    #     membro_info = get_user_by_nick(nick_membro)
    #     if membro_info:
    #         membros_detalhes.append({"nick": membro_info["nick"], "nivel": membro_info["nivel"]})
    
    # grupo_data["membros_detalhes"] = membros_detalhes

    return {"sucesso": True, "grupo": grupo_data}


# Adicione estas funções no final do seu arquivo game/models.py

# --- FUNÇÕES DE GRUPO ---

@firestore.transactional
def criar_grupo_transaction(transaction, uid, nome_grupo):
    jogador_ref = db.collection('usuarios').document(uid)
    jogador_doc = jogador_ref.get(transaction=transaction)
    jogador = jogador_doc.to_dict()

    if jogador.get("group"):
        return {"sucesso": False, "mensagem": "Você já está em um grupo."}
    
    custo_criacao = 10000
    if jogador.get("dinheiro", 0) < custo_criacao:
        return {"sucesso": False, "mensagem": f"Dinheiro insuficiente. Custa ${custo_criacao} para criar um grupo."}

    # Gera um ID de grupo único e curto
    id_grupo = str(uuid.uuid4())[:6].upper()
    grupo_ref = db.collection('grupos').document(id_grupo)

    grupo_data = {
        "id": id_grupo,
        "nome": nome_grupo,
        "lider": jogador["nick"],
        "membros": [jogador["nick"]],
        "max_membros": 5,
        "deface_points": 0,
        "tournament_points": 0
    }
    
    transaction.set(grupo_ref, grupo_data)
    transaction.update(jogador_ref, {
        "dinheiro": firestore.Increment(-custo_criacao),
        "group": id_grupo,
        "grupo_nome": nome_grupo,
        "grupo_id": id_grupo
    })
    
    return {"sucesso": True, "mensagem": f"Grupo '{nome_grupo}' criado com sucesso!"}

def criar_grupo(uid, nome_grupo):
    transaction = db.transaction()
    return criar_grupo_transaction(transaction, uid, nome_grupo)

def corrigir_dados_grupo_usuarios():
    """
    Função para corrigir dados de grupo de usuários existentes que não têm grupo_nome
    """
    try:
        # Busca usuários que têm group mas não têm grupo_nome
        usuarios_query = db.collection('usuarios').where('group', '!=', None).stream()
        
        corrigidos = 0
        for usuario_doc in usuarios_query:
            usuario_data = usuario_doc.to_dict()
            
            # Se já tem grupo_nome, pula
            if usuario_data.get('grupo_nome'):
                continue
                
            group_id = usuario_data.get('group')
            if not group_id:
                continue
                
            # Busca o grupo correspondente
            grupo_doc = db.collection('grupos').document(group_id).get()
            if not grupo_doc.exists:
                continue
                
            grupo_data = grupo_doc.to_dict()
            nome_grupo = grupo_data.get('nome')
            
            if nome_grupo:
                # Atualiza o usuário com as informações do grupo
                db.collection('usuarios').document(usuario_doc.id).update({
                    'grupo_nome': nome_grupo,
                    'grupo_id': group_id
                })
                corrigidos += 1
                print(f"✅ Corrigido usuário {usuario_data.get('nick')} - grupo: {nome_grupo}")
        
        print(f"🎯 Correção concluída: {corrigidos} usuários corrigidos")
        return {"sucesso": True, "corrigidos": corrigidos}
        
    except Exception as e:
        print(f"❌ Erro na correção: {str(e)}")
        return {"sucesso": False, "erro": str(e)}


@firestore.transactional
def entrar_em_grupo_transaction(transaction, uid, id_grupo):
    jogador_ref = db.collection('usuarios').document(uid)
    jogador_doc = jogador_ref.get(transaction=transaction)
    jogador = jogador_doc.to_dict()

    if jogador.get("group"):
        return {"sucesso": False, "mensagem": "Você já está em um grupo."}
    
    grupo_ref = db.collection('grupos').document(id_grupo)
    grupo_doc = grupo_ref.get(transaction=transaction)

    if not grupo_doc.exists:
        return {"sucesso": False, "mensagem": "Grupo não encontrado."}
    
    grupo = grupo_doc.to_dict()
    if len(grupo.get("membros", [])) >= grupo.get("max_membros", 5):
        return {"sucesso": False, "mensagem": "Este grupo está cheio."}
        
    transaction.update(grupo_ref, {
        "membros": firestore.ArrayUnion([jogador["nick"]])
    })
    transaction.update(jogador_ref, {
        "group": id_grupo,
        "grupo_nome": grupo["nome"],
        "grupo_id": id_grupo
    })

    return {"sucesso": True, "mensagem": f"Você entrou no grupo '{grupo['nome']}'."}

def entrar_em_grupo(uid, id_grupo):
    transaction = db.transaction()
    return entrar_em_grupo_transaction(transaction, uid, id_grupo)


def get_dados_completos_grupo(uid):
    """Busca os dados do jogador e, se ele tiver um grupo, busca os dados do grupo."""
    jogador = get_jogador(uid)
    if not jogador:
        return {"sucesso": False, "mensagem": "Jogador não encontrado."}
    
    id_grupo = jogador.get("group")
    if not id_grupo:
        return {"sucesso": True, "jogador": jogador, "grupo": None} # Jogador não tem grupo
        
    grupo_doc = db.collection('grupos').document(id_grupo).get()
    if not grupo_doc.exists:
        # Grupo pode ter sido deletado, limpa a referência no jogador
        db.collection('usuarios').document(uid).update({"group": None})
        return {"sucesso": True, "jogador": jogador, "grupo": None}
        
    return {"sucesso": True, "jogador": jogador, "grupo": grupo_doc.to_dict()}


# --- FUNÇÃO DE DEFACE (Ação de Grupo) ---

@firestore.transactional
def realizar_deface_transaction(transaction, uid, ip_alvo):
    jogador_ref = db.collection('usuarios').document(uid)
    vitima_ref = db.collection('vitimas').document(ip_alvo)

    jogador_doc = jogador_ref.get(transaction=transaction)
    vitima_doc = vitima_ref.get(transaction=transaction)

    if not jogador_doc.exists or not vitima_doc.exists:
        return {"sucesso": False, "mensagem": "Alvo ou jogador inválido."}

    jogador = jogador_doc.to_dict()
    vitima = vitima_doc.to_dict()

    id_grupo = jogador.get("group")
    if not id_grupo:
        return {"sucesso": False, "mensagem": "Você precisa estar em um grupo para realizar um Deface."}

    # Lógica de Cooldown (ex: 30 minutos)
    cooldown_period = 30 * 60
    now_timestamp = datetime.datetime.now().timestamp()
    last_deface_timestamp = jogador.get('last_deface_timestamp', 0)
    
    if now_timestamp - last_deface_timestamp < cooldown_period:
        tempo_restante = int((cooldown_period - (now_timestamp - last_deface_timestamp)) / 60)
        return {"sucesso": False, "mensagem": f"Aguarde o cooldown. Tente novamente em {tempo_restante+1} min."}

    poder_ataque = jogador.get("malware_kit", 1)
    poder_defesa = vitima.get("antivirus", 1)

    if poder_ataque > poder_defesa:
        # SUCESSO NO DEFACE
        pontos_ganhos = 15 + jogador.get("malware_kit", 1)
        xp_ganho = pontos_ganhos * 3
        
        jogador = ganhar_xp(jogador, xp_ganho) # Reutiliza a função de ganhar XP
        jogador['historico'].append(f"🛡️ Deface em {ip_alvo}! +{pontos_ganhos} pts. (+{xp_ganho} XP)")
        
        # Atualiza jogador
        transaction.update(jogador_ref, {
            'deface_points_individual': firestore.Increment(pontos_ganhos),
            'last_deface_timestamp': now_timestamp,
            'xp': jogador['xp'],
            'nivel': jogador['nivel'],
            'historico': jogador['historico']
        })

        # Atualiza grupo
        grupo_ref = db.collection('grupos').document(id_grupo)
        transaction.update(grupo_ref, {'deface_points': firestore.Increment(pontos_ganhos)})

        return {"sucesso": True, "mensagem": f"Deface bem-sucedido! Você e seu grupo ganharam {pontos_ganhos} pontos."}
    else:
        # FALHA NO DEFACE
        return {"sucesso": False, "mensagem": "Deface falhou! O Antivirus do alvo era muito forte."}

def realizar_deface(atacante_uid, alvo_uid):
    transaction = db.transaction()
    return realizar_deface_transaction(transaction, atacante_uid, alvo_uid)

def calcular_geracao_shack(nivel_mineradora):
    """Define quantos Shacks são gerados por segundo, com base no nível."""
    # Drasticamente reduzido: 0.01 Shack/segundo por nível
    # Nível 1: 0.6 Shacks/min ou 36 Shacks/hora
    # Nível 10: 6 Shacks/min ou 360 Shacks/hora
    return nivel_mineradora * 0.01

@firestore.transactional
def comprar_item_mercado_negro_transaction(transaction, uid, item_id):
    if item_id not in MERCADO_NEGRO_ITEMS:
        return {"sucesso": False, "mensagem": "Item não encontrado no Mercado Negro."}

    item_info = MERCADO_NEGRO_ITEMS[item_id]
    
    jogador_ref = db.collection('usuarios').document(uid)
    jogador_doc = jogador_ref.get(transaction=transaction)
    
    if not jogador_doc.exists:
        return {"sucesso": False, "mensagem": "Jogador não encontrado."}

    jogador = jogador_doc.to_dict()
    
    # --- LÓGICA DE PREÇO DINÂMICO ---
    preco_item = 0
    if item_info["tipo"] == "upgrade_mineradora":
        nivel_atual = jogador.get("nivel_mineradora", 1)
        preco_item = calcular_custo_upgrade_mineradora(nivel_atual)
    else:
        # Para outros itens, continua usando o preço fixo
        preco_item = item_info.get("preco_shack", 0)
    # --- FIM DA LÓGICA DE PREÇO ---

    if jogador.get("shack", 0) < preco_item:
        return {"sucesso": False, "mensagem": "Shacks insuficientes."}

    updates = {'shack': firestore.Increment(-preco_item)}

    if item_info["tipo"] == "upgrade_mineradora":
        updates['nivel_mineradora'] = firestore.Increment(1)
    elif item_info["tipo"] == "boost_deface":
        caminho_inventario = f'inventario.{item_info["tipo"]}'
        updates[caminho_inventario] = firestore.Increment(1)
    
    transaction.update(jogador_ref, updates)
    return {"sucesso": True, "mensagem": f"Você comprou '{item_info['nome']}'!"}

def comprar_item_mercado_negro(uid, item_id):
    transaction = db.transaction()
    return comprar_item_mercado_negro_transaction(transaction, uid, item_id)

def enviar_mensagem_chat(uid, texto_mensagem):
    """Salva uma nova mensagem de chat no Firestore."""
    db = firestore.client()
    jogador = get_jogador(uid) # Reutiliza a função para pegar o nick
    
    if not texto_mensagem or not texto_mensagem.strip():
        return {"sucesso": False, "mensagem": "A mensagem não pode estar vazia."}

    if not jogador:
        return {"sucesso": False, "mensagem": "Usuário não encontrado."}
        
    mensagem_data = {
        "uid": uid,
        "autor": jogador.get("nick", "Anônimo"),  # Mudado de "nick" para "autor"
        "texto": texto_mensagem,
        "timestamp": firestore.SERVER_TIMESTAMP # Usa o carimbo de data/hora do servidor
    }
    
    db.collection("messages").add(mensagem_data)
    return {"sucesso": True}
def get_meu_log(uid):
    """Busca e retorna os últimos 50 registros de log de um jogador, ordenados por data."""
    import datetime
    
    db = firestore.client()
    jogador_doc = db.collection('usuarios').document(uid).get()

    if not jogador_doc.exists:
        return {"sucesso": False, "mensagem": "Jogador não encontrado."}

    # Pega o campo 'log', ou uma lista vazia se ele não existir
    logs = jogador_doc.to_dict().get('log', [])
    
    # Converte todos os timestamps para float antes de ordenar
    logs_processados = []
    for log in logs:
        log_copia = log.copy()
        timestamp = log_copia.get('timestamp')
        
        # Converte diferentes tipos de timestamp para float
        if hasattr(timestamp, 'timestamp'):  # DatetimeWithNanoseconds do Firestore
            log_copia['timestamp'] = timestamp.timestamp()
        elif isinstance(timestamp, datetime.datetime):  # datetime padrão
            log_copia['timestamp'] = timestamp.timestamp()
        elif not isinstance(timestamp, (int, float)):  # Caso não seja número
            log_copia['timestamp'] = 0  # Timestamp padrão para casos inválidos
        
        logs_processados.append(log_copia)
    
    # Ordena os logs pelo timestamp (agora todos são float), do mais recente para o mais antigo
    logs_recentes = sorted(logs_processados, key=lambda x: x.get('timestamp', 0), reverse=True)[:50]

    return {"sucesso": True, "logs": logs_recentes}

def limpar_meu_log(uid):
    """Limpa todos os logs do jogador."""
    if not uid:
        return {"sucesso": False, "mensagem": "UID não fornecido."}
    
    try:
        jogador_ref = db.collection('usuarios').document(uid)
        jogador_doc = jogador_ref.get()
        
        if not jogador_doc.exists:
            return {"sucesso": False, "mensagem": "Jogador não encontrado."}
        
        # Limpa o array de logs
        jogador_ref.update({"log": []})
        
        return {"sucesso": True, "mensagem": "Log limpo com sucesso!"}
    except Exception as e:
        return {"sucesso": False, "mensagem": f"Erro ao limpar log: {str(e)}"}

# PREÇO DA MINERADORA A CADA UPGRADE
def calcular_custo_upgrade_mineradora(nivel_atual):
    """Calcula o custo em Shacks para o próximo upgrade da mineradora."""
    # Fórmula: CustoBase * (Multiplicador ^ (NívelAtual - 1))
    # Alinhado com o frontend: base 500, multiplicador 1.5
    custo_base = 500  # O custo para ir do nível 1 para o 2
    multiplicador = 1.5 # A cada nível, o preço aumenta em 50%
    
    return int(custo_base * (multiplicador ** (nivel_atual - 1)))

def realizar_exploit(atacante_uid, alvo_ip):
    """
    Realiza o exploit de um alvo. Retorna sucesso/falha baseado nas habilidades dos jogadores.
    """
    # Busca o atacante
    atacante_doc = db.collection('usuarios').document(atacante_uid).get()
    if not atacante_doc.exists:
        return {"sucesso": False, "mensagem": "Atacante não encontrado."}
    
    atacante = atacante_doc.to_dict()
    
    # Busca o alvo pelo IP
    alvos_query = db.collection('usuarios').where('ip', '==', alvo_ip).limit(1)
    alvos_docs = list(alvos_query.stream())
    
    if not alvos_docs:
        return {"sucesso": False, "mensagem": "Alvo não encontrado."}
    
    alvo_doc = alvos_docs[0]
    alvo = alvo_doc.to_dict()
    alvo['uid'] = alvo_doc.id  # Adiciona o UID do documento
    
    # Não pode atacar a si mesmo
    if atacante_uid == alvo_doc.id:
        return {"sucesso": False, "mensagem": "Você não pode atacar a si mesmo."}
    
    # NOVA LÓGICA: CPU vs FIREWALL (CPU deve ser maior que FIREWALL)
    poder_ataque = atacante.get('cpu', 1)
    defesa_alvo = alvo.get('firewall', 1)
    
    sucesso = poder_ataque > defesa_alvo
    
    timestamp_atual = datetime.datetime.now(datetime.timezone.utc).timestamp()
    
    if sucesso:
        # Log para o ALVO (invasão recebida)
        log_invasao_alvo = {
            "mensagem": f"🚨 Sistema invadido por {atacante['nick']} (IP: {atacante['ip']})",
            "timestamp": timestamp_atual,
            "tipo": "invasao_recebida"
        }
        
        # Log para o ATACANTE (invasão realizada)
        log_invasao_atacante = {
            "mensagem": f"🚀 Exploitou com sucesso {alvo['nick']} (IP: {alvo['ip']})",
            "timestamp": timestamp_atual,
            "tipo": "invasao_realizada"
        }
        
        # Atualiza o log do alvo
        alvo_ref = db.collection('usuarios').document(alvo_doc.id)
        logs_atuais_alvo = alvo.get('log', [])
        logs_atuais_alvo.append(log_invasao_alvo)
        
        # Mantém apenas os últimos 100 logs
        if len(logs_atuais_alvo) > 100:
            logs_atuais_alvo = logs_atuais_alvo[-100:]
        
        alvo_ref.update({'log': logs_atuais_alvo})
        
        # Atualiza o log do atacante
        atacante_ref = db.collection('usuarios').document(atacante_uid)
        logs_atuais_atacante = atacante.get('log', [])
        logs_atuais_atacante.append(log_invasao_atacante)
        
        # Mantém apenas os últimos 100 logs
        if len(logs_atuais_atacante) > 100:
            logs_atuais_atacante = logs_atuais_atacante[-100:]
        
        # Adiciona XP para o atacante e atualiza log
        atacante_atualizado = ganhar_xp(atacante, 25)  # 25 XP por exploit bem-sucedido
        atacante_ref.update({
            'xp': atacante_atualizado['xp'],
            'nivel': atacante_atualizado['nivel'],
            'log': logs_atuais_atacante
        })
        
        return {
            "sucesso": True, 
            "mensagem": f"Exploit bem-sucedido! Você invadiu o sistema de {alvo['nick']}.",
            "alvo_explorado": {
                "uid": alvo_doc.id,
                "nick": alvo['nick'],
                "ip": alvo['ip'],
                "nivel": alvo.get('nivel', 1),
                "dinheiro": alvo.get('dinheiro', 0)
            }
        }
    else:
        # Log para o atacante (falha na invasão)
        log_falha_atacante = {
            "mensagem": f"❌ Falha ao exploitar {alvo['nick']} (IP: {alvo['ip']}) - Firewall muito forte",
            "timestamp": timestamp_atual,
            "tipo": "invasao_falhada"
        }
        
        # Atualiza o log do atacante
        atacante_ref = db.collection('usuarios').document(atacante_uid)
        logs_atuais_atacante = atacante.get('log', [])
        logs_atuais_atacante.append(log_falha_atacante)
        
        if len(logs_atuais_atacante) > 100:
            logs_atuais_atacante = logs_atuais_atacante[-100:]
        
        atacante_ref.update({'log': logs_atuais_atacante})
        
        return {
            "sucesso": False, 
            "mensagem": f"Exploit falhou! O firewall de {alvo['nick']} (Nível {defesa_alvo}) é mais forte que seu CPU (Nível {poder_ataque})."
        }

@firestore.transactional
def transferir_dinheiro_alvo_transaction(transaction, atacante_uid, alvo_uid, porcentagem):
    try:
        atacante_ref = db.collection('usuarios').document(atacante_uid)
        alvo_ref = db.collection('usuarios').document(alvo_uid)
        
        atacante_doc = atacante_ref.get(transaction=transaction)
        alvo_doc = alvo_ref.get(transaction=transaction)
        
        if not atacante_doc.exists or not alvo_doc.exists:
            return {"sucesso": False, "mensagem": "Jogador não encontrado."}
        
        atacante = atacante_doc.to_dict()
        alvo = alvo_doc.to_dict()
        
        # Verifica cooldown de roubo por IP (1 hora)
        alvo_ip = alvo.get('ip')
        if alvo_ip:
            cooldowns_roubo = atacante.get('cooldown_roubo_ips', {})
            timestamp_atual = datetime.datetime.now(datetime.timezone.utc).timestamp()
            cooldown_ip = cooldowns_roubo.get(alvo_ip, 0)
            
            if timestamp_atual < cooldown_ip:
                tempo_restante = int((cooldown_ip - timestamp_atual) // 60)
                return {"sucesso": False, "mensagem": f"Cooldown ativo para este IP. Aguarde {tempo_restante + 1} minutos."}
        
        saldo_alvo = alvo.get('dinheiro', 0)
        quantia_transferir = int(saldo_alvo * (porcentagem / 100))
        
        if quantia_transferir <= 0:
            return {"sucesso": False, "mensagem": "Não há dinheiro suficiente para transferir."}
        
        # Calcula novos saldos
        novo_saldo_atacante = atacante.get('dinheiro', 0) + quantia_transferir
        novo_saldo_alvo = saldo_alvo - quantia_transferir
        
        timestamp_atual = datetime.datetime.now(datetime.timezone.utc).timestamp()
        
        # Log para o ATACANTE (entrada de dinheiro)
        log_entrada_atacante = {
            "mensagem": f"💰 Recebeu ${quantia_transferir} de {alvo['nick']} ({porcentagem}%)",
            "timestamp": timestamp_atual,
            "tipo": "entrada_dinheiro",
            "valor": quantia_transferir
        }
        
        # Log para o ALVO (saída de dinheiro)
        log_saida_alvo = {
            "mensagem": f"💸 ${quantia_transferir} transferidos para {atacante['nick']} ({porcentagem}%)",
            "timestamp": timestamp_atual,
            "tipo": "saida_dinheiro",
            "valor": -quantia_transferir
        }
        
        # Atualiza logs
        logs_atacante = atacante.get('log', [])
        logs_atacante.append(log_entrada_atacante)
        if len(logs_atacante) > 100:
            logs_atacante = logs_atacante[-100:]
        
        logs_alvo = alvo.get('log', [])
        logs_alvo.append(log_saida_alvo)
        if len(logs_alvo) > 100:
            logs_alvo = logs_alvo[-100:]
        
        # Atualiza cooldown de roubo para este IP (1 hora)
        cooldowns_roubo = atacante.get('cooldown_roubo_ips', {})
        if alvo_ip:
            cooldowns_roubo[alvo_ip] = timestamp_atual + 3600  # 1 hora = 3600 segundos
        
        # Atualiza os documentos
        transaction.update(atacante_ref, {
            'dinheiro': novo_saldo_atacante,
            'log': logs_atacante,
            'cooldown_roubo_ips': cooldowns_roubo
        })
        
        transaction.update(alvo_ref, {
            'dinheiro': novo_saldo_alvo,
            'log': logs_alvo
        })
        
        return {
            "sucesso": True,
            "mensagem": f"Transferência concluída! Você roubou ${quantia_transferir} ({porcentagem}%)",
            "saldo_restante_alvo": novo_saldo_alvo,
            "quantia_transferida": quantia_transferir
        }
        
    except Exception as e:
        print(f"ERRO na transferência transaction: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro interno na transferência: {str(e)}"}

def transferir_dinheiro_alvo(atacante_uid, alvo_uid, porcentagem):
    transaction = db.transaction()
    return transferir_dinheiro_alvo_transaction(transaction, atacante_uid, alvo_uid, porcentagem)

@firestore.transactional
def realizar_deface_transaction(transaction, atacante_uid, alvo_uid):
    atacante_ref = db.collection('usuarios').document(atacante_uid)
    alvo_ref = db.collection('usuarios').document(alvo_uid)
    
    atacante_doc = atacante_ref.get(transaction=transaction)
    alvo_doc = alvo_ref.get(transaction=transaction)
    
    if not atacante_doc.exists or not alvo_doc.exists:
        return {"sucesso": False, "mensagem": "Jogador não encontrado."}
    
    atacante = atacante_doc.to_dict()
    alvo = alvo_doc.to_dict()
    
    # Verifica se o atacante tem um grupo
    grupo_id = atacante.get('group')
    if not grupo_id:
        return {"sucesso": False, "mensagem": "Você precisa estar em um grupo para realizar deface."}
    
    timestamp_atual = datetime.datetime.now(datetime.timezone.utc).timestamp()
    
    # Verifica cooldown de deface (30 minutos)
    last_deface_timestamp = atacante.get('last_deface_timestamp', 0)
    cooldown_deface = 30 * 60  # 30 minutos em segundos
    
    if timestamp_atual - last_deface_timestamp < cooldown_deface:
        tempo_restante = cooldown_deface - (timestamp_atual - last_deface_timestamp)
        minutos_restantes = int(tempo_restante // 60)
        segundos_restantes = int(tempo_restante % 60)
        return {"sucesso": False, "mensagem": f"Cooldown ativo! Aguarde {minutos_restantes}m {segundos_restantes}s para fazer outro deface."}
    
    # Log para o ALVO (deface recebido) - DEVE APARECER NO TOPO
    log_deface_alvo = {
        "mensagem": f"🔥 DESFIGURADO por {atacante['nick']} do grupo {grupo_id}",
        "timestamp": timestamp_atual,
        "tipo": "deface_recebido",
        "prioridade": True  # Marca como alta prioridade para aparecer no topo
    }
    
    # Log para o ATACANTE (deface realizado)
    log_deface_atacante = {
        "mensagem": f"🔥 Desfigurou {alvo['nick']} (IP: {alvo['ip']})",
        "timestamp": timestamp_atual,
        "tipo": "deface_realizado"
    }
    
    # Atualiza logs do alvo (deface no topo)
    logs_alvo = alvo.get('log', [])
    # Insere no início da lista para aparecer no topo
    logs_alvo.insert(0, log_deface_alvo)
    if len(logs_alvo) > 100:
        logs_alvo = logs_alvo[:100]  # Mantém os 100 primeiros (mais recentes)
    
    # Atualiza logs do atacante
    logs_atacante = atacante.get('log', [])
    logs_atacante.append(log_deface_atacante)
    if len(logs_atacante) > 100:
        logs_atacante = logs_atacante[-100:]
    
    # Atualiza pontos de deface do atacante
    pontos_individuais = atacante.get('deface_points_individual', 0) + 30
    pontos_torneio_individuais = atacante.get('tournament_points_individual', 0) + 30
    
    # Atualiza os documentos
    transaction.update(atacante_ref, {
        'deface_points_individual': pontos_individuais,
        'tournament_points_individual': pontos_torneio_individuais,
        'last_deface_timestamp': timestamp_atual,
        'log': logs_atacante
    })
    
    transaction.update(alvo_ref, {
        'log': logs_alvo
    })
    
    # Atualiza pontos do grupo (normal e torneio)
    grupo_ref = db.collection('grupos').document(grupo_id)
    grupo_doc = grupo_ref.get(transaction=transaction)
    
    if grupo_doc.exists:
        grupo_data = grupo_doc.to_dict()
        novos_pontos_grupo = grupo_data.get('deface_points', 0) + 30
        novos_pontos_torneio_grupo = grupo_data.get('tournament_points', 0) + 30
        transaction.update(grupo_ref, {
            'deface_points': novos_pontos_grupo,
            'tournament_points': novos_pontos_torneio_grupo
        })
    
    return {
        "sucesso": True,
        "mensagem": f"Deface realizado em {alvo['nick']}! +30 pontos para você e seu grupo (incluindo torneio)."
    }

def realizar_deface(atacante_uid, alvo_uid):
    """
    Realiza deface em um alvo. Requer que o atacante esteja em um grupo.
    """
    try:
        # Busca o atacante
        atacante_doc = db.collection('usuarios').document(atacante_uid).get()
        if not atacante_doc.exists:
            return {"sucesso": False, "mensagem": "Atacante não encontrado."}
        
        atacante = atacante_doc.to_dict()
        
        # Verifica se o atacante tem um grupo
        grupo_id = atacante.get('group')
        if not grupo_id:
            return {"sucesso": False, "mensagem": "Você precisa estar em um grupo para realizar deface."}
        
        # Busca o alvo
        alvo_doc = db.collection('usuarios').document(alvo_uid).get()
        if not alvo_doc.exists:
            return {"sucesso": False, "mensagem": "Alvo não encontrado."}
        
        alvo = alvo_doc.to_dict()
        
        # Não pode fazer deface em si mesmo
        if atacante_uid == alvo_uid:
            return {"sucesso": False, "mensagem": "Você não pode fazer deface em si mesmo."}
        
        # Define timestamp_atual ANTES de usar
        timestamp_atual = datetime.datetime.now(datetime.timezone.utc).timestamp()
        
        # Verifica cooldown de deface (30 minutos)
        last_deface_timestamp = atacante.get('last_deface_timestamp', 0)
        cooldown_deface = 30 * 60  # 30 minutos em segundos
        
        if timestamp_atual - last_deface_timestamp < cooldown_deface:
            tempo_restante = cooldown_deface - (timestamp_atual - last_deface_timestamp)
            minutos_restantes = int(tempo_restante // 60)
            segundos_restantes = int(tempo_restante % 60)
            return {"sucesso": False, "mensagem": f"Cooldown ativo! Aguarde {minutos_restantes}m {segundos_restantes}s para fazer outro deface."}
        
        # Prepara logs
        log_deface_alvo = {
            "mensagem": f"🔥 SISTEMA DESFIGURADO por {atacante['nick']} do grupo {grupo_id}",
            "timestamp": timestamp_atual,
            "tipo": "deface_recebido"
        }
        
        log_deface_atacante = {
            "mensagem": f"🔥 Desfigurou o sistema de {alvo['nick']} (IP: {alvo['ip']})",
            "timestamp": timestamp_atual,
            "tipo": "deface_realizado"
        }
        
        # Usa batch para garantir atomicidade
        batch = db.batch()
        
        # Atualiza alvo
        logs_alvo = alvo.get('log', [])
        logs_alvo.insert(0, log_deface_alvo)  # Insere no início
        if len(logs_alvo) > 100:
            logs_alvo = logs_alvo[:100]
            
        batch.update(db.collection('usuarios').document(alvo_uid), {
            'log': logs_alvo
        })
        
        # Atualiza atacante
        logs_atacante = atacante.get('log', [])
        logs_atacante.append(log_deface_atacante)
        if len(logs_atacante) > 100:
            logs_atacante = logs_atacante[-100:]
            
        pontos_individuais = atacante.get('deface_points_individual', 0) + 30
        pontos_torneio_individuais = atacante.get('tournament_points_individual', 0) + 30
        
        batch.update(db.collection('usuarios').document(atacante_uid), {
            'deface_points_individual': pontos_individuais,
            'tournament_points_individual': pontos_torneio_individuais,
            'last_deface_timestamp': timestamp_atual,
            'log': logs_atacante
        })
        
        # Atualiza grupo (pontos normais e do torneio)
        grupo_doc = db.collection('grupos').document(grupo_id).get()
        if grupo_doc.exists:
            grupo_data = grupo_doc.to_dict()
            novos_pontos_grupo = grupo_data.get('deface_points', 0) + 30
            novos_pontos_torneio_grupo = grupo_data.get('tournament_points', 0) + 30
            batch.update(db.collection('grupos').document(grupo_id), {
                'deface_points': novos_pontos_grupo,
                'tournament_points': novos_pontos_torneio_grupo
            })
        
        # Executa todas as operações
        batch.commit()
        
        return {
            "sucesso": True,
            "mensagem": f"Deface realizado com sucesso em {alvo['nick']}! +30 pontos para você e seu grupo (incluindo torneio)."
        }
        
    except Exception as e:
        print(f"Erro no deface: {str(e)}")  # Para debug
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

# Aqui o app banco - CORRIGIDO
def get_extrato_bancario(uid):
    """Filtra o log de um jogador para retornar apenas transações financeiras."""
    jogador = get_jogador(uid)
    if not jogador:
        return []

    logs_financeiros = []
    for log in jogador.get('log', []):
        # Verifica se é uma transação financeira - incluindo mais tipos
        tipos_financeiros = [
            'entrada_dinheiro', 'saida_dinheiro', 
            'receita_roubo', 'despesa_roubo',
            'compra', 'venda', 'transferencia',
            'exploit_sucesso', 'roubo'
        ]
        
        if log.get('tipo') in tipos_financeiros or 'dinheiro' in log.get('acao', '').lower():
            log_copia = log.copy()
            timestamp = log_copia.get('timestamp')
            
            # Converte diferentes tipos de timestamp para float
            if hasattr(timestamp, 'timestamp'):  # DatetimeWithNanoseconds do Firestore
                log_copia['timestamp'] = timestamp.timestamp()
            elif isinstance(timestamp, datetime.datetime):  # datetime padrão
                log_copia['timestamp'] = timestamp.timestamp()
            elif not isinstance(timestamp, (int, float)):  # Caso não seja número
                log_copia['timestamp'] = 0  # Timestamp padrão para casos inválidos
            
            # Extrai valor da mensagem se não existir no campo valor
            if 'valor' not in log_copia:
                mensagem = log_copia.get('mensagem', '')
                acao = log_copia.get('acao', '')
                # Tenta extrair valor da mensagem usando regex
                valor_match = re.search(r'\$(\d+(?:,\d+)*)', mensagem + ' ' + acao)
                if valor_match:
                    valor_str = valor_match.group(1).replace(',', '')
                    log_copia['valor'] = int(valor_str)
                    # Determina se é entrada ou saída baseado na mensagem
                    if any(word in (mensagem + ' ' + acao).lower() for word in ['ganhou', 'recebeu', '+', 'entrada']):
                        log_copia['valor'] = abs(log_copia['valor'])
                    elif any(word in (mensagem + ' ' + acao).lower() for word in ['perdeu', 'gastou', '-', 'saida', 'comprou']):
                        log_copia['valor'] = -abs(log_copia['valor'])
                else:
                    log_copia['valor'] = 0
            
            logs_financeiros.append(log_copia)
    
    # Ordena do mais recente para o mais antigo
    return sorted(logs_financeiros, key=lambda x: x.get('timestamp', 0), reverse=True)

def encontrar_alvos_para_jogador(atacante_uid):
    """
    Encontra exatamente 5 jogadores aleatórios como alvos para scan.
    A cada chamada, retorna 5 alvos diferentes sorteados aleatoriamente.
    """
    try:
        # Busca o jogador atacante para excluí-lo dos resultados
        atacante_doc = db.collection('usuarios').document(atacante_uid).get()
        if not atacante_doc.exists:
            return []
        
        # Busca todos os jogadores (exceto o atacante)
        # Usa um limite maior para ter mais opções para o sorteio
        jogadores_query = db.collection('usuarios').limit(50).stream()
        
        todos_jogadores = []
        for doc in jogadores_query:
            # Não incluir o próprio atacante
            if doc.id == atacante_uid:
                continue
                
            jogador_data = doc.to_dict()
            todos_jogadores.append({
                "uid": doc.id,
                "nick": jogador_data.get('nick', 'Desconhecido'),
                "ip": jogador_data.get('ip', '0.0.0.0'),
                "nivel": jogador_data.get('nivel', 1),
                "dinheiro": jogador_data.get('dinheiro', 0),
                "firewall": jogador_data.get('firewall', 1),
                "antivirus": jogador_data.get('antivirus', 1)
            })
        
        # Se não tem jogadores suficientes, retorna todos os disponíveis
        if len(todos_jogadores) <= 5:
            return todos_jogadores
        
        # Sorteia exatamente 5 alvos aleatórios
        alvos_sorteados = random.sample(todos_jogadores, 5)
        
        return alvos_sorteados
        
    except Exception as e:
        print(f"Erro ao encontrar alvos: {str(e)}")
        return []

# --- SISTEMA DE MINERAÇÃO OFFLINE PARA TODOS OS JOGADORES ---

def processar_mineracao_offline_todos_jogadores():
    """
    Processa a mineração de todos os jogadores, mesmo os que estão offline.
    Esta função deve ser chamada periodicamente (ex: a cada hora).
    """
    try:
        print("🔄 Iniciando processamento de mineração offline para todos os jogadores...")
        
        # Busca todos os jogadores
        usuarios_ref = db.collection('usuarios')
        usuarios_docs = usuarios_ref.stream()
        
        processados = 0
        erros = 0
        
        for usuario_doc in usuarios_docs:
            try:
                uid = usuario_doc.id
                # Usa transação para garantir consistência
                transaction = db.transaction()
                jogador = get_jogador_com_recursos_transaction(transaction, uid)
                
                if jogador:
                    processados += 1
                    print(f"✅ Mineração processada para {jogador.get('nick', uid)}")
                    
            except Exception as e:
                erros += 1
                print(f"❌ Erro ao processar jogador {uid}: {str(e)}")
                continue
        
        print(f"🎯 Processamento concluído: {processados} jogadores processados, {erros} erros")
        return {
            "sucesso": True,
            "processados": processados,
            "erros": erros,
            "mensagem": f"Mineração offline processada para {processados} jogadores"
        }
        
    except Exception as e:
        print(f"❌ Erro crítico no processamento offline: {str(e)}")
        return {
            "sucesso": False,
            "mensagem": f"Erro crítico: {str(e)}"
        }

def processar_mineracao_jogador_especifico(uid):
    """
    Processa a mineração de um jogador específico.
    Útil para testes ou processamento manual.
    """
    try:
        transaction = db.transaction()
        jogador = get_jogador_com_recursos_transaction(transaction, uid)
        
        if jogador:
            return {
                "sucesso": True,
                "jogador": jogador,
                "mensagem": f"Mineração processada para {jogador.get('nick', uid)}"
            }
        else:
            return {
                "sucesso": False,
                "mensagem": "Jogador não encontrado"
            }
            
    except Exception as e:
        print(f"❌ Erro ao processar jogador {uid}: {str(e)}")
        return {
            "sucesso": False,
            "mensagem": f"Erro: {str(e)}"
        }


# === SISTEMA DE NOTÍCIAS ===

def obter_noticias():
    """
    Obter todas as notícias ordenadas por data (mais recentes primeiro)
    """
    try:
        noticias_ref = db.collection('noticias').order_by('created_at', direction=firestore.Query.DESCENDING)
        noticias_docs = noticias_ref.stream()
        
        noticias = []
        for doc in noticias_docs:
            noticia_data = doc.to_dict()
            noticia_data['id'] = doc.id
            noticias.append(noticia_data)
        
        return noticias
        
    except Exception as e:
        print(f"❌ Erro ao obter notícias: {str(e)}")
        return []


def criar_noticia(admin_uid, title, content, priority=False):
    """
    Criar nova notícia (apenas administradores)
    """
    try:
        # Verificar se o usuário é admin
        admin_user = get_jogador(admin_uid)
        if not admin_user or not admin_user.get('is_admin', False):
            raise Exception("Apenas administradores podem criar notícias")
        
        noticia_data = {
            'title': title,
            'content': content,
            'priority': priority,
            'author_uid': admin_uid,
            'author_name': admin_user.get('nick', 'Admin'),
            'created_at': datetime.datetime.now(),
            'updated_at': datetime.datetime.now()
        }
        
        # Adicionar notícia ao Firestore
        doc_ref = db.collection('noticias').add(noticia_data)
        noticia_id = doc_ref[1].id
        
        print(f"✅ Notícia criada: {title} (ID: {noticia_id})")
        return noticia_id
        
    except Exception as e:
        print(f"❌ Erro ao criar notícia: {str(e)}")
        raise


def excluir_noticia(noticia_id):
    """
    Excluir notícia
    """
    try:
        noticia_ref = db.collection('noticias').document(noticia_id)
        noticia_doc = noticia_ref.get()
        
        if not noticia_doc.exists:
            return False
        
        noticia_ref.delete()
        print(f"✅ Notícia excluída: {noticia_id}")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao excluir notícia: {str(e)}")
        return False


def marcar_usuario_como_admin(uid):
    """
    Marcar usuário como administrador (função utilitária)
    """
    try:
        usuario_ref = db.collection('usuarios').document(uid)
        usuario_ref.update({'is_admin': True})
        print(f"✅ Usuário {uid} marcado como administrador")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao marcar usuário como admin: {str(e)}")
        return False

# === SISTEMA DE TORNEIO DIÁRIO DE DEFACE ===

def get_tournament_status():
    """
    Obter status do torneio atual (data início, fim, etc.)
    """
    try:
        now = datetime.datetime.now(datetime.timezone.utc)
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        tomorrow_start = today_start + datetime.timedelta(days=1)
        
        time_left = tomorrow_start - now
        
        return {
            "sucesso": True,
            "torneio_ativo": True,
            "inicio": today_start.isoformat(),
            "fim": tomorrow_start.isoformat(),
            "tempo_restante_segundos": int(time_left.total_seconds()),
            "tempo_restante_formatado": {
                "horas": int(time_left.total_seconds() // 3600),
                "minutos": int((time_left.total_seconds() % 3600) // 60),
                "segundos": int(time_left.total_seconds() % 60)
            }
        }
    except Exception as e:
        print(f"❌ Erro ao obter status do torneio: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

def get_tournament_ranking():
    """
    Obter ranking do torneio diário atual
    """
    try:
        # Buscar grupos ordenados pelos pontos do torneio
        grupos_query = db.collection('grupos').order_by('tournament_points', direction=firestore.Query.DESCENDING).limit(50)
        grupos_docs = grupos_query.stream()
        
        ranking_grupos = []
        for grupo_doc in grupos_docs:
            grupo_data = grupo_doc.to_dict()
            ranking_grupos.append({
                "id": grupo_data.get("id", ""),
                "nome": grupo_data.get("nome", ""),
                "tournament_points": grupo_data.get("tournament_points", 0),
                "deface_points": grupo_data.get("deface_points", 0),
                "membros": grupo_data.get("membros", []),
                "lider": grupo_data.get("lider", "")
            })
        
        # Buscar jogadores individuais por pontos do torneio
        usuarios_query = db.collection('usuarios').order_by('tournament_points_individual', direction=firestore.Query.DESCENDING).limit(50)
        usuarios_docs = usuarios_query.stream()
        
        ranking_jogadores = []
        for usuario_doc in usuarios_docs:
            usuario_data = usuario_doc.to_dict()
            ranking_jogadores.append({
                "uid": usuario_doc.id,
                "nick": usuario_data.get("nick", ""),
                "tournament_points": usuario_data.get("tournament_points_individual", 0),
                "deface_points": usuario_data.get("deface_points_individual", 0),
                "grupo": usuario_data.get("group", None),
                "grupo_nome": usuario_data.get("grupo_nome", None),
                "grupo_id": usuario_data.get("grupo_id", None),
                "nivel": usuario_data.get("nivel", 1)
            })
        
        return {
            "sucesso": True,
            "ranking_grupos": ranking_grupos,
            "ranking_jogadores": ranking_jogadores,
            "status": get_tournament_status()
        }
        
    except Exception as e:
        print(f"❌ Erro ao obter ranking do torneio: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

@firestore.transactional
def reset_daily_tournament_transaction(transaction):
    """
    Resetar o torneio diário com transação
    """
    try:
        # Buscar top 3 grupos para dar recompensas
        grupos_query = db.collection('grupos').order_by('tournament_points', direction=firestore.Query.DESCENDING).limit(3)
        grupos_docs = list(grupos_query.stream())
        
        recompensas = [
            {"dinheiro": 50000, "shacks": 500},  # 1º lugar
            {"dinheiro": 30000, "shacks": 300},  # 2º lugar  
            {"dinheiro": 20000, "shacks": 200}   # 3º lugar
        ]
        
        grupos_premiados = []
        
        # Dar recompensas aos grupos vencedores
        for i, grupo_doc in enumerate(grupos_docs):
            if i >= 3:
                break
                
            grupo_data = grupo_doc.to_dict()
            recompensa = recompensas[i]
            
            # Dar recompensa para todos os membros do grupo
            for membro_nick in grupo_data.get("membros", []):
                # Buscar o membro pelo nick
                membro_query = db.collection('usuarios').where('nick', '==', membro_nick).limit(1)
                membro_docs = list(membro_query.stream())
                
                if membro_docs:
                    membro_doc = membro_docs[0]
                    membro_ref = db.collection('usuarios').document(membro_doc.id)
                    
                    # Atualizar com recompensa
                    transaction.update(membro_ref, {
                        'dinheiro': firestore.Increment(recompensa["dinheiro"]),
                        'shack': firestore.Increment(recompensa["shacks"])
                    })
            
            grupos_premiados.append({
                "posicao": i + 1,
                "grupo": grupo_data.get("nome", ""),
                "pontos": grupo_data.get("tournament_points", 0),
                "recompensa": recompensa
            })
        
        # Resetar pontos do torneio de todos os grupos
        todos_grupos = db.collection('grupos').stream()
        for grupo_doc in todos_grupos:
            grupo_ref = db.collection('grupos').document(grupo_doc.id)
            transaction.update(grupo_ref, {
                'tournament_points': 0
            })
        
        # Resetar pontos do torneio de todos os usuários
        todos_usuarios = db.collection('usuarios').stream()
        for usuario_doc in todos_usuarios:
            usuario_ref = db.collection('usuarios').document(usuario_doc.id)
            transaction.update(usuario_ref, {
                'tournament_points_individual': 0
            })
        
        return {
            "sucesso": True,
            "mensagem": "Torneio resetado com sucesso!",
            "grupos_premiados": grupos_premiados,
            "reset_timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat()
        }
        
    except Exception as e:
        print(f"❌ Erro ao resetar torneio: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

def reset_daily_tournament():
    """
    Resetar o torneio diário e dar recompensas
    """
    transaction = db.transaction()
    return reset_daily_tournament_transaction(transaction)

def should_reset_tournament():
    """
    Verificar se o torneio deve ser resetado (chamada automática)
    """
    try:
        # Verificar se já passou da meia-noite
        now = datetime.datetime.now(datetime.timezone.utc)
        
        # Buscar último reset do sistema
        sistema_ref = db.collection('sistema').document('torneio')
        sistema_doc = sistema_ref.get()
        
        if sistema_doc.exists:
            ultimo_reset = sistema_doc.to_dict().get('ultimo_reset')
            if ultimo_reset:
                ultimo_reset_dt = datetime.datetime.fromisoformat(ultimo_reset.replace('Z', '+00:00'))
                
                # Se já passou um dia desde o último reset
                if (now - ultimo_reset_dt).days >= 1:
                    return True
        else:
            # Primeira vez, deve resetar
            return True
            
        return False
        
    except Exception as e:
        print(f"❌ Erro ao verificar reset do torneio: {str(e)}")
        return False

def auto_reset_tournament_if_needed():
    """
    Função para ser chamada periodicamente para resetar o torneio automaticamente
    """
    try:
        if should_reset_tournament():
            resultado = reset_daily_tournament()
            
            # Atualizar timestamp do último reset
            sistema_ref = db.collection('sistema').document('torneio')
            sistema_ref.set({
                'ultimo_reset': datetime.datetime.now(datetime.timezone.utc).isoformat(),
                'ultimo_resultado': resultado
            })
            
            print(f"🏆 Torneio resetado automaticamente: {resultado}")
            return resultado
        
        return {"sucesso": True, "mensagem": "Reset não necessário"}
        
    except Exception as e:
        print(f"❌ Erro no reset automático do torneio: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

# === SISTEMA DE HABILIDADES NFT ===

@firestore.transactional
def comprar_habilidade_nft_transaction(transaction, uid, habilidade_id):
    """
    Permite a um jogador comprar uma habilidade NFT, desde que ainda tenha estoque disponível.
    """
    # Verifica se a habilidade existe
    if habilidade_id not in HABILIDADES_NFT:
        return {"sucesso": False, "mensagem": "Habilidade não encontrada."}
    
    habilidade_info = HABILIDADES_NFT[habilidade_id]
    
    # Verifica estoque
    if habilidade_info["estoque_atual"] <= 0:
        return {"sucesso": False, "mensagem": "Esta habilidade está esgotada!"}
    
    # Busca o jogador
    jogador_ref = db.collection('usuarios').document(uid)
    jogador_doc = jogador_ref.get(transaction=transaction)
    
    if not jogador_doc.exists:
        return {"sucesso": False, "mensagem": "Jogador não encontrado."}
    
    jogador = jogador_doc.to_dict()
    
    # Verifica se o jogador já possui a habilidade
    habilidades_adquiridas = jogador.get("habilidades_adquiridas", {})
    if habilidade_id in habilidades_adquiridas:
        return {"sucesso": False, "mensagem": "Você já possui esta habilidade!"}
    
    # Verifica se tem Shacks suficientes
    preco_shack = habilidade_info["preco_shack"]
    if jogador.get("shack", 0) < preco_shack:
        return {"sucesso": False, "mensagem": f"Shacks insuficientes. Precisa de {preco_shack} Shacks."}
    
    # Prepara a transação
    timestamp_atual = datetime.datetime.now(datetime.timezone.utc).timestamp()
    
    # Registra a compra no log do jogador
    log_compra = {
        "mensagem": f"🔮 Adquiriu habilidade exclusiva: {habilidade_info['nome']}",
        "timestamp": timestamp_atual,
        "tipo": "compra_habilidade_nft",
        "habilidade": habilidade_id,
        "preco": preco_shack
    }
    
    logs_atuais = jogador.get('log', [])
    logs_atuais.append(log_compra)
    if len(logs_atuais) > 100:
        logs_atuais = logs_atuais[-100:]
    
    # Adiciona a habilidade ao jogador
    habilidades_adquiridas[habilidade_id] = {
        "id": habilidade_id,
        "nome": habilidade_info["nome"],
        "descricao": habilidade_info["descricao"],
        "efeito": habilidade_info["efeito"],
        "adquirido_em": timestamp_atual
    }
    
    # Atualiza o jogador
    transaction.update(jogador_ref, {
        "habilidades_adquiridas": habilidades_adquiridas,
        "shack": firestore.Increment(-preco_shack),
        "log": logs_atuais,
        "historico": firestore.ArrayUnion([f"🔮 Adquiriu habilidade exclusiva: {habilidade_info['nome']}"])
    })
    
    # Atualiza o estoque global
    HABILIDADES_NFT[habilidade_id]["estoque_atual"] -= 1
    
    # Salva o estoque atualizado no Firestore para persistência
    habilidades_config_ref = db.collection('config').document('habilidades_nft')
    transaction.set(habilidades_config_ref, {"habilidades": HABILIDADES_NFT}, merge=True)
    
    return {
        "sucesso": True,
        "mensagem": f"Você adquiriu a habilidade {habilidade_info['nome']}!",
        "estoque_restante": HABILIDADES_NFT[habilidade_id]["estoque_atual"]
    }

def comprar_habilidade_nft(uid, habilidade_id):
    """Função principal para comprar uma habilidade NFT"""
    transaction = db.transaction()
    return comprar_habilidade_nft_transaction(transaction, uid, habilidade_id)

def get_habilidades_nft_disponiveis():
    """Retorna todas as habilidades NFT disponíveis com seu estoque atual"""
    return {
        "sucesso": True, 
        "habilidades": HABILIDADES_NFT
    }

def aplicar_efeito_habilidade(jogador, habilidade_id, contexto):
    """
    Aplica o efeito de uma habilidade NFT em um contexto específico.
    
    :param jogador: Dicionário com os dados do jogador
    :param habilidade_id: ID da habilidade a ser aplicada
    :param contexto: Dicionário com informações contextuais (ex: {"adversario_firewall": 10})
    :return: Dicionário com os efeitos aplicados
    """
    # Verifica se o jogador possui a habilidade
    habilidades_adquiridas = jogador.get("habilidades_adquiridas", {})
    if habilidade_id not in habilidades_adquiridas:
        return contexto  # Retorna o contexto sem alterações
    
    habilidade = habilidades_adquiridas[habilidade_id]
    
    # Aplica o efeito com base no ID da habilidade
    if habilidade_id == "firewall_reverso" and "adversario_firewall" in contexto:
        # Reduz o firewall adversário em 5%
        reducao = contexto["adversario_firewall"] * habilidade["efeito"]
        contexto["adversario_firewall"] -= reducao
        contexto["efeitos_aplicados"] = contexto.get("efeitos_aplicados", []) + [
            f"Firewall Reverso: -{int(reducao * 100)}% de eficácia do Firewall"
        ]
    
    # Outros tipos de habilidades podem ser implementados aqui
    
    return contexto

# === FERRAMENTAS DE ADMINISTRAÇÃO DE HABILIDADES ===

def carregar_habilidades_nft_do_firestore():
    """
    Carrega as definições de habilidades NFT do Firestore para a variável global HABILIDADES_NFT.
    Deve ser chamada na inicialização da aplicação.
    """
    global HABILIDADES_NFT
    
    try:
        habilidades_config_ref = db.collection('config').document('habilidades_nft')
        habilidades_doc = habilidades_config_ref.get()
        
        if habilidades_doc.exists:
            habilidades_data = habilidades_doc.to_dict()
            if "habilidades" in habilidades_data:
                HABILIDADES_NFT = habilidades_data["habilidades"]
                print(f"✅ {len(HABILIDADES_NFT)} habilidades NFT carregadas do Firestore")
                return {"sucesso": True, "quantidade": len(HABILIDADES_NFT)}
        
        # Se não existir, inicializa com os valores padrão
        habilidades_config_ref.set({"habilidades": HABILIDADES_NFT})
        print("✅ Configuração inicial de habilidades NFT criada no Firestore")
        return {"sucesso": True, "quantidade": len(HABILIDADES_NFT), "mensagem": "Configuração inicial criada"}
        
    except Exception as e:
        print(f"❌ Erro ao carregar habilidades NFT: {str(e)}")
        return {"sucesso": False, "erro": str(e)}

def admin_criar_habilidade_nft(nome, descricao, efeito, preco_shack, estoque_maximo, habilidade_id=None):
    """
    Ferramenta de administração para criar uma nova habilidade NFT.
    """
    global HABILIDADES_NFT
    
    try:
        # Se não for fornecido um ID, gera um baseado no nome
        if not habilidade_id:
            habilidade_id = nome.lower().replace(' ', '_').replace('-', '_')
            
        # Verifica se já existe uma habilidade com este ID
        if habilidade_id in HABILIDADES_NFT:
            return {"sucesso": False, "mensagem": f"Já existe uma habilidade com o ID '{habilidade_id}'"}
        
        # Cria a nova habilidade
        nova_habilidade = {
            "nome": nome,
            "descricao": descricao,
            "efeito": float(efeito),
            "preco_shack": int(preco_shack),
            "estoque_maximo": int(estoque_maximo),
            "estoque_atual": int(estoque_maximo),
            "tipo": "habilidade_nft"
        }
        
        # Adiciona à estrutura em memória
        HABILIDADES_NFT[habilidade_id] = nova_habilidade
        
        # Salva no Firestore
        habilidades_config_ref = db.collection('config').document('habilidades_nft')
        habilidades_config_ref.set({"habilidades": HABILIDADES_NFT}, merge=True)
        
        print(f"✅ Habilidade NFT '{nome}' criada com sucesso (ID: {habilidade_id})")
        return {
            "sucesso": True,
            "mensagem": f"Habilidade '{nome}' criada com sucesso!",
            "habilidade_id": habilidade_id
        }
        
    except Exception as e:
        print(f"❌ Erro ao criar habilidade NFT: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro ao criar habilidade: {str(e)}"}

def admin_atualizar_habilidade_nft(habilidade_id, campos_atualizacao):
    """
    Ferramenta de administração para atualizar uma habilidade NFT existente.
    
    :param habilidade_id: ID da habilidade a ser atualizada
    :param campos_atualizacao: Dicionário com os campos a serem atualizados
    :return: Resultado da operação
    """
    global HABILIDADES_NFT
    
    try:
        # Verifica se a habilidade existe
        if habilidade_id not in HABILIDADES_NFT:
            return {"sucesso": False, "mensagem": f"Habilidade com ID '{habilidade_id}' não encontrada"}
        
        # Atualiza os campos
        for campo, valor in campos_atualizacao.items():
            # Impede a atualização de campos que não devem ser modificados
            if campo in ["tipo"]:
                continue
            
            # Converte tipos de dados para garantir consistência
            if campo == "efeito":
                valor = float(valor)
            elif campo in ["preco_shack", "estoque_maximo", "estoque_atual"]:
                valor = int(valor)
                
            # Atualiza o campo
            HABILIDADES_NFT[habilidade_id][campo] = valor
        
        # Salva no Firestore
        habilidades_config_ref = db.collection('config').document('habilidades_nft')
        habilidades_config_ref.set({"habilidades": HABILIDADES_NFT}, merge=True)
        
        print(f"✅ Habilidade NFT '{habilidade_id}' atualizada com sucesso")
        return {
            "sucesso": True,
            "mensagem": f"Habilidade '{HABILIDADES_NFT[habilidade_id]['nome']}' atualizada com sucesso!",
            "habilidade": HABILIDADES_NFT[habilidade_id]
        }
        
    except Exception as e:
        print(f"❌ Erro ao atualizar habilidade NFT: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro ao atualizar habilidade: {str(e)}"}

def admin_excluir_habilidade_nft(habilidade_id):
    """
    Ferramenta de administração para excluir uma habilidade NFT.
    Atenção: Isso não remove a habilidade dos jogadores que já a adquiriram.
    """
    global HABILIDADES_NFT
    
    try:
        # Verifica se a habilidade existe
        if habilidade_id not in HABILIDADES_NFT:
            return {"sucesso": False, "mensagem": f"Habilidade com ID '{habilidade_id}' não encontrada"}
            
        # Guarda o nome para mensagem de retorno
        nome_habilidade = HABILIDADES_NFT[habilidade_id]["nome"]
        
        # Remove a habilidade
        del HABILIDADES_NFT[habilidade_id]
        
        # Salva no Firestore
        habilidades_config_ref = db.collection('config').document('habilidades_nft')
        habilidades_config_ref.set({"habilidades": HABILIDADES_NFT}, merge=True)
        
        print(f"✅ Habilidade NFT '{nome_habilidade}' (ID: {habilidade_id}) excluída com sucesso")
        return {
            "sucesso": True,
            "mensagem": f"Habilidade '{nome_habilidade}' excluída com sucesso!"
        }
        
    except Exception as e:
        print(f"❌ Erro ao excluir habilidade NFT: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro ao excluir habilidade: {str(e)}"}

def migrar_jogadores_para_novo_sistema_habilidades():
    """
    Migra todos os jogadores do sistema antigo de habilidades para o novo sistema NFT.
    Remove os campos antigos e inicializa o novo campo habilidades_adquiridas.
    """
    try:
        print("🔄 Migrando jogadores para o novo sistema de habilidades NFT...")
        
        usuarios_ref = db.collection('usuarios')
        usuarios_docs = usuarios_ref.stream()
        
        atualizados = 0
        erros = 0
        
        for usuario_doc in usuarios_docs:
            try:
                uid = usuario_doc.id
                jogador = usuario_doc.to_dict()
                nick = jogador.get('nick', uid)
                
                # Verifica se já tem o novo sistema
                if 'habilidades_adquiridas' in jogador:
                    continue  # Já está no novo sistema
                
                # Prepara o update
                updates = {
                    # Adiciona o novo campo
                    "habilidades_adquiridas": {},
                    
                    # Remove os campos antigos
                    "pontos_habilidade": firestore.DELETE_FIELD,
                    "habilidade_forca_bruta": firestore.DELETE_FIELD,
                    "habilidade_stealth": firestore.DELETE_FIELD,
                    "habilidade_exploracao": firestore.DELETE_FIELD,
                    "habilidade_criptografia": firestore.DELETE_FIELD,
                    "habilidade_mineracao": firestore.DELETE_FIELD,
                    "habilidade_social": firestore.DELETE_FIELD
                }
                
                # Atualiza o jogador
                usuario_doc.reference.update(updates)
                atualizados += 1
                print(f"✅ Jogador {nick} migrado para o novo sistema de habilidades")
                
            except Exception as e:
                erros += 1
                print(f"❌ Erro ao migrar jogador {uid}: {str(e)}")
                continue
        
        print(f"🎯 Migração concluída: {atualizados} jogadores atualizados, {erros} erros")
        return {
            "sucesso": True,
            "atualizados": atualizados,
            "erros": erros,
            "mensagem": f"Sistema de habilidades NFT aplicado para {atualizados} jogadores"
        }
        
    except Exception as e:
        print(f"❌ Erro crítico na migração: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro crítico: {str(e)}"}

# Carrega as habilidades NFT do Firestore na inicialização
# carregar_habilidades_nft_do_firestore()  # COMENTADO PARA EVITAR TRAVAMENTO NA INICIALIZAÇÃO
print("🔧 Models inicializado - carregamento de habilidades NFT movido para app.py")
