{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleGamePage.tsx\",\n  _s = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route } from 'react-router-dom';\n\n// Componente do Dashboard Simplificado\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleDashboard = () => {\n  _s();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    loadPlayerData,\n    hasPlayerData\n  } = usePlayer();\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold cyber-text\",\n            children: \"Terminal Principal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-text-muted\",\n            children: [\"Operador: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-cyber-primary\",\n              children: (user === null || user === void 0 ? void 0 : user.nick) || 'Jogador'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), isLoadingPlayer && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 cyber-text\",\n          children: \"\\uD83D\\uDCCA Status do Sistema\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-blue-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-blue-400\",\n                children: isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-blue-300\",\n                children: \"PONTOS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-green-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-green-400\",\n                children: isLoadingPlayer ? '...' : playerData.nivel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-green-300\",\n                children: \"N\\xCDVEL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-purple-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-purple-400\",\n                children: isLoadingPlayer ? '...' : playerData.conquistas\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-purple-300\",\n                children: \"CONQUISTAS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-orange-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-orange-400\",\n                children: isLoadingPlayer ? '...' : playerData.ranking\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-orange-300\",\n                children: \"RANKING\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 cyber-text\",\n          children: \"\\u26A1 Acesso R\\xE1pido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.href = '/game/scanner',\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.href = '/game/chat',\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Loja\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Config\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 cyber-text\",\n          children: \"\\uD83D\\uDCC8 Log do Sistema\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-sm\",\n                  children: \"+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Pontos ganhos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-text-muted\",\n                  children: \"02:15:33\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-green-400 font-bold text-sm\",\n              children: \"+150\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-sm\",\n                  children: \"\\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Nova conquista\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-text-muted\",\n                  children: \"01:22:15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-400 font-bold text-sm\",\n              children: \"HACK\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-sm\",\n                  children: \"\\u2191\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Level UP\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-text-muted\",\n                  children: \"00:45:22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-purple-400 font-bold text-sm\",\n              children: \"LV.15\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n\n// Componente Scanner Completo\n_s(SimpleDashboard, \"4ZBWbfZBu+lAD/wW1WhGuHvBS6I=\", true);\n_c = SimpleDashboard;\nconst SimpleScanner = () => {\n  _s3();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer\n  } = usePlayer();\n  const [isScanning, setIsScanning] = React.useState(false);\n  const [scanTargets, setScanTargets] = React.useState([]);\n  const [scanError, setScanError] = React.useState(null);\n  const [specificIP, setSpecificIP] = React.useState('');\n  const [isAdvancedScan, setIsAdvancedScan] = React.useState(false);\n  const [connectionStatus, setConnectionStatus] = React.useState('Não testado');\n  const [loginStatus, setLoginStatus] = React.useState('Não logado');\n  const handleQuickScan = async () => {\n    setIsScanning(true);\n    setScanError(null);\n    try {\n      console.log('🔍 Iniciando scan rápido...');\n      const data = await gameApi.scanTargets();\n      if (data.sucesso && data.alvos) {\n        setScanTargets(data.alvos);\n        console.log('✅ Scan realizado com sucesso:', data.alvos);\n      } else {\n        setScanError(data.mensagem || 'Erro ao escanear alvos');\n        console.log('❌ Erro no scan:', data.mensagem);\n      }\n    } catch (error) {\n      console.error('❌ Erro no scan:', error);\n      setScanError(error.message || 'Erro de conexão com o servidor');\n    } finally {\n      setIsScanning(false);\n    }\n  };\n  const handleAdvancedScan = async () => {\n    if (!specificIP.trim()) {\n      setScanError('Digite um IP válido');\n      return;\n    }\n    setIsAdvancedScan(true);\n    setScanError(null);\n    try {\n      console.log(`🎯 Iniciando scan avançado para IP: ${specificIP}`);\n      const data = await gameApi.scanSpecificIP(specificIP);\n      if (data.sucesso && data.alvo) {\n        setScanTargets([data.alvo]);\n        console.log('✅ Scan avançado realizado:', data.alvo);\n      } else {\n        setScanError(data.mensagem || 'IP não encontrado');\n        console.log('❌ IP não encontrado:', data.mensagem);\n      }\n    } catch (error) {\n      console.error('❌ Erro no scan avançado:', error);\n      setScanError(error.message || 'Erro de conexão com o servidor');\n    } finally {\n      setIsAdvancedScan(false);\n    }\n  };\n  const handleExploit = async target => {\n    try {\n      console.log(`⚔️ Iniciando exploit contra: ${target.nick} (${target.ip})`);\n      const data = await gameApi.exploitTarget(target.ip);\n      if (data.sucesso) {\n        console.log('✅ Exploit realizado com sucesso:', data);\n\n        // Mostrar opções de transferência se o exploit foi bem-sucedido\n        if (data.conexao_criada) {\n          var _target$dinheiro;\n          const transferAmount = prompt(`Exploit bem-sucedido em ${target.nick}!\\n\\n` + `Dinheiro disponível: $${((_target$dinheiro = target.dinheiro) === null || _target$dinheiro === void 0 ? void 0 : _target$dinheiro.toLocaleString()) || '0'}\\n\\n` + `Digite a porcentagem para transferir (20-80%):`);\n          if (transferAmount) {\n            const percentage = parseInt(transferAmount);\n            if (percentage >= 20 && percentage <= 80) {\n              try {\n                const transferResponse = await gameApi.forceTransfer(target.uid || target.ip, percentage);\n                if (transferResponse.sucesso) {\n                  alert(`Transferência realizada! ${transferResponse.mensagem}`);\n                } else {\n                  alert(`Erro na transferência: ${transferResponse.mensagem}`);\n                }\n              } catch (error) {\n                alert('Erro na transferência');\n              }\n            } else {\n              alert('Porcentagem deve estar entre 20% e 80%');\n            }\n          }\n        } else {\n          alert(`✅ Exploit bem-sucedido! ${data.mensagem}`);\n        }\n\n        // Recarregar lista de alvos\n        handleQuickScan();\n      } else {\n        alert(`❌ Exploit falhou: ${data.mensagem}`);\n        console.log('❌ Exploit falhou:', data.mensagem);\n      }\n    } catch (error) {\n      console.error('❌ Erro no exploit:', error);\n      alert(`❌ Erro no exploit: ${error.message}`);\n    }\n  };\n  const testConnection = async () => {\n    try {\n      setConnectionStatus('Testando...');\n      const result = await gameApi.testConnection();\n      if (result.success) {\n        setConnectionStatus('✅ Conectado');\n        console.log('✅ Conexão com backend OK:', result.data);\n      } else {\n        setConnectionStatus('❌ Erro de conexão');\n        console.log('❌ Erro de conexão:', result.error);\n      }\n    } catch (error) {\n      setConnectionStatus('❌ Falha na conexão');\n      console.error('❌ Falha no teste de conexão:', error);\n    }\n  };\n  const quickLogin = async () => {\n    try {\n      var _s2 = $RefreshSig$();\n      setLoginStatus('Fazendo login...');\n      const {\n        quickLogin: doQuickLogin\n      } = await _s2(import('../stores/authStore').then(_s2(m => {\n        _s2();\n        return m.useAuth();\n      }, \"aXa0DhOnbpb+WuJfaBQuXhXHp4U=\", false, function () {\n        return [m.useAuth];\n      })), \"aXa0DhOnbpb+WuJfaBQuXhXHp4U=\", true);\n      const success = await doQuickLogin();\n      if (success) {\n        setLoginStatus('✅ Logado');\n        console.log('✅ Login realizado com sucesso');\n      } else {\n        setLoginStatus('❌ Erro no login');\n        console.log('❌ Erro no login');\n      }\n    } catch (error) {\n      setLoginStatus('❌ Falha no login');\n      console.error('❌ Falha no login:', error);\n    }\n  };\n  const playerCPU = (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.cpu) || 1;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.history.back(),\n            className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\u2190\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-lg font-bold\",\n              children: \"\\uD83D\\uDD0D Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-400\",\n              children: \"Sistema de Reconhecimento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: testConnection,\n              className: \"text-xs bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded block w-full\",\n              children: \"Testar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: quickLogin,\n              className: \"text-xs bg-green-600 hover:bg-green-700 px-2 py-1 rounded block w-full\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs\",\n              children: connectionStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs\",\n              children: loginStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-3 text-white\",\n          children: \"\\u26A1 Scan R\\xE1pido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 mb-4\",\n          children: \"Encontra alvos aleat\\xF3rios na rede\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleQuickScan,\n          disabled: isScanning,\n          className: \"w-full py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n          children: isScanning ? 'Escaneando...' : 'Iniciar Scan Rápido'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-3 text-white\",\n          children: \"\\uD83C\\uDFAF Scan Avan\\xE7ado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 mb-4\",\n          children: \"Busca por IP espec\\xEDfico\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: specificIP,\n            onChange: e => setSpecificIP(e.target.value),\n            placeholder: \"*************\",\n            className: \"flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white font-mono text-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAdvancedScan,\n            disabled: isAdvancedScan,\n            className: \"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n            children: isAdvancedScan ? '...' : 'Scan'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), scanError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-300 text-sm font-mono\",\n          children: [\"\\u274C \", scanError]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 11\n      }, this), scanTargets.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"\\uD83D\\uDCCA Alvos Encontrados\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: scanTargets.map((target, index) => {\n            const canExploit = playerCPU > (target.firewall || 1);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-700 rounded-lg p-4 border border-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-blue-400\",\n                    children: target.nick\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400 font-mono\",\n                    children: [\"IP: \", target.ip]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400\",\n                    children: [\"N\\xEDvel: \", target.nivel]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400\",\n                    children: [\"Firewall: \", target.firewall || 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400\",\n                    children: [\"Dinheiro: $\", (target.dinheiro || 0).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded ${canExploit ? 'bg-green-600 text-green-100' : 'bg-red-600 text-red-100'}`,\n                    children: canExploit ? 'VULNERÁVEL' : 'PROTEGIDO'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleExploit(target),\n                  disabled: !canExploit,\n                  className: `px-4 py-2 rounded font-semibold text-sm ${canExploit ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-600 text-gray-400 cursor-not-allowed'}`,\n                  children: canExploit ? 'EXPLOITAR' : 'CPU INSUFICIENTE'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-semibold mb-2 text-white\",\n          children: \"\\uD83D\\uDCBB Seu Sistema\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-2 text-xs\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"CPU: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-400\",\n              children: playerCPU\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"Status: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-400\",\n              children: \"ONLINE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 26\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(GameFooter, {\n      currentPage: \"scanner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 313,\n    columnNumber: 5\n  }, this);\n};\n_s3(SimpleScanner, \"I2Y3tVnn6VOWAAFfKefWQeN+/ao=\", true);\n_c2 = SimpleScanner;\nconst SimpleChat = () => {\n  _s4();\n  const {\n    messages,\n    isLoading: isLoadingMessages,\n    loadMessages,\n    sendMessage,\n    isSending\n  } = useChat();\n  const [newMessage, setNewMessage] = React.useState('');\n  useEffect(() => {\n    if (messages.length === 0 && !isLoadingMessages) {\n      console.log('SimpleChat - Carregando mensagens...');\n      loadMessages();\n    }\n  }, [messages.length, isLoadingMessages, loadMessages]);\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    if (newMessage.trim() && !isSending) {\n      await sendMessage(newMessage.trim());\n      setNewMessage('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold\",\n            children: \"\\uD83D\\uDCAC Chat Global\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Canal Global - Criptografado\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-black rounded-lg p-3 h-full overflow-y-auto mb-4 border border-gray-600\",\n        children: isLoadingMessages ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-blue-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400 mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-mono\",\n            children: \"CARREGANDO DADOS...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this) : messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-blue-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl mb-2\",\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs font-mono\",\n            children: \"CANAL VAZIO - AGUARDANDO TRANSMISS\\xC3O\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-l-2 border-blue-400 pl-3 py-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-400 font-mono text-xs\",\n                children: [\"[\", new Date(message.timestamp).toLocaleTimeString(), \"]\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-400 font-mono text-xs font-bold\",\n                children: [message.usuario, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white font-mono text-sm pl-2\",\n              children: message.mensagem\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 19\n            }, this)]\n          }, message.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSendMessage,\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: newMessage,\n          onChange: e => setNewMessage(e.target.value),\n          placeholder: \"> Digite sua mensagem...\",\n          className: \"flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded font-mono text-white text-sm focus:outline-none focus:border-blue-400 placeholder-gray-400\",\n          disabled: isSending\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: !newMessage.trim() || isSending,\n          className: `px-4 py-2 rounded font-mono text-sm transition-all ${!newMessage.trim() || isSending ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700 font-bold'}`,\n          children: isSending ? '...' : 'ENVIAR'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(GameFooter, {\n      currentPage: \"chat\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 480,\n    columnNumber: 5\n  }, this);\n};\n\n// Navegação Estilo Celular/Jogo\n_s4(SimpleChat, \"OOvOc6vVu05UbQBZJwuAFiCSFbw=\", true);\n_c3 = SimpleChat;\nconst SimpleNavigation = () => {\n  const currentPath = window.location.pathname;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-bg-primary\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-900 to-purple-900 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-black font-bold text-lg\",\n              children: \"S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold cyber-text\",\n              children: \"SHACK\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-text-muted\",\n              children: \"Web Terminal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"status-online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-green-400\",\n            children: \"ONLINE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-bg-secondary border-b border-cyber-primary\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/game\",\n          className: `flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath === '/game' || currentPath === '/game/' ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary' : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg mb-1\",\n            children: \"\\uD83C\\uDFE0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/game/scanner\",\n          className: `flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath.includes('/scanner') ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary' : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg mb-1\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Scanner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/game/chat\",\n          className: `flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath.includes('/chat') ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary' : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg mb-1\",\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 567,\n    columnNumber: 5\n  }, this);\n};\n\n// Página Principal do Jogo Simplificada\n_c4 = SimpleNavigation;\nconst SimpleGamePage = () => {\n  console.log('SimpleGamePage - Renderizando...');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(GameMainPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/scanner\",\n        element: /*#__PURE__*/_jsxDEV(ScannerPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/invaded\",\n        element: /*#__PURE__*/_jsxDEV(InvadedPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/chat\",\n        element: /*#__PURE__*/_jsxDEV(SimpleChat, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/apps\",\n        element: /*#__PURE__*/_jsxDEV(AppsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/transfer\",\n        element: /*#__PURE__*/_jsxDEV(TransferPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/upgrades\",\n        element: /*#__PURE__*/_jsxDEV(UpgradePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/shop\",\n        element: /*#__PURE__*/_jsxDEV(ShopPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/ranking\",\n        element: /*#__PURE__*/_jsxDEV(RankingPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/logs\",\n        element: /*#__PURE__*/_jsxDEV(LogsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/config\",\n        element: /*#__PURE__*/_jsxDEV(ConfigPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/mining\",\n        element: /*#__PURE__*/_jsxDEV(MiningPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/terminal\",\n        element: /*#__PURE__*/_jsxDEV(TerminalPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/bank\",\n        element: /*#__PURE__*/_jsxDEV(BankPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(GameMainPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 650,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 634,\n    columnNumber: 5\n  }, this);\n};\n_c5 = SimpleGamePage;\nexport default SimpleGamePage;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"SimpleDashboard\");\n$RefreshReg$(_c2, \"SimpleScanner\");\n$RefreshReg$(_c3, \"SimpleChat\");\n$RefreshReg$(_c4, \"SimpleNavigation\");\n$RefreshReg$(_c5, \"SimpleGamePage\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "jsxDEV", "_jsxDEV", "SimpleDashboard", "_s", "user", "useSimpleAuth", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "usePlayer", "useEffect", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "conquistas", "ranking", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "toLocaleString", "onClick", "window", "location", "href", "_c", "SimpleScanner", "_s3", "isScanning", "setIsScanning", "useState", "scanTargets", "setScanTargets", "scanError", "setScanError", "specificIP", "setSpecificIP", "isAdvancedScan", "setIsAdvancedScan", "connectionStatus", "setConnectionStatus", "loginStatus", "setLoginStatus", "handleQuickScan", "data", "gameApi", "sucesso", "alvos", "mensagem", "error", "message", "handleAdvancedScan", "trim", "scanSpecificIP", "alvo", "handleExploit", "target", "ip", "exploitTarget", "conexao_criada", "_target$dinheiro", "transferAmount", "prompt", "<PERSON><PERSON><PERSON>", "percentage", "parseInt", "transferResponse", "forceTransfer", "uid", "alert", "testConnection", "result", "success", "quickLogin", "_s2", "$RefreshSig$", "doQuickLogin", "then", "m", "useAuth", "playerCPU", "cpu", "history", "back", "disabled", "type", "value", "onChange", "e", "placeholder", "length", "map", "index", "canExploit", "firewall", "GameFooter", "currentPage", "_c2", "SimpleChat", "_s4", "messages", "isLoading", "isLoadingMessages", "loadMessages", "sendMessage", "isSending", "useChat", "newMessage", "setNewMessage", "handleSendMessage", "preventDefault", "Date", "timestamp", "toLocaleTimeString", "usuario", "id", "onSubmit", "_c3", "SimpleNavigation", "currentPath", "pathname", "includes", "_c4", "SimpleGamePage", "path", "element", "GameMainPage", "ScannerPage", "InvadedPage", "AppsPage", "TransferPage", "UpgradePage", "ShopPage", "RankingPage", "LogsPage", "ConfigPage", "MiningPage", "TerminalPage", "BankPage", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleGamePage.tsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route } from 'react-router-dom';\n\n// Componente do Dashboard Simplificado\nconst SimpleDashboard: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n\n  return (\n    <div className=\"p-4\">\n      {/* Header compacto */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold cyber-text\">\n              Terminal Principal\n            </h1>\n            <p className=\"text-sm text-text-muted\">\n              Operador: <span className=\"text-cyber-primary\">{user?.nick || 'Jogador'}</span>\n            </p>\n          </div>\n          {isLoadingPlayer && (\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary\"></div>\n          )}\n        </div>\n      </div>\n\n      {/* Stats do Jogador - Layout Celular */}\n      <div className=\"space-y-4\">\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">📊 Status do Sistema</h2>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-blue-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-blue-400\">\n                    {isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()}\n                  </div>\n                  <div className=\"text-xs text-blue-300\">PONTOS</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-green-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-green-400\">\n                    {isLoadingPlayer ? '...' : playerData.nivel}\n                  </div>\n                  <div className=\"text-xs text-green-300\">NÍVEL</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-purple-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-purple-400\">\n                    {isLoadingPlayer ? '...' : playerData.conquistas}\n                  </div>\n                  <div className=\"text-xs text-purple-300\">CONQUISTAS</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-orange-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-orange-400\">\n                    {isLoadingPlayer ? '...' : playerData.ranking}\n                  </div>\n                  <div className=\"text-xs text-orange-300\">RANKING</div>\n                </div>\n              </div>\n            </div>\n        </div>\n\n        {/* Ações Rápidas - Estilo Celular */}\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">⚡ Acesso Rápido</h2>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <button\n                onClick={() => window.location.href = '/game/scanner'}\n                className=\"btn-cyber text-sm py-3\"\n              >\n                <div className=\"text-lg mb-1\">🔍</div>\n                <div>Scanner</div>\n              </button>\n              <button\n                onClick={() => window.location.href = '/game/chat'}\n                className=\"btn-cyber text-sm py-3\"\n              >\n                <div className=\"text-lg mb-1\">💬</div>\n                <div>Chat</div>\n              </button>\n              <button className=\"btn-cyber text-sm py-3\">\n                <div className=\"text-lg mb-1\">🏆</div>\n                <div>Loja</div>\n              </button>\n              <button className=\"btn-cyber text-sm py-3\">\n                <div className=\"text-lg mb-1\">⚙️</div>\n                <div>Config</div>\n              </button>\n            </div>\n        </div>\n\n        {/* Log de Atividades - Estilo Terminal */}\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">📈 Log do Sistema</h2>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">+</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Pontos ganhos</div>\n                    <div className=\"text-xs text-text-muted\">02:15:33</div>\n                  </div>\n                </div>\n                <div className=\"text-green-400 font-bold text-sm\">+150</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">🏆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Nova conquista</div>\n                    <div className=\"text-xs text-text-muted\">01:22:15</div>\n                  </div>\n                </div>\n                <div className=\"text-blue-400 font-bold text-sm\">HACK</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">↑</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Level UP</div>\n                    <div className=\"text-xs text-text-muted\">00:45:22</div>\n                  </div>\n                </div>\n                <div className=\"text-purple-400 font-bold text-sm\">LV.15</div>\n              </div>\n            </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Componente Scanner Completo\nconst SimpleScanner: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer } = usePlayer();\n  const [isScanning, setIsScanning] = React.useState(false);\n  const [scanTargets, setScanTargets] = React.useState<any[]>([]);\n  const [scanError, setScanError] = React.useState<string | null>(null);\n  const [specificIP, setSpecificIP] = React.useState('');\n  const [isAdvancedScan, setIsAdvancedScan] = React.useState(false);\n  const [connectionStatus, setConnectionStatus] = React.useState<string>('Não testado');\n  const [loginStatus, setLoginStatus] = React.useState<string>('Não logado');\n\n  const handleQuickScan = async () => {\n    setIsScanning(true);\n    setScanError(null);\n\n    try {\n      console.log('🔍 Iniciando scan rápido...');\n      const data = await gameApi.scanTargets();\n\n      if (data.sucesso && data.alvos) {\n        setScanTargets(data.alvos);\n        console.log('✅ Scan realizado com sucesso:', data.alvos);\n      } else {\n        setScanError(data.mensagem || 'Erro ao escanear alvos');\n        console.log('❌ Erro no scan:', data.mensagem);\n      }\n    } catch (error: any) {\n      console.error('❌ Erro no scan:', error);\n      setScanError(error.message || 'Erro de conexão com o servidor');\n    } finally {\n      setIsScanning(false);\n    }\n  };\n\n  const handleAdvancedScan = async () => {\n    if (!specificIP.trim()) {\n      setScanError('Digite um IP válido');\n      return;\n    }\n\n    setIsAdvancedScan(true);\n    setScanError(null);\n\n    try {\n      console.log(`🎯 Iniciando scan avançado para IP: ${specificIP}`);\n      const data = await gameApi.scanSpecificIP(specificIP);\n\n      if (data.sucesso && data.alvo) {\n        setScanTargets([data.alvo]);\n        console.log('✅ Scan avançado realizado:', data.alvo);\n      } else {\n        setScanError(data.mensagem || 'IP não encontrado');\n        console.log('❌ IP não encontrado:', data.mensagem);\n      }\n    } catch (error: any) {\n      console.error('❌ Erro no scan avançado:', error);\n      setScanError(error.message || 'Erro de conexão com o servidor');\n    } finally {\n      setIsAdvancedScan(false);\n    }\n  };\n\n  const handleExploit = async (target: any) => {\n    try {\n      console.log(`⚔️ Iniciando exploit contra: ${target.nick} (${target.ip})`);\n      const data = await gameApi.exploitTarget(target.ip);\n\n      if (data.sucesso) {\n        console.log('✅ Exploit realizado com sucesso:', data);\n\n        // Mostrar opções de transferência se o exploit foi bem-sucedido\n        if (data.conexao_criada) {\n          const transferAmount = prompt(\n            `Exploit bem-sucedido em ${target.nick}!\\n\\n` +\n            `Dinheiro disponível: $${target.dinheiro?.toLocaleString() || '0'}\\n\\n` +\n            `Digite a porcentagem para transferir (20-80%):`\n          );\n\n          if (transferAmount) {\n            const percentage = parseInt(transferAmount);\n            if (percentage >= 20 && percentage <= 80) {\n              try {\n                const transferResponse = await gameApi.forceTransfer(target.uid || target.ip, percentage);\n                if (transferResponse.sucesso) {\n                  alert(`Transferência realizada! ${transferResponse.mensagem}`);\n                } else {\n                  alert(`Erro na transferência: ${transferResponse.mensagem}`);\n                }\n              } catch (error) {\n                alert('Erro na transferência');\n              }\n            } else {\n              alert('Porcentagem deve estar entre 20% e 80%');\n            }\n          }\n        } else {\n          alert(`✅ Exploit bem-sucedido! ${data.mensagem}`);\n        }\n\n        // Recarregar lista de alvos\n        handleQuickScan();\n      } else {\n        alert(`❌ Exploit falhou: ${data.mensagem}`);\n        console.log('❌ Exploit falhou:', data.mensagem);\n      }\n    } catch (error: any) {\n      console.error('❌ Erro no exploit:', error);\n      alert(`❌ Erro no exploit: ${error.message}`);\n    }\n  };\n\n  const testConnection = async () => {\n    try {\n      setConnectionStatus('Testando...');\n      const result = await gameApi.testConnection();\n\n      if (result.success) {\n        setConnectionStatus('✅ Conectado');\n        console.log('✅ Conexão com backend OK:', result.data);\n      } else {\n        setConnectionStatus('❌ Erro de conexão');\n        console.log('❌ Erro de conexão:', result.error);\n      }\n    } catch (error) {\n      setConnectionStatus('❌ Falha na conexão');\n      console.error('❌ Falha no teste de conexão:', error);\n    }\n  };\n\n  const quickLogin = async () => {\n    try {\n      setLoginStatus('Fazendo login...');\n      const { quickLogin: doQuickLogin } = await import('../stores/authStore').then(m => m.useAuth());\n      const success = await doQuickLogin();\n\n      if (success) {\n        setLoginStatus('✅ Logado');\n        console.log('✅ Login realizado com sucesso');\n      } else {\n        setLoginStatus('❌ Erro no login');\n        console.log('❌ Erro no login');\n      }\n    } catch (error) {\n      setLoginStatus('❌ Falha no login');\n      console.error('❌ Falha no login:', error);\n    }\n  };\n\n  const playerCPU = currentPlayer?.cpu || 1;\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header com informações do scanner */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => window.history.back()}\n              className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n            >\n              <span className=\"text-lg\">←</span>\n            </button>\n            <div>\n              <h1 className=\"text-lg font-bold\">🔍 Scanner</h1>\n              <p className=\"text-xs text-gray-400\">Sistema de Reconhecimento</p>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"space-y-1\">\n              <button\n                onClick={testConnection}\n                className=\"text-xs bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded block w-full\"\n              >\n                Testar\n              </button>\n              <button\n                onClick={quickLogin}\n                className=\"text-xs bg-green-600 hover:bg-green-700 px-2 py-1 rounded block w-full\"\n              >\n                Login\n              </button>\n              <p className=\"text-xs\">{connectionStatus}</p>\n              <p className=\"text-xs\">{loginStatus}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-4\">\n        {/* Scan Rápido */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-3 text-white\">⚡ Scan Rápido</h3>\n          <p className=\"text-sm text-gray-400 mb-4\">Encontra alvos aleatórios na rede</p>\n          <button\n            onClick={handleQuickScan}\n            disabled={isScanning}\n            className=\"w-full py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n          >\n            {isScanning ? 'Escaneando...' : 'Iniciar Scan Rápido'}\n          </button>\n        </div>\n\n        {/* Scan Avançado */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-3 text-white\">🎯 Scan Avançado</h3>\n          <p className=\"text-sm text-gray-400 mb-4\">Busca por IP específico</p>\n          <div className=\"flex space-x-2\">\n            <input\n              type=\"text\"\n              value={specificIP}\n              onChange={(e) => setSpecificIP(e.target.value)}\n              placeholder=\"*************\"\n              className=\"flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white font-mono text-sm\"\n            />\n            <button\n              onClick={handleAdvancedScan}\n              disabled={isAdvancedScan}\n              className=\"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n            >\n              {isAdvancedScan ? '...' : 'Scan'}\n            </button>\n          </div>\n        </div>\n\n        {/* Erro */}\n        {scanError && (\n          <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n            <p className=\"text-red-300 text-sm font-mono\">❌ {scanError}</p>\n          </div>\n        )}\n\n        {/* Resultados */}\n        {scanTargets.length > 0 && (\n          <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n            <h3 className=\"text-lg font-semibold mb-4 text-white\">📊 Alvos Encontrados</h3>\n            <div className=\"space-y-3\">\n              {scanTargets.map((target, index) => {\n                const canExploit = playerCPU > (target.firewall || 1);\n\n                return (\n                  <div key={index} className=\"bg-gray-700 rounded-lg p-4 border border-gray-600\">\n                    <div className=\"flex justify-between items-start mb-3\">\n                      <div>\n                        <h4 className=\"font-bold text-blue-400\">{target.nick}</h4>\n                        <p className=\"text-xs text-gray-400 font-mono\">IP: {target.ip}</p>\n                        <p className=\"text-xs text-gray-400\">Nível: {target.nivel}</p>\n                      </div>\n                      <div className=\"text-right\">\n                        <p className=\"text-xs text-gray-400\">Firewall: {target.firewall || 1}</p>\n                        <p className=\"text-xs text-gray-400\">Dinheiro: ${(target.dinheiro || 0).toLocaleString()}</p>\n                      </div>\n                    </div>\n\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"text-xs\">\n                        <span className={`px-2 py-1 rounded ${\n                          canExploit ? 'bg-green-600 text-green-100' : 'bg-red-600 text-red-100'\n                        }`}>\n                          {canExploit ? 'VULNERÁVEL' : 'PROTEGIDO'}\n                        </span>\n                      </div>\n\n                      <button\n                        onClick={() => handleExploit(target)}\n                        disabled={!canExploit}\n                        className={`px-4 py-2 rounded font-semibold text-sm ${\n                          canExploit\n                            ? 'bg-green-600 hover:bg-green-700 text-white'\n                            : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                        }`}\n                      >\n                        {canExploit ? 'EXPLOITAR' : 'CPU INSUFICIENTE'}\n                      </button>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        )}\n\n        {/* Info do Jogador */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-sm font-semibold mb-2 text-white\">💻 Seu Sistema</h3>\n          <div className=\"grid grid-cols-2 gap-2 text-xs\">\n            <div>CPU: <span className=\"text-blue-400\">{playerCPU}</span></div>\n            <div>Status: <span className=\"text-green-400\">ONLINE</span></div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer com navegação */}\n      <GameFooter currentPage=\"scanner\" />\n    </div>\n  );\n};\n\nconst SimpleChat: React.FC = () => {\n  const { messages, isLoading: isLoadingMessages, loadMessages, sendMessage, isSending } = useChat();\n  const [newMessage, setNewMessage] = React.useState('');\n\n  useEffect(() => {\n    if (messages.length === 0 && !isLoadingMessages) {\n      console.log('SimpleChat - Carregando mensagens...');\n      loadMessages();\n    }\n  }, [messages.length, isLoadingMessages, loadMessages]);\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (newMessage.trim() && !isSending) {\n      await sendMessage(newMessage.trim());\n      setNewMessage('');\n    }\n  };\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header do chat */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => window.history.back()}\n            className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n          >\n            <span className=\"text-lg\">←</span>\n          </button>\n          <div>\n            <h1 className=\"text-lg font-bold\">💬 Chat Global</h1>\n            <p className=\"text-xs text-gray-400\">Canal Global - Criptografado</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Área de mensagens */}\n      <div className=\"flex-1 p-4 overflow-hidden\">\n        <div className=\"bg-black rounded-lg p-3 h-full overflow-y-auto mb-4 border border-gray-600\">\n          {isLoadingMessages ? (\n            <div className=\"text-center text-blue-400\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400 mx-auto mb-2\"></div>\n              <span className=\"text-xs font-mono\">CARREGANDO DADOS...</span>\n            </div>\n          ) : messages.length === 0 ? (\n            <div className=\"text-center text-blue-400\">\n              <div className=\"text-2xl mb-2\">💬</div>\n              <p className=\"text-xs font-mono\">CANAL VAZIO - AGUARDANDO TRANSMISSÃO</p>\n            </div>\n          ) : (\n            <div className=\"space-y-2\">\n              {messages.map((message) => (\n                <div key={message.id} className=\"border-l-2 border-blue-400 pl-3 py-1\">\n                  <div className=\"flex items-center space-x-2 mb-1\">\n                    <span className=\"text-blue-400 font-mono text-xs\">\n                      [{new Date(message.timestamp).toLocaleTimeString()}]\n                    </span>\n                    <span className=\"text-green-400 font-mono text-xs font-bold\">\n                      {message.usuario}:\n                    </span>\n                  </div>\n                  <p className=\"text-white font-mono text-sm pl-2\">{message.mensagem}</p>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n      </div>\n\n      {/* Formulário de envio */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n          <input\n            type=\"text\"\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            placeholder=\"> Digite sua mensagem...\"\n            className=\"flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded font-mono text-white text-sm focus:outline-none focus:border-blue-400 placeholder-gray-400\"\n            disabled={isSending}\n          />\n          <button\n            type=\"submit\"\n            disabled={!newMessage.trim() || isSending}\n            className={`px-4 py-2 rounded font-mono text-sm transition-all ${\n              !newMessage.trim() || isSending\n                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                : 'bg-blue-600 text-white hover:bg-blue-700 font-bold'\n            }`}\n          >\n            {isSending ? '...' : 'ENVIAR'}\n          </button>\n        </form>\n      </div>\n\n      {/* Footer com navegação */}\n      <GameFooter currentPage=\"chat\" />\n    </div>\n  );\n};\n\n// Navegação Estilo Celular/Jogo\nconst SimpleNavigation: React.FC = () => {\n  const currentPath = window.location.pathname;\n\n  return (\n    <div className=\"bg-bg-primary\">\n      {/* Header estilo celular */}\n      <div className=\"bg-gradient-to-r from-blue-900 to-purple-900 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-black font-bold text-lg\">S</span>\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold cyber-text\">SHACK</h1>\n              <p className=\"text-xs text-text-muted\">Web Terminal</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"status-online\"></div>\n            <span className=\"text-xs text-green-400\">ONLINE</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Menu de navegação estilo celular */}\n      <div className=\"bg-bg-secondary border-b border-cyber-primary\">\n        <div className=\"flex\">\n          <a\n            href=\"/game\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath === '/game' || currentPath === '/game/'\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">🏠</div>\n            <div>Dashboard</div>\n          </a>\n          <a\n            href=\"/game/scanner\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath.includes('/scanner')\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">🔍</div>\n            <div>Scanner</div>\n          </a>\n          <a\n            href=\"/game/chat\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath.includes('/chat')\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">💬</div>\n            <div>Chat</div>\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Página Principal do Jogo Simplificada\nconst SimpleGamePage: React.FC = () => {\n  console.log('SimpleGamePage - Renderizando...');\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white\">\n      <Routes>\n        <Route path=\"/\" element={<GameMainPage />} />\n        <Route path=\"/scanner\" element={<ScannerPage />} />\n        <Route path=\"/invaded\" element={<InvadedPage />} />\n        <Route path=\"/chat\" element={<SimpleChat />} />\n        <Route path=\"/apps\" element={<AppsPage />} />\n        <Route path=\"/transfer\" element={<TransferPage />} />\n        <Route path=\"/upgrades\" element={<UpgradePage />} />\n        <Route path=\"/shop\" element={<ShopPage />} />\n        <Route path=\"/ranking\" element={<RankingPage />} />\n        <Route path=\"/logs\" element={<LogsPage />} />\n        <Route path=\"/config\" element={<ConfigPage />} />\n        <Route path=\"/mining\" element={<MiningPage />} />\n        <Route path=\"/terminal\" element={<TerminalPage />} />\n        <Route path=\"/bank\" element={<BankPage />} />\n        <Route path=\"*\" element={<GameMainPage />} />\n      </Routes>\n    </div>\n  );\n};\n\nexport default SimpleGamePage;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGC,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEC,aAAa;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGC,SAAS,CAAC,CAAC;EAErFC,SAAS,CAAC,MAAM;IACd,IAAI,CAACF,aAAa,IAAI,CAACF,eAAe,EAAE;MACtCK,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/DL,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACC,aAAa,EAAEF,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAEpD;EACA,MAAMM,UAAU,GAAGR,aAAa,IAAI;IAClCS,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE;EACX,CAAC;EAED,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElBnB,OAAA;MAAKkB,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBnB,OAAA;QAAKkB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDnB,OAAA;UAAAmB,QAAA,gBACEnB,OAAA;YAAIkB,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvB,OAAA;YAAGkB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,GAAC,YAC3B,eAAAnB,OAAA;cAAMkB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAE,CAAAhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,KAAI;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLjB,eAAe,iBACdN,OAAA;UAAKkB,SAAS,EAAC;QAAmE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACzF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBnB,OAAA;QAAKkB,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC1FnB,OAAA;UAAIkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EvB,OAAA;UAAKkB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCnB,OAAA;YAAKkB,SAAS,EAAC,sDAAsD;YAAAC,QAAA,eACnEnB,OAAA;cAAKkB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnB,OAAA;gBAAKkB,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC7Cb,eAAe,GAAG,KAAK,GAAGO,UAAU,CAACC,MAAM,CAACW,cAAc,CAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNvB,OAAA;gBAAKkB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvB,OAAA;YAAKkB,SAAS,EAAC,uDAAuD;YAAAC,QAAA,eACpEnB,OAAA;cAAKkB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnB,OAAA;gBAAKkB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9Cb,eAAe,GAAG,KAAK,GAAGO,UAAU,CAACE;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACNvB,OAAA;gBAAKkB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvB,OAAA;YAAKkB,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrEnB,OAAA;cAAKkB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnB,OAAA;gBAAKkB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/Cb,eAAe,GAAG,KAAK,GAAGO,UAAU,CAACG;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNvB,OAAA;gBAAKkB,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvB,OAAA;YAAKkB,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrEnB,OAAA;cAAKkB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnB,OAAA;gBAAKkB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/Cb,eAAe,GAAG,KAAK,GAAGO,UAAU,CAACI;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNvB,OAAA;gBAAKkB,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC1FnB,OAAA;UAAIkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EvB,OAAA;UAAKkB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCnB,OAAA;YACE0B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,eAAgB;YACtDX,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAElCnB,OAAA;cAAKkB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCvB,OAAA;cAAAmB,QAAA,EAAK;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACTvB,OAAA;YACE0B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAa;YACnDX,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAElCnB,OAAA;cAAKkB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCvB,OAAA;cAAAmB,QAAA,EAAK;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACTvB,OAAA;YAAQkB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACxCnB,OAAA;cAAKkB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCvB,OAAA;cAAAmB,QAAA,EAAK;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACTvB,OAAA;YAAQkB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACxCnB,OAAA;cAAKkB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCvB,OAAA;cAAAmB,QAAA,EAAK;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC1FnB,OAAA;UAAIkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EvB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAKkB,SAAS,EAAC,qFAAqF;YAAAC,QAAA,gBAClGnB,OAAA;cAAKkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnB,OAAA;gBAAKkB,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,eACjFnB,OAAA;kBAAMkB,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNvB,OAAA;gBAAAmB,QAAA,gBACEnB,OAAA;kBAAKkB,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDvB,OAAA;kBAAKkB,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvB,OAAA;cAAKkB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAENvB,OAAA;YAAKkB,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGnB,OAAA;cAAKkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnB,OAAA;gBAAKkB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAChFnB,OAAA;kBAAMkB,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNvB,OAAA;gBAAAmB,QAAA,gBACEnB,OAAA;kBAAKkB,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzDvB,OAAA;kBAAKkB,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvB,OAAA;cAAKkB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAENvB,OAAA;YAAKkB,SAAS,EAAC,sFAAsF;YAAAC,QAAA,gBACnGnB,OAAA;cAAKkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnB,OAAA;gBAAKkB,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,eAClFnB,OAAA;kBAAMkB,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNvB,OAAA;gBAAAmB,QAAA,gBACEnB,OAAA;kBAAKkB,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDvB,OAAA;kBAAKkB,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvB,OAAA;cAAKkB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAArB,EAAA,CA5JMD,eAAyB;AAAA6B,EAAA,GAAzB7B,eAAyB;AA6J/B,MAAM8B,aAAuB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpC,MAAM;IAAE7B;EAAK,CAAC,GAAGC,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEC;EAAc,CAAC,GAAGI,SAAS,CAAC,CAAC;EACrC,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGtC,KAAK,CAACuC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,KAAK,CAACuC,QAAQ,CAAQ,EAAE,CAAC;EAC/D,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAG3C,KAAK,CAACuC,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAG7C,KAAK,CAACuC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACO,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,KAAK,CAACuC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACS,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,KAAK,CAACuC,QAAQ,CAAS,aAAa,CAAC;EACrF,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGnD,KAAK,CAACuC,QAAQ,CAAS,YAAY,CAAC;EAE1E,MAAMa,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCd,aAAa,CAAC,IAAI,CAAC;IACnBK,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF5B,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,MAAMqC,IAAI,GAAG,MAAMC,OAAO,CAACd,WAAW,CAAC,CAAC;MAExC,IAAIa,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACG,KAAK,EAAE;QAC9Bf,cAAc,CAACY,IAAI,CAACG,KAAK,CAAC;QAC1BzC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEqC,IAAI,CAACG,KAAK,CAAC;MAC1D,CAAC,MAAM;QACLb,YAAY,CAACU,IAAI,CAACI,QAAQ,IAAI,wBAAwB,CAAC;QACvD1C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqC,IAAI,CAACI,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB3C,OAAO,CAAC2C,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCf,YAAY,CAACe,KAAK,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACjE,CAAC,SAAS;MACRrB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMsB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAChB,UAAU,CAACiB,IAAI,CAAC,CAAC,EAAE;MACtBlB,YAAY,CAAC,qBAAqB,CAAC;MACnC;IACF;IAEAI,iBAAiB,CAAC,IAAI,CAAC;IACvBJ,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF5B,OAAO,CAACC,GAAG,CAAC,uCAAuC4B,UAAU,EAAE,CAAC;MAChE,MAAMS,IAAI,GAAG,MAAMC,OAAO,CAACQ,cAAc,CAAClB,UAAU,CAAC;MAErD,IAAIS,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACU,IAAI,EAAE;QAC7BtB,cAAc,CAAC,CAACY,IAAI,CAACU,IAAI,CAAC,CAAC;QAC3BhD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqC,IAAI,CAACU,IAAI,CAAC;MACtD,CAAC,MAAM;QACLpB,YAAY,CAACU,IAAI,CAACI,QAAQ,IAAI,mBAAmB,CAAC;QAClD1C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEqC,IAAI,CAACI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB3C,OAAO,CAAC2C,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDf,YAAY,CAACe,KAAK,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACjE,CAAC,SAAS;MACRZ,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMiB,aAAa,GAAG,MAAOC,MAAW,IAAK;IAC3C,IAAI;MACFlD,OAAO,CAACC,GAAG,CAAC,gCAAgCiD,MAAM,CAACrC,IAAI,KAAKqC,MAAM,CAACC,EAAE,GAAG,CAAC;MACzE,MAAMb,IAAI,GAAG,MAAMC,OAAO,CAACa,aAAa,CAACF,MAAM,CAACC,EAAE,CAAC;MAEnD,IAAIb,IAAI,CAACE,OAAO,EAAE;QAChBxC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEqC,IAAI,CAAC;;QAErD;QACA,IAAIA,IAAI,CAACe,cAAc,EAAE;UAAA,IAAAC,gBAAA;UACvB,MAAMC,cAAc,GAAGC,MAAM,CAC3B,2BAA2BN,MAAM,CAACrC,IAAI,OAAO,GAC7C,yBAAyB,EAAAyC,gBAAA,GAAAJ,MAAM,CAACO,QAAQ,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBxC,cAAc,CAAC,CAAC,KAAI,GAAG,MAAM,GACvE,gDACF,CAAC;UAED,IAAIyC,cAAc,EAAE;YAClB,MAAMG,UAAU,GAAGC,QAAQ,CAACJ,cAAc,CAAC;YAC3C,IAAIG,UAAU,IAAI,EAAE,IAAIA,UAAU,IAAI,EAAE,EAAE;cACxC,IAAI;gBACF,MAAME,gBAAgB,GAAG,MAAMrB,OAAO,CAACsB,aAAa,CAACX,MAAM,CAACY,GAAG,IAAIZ,MAAM,CAACC,EAAE,EAAEO,UAAU,CAAC;gBACzF,IAAIE,gBAAgB,CAACpB,OAAO,EAAE;kBAC5BuB,KAAK,CAAC,4BAA4BH,gBAAgB,CAAClB,QAAQ,EAAE,CAAC;gBAChE,CAAC,MAAM;kBACLqB,KAAK,CAAC,0BAA0BH,gBAAgB,CAAClB,QAAQ,EAAE,CAAC;gBAC9D;cACF,CAAC,CAAC,OAAOC,KAAK,EAAE;gBACdoB,KAAK,CAAC,uBAAuB,CAAC;cAChC;YACF,CAAC,MAAM;cACLA,KAAK,CAAC,wCAAwC,CAAC;YACjD;UACF;QACF,CAAC,MAAM;UACLA,KAAK,CAAC,2BAA2BzB,IAAI,CAACI,QAAQ,EAAE,CAAC;QACnD;;QAEA;QACAL,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACL0B,KAAK,CAAC,qBAAqBzB,IAAI,CAACI,QAAQ,EAAE,CAAC;QAC3C1C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEqC,IAAI,CAACI,QAAQ,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB3C,OAAO,CAAC2C,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CoB,KAAK,CAAC,sBAAsBpB,KAAK,CAACC,OAAO,EAAE,CAAC;IAC9C;EACF,CAAC;EAED,MAAMoB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF9B,mBAAmB,CAAC,aAAa,CAAC;MAClC,MAAM+B,MAAM,GAAG,MAAM1B,OAAO,CAACyB,cAAc,CAAC,CAAC;MAE7C,IAAIC,MAAM,CAACC,OAAO,EAAE;QAClBhC,mBAAmB,CAAC,aAAa,CAAC;QAClClC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgE,MAAM,CAAC3B,IAAI,CAAC;MACvD,CAAC,MAAM;QACLJ,mBAAmB,CAAC,mBAAmB,CAAC;QACxClC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgE,MAAM,CAACtB,KAAK,CAAC;MACjD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdT,mBAAmB,CAAC,oBAAoB,CAAC;MACzClC,OAAO,CAAC2C,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMwB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MAAA,IAAAC,GAAA,GAAAC,YAAA;MACFjC,cAAc,CAAC,kBAAkB,CAAC;MAClC,MAAM;QAAE+B,UAAU,EAAEG;MAAa,CAAC,GAAG,MAAAF,GAAA,CAAM,MAAM,CAAC,qBAAqB,CAAC,CAACG,IAAI,CAAAH,GAAA,CAACI,CAAC;QAAAJ,GAAA;QAAA,OAAII,CAAC,CAACC,OAAO,CAAC,CAAC;MAAA;QAAA,QAAXD,CAAC,CAACC,OAAO;MAAA,EAAE,CAAC;MAC/F,MAAMP,OAAO,GAAG,MAAMI,YAAY,CAAC,CAAC;MAEpC,IAAIJ,OAAO,EAAE;QACX9B,cAAc,CAAC,UAAU,CAAC;QAC1BpC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC9C,CAAC,MAAM;QACLmC,cAAc,CAAC,iBAAiB,CAAC;QACjCpC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAChC;IACF,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACdP,cAAc,CAAC,kBAAkB,CAAC;MAClCpC,OAAO,CAAC2C,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C;EACF,CAAC;EAED,MAAM+B,SAAS,GAAG,CAAAhF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiF,GAAG,KAAI,CAAC;EAEzC,oBACEtF,OAAA;IAAKkB,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DnB,OAAA;MAAKkB,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEnB,OAAA;QAAKkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnB,OAAA;UAAKkB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CnB,OAAA;YACE0B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAAC4D,OAAO,CAACC,IAAI,CAAC,CAAE;YACrCtE,SAAS,EAAC,mFAAmF;YAAAC,QAAA,eAE7FnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACTvB,OAAA;YAAAmB,QAAA,gBACEnB,OAAA;cAAIkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDvB,OAAA;cAAGkB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvB,OAAA;UAAKkB,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBnB,OAAA;YAAKkB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBnB,OAAA;cACE0B,OAAO,EAAEiD,cAAe;cACxBzD,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EACjF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvB,OAAA;cACE0B,OAAO,EAAEoD,UAAW;cACpB5D,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EACnF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvB,OAAA;cAAGkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEyB;YAAgB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7CvB,OAAA;cAAGkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAE2B;YAAW;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBAEnDnB,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEnB,OAAA;UAAIkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEvB,OAAA;UAAGkB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/EvB,OAAA;UACE0B,OAAO,EAAEsB,eAAgB;UACzByC,QAAQ,EAAExD,UAAW;UACrBf,SAAS,EAAC,oGAAoG;UAAAC,QAAA,EAE7Gc,UAAU,GAAG,eAAe,GAAG;QAAqB;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEnB,OAAA;UAAIkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EvB,OAAA;UAAGkB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrEvB,OAAA;UAAKkB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BnB,OAAA;YACE0F,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEnD,UAAW;YAClBoD,QAAQ,EAAGC,CAAC,IAAKpD,aAAa,CAACoD,CAAC,CAAChC,MAAM,CAAC8B,KAAK,CAAE;YAC/CG,WAAW,EAAC,eAAe;YAC3B5E,SAAS,EAAC;UAA0F;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,eACFvB,OAAA;YACE0B,OAAO,EAAE8B,kBAAmB;YAC5BiC,QAAQ,EAAE/C,cAAe;YACzBxB,SAAS,EAAC,oGAAoG;YAAAC,QAAA,EAE7GuB,cAAc,GAAG,KAAK,GAAG;UAAM;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLe,SAAS,iBACRtC,OAAA;QAAKkB,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DnB,OAAA;UAAGkB,SAAS,EAAC,gCAAgC;UAAAC,QAAA,GAAC,SAAE,EAACmB,SAAS;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CACN,EAGAa,WAAW,CAAC2D,MAAM,GAAG,CAAC,iBACrB/F,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEnB,OAAA;UAAIkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EvB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBiB,WAAW,CAAC4D,GAAG,CAAC,CAACnC,MAAM,EAAEoC,KAAK,KAAK;YAClC,MAAMC,UAAU,GAAGb,SAAS,IAAIxB,MAAM,CAACsC,QAAQ,IAAI,CAAC,CAAC;YAErD,oBACEnG,OAAA;cAAiBkB,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAC5EnB,OAAA;gBAAKkB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDnB,OAAA;kBAAAmB,QAAA,gBACEnB,OAAA;oBAAIkB,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAE0C,MAAM,CAACrC;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1DvB,OAAA;oBAAGkB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAAC,MAAI,EAAC0C,MAAM,CAACC,EAAE;kBAAA;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClEvB,OAAA;oBAAGkB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,YAAO,EAAC0C,MAAM,CAAC9C,KAAK;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACNvB,OAAA;kBAAKkB,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBnB,OAAA;oBAAGkB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,YAAU,EAAC0C,MAAM,CAACsC,QAAQ,IAAI,CAAC;kBAAA;oBAAA/E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzEvB,OAAA;oBAAGkB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,aAAW,EAAC,CAAC0C,MAAM,CAACO,QAAQ,IAAI,CAAC,EAAE3C,cAAc,CAAC,CAAC;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvB,OAAA;gBAAKkB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDnB,OAAA;kBAAKkB,SAAS,EAAC,SAAS;kBAAAC,QAAA,eACtBnB,OAAA;oBAAMkB,SAAS,EAAE,qBACfgF,UAAU,GAAG,6BAA6B,GAAG,yBAAyB,EACrE;oBAAA/E,QAAA,EACA+E,UAAU,GAAG,YAAY,GAAG;kBAAW;oBAAA9E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENvB,OAAA;kBACE0B,OAAO,EAAEA,CAAA,KAAMkC,aAAa,CAACC,MAAM,CAAE;kBACrC4B,QAAQ,EAAE,CAACS,UAAW;kBACtBhF,SAAS,EAAE,2CACTgF,UAAU,GACN,4CAA4C,GAC5C,8CAA8C,EACjD;kBAAA/E,QAAA,EAEF+E,UAAU,GAAG,WAAW,GAAG;gBAAkB;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAjCE0E,KAAK;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDvB,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEnB,OAAA;UAAIkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEvB,OAAA;UAAKkB,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7CnB,OAAA;YAAAmB,QAAA,GAAK,OAAK,eAAAnB,OAAA;cAAMkB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEkE;YAAS;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClEvB,OAAA;YAAAmB,QAAA,GAAK,UAAQ,eAAAnB,OAAA;cAAMkB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA,CAACoG,UAAU;MAACC,WAAW,EAAC;IAAS;MAAAjF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjC,CAAC;AAEV,CAAC;AAACS,GAAA,CAxSID,aAAuB;AAAAuE,GAAA,GAAvBvE,aAAuB;AA0S7B,MAAMwE,UAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM;IAAEC,QAAQ;IAAEC,SAAS,EAAEC,iBAAiB;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGC,OAAO,CAAC,CAAC;EAClG,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrH,KAAK,CAACuC,QAAQ,CAAC,EAAE,CAAC;EAEtDzB,SAAS,CAAC,MAAM;IACd,IAAI+F,QAAQ,CAACV,MAAM,KAAK,CAAC,IAAI,CAACY,iBAAiB,EAAE;MAC/ChG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnDgG,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACH,QAAQ,CAACV,MAAM,EAAEY,iBAAiB,EAAEC,YAAY,CAAC,CAAC;EAEtD,MAAMM,iBAAiB,GAAG,MAAOrB,CAAkB,IAAK;IACtDA,CAAC,CAACsB,cAAc,CAAC,CAAC;IAClB,IAAIH,UAAU,CAACvD,IAAI,CAAC,CAAC,IAAI,CAACqD,SAAS,EAAE;MACnC,MAAMD,WAAW,CAACG,UAAU,CAACvD,IAAI,CAAC,CAAC,CAAC;MACpCwD,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,oBACEjH,OAAA;IAAKkB,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DnB,OAAA;MAAKkB,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEnB,OAAA;QAAKkB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CnB,OAAA;UACE0B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAAC4D,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCtE,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7FnB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACTvB,OAAA;UAAAmB,QAAA,gBACEnB,OAAA;YAAIkB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDvB,OAAA;YAAGkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCnB,OAAA;QAAKkB,SAAS,EAAC,4EAA4E;QAAAC,QAAA,EACxFwF,iBAAiB,gBAChB3G,OAAA;UAAKkB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCnB,OAAA;YAAKkB,SAAS,EAAC;UAA2E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjGvB,OAAA;YAAMkB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,GACJkF,QAAQ,CAACV,MAAM,KAAK,CAAC,gBACvB/F,OAAA;UAAKkB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAGkB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,gBAENvB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBsF,QAAQ,CAACT,GAAG,CAAEzC,OAAO,iBACpBvD,OAAA;YAAsBkB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACpEnB,OAAA;cAAKkB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CnB,OAAA;gBAAMkB,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAAC,GAC/C,EAAC,IAAIiG,IAAI,CAAC7D,OAAO,CAAC8D,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAC,GACrD;cAAA;gBAAAlG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPvB,OAAA;gBAAMkB,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,GACzDoC,OAAO,CAACgE,OAAO,EAAC,GACnB;cAAA;gBAAAnG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNvB,OAAA;cAAGkB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEoC,OAAO,CAACF;YAAQ;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAT/DgC,OAAO,CAACiE,EAAE;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEnB,OAAA;QAAMyH,QAAQ,EAAEP,iBAAkB;QAAChG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3DnB,OAAA;UACE0F,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEqB,UAAW;UAClBpB,QAAQ,EAAGC,CAAC,IAAKoB,aAAa,CAACpB,CAAC,CAAChC,MAAM,CAAC8B,KAAK,CAAE;UAC/CG,WAAW,EAAC,0BAA0B;UACtC5E,SAAS,EAAC,wJAAwJ;UAClKuE,QAAQ,EAAEqB;QAAU;UAAA1F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACFvB,OAAA;UACE0F,IAAI,EAAC,QAAQ;UACbD,QAAQ,EAAE,CAACuB,UAAU,CAACvD,IAAI,CAAC,CAAC,IAAIqD,SAAU;UAC1C5F,SAAS,EAAE,sDACT,CAAC8F,UAAU,CAACvD,IAAI,CAAC,CAAC,IAAIqD,SAAS,GAC3B,8CAA8C,GAC9C,oDAAoD,EACvD;UAAA3F,QAAA,EAEF2F,SAAS,GAAG,KAAK,GAAG;QAAQ;UAAA1F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNvB,OAAA,CAACoG,UAAU;MAACC,WAAW,EAAC;IAAM;MAAAjF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9B,CAAC;AAEV,CAAC;;AAED;AAAAiF,GAAA,CAtGMD,UAAoB;AAAAmB,GAAA,GAApBnB,UAAoB;AAuG1B,MAAMoB,gBAA0B,GAAGA,CAAA,KAAM;EACvC,MAAMC,WAAW,GAAGjG,MAAM,CAACC,QAAQ,CAACiG,QAAQ;EAE5C,oBACE7H,OAAA;IAAKkB,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BnB,OAAA;MAAKkB,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eAC/DnB,OAAA;QAAKkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnB,OAAA;UAAKkB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CnB,OAAA;YAAKkB,SAAS,EAAC,wEAAwE;YAAAC,QAAA,eACrFnB,OAAA;cAAMkB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNvB,OAAA;YAAAmB,QAAA,gBACEnB,OAAA;cAAIkB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDvB,OAAA;cAAGkB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvB,OAAA;UAAKkB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CnB,OAAA;YAAKkB,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrCvB,OAAA;YAAMkB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DnB,OAAA;QAAKkB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBnB,OAAA;UACE6B,IAAI,EAAC,OAAO;UACZX,SAAS,EAAE,mEACT0G,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,QAAQ,GAC/C,6DAA6D,GAC7D,+DAA+D,EAClE;UAAAzG,QAAA,gBAEHnB,OAAA;YAAKkB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCvB,OAAA;YAAAmB,QAAA,EAAK;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACJvB,OAAA;UACE6B,IAAI,EAAC,eAAe;UACpBX,SAAS,EAAE,mEACT0G,WAAW,CAACE,QAAQ,CAAC,UAAU,CAAC,GAC5B,6DAA6D,GAC7D,+DAA+D,EAClE;UAAA3G,QAAA,gBAEHnB,OAAA;YAAKkB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCvB,OAAA;YAAAmB,QAAA,EAAK;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACJvB,OAAA;UACE6B,IAAI,EAAC,YAAY;UACjBX,SAAS,EAAE,mEACT0G,WAAW,CAACE,QAAQ,CAAC,OAAO,CAAC,GACzB,6DAA6D,GAC7D,+DAA+D,EAClE;UAAA3G,QAAA,gBAEHnB,OAAA;YAAKkB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCvB,OAAA;YAAAmB,QAAA,EAAK;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAwG,GAAA,GAlEMJ,gBAA0B;AAmEhC,MAAMK,cAAwB,GAAGA,CAAA,KAAM;EACrCrH,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAE/C,oBACEZ,OAAA;IAAKkB,SAAS,EAAC,qCAAqC;IAAAC,QAAA,eAClDnB,OAAA,CAACH,MAAM;MAAAsB,QAAA,gBACLnB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,GAAG;QAACC,OAAO,eAAElI,OAAA,CAACmI,YAAY;UAAA/G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,UAAU;QAACC,OAAO,eAAElI,OAAA,CAACoI,WAAW;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnDvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,UAAU;QAACC,OAAO,eAAElI,OAAA,CAACqI,WAAW;UAAAjH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnDvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,OAAO;QAACC,OAAO,eAAElI,OAAA,CAACuG,UAAU;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,OAAO;QAACC,OAAO,eAAElI,OAAA,CAACsI,QAAQ;UAAAlH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,WAAW;QAACC,OAAO,eAAElI,OAAA,CAACuI,YAAY;UAAAnH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,WAAW;QAACC,OAAO,eAAElI,OAAA,CAACwI,WAAW;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpDvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,OAAO;QAACC,OAAO,eAAElI,OAAA,CAACyI,QAAQ;UAAArH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,UAAU;QAACC,OAAO,eAAElI,OAAA,CAAC0I,WAAW;UAAAtH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnDvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,OAAO;QAACC,OAAO,eAAElI,OAAA,CAAC2I,QAAQ;UAAAvH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,SAAS;QAACC,OAAO,eAAElI,OAAA,CAAC4I,UAAU;UAAAxH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,SAAS;QAACC,OAAO,eAAElI,OAAA,CAAC6I,UAAU;UAAAzH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,WAAW;QAACC,OAAO,eAAElI,OAAA,CAAC8I,YAAY;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,OAAO;QAACC,OAAO,eAAElI,OAAA,CAAC+I,QAAQ;UAAA3H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CvB,OAAA,CAACF,KAAK;QAACmI,IAAI,EAAC,GAAG;QAACC,OAAO,eAAElI,OAAA,CAACmI,YAAY;UAAA/G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACyH,GAAA,GAxBIhB,cAAwB;AA0B9B,eAAeA,cAAc;AAAC,IAAAlG,EAAA,EAAAwE,GAAA,EAAAoB,GAAA,EAAAK,GAAA,EAAAiB,GAAA;AAAAC,YAAA,CAAAnH,EAAA;AAAAmH,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}