[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\HackGamePage.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\hackGameStore.ts": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameSetupPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameHomePage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameAppsPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\game\\MobilePhone.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\types\\game.ts": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameService.ts": "25", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\lib\\supabase.ts": "26", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\backendService.ts": "27"}, {"size": 484, "mtime": 1752163887179, "results": "28", "hashOfConfig": "29"}, {"size": 425, "mtime": 1752128880040, "results": "30", "hashOfConfig": "29"}, {"size": 2021, "mtime": 1752200119447, "results": "31", "hashOfConfig": "29"}, {"size": 1341, "mtime": 1752165115921, "results": "32", "hashOfConfig": "29"}, {"size": 9261, "mtime": 1752128827498, "results": "33", "hashOfConfig": "29"}, {"size": 6038, "mtime": 1752128793748, "results": "34", "hashOfConfig": "29"}, {"size": 1634, "mtime": 1752128735960, "results": "35", "hashOfConfig": "29"}, {"size": 9347, "mtime": 1752129057056, "results": "36", "hashOfConfig": "29"}, {"size": 6109, "mtime": 1752165017979, "results": "37", "hashOfConfig": "29"}, {"size": 1872, "mtime": 1752162499900, "results": "38", "hashOfConfig": "29"}, {"size": 2725, "mtime": 1752162531754, "results": "39", "hashOfConfig": "29"}, {"size": 4039, "mtime": 1752163438286, "results": "40", "hashOfConfig": "29"}, {"size": 3178, "mtime": 1752165557372, "results": "41", "hashOfConfig": "29"}, {"size": 7659, "mtime": 1752166283822, "results": "42", "hashOfConfig": "29"}, {"size": 5631, "mtime": 1752166226242, "results": "43", "hashOfConfig": "29"}, {"size": 6228, "mtime": 1752196918984, "results": "44", "hashOfConfig": "29"}, {"size": 10935, "mtime": 1752198397095, "results": "45", "hashOfConfig": "29"}, {"size": 5320, "mtime": 1752203057968, "results": "46", "hashOfConfig": "29"}, {"size": 9769, "mtime": 1752204997391, "results": "47", "hashOfConfig": "29"}, {"size": 6304, "mtime": 1752201767860, "results": "48", "hashOfConfig": "29"}, {"size": 8874, "mtime": 1752204630774, "results": "49", "hashOfConfig": "29"}, {"size": 6141, "mtime": 1752204581296, "results": "50", "hashOfConfig": "29"}, {"size": 3833, "mtime": 1752201285318, "results": "51", "hashOfConfig": "29"}, {"size": 6885, "mtime": 1752204425014, "results": "52", "hashOfConfig": "29"}, {"size": 8882, "mtime": 1752201509264, "results": "53", "hashOfConfig": "29"}, {"size": 3092, "mtime": 1752201464175, "results": "54", "hashOfConfig": "29"}, {"size": 7985, "mtime": 1752204930861, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qlzb5l", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx", ["137"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\HackGamePage.tsx", ["138"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\hackGameStore.ts", ["139", "140", "141"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameSetupPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameHomePage.tsx", ["142", "143"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameAppsPage.tsx", ["144", "145"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\game\\MobilePhone.tsx", ["146"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\types\\game.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameService.ts", ["147"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\backendService.ts", ["148"], [], {"ruleId": "149", "severity": 1, "message": "150", "line": 1, "column": 17, "nodeType": "151", "messageId": "152", "endLine": 1, "endColumn": 26}, {"ruleId": "149", "severity": 1, "message": "153", "line": 10, "column": 34, "nodeType": "151", "messageId": "152", "endLine": 10, "endColumn": 50}, {"ruleId": "149", "severity": 1, "message": "154", "line": 5, "column": 3, "nodeType": "151", "messageId": "152", "endLine": 5, "endColumn": 17}, {"ruleId": "149", "severity": 1, "message": "155", "line": 8, "column": 3, "nodeType": "151", "messageId": "152", "endLine": 8, "endColumn": 20}, {"ruleId": "149", "severity": 1, "message": "156", "line": 15, "column": 26, "nodeType": "151", "messageId": "152", "endLine": 15, "endColumn": 39}, {"ruleId": "149", "severity": 1, "message": "157", "line": 3, "column": 10, "nodeType": "151", "messageId": "152", "endLine": 3, "endColumn": 19}, {"ruleId": "149", "severity": 1, "message": "158", "line": 9, "column": 5, "nodeType": "151", "messageId": "152", "endLine": 9, "endColumn": 18}, {"ruleId": "149", "severity": 1, "message": "159", "line": 3, "column": 21, "nodeType": "151", "messageId": "152", "endLine": 3, "endColumn": 32}, {"ruleId": "149", "severity": 1, "message": "160", "line": 39, "column": 11, "nodeType": "151", "messageId": "152", "endLine": 39, "endColumn": 21}, {"ruleId": "149", "severity": 1, "message": "161", "line": 9, "column": 11, "nodeType": "151", "messageId": "152", "endLine": 9, "endColumn": 17}, {"ruleId": "162", "severity": 1, "message": "163", "line": 331, "column": 1, "nodeType": "164", "endLine": 331, "endColumn": 34}, {"ruleId": "149", "severity": 1, "message": "165", "line": 223, "column": 13, "nodeType": "151", "messageId": "152", "endLine": 223, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'setCurrentScreen' is assigned a value but never used.", "'getPlayerLevel' is defined but never used.", "'calculateXpReward' is defined but never used.", "'BackendPlayer' is defined but never used.", "'GAME_APPS' is defined but never used.", "'currentScreen' is assigned a value but never used.", "'GAME_CONFIG' is defined but never used.", "'isMaxLevel' is assigned a value but never used.", "'player' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "'response' is assigned a value but never used."]