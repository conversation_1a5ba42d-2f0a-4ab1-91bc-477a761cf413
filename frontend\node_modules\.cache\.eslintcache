[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\HackGamePage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\hackGameStore.ts": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameSetupPage.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameHomePage.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameAppsPage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\game\\MobilePhone.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\types\\game.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameService.ts": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\lib\\supabase.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\backendService.ts": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\DebugPanel.tsx": "25"}, {"size": 484, "mtime": 1752163887179, "results": "26", "hashOfConfig": "27"}, {"size": 425, "mtime": 1752128880040, "results": "28", "hashOfConfig": "27"}, {"size": 1838, "mtime": 1752229169948, "results": "29", "hashOfConfig": "27"}, {"size": 1634, "mtime": 1752128735960, "results": "30", "hashOfConfig": "27"}, {"size": 9347, "mtime": 1752129057056, "results": "31", "hashOfConfig": "27"}, {"size": 6109, "mtime": 1752165017979, "results": "32", "hashOfConfig": "27"}, {"size": 1872, "mtime": 1752162499900, "results": "33", "hashOfConfig": "27"}, {"size": 2725, "mtime": 1752162531754, "results": "34", "hashOfConfig": "27"}, {"size": 4039, "mtime": 1752163438286, "results": "35", "hashOfConfig": "27"}, {"size": 3178, "mtime": 1752165557372, "results": "36", "hashOfConfig": "27"}, {"size": 9044, "mtime": 1752230363723, "results": "37", "hashOfConfig": "27"}, {"size": 7097, "mtime": 1752230112665, "results": "38", "hashOfConfig": "27"}, {"size": 6228, "mtime": 1752196918984, "results": "39", "hashOfConfig": "27"}, {"size": 10935, "mtime": 1752198397095, "results": "40", "hashOfConfig": "27"}, {"size": 6224, "mtime": 1752230470068, "results": "41", "hashOfConfig": "27"}, {"size": 10703, "mtime": 1752224820402, "results": "42", "hashOfConfig": "27"}, {"size": 6304, "mtime": 1752201767860, "results": "43", "hashOfConfig": "27"}, {"size": 8874, "mtime": 1752204630774, "results": "44", "hashOfConfig": "27"}, {"size": 6141, "mtime": 1752204581296, "results": "45", "hashOfConfig": "27"}, {"size": 3833, "mtime": 1752201285318, "results": "46", "hashOfConfig": "27"}, {"size": 6885, "mtime": 1752204425014, "results": "47", "hashOfConfig": "27"}, {"size": 8882, "mtime": 1752201509264, "results": "48", "hashOfConfig": "27"}, {"size": 3092, "mtime": 1752201464175, "results": "49", "hashOfConfig": "27"}, {"size": 16475, "mtime": 1752248919226, "results": "50", "hashOfConfig": "27"}, {"size": 2570, "mtime": 1752230421114, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qlzb5l", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx", ["127"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\HackGamePage.tsx", ["128"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\hackGameStore.ts", ["129", "130", "131"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameSetupPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameHomePage.tsx", ["132", "133"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameAppsPage.tsx", ["134", "135"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\game\\MobilePhone.tsx", ["136"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\types\\game.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameService.ts", ["137"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\backendService.ts", ["138"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\DebugPanel.tsx", [], [], {"ruleId": "139", "severity": 1, "message": "140", "line": 70, "column": 9, "nodeType": "141", "messageId": "142", "endLine": 70, "endColumn": 19}, {"ruleId": "139", "severity": 1, "message": "143", "line": 11, "column": 34, "nodeType": "141", "messageId": "142", "endLine": 11, "endColumn": 50}, {"ruleId": "139", "severity": 1, "message": "144", "line": 5, "column": 3, "nodeType": "141", "messageId": "142", "endLine": 5, "endColumn": 17}, {"ruleId": "139", "severity": 1, "message": "145", "line": 8, "column": 3, "nodeType": "141", "messageId": "142", "endLine": 8, "endColumn": 20}, {"ruleId": "139", "severity": 1, "message": "146", "line": 15, "column": 26, "nodeType": "141", "messageId": "142", "endLine": 15, "endColumn": 39}, {"ruleId": "139", "severity": 1, "message": "147", "line": 3, "column": 10, "nodeType": "141", "messageId": "142", "endLine": 3, "endColumn": 19}, {"ruleId": "139", "severity": 1, "message": "148", "line": 9, "column": 5, "nodeType": "141", "messageId": "142", "endLine": 9, "endColumn": 18}, {"ruleId": "139", "severity": 1, "message": "149", "line": 3, "column": 21, "nodeType": "141", "messageId": "142", "endLine": 3, "endColumn": 32}, {"ruleId": "139", "severity": 1, "message": "150", "line": 39, "column": 11, "nodeType": "141", "messageId": "142", "endLine": 39, "endColumn": 21}, {"ruleId": "139", "severity": 1, "message": "151", "line": 9, "column": 11, "nodeType": "141", "messageId": "142", "endLine": 9, "endColumn": 17}, {"ruleId": "152", "severity": 1, "message": "153", "line": 331, "column": 1, "nodeType": "154", "endLine": 331, "endColumn": 34}, {"ruleId": "139", "severity": 1, "message": "155", "line": 346, "column": 13, "nodeType": "141", "messageId": "142", "endLine": 346, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'toggleMode' is assigned a value but never used.", "Identifier", "unusedVar", "'setCurrentScreen' is assigned a value but never used.", "'getPlayerLevel' is defined but never used.", "'calculateXpReward' is defined but never used.", "'BackendPlayer' is defined but never used.", "'GAME_APPS' is defined but never used.", "'currentScreen' is assigned a value but never used.", "'GAME_CONFIG' is defined but never used.", "'isMaxLevel' is assigned a value but never used.", "'player' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "'response' is assigned a value but never used."]