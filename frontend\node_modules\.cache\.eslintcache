[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts": "28", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameMainPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TransferPage.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\UpgradePage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\RankingPage.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ShopPage.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LogsPage.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ConfigPage.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\MiningPage.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TerminalPage.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\BankPage.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\GameFooter.tsx": "43"}, {"size": 484, "mtime": *************, "results": "44", "hashOfConfig": "45"}, {"size": 425, "mtime": *************, "results": "46", "hashOfConfig": "45"}, {"size": 2027, "mtime": *************, "results": "47", "hashOfConfig": "45"}, {"size": 2266, "mtime": *************, "results": "48", "hashOfConfig": "45"}, {"size": 10762, "mtime": *************, "results": "49", "hashOfConfig": "45"}, {"size": 2435, "mtime": *************, "results": "50", "hashOfConfig": "45"}, {"size": 1341, "mtime": *************, "results": "51", "hashOfConfig": "45"}, {"size": 9261, "mtime": *************, "results": "52", "hashOfConfig": "45"}, {"size": 6038, "mtime": *************, "results": "53", "hashOfConfig": "45"}, {"size": 1634, "mtime": *************, "results": "54", "hashOfConfig": "45"}, {"size": 7878, "mtime": *************, "results": "55", "hashOfConfig": "45"}, {"size": 6408, "mtime": 1752128690546, "results": "56", "hashOfConfig": "45"}, {"size": 6815, "mtime": 1752166912159, "results": "57", "hashOfConfig": "45"}, {"size": 9347, "mtime": 1752129057056, "results": "58", "hashOfConfig": "45"}, {"size": 5606, "mtime": 1752131581102, "results": "59", "hashOfConfig": "45"}, {"size": 2841, "mtime": 1752129361158, "results": "60", "hashOfConfig": "45"}, {"size": 9382, "mtime": 1752131549084, "results": "61", "hashOfConfig": "45"}, {"size": 8066, "mtime": 1752131568683, "results": "62", "hashOfConfig": "45"}, {"size": 9580, "mtime": 1752129162226, "results": "63", "hashOfConfig": "45"}, {"size": 5543, "mtime": 1752129239339, "results": "64", "hashOfConfig": "45"}, {"size": 6109, "mtime": 1752165017979, "results": "65", "hashOfConfig": "45"}, {"size": 1872, "mtime": 1752162499900, "results": "66", "hashOfConfig": "45"}, {"size": 2725, "mtime": 1752162531754, "results": "67", "hashOfConfig": "45"}, {"size": 4039, "mtime": 1752163438286, "results": "68", "hashOfConfig": "45"}, {"size": 6737, "mtime": 1752163755002, "results": "69", "hashOfConfig": "45"}, {"size": 3178, "mtime": 1752165557372, "results": "70", "hashOfConfig": "45"}, {"size": 7659, "mtime": 1752166283822, "results": "71", "hashOfConfig": "45"}, {"size": 5631, "mtime": 1752166226242, "results": "72", "hashOfConfig": "45"}, {"size": 26970, "mtime": 1752194615907, "results": "73", "hashOfConfig": "45"}, {"size": 6104, "mtime": 1752168427083, "results": "74", "hashOfConfig": "45"}, {"size": 2548, "mtime": 1752166449807, "results": "75", "hashOfConfig": "45"}, {"size": 10365, "mtime": 1752174059882, "results": "76", "hashOfConfig": "45"}, {"size": 6831, "mtime": 1752194441204, "results": "77", "hashOfConfig": "45"}, {"size": 13254, "mtime": 1752173730684, "results": "78", "hashOfConfig": "45"}, {"size": 10991, "mtime": 1752172632580, "results": "79", "hashOfConfig": "45"}, {"size": 10954, "mtime": 1752172838857, "results": "80", "hashOfConfig": "45"}, {"size": 12582, "mtime": 1752172793775, "results": "81", "hashOfConfig": "45"}, {"size": 11154, "mtime": 1752172977498, "results": "82", "hashOfConfig": "45"}, {"size": 13603, "mtime": 1752194556779, "results": "83", "hashOfConfig": "45"}, {"size": 13321, "mtime": 1752173785553, "results": "84", "hashOfConfig": "45"}, {"size": 10835, "mtime": 1752194508090, "results": "85", "hashOfConfig": "45"}, {"size": 18872, "mtime": 1752174046879, "results": "86", "hashOfConfig": "45"}, {"size": 3074, "mtime": 1752194412149, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qlzb5l", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx", ["217"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx", ["218", "219", "220"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameMainPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TransferPage.tsx", ["221", "222", "223", "224"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\UpgradePage.tsx", ["225", "226", "227", "228"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\RankingPage.tsx", ["229", "230"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ShopPage.tsx", ["231", "232"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LogsPage.tsx", ["233", "234", "235"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ConfigPage.tsx", ["236", "237", "238"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\MiningPage.tsx", ["239", "240"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TerminalPage.tsx", ["241"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\BankPage.tsx", ["242", "243"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\GameFooter.tsx", [], [], {"ruleId": "244", "severity": 1, "message": "245", "line": 1, "column": 17, "nodeType": "246", "messageId": "247", "endLine": 1, "endColumn": 26}, {"ruleId": "244", "severity": 1, "message": "248", "line": 20, "column": 7, "nodeType": "246", "messageId": "247", "endLine": 20, "endColumn": 32}, {"ruleId": "244", "severity": 1, "message": "249", "line": 178, "column": 11, "nodeType": "246", "messageId": "247", "endLine": 178, "endColumn": 15}, {"ruleId": "244", "severity": 1, "message": "250", "line": 578, "column": 7, "nodeType": "246", "messageId": "247", "endLine": 578, "endColumn": 33}, {"ruleId": "244", "severity": 1, "message": "249", "line": 7, "column": 11, "nodeType": "246", "messageId": "247", "endLine": 7, "endColumn": 15}, {"ruleId": "244", "severity": 1, "message": "251", "line": 25, "column": 10, "nodeType": "246", "messageId": "247", "endLine": 25, "endColumn": 30}, {"ruleId": "244", "severity": 1, "message": "252", "line": 28, "column": 10, "nodeType": "246", "messageId": "247", "endLine": 28, "endColumn": 25}, {"ruleId": "244", "severity": 1, "message": "253", "line": 29, "column": 10, "nodeType": "246", "messageId": "247", "endLine": 29, "endColumn": 26}, {"ruleId": "244", "severity": 1, "message": "249", "line": 20, "column": 11, "nodeType": "246", "messageId": "247", "endLine": 20, "endColumn": 15}, {"ruleId": "244", "severity": 1, "message": "254", "line": 24, "column": 10, "nodeType": "246", "messageId": "247", "endLine": 24, "endColumn": 19}, {"ruleId": "244", "severity": 1, "message": "255", "line": 24, "column": 21, "nodeType": "246", "messageId": "247", "endLine": 24, "endColumn": 33}, {"ruleId": "256", "severity": 1, "message": "257", "line": 33, "column": 6, "nodeType": "258", "endLine": 33, "endColumn": 38, "suggestions": "259"}, {"ruleId": "244", "severity": 1, "message": "249", "line": 18, "column": 11, "nodeType": "246", "messageId": "247", "endLine": 18, "endColumn": 15}, {"ruleId": "256", "severity": 1, "message": "260", "line": 30, "column": 6, "nodeType": "258", "endLine": 30, "endColumn": 41, "suggestions": "261"}, {"ruleId": "244", "severity": 1, "message": "249", "line": 21, "column": 11, "nodeType": "246", "messageId": "247", "endLine": 21, "endColumn": 15}, {"ruleId": "256", "severity": 1, "message": "262", "line": 35, "column": 6, "nodeType": "258", "endLine": 35, "endColumn": 23, "suggestions": "263"}, {"ruleId": "244", "severity": 1, "message": "249", "line": 17, "column": 11, "nodeType": "246", "messageId": "247", "endLine": 17, "endColumn": 15}, {"ruleId": "244", "severity": 1, "message": "264", "line": 18, "column": 11, "nodeType": "246", "messageId": "247", "endLine": 18, "endColumn": 24}, {"ruleId": "256", "severity": 1, "message": "265", "line": 28, "column": 6, "nodeType": "258", "endLine": 28, "endColumn": 41, "suggestions": "266"}, {"ruleId": "244", "severity": 1, "message": "267", "line": 9, "column": 26, "nodeType": "246", "messageId": "247", "endLine": 9, "endColumn": 40}, {"ruleId": "268", "severity": 2, "message": "269", "line": 97, "column": 9, "nodeType": "246", "messageId": "270", "endLine": 97, "endColumn": 16}, {"ruleId": "268", "severity": 2, "message": "269", "line": 104, "column": 9, "nodeType": "246", "messageId": "270", "endLine": 104, "endColumn": 16}, {"ruleId": "244", "severity": 1, "message": "249", "line": 19, "column": 11, "nodeType": "246", "messageId": "247", "endLine": 19, "endColumn": 15}, {"ruleId": "256", "severity": 1, "message": "271", "line": 43, "column": 6, "nodeType": "258", "endLine": 43, "endColumn": 23, "suggestions": "272"}, {"ruleId": "256", "severity": 1, "message": "273", "line": 31, "column": 6, "nodeType": "258", "endLine": 31, "endColumn": 23, "suggestions": "274"}, {"ruleId": "244", "severity": 1, "message": "249", "line": 26, "column": 11, "nodeType": "246", "messageId": "247", "endLine": 26, "endColumn": 15}, {"ruleId": "256", "severity": 1, "message": "275", "line": 47, "column": 6, "nodeType": "258", "endLine": 47, "endColumn": 23, "suggestions": "276"}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'SimpleDashboard' is assigned a value but never used.", "'user' is assigned a value but never used.", "'SimpleNavigation' is assigned a value but never used.", "'isLoadingConnections' is assigned a value but never used.", "'transferHistory' is assigned a value but never used.", "'isLoadingHistory' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUpgradeItems'. Either include it or remove the dependency array.", "ArrayExpression", ["277"], "React Hook useEffect has a missing dependency: 'loadRanking'. Either include it or remove the dependency array.", ["278"], "React Hook useEffect has a missing dependency: 'loadShopItems'. Either include it or remove the dependency array.", ["279"], "'currentPlayer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadLogs'. Either include it or remove the dependency array.", ["280"], "'loadPlayerData' is assigned a value but never used.", "no-restricted-globals", "Unexpected use of 'confirm'.", "defaultMessage", "React Hook useEffect has a missing dependency: 'loadMiningStatus'. Either include it or remove the dependency array.", ["281"], "React Hook useEffect has a missing dependency: 'initializeTerminal'. Either include it or remove the dependency array.", ["282"], "React Hook useEffect has missing dependencies: 'loadBankData' and 'loadTransactions'. Either include them or remove the dependency array.", ["283"], {"desc": "284", "fix": "285"}, {"desc": "286", "fix": "287"}, {"desc": "288", "fix": "289"}, {"desc": "290", "fix": "291"}, {"desc": "292", "fix": "293"}, {"desc": "294", "fix": "295"}, {"desc": "296", "fix": "297"}, "Update the dependencies array to be: [isAuthenticated, currentPlayer, loadUpgradeItems]", {"range": "298", "text": "299"}, "Update the dependencies array to be: [isAuthenticated, loadRanking, selectedCategory]", {"range": "300", "text": "301"}, "Update the dependencies array to be: [isAuthenticated, loadShopItems]", {"range": "302", "text": "303"}, "Update the dependencies array to be: [isAuthenticated, loadLogs, selectedCategory]", {"range": "304", "text": "305"}, "Update the dependencies array to be: [isAuthenticated, loadMiningStatus]", {"range": "306", "text": "307"}, "Update the dependencies array to be: [initializeTerminal, isAuthenticated]", {"range": "308", "text": "309"}, "Update the dependencies array to be: [isAuthenticated, loadBankData, loadTransactions]", {"range": "310", "text": "311"}, [997, 1029], "[isAuthenticated, currentPlayer, loadUpgradeItems]", [874, 909], "[isAuthenticated, loadRanking, selectedCategory]", [1096, 1113], "[isAuthenticated, loadShopItems]", [798, 833], "[isAuthenticated, loadLogs, selectedCategory]", [1328, 1345], "[isAuthenticated, loadMiningStatus]", [1000, 1017], "[initializeTerminal, isAuthenticated]", [1433, 1450], "[isAuthenticated, loadBankData, loadTransactions]"]