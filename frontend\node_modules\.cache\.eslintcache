[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts": "28", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts": "31"}, {"size": 484, "mtime": 1752163887179, "results": "32", "hashOfConfig": "33"}, {"size": 425, "mtime": 1752128880040, "results": "34", "hashOfConfig": "33"}, {"size": 2027, "mtime": 1752165973318, "results": "35", "hashOfConfig": "33"}, {"size": 2266, "mtime": 1752131595650, "results": "36", "hashOfConfig": "33"}, {"size": 10762, "mtime": 1752162425065, "results": "37", "hashOfConfig": "33"}, {"size": 2435, "mtime": 1752128722947, "results": "38", "hashOfConfig": "33"}, {"size": 1341, "mtime": 1752165115921, "results": "39", "hashOfConfig": "33"}, {"size": 9261, "mtime": 1752128827498, "results": "40", "hashOfConfig": "33"}, {"size": 6038, "mtime": 1752128793748, "results": "41", "hashOfConfig": "33"}, {"size": 1634, "mtime": 1752128735960, "results": "42", "hashOfConfig": "33"}, {"size": 5702, "mtime": 1752165061752, "results": "43", "hashOfConfig": "33"}, {"size": 6408, "mtime": 1752128690546, "results": "44", "hashOfConfig": "33"}, {"size": 6368, "mtime": 1752166536196, "results": "45", "hashOfConfig": "33"}, {"size": 9347, "mtime": 1752129057056, "results": "46", "hashOfConfig": "33"}, {"size": 5606, "mtime": 1752131581102, "results": "47", "hashOfConfig": "33"}, {"size": 2841, "mtime": 1752129361158, "results": "48", "hashOfConfig": "33"}, {"size": 9382, "mtime": 1752131549084, "results": "49", "hashOfConfig": "33"}, {"size": 8066, "mtime": 1752131568683, "results": "50", "hashOfConfig": "33"}, {"size": 9580, "mtime": 1752129162226, "results": "51", "hashOfConfig": "33"}, {"size": 5543, "mtime": 1752129239339, "results": "52", "hashOfConfig": "33"}, {"size": 6109, "mtime": 1752165017979, "results": "53", "hashOfConfig": "33"}, {"size": 1872, "mtime": 1752162499900, "results": "54", "hashOfConfig": "33"}, {"size": 2725, "mtime": 1752162531754, "results": "55", "hashOfConfig": "33"}, {"size": 4039, "mtime": 1752163438286, "results": "56", "hashOfConfig": "33"}, {"size": 6737, "mtime": 1752163755002, "results": "57", "hashOfConfig": "33"}, {"size": 3178, "mtime": 1752165557372, "results": "58", "hashOfConfig": "33"}, {"size": 7659, "mtime": 1752166283822, "results": "59", "hashOfConfig": "33"}, {"size": 5631, "mtime": 1752166226242, "results": "60", "hashOfConfig": "33"}, {"size": 11775, "mtime": 1752166752775, "results": "61", "hashOfConfig": "33"}, {"size": 5648, "mtime": 1752166153514, "results": "62", "hashOfConfig": "33"}, {"size": 2548, "mtime": 1752166449807, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qlzb5l", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx", ["157"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts", [], [], {"ruleId": "158", "severity": 1, "message": "159", "line": 1, "column": 17, "nodeType": "160", "messageId": "161", "endLine": 1, "endColumn": 26}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar"]