[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts": "28", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameMainPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TransferPage.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\UpgradePage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\RankingPage.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ShopPage.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LogsPage.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ConfigPage.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\MiningPage.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TerminalPage.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\BankPage.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\GameFooter.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\GameIcons.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\PlayerProfile.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\AppsPage.tsx": "46"}, {"size": 484, "mtime": *************, "results": "47", "hashOfConfig": "48"}, {"size": 425, "mtime": *************, "results": "49", "hashOfConfig": "48"}, {"size": 2027, "mtime": *************, "results": "50", "hashOfConfig": "48"}, {"size": 2266, "mtime": *************, "results": "51", "hashOfConfig": "48"}, {"size": 10762, "mtime": *************, "results": "52", "hashOfConfig": "48"}, {"size": 2435, "mtime": *************, "results": "53", "hashOfConfig": "48"}, {"size": 1341, "mtime": 1752165115921, "results": "54", "hashOfConfig": "48"}, {"size": 9261, "mtime": 1752128827498, "results": "55", "hashOfConfig": "48"}, {"size": 6038, "mtime": 1752128793748, "results": "56", "hashOfConfig": "48"}, {"size": 1634, "mtime": 1752128735960, "results": "57", "hashOfConfig": "48"}, {"size": 7878, "mtime": 1752172522843, "results": "58", "hashOfConfig": "48"}, {"size": 6408, "mtime": 1752128690546, "results": "59", "hashOfConfig": "48"}, {"size": 6815, "mtime": 1752166912159, "results": "60", "hashOfConfig": "48"}, {"size": 9347, "mtime": 1752129057056, "results": "61", "hashOfConfig": "48"}, {"size": 5606, "mtime": 1752131581102, "results": "62", "hashOfConfig": "48"}, {"size": 2841, "mtime": 1752129361158, "results": "63", "hashOfConfig": "48"}, {"size": 9382, "mtime": 1752131549084, "results": "64", "hashOfConfig": "48"}, {"size": 8066, "mtime": 1752131568683, "results": "65", "hashOfConfig": "48"}, {"size": 9580, "mtime": 1752129162226, "results": "66", "hashOfConfig": "48"}, {"size": 5543, "mtime": 1752129239339, "results": "67", "hashOfConfig": "48"}, {"size": 6109, "mtime": 1752165017979, "results": "68", "hashOfConfig": "48"}, {"size": 1872, "mtime": 1752162499900, "results": "69", "hashOfConfig": "48"}, {"size": 2725, "mtime": 1752162531754, "results": "70", "hashOfConfig": "48"}, {"size": 4039, "mtime": 1752163438286, "results": "71", "hashOfConfig": "48"}, {"size": 6737, "mtime": 1752163755002, "results": "72", "hashOfConfig": "48"}, {"size": 3178, "mtime": 1752165557372, "results": "73", "hashOfConfig": "48"}, {"size": 7659, "mtime": 1752166283822, "results": "74", "hashOfConfig": "48"}, {"size": 5631, "mtime": 1752166226242, "results": "75", "hashOfConfig": "48"}, {"size": 27059, "mtime": 1752196476550, "results": "76", "hashOfConfig": "48"}, {"size": 6104, "mtime": 1752168427083, "results": "77", "hashOfConfig": "48"}, {"size": 2548, "mtime": 1752166449807, "results": "78", "hashOfConfig": "48"}, {"size": 10365, "mtime": 1752174059882, "results": "79", "hashOfConfig": "48"}, {"size": 11675, "mtime": 1752196389023, "results": "80", "hashOfConfig": "48"}, {"size": 13254, "mtime": 1752173730684, "results": "81", "hashOfConfig": "48"}, {"size": 10991, "mtime": 1752172632580, "results": "82", "hashOfConfig": "48"}, {"size": 10954, "mtime": 1752172838857, "results": "83", "hashOfConfig": "48"}, {"size": 12582, "mtime": 1752172793775, "results": "84", "hashOfConfig": "48"}, {"size": 11154, "mtime": 1752172977498, "results": "85", "hashOfConfig": "48"}, {"size": 13603, "mtime": 1752194556779, "results": "86", "hashOfConfig": "48"}, {"size": 13321, "mtime": 1752173785553, "results": "87", "hashOfConfig": "48"}, {"size": 10835, "mtime": 1752194508090, "results": "88", "hashOfConfig": "48"}, {"size": 18872, "mtime": 1752174046879, "results": "89", "hashOfConfig": "48"}, {"size": 3533, "mtime": 1752196493957, "results": "90", "hashOfConfig": "48"}, {"size": 6802, "mtime": 1752195435739, "results": "91", "hashOfConfig": "48"}, {"size": 4156, "mtime": 1752196350024, "results": "92", "hashOfConfig": "48"}, {"size": 6422, "mtime": 1752196430644, "results": "93", "hashOfConfig": "48"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qlzb5l", {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx", ["232"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx", ["233", "234", "235"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameMainPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TransferPage.tsx", ["236", "237", "238", "239"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\UpgradePage.tsx", ["240", "241", "242", "243"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\RankingPage.tsx", ["244", "245"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ShopPage.tsx", ["246", "247"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LogsPage.tsx", ["248", "249", "250"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ConfigPage.tsx", ["251", "252", "253"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\MiningPage.tsx", ["254", "255"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TerminalPage.tsx", ["256"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\BankPage.tsx", ["257", "258"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\GameFooter.tsx", ["259", "260"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\GameIcons.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\PlayerProfile.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\AppsPage.tsx", [], [], {"ruleId": "261", "severity": 1, "message": "262", "line": 1, "column": 17, "nodeType": "263", "messageId": "264", "endLine": 1, "endColumn": 26}, {"ruleId": "261", "severity": 1, "message": "265", "line": 21, "column": 7, "nodeType": "263", "messageId": "264", "endLine": 21, "endColumn": 32}, {"ruleId": "261", "severity": 1, "message": "266", "line": 179, "column": 11, "nodeType": "263", "messageId": "264", "endLine": 179, "endColumn": 15}, {"ruleId": "261", "severity": 1, "message": "267", "line": 579, "column": 7, "nodeType": "263", "messageId": "264", "endLine": 579, "endColumn": 33}, {"ruleId": "261", "severity": 1, "message": "266", "line": 7, "column": 11, "nodeType": "263", "messageId": "264", "endLine": 7, "endColumn": 15}, {"ruleId": "261", "severity": 1, "message": "268", "line": 25, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 25, "endColumn": 30}, {"ruleId": "261", "severity": 1, "message": "269", "line": 28, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 28, "endColumn": 25}, {"ruleId": "261", "severity": 1, "message": "270", "line": 29, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 29, "endColumn": 26}, {"ruleId": "261", "severity": 1, "message": "266", "line": 20, "column": 11, "nodeType": "263", "messageId": "264", "endLine": 20, "endColumn": 15}, {"ruleId": "261", "severity": 1, "message": "271", "line": 24, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 24, "endColumn": 19}, {"ruleId": "261", "severity": 1, "message": "272", "line": 24, "column": 21, "nodeType": "263", "messageId": "264", "endLine": 24, "endColumn": 33}, {"ruleId": "273", "severity": 1, "message": "274", "line": 33, "column": 6, "nodeType": "275", "endLine": 33, "endColumn": 38, "suggestions": "276"}, {"ruleId": "261", "severity": 1, "message": "266", "line": 18, "column": 11, "nodeType": "263", "messageId": "264", "endLine": 18, "endColumn": 15}, {"ruleId": "273", "severity": 1, "message": "277", "line": 30, "column": 6, "nodeType": "275", "endLine": 30, "endColumn": 41, "suggestions": "278"}, {"ruleId": "261", "severity": 1, "message": "266", "line": 21, "column": 11, "nodeType": "263", "messageId": "264", "endLine": 21, "endColumn": 15}, {"ruleId": "273", "severity": 1, "message": "279", "line": 35, "column": 6, "nodeType": "275", "endLine": 35, "endColumn": 23, "suggestions": "280"}, {"ruleId": "261", "severity": 1, "message": "266", "line": 17, "column": 11, "nodeType": "263", "messageId": "264", "endLine": 17, "endColumn": 15}, {"ruleId": "261", "severity": 1, "message": "281", "line": 18, "column": 11, "nodeType": "263", "messageId": "264", "endLine": 18, "endColumn": 24}, {"ruleId": "273", "severity": 1, "message": "282", "line": 28, "column": 6, "nodeType": "275", "endLine": 28, "endColumn": 41, "suggestions": "283"}, {"ruleId": "261", "severity": 1, "message": "284", "line": 9, "column": 26, "nodeType": "263", "messageId": "264", "endLine": 9, "endColumn": 40}, {"ruleId": "285", "severity": 2, "message": "286", "line": 97, "column": 9, "nodeType": "263", "messageId": "287", "endLine": 97, "endColumn": 16}, {"ruleId": "285", "severity": 2, "message": "286", "line": 104, "column": 9, "nodeType": "263", "messageId": "287", "endLine": 104, "endColumn": 16}, {"ruleId": "261", "severity": 1, "message": "266", "line": 19, "column": 11, "nodeType": "263", "messageId": "264", "endLine": 19, "endColumn": 15}, {"ruleId": "273", "severity": 1, "message": "288", "line": 43, "column": 6, "nodeType": "275", "endLine": 43, "endColumn": 23, "suggestions": "289"}, {"ruleId": "273", "severity": 1, "message": "290", "line": 31, "column": 6, "nodeType": "275", "endLine": 31, "endColumn": 23, "suggestions": "291"}, {"ruleId": "261", "severity": 1, "message": "266", "line": 26, "column": 11, "nodeType": "263", "messageId": "264", "endLine": 26, "endColumn": 15}, {"ruleId": "273", "severity": 1, "message": "292", "line": 47, "column": 6, "nodeType": "275", "endLine": 47, "endColumn": 23, "suggestions": "293"}, {"ruleId": "261", "severity": 1, "message": "294", "line": 3, "column": 20, "nodeType": "263", "messageId": "264", "endLine": 3, "endColumn": 28}, {"ruleId": "261", "severity": 1, "message": "295", "line": 3, "column": 40, "nodeType": "263", "messageId": "264", "endLine": 3, "endColumn": 52}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'SimpleDashboard' is assigned a value but never used.", "'user' is assigned a value but never used.", "'SimpleNavigation' is assigned a value but never used.", "'isLoadingConnections' is assigned a value but never used.", "'transferHistory' is assigned a value but never used.", "'isLoadingHistory' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUpgradeItems'. Either include it or remove the dependency array.", "ArrayExpression", ["296"], "React Hook useEffect has a missing dependency: 'loadRanking'. Either include it or remove the dependency array.", ["297"], "React Hook useEffect has a missing dependency: 'loadShopItems'. Either include it or remove the dependency array.", ["298"], "'currentPlayer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadLogs'. Either include it or remove the dependency array.", ["299"], "'loadPlayerData' is assigned a value but never used.", "no-restricted-globals", "Unexpected use of 'confirm'.", "defaultMessage", "React Hook useEffect has a missing dependency: 'loadMiningStatus'. Either include it or remove the dependency array.", ["300"], "React Hook useEffect has a missing dependency: 'initializeTerminal'. Either include it or remove the dependency array.", ["301"], "React Hook useEffect has missing dependencies: 'loadBankData' and 'loadTransactions'. Either include them or remove the dependency array.", ["302"], "'ChatIcon' is defined but never used.", "'TerminalIcon' is defined but never used.", {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, {"desc": "307", "fix": "308"}, {"desc": "309", "fix": "310"}, {"desc": "311", "fix": "312"}, {"desc": "313", "fix": "314"}, {"desc": "315", "fix": "316"}, "Update the dependencies array to be: [isAuthenticated, currentPlayer, loadUpgradeItems]", {"range": "317", "text": "318"}, "Update the dependencies array to be: [isAuthenticated, loadRanking, selectedCategory]", {"range": "319", "text": "320"}, "Update the dependencies array to be: [isAuthenticated, loadShopItems]", {"range": "321", "text": "322"}, "Update the dependencies array to be: [isAuthenticated, loadLogs, selectedCategory]", {"range": "323", "text": "324"}, "Update the dependencies array to be: [isAuthenticated, loadMiningStatus]", {"range": "325", "text": "326"}, "Update the dependencies array to be: [initializeTerminal, isAuthenticated]", {"range": "327", "text": "328"}, "Update the dependencies array to be: [isAuthenticated, loadBankData, loadTransactions]", {"range": "329", "text": "330"}, [997, 1029], "[isAuthenticated, currentPlayer, loadUpgradeItems]", [874, 909], "[isAuthenticated, loadRanking, selectedCategory]", [1096, 1113], "[isAuthenticated, loadShopItems]", [798, 833], "[isAuthenticated, loadLogs, selectedCategory]", [1328, 1345], "[isAuthenticated, loadMiningStatus]", [1000, 1017], "[initializeTerminal, isAuthenticated]", [1433, 1450], "[isAuthenticated, loadBankData, loadTransactions]"]