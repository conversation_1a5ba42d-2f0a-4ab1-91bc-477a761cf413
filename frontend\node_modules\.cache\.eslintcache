[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts": "28", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameMainPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TransferPage.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\UpgradePage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\RankingPage.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ShopPage.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LogsPage.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ConfigPage.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\MiningPage.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TerminalPage.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\BankPage.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\GameFooter.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\GameIcons.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\PlayerProfile.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\AppsPage.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ScannerPage.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\InvadedPage.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\hooks\\useGameSystem.ts": "49", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameService.ts": "50"}, {"size": 484, "mtime": 1752163887179, "results": "51", "hashOfConfig": "52"}, {"size": 425, "mtime": 1752128880040, "results": "53", "hashOfConfig": "52"}, {"size": 2027, "mtime": 1752165973318, "results": "54", "hashOfConfig": "52"}, {"size": 2266, "mtime": 1752131595650, "results": "55", "hashOfConfig": "52"}, {"size": 10762, "mtime": 1752162425065, "results": "56", "hashOfConfig": "52"}, {"size": 2435, "mtime": 1752128722947, "results": "57", "hashOfConfig": "52"}, {"size": 1341, "mtime": 1752165115921, "results": "58", "hashOfConfig": "52"}, {"size": 9261, "mtime": 1752128827498, "results": "59", "hashOfConfig": "52"}, {"size": 6038, "mtime": 1752128793748, "results": "60", "hashOfConfig": "52"}, {"size": 1634, "mtime": 1752128735960, "results": "61", "hashOfConfig": "52"}, {"size": 7878, "mtime": 1752172522843, "results": "62", "hashOfConfig": "52"}, {"size": 6408, "mtime": 1752128690546, "results": "63", "hashOfConfig": "52"}, {"size": 6815, "mtime": 1752166912159, "results": "64", "hashOfConfig": "52"}, {"size": 9347, "mtime": 1752129057056, "results": "65", "hashOfConfig": "52"}, {"size": 5606, "mtime": 1752131581102, "results": "66", "hashOfConfig": "52"}, {"size": 2841, "mtime": 1752129361158, "results": "67", "hashOfConfig": "52"}, {"size": 9382, "mtime": 1752131549084, "results": "68", "hashOfConfig": "52"}, {"size": 8066, "mtime": 1752131568683, "results": "69", "hashOfConfig": "52"}, {"size": 9580, "mtime": 1752129162226, "results": "70", "hashOfConfig": "52"}, {"size": 5543, "mtime": 1752129239339, "results": "71", "hashOfConfig": "52"}, {"size": 6109, "mtime": 1752165017979, "results": "72", "hashOfConfig": "52"}, {"size": 1872, "mtime": 1752162499900, "results": "73", "hashOfConfig": "52"}, {"size": 2725, "mtime": 1752162531754, "results": "74", "hashOfConfig": "52"}, {"size": 4039, "mtime": 1752163438286, "results": "75", "hashOfConfig": "52"}, {"size": 6737, "mtime": 1752163755002, "results": "76", "hashOfConfig": "52"}, {"size": 3178, "mtime": 1752165557372, "results": "77", "hashOfConfig": "52"}, {"size": 7659, "mtime": 1752166283822, "results": "78", "hashOfConfig": "52"}, {"size": 5631, "mtime": 1752166226242, "results": "79", "hashOfConfig": "52"}, {"size": 27255, "mtime": 1752197926438, "results": "80", "hashOfConfig": "52"}, {"size": 6228, "mtime": 1752196918984, "results": "81", "hashOfConfig": "52"}, {"size": 2548, "mtime": 1752166449807, "results": "82", "hashOfConfig": "52"}, {"size": 10935, "mtime": 1752198397095, "results": "83", "hashOfConfig": "52"}, {"size": 11729, "mtime": 1752198246141, "results": "84", "hashOfConfig": "52"}, {"size": 13262, "mtime": 1752197138631, "results": "85", "hashOfConfig": "52"}, {"size": 11021, "mtime": 1752197181613, "results": "86", "hashOfConfig": "52"}, {"size": 10954, "mtime": 1752172838857, "results": "87", "hashOfConfig": "52"}, {"size": 12612, "mtime": 1752197124550, "results": "88", "hashOfConfig": "52"}, {"size": 11154, "mtime": 1752172977498, "results": "89", "hashOfConfig": "52"}, {"size": 13711, "mtime": 1752196974278, "results": "90", "hashOfConfig": "52"}, {"size": 13348, "mtime": 1752197080242, "results": "91", "hashOfConfig": "52"}, {"size": 10807, "mtime": 1752198381073, "results": "92", "hashOfConfig": "52"}, {"size": 18884, "mtime": 1752197022207, "results": "93", "hashOfConfig": "52"}, {"size": 3533, "mtime": 1752196493957, "results": "94", "hashOfConfig": "52"}, {"size": 8628, "mtime": 1752198221999, "results": "95", "hashOfConfig": "52"}, {"size": 4156, "mtime": 1752196350024, "results": "96", "hashOfConfig": "52"}, {"size": 6422, "mtime": 1752196430644, "results": "97", "hashOfConfig": "52"}, {"size": 10811, "mtime": 1752197846447, "results": "98", "hashOfConfig": "52"}, {"size": 11861, "mtime": 1752197897170, "results": "99", "hashOfConfig": "52"}, {"size": 9608, "mtime": 1752197741544, "results": "100", "hashOfConfig": "52"}, {"size": 9891, "mtime": 1752198301661, "results": "101", "hashOfConfig": "52"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qlzb5l", {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx", ["252"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx", ["253", "254", "255", "256", "257"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameMainPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TransferPage.tsx", ["258", "259", "260", "261"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\UpgradePage.tsx", ["262", "263", "264", "265"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\RankingPage.tsx", ["266", "267"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ShopPage.tsx", ["268", "269"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LogsPage.tsx", ["270", "271", "272"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ConfigPage.tsx", ["273"], ["274", "275"], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\MiningPage.tsx", ["276", "277"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TerminalPage.tsx", ["278", "279", "280", "281", "282", "283", "284"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\BankPage.tsx", ["285", "286"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\GameFooter.tsx", ["287", "288"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\GameIcons.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\PlayerProfile.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\AppsPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ScannerPage.tsx", ["289"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\InvadedPage.tsx", ["290", "291"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\hooks\\useGameSystem.ts", ["292", "293"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameService.ts", ["294"], [], {"ruleId": "295", "severity": 1, "message": "296", "line": 1, "column": 17, "nodeType": "297", "messageId": "298", "endLine": 1, "endColumn": 26}, {"ruleId": "295", "severity": 1, "message": "299", "line": 6, "column": 10, "nodeType": "297", "messageId": "298", "endLine": 6, "endColumn": 23}, {"ruleId": "295", "severity": 1, "message": "300", "line": 24, "column": 7, "nodeType": "297", "messageId": "298", "endLine": 24, "endColumn": 32}, {"ruleId": "295", "severity": 1, "message": "301", "line": 181, "column": 7, "nodeType": "297", "messageId": "298", "endLine": 181, "endColumn": 30}, {"ruleId": "295", "severity": 1, "message": "302", "line": 182, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 182, "endColumn": 15}, {"ruleId": "295", "severity": 1, "message": "303", "line": 582, "column": 7, "nodeType": "297", "messageId": "298", "endLine": 582, "endColumn": 33}, {"ruleId": "295", "severity": 1, "message": "302", "line": 7, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 7, "endColumn": 15}, {"ruleId": "295", "severity": 1, "message": "304", "line": 25, "column": 10, "nodeType": "297", "messageId": "298", "endLine": 25, "endColumn": 30}, {"ruleId": "295", "severity": 1, "message": "305", "line": 28, "column": 10, "nodeType": "297", "messageId": "298", "endLine": 28, "endColumn": 25}, {"ruleId": "295", "severity": 1, "message": "306", "line": 29, "column": 10, "nodeType": "297", "messageId": "298", "endLine": 29, "endColumn": 26}, {"ruleId": "295", "severity": 1, "message": "302", "line": 20, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 20, "endColumn": 15}, {"ruleId": "295", "severity": 1, "message": "307", "line": 24, "column": 10, "nodeType": "297", "messageId": "298", "endLine": 24, "endColumn": 19}, {"ruleId": "295", "severity": 1, "message": "308", "line": 24, "column": 21, "nodeType": "297", "messageId": "298", "endLine": 24, "endColumn": 33}, {"ruleId": "309", "severity": 1, "message": "310", "line": 33, "column": 6, "nodeType": "311", "endLine": 33, "endColumn": 38, "suggestions": "312"}, {"ruleId": "295", "severity": 1, "message": "302", "line": 18, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 18, "endColumn": 15}, {"ruleId": "309", "severity": 1, "message": "313", "line": 30, "column": 6, "nodeType": "311", "endLine": 30, "endColumn": 41, "suggestions": "314"}, {"ruleId": "295", "severity": 1, "message": "302", "line": 21, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 21, "endColumn": 15}, {"ruleId": "309", "severity": 1, "message": "315", "line": 35, "column": 6, "nodeType": "311", "endLine": 35, "endColumn": 23, "suggestions": "316"}, {"ruleId": "295", "severity": 1, "message": "302", "line": 17, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 17, "endColumn": 15}, {"ruleId": "295", "severity": 1, "message": "317", "line": 18, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 18, "endColumn": 24}, {"ruleId": "309", "severity": 1, "message": "318", "line": 28, "column": 6, "nodeType": "311", "endLine": 28, "endColumn": 41, "suggestions": "319"}, {"ruleId": "295", "severity": 1, "message": "320", "line": 9, "column": 26, "nodeType": "297", "messageId": "298", "endLine": 9, "endColumn": 40}, {"ruleId": "321", "severity": 2, "message": "322", "line": 98, "column": 9, "nodeType": "297", "messageId": "323", "endLine": 98, "endColumn": 16, "suppressions": "324"}, {"ruleId": "321", "severity": 2, "message": "322", "line": 106, "column": 9, "nodeType": "297", "messageId": "323", "endLine": 106, "endColumn": 16, "suppressions": "325"}, {"ruleId": "295", "severity": 1, "message": "302", "line": 19, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 19, "endColumn": 15}, {"ruleId": "309", "severity": 1, "message": "326", "line": 43, "column": 6, "nodeType": "311", "endLine": 43, "endColumn": 23, "suggestions": "327"}, {"ruleId": "295", "severity": 1, "message": "328", "line": 7, "column": 10, "nodeType": "297", "messageId": "298", "endLine": 7, "endColumn": 22}, {"ruleId": "295", "severity": 1, "message": "329", "line": 7, "column": 24, "nodeType": "297", "messageId": "298", "endLine": 7, "endColumn": 33}, {"ruleId": "295", "severity": 1, "message": "330", "line": 7, "column": 35, "nodeType": "297", "messageId": "298", "endLine": 7, "endColumn": 49}, {"ruleId": "295", "severity": 1, "message": "331", "line": 20, "column": 5, "nodeType": "297", "messageId": "298", "endLine": 20, "endColumn": 22}, {"ruleId": "295", "severity": 1, "message": "332", "line": 23, "column": 5, "nodeType": "297", "messageId": "298", "endLine": 23, "endColumn": 20}, {"ruleId": "295", "severity": 1, "message": "333", "line": 24, "column": 5, "nodeType": "297", "messageId": "298", "endLine": 24, "endColumn": 20}, {"ruleId": "309", "severity": 1, "message": "334", "line": 39, "column": 6, "nodeType": "311", "endLine": 39, "endColumn": 46, "suggestions": "335"}, {"ruleId": "295", "severity": 1, "message": "302", "line": 26, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 26, "endColumn": 15}, {"ruleId": "309", "severity": 1, "message": "336", "line": 47, "column": 6, "nodeType": "311", "endLine": 47, "endColumn": 23, "suggestions": "337"}, {"ruleId": "295", "severity": 1, "message": "338", "line": 3, "column": 20, "nodeType": "297", "messageId": "298", "endLine": 3, "endColumn": 28}, {"ruleId": "295", "severity": 1, "message": "328", "line": 3, "column": 40, "nodeType": "297", "messageId": "298", "endLine": 3, "endColumn": 52}, {"ruleId": "295", "severity": 1, "message": "302", "line": 10, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 10, "endColumn": 15}, {"ruleId": "295", "severity": 1, "message": "339", "line": 5, "column": 34, "nodeType": "297", "messageId": "298", "endLine": 5, "endColumn": 46}, {"ruleId": "295", "severity": 1, "message": "340", "line": 5, "column": 48, "nodeType": "297", "messageId": "298", "endLine": 5, "endColumn": 60}, {"ruleId": "309", "severity": 1, "message": "341", "line": 143, "column": 6, "nodeType": "311", "endLine": 143, "endColumn": 8, "suggestions": "342"}, {"ruleId": "309", "severity": 1, "message": "343", "line": 283, "column": 6, "nodeType": "311", "endLine": 283, "endColumn": 8, "suggestions": "344"}, {"ruleId": "345", "severity": 1, "message": "346", "line": 331, "column": 1, "nodeType": "347", "endLine": 331, "endColumn": 34}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'useGameSystem' is defined but never used.", "'SimpleDashboard' is assigned a value but never used.", "'SimpleScanner' is assigned a value but never used.", "'user' is assigned a value but never used.", "'SimpleNavigation' is assigned a value but never used.", "'isLoadingConnections' is assigned a value but never used.", "'transferHistory' is assigned a value but never used.", "'isLoadingHistory' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUpgradeItems'. Either include it or remove the dependency array.", "ArrayExpression", ["348"], "React Hook useEffect has a missing dependency: 'loadRanking'. Either include it or remove the dependency array.", ["349"], "React Hook useEffect has a missing dependency: 'loadShopItems'. Either include it or remove the dependency array.", ["350"], "'currentPlayer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadLogs'. Either include it or remove the dependency array.", ["351"], "'loadPlayerData' is assigned a value but never used.", "no-restricted-globals", "Unexpected use of 'confirm'.", "defaultMessage", ["352"], ["353"], "React Hook useEffect has a missing dependency: 'loadMiningStatus'. Either include it or remove the dependency array.", ["354"], "'TerminalIcon' is defined but never used.", "'CloseIcon' is defined but never used.", "'BruteforceIcon' is defined but never used.", "'activeConnections' is assigned a value but never used.", "'startBruteforce' is assigned a value but never used.", "'closeConnection' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initializeTerminal'. Either include it or remove the dependency array.", ["355"], "React Hook useEffect has missing dependencies: 'loadBankData' and 'loadTransactions'. Either include them or remove the dependency array.", ["356"], "'ChatIcon' is defined but never used.", "'TransferIcon' is defined but never used.", "'SecurityIcon' is defined but never used.", "React Hook useCallback has a missing dependency: 'loadActiveConnections'. Either include it or remove the dependency array.", ["357"], "React Hook useEffect has missing dependencies: 'loadActiveConnections', 'loadPlayerData', 'startAutoUpdate', and 'stopAutoUpdate'. Either include them or remove the dependency array.", ["358"], "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "359", "fix": "360"}, {"desc": "361", "fix": "362"}, {"desc": "363", "fix": "364"}, {"desc": "365", "fix": "366"}, {"kind": "367", "justification": "368"}, {"kind": "367", "justification": "368"}, {"desc": "369", "fix": "370"}, {"desc": "371", "fix": "372"}, {"desc": "373", "fix": "374"}, {"desc": "375", "fix": "376"}, {"desc": "377", "fix": "378"}, "Update the dependencies array to be: [isAuthenticated, currentPlayer, loadUpgradeItems]", {"range": "379", "text": "380"}, "Update the dependencies array to be: [isAuthenticated, loadRanking, selectedCategory]", {"range": "381", "text": "382"}, "Update the dependencies array to be: [isAuthenticated, loadShopItems]", {"range": "383", "text": "384"}, "Update the dependencies array to be: [isAuthenticated, loadLogs, selectedCategory]", {"range": "385", "text": "386"}, "directive", "", "Update the dependencies array to be: [isAuthenticated, loadMiningStatus]", {"range": "387", "text": "388"}, "Update the dependencies array to be: [initializeTerminal, isAuthenticated, loadActiveConnections]", {"range": "389", "text": "390"}, "Update the dependencies array to be: [isAuthenticated, loadBankData, loadTransactions]", {"range": "391", "text": "392"}, "Update the dependencies array to be: [loadActiveConnections]", {"range": "393", "text": "394"}, "Update the dependencies array to be: [loadActiveConnections, loadPlayerData, startAutoUpdate, stopAutoUpdate]", {"range": "395", "text": "396"}, [997, 1029], "[isAuthenticated, currentPlayer, loadUpgradeItems]", [874, 909], "[isAuthenticated, loadRanking, selectedCategory]", [1096, 1113], "[isAuthenticated, loadShopItems]", [798, 833], "[isAuthenticated, loadLogs, selectedCategory]", [1328, 1345], "[isAuthenticated, loadMiningStatus]", [1217, 1257], "[initializeTerminal, isAuthenticated, loadActiveConnections]", [1433, 1450], "[isAuthenticated, loadBankData, loadTransactions]", [3900, 3902], "[loadActiveConnections]", [8105, 8107], "[loadActiveConnections, loadPlayerData, startAutoUpdate, stopAutoUpdate]"]