[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts": "28", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameMainPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TransferPage.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\UpgradePage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\RankingPage.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ShopPage.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LogsPage.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ConfigPage.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\MiningPage.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TerminalPage.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\BankPage.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\GameFooter.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\GameIcons.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\PlayerProfile.tsx": "45"}, {"size": 484, "mtime": *************, "results": "46", "hashOfConfig": "47"}, {"size": 425, "mtime": *************, "results": "48", "hashOfConfig": "47"}, {"size": 2027, "mtime": *************, "results": "49", "hashOfConfig": "47"}, {"size": 2266, "mtime": *************, "results": "50", "hashOfConfig": "47"}, {"size": 10762, "mtime": *************, "results": "51", "hashOfConfig": "47"}, {"size": 2435, "mtime": *************, "results": "52", "hashOfConfig": "47"}, {"size": 1341, "mtime": *************, "results": "53", "hashOfConfig": "47"}, {"size": 9261, "mtime": *************, "results": "54", "hashOfConfig": "47"}, {"size": 6038, "mtime": 1752128793748, "results": "55", "hashOfConfig": "47"}, {"size": 1634, "mtime": 1752128735960, "results": "56", "hashOfConfig": "47"}, {"size": 7878, "mtime": 1752172522843, "results": "57", "hashOfConfig": "47"}, {"size": 6408, "mtime": 1752128690546, "results": "58", "hashOfConfig": "47"}, {"size": 6815, "mtime": 1752166912159, "results": "59", "hashOfConfig": "47"}, {"size": 9347, "mtime": 1752129057056, "results": "60", "hashOfConfig": "47"}, {"size": 5606, "mtime": 1752131581102, "results": "61", "hashOfConfig": "47"}, {"size": 2841, "mtime": 1752129361158, "results": "62", "hashOfConfig": "47"}, {"size": 9382, "mtime": 1752131549084, "results": "63", "hashOfConfig": "47"}, {"size": 8066, "mtime": 1752131568683, "results": "64", "hashOfConfig": "47"}, {"size": 9580, "mtime": 1752129162226, "results": "65", "hashOfConfig": "47"}, {"size": 5543, "mtime": 1752129239339, "results": "66", "hashOfConfig": "47"}, {"size": 6109, "mtime": 1752165017979, "results": "67", "hashOfConfig": "47"}, {"size": 1872, "mtime": 1752162499900, "results": "68", "hashOfConfig": "47"}, {"size": 2725, "mtime": 1752162531754, "results": "69", "hashOfConfig": "47"}, {"size": 4039, "mtime": 1752163438286, "results": "70", "hashOfConfig": "47"}, {"size": 6737, "mtime": 1752163755002, "results": "71", "hashOfConfig": "47"}, {"size": 3178, "mtime": 1752165557372, "results": "72", "hashOfConfig": "47"}, {"size": 7659, "mtime": 1752166283822, "results": "73", "hashOfConfig": "47"}, {"size": 5631, "mtime": 1752166226242, "results": "74", "hashOfConfig": "47"}, {"size": 26970, "mtime": 1752194615907, "results": "75", "hashOfConfig": "47"}, {"size": 6104, "mtime": 1752168427083, "results": "76", "hashOfConfig": "47"}, {"size": 2548, "mtime": 1752166449807, "results": "77", "hashOfConfig": "47"}, {"size": 10365, "mtime": 1752174059882, "results": "78", "hashOfConfig": "47"}, {"size": 11102, "mtime": 1752195627647, "results": "79", "hashOfConfig": "47"}, {"size": 13254, "mtime": 1752173730684, "results": "80", "hashOfConfig": "47"}, {"size": 10991, "mtime": 1752172632580, "results": "81", "hashOfConfig": "47"}, {"size": 10954, "mtime": 1752172838857, "results": "82", "hashOfConfig": "47"}, {"size": 12582, "mtime": 1752172793775, "results": "83", "hashOfConfig": "47"}, {"size": 11154, "mtime": 1752172977498, "results": "84", "hashOfConfig": "47"}, {"size": 13603, "mtime": 1752194556779, "results": "85", "hashOfConfig": "47"}, {"size": 13321, "mtime": 1752173785553, "results": "86", "hashOfConfig": "47"}, {"size": 10835, "mtime": 1752194508090, "results": "87", "hashOfConfig": "47"}, {"size": 18872, "mtime": 1752174046879, "results": "88", "hashOfConfig": "47"}, {"size": 3535, "mtime": 1752195500659, "results": "89", "hashOfConfig": "47"}, {"size": 6802, "mtime": 1752195435739, "results": "90", "hashOfConfig": "47"}, {"size": 4278, "mtime": 1752195461208, "results": "91", "hashOfConfig": "47"}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qlzb5l", {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx", ["227"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx", ["228", "229", "230"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameMainPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TransferPage.tsx", ["231", "232", "233", "234"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\UpgradePage.tsx", ["235", "236", "237", "238"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\RankingPage.tsx", ["239", "240"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ShopPage.tsx", ["241", "242"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LogsPage.tsx", ["243", "244", "245"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ConfigPage.tsx", ["246", "247", "248"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\MiningPage.tsx", ["249", "250"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TerminalPage.tsx", ["251"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\BankPage.tsx", ["252", "253"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\GameFooter.tsx", ["254", "255"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\GameIcons.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\ui\\PlayerProfile.tsx", [], [], {"ruleId": "256", "severity": 1, "message": "257", "line": 1, "column": 17, "nodeType": "258", "messageId": "259", "endLine": 1, "endColumn": 26}, {"ruleId": "256", "severity": 1, "message": "260", "line": 20, "column": 7, "nodeType": "258", "messageId": "259", "endLine": 20, "endColumn": 32}, {"ruleId": "256", "severity": 1, "message": "261", "line": 178, "column": 11, "nodeType": "258", "messageId": "259", "endLine": 178, "endColumn": 15}, {"ruleId": "256", "severity": 1, "message": "262", "line": 578, "column": 7, "nodeType": "258", "messageId": "259", "endLine": 578, "endColumn": 33}, {"ruleId": "256", "severity": 1, "message": "261", "line": 7, "column": 11, "nodeType": "258", "messageId": "259", "endLine": 7, "endColumn": 15}, {"ruleId": "256", "severity": 1, "message": "263", "line": 25, "column": 10, "nodeType": "258", "messageId": "259", "endLine": 25, "endColumn": 30}, {"ruleId": "256", "severity": 1, "message": "264", "line": 28, "column": 10, "nodeType": "258", "messageId": "259", "endLine": 28, "endColumn": 25}, {"ruleId": "256", "severity": 1, "message": "265", "line": 29, "column": 10, "nodeType": "258", "messageId": "259", "endLine": 29, "endColumn": 26}, {"ruleId": "256", "severity": 1, "message": "261", "line": 20, "column": 11, "nodeType": "258", "messageId": "259", "endLine": 20, "endColumn": 15}, {"ruleId": "256", "severity": 1, "message": "266", "line": 24, "column": 10, "nodeType": "258", "messageId": "259", "endLine": 24, "endColumn": 19}, {"ruleId": "256", "severity": 1, "message": "267", "line": 24, "column": 21, "nodeType": "258", "messageId": "259", "endLine": 24, "endColumn": 33}, {"ruleId": "268", "severity": 1, "message": "269", "line": 33, "column": 6, "nodeType": "270", "endLine": 33, "endColumn": 38, "suggestions": "271"}, {"ruleId": "256", "severity": 1, "message": "261", "line": 18, "column": 11, "nodeType": "258", "messageId": "259", "endLine": 18, "endColumn": 15}, {"ruleId": "268", "severity": 1, "message": "272", "line": 30, "column": 6, "nodeType": "270", "endLine": 30, "endColumn": 41, "suggestions": "273"}, {"ruleId": "256", "severity": 1, "message": "261", "line": 21, "column": 11, "nodeType": "258", "messageId": "259", "endLine": 21, "endColumn": 15}, {"ruleId": "268", "severity": 1, "message": "274", "line": 35, "column": 6, "nodeType": "270", "endLine": 35, "endColumn": 23, "suggestions": "275"}, {"ruleId": "256", "severity": 1, "message": "261", "line": 17, "column": 11, "nodeType": "258", "messageId": "259", "endLine": 17, "endColumn": 15}, {"ruleId": "256", "severity": 1, "message": "276", "line": 18, "column": 11, "nodeType": "258", "messageId": "259", "endLine": 18, "endColumn": 24}, {"ruleId": "268", "severity": 1, "message": "277", "line": 28, "column": 6, "nodeType": "270", "endLine": 28, "endColumn": 41, "suggestions": "278"}, {"ruleId": "256", "severity": 1, "message": "279", "line": 9, "column": 26, "nodeType": "258", "messageId": "259", "endLine": 9, "endColumn": 40}, {"ruleId": "280", "severity": 2, "message": "281", "line": 97, "column": 9, "nodeType": "258", "messageId": "282", "endLine": 97, "endColumn": 16}, {"ruleId": "280", "severity": 2, "message": "281", "line": 104, "column": 9, "nodeType": "258", "messageId": "282", "endLine": 104, "endColumn": 16}, {"ruleId": "256", "severity": 1, "message": "261", "line": 19, "column": 11, "nodeType": "258", "messageId": "259", "endLine": 19, "endColumn": 15}, {"ruleId": "268", "severity": 1, "message": "283", "line": 43, "column": 6, "nodeType": "270", "endLine": 43, "endColumn": 23, "suggestions": "284"}, {"ruleId": "268", "severity": 1, "message": "285", "line": 31, "column": 6, "nodeType": "270", "endLine": 31, "endColumn": 23, "suggestions": "286"}, {"ruleId": "256", "severity": 1, "message": "261", "line": 26, "column": 11, "nodeType": "258", "messageId": "259", "endLine": 26, "endColumn": 15}, {"ruleId": "268", "severity": 1, "message": "287", "line": 47, "column": 6, "nodeType": "270", "endLine": 47, "endColumn": 23, "suggestions": "288"}, {"ruleId": "256", "severity": 1, "message": "289", "line": 3, "column": 20, "nodeType": "258", "messageId": "259", "endLine": 3, "endColumn": 28}, {"ruleId": "256", "severity": 1, "message": "290", "line": 3, "column": 40, "nodeType": "258", "messageId": "259", "endLine": 3, "endColumn": 52}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'SimpleDashboard' is assigned a value but never used.", "'user' is assigned a value but never used.", "'SimpleNavigation' is assigned a value but never used.", "'isLoadingConnections' is assigned a value but never used.", "'transferHistory' is assigned a value but never used.", "'isLoadingHistory' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUpgradeItems'. Either include it or remove the dependency array.", "ArrayExpression", ["291"], "React Hook useEffect has a missing dependency: 'loadRanking'. Either include it or remove the dependency array.", ["292"], "React Hook useEffect has a missing dependency: 'loadShopItems'. Either include it or remove the dependency array.", ["293"], "'currentPlayer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadLogs'. Either include it or remove the dependency array.", ["294"], "'loadPlayerData' is assigned a value but never used.", "no-restricted-globals", "Unexpected use of 'confirm'.", "defaultMessage", "React Hook useEffect has a missing dependency: 'loadMiningStatus'. Either include it or remove the dependency array.", ["295"], "React Hook useEffect has a missing dependency: 'initializeTerminal'. Either include it or remove the dependency array.", ["296"], "React Hook useEffect has missing dependencies: 'loadBankData' and 'loadTransactions'. Either include them or remove the dependency array.", ["297"], "'ChatIcon' is defined but never used.", "'TerminalIcon' is defined but never used.", {"desc": "298", "fix": "299"}, {"desc": "300", "fix": "301"}, {"desc": "302", "fix": "303"}, {"desc": "304", "fix": "305"}, {"desc": "306", "fix": "307"}, {"desc": "308", "fix": "309"}, {"desc": "310", "fix": "311"}, "Update the dependencies array to be: [isAuthenticated, currentPlayer, loadUpgradeItems]", {"range": "312", "text": "313"}, "Update the dependencies array to be: [isAuthenticated, loadRanking, selectedCategory]", {"range": "314", "text": "315"}, "Update the dependencies array to be: [isAuthenticated, loadShopItems]", {"range": "316", "text": "317"}, "Update the dependencies array to be: [isAuthenticated, loadLogs, selectedCategory]", {"range": "318", "text": "319"}, "Update the dependencies array to be: [isAuthenticated, loadMiningStatus]", {"range": "320", "text": "321"}, "Update the dependencies array to be: [initializeTerminal, isAuthenticated]", {"range": "322", "text": "323"}, "Update the dependencies array to be: [isAuthenticated, loadBankData, loadTransactions]", {"range": "324", "text": "325"}, [997, 1029], "[isAuthenticated, currentPlayer, loadUpgradeItems]", [874, 909], "[isAuthenticated, loadRanking, selectedCategory]", [1096, 1113], "[isAuthenticated, loadShopItems]", [798, 833], "[isAuthenticated, loadLogs, selectedCategory]", [1328, 1345], "[isAuthenticated, loadMiningStatus]", [1000, 1017], "[initializeTerminal, isAuthenticated]", [1433, 1450], "[isAuthenticated, loadBankData, loadTransactions]"]