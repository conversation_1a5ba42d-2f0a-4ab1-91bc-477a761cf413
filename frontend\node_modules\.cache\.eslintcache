[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts": "28", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameMainPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TransferPage.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\UpgradePage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\RankingPage.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ShopPage.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LogsPage.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ConfigPage.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\MiningPage.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TerminalPage.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\BankPage.tsx": "42"}, {"size": 484, "mtime": *************, "results": "43", "hashOfConfig": "44"}, {"size": 425, "mtime": *************, "results": "45", "hashOfConfig": "44"}, {"size": 2027, "mtime": *************, "results": "46", "hashOfConfig": "44"}, {"size": 2266, "mtime": *************, "results": "47", "hashOfConfig": "44"}, {"size": 10762, "mtime": *************, "results": "48", "hashOfConfig": "44"}, {"size": 2435, "mtime": *************, "results": "49", "hashOfConfig": "44"}, {"size": 1341, "mtime": *************, "results": "50", "hashOfConfig": "44"}, {"size": 9261, "mtime": *************, "results": "51", "hashOfConfig": "44"}, {"size": 6038, "mtime": *************, "results": "52", "hashOfConfig": "44"}, {"size": 1634, "mtime": *************, "results": "53", "hashOfConfig": "44"}, {"size": 7878, "mtime": *************, "results": "54", "hashOfConfig": "44"}, {"size": 6408, "mtime": *************, "results": "55", "hashOfConfig": "44"}, {"size": 6815, "mtime": 1752166912159, "results": "56", "hashOfConfig": "44"}, {"size": 9347, "mtime": 1752129057056, "results": "57", "hashOfConfig": "44"}, {"size": 5606, "mtime": 1752131581102, "results": "58", "hashOfConfig": "44"}, {"size": 2841, "mtime": 1752129361158, "results": "59", "hashOfConfig": "44"}, {"size": 9382, "mtime": 1752131549084, "results": "60", "hashOfConfig": "44"}, {"size": 8066, "mtime": 1752131568683, "results": "61", "hashOfConfig": "44"}, {"size": 9580, "mtime": 1752129162226, "results": "62", "hashOfConfig": "44"}, {"size": 5543, "mtime": 1752129239339, "results": "63", "hashOfConfig": "44"}, {"size": 6109, "mtime": 1752165017979, "results": "64", "hashOfConfig": "44"}, {"size": 1872, "mtime": 1752162499900, "results": "65", "hashOfConfig": "44"}, {"size": 2725, "mtime": 1752162531754, "results": "66", "hashOfConfig": "44"}, {"size": 4039, "mtime": 1752163438286, "results": "67", "hashOfConfig": "44"}, {"size": 6737, "mtime": 1752163755002, "results": "68", "hashOfConfig": "44"}, {"size": 3178, "mtime": 1752165557372, "results": "69", "hashOfConfig": "44"}, {"size": 7659, "mtime": 1752166283822, "results": "70", "hashOfConfig": "44"}, {"size": 5631, "mtime": 1752166226242, "results": "71", "hashOfConfig": "44"}, {"size": 27251, "mtime": 1752174086765, "results": "72", "hashOfConfig": "44"}, {"size": 6104, "mtime": 1752168427083, "results": "73", "hashOfConfig": "44"}, {"size": 2548, "mtime": 1752166449807, "results": "74", "hashOfConfig": "44"}, {"size": 10365, "mtime": 1752174059882, "results": "75", "hashOfConfig": "44"}, {"size": 10129, "mtime": 1752174103342, "results": "76", "hashOfConfig": "44"}, {"size": 13254, "mtime": 1752173730684, "results": "77", "hashOfConfig": "44"}, {"size": 10991, "mtime": 1752172632580, "results": "78", "hashOfConfig": "44"}, {"size": 10954, "mtime": 1752172838857, "results": "79", "hashOfConfig": "44"}, {"size": 12582, "mtime": 1752172793775, "results": "80", "hashOfConfig": "44"}, {"size": 11154, "mtime": 1752172977498, "results": "81", "hashOfConfig": "44"}, {"size": 13943, "mtime": 1752173028183, "results": "82", "hashOfConfig": "44"}, {"size": 13321, "mtime": 1752173785553, "results": "83", "hashOfConfig": "44"}, {"size": 10695, "mtime": 1752173832974, "results": "84", "hashOfConfig": "44"}, {"size": 18872, "mtime": 1752174046879, "results": "85", "hashOfConfig": "44"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qlzb5l", {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GamePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\NotificationSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\AuthGuard.tsx", ["212"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\authStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\gameStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\chatStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatMessage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\chat\\ChatSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\PlayerStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\components\\dashboard\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.simple.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.gradual.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.query.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\App.stores.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TestPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleLoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\simpleAuthStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\SimpleGamePage.tsx", ["213", "214", "215"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\mockApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\stores\\playerStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\services\\gameApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\GameMainPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TransferPage.tsx", ["216", "217", "218", "219"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\UpgradePage.tsx", ["220", "221", "222", "223"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\RankingPage.tsx", ["224", "225"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ShopPage.tsx", ["226", "227"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\LogsPage.tsx", ["228", "229", "230"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\ConfigPage.tsx", ["231", "232", "233"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\MiningPage.tsx", ["234", "235"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\TerminalPage.tsx", ["236"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\src\\pages\\BankPage.tsx", ["237", "238"], [], {"ruleId": "239", "severity": 1, "message": "240", "line": 1, "column": 17, "nodeType": "241", "messageId": "242", "endLine": 1, "endColumn": 26}, {"ruleId": "239", "severity": 1, "message": "243", "line": 19, "column": 7, "nodeType": "241", "messageId": "242", "endLine": 19, "endColumn": 32}, {"ruleId": "239", "severity": 1, "message": "244", "line": 177, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 177, "endColumn": 15}, {"ruleId": "239", "severity": 1, "message": "245", "line": 584, "column": 7, "nodeType": "241", "messageId": "242", "endLine": 584, "endColumn": 33}, {"ruleId": "239", "severity": 1, "message": "244", "line": 7, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 7, "endColumn": 15}, {"ruleId": "239", "severity": 1, "message": "246", "line": 25, "column": 10, "nodeType": "241", "messageId": "242", "endLine": 25, "endColumn": 30}, {"ruleId": "239", "severity": 1, "message": "247", "line": 28, "column": 10, "nodeType": "241", "messageId": "242", "endLine": 28, "endColumn": 25}, {"ruleId": "239", "severity": 1, "message": "248", "line": 29, "column": 10, "nodeType": "241", "messageId": "242", "endLine": 29, "endColumn": 26}, {"ruleId": "239", "severity": 1, "message": "244", "line": 20, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 20, "endColumn": 15}, {"ruleId": "239", "severity": 1, "message": "249", "line": 24, "column": 10, "nodeType": "241", "messageId": "242", "endLine": 24, "endColumn": 19}, {"ruleId": "239", "severity": 1, "message": "250", "line": 24, "column": 21, "nodeType": "241", "messageId": "242", "endLine": 24, "endColumn": 33}, {"ruleId": "251", "severity": 1, "message": "252", "line": 33, "column": 6, "nodeType": "253", "endLine": 33, "endColumn": 38, "suggestions": "254"}, {"ruleId": "239", "severity": 1, "message": "244", "line": 18, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 18, "endColumn": 15}, {"ruleId": "251", "severity": 1, "message": "255", "line": 30, "column": 6, "nodeType": "253", "endLine": 30, "endColumn": 41, "suggestions": "256"}, {"ruleId": "239", "severity": 1, "message": "244", "line": 21, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 21, "endColumn": 15}, {"ruleId": "251", "severity": 1, "message": "257", "line": 35, "column": 6, "nodeType": "253", "endLine": 35, "endColumn": 23, "suggestions": "258"}, {"ruleId": "239", "severity": 1, "message": "244", "line": 17, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 17, "endColumn": 15}, {"ruleId": "239", "severity": 1, "message": "259", "line": 18, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 18, "endColumn": 24}, {"ruleId": "251", "severity": 1, "message": "260", "line": 28, "column": 6, "nodeType": "253", "endLine": 28, "endColumn": 41, "suggestions": "261"}, {"ruleId": "239", "severity": 1, "message": "262", "line": 8, "column": 26, "nodeType": "241", "messageId": "242", "endLine": 8, "endColumn": 40}, {"ruleId": "263", "severity": 2, "message": "264", "line": 96, "column": 9, "nodeType": "241", "messageId": "265", "endLine": 96, "endColumn": 16}, {"ruleId": "263", "severity": 2, "message": "264", "line": 103, "column": 9, "nodeType": "241", "messageId": "265", "endLine": 103, "endColumn": 16}, {"ruleId": "239", "severity": 1, "message": "244", "line": 19, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 19, "endColumn": 15}, {"ruleId": "251", "severity": 1, "message": "266", "line": 43, "column": 6, "nodeType": "253", "endLine": 43, "endColumn": 23, "suggestions": "267"}, {"ruleId": "251", "severity": 1, "message": "268", "line": 30, "column": 6, "nodeType": "253", "endLine": 30, "endColumn": 23, "suggestions": "269"}, {"ruleId": "239", "severity": 1, "message": "244", "line": 26, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 26, "endColumn": 15}, {"ruleId": "251", "severity": 1, "message": "270", "line": 47, "column": 6, "nodeType": "253", "endLine": 47, "endColumn": 23, "suggestions": "271"}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'SimpleDashboard' is assigned a value but never used.", "'user' is assigned a value but never used.", "'SimpleNavigation' is assigned a value but never used.", "'isLoadingConnections' is assigned a value but never used.", "'transferHistory' is assigned a value but never used.", "'isLoadingHistory' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUpgradeItems'. Either include it or remove the dependency array.", "ArrayExpression", ["272"], "React Hook useEffect has a missing dependency: 'loadRanking'. Either include it or remove the dependency array.", ["273"], "React Hook useEffect has a missing dependency: 'loadShopItems'. Either include it or remove the dependency array.", ["274"], "'currentPlayer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadLogs'. Either include it or remove the dependency array.", ["275"], "'loadPlayerData' is assigned a value but never used.", "no-restricted-globals", "Unexpected use of 'confirm'.", "defaultMessage", "React Hook useEffect has a missing dependency: 'loadMiningStatus'. Either include it or remove the dependency array.", ["276"], "React Hook useEffect has a missing dependency: 'initializeTerminal'. Either include it or remove the dependency array.", ["277"], "React Hook useEffect has missing dependencies: 'loadBankData' and 'loadTransactions'. Either include them or remove the dependency array.", ["278"], {"desc": "279", "fix": "280"}, {"desc": "281", "fix": "282"}, {"desc": "283", "fix": "284"}, {"desc": "285", "fix": "286"}, {"desc": "287", "fix": "288"}, {"desc": "289", "fix": "290"}, {"desc": "291", "fix": "292"}, "Update the dependencies array to be: [isAuthenticated, currentPlayer, loadUpgradeItems]", {"range": "293", "text": "294"}, "Update the dependencies array to be: [isAuthenticated, loadRanking, selectedCategory]", {"range": "295", "text": "296"}, "Update the dependencies array to be: [isAuthenticated, loadShopItems]", {"range": "297", "text": "298"}, "Update the dependencies array to be: [isAuthenticated, loadLogs, selectedCategory]", {"range": "299", "text": "300"}, "Update the dependencies array to be: [isAuthenticated, loadMiningStatus]", {"range": "301", "text": "302"}, "Update the dependencies array to be: [initializeTerminal, isAuthenticated]", {"range": "303", "text": "304"}, "Update the dependencies array to be: [isAuthenticated, loadBankData, loadTransactions]", {"range": "305", "text": "306"}, [997, 1029], "[isAuthenticated, currentPlayer, loadUpgradeItems]", [874, 909], "[isAuthenticated, loadRanking, selectedCategory]", [1096, 1113], "[isAuthenticated, loadShopItems]", [798, 833], "[isAuthenticated, loadLogs, selectedCategory]", [1328, 1345], "[isAuthenticated, loadMiningStatus]", [942, 959], "[initializeTerminal, isAuthenticated]", [1433, 1450], "[isAuthenticated, loadBankData, loadTransactions]"]