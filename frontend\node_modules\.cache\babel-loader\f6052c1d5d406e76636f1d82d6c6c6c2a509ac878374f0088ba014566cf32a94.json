{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../../stores/authStore';\nimport { useGame } from '../../stores/gameStore';\nimport { Bars3Icon, UserCircleIcon, CogIcon, ArrowRightOnRectangleIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  onMenuToggle\n}) => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    currentPlayer,\n    playerMoney,\n    playerNick\n  } = useGame();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const handleLogout = () => {\n    logout();\n    setShowUserMenu(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-bg-secondary border-b border-border-color shadow-lg\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 py-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onMenuToggle,\n            className: \"md:hidden p-2 rounded-lg hover:bg-bg-tertiary transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(Bars3Icon, {\n              className: \"h-6 w-6 text-text-primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-500 text-xl\",\n              children: \"\\u25CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold text-xl text-text-primary\",\n              children: \"SHACK\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-400 text-lg\",\n              children: \"WEB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-300 text-xl\",\n              children: \"\\u25CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden sm:block\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-text-muted bg-bg-tertiary px-2 py-1 rounded\",\n              children: \"React v1.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [currentPlayer && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden sm:flex items-center space-x-2 bg-bg-tertiary px-3 py-2 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-accent-green text-lg\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-text-primary font-mono font-bold\",\n              children: [\"$\", playerMoney.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowUserMenu(!showUserMenu),\n              className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-bg-tertiary transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(UserCircleIcon, {\n                className: \"h-8 w-8 text-text-secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-text-primary\",\n                  children: playerNick || (user === null || user === void 0 ? void 0 : user.nick)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-text-muted\",\n                  children: user === null || user === void 0 ? void 0 : user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute right-0 mt-2 w-48 bg-bg-secondary border border-border-color rounded-lg shadow-xl z-50\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-4 py-2 border-b border-border-color\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-text-primary\",\n                    children: playerNick || (user === null || user === void 0 ? void 0 : user.nick)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-text-muted\",\n                    children: user === null || user === void 0 ? void 0 : user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowUserMenu(false),\n                  className: \"w-full flex items-center px-4 py-2 text-sm text-text-secondary hover:text-text-primary hover:bg-bg-tertiary transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(CogIcon, {\n                    className: \"h-4 w-4 mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 23\n                  }, this), \"Configura\\xE7\\xF5es\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleLogout,\n                  className: \"w-full flex items-center px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-bg-tertiary transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n                    className: \"h-4 w-4 mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 23\n                  }, this), \"Sair\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), currentPlayer && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sm:hidden mt-3 flex items-center justify-between bg-bg-tertiary px-3 py-2 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-accent-green\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-text-primary font-mono font-bold text-sm\",\n            children: [\"$\", playerMoney.toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-text-muted\",\n          children: [\"CPU: \", currentPlayer.cpu, \" | FW: \", currentPlayer.firewall]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-40\",\n      onClick: () => setShowUserMenu(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"5d9NuUpYQF9mAomPyFnWga1tr+A=\", false, function () {\n  return [useAuth, useGame];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "useGame", "Bars3Icon", "UserCircleIcon", "CogIcon", "ArrowRightOnRectangleIcon", "jsxDEV", "_jsxDEV", "Header", "onMenuToggle", "_s", "user", "logout", "currentPlayer", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "showUserMenu", "setShowUserMenu", "handleLogout", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "nick", "email", "cpu", "firewall", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../../stores/authStore';\nimport { useGame } from '../../stores/gameStore';\nimport {\n  Bars3Icon,\n  UserCircleIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\n\ninterface HeaderProps {\n  onMenuToggle?: () => void;\n}\n\nconst Header: React.FC<HeaderProps> = ({ onMenuToggle }) => {\n  const { user, logout } = useAuth();\n  const { currentPlayer, playerMoney, playerNick } = useGame();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    setShowUserMenu(false);\n  };\n\n  return (\n    <header className=\"bg-bg-secondary border-b border-border-color shadow-lg\">\n      <div className=\"px-4 py-3\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo e Menu Mobile */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Botão do menu mobile */}\n            <button\n              onClick={onMenuToggle}\n              className=\"md:hidden p-2 rounded-lg hover:bg-bg-tertiary transition-colors\"\n            >\n              <Bars3Icon className=\"h-6 w-6 text-text-primary\" />\n            </button>\n\n            {/* Logo */}\n            <div className=\"flex items-center space-x-3\">\n              <span className=\"text-blue-500 text-xl\">●</span>\n              <span className=\"font-bold text-xl text-text-primary\">SHACK</span>\n              <span className=\"text-blue-400 text-lg\">WEB</span>\n              <span className=\"text-blue-300 text-xl\">●</span>\n            </div>\n\n            {/* Versão */}\n            <div className=\"hidden sm:block\">\n              <span className=\"text-xs text-text-muted bg-bg-tertiary px-2 py-1 rounded\">\n                React v1.0\n              </span>\n            </div>\n          </div>\n\n          {/* Informações do Jogador */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Dinheiro do jogador */}\n            {currentPlayer && (\n              <div className=\"hidden sm:flex items-center space-x-2 bg-bg-tertiary px-3 py-2 rounded-lg\">\n                <span className=\"text-accent-green text-lg\">💰</span>\n                <span className=\"text-text-primary font-mono font-bold\">\n                  ${playerMoney.toLocaleString()}\n                </span>\n              </div>\n            )}\n\n            {/* Menu do usuário */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowUserMenu(!showUserMenu)}\n                className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-bg-tertiary transition-colors\"\n              >\n                <UserCircleIcon className=\"h-8 w-8 text-text-secondary\" />\n                <div className=\"hidden sm:block text-left\">\n                  <div className=\"text-sm font-medium text-text-primary\">\n                    {playerNick || user?.nick}\n                  </div>\n                  <div className=\"text-xs text-text-muted\">\n                    {user?.email}\n                  </div>\n                </div>\n              </button>\n\n              {/* Dropdown do usuário */}\n              {showUserMenu && (\n                <div className=\"absolute right-0 mt-2 w-48 bg-bg-secondary border border-border-color rounded-lg shadow-xl z-50\">\n                  <div className=\"py-2\">\n                    {/* Informações do usuário */}\n                    <div className=\"px-4 py-2 border-b border-border-color\">\n                      <div className=\"text-sm font-medium text-text-primary\">\n                        {playerNick || user?.nick}\n                      </div>\n                      <div className=\"text-xs text-text-muted\">\n                        {user?.email}\n                      </div>\n                    </div>\n\n                    {/* Opções do menu */}\n                    <button\n                      onClick={() => setShowUserMenu(false)}\n                      className=\"w-full flex items-center px-4 py-2 text-sm text-text-secondary hover:text-text-primary hover:bg-bg-tertiary transition-colors\"\n                    >\n                      <CogIcon className=\"h-4 w-4 mr-3\" />\n                      Configurações\n                    </button>\n\n                    <button\n                      onClick={handleLogout}\n                      className=\"w-full flex items-center px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-bg-tertiary transition-colors\"\n                    >\n                      <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-3\" />\n                      Sair\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Barra de status (mobile) */}\n        {currentPlayer && (\n          <div className=\"sm:hidden mt-3 flex items-center justify-between bg-bg-tertiary px-3 py-2 rounded-lg\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-accent-green\">💰</span>\n              <span className=\"text-text-primary font-mono font-bold text-sm\">\n                ${playerMoney.toLocaleString()}\n              </span>\n            </div>\n            <div className=\"text-xs text-text-muted\">\n              CPU: {currentPlayer.cpu} | FW: {currentPlayer.firewall}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Overlay para fechar menu */}\n      {showUserMenu && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setShowUserMenu(false)}\n        />\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SACEC,SAAS,EACTC,cAAc,EACdC,OAAO,EACPC,yBAAyB,QACpB,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMrC,MAAMC,MAA6B,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEa,aAAa;IAAEC,WAAW;IAAEC;EAAW,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC5D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACzBN,MAAM,CAAC,CAAC;IACRK,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,oBACEV,OAAA;IAAQY,SAAS,EAAC,wDAAwD;IAAAC,QAAA,gBACxEb,OAAA;MAAKY,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBb,OAAA;QAAKY,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAEhDb,OAAA;UAAKY,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAE1Cb,OAAA;YACEc,OAAO,EAAEZ,YAAa;YACtBU,SAAS,EAAC,iEAAiE;YAAAC,QAAA,eAE3Eb,OAAA,CAACL,SAAS;cAACiB,SAAS,EAAC;YAA2B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAGTlB,OAAA;YAAKY,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1Cb,OAAA;cAAMY,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDlB,OAAA;cAAMY,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClElB,OAAA;cAAMY,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDlB,OAAA;cAAMY,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAGNlB,OAAA;YAAKY,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9Bb,OAAA;cAAMY,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlB,OAAA;UAAKY,SAAS,EAAC,6BAA6B;UAAAC,QAAA,GAEzCP,aAAa,iBACZN,OAAA;YAAKY,SAAS,EAAC,2EAA2E;YAAAC,QAAA,gBACxFb,OAAA;cAAMY,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDlB,OAAA;cAAMY,SAAS,EAAC,uCAAuC;cAAAC,QAAA,GAAC,GACrD,EAACN,WAAW,CAACY,cAAc,CAAC,CAAC;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN,eAGDlB,OAAA;YAAKY,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBb,OAAA;cACEc,OAAO,EAAEA,CAAA,KAAMJ,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CG,SAAS,EAAC,mFAAmF;cAAAC,QAAA,gBAE7Fb,OAAA,CAACJ,cAAc;gBAACgB,SAAS,EAAC;cAA6B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1DlB,OAAA;gBAAKY,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCb,OAAA;kBAAKY,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EACnDL,UAAU,KAAIJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI;gBAAA;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNlB,OAAA;kBAAKY,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EACrCT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EAGRT,YAAY,iBACXT,OAAA;cAAKY,SAAS,EAAC,iGAAiG;cAAAC,QAAA,eAC9Gb,OAAA;gBAAKY,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAEnBb,OAAA;kBAAKY,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDb,OAAA;oBAAKY,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnDL,UAAU,KAAIJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,eACNlB,OAAA;oBAAKY,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EACrCT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB;kBAAK;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlB,OAAA;kBACEc,OAAO,EAAEA,CAAA,KAAMJ,eAAe,CAAC,KAAK,CAAE;kBACtCE,SAAS,EAAC,+HAA+H;kBAAAC,QAAA,gBAEzIb,OAAA,CAACH,OAAO;oBAACe,SAAS,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uBAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAETlB,OAAA;kBACEc,OAAO,EAAEH,YAAa;kBACtBC,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAE7Hb,OAAA,CAACF,yBAAyB;oBAACc,SAAS,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,QAExD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLZ,aAAa,iBACZN,OAAA;QAAKY,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBACnGb,OAAA;UAAKY,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1Cb,OAAA;YAAMY,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7ClB,OAAA;YAAMY,SAAS,EAAC,+CAA+C;YAAAC,QAAA,GAAC,GAC7D,EAACN,WAAW,CAACY,cAAc,CAAC,CAAC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNlB,OAAA;UAAKY,SAAS,EAAC,yBAAyB;UAAAC,QAAA,GAAC,OAClC,EAACP,aAAa,CAACgB,GAAG,EAAC,SAAO,EAAChB,aAAa,CAACiB,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLT,YAAY,iBACXT,OAAA;MACEY,SAAS,EAAC,oBAAoB;MAC9BE,OAAO,EAAEA,CAAA,KAAMJ,eAAe,CAAC,KAAK;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACf,EAAA,CAnIIF,MAA6B;EAAA,QACRR,OAAO,EACmBC,OAAO;AAAA;AAAA8B,EAAA,GAFtDvB,MAA6B;AAqInC,eAAeA,MAAM;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}