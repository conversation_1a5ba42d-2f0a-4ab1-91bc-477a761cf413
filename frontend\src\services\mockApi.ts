// Mock API Service para desenvolvimento sem Flask
// Simula as respostas da API real

export interface ApiResponse<T = any> {
  sucesso: boolean;
  mensagem: string;
  dados?: T;
  user?: any;
  token?: string;
}

export interface PlayerData {
  uid: string;
  nick: string;
  email: string;
  pontos: number;
  nivel: number;
  conquistas: number;
  ranking: number;
  grupo?: string;
  ultimo_acesso: string;
  // Propriedades do jogo
  cpu?: number;
  ram?: number;
  firewall?: number;
  antivirus?: number;
  malware_kit?: number;
  bruteforce?: number;
  bankguard?: number;
  proxyvpn?: number;
  dinheiro?: number;
  ip?: string;
}

class MockApiService {
  private delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  // Simular delay de rede
  private async simulateNetworkDelay(): Promise<void> {
    await this.delay(Math.random() * 1000 + 500); // 500-1500ms
  }

  // === AUTENTICAÇÃO ===
  async login(credentials: { email: string; password: string }): Promise<ApiResponse> {
    await this.simulateNetworkDelay();
    
    console.log('MockAPI - Login:', credentials);
    
    // Simular validação
    if (!credentials.email || !credentials.password) {
      return {
        sucesso: false,
        mensagem: 'Email e senha são obrigatórios'
      };
    }

    // Simular usuário válido
    const mockUser = {
      uid: 'mock-user-' + Date.now(),
      nick: credentials.email.split('@')[0] || 'Player',
      email: credentials.email
    };

    const mockToken = 'mock-jwt-token-' + Date.now();

    return {
      sucesso: true,
      mensagem: 'Login realizado com sucesso',
      user: mockUser,
      token: mockToken
    };
  }

  async register(userData: { email: string; password: string; nick: string }): Promise<ApiResponse> {
    await this.simulateNetworkDelay();
    
    console.log('MockAPI - Register:', userData);
    
    // Simular validação
    if (!userData.email || !userData.password || !userData.nick) {
      return {
        sucesso: false,
        mensagem: 'Todos os campos são obrigatórios'
      };
    }

    // Simular criação de usuário
    const mockUser = {
      uid: 'mock-user-' + Date.now(),
      nick: userData.nick,
      email: userData.email
    };

    const mockToken = 'mock-jwt-token-' + Date.now();

    return {
      sucesso: true,
      mensagem: 'Conta criada com sucesso',
      user: mockUser,
      token: mockToken
    };
  }

  async verifyToken(): Promise<boolean> {
    await this.simulateNetworkDelay();
    
    console.log('MockAPI - Verificando token...');
    
    // Simular token sempre válido para desenvolvimento
    return true;
  }

  // === DADOS DO JOGADOR ===
  async getPlayerData(): Promise<ApiResponse<PlayerData>> {
    await this.simulateNetworkDelay();
    
    console.log('MockAPI - Carregando dados do jogador...');
    
    const mockPlayerData: PlayerData = {
      uid: 'mock-player-123',
      nick: 'TestPlayer',
      email: '<EMAIL>',
      pontos: 1250,
      nivel: 15,
      conquistas: 42,
      ranking: 7,
      grupo: 'Alpha',
      ultimo_acesso: new Date().toISOString(),
      // Propriedades do jogo
      cpu: 5,
      ram: 4,
      firewall: 3,
      antivirus: 2,
      malware_kit: 3,
      bruteforce: 2,
      bankguard: 1,
      proxyvpn: 1,
      dinheiro: 50000,
      ip: '*************'
    };

    return {
      sucesso: true,
      mensagem: 'Dados carregados com sucesso',
      dados: mockPlayerData
    };
  }

  // === CHAT ===
  async getChatMessages(): Promise<ApiResponse> {
    await this.simulateNetworkDelay();
    
    const mockMessages = [
      {
        id: 1,
        usuario: 'Player1',
        mensagem: 'Olá pessoal!',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        tipo: 'global'
      },
      {
        id: 2,
        usuario: 'Player2',
        mensagem: 'Como estão?',
        timestamp: new Date(Date.now() - 180000).toISOString(),
        tipo: 'global'
      },
      {
        id: 3,
        usuario: 'TestPlayer',
        mensagem: 'Tudo bem por aqui!',
        timestamp: new Date(Date.now() - 60000).toISOString(),
        tipo: 'global'
      }
    ];

    return {
      sucesso: true,
      mensagem: 'Mensagens carregadas',
      dados: mockMessages
    };
  }

  async sendChatMessage(message: { mensagem: string; tipo: string }): Promise<ApiResponse> {
    await this.simulateNetworkDelay();
    
    console.log('MockAPI - Enviando mensagem:', message);
    
    return {
      sucesso: true,
      mensagem: 'Mensagem enviada com sucesso'
    };
  }

  // === SCANNER ===
  async scanTarget(target: string): Promise<ApiResponse> {
    await this.simulateNetworkDelay();
    
    console.log('MockAPI - Escaneando alvo:', target);
    
    // Simular resultado de scan
    const mockScanResult = {
      alvo: target,
      vulnerabilidades: Math.floor(Math.random() * 5) + 1,
      dificuldade: ['Fácil', 'Médio', 'Difícil'][Math.floor(Math.random() * 3)],
      pontos_potenciais: Math.floor(Math.random() * 500) + 100,
      tempo_estimado: Math.floor(Math.random() * 30) + 5 + ' minutos'
    };

    return {
      sucesso: true,
      mensagem: 'Scan realizado com sucesso',
      dados: mockScanResult
    };
  }

  // === LOJA ===
  async getShopItems(): Promise<ApiResponse> {
    await this.simulateNetworkDelay();
    
    const mockItems = [
      {
        id: 1,
        nome: 'Scanner Avançado',
        descricao: 'Melhora a precisão dos scans',
        preco: 500,
        tipo: 'ferramenta'
      },
      {
        id: 2,
        nome: 'Boost de XP',
        descricao: 'Dobra a experiência por 1 hora',
        preco: 200,
        tipo: 'boost'
      },
      {
        id: 3,
        nome: 'Proteção Extra',
        descricao: 'Reduz danos recebidos',
        preco: 750,
        tipo: 'defesa'
      }
    ];

    return {
      sucesso: true,
      mensagem: 'Itens da loja carregados',
      dados: mockItems
    };
  }
}

// Instância singleton
const mockApiService = new MockApiService();
export default mockApiService;
