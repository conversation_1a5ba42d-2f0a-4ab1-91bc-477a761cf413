# 🛠️ React Implementation Guide

## 1. Project Setup Commands

```bash
# Create React app
npx create-react-app shack-frontend --template typescript
cd shack-frontend

# Install dependencies
npm install axios react-router-dom @tanstack/react-query zustand
npm install socket.io-client @headlessui/react @heroicons/react
npm install tailwindcss @tailwindcss/forms @tailwindcss/typography
npm install -D @types/node

# Initialize Tailwind
npx tailwindcss init -p
```

## 2. Flask Backend Modifications

### 2.1 CORS Setup
```python
# app.py or __init__.py
from flask_cors import CORS

app = Flask(__name__)
CORS(app, origins=["http://localhost:3000"])  # React dev server
```

### 2.2 API Response Standardization
```python
# utils/api_response.py
def api_response(success=True, data=None, message=None, status_code=200):
    response = {
        'sucesso': success,
        'mensagem': message,
        'data': data
    }
    return jsonify(response), status_code
```

## 3. Core React Components

### 3.1 App Component
```jsx
// src/App.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AuthGuard from './components/auth/AuthGuard';
import LoginPage from './pages/LoginPage';
import GamePage from './pages/GamePage';
import './styles/globals.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30000, // 30 seconds
      cacheTime: 300000, // 5 minutes
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-bg-primary text-text-primary">
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route
              path="/*"
              element={
                <AuthGuard>
                  <GamePage />
                </AuthGuard>
              }
            />
          </Routes>
        </div>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
```

### 3.2 Authentication Guard
```jsx
// src/components/auth/AuthGuard.tsx
import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { isAuthenticated, checkAuth, isLoading } = useAuthStore();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-blue"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

export default AuthGuard;
```

### 3.3 Game Layout
```jsx
// src/components/layout/GameLayout.tsx
import React, { useState } from 'react';
import Header from './Header';
import Navigation from './Navigation';
import ChatSystem from '../chat/ChatSystem';

interface GameLayoutProps {
  children: React.ReactNode;
}

const GameLayout: React.FC<GameLayoutProps> = ({ children }) => {
  const [isChatOpen, setIsChatOpen] = useState(false);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 p-4 overflow-y-auto">
        {children}
      </main>
      
      <Navigation />
      
      <ChatSystem 
        isOpen={isChatOpen} 
        onToggle={() => setIsChatOpen(!isChatOpen)} 
      />
    </div>
  );
};

export default GameLayout;
```

## 4. State Management

### 4.1 Auth Store
```typescript
// src/stores/authStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import ApiService from '../services/api';

interface User {
  uid: string;
  nick: string;
  email: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  login: (credentials: { email: string; password: string }) => Promise<void>;
  logout: () => void;
  checkAuth: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials) => {
        set({ isLoading: true });
        try {
          const response = await ApiService.login(credentials);
          if (response.sucesso) {
            set({
              user: response.user,
              token: response.token,
              isAuthenticated: true,
              isLoading: false,
            });
          } else {
            throw new Error(response.mensagem);
          }
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        });
      },

      checkAuth: () => {
        const { token } = get();
        if (token) {
          // Verify token validity
          ApiService.verifyToken(token)
            .then((valid) => {
              if (!valid) {
                get().logout();
              }
            })
            .catch(() => {
              get().logout();
            });
        }
      },
    }),
    {
      name: 'shack-auth',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
```

### 4.2 Game Store
```typescript
// src/stores/gameStore.ts
import { create } from 'zustand';
import ApiService from '../services/api';

interface Player {
  uid: string;
  nick: string;
  dinheiro: number;
  cpu: number;
  firewall: number;
  // ... other player properties
}

interface GameState {
  currentPlayer: Player | null;
  exploitedTarget: any | null;
  isLoading: boolean;
  
  loadPlayerData: () => Promise<void>;
  updatePlayer: (updates: Partial<Player>) => void;
  setExploitedTarget: (target: any) => void;
}

export const useGameStore = create<GameState>((set, get) => ({
  currentPlayer: null,
  exploitedTarget: null,
  isLoading: false,

  loadPlayerData: async () => {
    set({ isLoading: true });
    try {
      const response = await ApiService.getPlayerData();
      if (response.sucesso) {
        set({
          currentPlayer: response.jogador,
          isLoading: false,
        });
      }
    } catch (error) {
      set({ isLoading: false });
      console.error('Failed to load player data:', error);
    }
  },

  updatePlayer: (updates) => {
    set((state) => ({
      currentPlayer: state.currentPlayer
        ? { ...state.currentPlayer, ...updates }
        : null,
    }));
  },

  setExploitedTarget: (target) => {
    set({ exploitedTarget: target });
  },
}));
```

## 5. API Service Layer

### 5.1 Base API Service
```typescript
// src/services/api.ts
import axios, { AxiosInstance, AxiosResponse } from 'axios';

interface ApiResponse<T = any> {
  sucesso: boolean;
  mensagem?: string;
  data?: T;
  [key: string]: any;
}

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use((config) => {
      const token = localStorage.getItem('shack_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          localStorage.removeItem('shack_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth methods
  async login(credentials: { email: string; password: string }): Promise<ApiResponse> {
    const response = await this.client.post('/api/auth/login', credentials);
    return response.data;
  }

  async register(userData: { email: string; password: string; nick: string }): Promise<ApiResponse> {
    const response = await this.client.post('/api/auth/register', userData);
    return response.data;
  }

  // Game methods
  async getPlayerData(): Promise<ApiResponse> {
    const response = await this.client.get('/api/jogador');
    return response.data;
  }

  async scanTargets(): Promise<ApiResponse> {
    const response = await this.client.get('/api/scan');
    return response.data;
  }

  async exploitTarget(targetIp: string, percentage: number): Promise<ApiResponse> {
    const response = await this.client.post('/api/transferir', {
      alvo_ip: targetIp,
      porcentagem: percentage,
    });
    return response.data;
  }

  // Chat methods
  async getChatMessages(): Promise<ApiResponse> {
    const response = await this.client.get('/api/chat/messages');
    return response.data;
  }

  async sendChatMessage(message: string): Promise<ApiResponse> {
    const response = await this.client.post('/api/chat/send', { message });
    return response.data;
  }
}

export default new ApiService();
```

## 6. Custom Hooks

### 6.1 Game Data Hook
```typescript
// src/hooks/useGameData.ts
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useGameStore } from '../stores/gameStore';
import ApiService from '../services/api';

export const useGameData = () => {
  const queryClient = useQueryClient();
  const { currentPlayer, updatePlayer } = useGameStore();

  const {
    data: playerData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['playerData'],
    queryFn: async () => {
      const response = await ApiService.getPlayerData();
      if (response.sucesso) {
        updatePlayer(response.jogador);
        return response.jogador;
      }
      throw new Error(response.mensagem);
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const invalidatePlayerData = () => {
    queryClient.invalidateQueries({ queryKey: ['playerData'] });
  };

  return {
    playerData: currentPlayer || playerData,
    isLoading,
    error,
    refetch,
    invalidatePlayerData,
  };
};
```

### 6.2 Chat Hook
```typescript
// src/hooks/useChat.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import ApiService from '../services/api';

export const useChat = () => {
  const queryClient = useQueryClient();

  const {
    data: messages = [],
    isLoading,
  } = useQuery({
    queryKey: ['chatMessages'],
    queryFn: async () => {
      const response = await ApiService.getChatMessages();
      return response.sucesso ? response.messages : [];
    },
    refetchInterval: 2000, // Poll every 2 seconds
  });

  const sendMessageMutation = useMutation({
    mutationFn: (message: string) => ApiService.sendChatMessage(message),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chatMessages'] });
    },
  });

  return {
    messages,
    isLoading,
    sendMessage: sendMessageMutation.mutate,
    isSending: sendMessageMutation.isPending,
  };
};
```
