import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSimpleAuth } from '../stores/simpleAuthStore';
import { usePlayer } from '../stores/playerStore';
import ModernCard from '../components/ui/ModernCard';
import GlowingIcon from '../components/ui/GlowingIcon';
import AnimatedBackground from '../components/ui/AnimatedBackground';

// Layout principal do jogo - Estilo celular antigo
const GameMainPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useSimpleAuth();
  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();

  useEffect(() => {
    if (!hasPlayerData && !isLoadingPlayer) {
      console.log('GameMainPage - Carregando dados do jogador...');
      loadPlayerData();
    }
  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);

  // Usar dados do player se disponível, senão usar dados mockados
  const playerData = currentPlayer || {
    pontos: 1250,
    nivel: 1,
    dinheiro: 0,
    ip: '**************'
  };

  const handleIconClick = (route: string) => {
    navigate(route);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900/20 to-purple-900/20 text-white flex flex-col relative overflow-hidden">
      {/* Background animado */}
      <AnimatedBackground variant="particles" />

      {/* Header moderno */}
      <ModernCard variant="glass" className="m-4 p-4 flex-shrink-0 animate-fade-in-up" glow>
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <GlowingIcon color="green" size="sm" glow pulse className="animate-float">
              <span className="text-xs font-bold">S</span>
            </GlowingIcon>
            <div>
              <div className="text-lg font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent neon-blue">
                {user?.nick || 'Ivo77'}
              </div>
              <div className="text-xs text-gray-400">IP: {playerData.ip}</div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-blue-400 text-glow">Nível {playerData.nivel}</div>
            <div className="text-sm text-green-400 font-semibold neon-green">
              ${playerData.dinheiro || 0}
            </div>
          </div>
        </div>
      </ModernCard>

      {/* Grid principal de ícones */}
      <div className="flex-1 p-6 overflow-y-auto relative z-10 custom-scrollbar">
        <div className="grid grid-cols-3 gap-6 max-w-2xl mx-auto">
          {/* Primeira linha */}
          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-1"
            onClick={() => handleIconClick('/game/scanner')}
            glow
          >
            <GlowingIcon color="blue" size="md" className="mx-auto mb-2 animate-pulse-glow">
              🔍
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Scan</span>
          </ModernCard>

          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-2"
            onClick={() => handleIconClick('/game/apps')}
            glow
          >
            <GlowingIcon color="purple" size="md" className="mx-auto mb-2 animate-pulse-glow">
              💻
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Apps</span>
          </ModernCard>

          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-3"
            onClick={() => handleIconClick('/game/upgrades')}
            glow
          >
            <GlowingIcon color="yellow" size="md" className="mx-auto mb-2 animate-pulse-glow">
              ⚙️
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Upgrades</span>
          </ModernCard>

          {/* Segunda linha */}
          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-4"
            onClick={() => handleIconClick('/game/chat')}
            glow
          >
            <GlowingIcon color="green" size="md" className="mx-auto mb-2 animate-pulse-glow">
              📧
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Chat</span>
          </ModernCard>

          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-5"
            onClick={() => handleIconClick('/game/transfer')}
            glow
          >
            <GlowingIcon color="cyan" size="md" className="mx-auto mb-2 animate-pulse-glow">
              💰
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Transfer</span>
          </ModernCard>

          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-6"
            onClick={() => handleIconClick('/game/shop')}
            glow
          >
            <GlowingIcon color="pink" size="md" className="mx-auto mb-2 animate-pulse-glow">
              🛒
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Loja</span>
          </ModernCard>

          {/* Terceira linha */}
          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-7"
            onClick={() => handleIconClick('/game/logs')}
            glow
          >
            <GlowingIcon color="blue" size="md" className="mx-auto mb-2 animate-pulse-glow">
              📊
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Logs</span>
          </ModernCard>

          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-8"
            onClick={() => handleIconClick('/game/ranking')}
            glow
          >
            <GlowingIcon color="yellow" size="md" className="mx-auto mb-2 animate-pulse-glow">
              🏆
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Ranking</span>
          </ModernCard>

          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-9"
            onClick={() => handleIconClick('/game/bank')}
            glow
          >
            <GlowingIcon color="green" size="md" className="mx-auto mb-2 animate-pulse-glow">
              🏦
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Banco</span>
          </ModernCard>

          {/* Quarta linha */}
          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-7"
            onClick={() => handleIconClick('/game/mining')}
            glow
          >
            <GlowingIcon color="purple" size="md" className="mx-auto mb-2 animate-pulse-glow">
              ⛏️
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Mineração</span>
          </ModernCard>

          <ModernCard
            variant="neon"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-8 animate-glow"
            onClick={() => handleIconClick('/game/terminal')}
            glow
          >
            <GlowingIcon color="cyan" size="md" className="mx-auto mb-2 animate-pulse-glow">
              💻
            </GlowingIcon>
            <span className="text-sm font-medium text-cyan-200 neon-blue">Terminal</span>
          </ModernCard>

          <ModernCard
            variant="glass"
            className="p-4 text-center hover-lift animate-fade-in-scale stagger-9"
            onClick={() => handleIconClick('/game/supporters')}
            glow
          >
            <GlowingIcon color="red" size="md" className="mx-auto mb-2 animate-pulse-glow">
              ❤️
            </GlowingIcon>
            <span className="text-sm font-medium text-gray-200">Supporters</span>
          </ModernCard>
        </div>
      </div>

      {/* Footer moderno com navegação principal */}
      <ModernCard variant="glass" className="m-4 p-4 flex-shrink-0 animate-slide-in-right" glow>
        <div className="flex justify-around items-center">
          <button
            className="flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow"
            onClick={() => handleIconClick('/game')}
          >
            <GlowingIcon color="blue" size="sm" className="animate-float">
              🏠
            </GlowingIcon>
            <span className="text-xs font-medium">Home</span>
          </button>
          
          <button
            className="flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow"
            onClick={() => handleIconClick('/game/chat')}
          >
            <GlowingIcon color="green" size="sm" className="animate-float">
              💬
            </GlowingIcon>
            <span className="text-xs font-medium">Chat</span>
          </button>

          <button
            className="flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow"
            onClick={() => handleIconClick('/game/scanner')}
          >
            <GlowingIcon color="purple" size="sm" className="animate-float">
              🔍
            </GlowingIcon>
            <span className="text-xs font-medium">Scan</span>
          </button>

          <button
            className="flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow"
            onClick={() => handleIconClick('/game/transfer')}
          >
            <GlowingIcon color="cyan" size="sm" className="animate-float">
              💰
            </GlowingIcon>
            <span className="text-xs font-medium">Transfer</span>
          </button>

          <button
            className="flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow"
            onClick={() => handleIconClick('/game/config')}
          >
            <GlowingIcon color="yellow" size="sm" className="animate-float">
              ⚙️
            </GlowingIcon>
            <span className="text-xs font-medium">Config</span>
          </button>
        </div>
      </ModernCard>
    </div>
  );
};

export default GameMainPage;
