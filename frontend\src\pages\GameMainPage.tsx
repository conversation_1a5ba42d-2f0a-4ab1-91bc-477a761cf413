import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSimpleAuth } from '../stores/simpleAuthStore';
import { usePlayer } from '../stores/playerStore';

// Layout principal do jogo - Estilo celular antigo
const GameMainPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useSimpleAuth();
  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();

  useEffect(() => {
    if (!hasPlayerData && !isLoadingPlayer) {
      console.log('GameMainPage - Carregando dados do jogador...');
      loadPlayerData();
    }
  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);

  // Usar dados do player se disponível, senão usar dados mockados
  const playerData = currentPlayer || {
    pontos: 1250,
    nivel: 1,
    dinheiro: 0,
    ip: '**************'
  };

  const handleIconClick = (route: string) => {
    navigate(route);
  };

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* Header com informações do jogador */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-400 rounded-full"></div>
            <span className="text-sm font-mono">{user?.nick || 'Ivo77'}</span>
          </div>
          <div className="text-right">
            <div className="text-sm font-mono">IP: {playerData.ip}</div>
            <div className="text-xs text-gray-400">Nível: {playerData.nivel}</div>
            <div className="text-xs text-gray-400">${playerData.dinheiro || 0} / 500</div>
          </div>
        </div>
      </div>

      {/* Grid principal de ícones */}
      <div className="flex-1 p-8 overflow-y-auto">
        <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
          {/* Primeira linha */}
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/scanner')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">🔍</span>
            </div>
            <span className="text-sm text-gray-300">Scan</span>
          </div>
          
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/apps')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">💻</span>
            </div>
            <span className="text-sm text-gray-300">Apps</span>
          </div>
          
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/gears')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">⚙️</span>
            </div>
            <span className="text-sm text-gray-300">Gears</span>
          </div>

          {/* Segunda linha */}
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/chat')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">📧</span>
            </div>
            <span className="text-sm text-gray-300">Mensagem</span>
          </div>
          
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/market')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">🛒</span>
            </div>
            <span className="text-sm text-gray-300">Mercado</span>
          </div>
          
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/skills')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">🔧</span>
            </div>
            <span className="text-sm text-gray-300">Habilidades</span>
          </div>

          {/* Terceira linha */}
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/logs')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">📊</span>
            </div>
            <span className="text-sm text-gray-300">Logs</span>
          </div>
          
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/ranking')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">🏆</span>
            </div>
            <span className="text-sm text-gray-300">Ranking</span>
          </div>
          
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/bank')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">🏪</span>
            </div>
            <span className="text-sm text-gray-300">BANK</span>
          </div>

          {/* Quarta linha */}
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/config')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">⚙️</span>
            </div>
            <span className="text-sm text-gray-300">CONFIG</span>
          </div>
          
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/terminal')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">💻</span>
            </div>
            <span className="text-sm text-gray-300">Terminal</span>
          </div>
          
          <div 
            className="flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors"
            onClick={() => handleIconClick('/game/supporters')}
          >
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-2xl">❤️</span>
            </div>
            <span className="text-sm text-gray-300">Supporters</span>
          </div>
        </div>
      </div>

      {/* Footer com botões de navegação */}
      <div className="bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0">
        <div className="flex justify-around items-center max-w-md mx-auto">
          <button 
            className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors"
            onClick={() => handleIconClick('/game')}
          >
            <div className="w-6 h-6 flex items-center justify-center">
              <span className="text-lg">🏠</span>
            </div>
            <span className="text-xs">Home</span>
          </button>
          
          <button 
            className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors"
            onClick={() => handleIconClick('/game/chat')}
          >
            <div className="w-6 h-6 flex items-center justify-center">
              <span className="text-lg">💬</span>
            </div>
            <span className="text-xs">Chat</span>
          </button>
          
          <button 
            className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors"
            onClick={() => handleIconClick('/game/scanner')}
          >
            <div className="w-6 h-6 flex items-center justify-center">
              <span className="text-lg">🔍</span>
            </div>
            <span className="text-xs">Scan</span>
          </button>
          
          <button 
            className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors"
            onClick={() => handleIconClick('/game/market')}
          >
            <div className="w-6 h-6 flex items-center justify-center">
              <span className="text-lg">🛒</span>
            </div>
            <span className="text-xs">Market</span>
          </button>
          
          <button 
            className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors"
            onClick={() => handleIconClick('/game/config')}
          >
            <div className="w-6 h-6 flex items-center justify-center">
              <span className="text-lg">⚙️</span>
            </div>
            <span className="text-xs">Config</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameMainPage;
