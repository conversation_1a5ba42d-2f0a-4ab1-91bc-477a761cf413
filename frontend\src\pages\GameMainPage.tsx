import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSimpleAuth } from '../stores/simpleAuthStore';
import { usePlayer } from '../stores/playerStore';
import GameFooter from '../components/common/GameFooter';
import PlayerProfile from '../components/ui/PlayerProfile';
import {
  ScanIcon,
  AppsIcon,
  ConfigIcon,
  ChatIcon,
  ShopIcon,
  TerminalIcon,
  MiningIcon,
  LogsIcon,
  RankingIcon,
  BankIcon,
  SkillsIcon,
  SecurityIcon
} from '../components/ui/GameIcons';

// Layout principal do jogo - Estilo celular antigo
const GameMainPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useSimpleAuth();
  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();

  useEffect(() => {
    if (!hasPlayerData && !isLoadingPlayer) {
      console.log('GameMainPage - Carregando dados do jogador...');
      loadPlayerData();
    }
  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);

  // Usar dados do player se disponível, senão usar dados mockados
  const playerData = currentPlayer || {
    pontos: 1250,
    nivel: 1,
    dinheiro: 0,
    ip: '**************',
    shack: 38
  };

  const handleIconClick = (route: string) => {
    navigate(route);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white flex flex-col">
      {/* Header com logo */}
      <div className="bg-gradient-to-r from-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-gray-700/50 p-4 flex-shrink-0">
        <div className="flex items-center justify-center space-x-2">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-sm">S</span>
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                SHACK WEB
              </h1>
              <p className="text-xs text-gray-400">Versão de desenvolvimento</p>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {/* Chat Global */}
        <div className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-lg p-3 border border-gray-700/50">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-400">Chat Global</span>
            <span className="text-xs text-gray-500">|</span>
            <span className="text-xs text-blue-400 font-semibold">Ceifador:</span>
            <span className="text-xs text-gray-300">Td pobre pqp</span>
            <div className="ml-auto flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-gray-500">Online</span>
            </div>
          </div>
        </div>

        {/* Perfil do jogador */}
        <PlayerProfile
          nick={user?.nick || 'zakedev'}
          ip={playerData.ip || '*************'}
          level={playerData.nivel}
          cash={playerData.dinheiro || 3241}
          shack={playerData.shack || 38}
          exp={750}
          maxExp={2900}
        />

        {/* Grid de aplicativos */}
        <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50">
          <div className="grid grid-cols-3 gap-4">
            {/* Primeira linha */}
            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/scanner')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-blue-400 group-hover:text-blue-300 transition-colors">
                <ScanIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">Scan</span>
            </button>

            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/apps')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-purple-400 group-hover:text-purple-300 transition-colors">
                <AppsIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">Apps</span>
            </button>

            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/chat')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-green-400 group-hover:text-green-300 transition-colors">
                <ChatIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">Grupo</span>
            </button>

            {/* Segunda linha */}
            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/mining')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-yellow-400 group-hover:text-yellow-300 transition-colors">
                <MiningIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">Mineração</span>
            </button>

            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/shop')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-cyan-400 group-hover:text-cyan-300 transition-colors">
                <ShopIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">Mercado</span>
            </button>

            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/apps')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-pink-400 group-hover:text-pink-300 transition-colors">
                <SkillsIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">Habilidades</span>
            </button>

            {/* Terceira linha */}
            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/logs')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-blue-400 group-hover:text-blue-300 transition-colors">
                <LogsIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">Logs</span>
            </button>

            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/ranking')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-yellow-400 group-hover:text-yellow-300 transition-colors">
                <RankingIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">Ranking</span>
            </button>

            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/bank')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-green-400 group-hover:text-green-300 transition-colors">
                <BankIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">BANK</span>
            </button>

            {/* Quarta linha */}
            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/config')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-gray-400 group-hover:text-gray-300 transition-colors">
                <ConfigIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">CONFIG</span>
            </button>

            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/terminal')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-cyan-400 group-hover:text-cyan-300 transition-colors">
                <TerminalIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">Terminal</span>
            </button>

            <button
              className="flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group"
              onClick={() => handleIconClick('/game/security')}
            >
              <div className="w-12 h-12 flex items-center justify-center mb-2 text-red-400 group-hover:text-red-300 transition-colors">
                <SecurityIcon size={32} />
              </div>
              <span className="text-sm font-medium text-gray-200 group-hover:text-white transition-colors">Segurança</span>
            </button>
          </div>
        </div>
      </div>

      {/* Footer com navegação */}
      <GameFooter currentPage="home" />
    </div>
  );
};

export default GameMainPage;
