import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSimpleAuth } from '../stores/simpleAuthStore';
import { usePlayer } from '../stores/playerStore';
import GameFooter from '../components/common/GameFooter';

// Layout principal do jogo - Estilo celular antigo
const GameMainPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useSimpleAuth();
  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();

  useEffect(() => {
    if (!hasPlayerData && !isLoadingPlayer) {
      console.log('GameMainPage - Carregando dados do jogador...');
      loadPlayerData();
    }
  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);

  // Usar dados do player se disponível, senão usar dados mockados
  const playerData = currentPlayer || {
    pontos: 1250,
    nivel: 1,
    dinheiro: 0,
    ip: '**************'
  };

  const handleIconClick = (route: string) => {
    navigate(route);
  };

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
              <span className="text-xs font-bold">S</span>
            </div>
            <div>
              <div className="text-lg font-bold">{user?.nick || 'Ivo77'}</div>
              <div className="text-xs text-gray-400">IP: {playerData.ip}</div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-blue-400">Nível {playerData.nivel}</div>
            <div className="text-sm text-green-400 font-semibold">
              ${playerData.dinheiro || 0}
            </div>
          </div>
        </div>
      </div>

      {/* Grid principal de ícones */}
      <div className="flex-1 p-6 overflow-y-auto">
        <div className="grid grid-cols-3 gap-6 max-w-2xl mx-auto">
          {/* Primeira linha */}
          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/scanner')}
          >
            <div className="text-3xl mb-2">🔍</div>
            <span className="text-sm font-medium text-gray-200">Scan</span>
          </div>

          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/apps')}
          >
            <div className="text-3xl mb-2">💻</div>
            <span className="text-sm font-medium text-gray-200">Apps</span>
          </div>

          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/upgrades')}
          >
            <div className="text-3xl mb-2">⚙️</div>
            <span className="text-sm font-medium text-gray-200">Upgrades</span>
          </div>

          {/* Segunda linha */}
          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/chat')}
          >
            <div className="text-3xl mb-2">📧</div>
            <span className="text-sm font-medium text-gray-200">Chat</span>
          </div>

          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/shop')}
          >
            <div className="text-3xl mb-2">🛒</div>
            <span className="text-sm font-medium text-gray-200">Loja</span>
          </div>

          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/terminal')}
          >
            <div className="text-3xl mb-2">💻</div>
            <span className="text-sm font-medium text-gray-200">Terminal</span>
          </div>

          {/* Terceira linha */}
          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/logs')}
          >
            <div className="text-3xl mb-2">📊</div>
            <span className="text-sm font-medium text-gray-200">Logs</span>
          </div>

          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/ranking')}
          >
            <div className="text-3xl mb-2">🏆</div>
            <span className="text-sm font-medium text-gray-200">Ranking</span>
          </div>

          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/bank')}
          >
            <div className="text-3xl mb-2">🏦</div>
            <span className="text-sm font-medium text-gray-200">Banco</span>
          </div>

          {/* Quarta linha */}
          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/mining')}
          >
            <div className="text-3xl mb-2">⛏️</div>
            <span className="text-sm font-medium text-gray-200">Mineração</span>
          </div>

          <div
            className="bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => handleIconClick('/game/supporters')}
          >
            <div className="text-3xl mb-2">❤️</div>
            <span className="text-sm font-medium text-gray-200">Supporters</span>
          </div>

          {/* Espaço vazio para manter o grid 3x4 */}
          <div className="bg-gray-900 rounded-lg p-4 opacity-50">
            <div className="text-3xl mb-2 text-gray-600">📱</div>
            <span className="text-sm font-medium text-gray-600">Em breve</span>
          </div>
        </div>
      </div>

      {/* Footer com navegação */}
      <GameFooter currentPage="home" />
    </div>
  );
};

export default GameMainPage;
