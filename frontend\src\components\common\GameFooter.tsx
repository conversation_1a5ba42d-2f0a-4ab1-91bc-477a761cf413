import React from 'react';
import { useNavigate } from 'react-router-dom';

interface GameFooterProps {
  currentPage?: string;
}

const GameFooter: React.FC<GameFooterProps> = ({ currentPage = '' }) => {
  const navigate = useNavigate();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <div className="bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0">
      <div className="flex justify-around items-center max-w-md mx-auto">
        <button 
          className={`flex flex-col items-center space-y-1 transition-colors ${
            currentPage === 'scanner' ? 'text-blue-400' : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => handleNavigation('/game/scanner')}
        >
          <div className="w-6 h-6 flex items-center justify-center">
            <span className="text-lg">🔍</span>
          </div>
          <span className="text-xs">Scan</span>
        </button>

        <button 
          className={`flex flex-col items-center space-y-1 transition-colors ${
            currentPage === 'chat' ? 'text-blue-400' : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => handleNavigation('/game/chat')}
        >
          <div className="w-6 h-6 flex items-center justify-center">
            <span className="text-lg">💬</span>
          </div>
          <span className="text-xs">Chat</span>
        </button>
        
        {/* Botão Home no centro */}
        <button 
          className={`flex flex-col items-center space-y-1 transition-colors ${
            currentPage === 'home' ? 'text-blue-300' : 'text-blue-400 hover:text-blue-300'
          }`}
          onClick={() => handleNavigation('/game')}
        >
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            currentPage === 'home' ? 'bg-blue-500' : 'bg-blue-600'
          }`}>
            <span className="text-lg">🏠</span>
          </div>
          <span className="text-xs font-semibold">Home</span>
        </button>
        
        <button
          className={`flex flex-col items-center space-y-1 transition-colors ${
            currentPage === 'terminal' ? 'text-blue-400' : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => handleNavigation('/game/terminal')}
        >
          <div className="w-6 h-6 flex items-center justify-center">
            <span className="text-lg">💻</span>
          </div>
          <span className="text-xs">Terminal</span>
        </button>
        
        <button
          className={`flex flex-col items-center space-y-1 transition-colors ${
            currentPage === 'config' ? 'text-blue-400' : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => handleNavigation('/game/config')}
        >
          <div className="w-6 h-6 flex items-center justify-center">
            <span className="text-lg">⚙️</span>
          </div>
          <span className="text-xs">Config</span>
        </button>
      </div>
    </div>
  );
};

export default GameFooter;
