import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ScanIcon, ChatIcon, HomeIcon, TerminalIcon, ConfigIcon, ShopIcon, NewsIcon } from '../ui/GameIcons';

interface GameFooterProps {
  currentPage?: string;
}

const GameFooter: React.FC<GameFooterProps> = ({ currentPage = '' }) => {
  const navigate = useNavigate();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <div className="bg-gray-900/95 backdrop-blur-sm border-t border-gray-700/50 p-3 flex-shrink-0">
      <div className="flex justify-around items-center max-w-md mx-auto">
        <button
          className={`flex flex-col items-center space-y-1 transition-all duration-200 ${
            currentPage === 'scanner' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'
          }`}
          onClick={() => handleNavigation('/game/scanner')}
        >
          <div className="w-6 h-6 flex items-center justify-center">
            <ScanIcon size={20} />
          </div>
          <span className="text-xs font-medium">Scan</span>
        </button>

        <button
          className={`flex flex-col items-center space-y-1 transition-all duration-200 ${
            currentPage === 'shop' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'
          }`}
          onClick={() => handleNavigation('/game/shop')}
        >
          <div className="w-6 h-6 flex items-center justify-center">
            <ShopIcon size={20} />
          </div>
          <span className="text-xs font-medium">Shop</span>
        </button>

        {/* Botão Home no centro */}
        <button
          className={`flex flex-col items-center space-y-1 transition-all duration-200 ${
            currentPage === 'home' ? 'text-white scale-110' : 'text-blue-400 hover:text-white hover:scale-105'
          }`}
          onClick={() => handleNavigation('/game')}
        >
          <div className={`w-10 h-10 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 ${
            currentPage === 'home'
              ? 'bg-gradient-to-br from-blue-500 to-purple-600 shadow-blue-500/50'
              : 'bg-gradient-to-br from-blue-600 to-purple-700 hover:from-blue-500 hover:to-purple-600'
          }`}>
            <HomeIcon size={24} className="text-white" />
          </div>
          <span className="text-xs font-semibold">Home</span>
        </button>

        <button
          className={`flex flex-col items-center space-y-1 transition-all duration-200 ${
            currentPage === 'apps' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'
          }`}
          onClick={() => handleNavigation('/game/apps')}
        >
          <div className="w-6 h-6 flex items-center justify-center">
            <NewsIcon size={20} />
          </div>
          <span className="text-xs font-medium">Apps</span>
        </button>

        <button
          className={`flex flex-col items-center space-y-1 transition-all duration-200 ${
            currentPage === 'config' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'
          }`}
          onClick={() => handleNavigation('/game/config')}
        >
          <div className="w-6 h-6 flex items-center justify-center">
            <ConfigIcon size={20} />
          </div>
          <span className="text-xs font-medium">Config</span>
        </button>
      </div>
    </div>
  );
};

export default GameFooter;
