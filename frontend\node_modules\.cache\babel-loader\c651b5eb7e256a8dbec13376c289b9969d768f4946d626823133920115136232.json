{"ast": null, "code": "\"use client\";\n\n// src/HydrationBoundary.tsx\nimport * as React from \"react\";\nimport { hydrate } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nvar HydrationBoundary = ({\n  children,\n  options = {},\n  state,\n  queryClient\n}) => {\n  const client = useQueryClient(queryClient);\n  const optionsRef = React.useRef(options);\n  optionsRef.current = options;\n  const hydrationQueue = React.useMemo(() => {\n    if (state) {\n      if (typeof state !== \"object\") {\n        return;\n      }\n      const queryCache = client.getQueryCache();\n      const queries = state.queries || [];\n      const newQueries = [];\n      const existingQueries = [];\n      for (const dehydratedQuery of queries) {\n        const existingQuery = queryCache.get(dehydratedQuery.queryHash);\n        if (!existingQuery) {\n          newQueries.push(dehydratedQuery);\n        } else {\n          const hydrationIsNewer = dehydratedQuery.state.dataUpdatedAt > existingQuery.state.dataUpdatedAt || dehydratedQuery.promise && existingQuery.state.status !== \"pending\" && existingQuery.state.fetchStatus !== \"fetching\" && dehydratedQuery.dehydratedAt !== void 0 && dehydratedQuery.dehydratedAt > existingQuery.state.dataUpdatedAt;\n          if (hydrationIsNewer) {\n            existingQueries.push(dehydratedQuery);\n          }\n        }\n      }\n      if (newQueries.length > 0) {\n        hydrate(client, {\n          queries: newQueries\n        }, optionsRef.current);\n      }\n      if (existingQueries.length > 0) {\n        return existingQueries;\n      }\n    }\n    return void 0;\n  }, [client, state]);\n  React.useEffect(() => {\n    if (hydrationQueue) {\n      hydrate(client, {\n        queries: hydrationQueue\n      }, optionsRef.current);\n    }\n  }, [client, hydrationQueue]);\n  return children;\n};\nexport { HydrationBoundary };", "map": {"version": 3, "names": ["React", "hydrate", "useQueryClient", "HydrationBoundary", "children", "options", "state", "queryClient", "client", "optionsRef", "useRef", "current", "hydrationQueue", "useMemo", "queryCache", "get<PERSON><PERSON><PERSON><PERSON>ache", "queries", "newQueries", "existingQueries", "dehydrated<PERSON><PERSON>y", "existingQuery", "get", "queryHash", "push", "hydrationIsNewer", "dataUpdatedAt", "promise", "status", "fetchStatus", "dehydratedAt", "length", "useEffect"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\react-query\\src\\HydrationBoundary.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport { hydrate } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  DehydratedState,\n  HydrateOptions,\n  OmitKeyof,\n  QueryClient,\n} from '@tanstack/query-core'\n\nexport interface HydrationBoundaryProps {\n  state?: unknown\n  options?: OmitKeyof<HydrateOptions, 'defaultOptions'> & {\n    defaultOptions?: OmitKeyof<\n      Exclude<HydrateOptions['defaultOptions'], undefined>,\n      'mutations'\n    >\n  }\n  children?: React.ReactNode\n  queryClient?: QueryClient\n}\n\nexport const HydrationBoundary = ({\n  children,\n  options = {},\n  state,\n  queryClient,\n}: HydrationBoundaryProps) => {\n  const client = useQueryClient(queryClient)\n\n  const optionsRef = React.useRef(options)\n  optionsRef.current = options\n\n  // This useMemo is for performance reasons only, everything inside it must\n  // be safe to run in every render and code here should be read as \"in render\".\n  //\n  // This code needs to happen during the render phase, because after initial\n  // SSR, hydration needs to happen _before_ children render. Also, if hydrating\n  // during a transition, we want to hydrate as much as is safe in render so\n  // we can prerender as much as possible.\n  //\n  // For any queries that already exist in the cache, we want to hold back on\n  // hydrating until _after_ the render phase. The reason for this is that during\n  // transitions, we don't want the existing queries and observers to update to\n  // the new data on the current page, only _after_ the transition is committed.\n  // If the transition is aborted, we will have hydrated any _new_ queries, but\n  // we throw away the fresh data for any existing ones to avoid unexpectedly\n  // updating the UI.\n  const hydrationQueue: DehydratedState['queries'] | undefined =\n    React.useMemo(() => {\n      if (state) {\n        if (typeof state !== 'object') {\n          return\n        }\n\n        const queryCache = client.getQueryCache()\n        // State is supplied from the outside and we might as well fail\n        // gracefully if it has the wrong shape, so while we type `queries`\n        // as required, we still provide a fallback.\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        const queries = (state as DehydratedState).queries || []\n\n        const newQueries: DehydratedState['queries'] = []\n        const existingQueries: DehydratedState['queries'] = []\n        for (const dehydratedQuery of queries) {\n          const existingQuery = queryCache.get(dehydratedQuery.queryHash)\n\n          if (!existingQuery) {\n            newQueries.push(dehydratedQuery)\n          } else {\n            const hydrationIsNewer =\n              dehydratedQuery.state.dataUpdatedAt >\n                existingQuery.state.dataUpdatedAt ||\n              (dehydratedQuery.promise &&\n                existingQuery.state.status !== 'pending' &&\n                existingQuery.state.fetchStatus !== 'fetching' &&\n                dehydratedQuery.dehydratedAt !== undefined &&\n                dehydratedQuery.dehydratedAt >\n                  existingQuery.state.dataUpdatedAt)\n\n            if (hydrationIsNewer) {\n              existingQueries.push(dehydratedQuery)\n            }\n          }\n        }\n\n        if (newQueries.length > 0) {\n          // It's actually fine to call this with queries/state that already exists\n          // in the cache, or is older. hydrate() is idempotent for queries.\n          hydrate(client, { queries: newQueries }, optionsRef.current)\n        }\n        if (existingQueries.length > 0) {\n          return existingQueries\n        }\n      }\n      return undefined\n    }, [client, state])\n\n  React.useEffect(() => {\n    if (hydrationQueue) {\n      hydrate(client, { queries: hydrationQueue }, optionsRef.current)\n    }\n  }, [client, hydrationQueue])\n\n  return children as React.ReactElement\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AAEvB,SAASC,OAAA,QAAe;AACxB,SAASC,cAAA,QAAsB;AAoBxB,IAAMC,iBAAA,GAAoBA,CAAC;EAChCC,QAAA;EACAC,OAAA,GAAU,CAAC;EACXC,KAAA;EACAC;AACF,MAA8B;EAC5B,MAAMC,MAAA,GAASN,cAAA,CAAeK,WAAW;EAEzC,MAAME,UAAA,GAAmBT,KAAA,CAAAU,MAAA,CAAOL,OAAO;EACvCI,UAAA,CAAWE,OAAA,GAAUN,OAAA;EAiBrB,MAAMO,cAAA,GACEZ,KAAA,CAAAa,OAAA,CAAQ,MAAM;IAClB,IAAIP,KAAA,EAAO;MACT,IAAI,OAAOA,KAAA,KAAU,UAAU;QAC7B;MACF;MAEA,MAAMQ,UAAA,GAAaN,MAAA,CAAOO,aAAA,CAAc;MAKxC,MAAMC,OAAA,GAAWV,KAAA,CAA0BU,OAAA,IAAW,EAAC;MAEvD,MAAMC,UAAA,GAAyC,EAAC;MAChD,MAAMC,eAAA,GAA8C,EAAC;MACrD,WAAWC,eAAA,IAAmBH,OAAA,EAAS;QACrC,MAAMI,aAAA,GAAgBN,UAAA,CAAWO,GAAA,CAAIF,eAAA,CAAgBG,SAAS;QAE9D,IAAI,CAACF,aAAA,EAAe;UAClBH,UAAA,CAAWM,IAAA,CAAKJ,eAAe;QACjC,OAAO;UACL,MAAMK,gBAAA,GACJL,eAAA,CAAgBb,KAAA,CAAMmB,aAAA,GACpBL,aAAA,CAAcd,KAAA,CAAMmB,aAAA,IACrBN,eAAA,CAAgBO,OAAA,IACfN,aAAA,CAAcd,KAAA,CAAMqB,MAAA,KAAW,aAC/BP,aAAA,CAAcd,KAAA,CAAMsB,WAAA,KAAgB,cACpCT,eAAA,CAAgBU,YAAA,KAAiB,UACjCV,eAAA,CAAgBU,YAAA,GACdT,aAAA,CAAcd,KAAA,CAAMmB,aAAA;UAE1B,IAAID,gBAAA,EAAkB;YACpBN,eAAA,CAAgBK,IAAA,CAAKJ,eAAe;UACtC;QACF;MACF;MAEA,IAAIF,UAAA,CAAWa,MAAA,GAAS,GAAG;QAGzB7B,OAAA,CAAQO,MAAA,EAAQ;UAAEQ,OAAA,EAASC;QAAW,GAAGR,UAAA,CAAWE,OAAO;MAC7D;MACA,IAAIO,eAAA,CAAgBY,MAAA,GAAS,GAAG;QAC9B,OAAOZ,eAAA;MACT;IACF;IACA,OAAO;EACT,GAAG,CAACV,MAAA,EAAQF,KAAK,CAAC;EAEdN,KAAA,CAAA+B,SAAA,CAAU,MAAM;IACpB,IAAInB,cAAA,EAAgB;MAClBX,OAAA,CAAQO,MAAA,EAAQ;QAAEQ,OAAA,EAASJ;MAAe,GAAGH,UAAA,CAAWE,OAAO;IACjE;EACF,GAAG,CAACH,MAAA,EAAQI,cAAc,CAAC;EAE3B,OAAOR,QAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}