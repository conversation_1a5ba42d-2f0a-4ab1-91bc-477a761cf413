{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\App.stores.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { useAuth } from './stores/authStore';\nimport { useGame } from './stores/gameStore';\nimport { useChat } from './stores/chatStore';\nimport './styles/globals.css';\n\n// Configuração do React Query\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000 // 5 minutos\n    }\n  }\n});\n\n// Componente para testar stores\nconst StoreTestPage = ({\n  title\n}) => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    isLoading: authLoading\n  } = useAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer\n  } = useGame();\n  const {\n    messages,\n    isLoading: chatLoading\n  } = useChat();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-bg-primary text-text-primary p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"text-accent-blue hover:text-primary-light\",\n          children: \"\\u2190 Voltar ao in\\xEDcio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-6\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card bg-blue-900 border-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-blue-100 mb-2\",\n              children: \"\\uD83D\\uDD10 Auth Store\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-200 text-sm space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Autenticado: \", isAuthenticated ? '✅' : '❌']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Loading: \", authLoading ? '⏳' : '✅']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Usu\\xE1rio: \", user ? user.nick : 'Nenhum']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card bg-green-900 border-green-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-green-100 mb-2\",\n              children: \"\\uD83C\\uDFAE Game Store\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-green-200 text-sm space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Jogador: \", currentPlayer ? currentPlayer.nick : 'Nenhum']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Loading: \", isLoadingPlayer ? '⏳' : '✅']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Dinheiro: \", currentPlayer ? `$${currentPlayer.dinheiro}` : 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card bg-purple-900 border-purple-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-purple-100 mb-2\",\n              children: \"\\uD83D\\uDCAC Chat Store\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-purple-200 text-sm space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Mensagens: \", messages.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Loading: \", chatLoading ? '⏳' : '✅']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Status: Conectado\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card bg-bg-tertiary\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-text-primary mb-4\",\n            children: \"\\uD83E\\uDDEA Teste de A\\xE7\\xF5es dos Stores\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                console.log('Auth Store - Estado atual:', {\n                  user,\n                  isAuthenticated,\n                  authLoading\n                });\n                alert('Verifique o console para ver o estado do Auth Store');\n              },\n              className: \"btn-primary\",\n              children: \"Testar Auth Store\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                console.log('Game Store - Estado atual:', {\n                  currentPlayer,\n                  isLoadingPlayer\n                });\n                alert('Verifique o console para ver o estado do Game Store');\n              },\n              className: \"btn-secondary\",\n              children: \"Testar Game Store\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                console.log('Chat Store - Estado atual:', {\n                  messages,\n                  chatLoading\n                });\n                alert('Verifique o console para ver o estado do Chat Store');\n              },\n              className: \"btn-accent\",\n              children: \"Testar Chat Store\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n\n// Página inicial\n_s(StoreTestPage, \"45rqk0Kc1vpYxN/0vRdp06TOObA=\", false, function () {\n  return [useAuth, useGame, useChat];\n});\n_c = StoreTestPage;\nconst HomePage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"min-h-screen bg-bg-primary text-text-primary p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-4xl font-bold text-center mb-8\",\n      children: \"\\uD83C\\uDFAE SHACK Web Game - Teste Zustand Stores\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-semibold mb-6\",\n        children: \"Zustand Stores Adicionados!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"card bg-blue-900 border-blue-500 hover:bg-blue-800 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-blue-100 mb-2\",\n            children: \"\\uD83D\\uDD10 Teste Auth Store\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-200 text-sm\",\n            children: \"Testar store de autentica\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/game\",\n          className: \"card bg-green-900 border-green-500 hover:bg-green-800 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-green-100 mb-2\",\n            children: \"\\uD83C\\uDFAE Teste Game Store\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-green-200 text-sm\",\n            children: \"Testar store do jogo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-bg-tertiary border border-border-color rounded p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-text-muted text-sm space-y-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2705 React funcionando\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2705 React Router funcionando\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2705 Tailwind CSS funcionando\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2705 React Query Provider ativo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2705 Zustand Stores carregados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 123,\n  columnNumber: 3\n}, this);\n_c2 = HomePage;\nfunction App() {\n  console.log('App Stores - Renderizando com Zustand Stores...');\n  return /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(StoreTestPage, {\n            title: \"Teste Auth Store\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/game\",\n          element: /*#__PURE__*/_jsxDEV(StoreTestPage, {\n            title: \"Teste Game Store\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(StoreTestPage, {\n            title: \"Teste 404\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"StoreTestPage\");\n$RefreshReg$(_c2, \"HomePage\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "QueryClient", "QueryClientProvider", "useAuth", "useGame", "useChat", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "StoreTestPage", "title", "_s", "user", "isAuthenticated", "isLoading", "authLoading", "currentPlayer", "isLoadingPlayer", "messages", "chatLoading", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "<PERSON><PERSON><PERSON>", "length", "onClick", "console", "log", "alert", "_c", "HomePage", "_c2", "App", "client", "path", "element", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/App.stores.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { useAuth } from './stores/authStore';\nimport { useGame } from './stores/gameStore';\nimport { useChat } from './stores/chatStore';\nimport './styles/globals.css';\n\n// Configuração do React Query\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5 minutos\n    },\n  },\n});\n\n// Componente para testar stores\nconst StoreTestPage = ({ title }: { title: string }) => {\n  const { user, isAuthenticated, isLoading: authLoading } = useAuth();\n  const { currentPlayer, isLoadingPlayer } = useGame();\n  const { messages, isLoading: chatLoading } = useChat();\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"mb-6\">\n          <Link to=\"/\" className=\"text-accent-blue hover:text-primary-light\">\n            ← Voltar ao início\n          </Link>\n        </div>\n        \n        <div className=\"card\">\n          <h1 className=\"text-3xl font-bold mb-6\">{title}</h1>\n          \n          {/* Status dos Stores */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n            {/* Auth Store */}\n            <div className=\"card bg-blue-900 border-blue-500\">\n              <h3 className=\"text-lg font-semibold text-blue-100 mb-2\">\n                🔐 Auth Store\n              </h3>\n              <div className=\"text-blue-200 text-sm space-y-1\">\n                <p>Autenticado: {isAuthenticated ? '✅' : '❌'}</p>\n                <p>Loading: {authLoading ? '⏳' : '✅'}</p>\n                <p>Usuário: {user ? user.nick : 'Nenhum'}</p>\n              </div>\n            </div>\n\n            {/* Game Store */}\n            <div className=\"card bg-green-900 border-green-500\">\n              <h3 className=\"text-lg font-semibold text-green-100 mb-2\">\n                🎮 Game Store\n              </h3>\n              <div className=\"text-green-200 text-sm space-y-1\">\n                <p>Jogador: {currentPlayer ? currentPlayer.nick : 'Nenhum'}</p>\n                <p>Loading: {isLoadingPlayer ? '⏳' : '✅'}</p>\n                <p>Dinheiro: {currentPlayer ? `$${currentPlayer.dinheiro}` : 'N/A'}</p>\n              </div>\n            </div>\n\n            {/* Chat Store */}\n            <div className=\"card bg-purple-900 border-purple-500\">\n              <h3 className=\"text-lg font-semibold text-purple-100 mb-2\">\n                💬 Chat Store\n              </h3>\n              <div className=\"text-purple-200 text-sm space-y-1\">\n                <p>Mensagens: {messages.length}</p>\n                <p>Loading: {chatLoading ? '⏳' : '✅'}</p>\n                <p>Status: Conectado</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Teste de Ações */}\n          <div className=\"card bg-bg-tertiary\">\n            <h3 className=\"text-lg font-semibold text-text-primary mb-4\">\n              🧪 Teste de Ações dos Stores\n            </h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <button \n                onClick={() => {\n                  console.log('Auth Store - Estado atual:', { user, isAuthenticated, authLoading });\n                  alert('Verifique o console para ver o estado do Auth Store');\n                }}\n                className=\"btn-primary\"\n              >\n                Testar Auth Store\n              </button>\n              \n              <button \n                onClick={() => {\n                  console.log('Game Store - Estado atual:', { currentPlayer, isLoadingPlayer });\n                  alert('Verifique o console para ver o estado do Game Store');\n                }}\n                className=\"btn-secondary\"\n              >\n                Testar Game Store\n              </button>\n              \n              <button \n                onClick={() => {\n                  console.log('Chat Store - Estado atual:', { messages, chatLoading });\n                  alert('Verifique o console para ver o estado do Chat Store');\n                }}\n                className=\"btn-accent\"\n              >\n                Testar Chat Store\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Página inicial\nconst HomePage = () => (\n  <div className=\"min-h-screen bg-bg-primary text-text-primary p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <h1 className=\"text-4xl font-bold text-center mb-8\">\n        🎮 SHACK Web Game - Teste Zustand Stores\n      </h1>\n      \n      <div className=\"card text-center\">\n        <h2 className=\"text-2xl font-semibold mb-6\">\n          Zustand Stores Adicionados!\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n          <Link to=\"/login\" className=\"card bg-blue-900 border-blue-500 hover:bg-blue-800 transition-colors\">\n            <h3 className=\"text-lg font-semibold text-blue-100 mb-2\">\n              🔐 Teste Auth Store\n            </h3>\n            <p className=\"text-blue-200 text-sm\">\n              Testar store de autenticação\n            </p>\n          </Link>\n          \n          <Link to=\"/game\" className=\"card bg-green-900 border-green-500 hover:bg-green-800 transition-colors\">\n            <h3 className=\"text-lg font-semibold text-green-100 mb-2\">\n              🎮 Teste Game Store\n            </h3>\n            <p className=\"text-green-200 text-sm\">\n              Testar store do jogo\n            </p>\n          </Link>\n        </div>\n        \n        <div className=\"bg-bg-tertiary border border-border-color rounded p-4\">\n          <div className=\"text-text-muted text-sm space-y-1\">\n            <p>✅ React funcionando</p>\n            <p>✅ React Router funcionando</p>\n            <p>✅ Tailwind CSS funcionando</p>\n            <p>✅ React Query Provider ativo</p>\n            <p>✅ Zustand Stores carregados</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nfunction App() {\n  console.log('App Stores - Renderizando com Zustand Stores...');\n  \n  return (\n    <QueryClientProvider client={queryClient}>\n      <Router>\n        <Routes>\n          <Route path=\"/\" element={<HomePage />} />\n          <Route path=\"/login\" element={<StoreTestPage title=\"Teste Auth Store\" />} />\n          <Route path=\"/game\" element={<StoreTestPage title=\"Teste Game Store\" />} />\n          <Route path=\"*\" element={<StoreTestPage title=\"Teste 404\" />} />\n        </Routes>\n      </Router>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;AACxE,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAO,sBAAsB;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIP,WAAW,CAAC;EAClCQ,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;IAC5B;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAyB,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,SAAS,EAAEC;EAAY,CAAC,GAAGjB,OAAO,CAAC,CAAC;EACnE,MAAM;IAAEkB,aAAa;IAAEC;EAAgB,CAAC,GAAGlB,OAAO,CAAC,CAAC;EACpD,MAAM;IAAEmB,QAAQ;IAAEJ,SAAS,EAAEK;EAAY,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAEtD,oBACEE,OAAA;IAAKkB,SAAS,EAAC,kDAAkD;IAAAC,QAAA,eAC/DnB,OAAA;MAAKkB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCnB,OAAA;QAAKkB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBnB,OAAA,CAACP,IAAI;UAAC2B,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EAAC;QAEnE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENxB,OAAA;QAAKkB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBnB,OAAA;UAAIkB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAEX;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGpDxB,OAAA;UAAKkB,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBAEzDnB,OAAA;YAAKkB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CnB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxB,OAAA;cAAKkB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CnB,OAAA;gBAAAmB,QAAA,GAAG,eAAa,EAACR,eAAe,GAAG,GAAG,GAAG,GAAG;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjDxB,OAAA;gBAAAmB,QAAA,GAAG,WAAS,EAACN,WAAW,GAAG,GAAG,GAAG,GAAG;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzCxB,OAAA;gBAAAmB,QAAA,GAAG,cAAS,EAACT,IAAI,GAAGA,IAAI,CAACe,IAAI,GAAG,QAAQ;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxB,OAAA;YAAKkB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACjDnB,OAAA;cAAIkB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAE1D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxB,OAAA;cAAKkB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CnB,OAAA;gBAAAmB,QAAA,GAAG,WAAS,EAACL,aAAa,GAAGA,aAAa,CAACW,IAAI,GAAG,QAAQ;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/DxB,OAAA;gBAAAmB,QAAA,GAAG,WAAS,EAACJ,eAAe,GAAG,GAAG,GAAG,GAAG;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7CxB,OAAA;gBAAAmB,QAAA,GAAG,YAAU,EAACL,aAAa,GAAG,IAAIA,aAAa,CAACY,QAAQ,EAAE,GAAG,KAAK;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxB,OAAA;YAAKkB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnDnB,OAAA;cAAIkB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EAAC;YAE3D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxB,OAAA;cAAKkB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnB,OAAA;gBAAAmB,QAAA,GAAG,aAAW,EAACH,QAAQ,CAACW,MAAM;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCxB,OAAA;gBAAAmB,QAAA,GAAG,WAAS,EAACF,WAAW,GAAG,GAAG,GAAG,GAAG;cAAA;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzCxB,OAAA;gBAAAmB,QAAA,EAAG;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxB,OAAA;UAAKkB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCnB,OAAA;YAAIkB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAE7D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELxB,OAAA;YAAKkB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDnB,OAAA;cACE4B,OAAO,EAAEA,CAAA,KAAM;gBACbC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;kBAAEpB,IAAI;kBAAEC,eAAe;kBAAEE;gBAAY,CAAC,CAAC;gBACjFkB,KAAK,CAAC,qDAAqD,CAAC;cAC9D,CAAE;cACFb,SAAS,EAAC,aAAa;cAAAC,QAAA,EACxB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETxB,OAAA;cACE4B,OAAO,EAAEA,CAAA,KAAM;gBACbC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;kBAAEhB,aAAa;kBAAEC;gBAAgB,CAAC,CAAC;gBAC7EgB,KAAK,CAAC,qDAAqD,CAAC;cAC9D,CAAE;cACFb,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC1B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETxB,OAAA;cACE4B,OAAO,EAAEA,CAAA,KAAM;gBACbC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;kBAAEd,QAAQ;kBAAEC;gBAAY,CAAC,CAAC;gBACpEc,KAAK,CAAC,qDAAqD,CAAC;cAC9D,CAAE;cACFb,SAAS,EAAC,YAAY;cAAAC,QAAA,EACvB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAf,EAAA,CApGMF,aAAa;EAAA,QACyCX,OAAO,EACtBC,OAAO,EACLC,OAAO;AAAA;AAAAkC,EAAA,GAHhDzB,aAAa;AAqGnB,MAAM0B,QAAQ,GAAGA,CAAA,kBACfjC,OAAA;EAAKkB,SAAS,EAAC,kDAAkD;EAAAC,QAAA,eAC/DnB,OAAA;IAAKkB,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCnB,OAAA;MAAIkB,SAAS,EAAC,qCAAqC;MAAAC,QAAA,EAAC;IAEpD;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELxB,OAAA;MAAKkB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BnB,OAAA;QAAIkB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAE5C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELxB,OAAA;QAAKkB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDnB,OAAA,CAACP,IAAI;UAAC2B,EAAE,EAAC,QAAQ;UAACF,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBAChGnB,OAAA;YAAIkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxB,OAAA;YAAGkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEPxB,OAAA,CAACP,IAAI;UAAC2B,EAAE,EAAC,OAAO;UAACF,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBAClGnB,OAAA;YAAIkB,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAE1D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxB,OAAA;YAAGkB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAEtC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENxB,OAAA;QAAKkB,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpEnB,OAAA;UAAKkB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnB,OAAA;YAAAmB,QAAA,EAAG;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1BxB,OAAA;YAAAmB,QAAA,EAAG;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjCxB,OAAA;YAAAmB,QAAA,EAAG;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjCxB,OAAA;YAAAmB,QAAA,EAAG;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnCxB,OAAA;YAAAmB,QAAA,EAAG;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACU,GAAA,GA5CID,QAAQ;AA8Cd,SAASE,GAAGA,CAAA,EAAG;EACbN,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;EAE9D,oBACE9B,OAAA,CAACL,mBAAmB;IAACyC,MAAM,EAAEnC,WAAY;IAAAkB,QAAA,eACvCnB,OAAA,CAACV,MAAM;MAAA6B,QAAA,eACLnB,OAAA,CAACT,MAAM;QAAA4B,QAAA,gBACLnB,OAAA,CAACR,KAAK;UAAC6C,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEtC,OAAA,CAACiC,QAAQ;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCxB,OAAA,CAACR,KAAK;UAAC6C,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEtC,OAAA,CAACO,aAAa;YAACC,KAAK,EAAC;UAAkB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5ExB,OAAA,CAACR,KAAK;UAAC6C,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEtC,OAAA,CAACO,aAAa;YAACC,KAAK,EAAC;UAAkB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3ExB,OAAA,CAACR,KAAK;UAAC6C,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEtC,OAAA,CAACO,aAAa;YAACC,KAAK,EAAC;UAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAE1B;AAACe,GAAA,GAfQJ,GAAG;AAiBZ,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}