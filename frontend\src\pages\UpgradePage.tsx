import React, { useState, useEffect } from 'react';
import { useAuth } from '../stores/authStore';
import { usePlayer } from '../stores/playerStore';
import gameApi from '../services/gameApi';

interface UpgradeItem {
  name: string;
  displayName: string;
  icon: string;
  description: string;
  currentLevel: number;
  maxLevel: number;
  cost: {
    dinheiro: number;
    shacks?: number;
  };
}

const UpgradePage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { currentPlayer, loadPlayerData } = usePlayer();
  
  const [upgradeItems, setUpgradeItems] = useState<UpgradeItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [upgradeError, setUpgradeError] = useState<string | null>(null);
  const [upgradeSuccess, setUpgradeSuccess] = useState<string | null>(null);
  const [upgradingItem, setUpgradingItem] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated && currentPlayer) {
      loadUpgradeItems();
    }
  }, [isAuthenticated, currentPlayer]);

  const loadUpgradeItems = () => {
    if (!currentPlayer) return;

    const items: UpgradeItem[] = [
      {
        name: 'cpu',
        displayName: 'CPU',
        icon: '🖥️',
        description: 'Aumenta poder de processamento para exploits',
        currentLevel: currentPlayer.cpu || 1,
        maxLevel: 100,
        cost: calculateUpgradeCost('cpu', currentPlayer.cpu || 1),
      },
      {
        name: 'ram',
        displayName: 'RAM',
        icon: '💾',
        description: 'Melhora performance do sistema',
        currentLevel: currentPlayer.ram || 1,
        maxLevel: 100,
        cost: calculateUpgradeCost('ram', currentPlayer.ram || 1),
      },
      {
        name: 'firewall',
        displayName: 'Firewall',
        icon: '🛡️',
        description: 'Protege contra ataques externos',
        currentLevel: currentPlayer.firewall || 1,
        maxLevel: 100,
        cost: calculateUpgradeCost('firewall', currentPlayer.firewall || 1),
      },
      {
        name: 'antivirus',
        displayName: 'Antivírus',
        icon: '🦠',
        description: 'Detecta e remove malware',
        currentLevel: currentPlayer.antivirus || 1,
        maxLevel: 100,
        cost: calculateUpgradeCost('antivirus', currentPlayer.antivirus || 1),
      },
      {
        name: 'malware_kit',
        displayName: 'Malware Kit',
        icon: '🔧',
        description: 'Ferramentas para criar malware',
        currentLevel: currentPlayer.malware_kit || 1,
        maxLevel: 100,
        cost: calculateUpgradeCost('malware_kit', currentPlayer.malware_kit || 1),
      },
      {
        name: 'bruteforce',
        displayName: 'BruteForce',
        icon: '🔨',
        description: 'Força bruta para quebrar senhas',
        currentLevel: currentPlayer.bruteforce || 1,
        maxLevel: 100,
        cost: calculateUpgradeCost('bruteforce', currentPlayer.bruteforce || 1),
      },
      {
        name: 'bankguard',
        displayName: 'BankGuard',
        icon: '🏦',
        description: 'Protege transferências bancárias',
        currentLevel: currentPlayer.bankguard || 1,
        maxLevel: 100,
        cost: calculateUpgradeCost('bankguard', currentPlayer.bankguard || 1),
      },
      {
        name: 'proxyvpn',
        displayName: 'ProxyVPN',
        icon: '🌐',
        description: 'Oculta identidade online',
        currentLevel: currentPlayer.proxyvpn || 1,
        maxLevel: 100,
        cost: calculateUpgradeCost('proxyvpn', currentPlayer.proxyvpn || 1),
      },
    ];

    setUpgradeItems(items);
  };

  const calculateUpgradeCost = (item: string, currentLevel: number) => {
    // Fórmula básica de custo: base * level^1.5
    const baseCost = 1000;
    const dinheiro = Math.floor(baseCost * Math.pow(currentLevel, 1.5));
    const shacks = Math.floor(dinheiro * 0.1); // 10% do custo em shacks
    
    return { dinheiro, shacks };
  };

  const handleUpgrade = async (item: UpgradeItem) => {
    if (!currentPlayer) return;

    // Verificar se tem recursos suficientes
    if ((currentPlayer?.dinheiro || 0) < item.cost.dinheiro) {
      setUpgradeError('Dinheiro insuficiente');
      return;
    }

    if (item.cost.shacks && (currentPlayer?.shack || 0) < item.cost.shacks) {
      setUpgradeError('Shacks insuficientes');
      return;
    }

    if (item.currentLevel >= item.maxLevel) {
      setUpgradeError('Item já está no nível máximo');
      return;
    }

    setUpgradingItem(item.name);
    setUpgradeError(null);
    setUpgradeSuccess(null);

    try {
      const response = await gameApi.upgradeItem(item.name, 1);
      
      if (response.sucesso) {
        setUpgradeSuccess(`${item.displayName} atualizado para nível ${item.currentLevel + 1}!`);
        
        // Recarregar dados do jogador
        await loadPlayerData();
        
        // Atualizar lista de upgrades
        setTimeout(() => {
          loadUpgradeItems();
        }, 500);
      } else {
        setUpgradeError(response.mensagem || 'Erro no upgrade');
      }
    } catch (error: any) {
      setUpgradeError(error.message || 'Erro de conexão');
    } finally {
      setUpgradingItem(null);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">🔒 Acesso Negado</h1>
          <p className="text-gray-400">Você precisa estar logado para acessar os upgrades</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => window.history.back()}
            className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600"
          >
            <span className="text-lg">←</span>
          </button>
          <div>
            <h1 className="text-lg font-bold">🔧 Upgrades</h1>
            <p className="text-xs text-gray-400">Melhorias do Sistema</p>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {/* Recursos disponíveis */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold mb-2 text-white">💰 Recursos Disponíveis</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-xl font-bold text-green-400">
                ${currentPlayer?.dinheiro?.toLocaleString() || '0'}
              </div>
              <div className="text-xs text-gray-400">Dinheiro</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-blue-400">
                {(currentPlayer?.shack || 0).toLocaleString()}
              </div>
              <div className="text-xs text-gray-400">Shacks</div>
            </div>
          </div>
        </div>

        {/* Mensagens de erro/sucesso */}
        {upgradeError && (
          <div className="bg-red-900 border border-red-500 rounded-lg p-3">
            <p className="text-red-300 text-sm">❌ {upgradeError}</p>
          </div>
        )}

        {upgradeSuccess && (
          <div className="bg-green-900 border border-green-500 rounded-lg p-3">
            <p className="text-green-300 text-sm">✅ {upgradeSuccess}</p>
          </div>
        )}

        {/* Lista de upgrades */}
        <div className="space-y-3">
          {upgradeItems.map((item) => {
            const canAfford = currentPlayer &&
              (currentPlayer?.dinheiro || 0) >= item.cost.dinheiro &&
              (!item.cost.shacks || (currentPlayer?.shack || 0) >= item.cost.shacks);
            
            const isMaxLevel = item.currentLevel >= item.maxLevel;
            const isUpgrading = upgradingItem === item.name;

            return (
              <div key={item.name} className="bg-gray-800 rounded-lg p-4 border border-gray-600">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{item.icon}</div>
                    <div>
                      <h4 className="font-bold text-white">{item.displayName}</h4>
                      <p className="text-xs text-gray-400">{item.description}</p>
                      <div className="text-sm text-blue-400">
                        Nível {item.currentLevel}/{item.maxLevel}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-300">
                      ${item.cost.dinheiro.toLocaleString()}
                    </div>
                    {item.cost.shacks && (
                      <div className="text-xs text-blue-400">
                        {item.cost.shacks} Shacks
                      </div>
                    )}
                  </div>
                </div>

                {/* Barra de progresso */}
                <div className="w-full bg-gray-700 rounded-full h-2 mb-3">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${(item.currentLevel / item.maxLevel) * 100}%` }}
                  ></div>
                </div>

                <button
                  onClick={() => handleUpgrade(item)}
                  disabled={!canAfford || isMaxLevel || isUpgrading}
                  className={`w-full py-2 rounded-lg font-semibold text-sm ${
                    isMaxLevel
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : canAfford && !isUpgrading
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {isUpgrading ? 'Atualizando...' : 
                   isMaxLevel ? 'Nível Máximo' :
                   canAfford ? 'Atualizar' : 'Recursos Insuficientes'}
                </button>
              </div>
            );
          })}
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0">
        <div className="flex justify-center">
          <button 
            onClick={() => window.history.back()}
            className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
          >
            <span className="text-lg">←</span>
            <span className="text-sm">Voltar</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default UpgradePage;
