{% extends "base.html" %}

{% block content %}
<!-- <PERSON><PERSON> de News -->
<div id="news-section" class="game-section">
    <div class="container mx-auto max-w-4xl">
        <!-- Header -->
        <div class="text-center mb-6">
            <h1 class="text-3xl font-bold text-primary-text mb-2">NOTÍCIAS</h1>
            <p class="text-secondary-text">Últimas atualizações e notícias do sistema</p>
        </div>

        <!-- Lista de Notícias -->
        <div id="news-list" class="space-y-4">
            <!-- Loading state -->
            <div id="news-loading" class="text-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-blue mx-auto"></div>
                <p class="text-secondary-text mt-2">Carregando notícias...</p>
            </div>

            <!-- Empty state -->
            <div id="news-empty" class="text-center py-8 hidden">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 text-gray-500 mx-auto mb-4">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 01-2.25 2.25M16.5 7.5V18a2.25 2.25 0 002.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 002.25 2.25h6.375a4.5 4.5 0 003-1.125z" />
                </svg>
                <p class="text-secondary-text">Nenhuma notícia disponível</p>
            </div>

            <!-- Notícias aparecerão aqui dinamicamente -->
        </div>

        <!-- Botão Admin (aparece apenas para administradores) -->
        <div id="admin-panel" class="mt-8 hidden">
            <div class="bg-surface-elevated border border-border-color rounded-lg p-6">
                <h2 class="text-xl font-bold text-primary-text mb-4">Painel Administrativo</h2>
                
                <!-- Formulário para criar notícia -->
                <form id="create-news-form" class="space-y-4">
                    <div>
                        <label for="news-title" class="block text-sm font-medium text-secondary-text mb-2">Título</label>
                        <input type="text" id="news-title" name="title" required 
                               class="w-full p-3 bg-surface-default border border-border-color rounded-lg text-primary-text placeholder-gray-500"
                               placeholder="Digite o título da notícia">
                    </div>
                    
                    <div>
                        <label for="news-content" class="block text-sm font-medium text-secondary-text mb-2">Conteúdo</label>
                        <textarea id="news-content" name="content" rows="4" required
                                  class="w-full p-3 bg-surface-default border border-border-color rounded-lg text-primary-text placeholder-gray-500 resize-vertical"
                                  placeholder="Digite o conteúdo da notícia"></textarea>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" id="news-priority" name="priority" 
                               class="w-4 h-4 text-accent-blue bg-surface-default border-border-color rounded focus:ring-accent-blue">
                        <label for="news-priority" class="text-sm text-secondary-text">Notícia prioritária (destaque)</label>
                    </div>
                    
                    <button type="submit" class="w-full bg-accent-blue hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                        📢 Publicar Notícia
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
/* Estilos específicos para notícias */
.news-item {
    background: var(--surface-elevated);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.news-item:hover {
    border-color: var(--accent-blue);
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.2);
    transform: translateY(-2px);
}

.news-item.priority {
    border-color: #eab308;
    background: rgba(234, 179, 8, 0.05);
}

.news-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.news-title {
    font-size: 1.125rem;
    font-weight: bold;
    color: var(--primary-text);
}

.news-meta {
    font-size: 0.75rem;
    color: var(--secondary-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.news-content {
    color: var(--secondary-text);
    line-height: 1.6;
}

.news-priority-badge {
    background: #eab308;
    color: #000;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-weight: 500;
}

.admin-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.delete-btn {
    background: #dc2626;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
}

.delete-btn:hover {
    background: #b91c1c;
}
</style>

<script>
// Funções específicas para a seção de News
window.newsModule = {
    // Carregar notícias
    async loadNews() {
        try {
            const response = await fetch('/api/news', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            
            const data = await response.json();
            
            if (data.sucesso) {
                this.displayNews(data.noticias || []);
            } else {
                showNotification('Erro ao carregar notícias: ' + data.mensagem, 'error');
            }
        } catch (error) {
            console.error('Erro ao carregar notícias:', error);
            showNotification('Erro ao carregar notícias', 'error');
        } finally {
            document.getElementById('news-loading').classList.add('hidden');
        }
    },

    // Exibir notícias na interface
    displayNews(noticias) {
        const newsList = document.getElementById('news-list');
        const loading = document.getElementById('news-loading');
        const empty = document.getElementById('news-empty');
        
        // Remove loading
        loading.classList.add('hidden');
        
        // Limpa notícias existentes (exceto loading e empty)
        const existingNews = newsList.querySelectorAll('.news-item');
        existingNews.forEach(item => item.remove());
        
        if (noticias.length === 0) {
            empty.classList.remove('hidden');
            return;
        }
        
        empty.classList.add('hidden');
        
        // Ordena por data (mais recentes primeiro)
        noticias.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        
        noticias.forEach(noticia => {
            const newsItem = this.createNewsElement(noticia);
            newsList.appendChild(newsItem);
        });
    },

    // Criar elemento HTML para uma notícia
    createNewsElement(noticia) {
        const newsDiv = document.createElement('div');
        newsDiv.className = `news-item ${noticia.priority ? 'priority' : ''}`;
        
        const date = new Date(noticia.created_at).toLocaleString('pt-BR');
        const isAdmin = window.gameData?.userData?.is_admin || false;
        
        newsDiv.innerHTML = `
            <div class="news-header">
                <div class="flex-1">
                    <h3 class="news-title">${noticia.title}</h3>
                    <div class="news-meta">
                        <span>${date}</span>
                        ${noticia.priority ? '<span class="news-priority-badge">DESTAQUE</span>' : ''}
                    </div>
                </div>
            </div>
            <div class="news-content">${noticia.content.replace(/\n/g, '<br>')}</div>
            ${isAdmin ? `
                <div class="admin-actions">
                    <button onclick="window.newsModule.deleteNews('${noticia.id}')" class="delete-btn">
                        Excluir
                    </button>
                </div>
            ` : ''}
        `;
        
        return newsDiv;
    },

    // Criar nova notícia (admin)
    async createNews(title, content, priority = false) {
        try {
            const response = await fetch('/api/news', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    title: title,
                    content: content,
                    priority: priority
                })
            });
            
            const data = await response.json();
            
            if (data.sucesso) {
                showNotification('Notícia publicada com sucesso!', 'success');
                this.loadNews(); // Recarrega as notícias
                document.getElementById('create-news-form').reset();
            } else {
                showNotification('Erro ao criar notícia: ' + data.mensagem, 'error');
            }
        } catch (error) {
            console.error('Erro ao criar notícia:', error);
            showNotification('Erro ao criar notícia', 'error');
        }
    },

    // Excluir notícia (admin)
    async deleteNews(newsId) {
        if (!confirm('Tem certeza que deseja excluir esta notícia?')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/news/${newsId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            
            const data = await response.json();
            
            if (data.sucesso) {
                showNotification('Notícia excluída com sucesso!', 'success');
                this.loadNews(); // Recarrega as notícias
            } else {
                showNotification('Erro ao excluir notícia: ' + data.mensagem, 'error');
            }
        } catch (error) {
            console.error('Erro ao excluir notícia:', error);
            showNotification('Erro ao excluir notícia', 'error');
        }
    },

    // Verificar se usuário é admin e mostrar painel
    checkAdminAccess() {
        const isAdmin = window.gameData?.userData?.is_admin || false;
        const adminPanel = document.getElementById('admin-panel');
        
        if (isAdmin) {
            adminPanel.classList.remove('hidden');
        }
    },

    // Inicializar seção de news
    init() {
        this.loadNews();
        this.checkAdminAccess();
        
        // Event listener para formulário de criação
        const form = document.getElementById('create-news-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                const title = document.getElementById('news-title').value;
                const content = document.getElementById('news-content').value;
                const priority = document.getElementById('news-priority').checked;
                
                this.createNews(title, content, priority);
            });
        }
    }
};

// Inicializar quando a seção for carregada
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('news-section')) {
        window.newsModule.init();
    }
});
</script>
{% endblock %}
