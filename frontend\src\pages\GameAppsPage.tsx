import React from 'react';
import { useHackGameStore } from '../stores/hackGameStore';
import { GAME_APPS, calculateUpgradeCost, calculateXpReward } from '../types/game';

const GameAppsPage: React.FC = () => {
  const { 
    player, 
    playerApps, 
    setCurrentScreen,
    upgradeApp,
    error 
  } = useHackGameStore();

  if (!player) {
    return (
      <div className="flex items-center justify-center h-full text-white">
        <div className="text-center">
          <div className="text-4xl mb-4">⚙️</div>
          <p>Carregando aplicativos...</p>
        </div>
      </div>
    );
  }

  const handleUpgrade = (appId: keyof typeof playerApps) => {
    upgradeApp(appId);
  };

  const getAppsByCategory = (category: string) => {
    return Object.entries(GAME_APPS).filter(([_, app]) => app.category === category);
  };

  const renderAppCard = ([appId, appInfo]: [string, any]) => {
    const currentLevel = playerApps[appId as keyof typeof playerApps];
    const upgradeCost = calculateUpgradeCost(currentLevel);
    const xpReward = calculateXpReward(currentLevel);
    const canUpgrade = player.cash >= upgradeCost && currentLevel < appInfo.maxLevel;
    const isMaxLevel = currentLevel >= appInfo.maxLevel;

    return (
      <div key={appId} className="bg-gray-800/50 rounded-xl p-4 border border-gray-700/50">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{appInfo.icon}</div>
            <div>
              <h3 className="font-semibold text-white">{appInfo.name}</h3>
              <p className="text-xs text-gray-400">Nível {currentLevel}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm font-bold text-blue-400">Lv.{currentLevel}</div>
            <div className="text-xs text-gray-500">/{appInfo.maxLevel}</div>
          </div>
        </div>

        <p className="text-xs text-gray-300 mb-3">{appInfo.description}</p>

        {/* Barra de progresso do nível */}
        <div className="mb-3">
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentLevel / appInfo.maxLevel) * 100}%` }}
            ></div>
          </div>
        </div>

        {!isMaxLevel ? (
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span className="text-gray-400">Custo do upgrade:</span>
              <span className="text-yellow-400">${upgradeCost.toLocaleString()}</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-gray-400">XP ganho:</span>
              <span className="text-green-400">+{xpReward} XP</span>
            </div>
            <button
              onClick={() => handleUpgrade(appId as keyof typeof playerApps)}
              disabled={!canUpgrade}
              className={`w-full py-2 px-4 rounded-lg font-semibold text-sm transition-all ${
                canUpgrade
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-gray-600 text-gray-400 cursor-not-allowed'
              }`}
            >
              {canUpgrade ? 'Fazer Upgrade' : 'Dinheiro Insuficiente'}
            </button>
          </div>
        ) : (
          <div className="text-center py-2">
            <span className="text-green-400 font-semibold text-sm">✓ Nível Máximo</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full bg-gradient-to-br from-gray-900 to-black text-white flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setCurrentScreen('home')}
            className="text-blue-400 hover:text-blue-300 transition-colors"
          >
            ← Voltar
          </button>
          <div className="text-center">
            <h1 className="text-lg font-bold">Upgrades</h1>
            <p className="text-xs text-gray-400">Melhore seus aplicativos</p>
          </div>
          <div className="text-right">
            <div className="text-sm text-green-400">${player.cash.toLocaleString()}</div>
            <div className="text-xs text-gray-400">Cash</div>
          </div>
        </div>
      </div>

      {/* Erro */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-red-900/50 border border-red-700 rounded-lg">
          <p className="text-red-300 text-sm">{error}</p>
        </div>
      )}

      {/* Conteúdo */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Apps de Segurança */}
        <div>
          <h2 className="text-sm font-semibold text-blue-400 mb-3 flex items-center">
            🛡️ Segurança
          </h2>
          <div className="space-y-3">
            {getAppsByCategory('security').map(renderAppCard)}
          </div>
        </div>

        {/* Apps de Ataque */}
        <div>
          <h2 className="text-sm font-semibold text-red-400 mb-3 flex items-center">
            ⚔️ Ataque
          </h2>
          <div className="space-y-3">
            {getAppsByCategory('attack').map(renderAppCard)}
          </div>
        </div>

        {/* Apps Utilitários */}
        <div>
          <h2 className="text-sm font-semibold text-purple-400 mb-3 flex items-center">
            🔧 Utilitários
          </h2>
          <div className="space-y-3">
            {getAppsByCategory('utility').map(renderAppCard)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameAppsPage;
