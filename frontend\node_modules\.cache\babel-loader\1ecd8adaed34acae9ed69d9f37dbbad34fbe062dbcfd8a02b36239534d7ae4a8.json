{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleLoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleLoginPage = () => {\n  _s();\n  const {\n    isLoading,\n    error,\n    login,\n    register,\n    simulateLogin,\n    isAuthenticated,\n    user,\n    clearError\n  } = useSimpleAuth();\n  const [isRegisterMode, setIsRegisterMode] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    nick: ''\n  });\n  console.log('SimpleLoginPage - Estado:', {\n    isLoading,\n    isAuthenticated,\n    user: !!user\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Limpar erro quando usuário digita\n    if (error) clearError();\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (isRegisterMode) {\n      if (!formData.email || !formData.password || !formData.nick) {\n        return;\n      }\n      await register({\n        email: formData.email,\n        password: formData.password,\n        nick: formData.nick\n      });\n    } else {\n      if (!formData.email || !formData.password) {\n        return;\n      }\n      await login({\n        email: formData.email,\n        password: formData.password\n      });\n    }\n  };\n  const toggleMode = () => {\n    setIsRegisterMode(!isRegisterMode);\n    setFormData({\n      email: '',\n      password: '',\n      nick: ''\n    });\n    clearError();\n  };\n\n  // Se já está autenticado, mostrar sucesso e aguardar carregamento\n  if (isAuthenticated && user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-green-400 mb-4\",\n          children: \"Login Realizado!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-300 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Bem-vindo, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: user.nick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 27\n            }, this), \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: [\"UID: \", user.uid]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: [\"Email: \", user.email]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-green-400 mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-green-300\",\n            children: \"\\uD83C\\uDFAE Carregando o jogo...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-6xl mb-4\",\n        children: \"\\uD83D\\uDD12\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold mb-2\",\n        children: \"SHACK\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-400\",\n        children: \"Simulador de Hacking Online\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 px-6 py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800/50 rounded-2xl p-6 border border-gray-700/50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsRegisterMode(false),\n            className: `flex-1 py-2 px-4 rounded-l-lg font-semibold transition-colors ${!isRegisterMode ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsRegisterMode(true),\n            className: `flex-1 py-2 px-4 rounded-r-lg font-semibold transition-colors ${isRegisterMode ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,\n            children: \"Registrar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-300 text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-4\",\n          children: [isRegisterMode && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Nome do Hacker\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"nick\",\n              value: formData.nick,\n              onChange: handleInputChange,\n              placeholder: \"Digite seu nick\",\n              maxLength: 20,\n              disabled: isLoading,\n              className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleInputChange,\n              placeholder: \"<EMAIL>\",\n              disabled: isLoading,\n              className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Senha\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              name: \"password\",\n              value: formData.password,\n              onChange: handleInputChange,\n              placeholder: \"Digite sua senha\",\n              disabled: isLoading,\n              className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading,\n            className: \"w-full mt-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isRegisterMode ? 'Criando conta...' : 'Entrando...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this) : isRegisterMode ? 'Criar Conta' : 'Entrar'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold mb-2 flex items-center\",\n          children: \"\\uD83E\\uDDEA Teste R\\xE1pido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-300 mb-4\",\n          children: \"Para testar rapidamente sem preencher formul\\xE1rio:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: simulateLogin,\n          disabled: isLoading,\n          className: \"w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors\",\n          children: \"\\u26A1 Login Instant\\xE2neo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold mb-2 flex items-center\",\n          children: \"\\uD83C\\uDFAE Sobre o SHACK\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"text-sm text-gray-300 space-y-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Simulador de hacking online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Fa\\xE7a upgrades nos seus aplicativos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Invada outros jogadores\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Chat global em tempo real\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Sistema de ranking competitivo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 py-2 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500\",\n        children: \"SHACK v2.0 - Simulador de Hacking Online\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleLoginPage, \"roLM9JythJepd9gq3OU2o3qxVa4=\", false, function () {\n  return [useSimpleAuth];\n});\n_c = SimpleLoginPage;\nexport default SimpleLoginPage;\nvar _c;\n$RefreshReg$(_c, \"SimpleLoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useSimpleAuth", "jsxDEV", "_jsxDEV", "SimpleLoginPage", "_s", "isLoading", "error", "login", "register", "simulateLogin", "isAuthenticated", "user", "clearError", "isRegisterMode", "setIsRegisterMode", "formData", "setFormData", "email", "password", "nick", "console", "log", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "toggleMode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "uid", "onClick", "onSubmit", "type", "onChange", "placeholder", "max<PERSON><PERSON><PERSON>", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleLoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\n\nconst SimpleLoginPage: React.FC = () => {\n  const { isLoading, error, login, register, simulateLogin, isAuthenticated, user, clearError } = useSimpleAuth();\n\n  const [isRegisterMode, setIsRegisterMode] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    nick: ''\n  });\n\n  console.log('SimpleLoginPage - Estado:', { isLoading, isAuthenticated, user: !!user });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Limpar erro quando usuário digita\n    if (error) clearError();\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (isRegisterMode) {\n      if (!formData.email || !formData.password || !formData.nick) {\n        return;\n      }\n      await register({\n        email: formData.email,\n        password: formData.password,\n        nick: formData.nick\n      });\n    } else {\n      if (!formData.email || !formData.password) {\n        return;\n      }\n      await login({\n        email: formData.email,\n        password: formData.password\n      });\n    }\n  };\n\n  const toggleMode = () => {\n    setIsRegisterMode(!isRegisterMode);\n    setFormData({ email: '', password: '', nick: '' });\n    clearError();\n  };\n\n  // Se já está autenticado, mostrar sucesso e aguardar carregamento\n  if (isAuthenticated && user) {\n    return (\n      <div className=\"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">✅</div>\n          <h1 className=\"text-2xl font-bold text-green-400 mb-4\">\n            Login Realizado!\n          </h1>\n          <div className=\"text-gray-300 mb-6\">\n            <p>Bem-vindo, <strong>{user.nick}</strong>!</p>\n            <p className=\"text-sm\">UID: {user.uid}</p>\n            <p className=\"text-sm\">Email: {user.email}</p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-green-400 mx-auto mb-2\"></div>\n            <p className=\"text-sm text-green-300\">\n              🎮 Carregando o jogo...\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"p-6 text-center\">\n        <div className=\"text-6xl mb-4\">🔒</div>\n        <h1 className=\"text-2xl font-bold mb-2\">SHACK</h1>\n        <p className=\"text-sm text-gray-400\">Simulador de Hacking Online</p>\n      </div>\n\n      {/* Formulário */}\n      <div className=\"flex-1 px-6 py-4\">\n        <div className=\"bg-gray-800/50 rounded-2xl p-6 border border-gray-700/50\">\n          <div className=\"flex mb-6\">\n            <button\n              onClick={() => setIsRegisterMode(false)}\n              className={`flex-1 py-2 px-4 rounded-l-lg font-semibold transition-colors ${\n                !isRegisterMode\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n              }`}\n            >\n              Login\n            </button>\n            <button\n              onClick={() => setIsRegisterMode(true)}\n              className={`flex-1 py-2 px-4 rounded-r-lg font-semibold transition-colors ${\n                isRegisterMode\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n              }`}\n            >\n              Registrar\n            </button>\n          </div>\n\n          {/* Erro */}\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg\">\n              <p className=\"text-red-300 text-sm\">{error}</p>\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {isRegisterMode && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Nome do Hacker\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"nick\"\n                  value={formData.nick}\n                  onChange={handleInputChange}\n                  placeholder=\"Digite seu nick\"\n                  maxLength={20}\n                  disabled={isLoading}\n                  className=\"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n                />\n              </div>\n            )}\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Email\n              </label>\n              <input\n                type=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                placeholder=\"<EMAIL>\"\n                disabled={isLoading}\n                className=\"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Senha\n              </label>\n              <input\n                type=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                placeholder=\"Digite sua senha\"\n                disabled={isLoading}\n                className=\"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full mt-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center space-x-2\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  <span>{isRegisterMode ? 'Criando conta...' : 'Entrando...'}</span>\n                </div>\n              ) : (\n                isRegisterMode ? 'Criar Conta' : 'Entrar'\n              )}\n            </button>\n          </form>\n        </div>\n\n        {/* Teste Rápido */}\n        <div className=\"mt-6 bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\">\n          <h3 className=\"font-semibold mb-2 flex items-center\">\n            🧪 Teste Rápido\n          </h3>\n          <p className=\"text-sm text-gray-300 mb-4\">\n            Para testar rapidamente sem preencher formulário:\n          </p>\n          <button\n            onClick={simulateLogin}\n            disabled={isLoading}\n            className=\"w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors\"\n          >\n            ⚡ Login Instantâneo\n          </button>\n        </div>\n\n        {/* Informações do jogo */}\n        <div className=\"mt-6 bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\">\n          <h3 className=\"font-semibold mb-2 flex items-center\">\n            🎮 Sobre o SHACK\n          </h3>\n          <ul className=\"text-sm text-gray-300 space-y-1\">\n            <li>• Simulador de hacking online</li>\n            <li>• Faça upgrades nos seus aplicativos</li>\n            <li>• Invada outros jogadores</li>\n            <li>• Chat global em tempo real</li>\n            <li>• Sistema de ranking competitivo</li>\n          </ul>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"px-4 py-2 text-center\">\n        <p className=\"text-xs text-gray-500\">\n          SHACK v2.0 - Simulador de Hacking Online\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleLoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC,SAAS;IAAEC,KAAK;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,aAAa;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGZ,aAAa,CAAC,CAAC;EAE/G,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;EAEFC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;IAAEhB,SAAS;IAAEK,eAAe;IAAEC,IAAI,EAAE,CAAC,CAACA;EAAK,CAAC,CAAC;EAEtF,MAAMW,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCV,WAAW,CAACW,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAInB,KAAK,EAAEM,UAAU,CAAC,CAAC;EACzB,CAAC;EAED,MAAMgB,YAAY,GAAG,MAAOL,CAAkB,IAAK;IACjDA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,IAAIhB,cAAc,EAAE;MAClB,IAAI,CAACE,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,QAAQ,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;QAC3D;MACF;MACA,MAAMX,QAAQ,CAAC;QACbS,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BC,IAAI,EAAEJ,QAAQ,CAACI;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACJ,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;QACzC;MACF;MACA,MAAMX,KAAK,CAAC;QACVU,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvBhB,iBAAiB,CAAC,CAACD,cAAc,CAAC;IAClCG,WAAW,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC;IAClDP,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA,IAAIF,eAAe,IAAIC,IAAI,EAAE;IAC3B,oBACET,OAAA;MAAK6B,SAAS,EAAC,mHAAmH;MAAAC,QAAA,eAChI9B,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAK6B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtClC,OAAA;UAAI6B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAK6B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC9B,OAAA;YAAA8B,QAAA,GAAG,aAAW,eAAA9B,OAAA;cAAA8B,QAAA,EAASrB,IAAI,CAACQ;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/ClC,OAAA;YAAG6B,SAAS,EAAC,SAAS;YAAAC,QAAA,GAAC,OAAK,EAACrB,IAAI,CAAC0B,GAAG;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1ClC,OAAA;YAAG6B,SAAS,EAAC,SAAS;YAAAC,QAAA,GAAC,SAAO,EAACrB,IAAI,CAACM,KAAK;UAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNlC,OAAA;UAAK6B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9B,OAAA;YAAK6B,SAAS,EAAC;UAA4E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClGlC,OAAA;YAAG6B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAEtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAK6B,SAAS,EAAC,uFAAuF;IAAAC,QAAA,gBAEpG9B,OAAA;MAAK6B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B9B,OAAA;QAAK6B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvClC,OAAA;QAAI6B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClDlC,OAAA;QAAG6B,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAGNlC,OAAA;MAAK6B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B9B,OAAA;QAAK6B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE9B,OAAA;UAAK6B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9B,OAAA;YACEoC,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAAC,KAAK,CAAE;YACxCiB,SAAS,EAAE,iEACT,CAAClB,cAAc,GACX,wBAAwB,GACxB,6CAA6C,EAChD;YAAAmB,QAAA,EACJ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlC,OAAA;YACEoC,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAAC,IAAI,CAAE;YACvCiB,SAAS,EAAE,iEACTlB,cAAc,GACV,wBAAwB,GACxB,6CAA6C,EAChD;YAAAmB,QAAA,EACJ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGL9B,KAAK,iBACJJ,OAAA;UAAK6B,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eACtE9B,OAAA;YAAG6B,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAE1B;UAAK;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACN,eAEDlC,OAAA;UAAMqC,QAAQ,EAAEX,YAAa;UAACG,SAAS,EAAC,WAAW;UAAAC,QAAA,GAChDnB,cAAc,iBACbX,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAO6B,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACEsC,IAAI,EAAC,MAAM;cACXhB,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEV,QAAQ,CAACI,IAAK;cACrBsB,QAAQ,EAAEnB,iBAAkB;cAC5BoB,WAAW,EAAC,iBAAiB;cAC7BC,SAAS,EAAE,EAAG;cACdC,QAAQ,EAAEvC,SAAU;cACpB0B,SAAS,EAAC;YAAuJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAEDlC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAO6B,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACEsC,IAAI,EAAC,OAAO;cACZhB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEV,QAAQ,CAACE,KAAM;cACtBwB,QAAQ,EAAEnB,iBAAkB;cAC5BoB,WAAW,EAAC,eAAe;cAC3BE,QAAQ,EAAEvC,SAAU;cACpB0B,SAAS,EAAC;YAAuJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAO6B,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACEsC,IAAI,EAAC,UAAU;cACfhB,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEV,QAAQ,CAACG,QAAS;cACzBuB,QAAQ,EAAEnB,iBAAkB;cAC5BoB,WAAW,EAAC,kBAAkB;cAC9BE,QAAQ,EAAEvC,SAAU;cACpB0B,SAAS,EAAC;YAAuJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlC,OAAA;YACEsC,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAEvC,SAAU;YACpB0B,SAAS,EAAC,2HAA2H;YAAAC,QAAA,EAEpI3B,SAAS,gBACRH,OAAA;cAAK6B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD9B,OAAA;gBAAK6B,SAAS,EAAC;cAA2D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjFlC,OAAA;gBAAA8B,QAAA,EAAOnB,cAAc,GAAG,kBAAkB,GAAG;cAAa;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,GAENvB,cAAc,GAAG,aAAa,GAAG;UAClC;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlC,OAAA;QAAK6B,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3E9B,OAAA;UAAI6B,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAG6B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJlC,OAAA;UACEoC,OAAO,EAAE7B,aAAc;UACvBmC,QAAQ,EAAEvC,SAAU;UACpB0B,SAAS,EAAC,0HAA0H;UAAAC,QAAA,EACrI;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNlC,OAAA;QAAK6B,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3E9B,OAAA;UAAI6B,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAI6B,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC7C9B,OAAA;YAAA8B,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtClC,OAAA;YAAA8B,QAAA,EAAI;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ClC,OAAA;YAAA8B,QAAA,EAAI;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClClC,OAAA;YAAA8B,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpClC,OAAA;YAAA8B,QAAA,EAAI;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA;MAAK6B,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpC9B,OAAA;QAAG6B,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CAhOID,eAAyB;EAAA,QACmEH,aAAa;AAAA;AAAA6C,EAAA,GADzG1C,eAAyB;AAkO/B,eAAeA,eAAe;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}