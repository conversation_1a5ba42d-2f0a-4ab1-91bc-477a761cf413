import React from 'react';

interface GlowingIconProps {
  children: React.ReactNode;
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'cyan' | 'pink';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  glow?: boolean;
  pulse?: boolean;
  className?: string;
}

const GlowingIcon: React.FC<GlowingIconProps> = ({
  children,
  color = 'blue',
  size = 'md',
  glow = true,
  pulse = false,
  className = '',
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8 text-lg',
    md: 'w-12 h-12 text-2xl',
    lg: 'w-16 h-16 text-3xl',
    xl: 'w-20 h-20 text-4xl',
  };

  const colorClasses = {
    blue: 'text-blue-400 bg-blue-500/10 border-blue-500/30',
    green: 'text-green-400 bg-green-500/10 border-green-500/30',
    red: 'text-red-400 bg-red-500/10 border-red-500/30',
    yellow: 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30',
    purple: 'text-purple-400 bg-purple-500/10 border-purple-500/30',
    cyan: 'text-cyan-400 bg-cyan-500/10 border-cyan-500/30',
    pink: 'text-pink-400 bg-pink-500/10 border-pink-500/30',
  };

  const glowClasses = glow ? {
    blue: 'shadow-lg shadow-blue-500/30',
    green: 'shadow-lg shadow-green-500/30',
    red: 'shadow-lg shadow-red-500/30',
    yellow: 'shadow-lg shadow-yellow-500/30',
    purple: 'shadow-lg shadow-purple-500/30',
    cyan: 'shadow-lg shadow-cyan-500/30',
    pink: 'shadow-lg shadow-pink-500/30',
  } : {};

  const classes = [
    'rounded-xl border backdrop-blur-sm flex items-center justify-center transition-all duration-300 ease-in-out',
    sizeClasses[size],
    colorClasses[color],
    glow ? glowClasses[color] : '',
    pulse ? 'animate-pulse' : '',
    'hover:scale-110 hover:brightness-110',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

export default GlowingIcon;
