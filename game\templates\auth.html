<!DOCTYPE html>
<html>
<head>
    <title>SHACK - Login/Registro</title>
    <style>
        body { font-family: Arial, sans-serif; background: #0d1117; color: #fff; padding: 20px; }
        .auth-container { max-width: 400px; margin: 0 auto; background: #21262d; padding: 30px; border-radius: 10px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 10px; border: 1px solid #30363d; background: #0d1117; color: #fff; border-radius: 5px; }
        button { width: 100%; padding: 12px; background: #238636; color: #fff; border: none; border-radius: 5px; cursor: pointer; margin-top: 10px; }
        button:hover { background: #2ea043; }
        .toggle { text-align: center; margin-top: 20px; cursor: pointer; color: #58a6ff; }
        .error { color: #f85149; margin-top: 10px; }
        .success { color: #56d364; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="auth-container">
        <h2 id="auth-title">🎮 SHACK - Login</h2>
        
        <form id="auth-form">
            <div class="form-group" id="nick-group" style="display:none;">
                <label for="nick">Nickname:</label>
                <input type="text" id="nick" placeholder="Seu nickname no jogo">
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Senha:</label>
                <input type="password" id="password" placeholder="Sua senha" required>
            </div>
            
            <button type="submit" id="auth-button">🚀 Entrar</button>
        </form>
        
        <div class="toggle" id="toggle-auth">
            📝 Não tem conta? Registre-se
        </div>
        
        <div id="message"></div>
    </div>

    <script type="module">
        let isLogin = true;
        
        const authTitle = document.getElementById('auth-title');
        const authForm = document.getElementById('auth-form');
        const authButton = document.getElementById('auth-button');
        const toggleAuth = document.getElementById('toggle-auth');
        const nickGroup = document.getElementById('nick-group');
        const message = document.getElementById('message');
        
        function toggleMode() {
            isLogin = !isLogin;
            
            if (isLogin) {
                authTitle.textContent = '🎮 SHACK - Login';
                authButton.textContent = '🚀 Entrar';
                toggleAuth.textContent = '📝 Não tem conta? Registre-se';
                nickGroup.style.display = 'none';
                document.getElementById('nick').required = false;
            } else {
                authTitle.textContent = '🎮 SHACK - Registro';
                authButton.textContent = '📝 Criar Conta';
                toggleAuth.textContent = '🚀 Já tem conta? Faça login';
                nickGroup.style.display = 'block';
                document.getElementById('nick').required = true;
            }
            
            message.innerHTML = '';
        }
        
        toggleAuth.addEventListener('click', toggleMode);
        
        authForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const nick = document.getElementById('nick').value;
            
            const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register';
            const payload = isLogin ? { email, password } : { email, password, nick };
            
            try {
                authButton.textContent = isLogin ? '⏳ Entrando...' : '⏳ Criando...';
                authButton.disabled = true;
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(payload)
                });
                
                const result = await response.json();
                
                if (result.sucesso) {
                    message.innerHTML = `<div class="success">✅ ${isLogin ? 'Login' : 'Registro'} realizado com sucesso!</div>`;
                    
                    // Salva dados no localStorage
                    localStorage.setItem('shack_user', JSON.stringify(result.user));
                    localStorage.setItem('shack_token', result.token);
                    
                    // Redireciona para o jogo
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                    
                } else {
                    message.innerHTML = `<div class="error">❌ ${result.mensagem}</div>`;
                }
                
            } catch (error) {
                console.error('Erro:', error);
                message.innerHTML = `<div class="error">❌ Erro de conexão: ${error.message}</div>`;
            } finally {
                authButton.textContent = isLogin ? '🚀 Entrar' : '📝 Criar Conta';
                authButton.disabled = false;
            }
        });
        
        // Verifica se já está logado
        const savedUser = localStorage.getItem('shack_user');
        if (savedUser) {
            message.innerHTML = '<div class="success">✅ Você já está logado! Redirecionando...</div>';
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        }
    </script>
</body>
</html>
