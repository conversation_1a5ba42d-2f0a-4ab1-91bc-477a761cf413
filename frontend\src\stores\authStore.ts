import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import apiService from '../services/api';

export interface User {
  uid: string;
  nick: string;
  email: string;
}

interface AuthState {
  // Estado
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Ações
  login: (credentials: { email: string; password: string }) => Promise<void>;
  register: (userData: { email: string; password: string; nick: string }) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Estado inicial
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Ação de login
      login: async (credentials) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiService.login(credentials);
          
          if (response.sucesso && response.user && response.token) {
            // Salvar no localStorage (será persistido pelo zustand)
            set({
              user: response.user,
              token: response.token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });

            // Também salvar no localStorage para compatibilidade com código existente
            localStorage.setItem('shack_token', response.token);
            localStorage.setItem('shack_user', JSON.stringify(response.user));
          } else {
            throw new Error(response.mensagem || 'Erro desconhecido no login');
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Erro ao fazer login',
            isAuthenticated: false,
            user: null,
            token: null,
          });
          throw error;
        }
      },

      // Ação de registro
      register: async (userData) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiService.register(userData);
          
          if (response.sucesso && response.user && response.token) {
            set({
              user: response.user,
              token: response.token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });

            // Também salvar no localStorage para compatibilidade
            localStorage.setItem('shack_token', response.token);
            localStorage.setItem('shack_user', JSON.stringify(response.user));
          } else {
            throw new Error(response.mensagem || 'Erro desconhecido no registro');
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Erro ao criar conta',
            isAuthenticated: false,
            user: null,
            token: null,
          });
          throw error;
        }
      },

      // Ação de logout
      logout: () => {
        // Limpar localStorage
        localStorage.removeItem('shack_token');
        localStorage.removeItem('shack_user');
        
        // Limpar estado
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      // Verificar autenticação
      checkAuth: async () => {
        const { token, isLoading } = get();

        // Evitar múltiplas verificações simultâneas
        if (isLoading) {
          console.log('AuthStore - Verificação já em andamento, ignorando...');
          return;
        }

        if (!token) {
          console.log('AuthStore - Sem token, definindo como não autenticado');
          set({ isAuthenticated: false, isLoading: false });
          return;
        }

        console.log('AuthStore - Verificando token...');
        set({ isLoading: true });

        try {
          const isValid = await apiService.verifyToken();

          if (isValid) {
            console.log('AuthStore - Token válido');
            set({ isAuthenticated: true, isLoading: false });
          } else {
            console.log('AuthStore - Token inválido, fazendo logout');
            // Token inválido, fazer logout
            get().logout();
          }
        } catch (error) {
          // Erro na verificação, fazer logout
          console.error('AuthStore - Erro ao verificar autenticação:', error);
          get().logout();
        } finally {
          set({ isLoading: false });
        }
      },

      // Limpar erro
      clearError: () => {
        set({ error: null });
      },

      // Definir loading
      setLoading: (loading) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'shack-auth', // Nome da chave no localStorage
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Hook personalizado para usar autenticação
export const useAuth = () => {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    checkAuth,
    clearError,
    setLoading,
  } = useAuthStore();

  return {
    // Estado
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    
    // Ações
    login,
    register,
    logout,
    checkAuth,
    clearError,
    setLoading,
    
    // Computed
    isLoggedIn: isAuthenticated && !!user,
  };
};
