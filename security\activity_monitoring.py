from flask import request, g
import time
import json
import threading
import os
from datetime import datetime
import logging
from logging.handlers import RotatingFileHandler

class ActivityMonitor:
    """
    Class for monitoring user activity and detecting suspicious behavior
    """
    
    def __init__(self, app=None, log_dir="logs"):
        self.app = app
        self.log_dir = log_dir
        
        # Create logs directory if it doesn't exist
        os.makedirs(log_dir, exist_ok=True)
        
        # Set up logging
        self.setup_logging()
        
        # Set up suspicious activity thresholds
        self.suspicious_thresholds = {
            'failed_logins': 3,  # Number of failed logins before alert
            'rapid_requests': 20,  # Number of requests in rapid succession
            'rapid_request_window': 10,  # Time window in seconds for rapid requests
            'unauthorized_attempts': 3  # Number of unauthorized access attempts
        }
        
        # Activity tracking
        self.activity_log = {}
        self.lock = threading.Lock()
        
        if app is not None:
            self.init_app(app)
    
    def setup_logging(self):
        """Set up logging configuration"""
        log_file = os.path.join(self.log_dir, 'activity.log')
        
        # Configure handler
        handler = RotatingFileHandler(
            log_file, 
            maxBytes=10485760,  # 10MB
            backupCount=5
        )
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        # Configure logger
        self.logger = logging.getLogger('activity_monitor')
        self.logger.setLevel(logging.INFO)
        
        # Remove existing handlers to prevent duplicate logging
        for hdlr in self.logger.handlers[:]:
            self.logger.removeHandler(hdlr)
            
        self.logger.addHandler(handler)
    
    def init_app(self, app):
        """Initialize with Flask app"""
        self.app = app
        
        # Register before_request handler
        @app.before_request
        def log_request_start():
            # Record request start time
            g.start_time = time.time()
            g.request_id = str(int(g.start_time * 1000))
            
            # Basic request info
            request_info = {
                'timestamp': datetime.now().isoformat(),
                'method': request.method,
                'path': request.path,
                'ip': request.remote_addr,
                'user_agent': request.user_agent.string,
                'request_id': g.request_id
            }
            
            # Get user info if available (depends on your auth system)
            if hasattr(g, 'user'):
                request_info['user_id'] = getattr(g.user, 'id', None)
            
            # Log the request
            self.logger.info(f"Request: {json.dumps(request_info)}")
            
            # Record for pattern detection
            self._record_activity(request_info)
        
        # Register after_request handler
        @app.after_request
        def log_request_end(response):
            # If we have a start time, calculate duration
            if hasattr(g, 'start_time'):
                duration_ms = int((time.time() - g.start_time) * 1000)
                
                response_info = {
                    'request_id': getattr(g, 'request_id', 'unknown'),
                    'status_code': response.status_code,
                    'duration_ms': duration_ms
                }
                
                # Log the response
                self.logger.info(f"Response: {json.dumps(response_info)}")
            
            return response
        
        # Log application startup
        self.logger.info("Activity monitoring initialized")
        print("✅ Activity monitoring middleware initialized")
    
    def _record_activity(self, request_info):
        """Record user activity for pattern analysis"""
        ip = request_info['ip']
        
        with self.lock:
            if ip not in self.activity_log:
                self.activity_log[ip] = {
                    'requests': [],
                    'failed_logins': 0,
                    'unauthorized_attempts': 0,
                    'alerts': []
                }
            
            # Add to request list
            self.activity_log[ip]['requests'].append({
                'time': time.time(),
                'path': request_info['path'],
                'method': request_info['method']
            })
            
            # Clean up old requests
            current_time = time.time()
            cutoff_time = current_time - 3600  # Keep 1 hour of history
            self.activity_log[ip]['requests'] = [
                r for r in self.activity_log[ip]['requests'] 
                if r['time'] > cutoff_time
            ]
            
            # Check for rapid requests
            self._check_rapid_requests(ip)
    
    def _check_rapid_requests(self, ip):
        """Check if a user is making too many requests in a short time"""
        if ip not in self.activity_log:
            return
            
        requests = self.activity_log[ip]['requests']
        current_time = time.time()
        window_time = current_time - self.suspicious_thresholds['rapid_request_window']
        
        # Count requests in the time window
        recent_requests = [r for r in requests if r['time'] > window_time]
        
        if len(recent_requests) > self.suspicious_thresholds['rapid_requests']:
            alert_msg = f"Suspicious activity: {len(recent_requests)} requests in {self.suspicious_thresholds['rapid_request_window']}s from IP {ip}"
            self._record_alert(ip, alert_msg, 'rapid_requests')
    
    def log_failed_login(self, user_id, ip=None):
        """Log a failed login attempt"""
        if ip is None and request:
            ip = request.remote_addr
        
        if not ip:
            return
            
        with self.lock:
            if ip not in self.activity_log:
                self.activity_log[ip] = {
                    'requests': [],
                    'failed_logins': 0,
                    'unauthorized_attempts': 0,
                    'alerts': []
                }
            
            self.activity_log[ip]['failed_logins'] += 1
            
            # Log the failed login
            self.logger.warning(f"Failed login for user {user_id} from IP {ip}")
            
            # Check for multiple failed logins
            if self.activity_log[ip]['failed_logins'] >= self.suspicious_thresholds['failed_logins']:
                alert_msg = f"Multiple failed logins ({self.activity_log[ip]['failed_logins']}) for user {user_id} from IP {ip}"
                self._record_alert(ip, alert_msg, 'failed_logins')
    
    def log_unauthorized_access(self, resource, ip=None):
        """Log an unauthorized access attempt"""
        if ip is None and request:
            ip = request.remote_addr
            
        if not ip:
            return
            
        with self.lock:
            if ip not in self.activity_log:
                self.activity_log[ip] = {
                    'requests': [],
                    'failed_logins': 0,
                    'unauthorized_attempts': 0,
                    'alerts': []
                }
            
            self.activity_log[ip]['unauthorized_attempts'] += 1
            
            # Log the unauthorized access
            self.logger.warning(f"Unauthorized access attempt to {resource} from IP {ip}")
            
            # Check for multiple unauthorized attempts
            if self.activity_log[ip]['unauthorized_attempts'] >= self.suspicious_thresholds['unauthorized_attempts']:
                alert_msg = f"Multiple unauthorized access attempts ({self.activity_log[ip]['unauthorized_attempts']}) from IP {ip}"
                self._record_alert(ip, alert_msg, 'unauthorized_attempts')
    
    def _record_alert(self, ip, message, alert_type):
        """Record a security alert"""
        alert = {
            'time': time.time(),
            'type': alert_type,
            'message': message
        }
        
        # Add to IP's alert history
        self.activity_log[ip]['alerts'].append(alert)
        
        # Log the alert
        self.logger.error(f"SECURITY ALERT: {message}")
        print(f"⚠️ SECURITY ALERT: {message}")
        
        # Clean up old alerts
        current_time = time.time()
        cutoff_time = current_time - 86400  # Keep 24 hours of alerts
        self.activity_log[ip]['alerts'] = [
            a for a in self.activity_log[ip]['alerts'] 
            if a['time'] > cutoff_time
        ]