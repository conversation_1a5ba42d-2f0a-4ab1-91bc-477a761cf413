{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\TransferPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TransferPage = () => {\n  _s();\n  var _currentPlayer$dinhei;\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    currentPlayer,\n    loadPlayerData\n  } = usePlayer();\n\n  // Estados para transferência voluntária\n  const [targetNick, setTargetNick] = useState('');\n  const [amount, setAmount] = useState('');\n  const [description, setDescription] = useState('');\n  const [isTransferring, setIsTransferring] = useState(false);\n  const [transferError, setTransferError] = useState(null);\n  const [transferSuccess, setTransferSuccess] = useState(null);\n\n  // Estados para transferência forçada\n  const [forceTargetUid, setForceTargetUid] = useState('');\n  const [forcePercentage, setForcePercentage] = useState('50');\n  const [isForcingTransfer, setIsForcingTransfer] = useState(false);\n\n  // Estados para conexões ativas\n  const [activeConnections, setActiveConnections] = useState([]);\n  const [isLoadingConnections, setIsLoadingConnections] = useState(false);\n\n  // Estados para histórico\n  const [transferHistory, setTransferHistory] = useState([]);\n  const [isLoadingHistory, setIsLoadingHistory] = useState(false);\n  useEffect(() => {\n    if (isAuthenticated && !currentPlayer) {\n      loadPlayerData();\n    }\n    loadTransferHistory();\n    loadActiveConnections();\n  }, [isAuthenticated, currentPlayer, loadPlayerData]);\n  const loadTransferHistory = async () => {\n    setIsLoadingHistory(true);\n    try {\n      const response = await gameApi.getTransferHistory();\n      if (response.sucesso) {\n        setTransferHistory(response.transferencias || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar histórico:', error);\n    } finally {\n      setIsLoadingHistory(false);\n    }\n  };\n  const loadActiveConnections = async () => {\n    setIsLoadingConnections(true);\n    try {\n      const response = await gameApi.getActiveConnections();\n      if (response.sucesso) {\n        setActiveConnections(response.conexoes || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar conexões:', error);\n    } finally {\n      setIsLoadingConnections(false);\n    }\n  };\n  const handleVoluntaryTransfer = async e => {\n    e.preventDefault();\n    if (!targetNick.trim() || !amount.trim()) {\n      setTransferError('Nick do destinatário e valor são obrigatórios');\n      return;\n    }\n    const transferAmount = parseInt(amount);\n    if (isNaN(transferAmount) || transferAmount <= 0) {\n      setTransferError('Valor deve ser um número positivo');\n      return;\n    }\n    if (currentPlayer && transferAmount > ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.dinheiro) || 0)) {\n      setTransferError('Saldo insuficiente');\n      return;\n    }\n    setIsTransferring(true);\n    setTransferError(null);\n    setTransferSuccess(null);\n    try {\n      const response = await gameApi.transferMoney(targetNick, transferAmount, description);\n      if (response.sucesso) {\n        setTransferSuccess(`Transferência realizada com sucesso! ${response.mensagem}`);\n        setTargetNick('');\n        setAmount('');\n        setDescription('');\n\n        // Recarregar dados do jogador\n        loadPlayerData();\n        loadTransferHistory();\n      } else {\n        setTransferError(response.mensagem || 'Erro na transferência');\n      }\n    } catch (error) {\n      setTransferError(error.message || 'Erro de conexão');\n    } finally {\n      setIsTransferring(false);\n    }\n  };\n  const handleForceTransfer = async e => {\n    e.preventDefault();\n    if (!forceTargetUid.trim()) {\n      setTransferError('UID do alvo é obrigatório');\n      return;\n    }\n    const percentage = parseInt(forcePercentage);\n    if (isNaN(percentage) || percentage < 20 || percentage > 80) {\n      setTransferError('Porcentagem deve estar entre 20% e 80%');\n      return;\n    }\n    setIsForcingTransfer(true);\n    setTransferError(null);\n    setTransferSuccess(null);\n    try {\n      const response = await gameApi.forceTransfer(forceTargetUid, percentage);\n      if (response.sucesso) {\n        setTransferSuccess(`Transferência forçada realizada! ${response.mensagem}`);\n        setForceTargetUid('');\n\n        // Recarregar dados do jogador\n        loadPlayerData();\n        loadTransferHistory();\n      } else {\n        setTransferError(response.mensagem || 'Erro na transferência forçada');\n      }\n    } catch (error) {\n      setTransferError(error.message || 'Erro de conexão');\n    } finally {\n      setIsForcingTransfer(false);\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-900 text-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"\\uD83D\\uDD12 Acesso Negado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Voc\\xEA precisa estar logado para acessar as transfer\\xEAncias\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold\",\n            children: \"\\uD83D\\uDCB0 Transfer\\xEAncias\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Sistema de Pagamentos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-2 text-white\",\n          children: \"\\uD83D\\uDCB3 Saldo Atual\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-green-400\",\n          children: [\"$\", (currentPlayer === null || currentPlayer === void 0 ? void 0 : (_currentPlayer$dinhei = currentPlayer.dinheiro) === null || _currentPlayer$dinhei === void 0 ? void 0 : _currentPlayer$dinhei.toLocaleString()) || '0']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-400\",\n          children: \"Dispon\\xEDvel para transfer\\xEAncia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), transferError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-300 text-sm\",\n          children: [\"\\u274C \", transferError]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this), transferSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-900 border border-green-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-300 text-sm\",\n          children: [\"\\u2705 \", transferSuccess]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"\\uD83D\\uDCE4 Transfer\\xEAncia Volunt\\xE1ria\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleVoluntaryTransfer,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Nick do Destinat\\xE1rio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: targetNick,\n              onChange: e => setTargetNick(e.target.value),\n              placeholder: \"Digite o nick do jogador\",\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isTransferring\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Valor ($)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: amount,\n              onChange: e => setAmount(e.target.value),\n              placeholder: \"0\",\n              min: \"1\",\n              max: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.dinheiro) || 0,\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isTransferring\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Descri\\xE7\\xE3o (opcional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: description,\n              onChange: e => setDescription(e.target.value),\n              placeholder: \"Motivo da transfer\\xEAncia\",\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isTransferring\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isTransferring || !targetNick.trim() || !amount.trim(),\n            className: \"w-full py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n            children: isTransferring ? 'Transferindo...' : 'Transferir Dinheiro'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), activeConnections.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-green-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-green-400\",\n          children: \"\\uD83D\\uDD17 Conex\\xF5es Ativas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-400 mb-4\",\n          children: \"Alvos exploitados dispon\\xEDveis para transfer\\xEAncia for\\xE7ada\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: activeConnections.map((connection, index) => {\n            var _connection$alvo_dinh;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-700 rounded-lg p-3 border border-gray-600\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-bold text-white\",\n                    children: connection.alvo_nick\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-400\",\n                    children: [\"IP: \", connection.alvo_ip]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-green-400\",\n                    children: [\"Dinheiro: $\", ((_connection$alvo_dinh = connection.alvo_dinheiro) === null || _connection$alvo_dinh === void 0 ? void 0 : _connection$alvo_dinh.toLocaleString()) || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setForceTargetUid(connection.alvo_uid || connection.alvo_ip);\n                    setForcePercentage('50');\n                  },\n                  className: \"px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm\",\n                  children: \"Selecionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-red-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-red-400\",\n          children: \"\\u2694\\uFE0F Transfer\\xEAncia For\\xE7ada\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-400 mb-4\",\n          children: \"Apenas dispon\\xEDvel ap\\xF3s exploit bem-sucedido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleForceTransfer,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"UID do Alvo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: forceTargetUid,\n              onChange: e => setForceTargetUid(e.target.value),\n              placeholder: \"UID do jogador exploitado\",\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isForcingTransfer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Porcentagem (20% - 80%)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: \"20\",\n              max: \"80\",\n              value: forcePercentage,\n              onChange: e => setForcePercentage(e.target.value),\n              className: \"w-full\",\n              disabled: isForcingTransfer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-sm text-gray-400 mt-1\",\n              children: [forcePercentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isForcingTransfer || !forceTargetUid.trim(),\n            className: \"w-full py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n            children: isForcingTransfer ? 'Forçando...' : 'Forçar Transferência'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"Voltar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(TransferPage, \"5tAsHJ4BYcxQFw7+WLoYFJnKMU4=\", false, function () {\n  return [useAuth, usePlayer];\n});\n_c = TransferPage;\nexport default TransferPage;\nvar _c;\n$RefreshReg$(_c, \"TransferPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "usePlayer", "gameApi", "jsxDEV", "_jsxDEV", "TransferPage", "_s", "_currentPlayer$dinhei", "user", "isAuthenticated", "currentPlayer", "loadPlayerData", "targetNick", "setTargetNick", "amount", "setAmount", "description", "setDescription", "isTransferring", "setIsTransferring", "transferError", "setTransferError", "transferSuccess", "setTransferSuccess", "forceTargetUid", "setForceTargetUid", "forcePercentage", "setForcePercentage", "isForcingTransfer", "setIsForcingTransfer", "activeConnections", "setActiveConnections", "isLoadingConnections", "setIsLoadingConnections", "transferHistory", "setTransferHistory", "isLoadingHistory", "setIsLoadingHistory", "loadTransferHistory", "loadActiveConnections", "response", "getTransferHistory", "sucesso", "transferencias", "error", "console", "getActiveConnections", "conexoes", "handleVoluntaryTransfer", "e", "preventDefault", "trim", "transferAmount", "parseInt", "isNaN", "<PERSON><PERSON><PERSON>", "transferMoney", "mensagem", "message", "handleForceTransfer", "percentage", "forceTransfer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "history", "back", "toLocaleString", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "min", "max", "length", "map", "connection", "index", "_connection$alvo_dinh", "alvo_nick", "alvo_ip", "alvo_din<PERSON><PERSON>", "alvo_uid", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/TransferPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\n\nconst TransferPage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const { currentPlayer, loadPlayerData } = usePlayer();\n  \n  // Estados para transferência voluntária\n  const [targetNick, setTargetNick] = useState('');\n  const [amount, setAmount] = useState('');\n  const [description, setDescription] = useState('');\n  const [isTransferring, setIsTransferring] = useState(false);\n  const [transferError, setTransferError] = useState<string | null>(null);\n  const [transferSuccess, setTransferSuccess] = useState<string | null>(null);\n  \n  // Estados para transferência forçada\n  const [forceTargetUid, setForceTargetUid] = useState('');\n  const [forcePercentage, setForcePercentage] = useState('50');\n  const [isForcingTransfer, setIsForcingTransfer] = useState(false);\n\n  // Estados para conexões ativas\n  const [activeConnections, setActiveConnections] = useState<any[]>([]);\n  const [isLoadingConnections, setIsLoadingConnections] = useState(false);\n  \n  // Estados para histórico\n  const [transferHistory, setTransferHistory] = useState<any[]>([]);\n  const [isLoadingHistory, setIsLoadingHistory] = useState(false);\n\n  useEffect(() => {\n    if (isAuthenticated && !currentPlayer) {\n      loadPlayerData();\n    }\n    loadTransferHistory();\n    loadActiveConnections();\n  }, [isAuthenticated, currentPlayer, loadPlayerData]);\n\n  const loadTransferHistory = async () => {\n    setIsLoadingHistory(true);\n    try {\n      const response = await gameApi.getTransferHistory();\n      if (response.sucesso) {\n        setTransferHistory(response.transferencias || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar histórico:', error);\n    } finally {\n      setIsLoadingHistory(false);\n    }\n  };\n\n  const loadActiveConnections = async () => {\n    setIsLoadingConnections(true);\n    try {\n      const response = await gameApi.getActiveConnections();\n      if (response.sucesso) {\n        setActiveConnections(response.conexoes || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar conexões:', error);\n    } finally {\n      setIsLoadingConnections(false);\n    }\n  };\n\n  const handleVoluntaryTransfer = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!targetNick.trim() || !amount.trim()) {\n      setTransferError('Nick do destinatário e valor são obrigatórios');\n      return;\n    }\n\n    const transferAmount = parseInt(amount);\n    if (isNaN(transferAmount) || transferAmount <= 0) {\n      setTransferError('Valor deve ser um número positivo');\n      return;\n    }\n\n    if (currentPlayer && transferAmount > (currentPlayer?.dinheiro || 0)) {\n      setTransferError('Saldo insuficiente');\n      return;\n    }\n\n    setIsTransferring(true);\n    setTransferError(null);\n    setTransferSuccess(null);\n\n    try {\n      const response = await gameApi.transferMoney(targetNick, transferAmount, description);\n      \n      if (response.sucesso) {\n        setTransferSuccess(`Transferência realizada com sucesso! ${response.mensagem}`);\n        setTargetNick('');\n        setAmount('');\n        setDescription('');\n        \n        // Recarregar dados do jogador\n        loadPlayerData();\n        loadTransferHistory();\n      } else {\n        setTransferError(response.mensagem || 'Erro na transferência');\n      }\n    } catch (error: any) {\n      setTransferError(error.message || 'Erro de conexão');\n    } finally {\n      setIsTransferring(false);\n    }\n  };\n\n  const handleForceTransfer = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!forceTargetUid.trim()) {\n      setTransferError('UID do alvo é obrigatório');\n      return;\n    }\n\n    const percentage = parseInt(forcePercentage);\n    if (isNaN(percentage) || percentage < 20 || percentage > 80) {\n      setTransferError('Porcentagem deve estar entre 20% e 80%');\n      return;\n    }\n\n    setIsForcingTransfer(true);\n    setTransferError(null);\n    setTransferSuccess(null);\n\n    try {\n      const response = await gameApi.forceTransfer(forceTargetUid, percentage);\n      \n      if (response.sucesso) {\n        setTransferSuccess(`Transferência forçada realizada! ${response.mensagem}`);\n        setForceTargetUid('');\n        \n        // Recarregar dados do jogador\n        loadPlayerData();\n        loadTransferHistory();\n      } else {\n        setTransferError(response.mensagem || 'Erro na transferência forçada');\n      }\n    } catch (error: any) {\n      setTransferError(error.message || 'Erro de conexão');\n    } finally {\n      setIsForcingTransfer(false);\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"h-screen bg-gray-900 text-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-4\">🔒 Acesso Negado</h1>\n          <p className=\"text-gray-400\">Você precisa estar logado para acessar as transferências</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n          >\n            <span className=\"text-lg\">←</span>\n          </button>\n          <div>\n            <h1 className=\"text-lg font-bold\">💰 Transferências</h1>\n            <p className=\"text-xs text-gray-400\">Sistema de Pagamentos</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-4\">\n        {/* Saldo atual */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-2 text-white\">💳 Saldo Atual</h3>\n          <div className=\"text-2xl font-bold text-green-400\">\n            ${currentPlayer?.dinheiro?.toLocaleString() || '0'}\n          </div>\n          <p className=\"text-xs text-gray-400\">Disponível para transferência</p>\n        </div>\n\n        {/* Mensagens de erro/sucesso */}\n        {transferError && (\n          <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n            <p className=\"text-red-300 text-sm\">❌ {transferError}</p>\n          </div>\n        )}\n\n        {transferSuccess && (\n          <div className=\"bg-green-900 border border-green-500 rounded-lg p-3\">\n            <p className=\"text-green-300 text-sm\">✅ {transferSuccess}</p>\n          </div>\n        )}\n\n        {/* Transferência Voluntária */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-4 text-white\">📤 Transferência Voluntária</h3>\n          <form onSubmit={handleVoluntaryTransfer} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Nick do Destinatário\n              </label>\n              <input\n                type=\"text\"\n                value={targetNick}\n                onChange={(e) => setTargetNick(e.target.value)}\n                placeholder=\"Digite o nick do jogador\"\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isTransferring}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Valor ($)\n              </label>\n              <input\n                type=\"number\"\n                value={amount}\n                onChange={(e) => setAmount(e.target.value)}\n                placeholder=\"0\"\n                min=\"1\"\n                max={currentPlayer?.dinheiro || 0}\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isTransferring}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Descrição (opcional)\n              </label>\n              <input\n                type=\"text\"\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                placeholder=\"Motivo da transferência\"\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isTransferring}\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isTransferring || !targetNick.trim() || !amount.trim()}\n              className=\"w-full py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n            >\n              {isTransferring ? 'Transferindo...' : 'Transferir Dinheiro'}\n            </button>\n          </form>\n        </div>\n\n        {/* Conexões Ativas */}\n        {activeConnections.length > 0 && (\n          <div className=\"bg-gray-800 rounded-lg p-4 border border-green-600\">\n            <h3 className=\"text-lg font-semibold mb-4 text-green-400\">🔗 Conexões Ativas</h3>\n            <p className=\"text-xs text-gray-400 mb-4\">\n              Alvos exploitados disponíveis para transferência forçada\n            </p>\n            <div className=\"space-y-3\">\n              {activeConnections.map((connection, index) => (\n                <div key={index} className=\"bg-gray-700 rounded-lg p-3 border border-gray-600\">\n                  <div className=\"flex justify-between items-center\">\n                    <div>\n                      <div className=\"font-bold text-white\">{connection.alvo_nick}</div>\n                      <div className=\"text-xs text-gray-400\">IP: {connection.alvo_ip}</div>\n                      <div className=\"text-xs text-green-400\">\n                        Dinheiro: ${connection.alvo_dinheiro?.toLocaleString() || '0'}\n                      </div>\n                    </div>\n                    <button\n                      onClick={() => {\n                        setForceTargetUid(connection.alvo_uid || connection.alvo_ip);\n                        setForcePercentage('50');\n                      }}\n                      className=\"px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm\"\n                    >\n                      Selecionar\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Transferência Forçada */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-red-600\">\n          <h3 className=\"text-lg font-semibold mb-4 text-red-400\">⚔️ Transferência Forçada</h3>\n          <p className=\"text-xs text-gray-400 mb-4\">\n            Apenas disponível após exploit bem-sucedido\n          </p>\n          <form onSubmit={handleForceTransfer} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                UID do Alvo\n              </label>\n              <input\n                type=\"text\"\n                value={forceTargetUid}\n                onChange={(e) => setForceTargetUid(e.target.value)}\n                placeholder=\"UID do jogador exploitado\"\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isForcingTransfer}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Porcentagem (20% - 80%)\n              </label>\n              <input\n                type=\"range\"\n                min=\"20\"\n                max=\"80\"\n                value={forcePercentage}\n                onChange={(e) => setForcePercentage(e.target.value)}\n                className=\"w-full\"\n                disabled={isForcingTransfer}\n              />\n              <div className=\"text-center text-sm text-gray-400 mt-1\">\n                {forcePercentage}%\n              </div>\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isForcingTransfer || !forceTargetUid.trim()}\n              className=\"w-full py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n            >\n              {isForcingTransfer ? 'Forçando...' : 'Forçar Transferência'}\n            </button>\n          </form>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-center\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\"\n          >\n            <span className=\"text-lg\">←</span>\n            <span className=\"text-sm\">Voltar</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TransferPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACnC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEU,aAAa;IAAEC;EAAe,CAAC,GAAGV,SAAS,CAAC,CAAC;;EAErD;EACA,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;;EAE3E;EACA,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACgC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjC,QAAQ,CAAQ,EAAE,CAAC;EACrE,MAAM,CAACkC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAQ,EAAE,CAAC;EACjE,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAE/DC,SAAS,CAAC,MAAM;IACd,IAAIU,eAAe,IAAI,CAACC,aAAa,EAAE;MACrCC,cAAc,CAAC,CAAC;IAClB;IACA2B,mBAAmB,CAAC,CAAC;IACrBC,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC9B,eAAe,EAAEC,aAAa,EAAEC,cAAc,CAAC,CAAC;EAEpD,MAAM2B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCD,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMtC,OAAO,CAACuC,kBAAkB,CAAC,CAAC;MACnD,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBP,kBAAkB,CAACK,QAAQ,CAACG,cAAc,IAAI,EAAE,CAAC;MACnD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACRP,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAME,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxCN,uBAAuB,CAAC,IAAI,CAAC;IAC7B,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMtC,OAAO,CAAC4C,oBAAoB,CAAC,CAAC;MACrD,IAAIN,QAAQ,CAACE,OAAO,EAAE;QACpBX,oBAAoB,CAACS,QAAQ,CAACO,QAAQ,IAAI,EAAE,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRX,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAMe,uBAAuB,GAAG,MAAOC,CAAkB,IAAK;IAC5DA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACtC,UAAU,CAACuC,IAAI,CAAC,CAAC,IAAI,CAACrC,MAAM,CAACqC,IAAI,CAAC,CAAC,EAAE;MACxC9B,gBAAgB,CAAC,+CAA+C,CAAC;MACjE;IACF;IAEA,MAAM+B,cAAc,GAAGC,QAAQ,CAACvC,MAAM,CAAC;IACvC,IAAIwC,KAAK,CAACF,cAAc,CAAC,IAAIA,cAAc,IAAI,CAAC,EAAE;MAChD/B,gBAAgB,CAAC,mCAAmC,CAAC;MACrD;IACF;IAEA,IAAIX,aAAa,IAAI0C,cAAc,IAAI,CAAA1C,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6C,QAAQ,KAAI,CAAC,CAAC,EAAE;MACpElC,gBAAgB,CAAC,oBAAoB,CAAC;MACtC;IACF;IAEAF,iBAAiB,CAAC,IAAI,CAAC;IACvBE,gBAAgB,CAAC,IAAI,CAAC;IACtBE,kBAAkB,CAAC,IAAI,CAAC;IAExB,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAMtC,OAAO,CAACsD,aAAa,CAAC5C,UAAU,EAAEwC,cAAc,EAAEpC,WAAW,CAAC;MAErF,IAAIwB,QAAQ,CAACE,OAAO,EAAE;QACpBnB,kBAAkB,CAAC,wCAAwCiB,QAAQ,CAACiB,QAAQ,EAAE,CAAC;QAC/E5C,aAAa,CAAC,EAAE,CAAC;QACjBE,SAAS,CAAC,EAAE,CAAC;QACbE,cAAc,CAAC,EAAE,CAAC;;QAElB;QACAN,cAAc,CAAC,CAAC;QAChB2B,mBAAmB,CAAC,CAAC;MACvB,CAAC,MAAM;QACLjB,gBAAgB,CAACmB,QAAQ,CAACiB,QAAQ,IAAI,uBAAuB,CAAC;MAChE;IACF,CAAC,CAAC,OAAOb,KAAU,EAAE;MACnBvB,gBAAgB,CAACuB,KAAK,CAACc,OAAO,IAAI,iBAAiB,CAAC;IACtD,CAAC,SAAS;MACRvC,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMwC,mBAAmB,GAAG,MAAOV,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC1B,cAAc,CAAC2B,IAAI,CAAC,CAAC,EAAE;MAC1B9B,gBAAgB,CAAC,2BAA2B,CAAC;MAC7C;IACF;IAEA,MAAMuC,UAAU,GAAGP,QAAQ,CAAC3B,eAAe,CAAC;IAC5C,IAAI4B,KAAK,CAACM,UAAU,CAAC,IAAIA,UAAU,GAAG,EAAE,IAAIA,UAAU,GAAG,EAAE,EAAE;MAC3DvC,gBAAgB,CAAC,wCAAwC,CAAC;MAC1D;IACF;IAEAQ,oBAAoB,CAAC,IAAI,CAAC;IAC1BR,gBAAgB,CAAC,IAAI,CAAC;IACtBE,kBAAkB,CAAC,IAAI,CAAC;IAExB,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAMtC,OAAO,CAAC2D,aAAa,CAACrC,cAAc,EAAEoC,UAAU,CAAC;MAExE,IAAIpB,QAAQ,CAACE,OAAO,EAAE;QACpBnB,kBAAkB,CAAC,oCAAoCiB,QAAQ,CAACiB,QAAQ,EAAE,CAAC;QAC3EhC,iBAAiB,CAAC,EAAE,CAAC;;QAErB;QACAd,cAAc,CAAC,CAAC;QAChB2B,mBAAmB,CAAC,CAAC;MACvB,CAAC,MAAM;QACLjB,gBAAgB,CAACmB,QAAQ,CAACiB,QAAQ,IAAI,+BAA+B,CAAC;MACxE;IACF,CAAC,CAAC,OAAOb,KAAU,EAAE;MACnBvB,gBAAgB,CAACuB,KAAK,CAACc,OAAO,IAAI,iBAAiB,CAAC;IACtD,CAAC,SAAS;MACR7B,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,IAAI,CAACpB,eAAe,EAAE;IACpB,oBACEL,OAAA;MAAK0D,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/E3D,OAAA;QAAK0D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3D,OAAA;UAAI0D,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7D/D,OAAA;UAAG0D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAwD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/D,OAAA;IAAK0D,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5D3D,OAAA;MAAK0D,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrE3D,OAAA;QAAK0D,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C3D,OAAA;UACEgE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCT,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7F3D,OAAA;YAAM0D,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACT/D,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAAI0D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxD/D,OAAA;YAAG0D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/D,OAAA;MAAK0D,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBAEnD3D,OAAA;QAAK0D,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChE3D,OAAA;UAAI0D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE/D,OAAA;UAAK0D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAAC,GAChD,EAAC,CAAArD,aAAa,aAAbA,aAAa,wBAAAH,qBAAA,GAAbG,aAAa,CAAE6C,QAAQ,cAAAhD,qBAAA,uBAAvBA,qBAAA,CAAyBiE,cAAc,CAAC,CAAC,KAAI,GAAG;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACN/D,OAAA;UAAG0D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,EAGL/C,aAAa,iBACZhB,OAAA;QAAK0D,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9D3D,OAAA;UAAG0D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,SAAE,EAAC3C,aAAa;QAAA;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN,EAEA7C,eAAe,iBACdlB,OAAA;QAAK0D,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClE3D,OAAA;UAAG0D,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,SAAE,EAACzC,eAAe;QAAA;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CACN,eAGD/D,OAAA;QAAK0D,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChE3D,OAAA;UAAI0D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtF/D,OAAA;UAAMqE,QAAQ,EAAEzB,uBAAwB;UAACc,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAC5D3D,OAAA;YAAA2D,QAAA,gBACE3D,OAAA;cAAO0D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA;cACEsE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE/D,UAAW;cAClBgE,QAAQ,EAAG3B,CAAC,IAAKpC,aAAa,CAACoC,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cAC/CG,WAAW,EAAC,0BAA0B;cACtChB,SAAS,EAAC,wEAAwE;cAClFiB,QAAQ,EAAE7D;YAAe;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN/D,OAAA;YAAA2D,QAAA,gBACE3D,OAAA;cAAO0D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAE7D,MAAO;cACd8D,QAAQ,EAAG3B,CAAC,IAAKlC,SAAS,CAACkC,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cAC3CG,WAAW,EAAC,GAAG;cACfE,GAAG,EAAC,GAAG;cACPC,GAAG,EAAE,CAAAvE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6C,QAAQ,KAAI,CAAE;cAClCO,SAAS,EAAC,wEAAwE;cAClFiB,QAAQ,EAAE7D;YAAe;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN/D,OAAA;YAAA2D,QAAA,gBACE3D,OAAA;cAAO0D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA;cACEsE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE3D,WAAY;cACnB4D,QAAQ,EAAG3B,CAAC,IAAKhC,cAAc,CAACgC,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cAChDG,WAAW,EAAC,4BAAyB;cACrChB,SAAS,EAAC,wEAAwE;cAClFiB,QAAQ,EAAE7D;YAAe;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN/D,OAAA;YACEsE,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAE7D,cAAc,IAAI,CAACN,UAAU,CAACuC,IAAI,CAAC,CAAC,IAAI,CAACrC,MAAM,CAACqC,IAAI,CAAC,CAAE;YACjEW,SAAS,EAAC,sGAAsG;YAAAC,QAAA,EAE/G7C,cAAc,GAAG,iBAAiB,GAAG;UAAqB;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGLrC,iBAAiB,CAACoD,MAAM,GAAG,CAAC,iBAC3B9E,OAAA;QAAK0D,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjE3D,OAAA;UAAI0D,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjF/D,OAAA;UAAG0D,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ/D,OAAA;UAAK0D,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBjC,iBAAiB,CAACqD,GAAG,CAAC,CAACC,UAAU,EAAEC,KAAK;YAAA,IAAAC,qBAAA;YAAA,oBACvClF,OAAA;cAAiB0D,SAAS,EAAC,mDAAmD;cAAAC,QAAA,eAC5E3D,OAAA;gBAAK0D,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD3D,OAAA;kBAAA2D,QAAA,gBACE3D,OAAA;oBAAK0D,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAEqB,UAAU,CAACG;kBAAS;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClE/D,OAAA;oBAAK0D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,MAAI,EAACqB,UAAU,CAACI,OAAO;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrE/D,OAAA;oBAAK0D,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,GAAC,aAC3B,EAAC,EAAAuB,qBAAA,GAAAF,UAAU,CAACK,aAAa,cAAAH,qBAAA,uBAAxBA,qBAAA,CAA0Bd,cAAc,CAAC,CAAC,KAAI,GAAG;kBAAA;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/D,OAAA;kBACEgE,OAAO,EAAEA,CAAA,KAAM;oBACb3C,iBAAiB,CAAC2D,UAAU,CAACM,QAAQ,IAAIN,UAAU,CAACI,OAAO,CAAC;oBAC5D7D,kBAAkB,CAAC,IAAI,CAAC;kBAC1B,CAAE;kBACFmC,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,EACjF;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC,GAlBEkB,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBV,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD/D,OAAA;QAAK0D,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/D3D,OAAA;UAAI0D,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrF/D,OAAA;UAAG0D,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ/D,OAAA;UAAMqE,QAAQ,EAAEd,mBAAoB;UAACG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxD3D,OAAA;YAAA2D,QAAA,gBACE3D,OAAA;cAAO0D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA;cACEsE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEnD,cAAe;cACtBoD,QAAQ,EAAG3B,CAAC,IAAKxB,iBAAiB,CAACwB,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cACnDG,WAAW,EAAC,2BAA2B;cACvChB,SAAS,EAAC,wEAAwE;cAClFiB,QAAQ,EAAEnD;YAAkB;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN/D,OAAA;YAAA2D,QAAA,gBACE3D,OAAA;cAAO0D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA;cACEsE,IAAI,EAAC,OAAO;cACZM,GAAG,EAAC,IAAI;cACRC,GAAG,EAAC,IAAI;cACRN,KAAK,EAAEjD,eAAgB;cACvBkD,QAAQ,EAAG3B,CAAC,IAAKtB,kBAAkB,CAACsB,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cACpDb,SAAS,EAAC,QAAQ;cAClBiB,QAAQ,EAAEnD;YAAkB;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACF/D,OAAA;cAAK0D,SAAS,EAAC,wCAAwC;cAAAC,QAAA,GACpDrC,eAAe,EAAC,GACnB;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/D,OAAA;YACEsE,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAEnD,iBAAiB,IAAI,CAACJ,cAAc,CAAC2B,IAAI,CAAC,CAAE;YACtDW,SAAS,EAAC,kGAAkG;YAAAC,QAAA,EAE3GnC,iBAAiB,GAAG,aAAa,GAAG;UAAsB;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/D,OAAA;MAAK0D,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrE3D,OAAA;QAAK0D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClC3D,OAAA;UACEgE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCT,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExF3D,OAAA;YAAM0D,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClC/D,OAAA;YAAM0D,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CAjWID,YAAsB;EAAA,QACQL,OAAO,EACCC,SAAS;AAAA;AAAA0F,EAAA,GAF/CtF,YAAsB;AAmW5B,eAAeA,YAAY;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}