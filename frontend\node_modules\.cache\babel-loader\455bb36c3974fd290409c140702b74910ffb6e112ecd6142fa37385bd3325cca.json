{"ast": null, "code": "import gameApi from './gameApi';\n\n// Tipos para o sistema de jogo\n\nclass GameService {\n  constructor() {\n    this.currentPlayer = null;\n    this.exploitedTarget = null;\n    this.activeConnections = new Map();\n    this.bruteforceTimers = new Map();\n    // === SISTEMA DE CACHE ===\n    this.playerDataCache = null;\n    this.lastPlayerDataUpdate = 0;\n    this.CACHE_DURATION = 15000;\n  }\n  // 15 segundos\n\n  // === CARREGAMENTO DE DADOS ===\n  async loadPlayerData(forceRefresh = false) {\n    const now = Date.now();\n\n    // Usa cache se disponível e não forçou refresh\n    if (!forceRefresh && this.playerDataCache && now - this.lastPlayerDataUpdate < this.CACHE_DURATION) {\n      this.currentPlayer = this.playerDataCache;\n      return this.playerDataCache;\n    }\n    try {\n      const response = await gameApi.getPlayerData();\n      if (response.sucesso) {\n        this.currentPlayer = response.jogador;\n        this.playerDataCache = response.jogador;\n        this.lastPlayerDataUpdate = now;\n        return response.jogador;\n      } else {\n        throw new Error(response.mensagem || 'Erro ao carregar dados');\n      }\n    } catch (error) {\n      console.error('Erro ao carregar dados do jogador:', error);\n\n      // Se temos cache e erro não é crítico, usa cache\n      if (this.playerDataCache) {\n        this.currentPlayer = this.playerDataCache;\n        return this.playerDataCache;\n      }\n      throw error;\n    }\n  }\n  getCurrentPlayer() {\n    return this.currentPlayer;\n  }\n\n  // === SISTEMA DE SCAN ===\n  async performQuickScan() {\n    try {\n      const response = await gameApi.scanNetwork();\n      if (response.sucesso) {\n        return response.alvos || [];\n      } else {\n        throw new Error(response.mensagem || 'Erro no scan');\n      }\n    } catch (error) {\n      console.error('Erro no scan rápido:', error);\n      throw error;\n    }\n  }\n  async performAdvancedScan(targetIp) {\n    try {\n      const response = await gameApi.scanSpecificTarget(targetIp);\n      if (response.sucesso) {\n        return response.alvos ? [response.alvos] : [];\n      } else {\n        throw new Error(response.mensagem || 'Alvo não encontrado');\n      }\n    } catch (error) {\n      console.error('Erro no scan avançado:', error);\n      throw error;\n    }\n  }\n\n  // === SISTEMA DE EXPLOIT ===\n  async performExploit(target) {\n    try {\n      var _this$currentPlayer;\n      // Verificar se o jogador tem CPU suficiente\n      const playerCPU = ((_this$currentPlayer = this.currentPlayer) === null || _this$currentPlayer === void 0 ? void 0 : _this$currentPlayer.cpu) || 1;\n      const targetFirewall = target.firewall || 1;\n      if (playerCPU <= targetFirewall) {\n        throw new Error(`CPU insuficiente! Seu CPU (${playerCPU}) não pode quebrar Firewall (${targetFirewall})`);\n      }\n      const response = await gameApi.exploitTarget(target.ip);\n      if (response.sucesso && response.alvo_explorado) {\n        this.exploitedTarget = {\n          ...response.alvo_explorado,\n          exploited: true,\n          bruteforceStatus: 'none',\n          bankAccess: false\n        };\n\n        // Adicionar à lista de conexões ativas\n        this.activeConnections.set(target.ip, {\n          id: response.conexao_id || target.ip,\n          targetIp: target.ip,\n          targetNick: target.nick,\n          isActive: true,\n          bankAccess: false\n        });\n        return this.exploitedTarget;\n      } else {\n        throw new Error(response.mensagem || 'Exploit falhou');\n      }\n    } catch (error) {\n      console.error('Erro no exploit:', error);\n      throw error;\n    }\n  }\n  getExploitedTarget() {\n    return this.exploitedTarget;\n  }\n\n  // === SISTEMA DE TERMINAL E BRUTEFORCE ===\n  async getActiveConnections() {\n    try {\n      const response = await gameApi.getActiveConnections();\n      if (response.sucesso) {\n        var _response$conexoes;\n        // Atualizar mapa local de conexões\n        this.activeConnections.clear();\n        (_response$conexoes = response.conexoes) === null || _response$conexoes === void 0 ? void 0 : _response$conexoes.forEach(conn => {\n          this.activeConnections.set(conn.alvo_ip, {\n            id: conn.id,\n            targetIp: conn.alvo_ip,\n            targetNick: conn.alvo_nick,\n            isActive: true,\n            bankAccess: conn.banco_liberado || false\n          });\n        });\n        return Array.from(this.activeConnections.values());\n      }\n      return [];\n    } catch (error) {\n      console.error('Erro ao carregar conexões:', error);\n      return [];\n    }\n  }\n  async startBruteforce(connectionId, targetIp) {\n    try {\n      const response = await gameApi.startBruteforce(connectionId);\n      if (response.sucesso) {\n        const bruteforceStatus = {\n          isRunning: true,\n          timeRemaining: response.tempo_total || 60,\n          totalTime: response.tempo_total || 60,\n          targetIp,\n          progress: 0\n        };\n\n        // Atualizar status da conexão\n        const connection = this.activeConnections.get(targetIp);\n        if (connection) {\n          connection.bruteforceStatus = bruteforceStatus;\n        }\n\n        // Iniciar timer local\n        this.startBruteforceTimer(targetIp, bruteforceStatus.totalTime);\n        return bruteforceStatus;\n      } else {\n        throw new Error(response.mensagem || 'Erro ao iniciar bruteforce');\n      }\n    } catch (error) {\n      console.error('Erro ao iniciar bruteforce:', error);\n      throw error;\n    }\n  }\n  startBruteforceTimer(targetIp, totalTime) {\n    // Limpar timer existente\n    if (this.bruteforceTimers.has(targetIp)) {\n      clearInterval(this.bruteforceTimers.get(targetIp));\n    }\n    let timeRemaining = totalTime;\n    const timer = setInterval(async () => {\n      timeRemaining--;\n      const connection = this.activeConnections.get(targetIp);\n      if (connection !== null && connection !== void 0 && connection.bruteforceStatus) {\n        connection.bruteforceStatus.timeRemaining = timeRemaining;\n        connection.bruteforceStatus.progress = (totalTime - timeRemaining) / totalTime * 100;\n      }\n      if (timeRemaining <= 0) {\n        clearInterval(timer);\n        this.bruteforceTimers.delete(targetIp);\n\n        // Verificar conclusão do bruteforce\n        await this.checkBruteforceCompletion(targetIp);\n      }\n    }, 1000);\n    this.bruteforceTimers.set(targetIp, timer);\n  }\n  async checkBruteforceCompletion(targetIp) {\n    try {\n      const response = await gameApi.checkBruteforceStatus(targetIp);\n      if (response.sucesso && response.finalizado) {\n        var _this$exploitedTarget;\n        const connection = this.activeConnections.get(targetIp);\n        if (connection) {\n          connection.bruteforceStatus = undefined;\n          connection.bankAccess = true;\n        }\n\n        // Atualizar alvo exploitado se for o mesmo\n        if (((_this$exploitedTarget = this.exploitedTarget) === null || _this$exploitedTarget === void 0 ? void 0 : _this$exploitedTarget.ip) === targetIp) {\n          this.exploitedTarget.bruteforceStatus = 'completed';\n          this.exploitedTarget.bankAccess = true;\n        }\n      }\n    } catch (error) {\n      console.error('Erro ao verificar conclusão do bruteforce:', error);\n    }\n  }\n  async closeConnection(connectionId, targetIp) {\n    try {\n      const response = await gameApi.closeConnection(connectionId);\n      if (response.sucesso) {\n        var _this$exploitedTarget2;\n        this.activeConnections.delete(targetIp);\n\n        // Limpar timer se existir\n        if (this.bruteforceTimers.has(targetIp)) {\n          clearInterval(this.bruteforceTimers.get(targetIp));\n          this.bruteforceTimers.delete(targetIp);\n        }\n\n        // Limpar alvo exploitado se for o mesmo\n        if (((_this$exploitedTarget2 = this.exploitedTarget) === null || _this$exploitedTarget2 === void 0 ? void 0 : _this$exploitedTarget2.ip) === targetIp) {\n          this.exploitedTarget = null;\n        }\n      } else {\n        throw new Error(response.mensagem || 'Erro ao fechar conexão');\n      }\n    } catch (error) {\n      console.error('Erro ao fechar conexão:', error);\n      throw error;\n    }\n  }\n\n  // === SISTEMA DE TRANSFERÊNCIA BANCÁRIA ===\n  async performBankTransfer(targetIp, percentage) {\n    try {\n      const connection = this.activeConnections.get(targetIp);\n      if (!(connection !== null && connection !== void 0 && connection.bankAccess)) {\n        throw new Error('Acesso ao banco não liberado. Execute bruteforce primeiro.');\n      }\n      const response = await gameApi.transferMoney(targetIp, percentage);\n      if (response.sucesso) {\n        // Atualizar dados do jogador após transferência\n        await this.loadPlayerData(true);\n        return response;\n      } else {\n        throw new Error(response.mensagem || 'Erro na transferência');\n      }\n    } catch (error) {\n      console.error('Erro na transferência bancária:', error);\n      throw error;\n    }\n  }\n\n  // === LIMPEZA ===\n  cleanup() {\n    // Limpar todos os timers\n    this.bruteforceTimers.forEach(timer => clearInterval(timer));\n    this.bruteforceTimers.clear();\n\n    // Limpar dados\n    this.activeConnections.clear();\n    this.exploitedTarget = null;\n  }\n}\nexport default new GameService();", "map": {"version": 3, "names": ["gameApi", "GameService", "constructor", "currentPlayer", "<PERSON><PERSON>arget", "activeConnections", "Map", "bruteforceTimers", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastPlayerDataUpdate", "CACHE_DURATION", "loadPlayerData", "forceRefresh", "now", "Date", "response", "getPlayerData", "sucesso", "jogador", "Error", "mensagem", "error", "console", "getCurrentPlayer", "performQuickScan", "scanNetwork", "alvos", "performAdvancedScan", "targetIp", "scanSpecificTarget", "performExploit", "target", "_this$currentPlayer", "playerCPU", "cpu", "targetFirewall", "firewall", "exploitTarget", "ip", "alvo_explorado", "exploited", "bruteforceStatus", "bankAccess", "set", "id", "conexao_id", "targetNick", "nick", "isActive", "getExploitedTarget", "getActiveConnections", "_response$conexoes", "clear", "conexoes", "for<PERSON>ach", "conn", "alvo_ip", "alvo_nick", "banco_liberado", "Array", "from", "values", "startBruteforce", "connectionId", "isRunning", "timeRemaining", "tempo_total", "totalTime", "progress", "connection", "get", "startBruteforceTimer", "has", "clearInterval", "timer", "setInterval", "delete", "checkBruteforceCompletion", "checkBruteforceStatus", "finalizado", "_this$exploitedTarget", "undefined", "closeConnection", "_this$exploitedTarget2", "performBankTransfer", "percentage", "transferMoney", "cleanup"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/gameService.ts"], "sourcesContent": ["import gameApi from './gameApi';\n\n// Tipos para o sistema de jogo\nexport interface ScanTarget {\n  uid: string;\n  nick: string;\n  ip: string;\n  nivel: number;\n  firewall: number;\n  dinheiro: number;\n  cpu?: number;\n  ram?: number;\n  antivirus?: number;\n}\n\nexport interface ExploitedTarget extends ScanTarget {\n  exploited: boolean;\n  connectionId?: string;\n  bruteforceStatus?: 'none' | 'running' | 'completed';\n  bankAccess?: boolean;\n}\n\nexport interface BruteforceStatus {\n  isRunning: boolean;\n  timeRemaining: number;\n  totalTime: number;\n  targetIp: string;\n  progress: number;\n}\n\nexport interface ConnectionStatus {\n  id: string;\n  targetIp: string;\n  targetNick: string;\n  isActive: boolean;\n  bruteforceStatus?: BruteforceStatus;\n  bankAccess: boolean;\n}\n\nclass GameService {\n  private currentPlayer: any = null;\n  private exploitedTarget: ExploitedTarget | null = null;\n  private activeConnections: Map<string, ConnectionStatus> = new Map();\n  private bruteforceTimers: Map<string, NodeJS.Timeout> = new Map();\n\n  // === SISTEMA DE CACHE ===\n  private playerDataCache: any = null;\n  private lastPlayerDataUpdate = 0;\n  private readonly CACHE_DURATION = 15000; // 15 segundos\n\n  // === CARREGAMENTO DE DADOS ===\n  async loadPlayerData(forceRefresh = false): Promise<any> {\n    const now = Date.now();\n    \n    // Usa cache se disponível e não forçou refresh\n    if (!forceRefresh && this.playerDataCache && (now - this.lastPlayerDataUpdate) < this.CACHE_DURATION) {\n      this.currentPlayer = this.playerDataCache;\n      return this.playerDataCache;\n    }\n\n    try {\n      const response = await gameApi.getPlayerData();\n      if (response.sucesso) {\n        this.currentPlayer = response.jogador;\n        this.playerDataCache = response.jogador;\n        this.lastPlayerDataUpdate = now;\n        return response.jogador;\n      } else {\n        throw new Error(response.mensagem || 'Erro ao carregar dados');\n      }\n    } catch (error) {\n      console.error('Erro ao carregar dados do jogador:', error);\n      \n      // Se temos cache e erro não é crítico, usa cache\n      if (this.playerDataCache) {\n        this.currentPlayer = this.playerDataCache;\n        return this.playerDataCache;\n      }\n      \n      throw error;\n    }\n  }\n\n  getCurrentPlayer() {\n    return this.currentPlayer;\n  }\n\n  // === SISTEMA DE SCAN ===\n  async performQuickScan(): Promise<ScanTarget[]> {\n    try {\n      const response = await gameApi.scanNetwork();\n      if (response.sucesso) {\n        return response.alvos || [];\n      } else {\n        throw new Error(response.mensagem || 'Erro no scan');\n      }\n    } catch (error) {\n      console.error('Erro no scan rápido:', error);\n      throw error;\n    }\n  }\n\n  async performAdvancedScan(targetIp: string): Promise<ScanTarget[]> {\n    try {\n      const response = await gameApi.scanSpecificTarget(targetIp);\n      if (response.sucesso) {\n        return response.alvos ? [response.alvos] : [];\n      } else {\n        throw new Error(response.mensagem || 'Alvo não encontrado');\n      }\n    } catch (error) {\n      console.error('Erro no scan avançado:', error);\n      throw error;\n    }\n  }\n\n  // === SISTEMA DE EXPLOIT ===\n  async performExploit(target: ScanTarget): Promise<ExploitedTarget> {\n    try {\n      // Verificar se o jogador tem CPU suficiente\n      const playerCPU = this.currentPlayer?.cpu || 1;\n      const targetFirewall = target.firewall || 1;\n      \n      if (playerCPU <= targetFirewall) {\n        throw new Error(`CPU insuficiente! Seu CPU (${playerCPU}) não pode quebrar Firewall (${targetFirewall})`);\n      }\n\n      const response = await gameApi.exploitTarget(target.ip);\n      \n      if (response.sucesso && response.alvo_explorado) {\n        this.exploitedTarget = {\n          ...response.alvo_explorado,\n          exploited: true,\n          bruteforceStatus: 'none',\n          bankAccess: false\n        };\n        \n        // Adicionar à lista de conexões ativas\n        this.activeConnections.set(target.ip, {\n          id: response.conexao_id || target.ip,\n          targetIp: target.ip,\n          targetNick: target.nick,\n          isActive: true,\n          bankAccess: false\n        });\n        \n        return this.exploitedTarget;\n      } else {\n        throw new Error(response.mensagem || 'Exploit falhou');\n      }\n    } catch (error) {\n      console.error('Erro no exploit:', error);\n      throw error;\n    }\n  }\n\n  getExploitedTarget(): ExploitedTarget | null {\n    return this.exploitedTarget;\n  }\n\n  // === SISTEMA DE TERMINAL E BRUTEFORCE ===\n  async getActiveConnections(): Promise<ConnectionStatus[]> {\n    try {\n      const response = await gameApi.getActiveConnections();\n      if (response.sucesso) {\n        // Atualizar mapa local de conexões\n        this.activeConnections.clear();\n        response.conexoes?.forEach((conn: any) => {\n          this.activeConnections.set(conn.alvo_ip, {\n            id: conn.id,\n            targetIp: conn.alvo_ip,\n            targetNick: conn.alvo_nick,\n            isActive: true,\n            bankAccess: conn.banco_liberado || false\n          });\n        });\n        \n        return Array.from(this.activeConnections.values());\n      }\n      return [];\n    } catch (error) {\n      console.error('Erro ao carregar conexões:', error);\n      return [];\n    }\n  }\n\n  async startBruteforce(connectionId: string, targetIp: string): Promise<BruteforceStatus> {\n    try {\n      const response = await gameApi.startBruteforce(connectionId);\n      \n      if (response.sucesso) {\n        const bruteforceStatus: BruteforceStatus = {\n          isRunning: true,\n          timeRemaining: response.tempo_total || 60,\n          totalTime: response.tempo_total || 60,\n          targetIp,\n          progress: 0\n        };\n        \n        // Atualizar status da conexão\n        const connection = this.activeConnections.get(targetIp);\n        if (connection) {\n          connection.bruteforceStatus = bruteforceStatus;\n        }\n        \n        // Iniciar timer local\n        this.startBruteforceTimer(targetIp, bruteforceStatus.totalTime);\n        \n        return bruteforceStatus;\n      } else {\n        throw new Error(response.mensagem || 'Erro ao iniciar bruteforce');\n      }\n    } catch (error) {\n      console.error('Erro ao iniciar bruteforce:', error);\n      throw error;\n    }\n  }\n\n  private startBruteforceTimer(targetIp: string, totalTime: number) {\n    // Limpar timer existente\n    if (this.bruteforceTimers.has(targetIp)) {\n      clearInterval(this.bruteforceTimers.get(targetIp)!);\n    }\n    \n    let timeRemaining = totalTime;\n    \n    const timer = setInterval(async () => {\n      timeRemaining--;\n      \n      const connection = this.activeConnections.get(targetIp);\n      if (connection?.bruteforceStatus) {\n        connection.bruteforceStatus.timeRemaining = timeRemaining;\n        connection.bruteforceStatus.progress = ((totalTime - timeRemaining) / totalTime) * 100;\n      }\n      \n      if (timeRemaining <= 0) {\n        clearInterval(timer);\n        this.bruteforceTimers.delete(targetIp);\n        \n        // Verificar conclusão do bruteforce\n        await this.checkBruteforceCompletion(targetIp);\n      }\n    }, 1000);\n    \n    this.bruteforceTimers.set(targetIp, timer);\n  }\n\n  private async checkBruteforceCompletion(targetIp: string) {\n    try {\n      const response = await gameApi.checkBruteforceStatus(targetIp);\n      \n      if (response.sucesso && response.finalizado) {\n        const connection = this.activeConnections.get(targetIp);\n        if (connection) {\n          connection.bruteforceStatus = undefined;\n          connection.bankAccess = true;\n        }\n        \n        // Atualizar alvo exploitado se for o mesmo\n        if (this.exploitedTarget?.ip === targetIp) {\n          this.exploitedTarget.bruteforceStatus = 'completed';\n          this.exploitedTarget.bankAccess = true;\n        }\n      }\n    } catch (error) {\n      console.error('Erro ao verificar conclusão do bruteforce:', error);\n    }\n  }\n\n  async closeConnection(connectionId: string, targetIp: string): Promise<void> {\n    try {\n      const response = await gameApi.closeConnection(connectionId);\n      \n      if (response.sucesso) {\n        this.activeConnections.delete(targetIp);\n        \n        // Limpar timer se existir\n        if (this.bruteforceTimers.has(targetIp)) {\n          clearInterval(this.bruteforceTimers.get(targetIp)!);\n          this.bruteforceTimers.delete(targetIp);\n        }\n        \n        // Limpar alvo exploitado se for o mesmo\n        if (this.exploitedTarget?.ip === targetIp) {\n          this.exploitedTarget = null;\n        }\n      } else {\n        throw new Error(response.mensagem || 'Erro ao fechar conexão');\n      }\n    } catch (error) {\n      console.error('Erro ao fechar conexão:', error);\n      throw error;\n    }\n  }\n\n  // === SISTEMA DE TRANSFERÊNCIA BANCÁRIA ===\n  async performBankTransfer(targetIp: string, percentage: number): Promise<any> {\n    try {\n      const connection = this.activeConnections.get(targetIp);\n      if (!connection?.bankAccess) {\n        throw new Error('Acesso ao banco não liberado. Execute bruteforce primeiro.');\n      }\n\n      const response = await gameApi.transferMoney(targetIp, percentage);\n      \n      if (response.sucesso) {\n        // Atualizar dados do jogador após transferência\n        await this.loadPlayerData(true);\n        return response;\n      } else {\n        throw new Error(response.mensagem || 'Erro na transferência');\n      }\n    } catch (error) {\n      console.error('Erro na transferência bancária:', error);\n      throw error;\n    }\n  }\n\n  // === LIMPEZA ===\n  cleanup() {\n    // Limpar todos os timers\n    this.bruteforceTimers.forEach(timer => clearInterval(timer));\n    this.bruteforceTimers.clear();\n    \n    // Limpar dados\n    this.activeConnections.clear();\n    this.exploitedTarget = null;\n  }\n}\n\nexport default new GameService();\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;;AAE/B;;AAqCA,MAAMC,WAAW,CAAC;EAAAC,YAAA;IAAA,KACRC,aAAa,GAAQ,IAAI;IAAA,KACzBC,eAAe,GAA2B,IAAI;IAAA,KAC9CC,iBAAiB,GAAkC,IAAIC,GAAG,CAAC,CAAC;IAAA,KAC5DC,gBAAgB,GAAgC,IAAID,GAAG,CAAC,CAAC;IAEjE;IAAA,KACQE,eAAe,GAAQ,IAAI;IAAA,KAC3BC,oBAAoB,GAAG,CAAC;IAAA,KACfC,cAAc,GAAG,KAAK;EAAA;EAAE;;EAEzC;EACA,MAAMC,cAAcA,CAACC,YAAY,GAAG,KAAK,EAAgB;IACvD,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAI,CAACD,YAAY,IAAI,IAAI,CAACJ,eAAe,IAAKK,GAAG,GAAG,IAAI,CAACJ,oBAAoB,GAAI,IAAI,CAACC,cAAc,EAAE;MACpG,IAAI,CAACP,aAAa,GAAG,IAAI,CAACK,eAAe;MACzC,OAAO,IAAI,CAACA,eAAe;IAC7B;IAEA,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMf,OAAO,CAACgB,aAAa,CAAC,CAAC;MAC9C,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpB,IAAI,CAACd,aAAa,GAAGY,QAAQ,CAACG,OAAO;QACrC,IAAI,CAACV,eAAe,GAAGO,QAAQ,CAACG,OAAO;QACvC,IAAI,CAACT,oBAAoB,GAAGI,GAAG;QAC/B,OAAOE,QAAQ,CAACG,OAAO;MACzB,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAACK,QAAQ,IAAI,wBAAwB,CAAC;MAChE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;;MAE1D;MACA,IAAI,IAAI,CAACb,eAAe,EAAE;QACxB,IAAI,CAACL,aAAa,GAAG,IAAI,CAACK,eAAe;QACzC,OAAO,IAAI,CAACA,eAAe;MAC7B;MAEA,MAAMa,KAAK;IACb;EACF;EAEAE,gBAAgBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACpB,aAAa;EAC3B;;EAEA;EACA,MAAMqB,gBAAgBA,CAAA,EAA0B;IAC9C,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMf,OAAO,CAACyB,WAAW,CAAC,CAAC;MAC5C,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpB,OAAOF,QAAQ,CAACW,KAAK,IAAI,EAAE;MAC7B,CAAC,MAAM;QACL,MAAM,IAAIP,KAAK,CAACJ,QAAQ,CAACK,QAAQ,IAAI,cAAc,CAAC;MACtD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;EAEA,MAAMM,mBAAmBA,CAACC,QAAgB,EAAyB;IACjE,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMf,OAAO,CAAC6B,kBAAkB,CAACD,QAAQ,CAAC;MAC3D,IAAIb,QAAQ,CAACE,OAAO,EAAE;QACpB,OAAOF,QAAQ,CAACW,KAAK,GAAG,CAACX,QAAQ,CAACW,KAAK,CAAC,GAAG,EAAE;MAC/C,CAAC,MAAM;QACL,MAAM,IAAIP,KAAK,CAACJ,QAAQ,CAACK,QAAQ,IAAI,qBAAqB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMS,cAAcA,CAACC,MAAkB,EAA4B;IACjE,IAAI;MAAA,IAAAC,mBAAA;MACF;MACA,MAAMC,SAAS,GAAG,EAAAD,mBAAA,OAAI,CAAC7B,aAAa,cAAA6B,mBAAA,uBAAlBA,mBAAA,CAAoBE,GAAG,KAAI,CAAC;MAC9C,MAAMC,cAAc,GAAGJ,MAAM,CAACK,QAAQ,IAAI,CAAC;MAE3C,IAAIH,SAAS,IAAIE,cAAc,EAAE;QAC/B,MAAM,IAAIhB,KAAK,CAAC,8BAA8Bc,SAAS,gCAAgCE,cAAc,GAAG,CAAC;MAC3G;MAEA,MAAMpB,QAAQ,GAAG,MAAMf,OAAO,CAACqC,aAAa,CAACN,MAAM,CAACO,EAAE,CAAC;MAEvD,IAAIvB,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACwB,cAAc,EAAE;QAC/C,IAAI,CAACnC,eAAe,GAAG;UACrB,GAAGW,QAAQ,CAACwB,cAAc;UAC1BC,SAAS,EAAE,IAAI;UACfC,gBAAgB,EAAE,MAAM;UACxBC,UAAU,EAAE;QACd,CAAC;;QAED;QACA,IAAI,CAACrC,iBAAiB,CAACsC,GAAG,CAACZ,MAAM,CAACO,EAAE,EAAE;UACpCM,EAAE,EAAE7B,QAAQ,CAAC8B,UAAU,IAAId,MAAM,CAACO,EAAE;UACpCV,QAAQ,EAAEG,MAAM,CAACO,EAAE;UACnBQ,UAAU,EAAEf,MAAM,CAACgB,IAAI;UACvBC,QAAQ,EAAE,IAAI;UACdN,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,OAAO,IAAI,CAACtC,eAAe;MAC7B,CAAC,MAAM;QACL,MAAM,IAAIe,KAAK,CAACJ,QAAQ,CAACK,QAAQ,IAAI,gBAAgB,CAAC;MACxD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC,MAAMA,KAAK;IACb;EACF;EAEA4B,kBAAkBA,CAAA,EAA2B;IAC3C,OAAO,IAAI,CAAC7C,eAAe;EAC7B;;EAEA;EACA,MAAM8C,oBAAoBA,CAAA,EAAgC;IACxD,IAAI;MACF,MAAMnC,QAAQ,GAAG,MAAMf,OAAO,CAACkD,oBAAoB,CAAC,CAAC;MACrD,IAAInC,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAkC,kBAAA;QACpB;QACA,IAAI,CAAC9C,iBAAiB,CAAC+C,KAAK,CAAC,CAAC;QAC9B,CAAAD,kBAAA,GAAApC,QAAQ,CAACsC,QAAQ,cAAAF,kBAAA,uBAAjBA,kBAAA,CAAmBG,OAAO,CAAEC,IAAS,IAAK;UACxC,IAAI,CAAClD,iBAAiB,CAACsC,GAAG,CAACY,IAAI,CAACC,OAAO,EAAE;YACvCZ,EAAE,EAAEW,IAAI,CAACX,EAAE;YACXhB,QAAQ,EAAE2B,IAAI,CAACC,OAAO;YACtBV,UAAU,EAAES,IAAI,CAACE,SAAS;YAC1BT,QAAQ,EAAE,IAAI;YACdN,UAAU,EAAEa,IAAI,CAACG,cAAc,IAAI;UACrC,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvD,iBAAiB,CAACwD,MAAM,CAAC,CAAC,CAAC;MACpD;MACA,OAAO,EAAE;IACX,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO,EAAE;IACX;EACF;EAEA,MAAMyC,eAAeA,CAACC,YAAoB,EAAEnC,QAAgB,EAA6B;IACvF,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMf,OAAO,CAAC8D,eAAe,CAACC,YAAY,CAAC;MAE5D,IAAIhD,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMwB,gBAAkC,GAAG;UACzCuB,SAAS,EAAE,IAAI;UACfC,aAAa,EAAElD,QAAQ,CAACmD,WAAW,IAAI,EAAE;UACzCC,SAAS,EAAEpD,QAAQ,CAACmD,WAAW,IAAI,EAAE;UACrCtC,QAAQ;UACRwC,QAAQ,EAAE;QACZ,CAAC;;QAED;QACA,MAAMC,UAAU,GAAG,IAAI,CAAChE,iBAAiB,CAACiE,GAAG,CAAC1C,QAAQ,CAAC;QACvD,IAAIyC,UAAU,EAAE;UACdA,UAAU,CAAC5B,gBAAgB,GAAGA,gBAAgB;QAChD;;QAEA;QACA,IAAI,CAAC8B,oBAAoB,CAAC3C,QAAQ,EAAEa,gBAAgB,CAAC0B,SAAS,CAAC;QAE/D,OAAO1B,gBAAgB;MACzB,CAAC,MAAM;QACL,MAAM,IAAItB,KAAK,CAACJ,QAAQ,CAACK,QAAQ,IAAI,4BAA4B,CAAC;MACpE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;EAEQkD,oBAAoBA,CAAC3C,QAAgB,EAAEuC,SAAiB,EAAE;IAChE;IACA,IAAI,IAAI,CAAC5D,gBAAgB,CAACiE,GAAG,CAAC5C,QAAQ,CAAC,EAAE;MACvC6C,aAAa,CAAC,IAAI,CAAClE,gBAAgB,CAAC+D,GAAG,CAAC1C,QAAQ,CAAE,CAAC;IACrD;IAEA,IAAIqC,aAAa,GAAGE,SAAS;IAE7B,MAAMO,KAAK,GAAGC,WAAW,CAAC,YAAY;MACpCV,aAAa,EAAE;MAEf,MAAMI,UAAU,GAAG,IAAI,CAAChE,iBAAiB,CAACiE,GAAG,CAAC1C,QAAQ,CAAC;MACvD,IAAIyC,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE5B,gBAAgB,EAAE;QAChC4B,UAAU,CAAC5B,gBAAgB,CAACwB,aAAa,GAAGA,aAAa;QACzDI,UAAU,CAAC5B,gBAAgB,CAAC2B,QAAQ,GAAI,CAACD,SAAS,GAAGF,aAAa,IAAIE,SAAS,GAAI,GAAG;MACxF;MAEA,IAAIF,aAAa,IAAI,CAAC,EAAE;QACtBQ,aAAa,CAACC,KAAK,CAAC;QACpB,IAAI,CAACnE,gBAAgB,CAACqE,MAAM,CAAChD,QAAQ,CAAC;;QAEtC;QACA,MAAM,IAAI,CAACiD,yBAAyB,CAACjD,QAAQ,CAAC;MAChD;IACF,CAAC,EAAE,IAAI,CAAC;IAER,IAAI,CAACrB,gBAAgB,CAACoC,GAAG,CAACf,QAAQ,EAAE8C,KAAK,CAAC;EAC5C;EAEA,MAAcG,yBAAyBA,CAACjD,QAAgB,EAAE;IACxD,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMf,OAAO,CAAC8E,qBAAqB,CAAClD,QAAQ,CAAC;MAE9D,IAAIb,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACgE,UAAU,EAAE;QAAA,IAAAC,qBAAA;QAC3C,MAAMX,UAAU,GAAG,IAAI,CAAChE,iBAAiB,CAACiE,GAAG,CAAC1C,QAAQ,CAAC;QACvD,IAAIyC,UAAU,EAAE;UACdA,UAAU,CAAC5B,gBAAgB,GAAGwC,SAAS;UACvCZ,UAAU,CAAC3B,UAAU,GAAG,IAAI;QAC9B;;QAEA;QACA,IAAI,EAAAsC,qBAAA,OAAI,CAAC5E,eAAe,cAAA4E,qBAAA,uBAApBA,qBAAA,CAAsB1C,EAAE,MAAKV,QAAQ,EAAE;UACzC,IAAI,CAACxB,eAAe,CAACqC,gBAAgB,GAAG,WAAW;UACnD,IAAI,CAACrC,eAAe,CAACsC,UAAU,GAAG,IAAI;QACxC;MACF;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IACpE;EACF;EAEA,MAAM6D,eAAeA,CAACnB,YAAoB,EAAEnC,QAAgB,EAAiB;IAC3E,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMf,OAAO,CAACkF,eAAe,CAACnB,YAAY,CAAC;MAE5D,IAAIhD,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAkE,sBAAA;QACpB,IAAI,CAAC9E,iBAAiB,CAACuE,MAAM,CAAChD,QAAQ,CAAC;;QAEvC;QACA,IAAI,IAAI,CAACrB,gBAAgB,CAACiE,GAAG,CAAC5C,QAAQ,CAAC,EAAE;UACvC6C,aAAa,CAAC,IAAI,CAAClE,gBAAgB,CAAC+D,GAAG,CAAC1C,QAAQ,CAAE,CAAC;UACnD,IAAI,CAACrB,gBAAgB,CAACqE,MAAM,CAAChD,QAAQ,CAAC;QACxC;;QAEA;QACA,IAAI,EAAAuD,sBAAA,OAAI,CAAC/E,eAAe,cAAA+E,sBAAA,uBAApBA,sBAAA,CAAsB7C,EAAE,MAAKV,QAAQ,EAAE;UACzC,IAAI,CAACxB,eAAe,GAAG,IAAI;QAC7B;MACF,CAAC,MAAM;QACL,MAAM,IAAIe,KAAK,CAACJ,QAAQ,CAACK,QAAQ,IAAI,wBAAwB,CAAC;MAChE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAM+D,mBAAmBA,CAACxD,QAAgB,EAAEyD,UAAkB,EAAgB;IAC5E,IAAI;MACF,MAAMhB,UAAU,GAAG,IAAI,CAAChE,iBAAiB,CAACiE,GAAG,CAAC1C,QAAQ,CAAC;MACvD,IAAI,EAACyC,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE3B,UAAU,GAAE;QAC3B,MAAM,IAAIvB,KAAK,CAAC,4DAA4D,CAAC;MAC/E;MAEA,MAAMJ,QAAQ,GAAG,MAAMf,OAAO,CAACsF,aAAa,CAAC1D,QAAQ,EAAEyD,UAAU,CAAC;MAElE,IAAItE,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAM,IAAI,CAACN,cAAc,CAAC,IAAI,CAAC;QAC/B,OAAOI,QAAQ;MACjB,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAACJ,QAAQ,CAACK,QAAQ,IAAI,uBAAuB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF;;EAEA;EACAkE,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAAChF,gBAAgB,CAAC+C,OAAO,CAACoB,KAAK,IAAID,aAAa,CAACC,KAAK,CAAC,CAAC;IAC5D,IAAI,CAACnE,gBAAgB,CAAC6C,KAAK,CAAC,CAAC;;IAE7B;IACA,IAAI,CAAC/C,iBAAiB,CAAC+C,KAAK,CAAC,CAAC;IAC9B,IAAI,CAAChD,eAAe,GAAG,IAAI;EAC7B;AACF;AAEA,eAAe,IAAIH,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}