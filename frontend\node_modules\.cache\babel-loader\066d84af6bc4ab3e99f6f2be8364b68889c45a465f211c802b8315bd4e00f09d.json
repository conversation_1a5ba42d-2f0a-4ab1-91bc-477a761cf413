{"ast": null, "code": "// src/retryer.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport { isServer, sleep } from \"./utils.js\";\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = pendingThenable();\n  const cancel = cancelOptions => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => focusManager.isFocused() && (config.networkMode === \"always\" || onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise(continueResolve => {\n      continueFn = value => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch(error => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      sleep(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\nexport { CancelledError, canFetch, createRetryer, isCancelledError };", "map": {"version": 3, "names": ["focusManager", "onlineManager", "pendingThenable", "isServer", "sleep", "defaultRetryDelay", "failureCount", "Math", "min", "canFetch", "networkMode", "isOnline", "CancelledError", "Error", "constructor", "options", "revert", "silent", "isCancelledError", "value", "createRetryer", "config", "isRetryCancelled", "isResolved", "continueFn", "thenable", "cancel", "cancelOptions", "reject", "abort", "cancelRetry", "continueRetry", "canContinue", "isFocused", "canRun", "canStart", "resolve", "onSuccess", "onError", "pause", "Promise", "continueResolve", "onPause", "then", "onContinue", "run", "promiseOrValue", "initialPromise", "fn", "error", "catch", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "promise", "continue", "start"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\node_modules\\@tanstack\\query-core\\src\\retryer.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { pendingThenable } from './thenable'\nimport { isServer, sleep } from './utils'\nimport type { CancelOptions, DefaultError, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = DefaultError> {\n  fn: () => TData | Promise<TData>\n  initialPromise?: Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n  canRun: () => boolean\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n  canStart: () => boolean\n  start: () => Promise<TData>\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError = DefaultError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = DefaultError> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError extends Error {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    super('CancelledError')\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = DefaultError>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => void) | undefined\n\n  const thenable = pendingThenable<TData>()\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const canContinue = () =>\n    focusManager.isFocused() &&\n    (config.networkMode === 'always' || onlineManager.isOnline()) &&\n    config.canRun()\n\n  const canStart = () => canFetch(config.networkMode) && config.canRun()\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      thenable.resolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      thenable.reject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value)\n        }\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // we can re-use config.initialPromise on the first call of run()\n    const initialPromise =\n      failureCount === 0 ? config.initialPromise : undefined\n\n    // Execute query\n    try {\n      promiseOrValue = initialPromise ?? config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? (isServer ? 0 : 3)\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            return canContinue() ? undefined : pause()\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.()\n      return thenable\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      // Start loop\n      if (canStart()) {\n        run()\n      } else {\n        pause().then(run)\n      }\n      return thenable\n    },\n  }\n}\n"], "mappings": ";AAAA,SAASA,YAAA,QAAoB;AAC7B,SAASC,aAAA,QAAqB;AAC9B,SAASC,eAAA,QAAuB;AAChC,SAASC,QAAA,EAAUC,KAAA,QAAa;AA4ChC,SAASC,kBAAkBC,YAAA,EAAsB;EAC/C,OAAOC,IAAA,CAAKC,GAAA,CAAI,MAAO,KAAKF,YAAA,EAAc,GAAK;AACjD;AAEO,SAASG,SAASC,WAAA,EAA+C;EACtE,QAAQA,WAAA,IAAe,cAAc,WACjCT,aAAA,CAAcU,QAAA,CAAS,IACvB;AACN;AAEO,IAAMC,cAAA,GAAN,cAA6BC,KAAA,CAAM;EAGxCC,YAAYC,OAAA,EAAyB;IACnC,MAAM,gBAAgB;IACtB,KAAKC,MAAA,GAASD,OAAA,EAASC,MAAA;IACvB,KAAKC,MAAA,GAASF,OAAA,EAASE,MAAA;EACzB;AACF;AAEO,SAASC,iBAAiBC,KAAA,EAAqC;EACpE,OAAOA,KAAA,YAAiBP,cAAA;AAC1B;AAEO,SAASQ,cACdC,MAAA,EACgB;EAChB,IAAIC,gBAAA,GAAmB;EACvB,IAAIhB,YAAA,GAAe;EACnB,IAAIiB,UAAA,GAAa;EACjB,IAAIC,UAAA;EAEJ,MAAMC,QAAA,GAAWvB,eAAA,CAAuB;EAExC,MAAMwB,MAAA,GAAUC,aAAA,IAAwC;IACtD,IAAI,CAACJ,UAAA,EAAY;MACfK,MAAA,CAAO,IAAIhB,cAAA,CAAee,aAAa,CAAC;MAExCN,MAAA,CAAOQ,KAAA,GAAQ;IACjB;EACF;EACA,MAAMC,WAAA,GAAcA,CAAA,KAAM;IACxBR,gBAAA,GAAmB;EACrB;EAEA,MAAMS,aAAA,GAAgBA,CAAA,KAAM;IAC1BT,gBAAA,GAAmB;EACrB;EAEA,MAAMU,WAAA,GAAcA,CAAA,KAClBhC,YAAA,CAAaiC,SAAA,CAAU,MACtBZ,MAAA,CAAOX,WAAA,KAAgB,YAAYT,aAAA,CAAcU,QAAA,CAAS,MAC3DU,MAAA,CAAOa,MAAA,CAAO;EAEhB,MAAMC,QAAA,GAAWA,CAAA,KAAM1B,QAAA,CAASY,MAAA,CAAOX,WAAW,KAAKW,MAAA,CAAOa,MAAA,CAAO;EAErE,MAAME,OAAA,GAAWjB,KAAA,IAAe;IAC9B,IAAI,CAACI,UAAA,EAAY;MACfA,UAAA,GAAa;MACbF,MAAA,CAAOgB,SAAA,GAAYlB,KAAK;MACxBK,UAAA,GAAa;MACbC,QAAA,CAASW,OAAA,CAAQjB,KAAK;IACxB;EACF;EAEA,MAAMS,MAAA,GAAUT,KAAA,IAAe;IAC7B,IAAI,CAACI,UAAA,EAAY;MACfA,UAAA,GAAa;MACbF,MAAA,CAAOiB,OAAA,GAAUnB,KAAK;MACtBK,UAAA,GAAa;MACbC,QAAA,CAASG,MAAA,CAAOT,KAAK;IACvB;EACF;EAEA,MAAMoB,KAAA,GAAQA,CAAA,KAAM;IAClB,OAAO,IAAIC,OAAA,CAASC,eAAA,IAAoB;MACtCjB,UAAA,GAAcL,KAAA,IAAU;QACtB,IAAII,UAAA,IAAcS,WAAA,CAAY,GAAG;UAC/BS,eAAA,CAAgBtB,KAAK;QACvB;MACF;MACAE,MAAA,CAAOqB,OAAA,GAAU;IACnB,CAAC,EAAEC,IAAA,CAAK,MAAM;MACZnB,UAAA,GAAa;MACb,IAAI,CAACD,UAAA,EAAY;QACfF,MAAA,CAAOuB,UAAA,GAAa;MACtB;IACF,CAAC;EACH;EAGA,MAAMC,GAAA,GAAMA,CAAA,KAAM;IAEhB,IAAItB,UAAA,EAAY;MACd;IACF;IAEA,IAAIuB,cAAA;IAGJ,MAAMC,cAAA,GACJzC,YAAA,KAAiB,IAAIe,MAAA,CAAO0B,cAAA,GAAiB;IAG/C,IAAI;MACFD,cAAA,GAAiBC,cAAA,IAAkB1B,MAAA,CAAO2B,EAAA,CAAG;IAC/C,SAASC,KAAA,EAAO;MACdH,cAAA,GAAiBN,OAAA,CAAQZ,MAAA,CAAOqB,KAAK;IACvC;IAEAT,OAAA,CAAQJ,OAAA,CAAQU,cAAc,EAC3BH,IAAA,CAAKP,OAAO,EACZc,KAAA,CAAOD,KAAA,IAAU;MAEhB,IAAI1B,UAAA,EAAY;QACd;MACF;MAGA,MAAM4B,KAAA,GAAQ9B,MAAA,CAAO8B,KAAA,KAAUhD,QAAA,GAAW,IAAI;MAC9C,MAAMiD,UAAA,GAAa/B,MAAA,CAAO+B,UAAA,IAAc/C,iBAAA;MACxC,MAAMgD,KAAA,GACJ,OAAOD,UAAA,KAAe,aAClBA,UAAA,CAAW9C,YAAA,EAAc2C,KAAK,IAC9BG,UAAA;MACN,MAAME,WAAA,GACJH,KAAA,KAAU,QACT,OAAOA,KAAA,KAAU,YAAY7C,YAAA,GAAe6C,KAAA,IAC5C,OAAOA,KAAA,KAAU,cAAcA,KAAA,CAAM7C,YAAA,EAAc2C,KAAK;MAE3D,IAAI3B,gBAAA,IAAoB,CAACgC,WAAA,EAAa;QAEpC1B,MAAA,CAAOqB,KAAK;QACZ;MACF;MAEA3C,YAAA;MAGAe,MAAA,CAAOkC,MAAA,GAASjD,YAAA,EAAc2C,KAAK;MAGnC7C,KAAA,CAAMiD,KAAK,EAERV,IAAA,CAAK,MAAM;QACV,OAAOX,WAAA,CAAY,IAAI,SAAYO,KAAA,CAAM;MAC3C,CAAC,EACAI,IAAA,CAAK,MAAM;QACV,IAAIrB,gBAAA,EAAkB;UACpBM,MAAA,CAAOqB,KAAK;QACd,OAAO;UACLJ,GAAA,CAAI;QACN;MACF,CAAC;IACL,CAAC;EACL;EAEA,OAAO;IACLW,OAAA,EAAS/B,QAAA;IACTC,MAAA;IACA+B,QAAA,EAAUA,CAAA,KAAM;MACdjC,UAAA,GAAa;MACb,OAAOC,QAAA;IACT;IACAK,WAAA;IACAC,aAAA;IACAI,QAAA;IACAuB,KAAA,EAAOA,CAAA,KAAM;MAEX,IAAIvB,QAAA,CAAS,GAAG;QACdU,GAAA,CAAI;MACN,OAAO;QACLN,KAAA,CAAM,EAAEI,IAAA,CAAKE,GAAG;MAClB;MACA,OAAOpB,QAAA;IACT;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}