import React, { useState, useEffect } from 'react';
import { useAuth } from '../stores/authStore';
import { usePlayer } from '../stores/playerStore';
import gameApi from '../services/gameApi';

const TransferPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { currentPlayer, loadPlayerData } = usePlayer();
  
  // Estados para transferência voluntária
  const [targetNick, setTargetNick] = useState('');
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [isTransferring, setIsTransferring] = useState(false);
  const [transferError, setTransferError] = useState<string | null>(null);
  const [transferSuccess, setTransferSuccess] = useState<string | null>(null);
  
  // Estados para transferência forçada
  const [forceTargetUid, setForceTargetUid] = useState('');
  const [forcePercentage, setForcePercentage] = useState('50');
  const [isForcingTransfer, setIsForcingTransfer] = useState(false);

  // Estados para conexões ativas
  const [activeConnections, setActiveConnections] = useState<any[]>([]);
  const [isLoadingConnections, setIsLoadingConnections] = useState(false);
  
  // Estados para histórico
  const [transferHistory, setTransferHistory] = useState<any[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);

  useEffect(() => {
    if (isAuthenticated && !currentPlayer) {
      loadPlayerData();
    }
    loadTransferHistory();
    loadActiveConnections();
  }, [isAuthenticated, currentPlayer, loadPlayerData]);

  const loadTransferHistory = async () => {
    setIsLoadingHistory(true);
    try {
      const response = await gameApi.getTransferHistory();
      if (response.sucesso) {
        setTransferHistory(response.transferencias || []);
      }
    } catch (error) {
      console.error('Erro ao carregar histórico:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const loadActiveConnections = async () => {
    setIsLoadingConnections(true);
    try {
      const response = await gameApi.getActiveConnections();
      if (response.sucesso) {
        setActiveConnections(response.conexoes || []);
      }
    } catch (error) {
      console.error('Erro ao carregar conexões:', error);
    } finally {
      setIsLoadingConnections(false);
    }
  };

  const handleVoluntaryTransfer = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!targetNick.trim() || !amount.trim()) {
      setTransferError('Nick do destinatário e valor são obrigatórios');
      return;
    }

    const transferAmount = parseInt(amount);
    if (isNaN(transferAmount) || transferAmount <= 0) {
      setTransferError('Valor deve ser um número positivo');
      return;
    }

    if (currentPlayer && transferAmount > currentPlayer.dinheiro) {
      setTransferError('Saldo insuficiente');
      return;
    }

    setIsTransferring(true);
    setTransferError(null);
    setTransferSuccess(null);

    try {
      const response = await gameApi.transferMoney(targetNick, transferAmount, description);
      
      if (response.sucesso) {
        setTransferSuccess(`Transferência realizada com sucesso! ${response.mensagem}`);
        setTargetNick('');
        setAmount('');
        setDescription('');
        
        // Recarregar dados do jogador
        loadPlayerData();
        loadTransferHistory();
      } else {
        setTransferError(response.mensagem || 'Erro na transferência');
      }
    } catch (error: any) {
      setTransferError(error.message || 'Erro de conexão');
    } finally {
      setIsTransferring(false);
    }
  };

  const handleForceTransfer = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!forceTargetUid.trim()) {
      setTransferError('UID do alvo é obrigatório');
      return;
    }

    const percentage = parseInt(forcePercentage);
    if (isNaN(percentage) || percentage < 20 || percentage > 80) {
      setTransferError('Porcentagem deve estar entre 20% e 80%');
      return;
    }

    setIsForcingTransfer(true);
    setTransferError(null);
    setTransferSuccess(null);

    try {
      const response = await gameApi.forceTransfer(forceTargetUid, percentage);
      
      if (response.sucesso) {
        setTransferSuccess(`Transferência forçada realizada! ${response.mensagem}`);
        setForceTargetUid('');
        
        // Recarregar dados do jogador
        loadPlayerData();
        loadTransferHistory();
      } else {
        setTransferError(response.mensagem || 'Erro na transferência forçada');
      }
    } catch (error: any) {
      setTransferError(error.message || 'Erro de conexão');
    } finally {
      setIsForcingTransfer(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">🔒 Acesso Negado</h1>
          <p className="text-gray-400">Você precisa estar logado para acessar as transferências</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => window.history.back()}
            className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600"
          >
            <span className="text-lg">←</span>
          </button>
          <div>
            <h1 className="text-lg font-bold">💰 Transferências</h1>
            <p className="text-xs text-gray-400">Sistema de Pagamentos</p>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {/* Saldo atual */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold mb-2 text-white">💳 Saldo Atual</h3>
          <div className="text-2xl font-bold text-green-400">
            ${currentPlayer?.dinheiro?.toLocaleString() || '0'}
          </div>
          <p className="text-xs text-gray-400">Disponível para transferência</p>
        </div>

        {/* Mensagens de erro/sucesso */}
        {transferError && (
          <div className="bg-red-900 border border-red-500 rounded-lg p-3">
            <p className="text-red-300 text-sm">❌ {transferError}</p>
          </div>
        )}

        {transferSuccess && (
          <div className="bg-green-900 border border-green-500 rounded-lg p-3">
            <p className="text-green-300 text-sm">✅ {transferSuccess}</p>
          </div>
        )}

        {/* Transferência Voluntária */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold mb-4 text-white">📤 Transferência Voluntária</h3>
          <form onSubmit={handleVoluntaryTransfer} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Nick do Destinatário
              </label>
              <input
                type="text"
                value={targetNick}
                onChange={(e) => setTargetNick(e.target.value)}
                placeholder="Digite o nick do jogador"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                disabled={isTransferring}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Valor ($)
              </label>
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0"
                min="1"
                max={currentPlayer?.dinheiro || 0}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                disabled={isTransferring}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Descrição (opcional)
              </label>
              <input
                type="text"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Motivo da transferência"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                disabled={isTransferring}
              />
            </div>

            <button
              type="submit"
              disabled={isTransferring || !targetNick.trim() || !amount.trim()}
              className="w-full py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold"
            >
              {isTransferring ? 'Transferindo...' : 'Transferir Dinheiro'}
            </button>
          </form>
        </div>

        {/* Conexões Ativas */}
        {activeConnections.length > 0 && (
          <div className="bg-gray-800 rounded-lg p-4 border border-green-600">
            <h3 className="text-lg font-semibold mb-4 text-green-400">🔗 Conexões Ativas</h3>
            <p className="text-xs text-gray-400 mb-4">
              Alvos exploitados disponíveis para transferência forçada
            </p>
            <div className="space-y-3">
              {activeConnections.map((connection, index) => (
                <div key={index} className="bg-gray-700 rounded-lg p-3 border border-gray-600">
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-bold text-white">{connection.alvo_nick}</div>
                      <div className="text-xs text-gray-400">IP: {connection.alvo_ip}</div>
                      <div className="text-xs text-green-400">
                        Dinheiro: ${connection.alvo_dinheiro?.toLocaleString() || '0'}
                      </div>
                    </div>
                    <button
                      onClick={() => {
                        setForceTargetUid(connection.alvo_uid || connection.alvo_ip);
                        setForcePercentage('50');
                      }}
                      className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm"
                    >
                      Selecionar
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Transferência Forçada */}
        <div className="bg-gray-800 rounded-lg p-4 border border-red-600">
          <h3 className="text-lg font-semibold mb-4 text-red-400">⚔️ Transferência Forçada</h3>
          <p className="text-xs text-gray-400 mb-4">
            Apenas disponível após exploit bem-sucedido
          </p>
          <form onSubmit={handleForceTransfer} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                UID do Alvo
              </label>
              <input
                type="text"
                value={forceTargetUid}
                onChange={(e) => setForceTargetUid(e.target.value)}
                placeholder="UID do jogador exploitado"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                disabled={isForcingTransfer}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Porcentagem (20% - 80%)
              </label>
              <input
                type="range"
                min="20"
                max="80"
                value={forcePercentage}
                onChange={(e) => setForcePercentage(e.target.value)}
                className="w-full"
                disabled={isForcingTransfer}
              />
              <div className="text-center text-sm text-gray-400 mt-1">
                {forcePercentage}%
              </div>
            </div>

            <button
              type="submit"
              disabled={isForcingTransfer || !forceTargetUid.trim()}
              className="w-full py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white rounded-lg font-semibold"
            >
              {isForcingTransfer ? 'Forçando...' : 'Forçar Transferência'}
            </button>
          </form>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0">
        <div className="flex justify-center">
          <button 
            onClick={() => window.history.back()}
            className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
          >
            <span className="text-lg">←</span>
            <span className="text-sm">Voltar</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default TransferPage;
