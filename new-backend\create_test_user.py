#!/usr/bin/env python3
"""
Script para criar usuário de teste no Supabase
"""

import os
import hashlib
import uuid
from datetime import datetime, timezone
from dotenv import load_dotenv
from supabase import create_client, Client

# Carregar variáveis de ambiente
load_dotenv()

# Configurações
SUPABASE_URL = os.environ.get('SUPABASE_URL')
SUPABASE_KEY = os.environ.get('SUPABASE_ANON_KEY')

# Inicializar Supabase
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def hash_password(password):
    """Hash da senha usando SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_test_user():
    """Criar usuário de teste"""
    print("🔧 Criando usuário de teste...")
    
    # Dados do usuário de teste
    test_user = {
        'uid': str(uuid.uuid4()),
        'nick': 'zakedev',
        'email': '<EMAIL>',
        'password_hash': hash_password('teste123'),
        'ip': '*************',
        'nivel': 5,
        'xp': 250,
        'dinheiro': 1500,
        'shack': 250,
        'pontos': 1750,
        'antivirus': 3,
        'cpu': 2,
        'firewall': 3,
        'malware_kit': 2,
        'nivel_mineradora': 2,
        'ultimo_recurso_coletado_timestamp': datetime.now(timezone.utc).isoformat(),
        'created_at': datetime.now(timezone.utc).isoformat(),
        'updated_at': datetime.now(timezone.utc).isoformat(),
        'is_admin': False,
        'is_active': True
    }
    
    try:
        # Verificar se usuário já existe
        existing = supabase.table('usuarios').select('*').eq('email', test_user['email']).execute()
        
        if existing.data:
            print(f"✅ Usuário já existe: {existing.data[0]['nick']}")
            return existing.data[0]
        
        # Criar novo usuário
        result = supabase.table('usuarios').insert(test_user).execute()
        
        if result.data:
            print(f"✅ Usuário criado: {result.data[0]['nick']}")
            return result.data[0]
        else:
            print("❌ Erro ao criar usuário")
            return None
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return None

def list_users():
    """Listar usuários existentes"""
    print("\n👥 Usuários no banco:")
    try:
        result = supabase.table('usuarios').select('nick, email, nivel, dinheiro, created_at').limit(10).execute()
        
        if result.data:
            for i, user in enumerate(result.data, 1):
                print(f"{i}. {user['nick']} ({user['email']}) - Nível {user['nivel']} - ${user['dinheiro']}")
        else:
            print("Nenhum usuário encontrado")
            
    except Exception as e:
        print(f"❌ Erro ao listar usuários: {e}")

def test_connection():
    """Testar conexão com Supabase"""
    print("🔍 Testando conexão com Supabase...")
    try:
        result = supabase.table('usuarios').select('count').execute()
        print("✅ Conexão com Supabase OK!")
        return True
    except Exception as e:
        print(f"❌ Erro na conexão: {e}")
        return False

def main():
    print("=== SETUP DO USUÁRIO DE TESTE ===")
    print(f"Supabase URL: {SUPABASE_URL}")
    print()
    
    # Teste 1: Conexão
    if not test_connection():
        return
    
    # Teste 2: Listar usuários existentes
    list_users()
    
    # Teste 3: Criar usuário de teste
    user = create_test_user()
    
    if user:
        print(f"\n🎉 Setup concluído!")
        print(f"Email: {user['email']}")
        print(f"Senha: teste123")
        print(f"Nick: {user['nick']}")
        print(f"UID: {user['uid']}")
    else:
        print("\n❌ Falha no setup")

if __name__ == '__main__':
    main()
