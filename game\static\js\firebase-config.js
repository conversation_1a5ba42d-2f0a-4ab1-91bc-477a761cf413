// Importa as funções de inicialização do Firebase
import { initializeApp } from "https://www.gstatic.com/firebasejs/9.22.1/firebase-app.js";
import { getAuth } from "https://www.gstatic.com/firebasejs/9.22.1/firebase-auth.js";
import { getFirestore, collection, onSnapshot, query, orderBy, limit } from "https://www.gstatic.com/firebasejs/9.22.1/firebase-firestore.js";

// Sua configuração do Firebase que estava no base.html
const firebaseConfig = {
    apiKey: "AIzaSyDbA6NJUMJw4o3RuDpjYD-yIZsxirx3mgg",
    authDomain: "shackweb-5ec3a.firebaseapp.com",
    projectId: "shackweb-5ec3a",
    storageBucket: "shackweb-5ec3a.appspot.com",
    messagingSenderId: "1088354233669",
    appId: "1:1088354233669:web:be16dbffee6a5ba216a22c"
};

// Inicializa o Firebase App
const app = initializeApp(firebaseConfig);

// Cria e EXPORTA as instâncias que outros arquivos vão precisar
export const auth = getAuth(app);
export const db = getFirestore(app);

// Exporta as funções do Firestore para facilitar o uso
export const firestoreFunctions = { collection, onSnapshot, query, orderBy, limit };