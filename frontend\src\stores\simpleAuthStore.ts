import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface User {
  uid: string;
  nick: string;
  email: string;
}

interface SimpleAuthState {
  // Estado
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Ações simplificadas (sem API calls)
  setUser: (user: User) => void;
  setToken: (token: string) => void;
  logout: () => void;
  clearError: () => void;
  
  // Simulação de login para teste
  simulateLogin: () => void;
}

export const useSimpleAuthStore = create<SimpleAuthState>()(
  persist(
    (set, get) => ({
      // Estado inicial
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Definir usuário
      setUser: (user: User) => {
        console.log('SimpleAuth - Definindo usuário:', user);
        set({
          user,
          isAuthenticated: true,
          error: null,
        });
      },

      // Definir token
      setToken: (token: string) => {
        console.log('SimpleAuth - Definindo token');
        set({
          token,
          error: null,
        });
      },

      // Logout
      logout: () => {
        console.log('SimpleAuth - Fazendo logout');
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      // Limpar erro
      clearError: () => {
        set({ error: null });
      },

      // Simulação de login para teste
      simulateLogin: () => {
        console.log('SimpleAuth - Simulando login...');
        set({ isLoading: true });
        
        // Simular delay de API
        setTimeout(() => {
          const mockUser: User = {
            uid: 'test-user-123',
            nick: 'TestPlayer',
            email: '<EMAIL>'
          };
          
          const mockToken = 'mock-jwt-token-123';
          
          set({
            user: mockUser,
            token: mockToken,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
          
          console.log('SimpleAuth - Login simulado com sucesso!');
        }, 1000);
      },
    }),
    {
      name: 'simple-auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Hook simplificado
export const useSimpleAuth = () => {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    setUser,
    setToken,
    logout,
    clearError,
    simulateLogin,
  } = useSimpleAuthStore();

  return {
    // Estado
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    
    // Ações
    setUser,
    setToken,
    logout,
    clearError,
    simulateLogin,
    
    // Computed
    isLoggedIn: isAuthenticated && !!user,
  };
};
