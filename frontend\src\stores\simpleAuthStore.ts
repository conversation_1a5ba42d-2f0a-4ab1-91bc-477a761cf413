import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { backendService } from '../services/backendService';

export interface User {
  uid: string;
  nick: string;
  email: string;
}

interface SimpleAuthState {
  // Estado
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Ações com API mock
  login: (credentials: { email: string; password: string }) => Promise<void>;
  register: (userData: { email: string; password: string; nick: string }) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  checkAuth: () => Promise<void>;

  // Simulação de login para teste
  simulateLogin: () => void;
}

export const useSimpleAuthStore = create<SimpleAuthState>()(
  persist(
    (set, get) => ({
      // Estado inicial
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Login com backend real
      login: async (credentials) => {
        set({ isLoading: true, error: null });

        try {
          console.log('SimpleAuth - Fazendo login...');
          const response = await backendService.login(credentials.email, credentials.password);

          if (response.success && response.token && response.player) {
            // Salvar token no localStorage para o hackGameStore
            localStorage.setItem('auth_token', response.token);

            const user: User = {
              uid: response.player.uid,
              nick: response.player.nick,
              email: response.player.email,
            };

            set({
              user,
              token: response.token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });

            console.log('SimpleAuth - Login realizado com sucesso!', user);
          } else {
            set({
              isLoading: false,
              error: response.error || 'Erro no login',
            });
          }
        } catch (error) {
          console.error('SimpleAuth - Erro no login:', error);
          set({
            isLoading: false,
            error: 'Erro de conexão',
          });
        }
      },

      // Registro com backend real
      register: async (userData) => {
        set({ isLoading: true, error: null });

        try {
          console.log('SimpleAuth - Criando conta...');
          const response = await backendService.register(userData.nick, userData.email, userData.password);

          if (response.success && response.token && response.player) {
            // Salvar token no localStorage para o hackGameStore
            localStorage.setItem('auth_token', response.token);

            const user: User = {
              uid: response.player.uid,
              nick: response.player.nick,
              email: response.player.email,
            };

            set({
              user,
              token: response.token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });

            console.log('SimpleAuth - Conta criada com sucesso!', user);
          } else {
            set({
              isLoading: false,
              error: response.error || 'Erro no registro',
            });
          }
        } catch (error) {
          console.error('SimpleAuth - Erro no registro:', error);
          set({
            isLoading: false,
            error: 'Erro de conexão',
          });
        }
      },

      // Logout
      logout: () => {
        console.log('SimpleAuth - Fazendo logout');

        // Remover token do localStorage
        localStorage.removeItem('auth_token');

        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      // Verificar autenticação
      checkAuth: async () => {
        const { isLoading } = get();

        if (isLoading) {
          console.log('SimpleAuth - Verificação já em andamento');
          return;
        }

        // Verificar token no localStorage
        const token = localStorage.getItem('auth_token');

        if (!token) {
          console.log('SimpleAuth - Sem token no localStorage');
          set({ isAuthenticated: false, isLoading: false, token: null, user: null });
          return;
        }

        console.log('SimpleAuth - Token encontrado, verificando validade...');
        set({ isLoading: true });

        try {
          // Tentar carregar dados do jogador para verificar se o token é válido
          const result = await backendService.getPlayer();

          if (result.success && result.player) {
            const user: User = {
              uid: result.player.uid,
              nick: result.player.nick,
              email: result.player.email,
            };

            console.log('SimpleAuth - Token válido, usuário carregado:', user);
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false
            });
          } else {
            console.log('SimpleAuth - Token inválido');
            get().logout();
          }
        } catch (error) {
          console.error('SimpleAuth - Erro na verificação:', error);
          get().logout();
        }
      },

      // Limpar erro
      clearError: () => {
        set({ error: null });
      },

      // Simulação de login para teste
      simulateLogin: () => {
        console.log('SimpleAuth - Simulando login...');
        set({ isLoading: true });
        
        // Simular delay de API
        setTimeout(() => {
          const mockUser: User = {
            uid: 'test-user-123',
            nick: 'TestPlayer',
            email: '<EMAIL>'
          };
          
          const mockToken = 'mock-jwt-token-123';
          
          set({
            user: mockUser,
            token: mockToken,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
          
          console.log('SimpleAuth - Login simulado com sucesso!');
        }, 1000);
      },
    }),
    {
      name: 'simple-auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Hook simplificado
export const useSimpleAuth = () => {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    clearError,
    checkAuth,
    simulateLogin,
  } = useSimpleAuthStore();

  return {
    // Estado
    user,
    token,
    isAuthenticated,
    isLoading,
    error,

    // Ações
    login,
    register,
    logout,
    clearError,
    checkAuth,
    simulateLogin,

    // Computed
    isLoggedIn: isAuthenticated && !!user,
  };
};
