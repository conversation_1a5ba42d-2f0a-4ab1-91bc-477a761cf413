import axios, { AxiosInstance } from 'axios';

// Tipos para as respostas da API
export interface ApiResponse<T = any> {
  sucesso: boolean;
  mensagem?: string;
  data?: T;
  [key: string]: any;
}

export interface LoginResponse {
  sucesso: boolean;
  mensagem?: string;
  user?: {
    uid: string;
    nick: string;
    email: string;
  };
  token?: string;
}

export interface PlayerData {
  uid: string;
  nick: string;
  email: string;
  dinheiro: number;
  cpu: number;
  firewall: number;
  bankguard: number;
  bruteforce: number;
  proxyvpn: number;
  ip: string;
  last_seen: string;
  created_at: string;
}

export interface ChatMessage {
  id: string;
  uid: string;
  nick: string;
  message: string;
  timestamp: string;
}

export interface ScanTarget {
  ip: string;
  nick: string;
  dinheiro: number;
  cpu: number;
  firewall: number;
  last_seen: string;
}

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.REACT_APP_API_URL || '',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - adiciona token de autenticação
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('shack_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor - trata erros de autenticação
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token inválido ou expirado
          localStorage.removeItem('shack_token');
          localStorage.removeItem('shack_user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // === MÉTODOS DE AUTENTICAÇÃO ===
  async login(credentials: { email: string; password: string }): Promise<LoginResponse> {
    try {
      const response = await this.client.post('/api/auth/login', credentials);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.mensagem || 'Erro ao fazer login');
    }
  }

  async register(userData: { email: string; password: string; nick: string }): Promise<LoginResponse> {
    try {
      const response = await this.client.post('/api/auth/register', userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.mensagem || 'Erro ao criar conta');
    }
  }

  async verifyToken(): Promise<boolean> {
    try {
      const response = await this.client.get('/api/auth/check');
      return response.data.sucesso;
    } catch (error) {
      console.log('Erro na verificação de token:', error);
      return false;
    }
  }

  // === MÉTODOS DO JOGO ===
  async getPlayerData(): Promise<ApiResponse<PlayerData>> {
    try {
      const response = await this.client.get('/api/jogador');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.mensagem || 'Erro ao carregar dados do jogador');
    }
  }

  async scanTargets(): Promise<ApiResponse<{ alvos: ScanTarget[] }>> {
    try {
      const response = await this.client.get('/api/scan');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.mensagem || 'Erro ao escanear alvos');
    }
  }

  async exploitTarget(targetIp: string, percentage: number): Promise<ApiResponse> {
    try {
      const response = await this.client.post('/api/transferir', {
        alvo_ip: targetIp,
        porcentagem: percentage,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.mensagem || 'Erro ao exploitar alvo');
    }
  }

  // === MÉTODOS DE CHAT ===
  async getChatMessages(): Promise<ApiResponse<{ messages: ChatMessage[] }>> {
    try {
      const response = await this.client.get('/api/chat/messages');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.mensagem || 'Erro ao carregar mensagens');
    }
  }

  async sendChatMessage(message: string): Promise<ApiResponse> {
    try {
      const response = await this.client.post('/api/chat/send', { message });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.mensagem || 'Erro ao enviar mensagem');
    }
  }

  // === MÉTODOS DE APPS ===
  async getAppStore(): Promise<ApiResponse> {
    try {
      const response = await this.client.get('/api/appstore');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.mensagem || 'Erro ao carregar loja');
    }
  }

  async purchaseApp(appName: string, quantity: number): Promise<ApiResponse> {
    try {
      const response = await this.client.post('/api/appstore/comprar', {
        item: appName,
        quantidade: quantity,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.mensagem || 'Erro ao comprar app');
    }
  }

  // === MÉTODOS UTILITÁRIOS ===
  async getCsrfToken(): Promise<string> {
    try {
      const response = await this.client.get('/api/csrf-token');
      return response.data.csrf_token;
    } catch (error) {
      return '';
    }
  }

  // Método para fazer requisições customizadas
  async customRequest<T = any>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any
  ): Promise<T> {
    try {
      const response = await this.client.request({
        method,
        url: endpoint,
        data,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.mensagem || 'Erro na requisição');
    }
  }
}

// Instância singleton do serviço de API
const apiService = new ApiService();
export default apiService;
