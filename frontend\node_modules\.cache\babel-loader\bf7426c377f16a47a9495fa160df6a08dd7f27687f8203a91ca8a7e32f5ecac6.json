{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';// Componentes\nimport TestPage from'./pages/TestPage';import SimpleLoginPage from'./pages/SimpleLoginPage';import SimpleGamePage from'./pages/SimpleGamePage';import{useSimpleAuth}from'./stores/simpleAuthStore';// Estilos\nimport'./styles/globals.css';// Componente para roteamento baseado em autenticação\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AppRouter=()=>{const{isAuthenticated,isLoading}=useSimpleAuth();console.log('AppRouter - Estado:',{isAuthenticated,isLoading});// Mostrar loading se necessário\nif(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Carregando...\"})]})});}return/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(SimpleLoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/test\",element:/*#__PURE__*/_jsx(TestPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/game/*\",element:isAuthenticated?/*#__PURE__*/_jsx(SimpleGamePage,{}):/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:isAuthenticated?/*#__PURE__*/_jsx(Navigate,{to:\"/game\",replace:true}):/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]});};function App(){console.log('App - Renderizando com autenticação simples...');return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-bg-primary text-text-primary\",children:/*#__PURE__*/_jsx(AppRouter,{})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "TestPage", "SimpleLoginPage", "SimpleGamePage", "useSimpleAuth", "jsx", "_jsx", "jsxs", "_jsxs", "AppRouter", "isAuthenticated", "isLoading", "console", "log", "className", "children", "path", "element", "to", "replace", "App"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\n\n// Componentes\nimport TestPage from './pages/TestPage';\nimport SimpleLoginPage from './pages/SimpleLoginPage';\nimport SimpleGamePage from './pages/SimpleGamePage';\nimport { useSimpleAuth } from './stores/simpleAuthStore';\n\n// Estilos\nimport './styles/globals.css';\n\n// Componente para roteamento baseado em autenticação\nconst AppRouter: React.FC = () => {\n  const { isAuthenticated, isLoading } = useSimpleAuth();\n\n  console.log('AppRouter - Estado:', { isAuthenticated, isLoading });\n\n  // Mostrar loading se necessário\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n          <p>Carregando...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <Routes>\n      {/* Rota de login */}\n      <Route path=\"/login\" element={<SimpleLoginPage />} />\n\n      {/* Rota de teste */}\n      <Route path=\"/test\" element={<TestPage />} />\n\n      {/* Rota do jogo (protegida) */}\n      <Route\n        path=\"/game/*\"\n        element={\n          isAuthenticated ? (\n            <SimpleGamePage />\n          ) : (\n            <Navigate to=\"/login\" replace />\n          )\n        }\n      />\n\n      {/* Rota raiz */}\n      <Route\n        path=\"/\"\n        element={\n          isAuthenticated ? (\n            <Navigate to=\"/game\" replace />\n          ) : (\n            <Navigate to=\"/login\" replace />\n          )\n        }\n      />\n\n      {/* 404 */}\n      <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n    </Routes>\n  );\n};\n\nfunction App() {\n  console.log('App - Renderizando com autenticação simples...');\n\n  return (\n    <Router>\n      <div className=\"min-h-screen bg-bg-primary text-text-primary\">\n        <AppRouter />\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CAEnF;AACA,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,OAASC,aAAa,KAAQ,0BAA0B,CAExD;AACA,MAAO,sBAAsB,CAE7B;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAEC,eAAe,CAAEC,SAAU,CAAC,CAAGP,aAAa,CAAC,CAAC,CAEtDQ,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE,CAAEH,eAAe,CAAEC,SAAU,CAAC,CAAC,CAElE;AACA,GAAIA,SAAS,CAAE,CACb,mBACEL,IAAA,QAAKQ,SAAS,CAAC,+EAA+E,CAAAC,QAAA,cAC5FP,KAAA,QAAKM,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BT,IAAA,QAAKQ,SAAS,CAAC,2EAA2E,CAAM,CAAC,cACjGR,IAAA,MAAAS,QAAA,CAAG,eAAa,CAAG,CAAC,EACjB,CAAC,CACH,CAAC,CAEV,CAEA,mBACEP,KAAA,CAACV,MAAM,EAAAiB,QAAA,eAELT,IAAA,CAACP,KAAK,EAACiB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEX,IAAA,CAACJ,eAAe,GAAE,CAAE,CAAE,CAAC,cAGrDI,IAAA,CAACP,KAAK,EAACiB,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEX,IAAA,CAACL,QAAQ,GAAE,CAAE,CAAE,CAAC,cAG7CK,IAAA,CAACP,KAAK,EACJiB,IAAI,CAAC,SAAS,CACdC,OAAO,CACLP,eAAe,cACbJ,IAAA,CAACH,cAAc,GAAE,CAAC,cAElBG,IAAA,CAACN,QAAQ,EAACkB,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAElC,CACF,CAAC,cAGFb,IAAA,CAACP,KAAK,EACJiB,IAAI,CAAC,GAAG,CACRC,OAAO,CACLP,eAAe,cACbJ,IAAA,CAACN,QAAQ,EAACkB,EAAE,CAAC,OAAO,CAACC,OAAO,MAAE,CAAC,cAE/Bb,IAAA,CAACN,QAAQ,EAACkB,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAElC,CACF,CAAC,cAGFb,IAAA,CAACP,KAAK,EAACiB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEX,IAAA,CAACN,QAAQ,EAACkB,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CAAC,CAEb,CAAC,CAED,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACbR,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC,CAE7D,mBACEP,IAAA,CAACT,MAAM,EAAAkB,QAAA,cACLT,IAAA,QAAKQ,SAAS,CAAC,8CAA8C,CAAAC,QAAA,cAC3DT,IAAA,CAACG,SAAS,GAAE,CAAC,CACV,CAAC,CACA,CAAC,CAEb,CAEA,cAAe,CAAAW,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}