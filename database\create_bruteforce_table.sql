-- SQL para criar tabela de bruteforce_ataques
-- Execute este SQL no Supabase SQL Editor

CREATE TABLE IF NOT EXISTS bruteforce_ataques (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    atacante_uid TEXT NOT NULL,
    alvo_ip TEXT NOT NULL,
    alvo_nick TEXT NOT NULL,
    alvo_uid TEXT NOT NULL,
    tempo_total INTEGER NOT NULL, -- em segundos
    chance_sucesso DECIMAL(5,3) NOT NULL, -- probabilidade 0.000-1.000
    inicio TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    fim_estimado TIMESTAMP WITH TIME ZONE NOT NULL,
    status TEXT DEFAULT 'executando', -- executando, concluido, falhado
    quantia_roubada INTEGER DEFAULT 0,
    porcentagem INTEGER DEFAULT 0, -- porcentagem roubada do alvo
    finalizado_em TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Índices para performance
    CONSTRAINT unique_atacante_alvo_bruteforce UNIQUE (atacante_uid, alvo_ip)
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_bruteforce_atacante ON bruteforce_ataques (atacante_uid);
CREATE INDEX IF NOT EXISTS idx_bruteforce_alvo_ip ON bruteforce_ataques (alvo_ip);
CREATE INDEX IF NOT EXISTS idx_bruteforce_status ON bruteforce_ataques (status);
CREATE INDEX IF NOT EXISTS idx_bruteforce_fim_estimado ON bruteforce_ataques (fim_estimado);
CREATE INDEX IF NOT EXISTS idx_bruteforce_created_at ON bruteforce_ataques (created_at);

-- Trigger para updated_at
CREATE OR REPLACE FUNCTION update_bruteforce_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_bruteforce_ataques_updated_at ON bruteforce_ataques;
CREATE TRIGGER update_bruteforce_ataques_updated_at 
    BEFORE UPDATE ON bruteforce_ataques
    FOR EACH ROW 
    EXECUTE FUNCTION update_bruteforce_updated_at();

-- Função para consultar histórico de ataques (sem deletar dados)
CREATE OR REPLACE FUNCTION get_bruteforce_historico(user_uid TEXT, limite INTEGER DEFAULT 50)
RETURNS TABLE (
    id UUID,
    alvo_ip TEXT,
    alvo_nick TEXT,
    tempo_total INTEGER,
    status TEXT,
    quantia_roubada INTEGER,
    porcentagem INTEGER,
    inicio TIMESTAMP WITH TIME ZONE,
    finalizado_em TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        b.id,
        b.alvo_ip,
        b.alvo_nick,
        b.tempo_total,
        b.status,
        b.quantia_roubada,
        b.porcentagem,
        b.inicio,
        b.finalizado_em
    FROM bruteforce_ataques b
    WHERE b.atacante_uid = user_uid
    ORDER BY b.created_at DESC
    LIMIT limite;
END;
$$ LANGUAGE plpgsql;

-- Função para limpar ataques concluídos antigos (12 horas)
CREATE OR REPLACE FUNCTION limpar_bruteforce_antigos()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Remove ataques concluídos há mais de 12 horas
    DELETE FROM bruteforce_ataques 
    WHERE status IN ('concluido', 'falhado') 
    AND finalizado_em < NOW() - INTERVAL '12 hours';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Trigger para limpeza automática (executa a cada nova inserção)
CREATE OR REPLACE FUNCTION trigger_limpar_bruteforce_antigos()
RETURNS TRIGGER AS $$
BEGIN
    -- Executa limpeza automática de registros antigos
    PERFORM limpar_bruteforce_antigos();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Cria trigger para limpeza automática
DROP TRIGGER IF EXISTS auto_limpar_bruteforce ON bruteforce_ataques;
CREATE TRIGGER auto_limpar_bruteforce
    AFTER INSERT ON bruteforce_ataques
    FOR EACH ROW
    EXECUTE FUNCTION trigger_limpar_bruteforce_antigos();

-- Comentário sobre o uso
-- Esta tabela armazena informações sobre ataques bruteforce em andamento e concluídos
-- Permite rastreamento em tempo real do progresso dos ataques
-- Suporta cooldown e histórico de ataques por jogador
-- LIMPEZA AUTOMÁTICA: Remove ataques concluídos após 12 horas automaticamente
