{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleGamePage.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useChat } from '../stores/chatStore';\n\n// Componente do Dashboard Simplificado\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleDashboard = () => {\n  _s();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    loadPlayerData,\n    hasPlayerData\n  } = usePlayer();\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold cyber-text\",\n            children: \"Terminal Principal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-text-muted\",\n            children: [\"Operador: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-cyber-primary\",\n              children: (user === null || user === void 0 ? void 0 : user.nick) || 'Jogador'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), isLoadingPlayer && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 cyber-text\",\n          children: \"\\uD83D\\uDCCA Status do Sistema\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-blue-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-blue-400\",\n                children: isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-blue-300\",\n                children: \"PONTOS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-green-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-green-400\",\n                children: isLoadingPlayer ? '...' : playerData.nivel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-green-300\",\n                children: \"N\\xCDVEL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-purple-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-purple-400\",\n                children: isLoadingPlayer ? '...' : playerData.conquistas\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-purple-300\",\n                children: \"CONQUISTAS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-bg-tertiary rounded-lg p-3 border border-orange-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl font-bold text-orange-400\",\n                children: isLoadingPlayer ? '...' : playerData.ranking\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-orange-300\",\n                children: \"RANKING\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 cyber-text\",\n          children: \"\\u26A1 Acesso R\\xE1pido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.href = '/game/scanner',\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.href = '/game/chat',\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Loja\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-cyber text-sm py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg mb-1\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Config\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 cyber-text\",\n          children: \"\\uD83D\\uDCC8 Log do Sistema\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-sm\",\n                  children: \"+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Pontos ganhos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-text-muted\",\n                  children: \"02:15:33\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-green-400 font-bold text-sm\",\n              children: \"+150\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-sm\",\n                  children: \"\\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Nova conquista\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-text-muted\",\n                  children: \"01:22:15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-400 font-bold text-sm\",\n              children: \"HACK\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-sm\",\n                  children: \"\\u2191\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Level UP\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-text-muted\",\n                  children: \"00:45:22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-purple-400 font-bold text-sm\",\n              children: \"LV.15\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n\n// Componente Scanner Completo\n_s(SimpleDashboard, \"4ZBWbfZBu+lAD/wW1WhGuHvBS6I=\", false, function () {\n  return [useSimpleAuth, usePlayer];\n});\n_c = SimpleDashboard;\nconst SimpleScanner = () => {\n  _s2();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer\n  } = usePlayer();\n  const [isScanning, setIsScanning] = React.useState(false);\n  const [scanTargets, setScanTargets] = React.useState([]);\n  const [scanError, setScanError] = React.useState(null);\n  const [specificIP, setSpecificIP] = React.useState('');\n  const [isAdvancedScan, setIsAdvancedScan] = React.useState(false);\n  const handleQuickScan = async () => {\n    setIsScanning(true);\n    setScanError(null);\n    try {\n      const response = await fetch('/api/scan', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.sucesso && data.alvos) {\n        setScanTargets(data.alvos);\n      } else {\n        setScanError(data.mensagem || 'Erro ao escanear alvos');\n      }\n    } catch (error) {\n      setScanError(error.message || 'Erro de conexão');\n    } finally {\n      setIsScanning(false);\n    }\n  };\n  const handleAdvancedScan = async () => {\n    if (!specificIP.trim()) {\n      setScanError('Digite um IP válido');\n      return;\n    }\n    setIsAdvancedScan(true);\n    setScanError(null);\n    try {\n      const response = await fetch(`/api/scan/ip/${specificIP}`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.sucesso && data.alvo) {\n        setScanTargets([data.alvo]);\n      } else {\n        setScanError(data.mensagem || 'IP não encontrado');\n      }\n    } catch (error) {\n      setScanError(error.message || 'Erro de conexão');\n    } finally {\n      setIsAdvancedScan(false);\n    }\n  };\n  const handleExploit = async target => {\n    try {\n      const response = await fetch(`/api/alvo/${target.ip}/exploit`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.sucesso) {\n        alert(`Exploit bem-sucedido! ${data.mensagem}`);\n      } else {\n        alert(`Exploit falhou: ${data.mensagem}`);\n      }\n    } catch (error) {\n      alert(`Erro no exploit: ${error.message}`);\n    }\n  };\n  const playerCPU = (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.cpu) || 1;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-cyber-primary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-bold cyber-text\",\n          children: \"\\uD83D\\uDD0D Scanner de Rede\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-text-muted\",\n          children: \"Sistema de Reconhecimento - Criptografado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-bg-primary rounded-lg p-4 border border-cyber-primary\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-3 cyber-text\",\n            children: \"\\u26A1 Scan R\\xE1pido\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-text-muted mb-4\",\n            children: \"Encontra alvos aleat\\xF3rios na rede\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleQuickScan,\n            disabled: isScanning,\n            className: \"btn-cyber w-full py-3\",\n            children: isScanning ? 'Escaneando...' : 'Iniciar Scan Rápido'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-bg-primary rounded-lg p-4 border border-cyber-primary\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-3 cyber-text\",\n            children: \"\\uD83C\\uDFAF Scan Avan\\xE7ado\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-text-muted mb-4\",\n            children: \"Busca por IP espec\\xEDfico\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: specificIP,\n              onChange: e => setSpecificIP(e.target.value),\n              placeholder: \"*************\",\n              className: \"flex-1 bg-bg-tertiary border border-cyber-primary rounded px-3 py-2 text-white font-mono text-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAdvancedScan,\n              disabled: isAdvancedScan,\n              className: \"btn-cyber px-6\",\n              children: isAdvancedScan ? '...' : 'Scan'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), scanError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-900 border border-red-500 rounded-lg p-3\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-300 text-sm font-mono\",\n            children: [\"\\u274C \", scanError]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), scanTargets.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-bg-primary rounded-lg p-4 border border-cyber-primary\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4 cyber-text\",\n            children: \"\\uD83D\\uDCCA Alvos Encontrados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: scanTargets.map((target, index) => {\n              const canExploit = playerCPU > (target.firewall || 1);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-bg-tertiary rounded-lg p-4 border border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-bold text-blue-400\",\n                      children: target.nick\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-text-muted font-mono\",\n                      children: [\"IP: \", target.ip]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-text-muted\",\n                      children: [\"N\\xEDvel: \", target.nivel]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-text-muted\",\n                      children: [\"Firewall: \", target.firewall || 1]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-text-muted\",\n                      children: [\"Dinheiro: $\", (target.dinheiro || 0).toLocaleString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 rounded ${canExploit ? 'bg-green-600 text-green-100' : 'bg-red-600 text-red-100'}`,\n                      children: canExploit ? 'VULNERÁVEL' : 'PROTEGIDO'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleExploit(target),\n                    disabled: !canExploit,\n                    className: `px-4 py-2 rounded font-semibold text-sm ${canExploit ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-600 text-gray-400 cursor-not-allowed'}`,\n                    children: canExploit ? 'EXPLOITAR' : 'CPU INSUFICIENTE'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-bg-primary rounded-lg p-4 border border-cyber-primary\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-semibold mb-2 cyber-text\",\n            children: \"\\uD83D\\uDCBB Seu Sistema\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-2 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"CPU: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-cyber-primary\",\n                children: playerCPU\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Status: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-400\",\n                children: \"ONLINE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 28\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 258,\n    columnNumber: 5\n  }, this);\n};\n_s2(SimpleScanner, \"oWlcAdAxmHoLOO0f0puy0zIAsaA=\", false, function () {\n  return [useSimpleAuth, usePlayer];\n});\n_c2 = SimpleScanner;\nconst SimpleChat = () => {\n  _s3();\n  const {\n    messages,\n    isLoading: isLoadingMessages,\n    loadMessages,\n    sendMessage,\n    isSending\n  } = useChat();\n  const [newMessage, setNewMessage] = React.useState('');\n  useEffect(() => {\n    if (messages.length === 0 && !isLoadingMessages) {\n      console.log('SimpleChat - Carregando mensagens...');\n      loadMessages();\n    }\n  }, [messages.length, isLoadingMessages, loadMessages]);\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    if (newMessage.trim() && !isSending) {\n      await sendMessage(newMessage.trim());\n      setNewMessage('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-cyber-primary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-bold cyber-text\",\n          children: \"\\uD83D\\uDCAC Terminal de Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-text-muted\",\n          children: \"Canal Global - Criptografado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-black rounded-lg p-3 h-80 overflow-y-auto mb-4 border border-cyber-primary\",\n          children: isLoadingMessages ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-cyber-primary\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary mx-auto mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-mono\",\n              children: \"CARREGANDO DADOS...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this) : messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-cyber-primary\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mb-2\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs font-mono\",\n              children: \"CANAL VAZIO - AGUARDANDO TRANSMISS\\xC3O\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-l-2 border-cyber-primary pl-3 py-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-cyber-primary font-mono text-xs\",\n                  children: [\"[\", new Date(message.timestamp).toLocaleTimeString(), \"]\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-400 font-mono text-xs font-bold\",\n                  children: [message.usuario, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-white font-mono text-sm pl-2\",\n                children: message.mensagem\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 21\n              }, this)]\n            }, message.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSendMessage,\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newMessage,\n            onChange: e => setNewMessage(e.target.value),\n            placeholder: \"> Digite comando...\",\n            className: \"flex-1 px-3 py-2 bg-black border border-cyber-primary rounded font-mono text-cyber-primary text-sm focus:outline-none focus:border-cyber-secondary placeholder-gray-500\",\n            disabled: isSending\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: !newMessage.trim() || isSending,\n            className: `px-4 py-2 rounded font-mono text-sm transition-all ${!newMessage.trim() || isSending ? 'bg-gray-800 text-gray-500 cursor-not-allowed' : 'bg-cyber-primary text-black hover:bg-cyber-secondary font-bold'}`,\n            children: isSending ? '...' : 'SEND'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 392,\n    columnNumber: 5\n  }, this);\n};\n\n// Navegação Estilo Celular/Jogo\n_s3(SimpleChat, \"OOvOc6vVu05UbQBZJwuAFiCSFbw=\", false, function () {\n  return [useChat];\n});\n_c3 = SimpleChat;\nconst SimpleNavigation = () => {\n  const currentPath = window.location.pathname;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-bg-primary\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-900 to-purple-900 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-black font-bold text-lg\",\n              children: \"S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold cyber-text\",\n              children: \"SHACK\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-text-muted\",\n              children: \"Web Terminal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"status-online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-green-400\",\n            children: \"ONLINE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-bg-secondary border-b border-cyber-primary\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/game\",\n          className: `flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath === '/game' || currentPath === '/game/' ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary' : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg mb-1\",\n            children: \"\\uD83C\\uDFE0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/game/scanner\",\n          className: `flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath.includes('/scanner') ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary' : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg mb-1\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Scanner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/game/chat\",\n          className: `flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath.includes('/chat') ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary' : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg mb-1\",\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 464,\n    columnNumber: 5\n  }, this);\n};\n\n// Página Principal do Jogo Simplificada\n_c4 = SimpleNavigation;\nconst SimpleGamePage = () => {\n  console.log('SimpleGamePage - Renderizando...');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-bg-primary text-text-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md mx-auto bg-bg-primary min-h-screen relative\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-b from-bg-primary to-bg-secondary min-h-screen\",\n        children: [/*#__PURE__*/_jsxDEV(SimpleNavigation, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pb-4\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(SimpleDashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/scanner\",\n              element: /*#__PURE__*/_jsxDEV(SimpleScanner, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/chat\",\n              element: /*#__PURE__*/_jsxDEV(SimpleChat, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(SimpleDashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 531,\n    columnNumber: 5\n  }, this);\n};\n_c5 = SimpleGamePage;\nexport default SimpleGamePage;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"SimpleDashboard\");\n$RefreshReg$(_c2, \"SimpleScanner\");\n$RefreshReg$(_c3, \"SimpleChat\");\n$RefreshReg$(_c4, \"SimpleNavigation\");\n$RefreshReg$(_c5, \"SimpleGamePage\");", "map": {"version": 3, "names": ["React", "useEffect", "Routes", "Route", "useSimpleAuth", "usePlayer", "useChat", "jsxDEV", "_jsxDEV", "SimpleDashboard", "_s", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "conquistas", "ranking", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "toLocaleString", "onClick", "window", "location", "href", "_c", "SimpleScanner", "_s2", "isScanning", "setIsScanning", "useState", "scanTargets", "setScanTargets", "scanError", "setScanError", "specificIP", "setSpecificIP", "isAdvancedScan", "setIsAdvancedScan", "handleQuickScan", "response", "fetch", "method", "headers", "localStorage", "getItem", "data", "json", "sucesso", "alvos", "mensagem", "error", "message", "handleAdvancedScan", "trim", "alvo", "handleExploit", "target", "ip", "alert", "playerCPU", "cpu", "disabled", "type", "value", "onChange", "e", "placeholder", "length", "map", "index", "canExploit", "firewall", "<PERSON><PERSON><PERSON>", "_c2", "SimpleChat", "_s3", "messages", "isLoading", "isLoadingMessages", "loadMessages", "sendMessage", "isSending", "newMessage", "setNewMessage", "handleSendMessage", "preventDefault", "Date", "timestamp", "toLocaleTimeString", "usuario", "id", "onSubmit", "_c3", "SimpleNavigation", "currentPath", "pathname", "includes", "_c4", "SimpleGamePage", "path", "element", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleGamePage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useChat } from '../stores/chatStore';\n\n// Componente do Dashboard Simplificado\nconst SimpleDashboard: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n\n  return (\n    <div className=\"p-4\">\n      {/* Header compacto */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold cyber-text\">\n              Terminal Principal\n            </h1>\n            <p className=\"text-sm text-text-muted\">\n              Operador: <span className=\"text-cyber-primary\">{user?.nick || 'Jogador'}</span>\n            </p>\n          </div>\n          {isLoadingPlayer && (\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary\"></div>\n          )}\n        </div>\n      </div>\n\n      {/* Stats do Jogador - Layout Celular */}\n      <div className=\"space-y-4\">\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">📊 Status do Sistema</h2>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-blue-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-blue-400\">\n                    {isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()}\n                  </div>\n                  <div className=\"text-xs text-blue-300\">PONTOS</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-green-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-green-400\">\n                    {isLoadingPlayer ? '...' : playerData.nivel}\n                  </div>\n                  <div className=\"text-xs text-green-300\">NÍVEL</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-purple-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-purple-400\">\n                    {isLoadingPlayer ? '...' : playerData.conquistas}\n                  </div>\n                  <div className=\"text-xs text-purple-300\">CONQUISTAS</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-orange-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-orange-400\">\n                    {isLoadingPlayer ? '...' : playerData.ranking}\n                  </div>\n                  <div className=\"text-xs text-orange-300\">RANKING</div>\n                </div>\n              </div>\n            </div>\n        </div>\n\n        {/* Ações Rápidas - Estilo Celular */}\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">⚡ Acesso Rápido</h2>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <button\n                onClick={() => window.location.href = '/game/scanner'}\n                className=\"btn-cyber text-sm py-3\"\n              >\n                <div className=\"text-lg mb-1\">🔍</div>\n                <div>Scanner</div>\n              </button>\n              <button\n                onClick={() => window.location.href = '/game/chat'}\n                className=\"btn-cyber text-sm py-3\"\n              >\n                <div className=\"text-lg mb-1\">💬</div>\n                <div>Chat</div>\n              </button>\n              <button className=\"btn-cyber text-sm py-3\">\n                <div className=\"text-lg mb-1\">🏆</div>\n                <div>Loja</div>\n              </button>\n              <button className=\"btn-cyber text-sm py-3\">\n                <div className=\"text-lg mb-1\">⚙️</div>\n                <div>Config</div>\n              </button>\n            </div>\n        </div>\n\n        {/* Log de Atividades - Estilo Terminal */}\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">📈 Log do Sistema</h2>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">+</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Pontos ganhos</div>\n                    <div className=\"text-xs text-text-muted\">02:15:33</div>\n                  </div>\n                </div>\n                <div className=\"text-green-400 font-bold text-sm\">+150</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">🏆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Nova conquista</div>\n                    <div className=\"text-xs text-text-muted\">01:22:15</div>\n                  </div>\n                </div>\n                <div className=\"text-blue-400 font-bold text-sm\">HACK</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">↑</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Level UP</div>\n                    <div className=\"text-xs text-text-muted\">00:45:22</div>\n                  </div>\n                </div>\n                <div className=\"text-purple-400 font-bold text-sm\">LV.15</div>\n              </div>\n            </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Componente Scanner Completo\nconst SimpleScanner: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer } = usePlayer();\n  const [isScanning, setIsScanning] = React.useState(false);\n  const [scanTargets, setScanTargets] = React.useState<any[]>([]);\n  const [scanError, setScanError] = React.useState<string | null>(null);\n  const [specificIP, setSpecificIP] = React.useState('');\n  const [isAdvancedScan, setIsAdvancedScan] = React.useState(false);\n\n  const handleQuickScan = async () => {\n    setIsScanning(true);\n    setScanError(null);\n\n    try {\n      const response = await fetch('/api/scan', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      const data = await response.json();\n\n      if (data.sucesso && data.alvos) {\n        setScanTargets(data.alvos);\n      } else {\n        setScanError(data.mensagem || 'Erro ao escanear alvos');\n      }\n    } catch (error: any) {\n      setScanError(error.message || 'Erro de conexão');\n    } finally {\n      setIsScanning(false);\n    }\n  };\n\n  const handleAdvancedScan = async () => {\n    if (!specificIP.trim()) {\n      setScanError('Digite um IP válido');\n      return;\n    }\n\n    setIsAdvancedScan(true);\n    setScanError(null);\n\n    try {\n      const response = await fetch(`/api/scan/ip/${specificIP}`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      const data = await response.json();\n\n      if (data.sucesso && data.alvo) {\n        setScanTargets([data.alvo]);\n      } else {\n        setScanError(data.mensagem || 'IP não encontrado');\n      }\n    } catch (error: any) {\n      setScanError(error.message || 'Erro de conexão');\n    } finally {\n      setIsAdvancedScan(false);\n    }\n  };\n\n  const handleExploit = async (target: any) => {\n    try {\n      const response = await fetch(`/api/alvo/${target.ip}/exploit`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      const data = await response.json();\n\n      if (data.sucesso) {\n        alert(`Exploit bem-sucedido! ${data.mensagem}`);\n      } else {\n        alert(`Exploit falhou: ${data.mensagem}`);\n      }\n    } catch (error: any) {\n      alert(`Erro no exploit: ${error.message}`);\n    }\n  };\n\n  const playerCPU = currentPlayer?.cpu || 1;\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary\">\n        <div className=\"p-4 border-b border-cyber-primary\">\n          <h1 className=\"text-xl font-bold cyber-text\">🔍 Scanner de Rede</h1>\n          <p className=\"text-xs text-text-muted\">Sistema de Reconhecimento - Criptografado</p>\n        </div>\n\n        <div className=\"p-4 space-y-4\">\n          {/* Scan Rápido */}\n          <div className=\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\">\n            <h3 className=\"text-lg font-semibold mb-3 cyber-text\">⚡ Scan Rápido</h3>\n            <p className=\"text-sm text-text-muted mb-4\">Encontra alvos aleatórios na rede</p>\n            <button\n              onClick={handleQuickScan}\n              disabled={isScanning}\n              className=\"btn-cyber w-full py-3\"\n            >\n              {isScanning ? 'Escaneando...' : 'Iniciar Scan Rápido'}\n            </button>\n          </div>\n\n          {/* Scan Avançado */}\n          <div className=\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\">\n            <h3 className=\"text-lg font-semibold mb-3 cyber-text\">🎯 Scan Avançado</h3>\n            <p className=\"text-sm text-text-muted mb-4\">Busca por IP específico</p>\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                value={specificIP}\n                onChange={(e) => setSpecificIP(e.target.value)}\n                placeholder=\"*************\"\n                className=\"flex-1 bg-bg-tertiary border border-cyber-primary rounded px-3 py-2 text-white font-mono text-sm\"\n              />\n              <button\n                onClick={handleAdvancedScan}\n                disabled={isAdvancedScan}\n                className=\"btn-cyber px-6\"\n              >\n                {isAdvancedScan ? '...' : 'Scan'}\n              </button>\n            </div>\n          </div>\n\n          {/* Erro */}\n          {scanError && (\n            <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n              <p className=\"text-red-300 text-sm font-mono\">❌ {scanError}</p>\n            </div>\n          )}\n\n          {/* Resultados */}\n          {scanTargets.length > 0 && (\n            <div className=\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\">\n              <h3 className=\"text-lg font-semibold mb-4 cyber-text\">📊 Alvos Encontrados</h3>\n              <div className=\"space-y-3\">\n                {scanTargets.map((target, index) => {\n                  const canExploit = playerCPU > (target.firewall || 1);\n\n                  return (\n                    <div key={index} className=\"bg-bg-tertiary rounded-lg p-4 border border-blue-500\">\n                      <div className=\"flex justify-between items-start mb-3\">\n                        <div>\n                          <h4 className=\"font-bold text-blue-400\">{target.nick}</h4>\n                          <p className=\"text-xs text-text-muted font-mono\">IP: {target.ip}</p>\n                          <p className=\"text-xs text-text-muted\">Nível: {target.nivel}</p>\n                        </div>\n                        <div className=\"text-right\">\n                          <p className=\"text-xs text-text-muted\">Firewall: {target.firewall || 1}</p>\n                          <p className=\"text-xs text-text-muted\">Dinheiro: ${(target.dinheiro || 0).toLocaleString()}</p>\n                        </div>\n                      </div>\n\n                      <div className=\"flex justify-between items-center\">\n                        <div className=\"text-xs\">\n                          <span className={`px-2 py-1 rounded ${\n                            canExploit ? 'bg-green-600 text-green-100' : 'bg-red-600 text-red-100'\n                          }`}>\n                            {canExploit ? 'VULNERÁVEL' : 'PROTEGIDO'}\n                          </span>\n                        </div>\n\n                        <button\n                          onClick={() => handleExploit(target)}\n                          disabled={!canExploit}\n                          className={`px-4 py-2 rounded font-semibold text-sm ${\n                            canExploit\n                              ? 'bg-green-600 hover:bg-green-700 text-white'\n                              : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                          }`}\n                        >\n                          {canExploit ? 'EXPLOITAR' : 'CPU INSUFICIENTE'}\n                        </button>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          )}\n\n          {/* Info do Jogador */}\n          <div className=\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\">\n            <h3 className=\"text-sm font-semibold mb-2 cyber-text\">💻 Seu Sistema</h3>\n            <div className=\"grid grid-cols-2 gap-2 text-xs\">\n              <div>CPU: <span className=\"text-cyber-primary\">{playerCPU}</span></div>\n              <div>Status: <span className=\"text-green-400\">ONLINE</span></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nconst SimpleChat: React.FC = () => {\n  const { messages, isLoading: isLoadingMessages, loadMessages, sendMessage, isSending } = useChat();\n  const [newMessage, setNewMessage] = React.useState('');\n\n  useEffect(() => {\n    if (messages.length === 0 && !isLoadingMessages) {\n      console.log('SimpleChat - Carregando mensagens...');\n      loadMessages();\n    }\n  }, [messages.length, isLoadingMessages, loadMessages]);\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (newMessage.trim() && !isSending) {\n      await sendMessage(newMessage.trim());\n      setNewMessage('');\n    }\n  };\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary\">\n        <div className=\"p-4 border-b border-cyber-primary\">\n          <h1 className=\"text-xl font-bold cyber-text\">💬 Terminal de Chat</h1>\n          <p className=\"text-xs text-text-muted\">Canal Global - Criptografado</p>\n        </div>\n\n        {/* Área de mensagens - Estilo Terminal */}\n        <div className=\"p-4\">\n          <div className=\"bg-black rounded-lg p-3 h-80 overflow-y-auto mb-4 border border-cyber-primary\">\n            {isLoadingMessages ? (\n              <div className=\"text-center text-cyber-primary\">\n                <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary mx-auto mb-2\"></div>\n                <span className=\"text-xs font-mono\">CARREGANDO DADOS...</span>\n              </div>\n            ) : messages.length === 0 ? (\n              <div className=\"text-center text-cyber-primary\">\n                <div className=\"text-2xl mb-2\">💬</div>\n                <p className=\"text-xs font-mono\">CANAL VAZIO - AGUARDANDO TRANSMISSÃO</p>\n              </div>\n            ) : (\n              <div className=\"space-y-2\">\n                {messages.map((message) => (\n                  <div key={message.id} className=\"border-l-2 border-cyber-primary pl-3 py-1\">\n                    <div className=\"flex items-center space-x-2 mb-1\">\n                      <span className=\"text-cyber-primary font-mono text-xs\">\n                        [{new Date(message.timestamp).toLocaleTimeString()}]\n                      </span>\n                      <span className=\"text-blue-400 font-mono text-xs font-bold\">\n                        {message.usuario}:\n                      </span>\n                    </div>\n                    <p className=\"text-white font-mono text-sm pl-2\">{message.mensagem}</p>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Formulário de envio - Estilo Terminal */}\n          <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n            <input\n              type=\"text\"\n              value={newMessage}\n              onChange={(e) => setNewMessage(e.target.value)}\n              placeholder=\"> Digite comando...\"\n              className=\"flex-1 px-3 py-2 bg-black border border-cyber-primary rounded font-mono text-cyber-primary text-sm focus:outline-none focus:border-cyber-secondary placeholder-gray-500\"\n              disabled={isSending}\n            />\n            <button\n              type=\"submit\"\n              disabled={!newMessage.trim() || isSending}\n              className={`px-4 py-2 rounded font-mono text-sm transition-all ${\n                !newMessage.trim() || isSending\n                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'\n                  : 'bg-cyber-primary text-black hover:bg-cyber-secondary font-bold'\n              }`}\n            >\n              {isSending ? '...' : 'SEND'}\n            </button>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Navegação Estilo Celular/Jogo\nconst SimpleNavigation: React.FC = () => {\n  const currentPath = window.location.pathname;\n\n  return (\n    <div className=\"bg-bg-primary\">\n      {/* Header estilo celular */}\n      <div className=\"bg-gradient-to-r from-blue-900 to-purple-900 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-black font-bold text-lg\">S</span>\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold cyber-text\">SHACK</h1>\n              <p className=\"text-xs text-text-muted\">Web Terminal</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"status-online\"></div>\n            <span className=\"text-xs text-green-400\">ONLINE</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Menu de navegação estilo celular */}\n      <div className=\"bg-bg-secondary border-b border-cyber-primary\">\n        <div className=\"flex\">\n          <a\n            href=\"/game\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath === '/game' || currentPath === '/game/'\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">🏠</div>\n            <div>Dashboard</div>\n          </a>\n          <a\n            href=\"/game/scanner\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath.includes('/scanner')\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">🔍</div>\n            <div>Scanner</div>\n          </a>\n          <a\n            href=\"/game/chat\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath.includes('/chat')\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">💬</div>\n            <div>Chat</div>\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Página Principal do Jogo Simplificada\nconst SimpleGamePage: React.FC = () => {\n  console.log('SimpleGamePage - Renderizando...');\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary\">\n      {/* Container estilo celular */}\n      <div className=\"max-w-md mx-auto bg-bg-primary min-h-screen relative\">\n        {/* Simulação de tela de celular */}\n        <div className=\"bg-gradient-to-b from-bg-primary to-bg-secondary min-h-screen\">\n          <SimpleNavigation />\n\n          {/* Conteúdo principal */}\n          <div className=\"pb-4\">\n            <Routes>\n              <Route path=\"/\" element={<SimpleDashboard />} />\n              <Route path=\"/scanner\" element={<SimpleScanner />} />\n              <Route path=\"/chat\" element={<SimpleChat />} />\n              <Route path=\"*\" element={<SimpleDashboard />} />\n            </Routes>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleGamePage;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,OAAO,QAAQ,qBAAqB;;AAE7C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGP,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEQ,aAAa;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGV,SAAS,CAAC,CAAC;EAErFJ,SAAS,CAAC,MAAM;IACd,IAAI,CAACc,aAAa,IAAI,CAACF,eAAe,EAAE;MACtCG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/DH,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACC,aAAa,EAAEF,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAEpD;EACA,MAAMI,UAAU,GAAGN,aAAa,IAAI;IAClCO,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE;EACX,CAAC;EAED,oBACEd,OAAA;IAAKe,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElBhB,OAAA;MAAKe,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBhB,OAAA;QAAKe,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDhB,OAAA;UAAAgB,QAAA,gBACEhB,OAAA;YAAIe,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpB,OAAA;YAAGe,SAAS,EAAC,yBAAyB;YAAAC,QAAA,GAAC,YAC3B,eAAAhB,OAAA;cAAMe,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAE,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,KAAI;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLf,eAAe,iBACdL,OAAA;UAAKe,SAAS,EAAC;QAAmE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACzF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBhB,OAAA;QAAKe,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC1FhB,OAAA;UAAIe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EpB,OAAA;UAAKe,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrChB,OAAA;YAAKe,SAAS,EAAC,sDAAsD;YAAAC,QAAA,eACnEhB,OAAA;cAAKe,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhB,OAAA;gBAAKe,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC7CX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACC,MAAM,CAACW,cAAc,CAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpB,OAAA;YAAKe,SAAS,EAAC,uDAAuD;YAAAC,QAAA,eACpEhB,OAAA;cAAKe,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhB,OAAA;gBAAKe,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9CX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACE;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpB,OAAA;YAAKe,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrEhB,OAAA;cAAKe,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhB,OAAA;gBAAKe,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/CX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACG;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpB,OAAA;YAAKe,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrEhB,OAAA;cAAKe,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhB,OAAA;gBAAKe,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/CX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACI;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNpB,OAAA;QAAKe,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC1FhB,OAAA;UAAIe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EpB,OAAA;UAAKe,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrChB,OAAA;YACEuB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,eAAgB;YACtDX,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAElChB,OAAA;cAAKe,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCpB,OAAA;cAAAgB,QAAA,EAAK;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACTpB,OAAA;YACEuB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAa;YACnDX,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAElChB,OAAA;cAAKe,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCpB,OAAA;cAAAgB,QAAA,EAAK;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACTpB,OAAA;YAAQe,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACxChB,OAAA;cAAKe,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCpB,OAAA;cAAAgB,QAAA,EAAK;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACTpB,OAAA;YAAQe,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACxChB,OAAA;cAAKe,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCpB,OAAA;cAAAgB,QAAA,EAAK;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNpB,OAAA;QAAKe,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC1FhB,OAAA;UAAIe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EpB,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhB,OAAA;YAAKe,SAAS,EAAC,qFAAqF;YAAAC,QAAA,gBAClGhB,OAAA;cAAKe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChB,OAAA;gBAAKe,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,eACjFhB,OAAA;kBAAMe,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNpB,OAAA;gBAAAgB,QAAA,gBACEhB,OAAA;kBAAKe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDpB,OAAA;kBAAKe,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAENpB,OAAA;YAAKe,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGhB,OAAA;cAAKe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChB,OAAA;gBAAKe,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAChFhB,OAAA;kBAAMe,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNpB,OAAA;gBAAAgB,QAAA,gBACEhB,OAAA;kBAAKe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzDpB,OAAA;kBAAKe,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAENpB,OAAA;YAAKe,SAAS,EAAC,sFAAsF;YAAAC,QAAA,gBACnGhB,OAAA;cAAKe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChB,OAAA;gBAAKe,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,eAClFhB,OAAA;kBAAMe,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNpB,OAAA;gBAAAgB,QAAA,gBACEhB,OAAA;kBAAKe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDpB,OAAA;kBAAKe,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAlB,EAAA,CA5JMD,eAAyB;EAAA,QACZL,aAAa,EAC4CC,SAAS;AAAA;AAAA8B,EAAA,GAF/E1B,eAAyB;AA6J/B,MAAM2B,aAAuB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpC,MAAM;IAAE1B;EAAK,CAAC,GAAGP,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEQ;EAAc,CAAC,GAAGP,SAAS,CAAC,CAAC;EACrC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,KAAK,CAACwC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,KAAK,CAACwC,QAAQ,CAAQ,EAAE,CAAC;EAC/D,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAG5C,KAAK,CAACwC,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAG9C,KAAK,CAACwC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACO,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,KAAK,CAACwC,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAMS,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCV,aAAa,CAAC,IAAI,CAAC;IACnBK,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,WAAW,EAAE;QACxCC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACG,KAAK,EAAE;QAC9BjB,cAAc,CAACc,IAAI,CAACG,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLf,YAAY,CAACY,IAAI,CAACI,QAAQ,IAAI,wBAAwB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBjB,YAAY,CAACiB,KAAK,CAACC,OAAO,IAAI,iBAAiB,CAAC;IAClD,CAAC,SAAS;MACRvB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMwB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAClB,UAAU,CAACmB,IAAI,CAAC,CAAC,EAAE;MACtBpB,YAAY,CAAC,qBAAqB,CAAC;MACnC;IACF;IAEAI,iBAAiB,CAAC,IAAI,CAAC;IACvBJ,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgBN,UAAU,EAAE,EAAE;QACzDO,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACS,IAAI,EAAE;QAC7BvB,cAAc,CAAC,CAACc,IAAI,CAACS,IAAI,CAAC,CAAC;MAC7B,CAAC,MAAM;QACLrB,YAAY,CAACY,IAAI,CAACI,QAAQ,IAAI,mBAAmB,CAAC;MACpD;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBjB,YAAY,CAACiB,KAAK,CAACC,OAAO,IAAI,iBAAiB,CAAC;IAClD,CAAC,SAAS;MACRd,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMkB,aAAa,GAAG,MAAOC,MAAW,IAAK;IAC3C,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMC,KAAK,CAAC,aAAagB,MAAM,CAACC,EAAE,UAAU,EAAE;QAC7DhB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBW,KAAK,CAAC,yBAAyBb,IAAI,CAACI,QAAQ,EAAE,CAAC;MACjD,CAAC,MAAM;QACLS,KAAK,CAAC,mBAAmBb,IAAI,CAACI,QAAQ,EAAE,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBQ,KAAK,CAAC,oBAAoBR,KAAK,CAACC,OAAO,EAAE,CAAC;IAC5C;EACF,CAAC;EAED,MAAMQ,SAAS,GAAG,CAAA1D,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2D,GAAG,KAAI,CAAC;EAEzC,oBACE/D,OAAA;IAAKe,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBhB,OAAA;MAAKe,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBACxFhB,OAAA;QAAKe,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDhB,OAAA;UAAIe,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpEpB,OAAA;UAAGe,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjF,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BhB,OAAA;UAAKe,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvEhB,OAAA;YAAIe,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEpB,OAAA;YAAGe,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjFpB,OAAA;YACEuB,OAAO,EAAEkB,eAAgB;YACzBuB,QAAQ,EAAElC,UAAW;YACrBf,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAEhCc,UAAU,GAAG,eAAe,GAAG;UAAqB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNpB,OAAA;UAAKe,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvEhB,OAAA;YAAIe,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3EpB,OAAA;YAAGe,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvEpB,OAAA;YAAKe,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhB,OAAA;cACEiE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE7B,UAAW;cAClB8B,QAAQ,EAAGC,CAAC,IAAK9B,aAAa,CAAC8B,CAAC,CAACT,MAAM,CAACO,KAAK,CAAE;cAC/CG,WAAW,EAAC,eAAe;cAC3BtD,SAAS,EAAC;YAAkG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7G,CAAC,eACFpB,OAAA;cACEuB,OAAO,EAAEgC,kBAAmB;cAC5BS,QAAQ,EAAEzB,cAAe;cACzBxB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAEzBuB,cAAc,GAAG,KAAK,GAAG;YAAM;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLe,SAAS,iBACRnC,OAAA;UAAKe,SAAS,EAAC,iDAAiD;UAAAC,QAAA,eAC9DhB,OAAA;YAAGe,SAAS,EAAC,gCAAgC;YAAAC,QAAA,GAAC,SAAE,EAACmB,SAAS;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CACN,EAGAa,WAAW,CAACqC,MAAM,GAAG,CAAC,iBACrBtE,OAAA;UAAKe,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvEhB,OAAA;YAAIe,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EpB,OAAA;YAAKe,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBiB,WAAW,CAACsC,GAAG,CAAC,CAACZ,MAAM,EAAEa,KAAK,KAAK;cAClC,MAAMC,UAAU,GAAGX,SAAS,IAAIH,MAAM,CAACe,QAAQ,IAAI,CAAC,CAAC;cAErD,oBACE1E,OAAA;gBAAiBe,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBAC/EhB,OAAA;kBAAKe,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDhB,OAAA;oBAAAgB,QAAA,gBACEhB,OAAA;sBAAIe,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAE2C,MAAM,CAACtC;oBAAI;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1DpB,OAAA;sBAAGe,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAAC,MAAI,EAAC2C,MAAM,CAACC,EAAE;oBAAA;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpEpB,OAAA;sBAAGe,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,GAAC,YAAO,EAAC2C,MAAM,CAAC/C,KAAK;oBAAA;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNpB,OAAA;oBAAKe,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBhB,OAAA;sBAAGe,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,GAAC,YAAU,EAAC2C,MAAM,CAACe,QAAQ,IAAI,CAAC;oBAAA;sBAAAzD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3EpB,OAAA;sBAAGe,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,GAAC,aAAW,EAAC,CAAC2C,MAAM,CAACgB,QAAQ,IAAI,CAAC,EAAErD,cAAc,CAAC,CAAC;oBAAA;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENpB,OAAA;kBAAKe,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDhB,OAAA;oBAAKe,SAAS,EAAC,SAAS;oBAAAC,QAAA,eACtBhB,OAAA;sBAAMe,SAAS,EAAE,qBACf0D,UAAU,GAAG,6BAA6B,GAAG,yBAAyB,EACrE;sBAAAzD,QAAA,EACAyD,UAAU,GAAG,YAAY,GAAG;oBAAW;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAENpB,OAAA;oBACEuB,OAAO,EAAEA,CAAA,KAAMmC,aAAa,CAACC,MAAM,CAAE;oBACrCK,QAAQ,EAAE,CAACS,UAAW;oBACtB1D,SAAS,EAAE,2CACT0D,UAAU,GACN,4CAA4C,GAC5C,8CAA8C,EACjD;oBAAAzD,QAAA,EAEFyD,UAAU,GAAG,WAAW,GAAG;kBAAkB;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GAjCEoD,KAAK;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkCV,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDpB,OAAA;UAAKe,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvEhB,OAAA;YAAIe,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEpB,OAAA;YAAKe,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7ChB,OAAA;cAAAgB,QAAA,GAAK,OAAK,eAAAhB,OAAA;gBAAMe,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAE8C;cAAS;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvEpB,OAAA;cAAAgB,QAAA,GAAK,UAAQ,eAAAhB,OAAA;gBAAMe,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACS,GAAA,CA7MID,aAAuB;EAAA,QACVhC,aAAa,EACJC,SAAS;AAAA;AAAA+E,GAAA,GAF/BhD,aAAuB;AA+M7B,MAAMiD,UAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM;IAAEC,QAAQ;IAAEC,SAAS,EAAEC,iBAAiB;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGtF,OAAO,CAAC,CAAC;EAClG,MAAM,CAACuF,UAAU,EAAEC,aAAa,CAAC,GAAG9F,KAAK,CAACwC,QAAQ,CAAC,EAAE,CAAC;EAEtDvC,SAAS,CAAC,MAAM;IACd,IAAIsF,QAAQ,CAACT,MAAM,KAAK,CAAC,IAAI,CAACW,iBAAiB,EAAE;MAC/CzE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnDyE,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACH,QAAQ,CAACT,MAAM,EAAEW,iBAAiB,EAAEC,YAAY,CAAC,CAAC;EAEtD,MAAMK,iBAAiB,GAAG,MAAOnB,CAAkB,IAAK;IACtDA,CAAC,CAACoB,cAAc,CAAC,CAAC;IAClB,IAAIH,UAAU,CAAC7B,IAAI,CAAC,CAAC,IAAI,CAAC4B,SAAS,EAAE;MACnC,MAAMD,WAAW,CAACE,UAAU,CAAC7B,IAAI,CAAC,CAAC,CAAC;MACpC8B,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,oBACEtF,OAAA;IAAKe,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBhB,OAAA;MAAKe,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBACxFhB,OAAA;QAAKe,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDhB,OAAA;UAAIe,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEpB,OAAA;UAAGe,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eAGNpB,OAAA;QAAKe,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClBhB,OAAA;UAAKe,SAAS,EAAC,+EAA+E;UAAAC,QAAA,EAC3FiE,iBAAiB,gBAChBjF,OAAA;YAAKe,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7ChB,OAAA;cAAKe,SAAS,EAAC;YAAgF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtGpB,OAAA;cAAMe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,GACJ2D,QAAQ,CAACT,MAAM,KAAK,CAAC,gBACvBtE,OAAA;YAAKe,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7ChB,OAAA;cAAKe,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCpB,OAAA;cAAGe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,gBAENpB,OAAA;YAAKe,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvB+D,QAAQ,CAACR,GAAG,CAAEjB,OAAO,iBACpBtD,OAAA;cAAsBe,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACzEhB,OAAA;gBAAKe,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/ChB,OAAA;kBAAMe,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,GAAC,GACpD,EAAC,IAAIyE,IAAI,CAACnC,OAAO,CAACoC,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAC,GACrD;gBAAA;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPpB,OAAA;kBAAMe,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GACxDsC,OAAO,CAACsC,OAAO,EAAC,GACnB;gBAAA;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNpB,OAAA;gBAAGe,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEsC,OAAO,CAACF;cAAQ;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAT/DkC,OAAO,CAACuC,EAAE;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUf,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNpB,OAAA;UAAM8F,QAAQ,EAAEP,iBAAkB;UAACxE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3DhB,OAAA;YACEiE,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEmB,UAAW;YAClBlB,QAAQ,EAAGC,CAAC,IAAKkB,aAAa,CAAClB,CAAC,CAACT,MAAM,CAACO,KAAK,CAAE;YAC/CG,WAAW,EAAC,qBAAqB;YACjCtD,SAAS,EAAC,yKAAyK;YACnLiD,QAAQ,EAAEoB;UAAU;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACFpB,OAAA;YACEiE,IAAI,EAAC,QAAQ;YACbD,QAAQ,EAAE,CAACqB,UAAU,CAAC7B,IAAI,CAAC,CAAC,IAAI4B,SAAU;YAC1CrE,SAAS,EAAE,sDACT,CAACsE,UAAU,CAAC7B,IAAI,CAAC,CAAC,IAAI4B,SAAS,GAC3B,8CAA8C,GAC9C,gEAAgE,EACnE;YAAApE,QAAA,EAEFoE,SAAS,GAAG,KAAK,GAAG;UAAM;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA0D,GAAA,CAvFMD,UAAoB;EAAA,QACiE/E,OAAO;AAAA;AAAAiG,GAAA,GAD5FlB,UAAoB;AAwF1B,MAAMmB,gBAA0B,GAAGA,CAAA,KAAM;EACvC,MAAMC,WAAW,GAAGzE,MAAM,CAACC,QAAQ,CAACyE,QAAQ;EAE5C,oBACElG,OAAA;IAAKe,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BhB,OAAA;MAAKe,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eAC/DhB,OAAA;QAAKe,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDhB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChB,OAAA;YAAKe,SAAS,EAAC,wEAAwE;YAAAC,QAAA,eACrFhB,OAAA;cAAMe,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNpB,OAAA;YAAAgB,QAAA,gBACEhB,OAAA;cAAIe,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDpB,OAAA;cAAGe,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChB,OAAA;YAAKe,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrCpB,OAAA;YAAMe,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DhB,OAAA;QAAKe,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhB,OAAA;UACE0B,IAAI,EAAC,OAAO;UACZX,SAAS,EAAE,mEACTkF,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,QAAQ,GAC/C,6DAA6D,GAC7D,+DAA+D,EAClE;UAAAjF,QAAA,gBAEHhB,OAAA;YAAKe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCpB,OAAA;YAAAgB,QAAA,EAAK;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACJpB,OAAA;UACE0B,IAAI,EAAC,eAAe;UACpBX,SAAS,EAAE,mEACTkF,WAAW,CAACE,QAAQ,CAAC,UAAU,CAAC,GAC5B,6DAA6D,GAC7D,+DAA+D,EAClE;UAAAnF,QAAA,gBAEHhB,OAAA;YAAKe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCpB,OAAA;YAAAgB,QAAA,EAAK;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACJpB,OAAA;UACE0B,IAAI,EAAC,YAAY;UACjBX,SAAS,EAAE,mEACTkF,WAAW,CAACE,QAAQ,CAAC,OAAO,CAAC,GACzB,6DAA6D,GAC7D,+DAA+D,EAClE;UAAAnF,QAAA,gBAEHhB,OAAA;YAAKe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCpB,OAAA;YAAAgB,QAAA,EAAK;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAgF,GAAA,GAlEMJ,gBAA0B;AAmEhC,MAAMK,cAAwB,GAAGA,CAAA,KAAM;EACrC7F,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAE/C,oBACET,OAAA;IAAKe,SAAS,EAAC,8CAA8C;IAAAC,QAAA,eAE3DhB,OAAA;MAAKe,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eAEnEhB,OAAA;QAAKe,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAC5EhB,OAAA,CAACgG,gBAAgB;UAAA/E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGpBpB,OAAA;UAAKe,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBhB,OAAA,CAACN,MAAM;YAAAsB,QAAA,gBACLhB,OAAA,CAACL,KAAK;cAAC2G,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEvG,OAAA,CAACC,eAAe;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDpB,OAAA,CAACL,KAAK;cAAC2G,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEvG,OAAA,CAAC4B,aAAa;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDpB,OAAA,CAACL,KAAK;cAAC2G,IAAI,EAAC,OAAO;cAACC,OAAO,eAAEvG,OAAA,CAAC6E,UAAU;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CpB,OAAA,CAACL,KAAK;cAAC2G,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEvG,OAAA,CAACC,eAAe;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACoF,GAAA,GAxBIH,cAAwB;AA0B9B,eAAeA,cAAc;AAAC,IAAA1E,EAAA,EAAAiD,GAAA,EAAAmB,GAAA,EAAAK,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAA9E,EAAA;AAAA8E,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}