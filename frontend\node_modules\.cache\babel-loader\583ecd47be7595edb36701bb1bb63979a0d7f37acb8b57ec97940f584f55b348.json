{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\ui\\\\GlowingIcon.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GlowingIcon = ({\n  children,\n  color = 'blue',\n  size = 'md',\n  glow = true,\n  pulse = false,\n  className = ''\n}) => {\n  const sizeClasses = {\n    sm: 'w-8 h-8 text-lg',\n    md: 'w-12 h-12 text-2xl',\n    lg: 'w-16 h-16 text-3xl',\n    xl: 'w-20 h-20 text-4xl'\n  };\n  const colorClasses = {\n    blue: 'text-blue-400 bg-blue-500/10 border-blue-500/30',\n    green: 'text-green-400 bg-green-500/10 border-green-500/30',\n    red: 'text-red-400 bg-red-500/10 border-red-500/30',\n    yellow: 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30',\n    purple: 'text-purple-400 bg-purple-500/10 border-purple-500/30',\n    cyan: 'text-cyan-400 bg-cyan-500/10 border-cyan-500/30',\n    pink: 'text-pink-400 bg-pink-500/10 border-pink-500/30'\n  };\n  const glowClasses = glow ? {\n    blue: 'shadow-lg shadow-blue-500/30',\n    green: 'shadow-lg shadow-green-500/30',\n    red: 'shadow-lg shadow-red-500/30',\n    yellow: 'shadow-lg shadow-yellow-500/30',\n    purple: 'shadow-lg shadow-purple-500/30',\n    cyan: 'shadow-lg shadow-cyan-500/30',\n    pink: 'shadow-lg shadow-pink-500/30'\n  } : {};\n  const classes = ['rounded-xl border backdrop-blur-sm flex items-center justify-center transition-all duration-300 ease-in-out', sizeClasses[size], colorClasses[color], glow ? glowClasses[color] : '', pulse ? 'animate-pulse' : '', 'hover:scale-110 hover:brightness-110', className].filter(Boolean).join(' ');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: classes,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_c = GlowingIcon;\nexport default GlowingIcon;\nvar _c;\n$RefreshReg$(_c, \"GlowingIcon\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "GlowingIcon", "children", "color", "size", "glow", "pulse", "className", "sizeClasses", "sm", "md", "lg", "xl", "colorClasses", "blue", "green", "red", "yellow", "purple", "cyan", "pink", "glowClasses", "classes", "filter", "Boolean", "join", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/ui/GlowingIcon.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface GlowingIconProps {\n  children: React.ReactNode;\n  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'cyan' | 'pink';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  glow?: boolean;\n  pulse?: boolean;\n  className?: string;\n}\n\nconst GlowingIcon: React.FC<GlowingIconProps> = ({\n  children,\n  color = 'blue',\n  size = 'md',\n  glow = true,\n  pulse = false,\n  className = '',\n}) => {\n  const sizeClasses = {\n    sm: 'w-8 h-8 text-lg',\n    md: 'w-12 h-12 text-2xl',\n    lg: 'w-16 h-16 text-3xl',\n    xl: 'w-20 h-20 text-4xl',\n  };\n\n  const colorClasses = {\n    blue: 'text-blue-400 bg-blue-500/10 border-blue-500/30',\n    green: 'text-green-400 bg-green-500/10 border-green-500/30',\n    red: 'text-red-400 bg-red-500/10 border-red-500/30',\n    yellow: 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30',\n    purple: 'text-purple-400 bg-purple-500/10 border-purple-500/30',\n    cyan: 'text-cyan-400 bg-cyan-500/10 border-cyan-500/30',\n    pink: 'text-pink-400 bg-pink-500/10 border-pink-500/30',\n  };\n\n  const glowClasses = glow ? {\n    blue: 'shadow-lg shadow-blue-500/30',\n    green: 'shadow-lg shadow-green-500/30',\n    red: 'shadow-lg shadow-red-500/30',\n    yellow: 'shadow-lg shadow-yellow-500/30',\n    purple: 'shadow-lg shadow-purple-500/30',\n    cyan: 'shadow-lg shadow-cyan-500/30',\n    pink: 'shadow-lg shadow-pink-500/30',\n  } : {};\n\n  const classes = [\n    'rounded-xl border backdrop-blur-sm flex items-center justify-center transition-all duration-300 ease-in-out',\n    sizeClasses[size],\n    colorClasses[color],\n    glow ? glowClasses[color] : '',\n    pulse ? 'animate-pulse' : '',\n    'hover:scale-110 hover:brightness-110',\n    className,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div className={classes}>\n      {children}\n    </div>\n  );\n};\n\nexport default GlowingIcon;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW1B,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,QAAQ;EACRC,KAAK,GAAG,MAAM;EACdC,IAAI,GAAG,IAAI;EACXC,IAAI,GAAG,IAAI;EACXC,KAAK,GAAG,KAAK;EACbC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,iBAAiB;IACrBC,EAAE,EAAE,oBAAoB;IACxBC,EAAE,EAAE,oBAAoB;IACxBC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,IAAI,EAAE,iDAAiD;IACvDC,KAAK,EAAE,oDAAoD;IAC3DC,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,uDAAuD;IAC/DC,MAAM,EAAE,uDAAuD;IAC/DC,IAAI,EAAE,iDAAiD;IACvDC,IAAI,EAAE;EACR,CAAC;EAED,MAAMC,WAAW,GAAGhB,IAAI,GAAG;IACzBS,IAAI,EAAE,8BAA8B;IACpCC,KAAK,EAAE,+BAA+B;IACtCC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,gCAAgC;IACxCC,MAAM,EAAE,gCAAgC;IACxCC,IAAI,EAAE,8BAA8B;IACpCC,IAAI,EAAE;EACR,CAAC,GAAG,CAAC,CAAC;EAEN,MAAME,OAAO,GAAG,CACd,6GAA6G,EAC7Gd,WAAW,CAACJ,IAAI,CAAC,EACjBS,YAAY,CAACV,KAAK,CAAC,EACnBE,IAAI,GAAGgB,WAAW,CAAClB,KAAK,CAAC,GAAG,EAAE,EAC9BG,KAAK,GAAG,eAAe,GAAG,EAAE,EAC5B,sCAAsC,EACtCC,SAAS,CACV,CAACgB,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAE3B,oBACEzB,OAAA;IAAKO,SAAS,EAAEe,OAAQ;IAAApB,QAAA,EACrBA;EAAQ;IAAAwB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACC,EAAA,GAlDI7B,WAAuC;AAoD7C,eAAeA,WAAW;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}