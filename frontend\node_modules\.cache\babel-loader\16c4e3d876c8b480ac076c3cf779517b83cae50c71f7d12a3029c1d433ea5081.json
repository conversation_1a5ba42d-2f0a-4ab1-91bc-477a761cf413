{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\common\\\\GameFooter.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameFooter = ({\n  currentPage = ''\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const handleNavigation = path => {\n    navigate(path);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-around items-center max-w-md mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `flex flex-col items-center space-y-1 transition-colors ${currentPage === 'scanner' ? 'text-blue-400' : 'text-gray-400 hover:text-white'}`,\n        onClick: () => handleNavigation('/game/scanner'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs\",\n          children: \"Scan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `flex flex-col items-center space-y-1 transition-colors ${currentPage === 'chat' ? 'text-blue-400' : 'text-gray-400 hover:text-white'}`,\n        onClick: () => handleNavigation('/game/chat'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs\",\n          children: \"Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `flex flex-col items-center space-y-1 transition-colors ${currentPage === 'home' ? 'text-blue-300' : 'text-blue-400 hover:text-blue-300'}`,\n        onClick: () => handleNavigation('/game'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-8 h-8 rounded-full flex items-center justify-center ${currentPage === 'home' ? 'bg-blue-500' : 'bg-blue-600'}`,\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\uD83C\\uDFE0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs font-semibold\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `flex flex-col items-center space-y-1 transition-colors ${currentPage === 'terminal' ? 'text-blue-400' : 'text-gray-400 hover:text-white'}`,\n        onClick: () => handleNavigation('/game/terminal'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\uD83D\\uDCBB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs\",\n          children: \"Terminal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `flex flex-col items-center space-y-1 transition-colors ${currentPage === 'config' ? 'text-blue-400' : 'text-gray-400 hover:text-white'}`,\n        onClick: () => handleNavigation('/game/config'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs\",\n          children: \"Config\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(GameFooter, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = GameFooter;\nexport default GameFooter;\nvar _c;\n$RefreshReg$(_c, \"GameFooter\");", "map": {"version": 3, "names": ["React", "useNavigate", "jsxDEV", "_jsxDEV", "GameFooter", "currentPage", "_s", "navigate", "handleNavigation", "path", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/common/GameFooter.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\n\ninterface GameFooterProps {\n  currentPage?: string;\n}\n\nconst GameFooter: React.FC<GameFooterProps> = ({ currentPage = '' }) => {\n  const navigate = useNavigate();\n\n  const handleNavigation = (path: string) => {\n    navigate(path);\n  };\n\n  return (\n    <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n      <div className=\"flex justify-around items-center max-w-md mx-auto\">\n        <button \n          className={`flex flex-col items-center space-y-1 transition-colors ${\n            currentPage === 'scanner' ? 'text-blue-400' : 'text-gray-400 hover:text-white'\n          }`}\n          onClick={() => handleNavigation('/game/scanner')}\n        >\n          <div className=\"w-6 h-6 flex items-center justify-center\">\n            <span className=\"text-lg\">🔍</span>\n          </div>\n          <span className=\"text-xs\">Scan</span>\n        </button>\n\n        <button \n          className={`flex flex-col items-center space-y-1 transition-colors ${\n            currentPage === 'chat' ? 'text-blue-400' : 'text-gray-400 hover:text-white'\n          }`}\n          onClick={() => handleNavigation('/game/chat')}\n        >\n          <div className=\"w-6 h-6 flex items-center justify-center\">\n            <span className=\"text-lg\">💬</span>\n          </div>\n          <span className=\"text-xs\">Chat</span>\n        </button>\n        \n        {/* Botão Home no centro */}\n        <button \n          className={`flex flex-col items-center space-y-1 transition-colors ${\n            currentPage === 'home' ? 'text-blue-300' : 'text-blue-400 hover:text-blue-300'\n          }`}\n          onClick={() => handleNavigation('/game')}\n        >\n          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n            currentPage === 'home' ? 'bg-blue-500' : 'bg-blue-600'\n          }`}>\n            <span className=\"text-lg\">🏠</span>\n          </div>\n          <span className=\"text-xs font-semibold\">Home</span>\n        </button>\n        \n        <button\n          className={`flex flex-col items-center space-y-1 transition-colors ${\n            currentPage === 'terminal' ? 'text-blue-400' : 'text-gray-400 hover:text-white'\n          }`}\n          onClick={() => handleNavigation('/game/terminal')}\n        >\n          <div className=\"w-6 h-6 flex items-center justify-center\">\n            <span className=\"text-lg\">💻</span>\n          </div>\n          <span className=\"text-xs\">Terminal</span>\n        </button>\n        \n        <button\n          className={`flex flex-col items-center space-y-1 transition-colors ${\n            currentPage === 'config' ? 'text-blue-400' : 'text-gray-400 hover:text-white'\n          }`}\n          onClick={() => handleNavigation('/game/config')}\n        >\n          <div className=\"w-6 h-6 flex items-center justify-center\">\n            <span className=\"text-lg\">⚙️</span>\n          </div>\n          <span className=\"text-xs\">Config</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default GameFooter;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM/C,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,WAAW,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAE9B,MAAMO,gBAAgB,GAAIC,IAAY,IAAK;IACzCF,QAAQ,CAACE,IAAI,CAAC;EAChB,CAAC;EAED,oBACEN,OAAA;IAAKO,SAAS,EAAC,wDAAwD;IAAAC,QAAA,eACrER,OAAA;MAAKO,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAChER,OAAA;QACEO,SAAS,EAAE,0DACTL,WAAW,KAAK,SAAS,GAAG,eAAe,GAAG,gCAAgC,EAC7E;QACHO,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAAC,eAAe,CAAE;QAAAG,QAAA,gBAEjDR,OAAA;UAAKO,SAAS,EAAC,0CAA0C;UAAAC,QAAA,eACvDR,OAAA;YAAMO,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNb,OAAA;UAAMO,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAETb,OAAA;QACEO,SAAS,EAAE,0DACTL,WAAW,KAAK,MAAM,GAAG,eAAe,GAAG,gCAAgC,EAC1E;QACHO,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAAC,YAAY,CAAE;QAAAG,QAAA,gBAE9CR,OAAA;UAAKO,SAAS,EAAC,0CAA0C;UAAAC,QAAA,eACvDR,OAAA;YAAMO,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNb,OAAA;UAAMO,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAGTb,OAAA;QACEO,SAAS,EAAE,0DACTL,WAAW,KAAK,MAAM,GAAG,eAAe,GAAG,mCAAmC,EAC7E;QACHO,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAAC,OAAO,CAAE;QAAAG,QAAA,gBAEzCR,OAAA;UAAKO,SAAS,EAAE,yDACdL,WAAW,KAAK,MAAM,GAAG,aAAa,GAAG,aAAa,EACrD;UAAAM,QAAA,eACDR,OAAA;YAAMO,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNb,OAAA;UAAMO,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAETb,OAAA;QACEO,SAAS,EAAE,0DACTL,WAAW,KAAK,UAAU,GAAG,eAAe,GAAG,gCAAgC,EAC9E;QACHO,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAAC,gBAAgB,CAAE;QAAAG,QAAA,gBAElDR,OAAA;UAAKO,SAAS,EAAC,0CAA0C;UAAAC,QAAA,eACvDR,OAAA;YAAMO,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNb,OAAA;UAAMO,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eAETb,OAAA;QACEO,SAAS,EAAE,0DACTL,WAAW,KAAK,QAAQ,GAAG,eAAe,GAAG,gCAAgC,EAC5E;QACHO,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAAC,cAAc,CAAE;QAAAG,QAAA,gBAEhDR,OAAA;UAAKO,SAAS,EAAC,0CAA0C;UAAAC,QAAA,eACvDR,OAAA;YAAMO,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNb,OAAA;UAAMO,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CA3EIF,UAAqC;EAAA,QACxBH,WAAW;AAAA;AAAAgB,EAAA,GADxBb,UAAqC;AA6E3C,eAAeA,UAAU;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}