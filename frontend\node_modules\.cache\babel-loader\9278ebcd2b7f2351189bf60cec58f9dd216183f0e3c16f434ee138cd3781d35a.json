{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\TerminalPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\nimport GameFooter from '../components/common/GameFooter';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TerminalPage = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    currentPlayer\n  } = usePlayer();\n  const [terminalLines, setTerminalLines] = useState([]);\n  const [currentInput, setCurrentInput] = useState('');\n  const [isExecuting, setIsExecuting] = useState(false);\n  const [bruteforceStatus, setBruteforceStatus] = useState(null);\n  const terminalRef = useRef(null);\n  const inputRef = useRef(null);\n  useEffect(() => {\n    if (isAuthenticated) {\n      initializeTerminal();\n      loadBruteforceStatus();\n    }\n  }, [isAuthenticated]);\n  useEffect(() => {\n    // Auto-scroll para o final\n    if (terminalRef.current) {\n      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;\n    }\n  }, [terminalLines]);\n  const initializeTerminal = () => {\n    const welcomeLines = [{\n      id: '1',\n      type: 'system',\n      content: '=== SHACK TERMINAL v2.0 ===',\n      timestamp: new Date()\n    }, {\n      id: '2',\n      type: 'system',\n      content: `Bem-vindo, ${(user === null || user === void 0 ? void 0 : user.nick) || 'Hacker'}`,\n      timestamp: new Date()\n    }, {\n      id: '3',\n      type: 'system',\n      content: 'Digite \"help\" para ver comandos disponíveis',\n      timestamp: new Date()\n    }, {\n      id: '4',\n      type: 'output',\n      content: 'root@shack:~$ ',\n      timestamp: new Date()\n    }];\n    setTerminalLines(welcomeLines);\n  };\n  const loadBruteforceStatus = async () => {\n    try {\n      const response = await gameApi.getBruteforceStatus();\n      if (response.sucesso) {\n        setBruteforceStatus(response.status);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar status do bruteforce:', error);\n    }\n  };\n  const addLine = (type, content) => {\n    const newLine = {\n      id: Date.now().toString(),\n      type,\n      content,\n      timestamp: new Date()\n    };\n    setTerminalLines(prev => [...prev, newLine]);\n  };\n  const executeCommand = async command => {\n    var _currentPlayer$dinhei;\n    // Adicionar comando digitado\n    addLine('input', `root@shack:~$ ${command}`);\n    const cmd = command.trim().toLowerCase();\n    const args = cmd.split(' ');\n    setIsExecuting(true);\n    try {\n      switch (args[0]) {\n        case 'help':\n          addLine('output', 'Comandos disponíveis:');\n          addLine('output', '  help - Mostra esta ajuda');\n          addLine('output', '  clear - Limpa o terminal');\n          addLine('output', '  status - Mostra status do sistema');\n          addLine('output', '  bruteforce <target> - Inicia ataque de força bruta');\n          addLine('output', '  scan - Escaneia rede local');\n          addLine('output', '  whoami - Mostra informações do usuário');\n          addLine('output', '  ps - Lista processos ativos');\n          break;\n        case 'clear':\n          setTerminalLines([]);\n          addLine('output', 'root@shack:~$ ');\n          break;\n        case 'status':\n          addLine('output', `Sistema: SHACK OS v2.0`);\n          addLine('output', `Usuário: ${(user === null || user === void 0 ? void 0 : user.nick) || 'Unknown'}`);\n          addLine('output', `IP: ${(user === null || user === void 0 ? void 0 : user.ip) || (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.ip) || 'Unknown'}`);\n          addLine('output', `CPU: Nível ${(currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.cpu) || 1}`);\n          addLine('output', `RAM: Nível ${(currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.ram) || 1}`);\n          addLine('output', `Firewall: Nível ${(currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.firewall) || 1}`);\n          addLine('output', `Dinheiro: $${(currentPlayer === null || currentPlayer === void 0 ? void 0 : (_currentPlayer$dinhei = currentPlayer.dinheiro) === null || _currentPlayer$dinhei === void 0 ? void 0 : _currentPlayer$dinhei.toLocaleString()) || '0'}`);\n          break;\n        case 'whoami':\n          addLine('output', `${(user === null || user === void 0 ? void 0 : user.nick) || 'Unknown'}`);\n          addLine('output', `UID: ${(user === null || user === void 0 ? void 0 : user.uid) || 'Unknown'}`);\n          addLine('output', `Email: ${(user === null || user === void 0 ? void 0 : user.email) || 'Unknown'}`);\n          addLine('output', `Nível: ${(currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.nivel) || 1}`);\n          break;\n        case 'ps':\n          addLine('output', 'PID  COMMAND');\n          addLine('output', '1    systemd');\n          addLine('output', '2    kthreadd');\n          addLine('output', '3    shack-daemon');\n          addLine('output', '4    network-scanner');\n          if (bruteforceStatus !== null && bruteforceStatus !== void 0 && bruteforceStatus.ativo) {\n            addLine('output', '5    bruteforce-engine');\n          }\n          break;\n        case 'bruteforce':\n          if (args.length < 2) {\n            addLine('error', 'Uso: bruteforce <target>');\n            break;\n          }\n          const target = args[1];\n          addLine('output', `Iniciando ataque de força bruta contra ${target}...`);\n          try {\n            const response = await gameApi.executeBruteforce(target, 'default');\n            if (response.sucesso) {\n              addLine('output', `✅ Bruteforce iniciado com sucesso!`);\n              addLine('output', `Target: ${target}`);\n              addLine('output', `Wordlist: default.txt`);\n              addLine('output', `Threads: ${(currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.bruteforce) || 1}`);\n\n              // Simular progresso\n              setTimeout(() => {\n                addLine('output', 'Testando senhas...');\n              }, 1000);\n              setTimeout(() => {\n                addLine('output', response.mensagem || 'Ataque concluído');\n              }, 3000);\n            } else {\n              addLine('error', response.mensagem || 'Falha no bruteforce');\n            }\n          } catch (error) {\n            addLine('error', `Erro: ${error.message}`);\n          }\n          break;\n        case 'scan':\n          addLine('output', 'Escaneando rede local...');\n          try {\n            const response = await gameApi.scanTargets();\n            if (response.sucesso && response.alvos) {\n              addLine('output', `Encontrados ${response.alvos.length} alvos:`);\n              response.alvos.slice(0, 5).forEach(alvo => {\n                addLine('output', `  ${alvo.ip} - ${alvo.nick} (Nível ${alvo.nivel})`);\n              });\n            } else {\n              addLine('output', 'Nenhum alvo encontrado');\n            }\n          } catch (error) {\n            addLine('error', `Erro no scan: ${error.message}`);\n          }\n          break;\n        case 'exit':\n          addLine('output', 'Saindo do terminal...');\n          setTimeout(() => {\n            window.history.back();\n          }, 1000);\n          break;\n        default:\n          if (cmd.trim()) {\n            addLine('error', `Comando não encontrado: ${args[0]}`);\n            addLine('output', 'Digite \"help\" para ver comandos disponíveis');\n          }\n          break;\n      }\n    } catch (error) {\n      addLine('error', `Erro: ${error.message}`);\n    } finally {\n      setIsExecuting(false);\n      addLine('output', 'root@shack:~$ ');\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (currentInput.trim() && !isExecuting) {\n      executeCommand(currentInput);\n      setCurrentInput('');\n    }\n  };\n  const getLineColor = type => {\n    switch (type) {\n      case 'input':\n        return 'text-white';\n      case 'output':\n        return 'text-green-400';\n      case 'error':\n        return 'text-red-400';\n      case 'system':\n        return 'text-blue-400';\n      default:\n        return 'text-gray-300';\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-900 text-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"\\uD83D\\uDD12 Acesso Negado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Voc\\xEA precisa estar logado para acessar o terminal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-black text-green-400 flex flex-col font-mono\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-900 border-b border-green-400 p-2 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-6 h-6 bg-gray-800 rounded flex items-center justify-center hover:bg-gray-700 text-green-400\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-green-400\",\n            children: \"\\u25CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"SHACK Terminal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500\",\n            children: [\"- \", user === null || user === void 0 ? void 0 : user.nick, \"@\", user === null || user === void 0 ? void 0 : user.ip]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: terminalRef,\n      className: \"flex-1 p-4 overflow-y-auto bg-black\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: [terminalLines.map(line => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${getLineColor(line.type)} text-sm leading-relaxed`,\n          children: line.content\n        }, line.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-green-400 mr-2\",\n            children: \"root@shack:~$\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            ref: inputRef,\n            type: \"text\",\n            value: currentInput,\n            onChange: e => setCurrentInput(e.target.value),\n            className: \"flex-1 bg-transparent text-green-400 outline-none\",\n            disabled: isExecuting,\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), isExecuting && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-yellow-400 ml-2 animate-pulse\",\n            children: \"\\u23F3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-900 border-t border-green-400 p-2 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"CPU: \", (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.cpu) || 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"RAM: \", (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.ram) || 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"BruteForce: \", (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.bruteforce) || 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-4\",\n          children: [(bruteforceStatus === null || bruteforceStatus === void 0 ? void 0 : bruteforceStatus.ativo) && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-yellow-400\",\n            children: \"\\uD83D\\uDD28 BruteForce Ativo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: new Date().toLocaleTimeString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(GameFooter, {\n      currentPage: \"terminal\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n};\n_s(TerminalPage, \"YunAC1TXa+OLjd3hns7qPJQlCrA=\", false, function () {\n  return [useAuth, usePlayer];\n});\n_c = TerminalPage;\nexport default TerminalPage;\nvar _c;\n$RefreshReg$(_c, \"TerminalPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "usePlayer", "gameApi", "GameFooter", "jsxDEV", "_jsxDEV", "TerminalPage", "_s", "user", "isAuthenticated", "currentPlayer", "terminalLines", "setTerminalLines", "currentInput", "setCurrentInput", "isExecuting", "setIsExecuting", "bruteforceStatus", "setBruteforceStatus", "terminalRef", "inputRef", "initializeTerminal", "loadBruteforceStatus", "current", "scrollTop", "scrollHeight", "welcomeLines", "id", "type", "content", "timestamp", "Date", "nick", "response", "getBruteforceStatus", "sucesso", "status", "error", "console", "addLine", "newLine", "now", "toString", "prev", "executeCommand", "command", "_currentPlayer$dinhei", "cmd", "trim", "toLowerCase", "args", "split", "ip", "cpu", "ram", "firewall", "<PERSON><PERSON><PERSON>", "toLocaleString", "uid", "email", "nivel", "ativo", "length", "target", "executeBruteforce", "bruteforce", "setTimeout", "mensagem", "message", "scanTargets", "alvos", "slice", "for<PERSON>ach", "alvo", "window", "history", "back", "handleSubmit", "e", "preventDefault", "getLineColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "ref", "map", "line", "onSubmit", "value", "onChange", "disabled", "autoFocus", "toLocaleTimeString", "currentPage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/TerminalPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\nimport GameFooter from '../components/common/GameFooter';\n\ninterface TerminalLine {\n  id: string;\n  type: 'input' | 'output' | 'error' | 'system';\n  content: string;\n  timestamp: Date;\n}\n\nconst TerminalPage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const { currentPlayer } = usePlayer();\n  \n  const [terminalLines, setTerminalLines] = useState<TerminalLine[]>([]);\n  const [currentInput, setCurrentInput] = useState('');\n  const [isExecuting, setIsExecuting] = useState(false);\n  const [bruteforceStatus, setBruteforceStatus] = useState<any>(null);\n  \n  const terminalRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      initializeTerminal();\n      loadBruteforceStatus();\n    }\n  }, [isAuthenticated]);\n\n  useEffect(() => {\n    // Auto-scroll para o final\n    if (terminalRef.current) {\n      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;\n    }\n  }, [terminalLines]);\n\n  const initializeTerminal = () => {\n    const welcomeLines: TerminalLine[] = [\n      {\n        id: '1',\n        type: 'system',\n        content: '=== SHACK TERMINAL v2.0 ===',\n        timestamp: new Date(),\n      },\n      {\n        id: '2',\n        type: 'system',\n        content: `Bem-vindo, ${user?.nick || 'Hacker'}`,\n        timestamp: new Date(),\n      },\n      {\n        id: '3',\n        type: 'system',\n        content: 'Digite \"help\" para ver comandos disponíveis',\n        timestamp: new Date(),\n      },\n      {\n        id: '4',\n        type: 'output',\n        content: 'root@shack:~$ ',\n        timestamp: new Date(),\n      },\n    ];\n\n    setTerminalLines(welcomeLines);\n  };\n\n  const loadBruteforceStatus = async () => {\n    try {\n      const response = await gameApi.getBruteforceStatus();\n      if (response.sucesso) {\n        setBruteforceStatus(response.status);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar status do bruteforce:', error);\n    }\n  };\n\n  const addLine = (type: TerminalLine['type'], content: string) => {\n    const newLine: TerminalLine = {\n      id: Date.now().toString(),\n      type,\n      content,\n      timestamp: new Date(),\n    };\n\n    setTerminalLines(prev => [...prev, newLine]);\n  };\n\n  const executeCommand = async (command: string) => {\n    // Adicionar comando digitado\n    addLine('input', `root@shack:~$ ${command}`);\n\n    const cmd = command.trim().toLowerCase();\n    const args = cmd.split(' ');\n\n    setIsExecuting(true);\n\n    try {\n      switch (args[0]) {\n        case 'help':\n          addLine('output', 'Comandos disponíveis:');\n          addLine('output', '  help - Mostra esta ajuda');\n          addLine('output', '  clear - Limpa o terminal');\n          addLine('output', '  status - Mostra status do sistema');\n          addLine('output', '  bruteforce <target> - Inicia ataque de força bruta');\n          addLine('output', '  scan - Escaneia rede local');\n          addLine('output', '  whoami - Mostra informações do usuário');\n          addLine('output', '  ps - Lista processos ativos');\n          break;\n\n        case 'clear':\n          setTerminalLines([]);\n          addLine('output', 'root@shack:~$ ');\n          break;\n\n        case 'status':\n          addLine('output', `Sistema: SHACK OS v2.0`);\n          addLine('output', `Usuário: ${user?.nick || 'Unknown'}`);\n          addLine('output', `IP: ${user?.ip || currentPlayer?.ip || 'Unknown'}`);\n          addLine('output', `CPU: Nível ${currentPlayer?.cpu || 1}`);\n          addLine('output', `RAM: Nível ${currentPlayer?.ram || 1}`);\n          addLine('output', `Firewall: Nível ${currentPlayer?.firewall || 1}`);\n          addLine('output', `Dinheiro: $${currentPlayer?.dinheiro?.toLocaleString() || '0'}`);\n          break;\n\n        case 'whoami':\n          addLine('output', `${user?.nick || 'Unknown'}`);\n          addLine('output', `UID: ${user?.uid || 'Unknown'}`);\n          addLine('output', `Email: ${user?.email || 'Unknown'}`);\n          addLine('output', `Nível: ${currentPlayer?.nivel || 1}`);\n          break;\n\n        case 'ps':\n          addLine('output', 'PID  COMMAND');\n          addLine('output', '1    systemd');\n          addLine('output', '2    kthreadd');\n          addLine('output', '3    shack-daemon');\n          addLine('output', '4    network-scanner');\n          if (bruteforceStatus?.ativo) {\n            addLine('output', '5    bruteforce-engine');\n          }\n          break;\n\n        case 'bruteforce':\n          if (args.length < 2) {\n            addLine('error', 'Uso: bruteforce <target>');\n            break;\n          }\n\n          const target = args[1];\n          addLine('output', `Iniciando ataque de força bruta contra ${target}...`);\n          \n          try {\n            const response = await gameApi.executeBruteforce(target, 'default');\n            \n            if (response.sucesso) {\n              addLine('output', `✅ Bruteforce iniciado com sucesso!`);\n              addLine('output', `Target: ${target}`);\n              addLine('output', `Wordlist: default.txt`);\n              addLine('output', `Threads: ${currentPlayer?.bruteforce || 1}`);\n              \n              // Simular progresso\n              setTimeout(() => {\n                addLine('output', 'Testando senhas...');\n              }, 1000);\n              \n              setTimeout(() => {\n                addLine('output', response.mensagem || 'Ataque concluído');\n              }, 3000);\n            } else {\n              addLine('error', response.mensagem || 'Falha no bruteforce');\n            }\n          } catch (error: any) {\n            addLine('error', `Erro: ${error.message}`);\n          }\n          break;\n\n        case 'scan':\n          addLine('output', 'Escaneando rede local...');\n          \n          try {\n            const response = await gameApi.scanTargets();\n            \n            if (response.sucesso && response.alvos) {\n              addLine('output', `Encontrados ${response.alvos.length} alvos:`);\n              response.alvos.slice(0, 5).forEach((alvo: any) => {\n                addLine('output', `  ${alvo.ip} - ${alvo.nick} (Nível ${alvo.nivel})`);\n              });\n            } else {\n              addLine('output', 'Nenhum alvo encontrado');\n            }\n          } catch (error: any) {\n            addLine('error', `Erro no scan: ${error.message}`);\n          }\n          break;\n\n        case 'exit':\n          addLine('output', 'Saindo do terminal...');\n          setTimeout(() => {\n            window.history.back();\n          }, 1000);\n          break;\n\n        default:\n          if (cmd.trim()) {\n            addLine('error', `Comando não encontrado: ${args[0]}`);\n            addLine('output', 'Digite \"help\" para ver comandos disponíveis');\n          }\n          break;\n      }\n    } catch (error: any) {\n      addLine('error', `Erro: ${error.message}`);\n    } finally {\n      setIsExecuting(false);\n      addLine('output', 'root@shack:~$ ');\n    }\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (currentInput.trim() && !isExecuting) {\n      executeCommand(currentInput);\n      setCurrentInput('');\n    }\n  };\n\n  const getLineColor = (type: TerminalLine['type']) => {\n    switch (type) {\n      case 'input':\n        return 'text-white';\n      case 'output':\n        return 'text-green-400';\n      case 'error':\n        return 'text-red-400';\n      case 'system':\n        return 'text-blue-400';\n      default:\n        return 'text-gray-300';\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"h-screen bg-gray-900 text-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-4\">🔒 Acesso Negado</h1>\n          <p className=\"text-gray-400\">Você precisa estar logado para acessar o terminal</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-black text-green-400 flex flex-col font-mono\">\n      {/* Header */}\n      <div className=\"bg-gray-900 border-b border-green-400 p-2 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"w-6 h-6 bg-gray-800 rounded flex items-center justify-center hover:bg-gray-700 text-green-400\"\n          >\n            <span className=\"text-sm\">←</span>\n          </button>\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-green-400\">●</span>\n            <span className=\"text-sm\">SHACK Terminal</span>\n            <span className=\"text-xs text-gray-500\">- {user?.nick}@{user?.ip}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Terminal */}\n      <div \n        ref={terminalRef}\n        className=\"flex-1 p-4 overflow-y-auto bg-black\"\n      >\n        <div className=\"space-y-1\">\n          {terminalLines.map((line) => (\n            <div \n              key={line.id} \n              className={`${getLineColor(line.type)} text-sm leading-relaxed`}\n            >\n              {line.content}\n            </div>\n          ))}\n          \n          {/* Input line */}\n          <form onSubmit={handleSubmit} className=\"flex items-center\">\n            <span className=\"text-green-400 mr-2\">root@shack:~$</span>\n            <input\n              ref={inputRef}\n              type=\"text\"\n              value={currentInput}\n              onChange={(e) => setCurrentInput(e.target.value)}\n              className=\"flex-1 bg-transparent text-green-400 outline-none\"\n              disabled={isExecuting}\n              autoFocus\n            />\n            {isExecuting && (\n              <span className=\"text-yellow-400 ml-2 animate-pulse\">⏳</span>\n            )}\n          </form>\n        </div>\n      </div>\n\n      {/* Status bar */}\n      <div className=\"bg-gray-900 border-t border-green-400 p-2 flex-shrink-0\">\n        <div className=\"flex justify-between items-center text-xs\">\n          <div className=\"flex space-x-4\">\n            <span>CPU: {currentPlayer?.cpu || 1}</span>\n            <span>RAM: {currentPlayer?.ram || 1}</span>\n            <span>BruteForce: {currentPlayer?.bruteforce || 1}</span>\n          </div>\n          <div className=\"flex space-x-4\">\n            {bruteforceStatus?.ativo && (\n              <span className=\"text-yellow-400\">🔨 BruteForce Ativo</span>\n            )}\n            <span>{new Date().toLocaleTimeString()}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer com navegação */}\n      <GameFooter currentPage=\"terminal\" />\n    </div>\n  );\n};\n\nexport default TerminalPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,UAAU,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASzD,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEU;EAAc,CAAC,GAAGT,SAAS,CAAC,CAAC;EAErC,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAM,IAAI,CAAC;EAEnE,MAAMsB,WAAW,GAAGpB,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAMqB,QAAQ,GAAGrB,MAAM,CAAmB,IAAI,CAAC;EAE/CD,SAAS,CAAC,MAAM;IACd,IAAIW,eAAe,EAAE;MACnBY,kBAAkB,CAAC,CAAC;MACpBC,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACb,eAAe,CAAC,CAAC;EAErBX,SAAS,CAAC,MAAM;IACd;IACA,IAAIqB,WAAW,CAACI,OAAO,EAAE;MACvBJ,WAAW,CAACI,OAAO,CAACC,SAAS,GAAGL,WAAW,CAACI,OAAO,CAACE,YAAY;IAClE;EACF,CAAC,EAAE,CAACd,aAAa,CAAC,CAAC;EAEnB,MAAMU,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMK,YAA4B,GAAG,CACnC;MACEC,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,6BAA6B;MACtCC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,EACD;MACEJ,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,cAAc,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,KAAI,QAAQ,EAAE;MAC/CF,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,EACD;MACEJ,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,6CAA6C;MACtDC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,EACD;MACEJ,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,gBAAgB;MACzBC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,CACF;IAEDnB,gBAAgB,CAACc,YAAY,CAAC;EAChC,CAAC;EAED,MAAMJ,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAM/B,OAAO,CAACgC,mBAAmB,CAAC,CAAC;MACpD,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBjB,mBAAmB,CAACe,QAAQ,CAACG,MAAM,CAAC;MACtC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC;EAED,MAAME,OAAO,GAAGA,CAACX,IAA0B,EAAEC,OAAe,KAAK;IAC/D,MAAMW,OAAqB,GAAG;MAC5Bb,EAAE,EAAEI,IAAI,CAACU,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBd,IAAI;MACJC,OAAO;MACPC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDnB,gBAAgB,CAAC+B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,OAAO,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMI,cAAc,GAAG,MAAOC,OAAe,IAAK;IAAA,IAAAC,qBAAA;IAChD;IACAP,OAAO,CAAC,OAAO,EAAE,iBAAiBM,OAAO,EAAE,CAAC;IAE5C,MAAME,GAAG,GAAGF,OAAO,CAACG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACxC,MAAMC,IAAI,GAAGH,GAAG,CAACI,KAAK,CAAC,GAAG,CAAC;IAE3BnC,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,QAAQkC,IAAI,CAAC,CAAC,CAAC;QACb,KAAK,MAAM;UACTX,OAAO,CAAC,QAAQ,EAAE,uBAAuB,CAAC;UAC1CA,OAAO,CAAC,QAAQ,EAAE,4BAA4B,CAAC;UAC/CA,OAAO,CAAC,QAAQ,EAAE,4BAA4B,CAAC;UAC/CA,OAAO,CAAC,QAAQ,EAAE,qCAAqC,CAAC;UACxDA,OAAO,CAAC,QAAQ,EAAE,sDAAsD,CAAC;UACzEA,OAAO,CAAC,QAAQ,EAAE,8BAA8B,CAAC;UACjDA,OAAO,CAAC,QAAQ,EAAE,0CAA0C,CAAC;UAC7DA,OAAO,CAAC,QAAQ,EAAE,+BAA+B,CAAC;UAClD;QAEF,KAAK,OAAO;UACV3B,gBAAgB,CAAC,EAAE,CAAC;UACpB2B,OAAO,CAAC,QAAQ,EAAE,gBAAgB,CAAC;UACnC;QAEF,KAAK,QAAQ;UACXA,OAAO,CAAC,QAAQ,EAAE,wBAAwB,CAAC;UAC3CA,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,KAAI,SAAS,EAAE,CAAC;UACxDO,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,EAAE,MAAI1C,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0C,EAAE,KAAI,SAAS,EAAE,CAAC;UACtEb,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAA7B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2C,GAAG,KAAI,CAAC,EAAE,CAAC;UAC1Dd,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAA7B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4C,GAAG,KAAI,CAAC,EAAE,CAAC;UAC1Df,OAAO,CAAC,QAAQ,EAAE,mBAAmB,CAAA7B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6C,QAAQ,KAAI,CAAC,EAAE,CAAC;UACpEhB,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAA7B,aAAa,aAAbA,aAAa,wBAAAoC,qBAAA,GAAbpC,aAAa,CAAE8C,QAAQ,cAAAV,qBAAA,uBAAvBA,qBAAA,CAAyBW,cAAc,CAAC,CAAC,KAAI,GAAG,EAAE,CAAC;UACnF;QAEF,KAAK,QAAQ;UACXlB,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,KAAI,SAAS,EAAE,CAAC;UAC/CO,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,GAAG,KAAI,SAAS,EAAE,CAAC;UACnDnB,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,KAAK,KAAI,SAAS,EAAE,CAAC;UACvDpB,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAA7B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkD,KAAK,KAAI,CAAC,EAAE,CAAC;UACxD;QAEF,KAAK,IAAI;UACPrB,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC;UACjCA,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC;UACjCA,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC;UAClCA,OAAO,CAAC,QAAQ,EAAE,mBAAmB,CAAC;UACtCA,OAAO,CAAC,QAAQ,EAAE,sBAAsB,CAAC;UACzC,IAAItB,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE4C,KAAK,EAAE;YAC3BtB,OAAO,CAAC,QAAQ,EAAE,wBAAwB,CAAC;UAC7C;UACA;QAEF,KAAK,YAAY;UACf,IAAIW,IAAI,CAACY,MAAM,GAAG,CAAC,EAAE;YACnBvB,OAAO,CAAC,OAAO,EAAE,0BAA0B,CAAC;YAC5C;UACF;UAEA,MAAMwB,MAAM,GAAGb,IAAI,CAAC,CAAC,CAAC;UACtBX,OAAO,CAAC,QAAQ,EAAE,0CAA0CwB,MAAM,KAAK,CAAC;UAExE,IAAI;YACF,MAAM9B,QAAQ,GAAG,MAAM/B,OAAO,CAAC8D,iBAAiB,CAACD,MAAM,EAAE,SAAS,CAAC;YAEnE,IAAI9B,QAAQ,CAACE,OAAO,EAAE;cACpBI,OAAO,CAAC,QAAQ,EAAE,oCAAoC,CAAC;cACvDA,OAAO,CAAC,QAAQ,EAAE,WAAWwB,MAAM,EAAE,CAAC;cACtCxB,OAAO,CAAC,QAAQ,EAAE,uBAAuB,CAAC;cAC1CA,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAA7B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuD,UAAU,KAAI,CAAC,EAAE,CAAC;;cAE/D;cACAC,UAAU,CAAC,MAAM;gBACf3B,OAAO,CAAC,QAAQ,EAAE,oBAAoB,CAAC;cACzC,CAAC,EAAE,IAAI,CAAC;cAER2B,UAAU,CAAC,MAAM;gBACf3B,OAAO,CAAC,QAAQ,EAAEN,QAAQ,CAACkC,QAAQ,IAAI,kBAAkB,CAAC;cAC5D,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL5B,OAAO,CAAC,OAAO,EAAEN,QAAQ,CAACkC,QAAQ,IAAI,qBAAqB,CAAC;YAC9D;UACF,CAAC,CAAC,OAAO9B,KAAU,EAAE;YACnBE,OAAO,CAAC,OAAO,EAAE,SAASF,KAAK,CAAC+B,OAAO,EAAE,CAAC;UAC5C;UACA;QAEF,KAAK,MAAM;UACT7B,OAAO,CAAC,QAAQ,EAAE,0BAA0B,CAAC;UAE7C,IAAI;YACF,MAAMN,QAAQ,GAAG,MAAM/B,OAAO,CAACmE,WAAW,CAAC,CAAC;YAE5C,IAAIpC,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACqC,KAAK,EAAE;cACtC/B,OAAO,CAAC,QAAQ,EAAE,eAAeN,QAAQ,CAACqC,KAAK,CAACR,MAAM,SAAS,CAAC;cAChE7B,QAAQ,CAACqC,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAEC,IAAS,IAAK;gBAChDlC,OAAO,CAAC,QAAQ,EAAE,KAAKkC,IAAI,CAACrB,EAAE,MAAMqB,IAAI,CAACzC,IAAI,WAAWyC,IAAI,CAACb,KAAK,GAAG,CAAC;cACxE,CAAC,CAAC;YACJ,CAAC,MAAM;cACLrB,OAAO,CAAC,QAAQ,EAAE,wBAAwB,CAAC;YAC7C;UACF,CAAC,CAAC,OAAOF,KAAU,EAAE;YACnBE,OAAO,CAAC,OAAO,EAAE,iBAAiBF,KAAK,CAAC+B,OAAO,EAAE,CAAC;UACpD;UACA;QAEF,KAAK,MAAM;UACT7B,OAAO,CAAC,QAAQ,EAAE,uBAAuB,CAAC;UAC1C2B,UAAU,CAAC,MAAM;YACfQ,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC;UACvB,CAAC,EAAE,IAAI,CAAC;UACR;QAEF;UACE,IAAI7B,GAAG,CAACC,IAAI,CAAC,CAAC,EAAE;YACdT,OAAO,CAAC,OAAO,EAAE,2BAA2BW,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YACtDX,OAAO,CAAC,QAAQ,EAAE,6CAA6C,CAAC;UAClE;UACA;MACJ;IACF,CAAC,CAAC,OAAOF,KAAU,EAAE;MACnBE,OAAO,CAAC,OAAO,EAAE,SAASF,KAAK,CAAC+B,OAAO,EAAE,CAAC;IAC5C,CAAC,SAAS;MACRpD,cAAc,CAAC,KAAK,CAAC;MACrBuB,OAAO,CAAC,QAAQ,EAAE,gBAAgB,CAAC;IACrC;EACF,CAAC;EAED,MAAMsC,YAAY,GAAIC,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAIlE,YAAY,CAACmC,IAAI,CAAC,CAAC,IAAI,CAACjC,WAAW,EAAE;MACvC6B,cAAc,CAAC/B,YAAY,CAAC;MAC5BC,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC;EAED,MAAMkE,YAAY,GAAIpD,IAA0B,IAAK;IACnD,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,QAAQ;QACX,OAAO,gBAAgB;MACzB,KAAK,OAAO;QACV,OAAO,cAAc;MACvB,KAAK,QAAQ;QACX,OAAO,eAAe;MACxB;QACE,OAAO,eAAe;IAC1B;EACF,CAAC;EAED,IAAI,CAACnB,eAAe,EAAE;IACpB,oBACEJ,OAAA;MAAK4E,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/E7E,OAAA;QAAK4E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7E,OAAA;UAAI4E,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DjF,OAAA;UAAG4E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEjF,OAAA;IAAK4E,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBAEvE7E,OAAA;MAAK4E,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtE7E,OAAA;QAAK4E,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C7E,OAAA;UACEkF,OAAO,EAAEA,CAAA,KAAMb,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCK,SAAS,EAAC,+FAA+F;UAAAC,QAAA,eAEzG7E,OAAA;YAAM4E,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACTjF,OAAA;UAAK4E,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C7E,OAAA;YAAM4E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCjF,OAAA;YAAM4E,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CjF,OAAA;YAAM4E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,IAAE,EAAC1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,EAAC,GAAC,EAACxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,EAAE;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA;MACEmF,GAAG,EAAErE,WAAY;MACjB8D,SAAS,EAAC,qCAAqC;MAAAC,QAAA,eAE/C7E,OAAA;QAAK4E,SAAS,EAAC,WAAW;QAAAC,QAAA,GACvBvE,aAAa,CAAC8E,GAAG,CAAEC,IAAI,iBACtBrF,OAAA;UAEE4E,SAAS,EAAE,GAAGD,YAAY,CAACU,IAAI,CAAC9D,IAAI,CAAC,0BAA2B;UAAAsD,QAAA,EAE/DQ,IAAI,CAAC7D;QAAO,GAHR6D,IAAI,CAAC/D,EAAE;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIT,CACN,CAAC,eAGFjF,OAAA;UAAMsF,QAAQ,EAAEd,YAAa;UAACI,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACzD7E,OAAA;YAAM4E,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1DjF,OAAA;YACEmF,GAAG,EAAEpE,QAAS;YACdQ,IAAI,EAAC,MAAM;YACXgE,KAAK,EAAE/E,YAAa;YACpBgF,QAAQ,EAAGf,CAAC,IAAKhE,eAAe,CAACgE,CAAC,CAACf,MAAM,CAAC6B,KAAK,CAAE;YACjDX,SAAS,EAAC,mDAAmD;YAC7Da,QAAQ,EAAE/E,WAAY;YACtBgF,SAAS;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EACDvE,WAAW,iBACVV,OAAA;YAAM4E,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC7D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA;MAAK4E,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtE7E,OAAA;QAAK4E,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxD7E,OAAA;UAAK4E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7E,OAAA;YAAA6E,QAAA,GAAM,OAAK,EAAC,CAAAxE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2C,GAAG,KAAI,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CjF,OAAA;YAAA6E,QAAA,GAAM,OAAK,EAAC,CAAAxE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4C,GAAG,KAAI,CAAC;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CjF,OAAA;YAAA6E,QAAA,GAAM,cAAY,EAAC,CAAAxE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuD,UAAU,KAAI,CAAC;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNjF,OAAA;UAAK4E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAC5B,CAAAjE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE4C,KAAK,kBACtBxD,OAAA;YAAM4E,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC5D,eACDjF,OAAA;YAAA6E,QAAA,EAAO,IAAInD,IAAI,CAAC,CAAC,CAACiE,kBAAkB,CAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA,CAACF,UAAU;MAAC8F,WAAW,EAAC;IAAU;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CAAC;AAEV,CAAC;AAAC/E,EAAA,CA9TID,YAAsB;EAAA,QACQN,OAAO,EACfC,SAAS;AAAA;AAAAiG,EAAA,GAF/B5F,YAAsB;AAgU5B,eAAeA,YAAY;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}