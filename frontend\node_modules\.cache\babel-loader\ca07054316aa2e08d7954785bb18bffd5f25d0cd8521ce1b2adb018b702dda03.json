{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatSystem.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useChat } from '../../stores/chatStore';\nimport ChatMessage from './ChatMessage';\nimport LoadingSpinner from '../common/LoadingSpinner';\nimport { ChatBubbleLeftRightIcon, XMarkIcon, MinusIcon, PlusIcon, PaperAirplaneIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ChatSystem = ({\n  isOpen,\n  onToggle\n}) => {\n  _s();\n  const {\n    messages,\n    isLoading,\n    error,\n    isSending,\n    sendError,\n    unreadCount,\n    hasUnread,\n    canSend,\n    isMinimized,\n    loadMessages,\n    sendMessage,\n    closeChat,\n    toggleChat,\n    minimizeChat,\n    maximizeChat,\n    markAsRead,\n    clearError,\n    formatDate\n  } = useChat();\n  const [messageInput, setMessageInput] = useState('');\n  const [lastMessageDate, setLastMessageDate] = useState(null);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n\n  // Auto-scroll para a última mensagem\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    if (messages.length > 0) {\n      scrollToBottom();\n    }\n  }, [messages]);\n\n  // Focar no input quando o chat abrir\n  useEffect(() => {\n    if (isOpen && !isMinimized && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isOpen, isMinimized]);\n\n  // Marcar como lido quando abrir\n  useEffect(() => {\n    if (isOpen && hasUnread) {\n      markAsRead();\n    }\n  }, [isOpen, hasUnread, markAsRead]);\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    if (!messageInput.trim() || !canSend) return;\n    const message = messageInput.trim();\n    setMessageInput('');\n    try {\n      await sendMessage(message);\n    } catch (error) {\n      // Erro já está sendo tratado no store\n      console.error('Erro ao enviar mensagem:', error);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage(e);\n    }\n  };\n\n  // Renderizar separador de data\n  const renderDateSeparator = date => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center py-2\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-bg-tertiary px-3 py-1 rounded-full\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-xs text-text-muted font-medium\",\n        children: formatDate(date)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n\n  // Chat minimizado (apenas ícone)\n  if (!isOpen) {\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onToggle || toggleChat,\n      className: `\n          fixed bottom-4 right-4 z-50 p-4 rounded-full shadow-lg transition-all duration-300\n          ${hasUnread ? 'bg-accent animate-pulse' : 'bg-primary hover:bg-primary-dark'}\n          text-white\n        `,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftRightIcon, {\n          className: \"h-6 w-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), hasUnread && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold\",\n          children: unreadCount > 99 ? '99+' : unreadCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      fixed bottom-4 right-4 z-50 bg-bg-secondary border border-border-color rounded-lg shadow-xl\n      transition-all duration-300 transform\n      ${isMinimized ? 'w-80 h-12' : 'w-80 h-96 md:w-96 md:h-[500px]'}\n    `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between p-3 border-b border-border-color bg-bg-tertiary rounded-t-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftRightIcon, {\n          className: \"h-5 w-5 text-accent-blue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-text-primary\",\n          children: \"Chat Global\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), hasUnread && !isMinimized && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-bold\",\n          children: unreadCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: isMinimized ? maximizeChat : minimizeChat,\n          className: \"p-1 rounded hover:bg-bg-secondary transition-colors\",\n          children: isMinimized ? /*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"h-4 w-4 text-text-muted\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(MinusIcon, {\n            className: \"h-4 w-4 text-text-muted\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onToggle || closeChat,\n          className: \"p-1 rounded hover:bg-bg-secondary transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n            className: \"h-4 w-4 text-text-muted\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), !isMinimized && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto h-80 md:h-96\",\n        children: isLoading && messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-full\",\n          children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n            size: \"md\",\n            text: \"Carregando mensagens...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 15\n        }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-full p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n              className: \"h-8 w-8 text-red-400 mx-auto mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-400 mb-2\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                clearError();\n                loadMessages();\n              },\n              className: \"text-xs text-accent-blue hover:text-primary-light\",\n              children: \"Tentar novamente\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 15\n        }, this) : messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-text-muted\",\n            children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftRightIcon, {\n              className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: \"Nenhuma mensagem ainda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs\",\n              children: \"Seja o primeiro a conversar!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-1\",\n          children: [messages.map((message, index) => {\n            const messageDate = message.timestamp.split('T')[0];\n            const showDateSeparator = messageDate !== lastMessageDate;\n            if (showDateSeparator) {\n              setLastMessageDate(messageDate);\n            }\n            return /*#__PURE__*/_jsxDEV(React.Fragment, {\n              children: [showDateSeparator && renderDateSeparator(message.timestamp), /*#__PURE__*/_jsxDEV(ChatMessage, {\n                message: message,\n                showAvatar: true,\n                showTimestamp: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 23\n              }, this)]\n            }, message.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 21\n            }, this);\n          }), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 border-t border-border-color\",\n        children: [sendError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-2 p-2 bg-red-900 border border-red-500 rounded text-xs text-red-100\",\n          children: sendError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSendMessage,\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            ref: inputRef,\n            type: \"text\",\n            value: messageInput,\n            onChange: e => setMessageInput(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Digite sua mensagem...\",\n            disabled: !canSend,\n            className: \" flex-1 px-3 py-2 text-sm rounded-lg bg-bg-primary text-text-primary border border-border-color focus:border-accent-blue focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed \",\n            maxLength: 500\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: !canSend || !messageInput.trim(),\n            className: \" p-2 rounded-lg bg-accent-blue hover:bg-primary-dark text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center \",\n            children: isSending ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"sm\",\n              color: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mt-2 text-xs text-text-muted\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [messageInput.length, \"/500\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Enter para enviar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatSystem, \"mqcTRVkxfjAYZcXyNH/JnwHA2cc=\", false, function () {\n  return [useChat];\n});\n_c = ChatSystem;\nexport default ChatSystem;\nvar _c;\n$RefreshReg$(_c, \"ChatSystem\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useChat", "ChatMessage", "LoadingSpinner", "ChatBubbleLeftRightIcon", "XMarkIcon", "MinusIcon", "PlusIcon", "PaperAirplaneIcon", "ExclamationTriangleIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ChatSystem", "isOpen", "onToggle", "_s", "messages", "isLoading", "error", "isSending", "sendError", "unreadCount", "hasUnread", "canSend", "isMinimized", "loadMessages", "sendMessage", "closeChat", "toggleChat", "minimizeChat", "maximizeChat", "mark<PERSON><PERSON><PERSON>", "clearError", "formatDate", "messageInput", "setMessageInput", "lastMessageDate", "setLastMessageDate", "messagesEndRef", "inputRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "length", "focus", "handleSendMessage", "e", "preventDefault", "trim", "message", "console", "handleKeyPress", "key", "shift<PERSON>ey", "renderDateSeparator", "date", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "size", "text", "map", "index", "messageDate", "timestamp", "split", "showDateSeparator", "showAvatar", "showTimestamp", "id", "ref", "onSubmit", "type", "value", "onChange", "target", "onKeyPress", "placeholder", "disabled", "max<PERSON><PERSON><PERSON>", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/chat/ChatSystem.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useChat } from '../../stores/chatStore';\nimport ChatMessage from './ChatMessage';\nimport LoadingSpinner from '../common/LoadingSpinner';\nimport {\n  ChatBubbleLeftRightIcon,\n  XMarkIcon,\n  MinusIcon,\n  PlusIcon,\n  PaperAirplaneIcon,\n  ExclamationTriangleIcon\n} from '@heroicons/react/24/outline';\n\ninterface ChatSystemProps {\n  isOpen?: boolean;\n  onToggle?: () => void;\n}\n\nconst ChatSystem: React.FC<ChatSystemProps> = ({ isOpen, onToggle }) => {\n  const {\n    messages,\n    isLoading,\n    error,\n    isSending,\n    sendError,\n    unreadCount,\n    hasUnread,\n    canSend,\n    isMinimized,\n    loadMessages,\n    sendMessage,\n    closeChat,\n    toggleChat,\n    minimizeChat,\n    maximizeChat,\n    markAsRead,\n    clearError,\n    formatDate,\n  } = useChat();\n\n  const [messageInput, setMessageInput] = useState('');\n  const [lastMessageDate, setLastMessageDate] = useState<string | null>(null);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // Auto-scroll para a última mensagem\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    if (messages.length > 0) {\n      scrollToBottom();\n    }\n  }, [messages]);\n\n  // Focar no input quando o chat abrir\n  useEffect(() => {\n    if (isOpen && !isMinimized && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isOpen, isMinimized]);\n\n  // Marcar como lido quando abrir\n  useEffect(() => {\n    if (isOpen && hasUnread) {\n      markAsRead();\n    }\n  }, [isOpen, hasUnread, markAsRead]);\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!messageInput.trim() || !canSend) return;\n\n    const message = messageInput.trim();\n    setMessageInput('');\n\n    try {\n      await sendMessage(message);\n    } catch (error) {\n      // Erro já está sendo tratado no store\n      console.error('Erro ao enviar mensagem:', error);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage(e as any);\n    }\n  };\n\n  // Renderizar separador de data\n  const renderDateSeparator = (date: string) => (\n    <div className=\"flex items-center justify-center py-2\">\n      <div className=\"bg-bg-tertiary px-3 py-1 rounded-full\">\n        <span className=\"text-xs text-text-muted font-medium\">\n          {formatDate(date)}\n        </span>\n      </div>\n    </div>\n  );\n\n  // Chat minimizado (apenas ícone)\n  if (!isOpen) {\n    return (\n      <button\n        onClick={onToggle || toggleChat}\n        className={`\n          fixed bottom-4 right-4 z-50 p-4 rounded-full shadow-lg transition-all duration-300\n          ${hasUnread ? 'bg-accent animate-pulse' : 'bg-primary hover:bg-primary-dark'}\n          text-white\n        `}\n      >\n        <div className=\"relative\">\n          <ChatBubbleLeftRightIcon className=\"h-6 w-6\" />\n          {hasUnread && (\n            <div className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold\">\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </div>\n          )}\n        </div>\n      </button>\n    );\n  }\n\n  return (\n    <div className={`\n      fixed bottom-4 right-4 z-50 bg-bg-secondary border border-border-color rounded-lg shadow-xl\n      transition-all duration-300 transform\n      ${isMinimized ? 'w-80 h-12' : 'w-80 h-96 md:w-96 md:h-[500px]'}\n    `}>\n      {/* Header do chat */}\n      <div className=\"flex items-center justify-between p-3 border-b border-border-color bg-bg-tertiary rounded-t-lg\">\n        <div className=\"flex items-center space-x-2\">\n          <ChatBubbleLeftRightIcon className=\"h-5 w-5 text-accent-blue\" />\n          <span className=\"font-medium text-text-primary\">Chat Global</span>\n          {hasUnread && !isMinimized && (\n            <div className=\"bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-bold\">\n              {unreadCount}\n            </div>\n          )}\n        </div>\n\n        <div className=\"flex items-center space-x-1\">\n          <button\n            onClick={isMinimized ? maximizeChat : minimizeChat}\n            className=\"p-1 rounded hover:bg-bg-secondary transition-colors\"\n          >\n            {isMinimized ? (\n              <PlusIcon className=\"h-4 w-4 text-text-muted\" />\n            ) : (\n              <MinusIcon className=\"h-4 w-4 text-text-muted\" />\n            )}\n          </button>\n          \n          <button\n            onClick={onToggle || closeChat}\n            className=\"p-1 rounded hover:bg-bg-secondary transition-colors\"\n          >\n            <XMarkIcon className=\"h-4 w-4 text-text-muted\" />\n          </button>\n        </div>\n      </div>\n\n      {/* Conteúdo do chat (oculto quando minimizado) */}\n      {!isMinimized && (\n        <>\n          {/* Área de mensagens */}\n          <div className=\"flex-1 overflow-y-auto h-80 md:h-96\">\n            {isLoading && messages.length === 0 ? (\n              <div className=\"flex items-center justify-center h-full\">\n                <LoadingSpinner size=\"md\" text=\"Carregando mensagens...\" />\n              </div>\n            ) : error ? (\n              <div className=\"flex items-center justify-center h-full p-4\">\n                <div className=\"text-center\">\n                  <ExclamationTriangleIcon className=\"h-8 w-8 text-red-400 mx-auto mb-2\" />\n                  <p className=\"text-sm text-red-400 mb-2\">{error}</p>\n                  <button\n                    onClick={() => {\n                      clearError();\n                      loadMessages();\n                    }}\n                    className=\"text-xs text-accent-blue hover:text-primary-light\"\n                  >\n                    Tentar novamente\n                  </button>\n                </div>\n              </div>\n            ) : messages.length === 0 ? (\n              <div className=\"flex items-center justify-center h-full\">\n                <div className=\"text-center text-text-muted\">\n                  <ChatBubbleLeftRightIcon className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n                  <p className=\"text-sm\">Nenhuma mensagem ainda</p>\n                  <p className=\"text-xs\">Seja o primeiro a conversar!</p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-1\">\n                {messages.map((message, index) => {\n                  const messageDate = message.timestamp.split('T')[0];\n                  const showDateSeparator = messageDate !== lastMessageDate;\n                  \n                  if (showDateSeparator) {\n                    setLastMessageDate(messageDate);\n                  }\n\n                  return (\n                    <React.Fragment key={message.id}>\n                      {showDateSeparator && renderDateSeparator(message.timestamp)}\n                      <ChatMessage\n                        message={message}\n                        showAvatar={true}\n                        showTimestamp={true}\n                      />\n                    </React.Fragment>\n                  );\n                })}\n                <div ref={messagesEndRef} />\n              </div>\n            )}\n          </div>\n\n          {/* Área de input */}\n          <div className=\"p-3 border-t border-border-color\">\n            {sendError && (\n              <div className=\"mb-2 p-2 bg-red-900 border border-red-500 rounded text-xs text-red-100\">\n                {sendError}\n              </div>\n            )}\n            \n            <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n              <input\n                ref={inputRef}\n                type=\"text\"\n                value={messageInput}\n                onChange={(e) => setMessageInput(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Digite sua mensagem...\"\n                disabled={!canSend}\n                className=\"\n                  flex-1 px-3 py-2 text-sm rounded-lg\n                  bg-bg-primary text-text-primary\n                  border border-border-color\n                  focus:border-accent-blue focus:outline-none\n                  disabled:opacity-50 disabled:cursor-not-allowed\n                \"\n                maxLength={500}\n              />\n              \n              <button\n                type=\"submit\"\n                disabled={!canSend || !messageInput.trim()}\n                className=\"\n                  p-2 rounded-lg bg-accent-blue hover:bg-primary-dark\n                  text-white transition-colors\n                  disabled:opacity-50 disabled:cursor-not-allowed\n                  flex items-center justify-center\n                \"\n              >\n                {isSending ? (\n                  <LoadingSpinner size=\"sm\" color=\"white\" />\n                ) : (\n                  <PaperAirplaneIcon className=\"h-4 w-4\" />\n                )}\n              </button>\n            </form>\n            \n            <div className=\"flex items-center justify-between mt-2 text-xs text-text-muted\">\n              <span>{messageInput.length}/500</span>\n              <span>Enter para enviar</span>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default ChatSystem;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,0BAA0B;AACrD,SACEC,uBAAuB,EACvBC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,iBAAiB,EACjBC,uBAAuB,QAClB,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOrC,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IACJC,QAAQ;IACRC,SAAS;IACTC,KAAK;IACLC,SAAS;IACTC,SAAS;IACTC,WAAW;IACXC,SAAS;IACTC,OAAO;IACPC,WAAW;IACXC,YAAY;IACZC,WAAW;IACXC,SAAS;IACTC,UAAU;IACVC,YAAY;IACZC,YAAY;IACZC,UAAU;IACVC,UAAU;IACVC;EACF,CAAC,GAAGlC,OAAO,CAAC,CAAC;EAEb,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM0C,cAAc,GAAGzC,MAAM,CAAiB,IAAI,CAAC;EACnD,MAAM0C,QAAQ,GAAG1C,MAAM,CAAmB,IAAI,CAAC;;EAE/C;EACA,MAAM2C,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAH,cAAc,CAACI,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED9C,SAAS,CAAC,MAAM;IACd,IAAIkB,QAAQ,CAAC6B,MAAM,GAAG,CAAC,EAAE;MACvBL,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACxB,QAAQ,CAAC,CAAC;;EAEd;EACAlB,SAAS,CAAC,MAAM;IACd,IAAIe,MAAM,IAAI,CAACW,WAAW,IAAIe,QAAQ,CAACG,OAAO,EAAE;MAC9CH,QAAQ,CAACG,OAAO,CAACI,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACjC,MAAM,EAAEW,WAAW,CAAC,CAAC;;EAEzB;EACA1B,SAAS,CAAC,MAAM;IACd,IAAIe,MAAM,IAAIS,SAAS,EAAE;MACvBS,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAClB,MAAM,EAAES,SAAS,EAAES,UAAU,CAAC,CAAC;EAEnC,MAAMgB,iBAAiB,GAAG,MAAOC,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACf,YAAY,CAACgB,IAAI,CAAC,CAAC,IAAI,CAAC3B,OAAO,EAAE;IAEtC,MAAM4B,OAAO,GAAGjB,YAAY,CAACgB,IAAI,CAAC,CAAC;IACnCf,eAAe,CAAC,EAAE,CAAC;IAEnB,IAAI;MACF,MAAMT,WAAW,CAACyB,OAAO,CAAC;IAC5B,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd;MACAkC,OAAO,CAAClC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMmC,cAAc,GAAIL,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACM,GAAG,KAAK,OAAO,IAAI,CAACN,CAAC,CAACO,QAAQ,EAAE;MACpCP,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBF,iBAAiB,CAACC,CAAQ,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMQ,mBAAmB,GAAIC,IAAY,iBACvChD,OAAA;IAAKiD,SAAS,EAAC,uCAAuC;IAAAC,QAAA,eACpDlD,OAAA;MAAKiD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDlD,OAAA;QAAMiD,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAClD1B,UAAU,CAACwB,IAAI;MAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,IAAI,CAAClD,MAAM,EAAE;IACX,oBACEJ,OAAA;MACEuD,OAAO,EAAElD,QAAQ,IAAIc,UAAW;MAChC8B,SAAS,EAAE;AACnB;AACA,YAAYpC,SAAS,GAAG,yBAAyB,GAAG,kCAAkC;AACtF;AACA,SAAU;MAAAqC,QAAA,eAEFlD,OAAA;QAAKiD,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBlD,OAAA,CAACP,uBAAuB;UAACwD,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC9CzC,SAAS,iBACRb,OAAA;UAAKiD,SAAS,EAAC,wHAAwH;UAAAC,QAAA,EACpItC,WAAW,GAAG,EAAE,GAAG,KAAK,GAAGA;QAAW;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACEtD,OAAA;IAAKiD,SAAS,EAAE;AACpB;AACA;AACA,QAAQlC,WAAW,GAAG,WAAW,GAAG,gCAAgC;AACpE,KAAM;IAAAmC,QAAA,gBAEAlD,OAAA;MAAKiD,SAAS,EAAC,gGAAgG;MAAAC,QAAA,gBAC7GlD,OAAA;QAAKiD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClD,OAAA,CAACP,uBAAuB;UAACwD,SAAS,EAAC;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEtD,OAAA;UAAMiD,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACjEzC,SAAS,IAAI,CAACE,WAAW,iBACxBf,OAAA;UAAKiD,SAAS,EAAC,+FAA+F;UAAAC,QAAA,EAC3GtC;QAAW;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtD,OAAA;QAAKiD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClD,OAAA;UACEuD,OAAO,EAAExC,WAAW,GAAGM,YAAY,GAAGD,YAAa;UACnD6B,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAE9DnC,WAAW,gBACVf,OAAA,CAACJ,QAAQ;YAACqD,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhDtD,OAAA,CAACL,SAAS;YAACsD,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACjD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAETtD,OAAA;UACEuD,OAAO,EAAElD,QAAQ,IAAIa,SAAU;UAC/B+B,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAE/DlD,OAAA,CAACN,SAAS;YAACuD,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAACvC,WAAW,iBACXf,OAAA,CAAAE,SAAA;MAAAgD,QAAA,gBAEElD,OAAA;QAAKiD,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EACjD1C,SAAS,IAAID,QAAQ,CAAC6B,MAAM,KAAK,CAAC,gBACjCpC,OAAA;UAAKiD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDlD,OAAA,CAACR,cAAc;YAACgE,IAAI,EAAC,IAAI;YAACC,IAAI,EAAC;UAAyB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,GACJ7C,KAAK,gBACPT,OAAA;UAAKiD,SAAS,EAAC,6CAA6C;UAAAC,QAAA,eAC1DlD,OAAA;YAAKiD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlD,OAAA,CAACF,uBAAuB;cAACmD,SAAS,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzEtD,OAAA;cAAGiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEzC;YAAK;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDtD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM;gBACbhC,UAAU,CAAC,CAAC;gBACZP,YAAY,CAAC,CAAC;cAChB,CAAE;cACFiC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9D;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJ/C,QAAQ,CAAC6B,MAAM,KAAK,CAAC,gBACvBpC,OAAA;UAAKiD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDlD,OAAA;YAAKiD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ClD,OAAA,CAACP,uBAAuB;cAACwD,SAAS,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvEtD,OAAA;cAAGiD,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjDtD,OAAA;cAAGiD,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENtD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAAAC,QAAA,GACvB3C,QAAQ,CAACmD,GAAG,CAAC,CAAChB,OAAO,EAAEiB,KAAK,KAAK;YAChC,MAAMC,WAAW,GAAGlB,OAAO,CAACmB,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACnD,MAAMC,iBAAiB,GAAGH,WAAW,KAAKjC,eAAe;YAEzD,IAAIoC,iBAAiB,EAAE;cACrBnC,kBAAkB,CAACgC,WAAW,CAAC;YACjC;YAEA,oBACE5D,OAAA,CAACd,KAAK,CAACe,QAAQ;cAAAiD,QAAA,GACZa,iBAAiB,IAAIhB,mBAAmB,CAACL,OAAO,CAACmB,SAAS,CAAC,eAC5D7D,OAAA,CAACT,WAAW;gBACVmD,OAAO,EAAEA,OAAQ;gBACjBsB,UAAU,EAAE,IAAK;gBACjBC,aAAa,EAAE;cAAK;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA,GANiBZ,OAAO,CAACwB,EAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOf,CAAC;UAErB,CAAC,CAAC,eACFtD,OAAA;YAAKmE,GAAG,EAAEtC;UAAe;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtD,OAAA;QAAKiD,SAAS,EAAC,kCAAkC;QAAAC,QAAA,GAC9CvC,SAAS,iBACRX,OAAA;UAAKiD,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EACpFvC;QAAS;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,eAEDtD,OAAA;UAAMoE,QAAQ,EAAE9B,iBAAkB;UAACW,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3DlD,OAAA;YACEmE,GAAG,EAAErC,QAAS;YACduC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE7C,YAAa;YACpB8C,QAAQ,EAAGhC,CAAC,IAAKb,eAAe,CAACa,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;YACjDG,UAAU,EAAE7B,cAAe;YAC3B8B,WAAW,EAAC,wBAAwB;YACpCC,QAAQ,EAAE,CAAC7D,OAAQ;YACnBmC,SAAS,EAAC,8LAMT;YACD2B,SAAS,EAAE;UAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEFtD,OAAA;YACEqE,IAAI,EAAC,QAAQ;YACbM,QAAQ,EAAE,CAAC7D,OAAO,IAAI,CAACW,YAAY,CAACgB,IAAI,CAAC,CAAE;YAC3CQ,SAAS,EAAC,qKAKT;YAAAC,QAAA,EAEAxC,SAAS,gBACRV,OAAA,CAACR,cAAc;cAACgE,IAAI,EAAC,IAAI;cAACqB,KAAK,EAAC;YAAO;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE1CtD,OAAA,CAACH,iBAAiB;cAACoD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACzC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEPtD,OAAA;UAAKiD,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7ElD,OAAA;YAAAkD,QAAA,GAAOzB,YAAY,CAACW,MAAM,EAAC,MAAI;UAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtCtD,OAAA;YAAAkD,QAAA,EAAM;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChD,EAAA,CArQIH,UAAqC;EAAA,QAoBrCb,OAAO;AAAA;AAAAwF,EAAA,GApBP3E,UAAqC;AAuQ3C,eAAeA,UAAU;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}