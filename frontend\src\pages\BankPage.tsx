import React, { useState, useEffect } from 'react';
import { useAuth } from '../stores/authStore';
import { usePlayer } from '../stores/playerStore';
import gameApi from '../services/gameApi';

interface BankAccount {
  saldo: number;
  juros_diarios: number;
  ultimo_juros: string;
  nivel_conta: number;
  limite_deposito: number;
  limite_saque: number;
}

interface Transaction {
  id: string;
  tipo: 'deposito' | 'saque' | 'juros' | 'transferencia';
  valor: number;
  descricao: string;
  timestamp: string;
  saldo_anterior: number;
  saldo_posterior: number;
}

const BankPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { currentPlayer, loadPlayerData } = usePlayer();
  
  const [bankAccount, setBankAccount] = useState<BankAccount | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [selectedTab, setSelectedTab] = useState<'deposito' | 'saque' | 'historico'>('deposito');
  
  // Estados para operações
  const [amount, setAmount] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [actionError, setActionError] = useState<string | null>(null);
  const [actionSuccess, setActionSuccess] = useState<string | null>(null);
  
  // Estados para carregamento
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      loadBankData();
      loadTransactions();
    }
  }, [isAuthenticated]);

  const loadBankData = async () => {
    setIsLoading(true);
    try {
      const response = await gameApi.getBankAccount();
      if (response.sucesso) {
        setBankAccount(response.conta);
      } else {
        // Fallback para dados mockados
        loadMockBankData();
      }
    } catch (error) {
      console.error('Erro ao carregar dados bancários:', error);
      loadMockBankData();
    } finally {
      setIsLoading(false);
    }
  };

  const loadMockBankData = () => {
    if (!currentPlayer) return;

    const mockAccount: BankAccount = {
      saldo: currentPlayer.banco_saldo || 0,
      juros_diarios: 0.05, // 5% ao dia
      ultimo_juros: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12h atrás
      nivel_conta: currentPlayer.banco_nivel || 1,
      limite_deposito: 100000 * (currentPlayer.banco_nivel || 1),
      limite_saque: 50000 * (currentPlayer.banco_nivel || 1),
    };

    setBankAccount(mockAccount);
  };

  const loadTransactions = async () => {
    try {
      const response = await gameApi.getBankTransactions();
      if (response.sucesso) {
        setTransactions(response.transacoes || []);
      } else {
        // Fallback para transações mockadas
        loadMockTransactions();
      }
    } catch (error) {
      console.error('Erro ao carregar transações:', error);
      loadMockTransactions();
    }
  };

  const loadMockTransactions = () => {
    const now = new Date();
    const mockTransactions: Transaction[] = [
      {
        id: '1',
        tipo: 'deposito',
        valor: 10000,
        descricao: 'Depósito via terminal',
        timestamp: new Date(now.getTime() - 60 * 60000).toISOString(),
        saldo_anterior: 5000,
        saldo_posterior: 15000,
      },
      {
        id: '2',
        tipo: 'juros',
        valor: 750,
        descricao: 'Juros diários (5%)',
        timestamp: new Date(now.getTime() - 24 * 60 * 60000).toISOString(),
        saldo_anterior: 15000,
        saldo_posterior: 15750,
      },
      {
        id: '3',
        tipo: 'saque',
        valor: 5000,
        descricao: 'Saque para carteira',
        timestamp: new Date(now.getTime() - 48 * 60 * 60000).toISOString(),
        saldo_anterior: 15750,
        saldo_posterior: 10750,
      },
    ];

    setTransactions(mockTransactions);
  };

  const handleDeposit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount.trim() || !bankAccount || !currentPlayer) {
      setActionError('Valor inválido');
      return;
    }

    const depositAmount = parseInt(amount);
    if (isNaN(depositAmount) || depositAmount <= 0) {
      setActionError('Valor deve ser um número positivo');
      return;
    }

    if (depositAmount > currentPlayer.dinheiro) {
      setActionError('Saldo insuficiente na carteira');
      return;
    }

    if (depositAmount > bankAccount.limite_deposito) {
      setActionError(`Limite de depósito: $${bankAccount.limite_deposito.toLocaleString()}`);
      return;
    }

    setIsProcessing(true);
    setActionError(null);
    setActionSuccess(null);

    try {
      const response = await gameApi.bankDeposit(depositAmount);
      
      if (response.sucesso) {
        setActionSuccess(`Depósito de $${depositAmount.toLocaleString()} realizado com sucesso!`);
        setAmount('');
        
        // Recarregar dados
        await loadPlayerData();
        await loadBankData();
        await loadTransactions();
      } else {
        setActionError(response.mensagem || 'Erro no depósito');
      }
    } catch (error: any) {
      setActionError(error.message || 'Erro de conexão');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleWithdraw = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount.trim() || !bankAccount) {
      setActionError('Valor inválido');
      return;
    }

    const withdrawAmount = parseInt(amount);
    if (isNaN(withdrawAmount) || withdrawAmount <= 0) {
      setActionError('Valor deve ser um número positivo');
      return;
    }

    if (withdrawAmount > bankAccount.saldo) {
      setActionError('Saldo insuficiente no banco');
      return;
    }

    if (withdrawAmount > bankAccount.limite_saque) {
      setActionError(`Limite de saque: $${bankAccount.limite_saque.toLocaleString()}`);
      return;
    }

    setIsProcessing(true);
    setActionError(null);
    setActionSuccess(null);

    try {
      const response = await gameApi.bankWithdraw(withdrawAmount);
      
      if (response.sucesso) {
        setActionSuccess(`Saque de $${withdrawAmount.toLocaleString()} realizado com sucesso!`);
        setAmount('');
        
        // Recarregar dados
        await loadPlayerData();
        await loadBankData();
        await loadTransactions();
      } else {
        setActionError(response.mensagem || 'Erro no saque');
      }
    } catch (error: any) {
      setActionError(error.message || 'Erro de conexão');
    } finally {
      setIsProcessing(false);
    }
  };

  const calculateNextInterest = () => {
    if (!bankAccount) return { amount: 0, timeLeft: '' };

    const lastInterest = new Date(bankAccount.ultimo_juros);
    const nextInterest = new Date(lastInterest.getTime() + 24 * 60 * 60 * 1000); // +24h
    const now = new Date();

    const amount = Math.floor(bankAccount.saldo * bankAccount.juros_diarios);
    
    if (now >= nextInterest) {
      return { amount, timeLeft: 'Disponível agora!' };
    }

    const diff = nextInterest.getTime() - now.getTime();
    const hours = Math.floor(diff / (60 * 60 * 1000));
    const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000));
    
    return { amount, timeLeft: `${hours}h ${minutes}m` };
  };

  const getTransactionIcon = (tipo: string) => {
    switch (tipo) {
      case 'deposito': return '📥';
      case 'saque': return '📤';
      case 'juros': return '💰';
      case 'transferencia': return '🔄';
      default: return '📝';
    }
  };

  const getTransactionColor = (tipo: string) => {
    switch (tipo) {
      case 'deposito': return 'text-green-400';
      case 'saque': return 'text-red-400';
      case 'juros': return 'text-yellow-400';
      case 'transferencia': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">🔒 Acesso Negado</h1>
          <p className="text-gray-400">Você precisa estar logado para acessar o banco</p>
        </div>
      </div>
    );
  }

  const nextInterest = calculateNextInterest();

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => window.history.back()}
            className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600"
          >
            <span className="text-lg">←</span>
          </button>
          <div>
            <h1 className="text-lg font-bold">🏦 Banco Shack</h1>
            <p className="text-xs text-gray-400">Sistema Bancário Seguro</p>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {/* Mensagens de erro/sucesso */}
        {actionError && (
          <div className="bg-red-900 border border-red-500 rounded-lg p-3">
            <p className="text-red-300 text-sm">❌ {actionError}</p>
          </div>
        )}

        {actionSuccess && (
          <div className="bg-green-900 border border-green-500 rounded-lg p-3">
            <p className="text-green-300 text-sm">✅ {actionSuccess}</p>
          </div>
        )}

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-gray-400">Carregando dados bancários...</p>
          </div>
        ) : bankAccount ? (
          <>
            {/* Saldo e informações da conta */}
            <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
              <h3 className="text-lg font-semibold mb-4 text-white">💳 Informações da Conta</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    ${bankAccount.saldo.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-400">Saldo no Banco</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">
                    Nível {bankAccount.nivel_conta}
                  </div>
                  <div className="text-xs text-gray-400">Conta</div>
                </div>
              </div>
              
              <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-gray-400">Carteira:</div>
                  <div className="text-white">${currentPlayer?.dinheiro?.toLocaleString() || '0'}</div>
                </div>
                <div>
                  <div className="text-gray-400">Juros Diários:</div>
                  <div className="text-yellow-400">{(bankAccount.juros_diarios * 100).toFixed(1)}%</div>
                </div>
              </div>
            </div>

            {/* Próximos juros */}
            <div className="bg-gray-800 rounded-lg p-4 border border-yellow-600">
              <h3 className="text-lg font-semibold mb-3 text-yellow-400">💰 Próximos Juros</h3>
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-lg font-bold text-yellow-400">
                    +${nextInterest.amount.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-400">Valor dos juros</div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-white">{nextInterest.timeLeft}</div>
                  <div className="text-xs text-gray-400">Tempo restante</div>
                </div>
              </div>
            </div>

            {/* Tabs */}
            <div className="bg-gray-800 rounded-lg border border-gray-600">
              <div className="flex border-b border-gray-700">
                {[
                  { id: 'deposito', name: 'Depósito', icon: '📥' },
                  { id: 'saque', name: 'Saque', icon: '📤' },
                  { id: 'historico', name: 'Histórico', icon: '📊' },
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setSelectedTab(tab.id as any)}
                    className={`flex-1 p-3 text-sm font-medium ${
                      selectedTab === tab.id
                        ? 'text-blue-400 border-b-2 border-blue-400'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    <span className="mr-1">{tab.icon}</span>
                    {tab.name}
                  </button>
                ))}
              </div>

              <div className="p-4">
                {selectedTab === 'deposito' && (
                  <form onSubmit={handleDeposit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Valor do Depósito
                      </label>
                      <input
                        type="number"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        placeholder="0"
                        min="1"
                        max={Math.min(currentPlayer?.dinheiro || 0, bankAccount.limite_deposito)}
                        className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                        disabled={isProcessing}
                      />
                      <div className="text-xs text-gray-400 mt-1">
                        Limite: ${bankAccount.limite_deposito.toLocaleString()}
                      </div>
                    </div>

                    <button
                      type="submit"
                      disabled={isProcessing || !amount.trim()}
                      className="w-full py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold"
                    >
                      {isProcessing ? 'Processando...' : 'Depositar'}
                    </button>
                  </form>
                )}

                {selectedTab === 'saque' && (
                  <form onSubmit={handleWithdraw} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Valor do Saque
                      </label>
                      <input
                        type="number"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        placeholder="0"
                        min="1"
                        max={Math.min(bankAccount.saldo, bankAccount.limite_saque)}
                        className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                        disabled={isProcessing}
                      />
                      <div className="text-xs text-gray-400 mt-1">
                        Limite: ${bankAccount.limite_saque.toLocaleString()}
                      </div>
                    </div>

                    <button
                      type="submit"
                      disabled={isProcessing || !amount.trim()}
                      className="w-full py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white rounded-lg font-semibold"
                    >
                      {isProcessing ? 'Processando...' : 'Sacar'}
                    </button>
                  </form>
                )}

                {selectedTab === 'historico' && (
                  <div className="space-y-3">
                    {transactions.length === 0 ? (
                      <div className="text-center py-8">
                        <div className="text-4xl mb-4">📊</div>
                        <p className="text-gray-400">Nenhuma transação encontrada</p>
                      </div>
                    ) : (
                      transactions.map((transaction) => (
                        <div key={transaction.id} className="bg-gray-700 rounded-lg p-3">
                          <div className="flex justify-between items-start">
                            <div className="flex items-center space-x-3">
                              <div className="text-xl">
                                {getTransactionIcon(transaction.tipo)}
                              </div>
                              <div>
                                <div className={`font-bold ${getTransactionColor(transaction.tipo)}`}>
                                  {transaction.tipo.charAt(0).toUpperCase() + transaction.tipo.slice(1)}
                                </div>
                                <div className="text-xs text-gray-400">
                                  {transaction.descricao}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className={`font-bold ${getTransactionColor(transaction.tipo)}`}>
                                {transaction.tipo === 'saque' ? '-' : '+'}${transaction.valor.toLocaleString()}
                              </div>
                              <div className="text-xs text-gray-400">
                                {new Date(transaction.timestamp).toLocaleDateString('pt-BR')}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <div className="text-4xl mb-4">🏦</div>
            <p className="text-gray-400">Erro ao carregar dados bancários</p>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0">
        <div className="flex justify-center">
          <button 
            onClick={() => window.history.back()}
            className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
          >
            <span className="text-lg">←</span>
            <span className="text-sm">Voltar</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default BankPage;
