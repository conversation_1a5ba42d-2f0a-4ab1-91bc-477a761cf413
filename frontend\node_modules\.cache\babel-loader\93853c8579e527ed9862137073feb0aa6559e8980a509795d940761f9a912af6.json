{"ast": null, "code": "import StorageFileApi from './packages/StorageFileApi';\nimport StorageBucketApi from './packages/StorageBucketApi';\nexport class StorageClient extends StorageBucketApi {\n  constructor(url, headers = {}, fetch) {\n    super(url, headers, fetch);\n  }\n  /**\n   * Perform file operation in a bucket.\n   *\n   * @param id The bucket id to operate on.\n   */\n  from(id) {\n    return new StorageFileApi(this.url, this.headers, id, this.fetch);\n  }\n}", "map": {"version": 3, "names": ["StorageFileApi", "StorageBucketApi", "StorageClient", "constructor", "url", "headers", "fetch", "from", "id"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\node_modules\\@supabase\\storage-js\\src\\StorageClient.ts"], "sourcesContent": ["import StorageFileApi from './packages/StorageFileApi'\nimport StorageBucketApi from './packages/StorageBucketApi'\nimport { Fetch } from './lib/fetch'\n\nexport class StorageClient extends StorageBucketApi {\n  constructor(url: string, headers: { [key: string]: string } = {}, fetch?: Fetch) {\n    super(url, headers, fetch)\n  }\n\n  /**\n   * Perform file operation in a bucket.\n   *\n   * @param id The bucket id to operate on.\n   */\n  from(id: string): StorageFileApi {\n    return new StorageFileApi(this.url, this.headers, id, this.fetch)\n  }\n}\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,6BAA6B;AAG1D,OAAM,MAAOC,aAAc,SAAQD,gBAAgB;EACjDE,YAAYC,GAAW,EAAEC,OAAA,GAAqC,EAAE,EAAEC,KAAa;IAC7E,KAAK,CAACF,GAAG,EAAEC,OAAO,EAAEC,KAAK,CAAC;EAC5B;EAEA;;;;;EAKAC,IAAIA,CAACC,EAAU;IACb,OAAO,IAAIR,cAAc,CAAC,IAAI,CAACI,GAAG,EAAE,IAAI,CAACC,OAAO,EAAEG,EAAE,EAAE,IAAI,CAACF,KAAK,CAAC;EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}