<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHACK Game - Teste</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e3a8a, #1e40af, #3b82f6);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .status {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            font-size: 16px;
        }
        .button:hover {
            background: #2563eb;
        }
        .logs {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">🔒 SHACK Game - Teste</div>
        
        <div class="status">
            <strong>Status do Sistema:</strong>
            <div id="status">Verificando...</div>
        </div>
        
        <button class="button" onclick="testReact()">Testar React</button>
        <button class="button" onclick="testBackend()">Testar Backend</button>
        <button class="button" onclick="testLogin()">Testar Login Mock</button>
        <button class="button" onclick="clearLogs()">Limpar Logs</button>
        
        <div class="logs" id="logs"></div>
    </div>

    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `[${timestamp}] ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
        }

        async function testReact() {
            log('🔍 Testando servidor React...');
            try {
                const response = await fetch('http://localhost:3000');
                if (response.ok) {
                    log('✅ Servidor React está funcionando');
                    updateStatus('React: ✅ OK');
                } else {
                    log(`❌ Servidor React retornou: ${response.status}`);
                    updateStatus('React: ❌ Erro');
                }
            } catch (error) {
                log(`❌ Erro ao conectar com React: ${error.message}`);
                updateStatus('React: ❌ Offline');
            }
        }

        async function testBackend() {
            log('🔍 Testando backend Flask...');
            try {
                const response = await fetch('http://localhost:5000');
                if (response.ok) {
                    const data = await response.text();
                    log('✅ Backend Flask está funcionando');
                    log(`📄 Resposta: ${data.substring(0, 100)}...`);
                    updateStatus('Backend: ✅ OK');
                } else {
                    log(`❌ Backend retornou: ${response.status}`);
                    updateStatus('Backend: ❌ Erro');
                }
            } catch (error) {
                log(`❌ Erro ao conectar com backend: ${error.message}`);
                updateStatus('Backend: ❌ Offline');
            }
        }

        async function testLogin() {
            log('🔍 Testando login mock...');
            
            // Simular dados mock
            const mockPlayer = {
                uid: 'test-user-123',
                nick: 'TestPlayer',
                email: '<EMAIL>',
                nivel: 5,
                xp: 250,
                dinheiro: 150,
                shack: 25
            };
            
            // Simular token
            const mockToken = 'mock-token-' + Date.now();
            
            try {
                // Salvar no localStorage
                localStorage.setItem('auth_token', mockToken);
                localStorage.setItem('mock_player', JSON.stringify(mockPlayer));
                
                log('✅ Login mock realizado com sucesso');
                log(`👤 Jogador: ${mockPlayer.nick}`);
                log(`💰 Dinheiro: $${mockPlayer.dinheiro}`);
                log(`⛏️ Shack: ${mockPlayer.shack}`);
                log(`🔑 Token: ${mockToken.substring(0, 20)}...`);
                
                updateStatus('Login Mock: ✅ OK');
                
                // Verificar se dados foram salvos
                const savedToken = localStorage.getItem('auth_token');
                const savedPlayer = localStorage.getItem('mock_player');
                
                if (savedToken && savedPlayer) {
                    log('✅ Dados salvos no localStorage');
                } else {
                    log('❌ Erro ao salvar dados no localStorage');
                }
                
            } catch (error) {
                log(`❌ Erro no login mock: ${error.message}`);
                updateStatus('Login Mock: ❌ Erro');
            }
        }

        // Verificar status inicial
        window.onload = function() {
            log('🚀 Teste do SHACK Game iniciado');
            
            // Verificar localStorage
            const token = localStorage.getItem('auth_token');
            const player = localStorage.getItem('mock_player');
            
            if (token) {
                log(`🔑 Token encontrado: ${token.substring(0, 20)}...`);
            }
            
            if (player) {
                try {
                    const playerData = JSON.parse(player);
                    log(`👤 Dados do jogador encontrados: ${playerData.nick}`);
                } catch (e) {
                    log('❌ Erro ao parsear dados do jogador');
                }
            }
            
            // Testar automaticamente
            setTimeout(() => {
                testReact();
                setTimeout(() => {
                    testBackend();
                }, 1000);
            }, 500);
        };
    </script>
</body>
</html>
