{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\DebugPanel.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DebugPanel = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    register,\n    simulateLogin,\n    clearError\n  } = useSimpleAuth();\n  const handleTestLogin = async () => {\n    console.log('[DebugPanel] Testando login...');\n    try {\n      await login({\n        email: '<EMAIL>',\n        password: 'test123'\n      });\n    } catch (err) {\n      console.error('[DebugPanel] Erro no login:', err);\n    }\n  };\n  const handleTestRegister = async () => {\n    console.log('[DebugPanel] Testando registro...');\n    try {\n      await register({\n        nick: 'TestPlayer',\n        email: '<EMAIL>',\n        password: 'test123'\n      });\n    } catch (err) {\n      console.error('[DebugPanel] Erro no registro:', err);\n    }\n  };\n  const handleSimulateLogin = () => {\n    console.log('[DebugPanel] Simulando login...');\n    simulateLogin();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-4 right-4 bg-gray-800 border border-gray-600 rounded-lg p-4 text-white text-sm max-w-xs z-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"font-bold mb-2\",\n      children: \"\\uD83D\\uDD27 Debug Panel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2 mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Autenticado: \", isAuthenticated ? '✅' : '❌']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Loading: \", isLoading ? '⏳' : '✅']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Usu\\xE1rio: \", (user === null || user === void 0 ? void 0 : user.nick) || 'Nenhum']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-400\",\n        children: [\"Erro: \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 19\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleTestLogin,\n        disabled: isLoading,\n        className: \"w-full py-1 px-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded text-xs\",\n        children: \"Teste Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleTestRegister,\n        disabled: isLoading,\n        className: \"w-full py-1 px-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded text-xs\",\n        children: \"Teste Registro\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSimulateLogin,\n        disabled: isLoading,\n        className: \"w-full py-1 px-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 rounded text-xs\",\n        children: \"Simular Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: clearError,\n        className: \"w-full py-1 px-2 bg-red-600 hover:bg-red-700 rounded text-xs\",\n        children: \"Limpar Erro\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_s(DebugPanel, \"N4xONBUxIom+7BNEUd5qVIRH3nY=\", false, function () {\n  return [useSimpleAuth];\n});\n_c = DebugPanel;\nexport default DebugPanel;\nvar _c;\n$RefreshReg$(_c, \"DebugPanel\");", "map": {"version": 3, "names": ["React", "useSimpleAuth", "jsxDEV", "_jsxDEV", "DebugPanel", "_s", "user", "isAuthenticated", "isLoading", "error", "login", "register", "simulateLogin", "clearError", "handleTestLogin", "console", "log", "email", "password", "err", "handleTestRegister", "nick", "handleSimulateLogin", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/DebugPanel.tsx"], "sourcesContent": ["import React from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\n\nconst DebugPanel: React.FC = () => {\n  const { \n    user, \n    isAuthenticated, \n    isLoading, \n    error, \n    login, \n    register, \n    simulateLogin,\n    clearError \n  } = useSimpleAuth();\n\n  const handleTestLogin = async () => {\n    console.log('[DebugPanel] Testando login...');\n    try {\n      await login({\n        email: '<EMAIL>',\n        password: 'test123'\n      });\n    } catch (err) {\n      console.error('[DebugPanel] Erro no login:', err);\n    }\n  };\n\n  const handleTestRegister = async () => {\n    console.log('[DebugPanel] Testando registro...');\n    try {\n      await register({\n        nick: 'TestPlayer',\n        email: '<EMAIL>',\n        password: 'test123'\n      });\n    } catch (err) {\n      console.error('[DebugPanel] Erro no registro:', err);\n    }\n  };\n\n  const handleSimulateLogin = () => {\n    console.log('[DebugPanel] Simulando login...');\n    simulateLogin();\n  };\n\n  return (\n    <div className=\"fixed top-4 right-4 bg-gray-800 border border-gray-600 rounded-lg p-4 text-white text-sm max-w-xs z-50\">\n      <h3 className=\"font-bold mb-2\">🔧 Debug Panel</h3>\n      \n      <div className=\"space-y-2 mb-4\">\n        <div>Autenticado: {isAuthenticated ? '✅' : '❌'}</div>\n        <div>Loading: {isLoading ? '⏳' : '✅'}</div>\n        <div>Usuário: {user?.nick || 'Nenhum'}</div>\n        {error && <div className=\"text-red-400\">Erro: {error}</div>}\n      </div>\n\n      <div className=\"space-y-2\">\n        <button\n          onClick={handleTestLogin}\n          disabled={isLoading}\n          className=\"w-full py-1 px-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded text-xs\"\n        >\n          Teste Login\n        </button>\n        \n        <button\n          onClick={handleTestRegister}\n          disabled={isLoading}\n          className=\"w-full py-1 px-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded text-xs\"\n        >\n          Teste Registro\n        </button>\n        \n        <button\n          onClick={handleSimulateLogin}\n          disabled={isLoading}\n          className=\"w-full py-1 px-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 rounded text-xs\"\n        >\n          Simular Login\n        </button>\n\n        {error && (\n          <button\n            onClick={clearError}\n            className=\"w-full py-1 px-2 bg-red-600 hover:bg-red-700 rounded text-xs\"\n          >\n            Limpar Erro\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default DebugPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IACJC,IAAI;IACJC,eAAe;IACfC,SAAS;IACTC,KAAK;IACLC,KAAK;IACLC,QAAQ;IACRC,aAAa;IACbC;EACF,CAAC,GAAGZ,aAAa,CAAC,CAAC;EAEnB,MAAMa,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C,IAAI;MACF,MAAMN,KAAK,CAAC;QACVO,KAAK,EAAE,eAAe;QACtBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZJ,OAAO,CAACN,KAAK,CAAC,6BAA6B,EAAEU,GAAG,CAAC;IACnD;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCL,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,IAAI;MACF,MAAML,QAAQ,CAAC;QACbU,IAAI,EAAE,YAAY;QAClBJ,KAAK,EAAE,eAAe;QACtBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZJ,OAAO,CAACN,KAAK,CAAC,gCAAgC,EAAEU,GAAG,CAAC;IACtD;EACF,CAAC;EAED,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChCP,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CJ,aAAa,CAAC,CAAC;EACjB,CAAC;EAED,oBACET,OAAA;IAAKoB,SAAS,EAAC,wGAAwG;IAAAC,QAAA,gBACrHrB,OAAA;MAAIoB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAElDzB,OAAA;MAAKoB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BrB,OAAA;QAAAqB,QAAA,GAAK,eAAa,EAACjB,eAAe,GAAG,GAAG,GAAG,GAAG;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrDzB,OAAA;QAAAqB,QAAA,GAAK,WAAS,EAAChB,SAAS,GAAG,GAAG,GAAG,GAAG;MAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3CzB,OAAA;QAAAqB,QAAA,GAAK,cAAS,EAAC,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI,KAAI,QAAQ;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC3CnB,KAAK,iBAAIN,OAAA;QAAKoB,SAAS,EAAC,cAAc;QAAAC,QAAA,GAAC,QAAM,EAACf,KAAK;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eAENzB,OAAA;MAAKoB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBrB,OAAA;QACE0B,OAAO,EAAEf,eAAgB;QACzBgB,QAAQ,EAAEtB,SAAU;QACpBe,SAAS,EAAC,qFAAqF;QAAAC,QAAA,EAChG;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETzB,OAAA;QACE0B,OAAO,EAAET,kBAAmB;QAC5BU,QAAQ,EAAEtB,SAAU;QACpBe,SAAS,EAAC,uFAAuF;QAAAC,QAAA,EAClG;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETzB,OAAA;QACE0B,OAAO,EAAEP,mBAAoB;QAC7BQ,QAAQ,EAAEtB,SAAU;QACpBe,SAAS,EAAC,yFAAyF;QAAAC,QAAA,EACpG;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERnB,KAAK,iBACJN,OAAA;QACE0B,OAAO,EAAEhB,UAAW;QACpBU,SAAS,EAAC,8DAA8D;QAAAC,QAAA,EACzE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CAzFID,UAAoB;EAAA,QAUpBH,aAAa;AAAA;AAAA8B,EAAA,GAVb3B,UAAoB;AA2F1B,eAAeA,UAAU;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}