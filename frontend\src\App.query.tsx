import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import './styles/globals.css';

// Configuração do React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutos
    },
  },
});

// Componente de teste simples
const TestPage = ({ title, description }: { title: string; description: string }) => (
  <div className="min-h-screen bg-bg-primary text-text-primary p-8">
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <Link to="/" className="text-accent-blue hover:text-primary-light">
          ← Voltar ao início
        </Link>
      </div>
      
      <div className="card">
        <h1 className="text-3xl font-bold mb-4">{title}</h1>
        <p className="text-text-secondary mb-4">
          {description}
        </p>
        
        <div className="bg-green-900 border border-green-500 rounded p-4">
          <p className="text-green-100 text-sm">
            ✅ React Query Provider ativo
          </p>
        </div>
      </div>
    </div>
  </div>
);

// Página inicial
const HomePage = () => (
  <div className="min-h-screen bg-bg-primary text-text-primary p-8">
    <div className="max-w-4xl mx-auto">
      <h1 className="text-4xl font-bold text-center mb-8">
        🎮 SHACK Web Game - Teste React Query
      </h1>
      
      <div className="card text-center">
        <h2 className="text-2xl font-semibold mb-6">
          React Query Adicionado!
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Link to="/login" className="card bg-blue-900 border-blue-500 hover:bg-blue-800 transition-colors">
            <h3 className="text-lg font-semibold text-blue-100 mb-2">
              🔐 Teste Login
            </h3>
            <p className="text-blue-200 text-sm">
              Página de login com React Query
            </p>
          </Link>
          
          <Link to="/game" className="card bg-green-900 border-green-500 hover:bg-green-800 transition-colors">
            <h3 className="text-lg font-semibold text-green-100 mb-2">
              🎮 Teste Game
            </h3>
            <p className="text-green-200 text-sm">
              Página do jogo com React Query
            </p>
          </Link>
        </div>
        
        <div className="bg-bg-tertiary border border-border-color rounded p-4">
          <div className="text-text-muted text-sm space-y-1">
            <p>✅ React funcionando</p>
            <p>✅ React Router funcionando</p>
            <p>✅ Tailwind CSS funcionando</p>
            <p>✅ React Query Provider ativo</p>
          </div>
        </div>
      </div>
    </div>
  </div>
);

function App() {
  console.log('App Query - Renderizando com React Query...');
  
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route 
            path="/login" 
            element={
              <TestPage 
                title="Página de Login" 
                description="Esta página está envolvida pelo QueryClientProvider do React Query."
              />
            } 
          />
          <Route 
            path="/game" 
            element={
              <TestPage 
                title="Página do Jogo" 
                description="Esta página pode usar hooks do React Query como useQuery e useMutation."
              />
            } 
          />
          <Route 
            path="*" 
            element={
              <TestPage 
                title="Página 404" 
                description="Página não encontrada, mas React Query ainda está ativo."
              />
            } 
          />
        </Routes>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
