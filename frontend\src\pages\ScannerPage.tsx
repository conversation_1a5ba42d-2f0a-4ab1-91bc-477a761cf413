import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGameSystem } from '../hooks/useGameSystem';
import { useSimpleAuth } from '../stores/simpleAuthStore';
import { usePlayer } from '../stores/playerStore';
import GameFooter from '../components/common/GameFooter';
import { ScanIcon, TargetIcon, ExploitIcon } from '../components/ui/GameIcons';

const ScannerPage: React.FC = () => {
  const { user } = useSimpleAuth();
  const { currentPlayer } = usePlayer();
  const navigate = useNavigate();
  
  const {
    scanTargets,
    isScanning,
    scanError,
    isExploiting,
    exploitError,
    performQuickScan,
    performAdvancedScan,
    performExploit,
    clearErrors
  } = useGameSystem();

  const [scanType, setScanType] = useState<'quick' | 'advanced'>('quick');
  const [targetIp, setTargetIp] = useState('');

  useEffect(() => {
    clearErrors();
  }, [clearErrors]);

  const handleQuickScan = async () => {
    try {
      await performQuickScan();
    } catch (error) {
      console.error('Erro no scan rápido:', error);
    }
  };

  const handleAdvancedScan = async () => {
    if (!targetIp.trim()) {
      alert('Digite um IP válido');
      return;
    }

    try {
      await performAdvancedScan(targetIp);
    } catch (error) {
      console.error('Erro no scan avançado:', error);
    }
  };

  const handleExploit = async (target: any) => {
    try {
      const exploited = await performExploit(target);
      if (exploited) {
        // Navegar para tela de invasão
        navigate('/game/invaded', { state: { target: exploited } });
      }
    } catch (error) {
      console.error('Erro no exploit:', error);
    }
  };

  const canExploit = (target: any) => {
    const playerCPU = currentPlayer?.cpu || 1;
    const targetFirewall = target.firewall || 1;
    return playerCPU > targetFirewall;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-gray-700/50 p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => navigate('/game')}
              className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors"
            >
              <span className="text-lg">←</span>
            </button>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                Network Scanner
              </h1>
              <p className="text-xs text-gray-400">Sistema de Reconhecimento</p>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-1 text-green-400 text-xs">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-4 overflow-y-auto space-y-6">
        {/* Controles de Scan */}
        <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50">
          <h2 className="text-lg font-semibold mb-4 text-white flex items-center space-x-2">
            <ScanIcon size={20} className="text-blue-400" />
            <span>Controles de Scanner</span>
          </h2>
          
          {/* Tipo de scan */}
          <div className="flex space-x-2 mb-4">
            <button
              onClick={() => setScanType('quick')}
              className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all ${
                scanType === 'quick'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Scan Rápido
            </button>
            <button
              onClick={() => setScanType('advanced')}
              className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all ${
                scanType === 'advanced'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Scan Avançado
            </button>
          </div>

          {/* Controles específicos */}
          {scanType === 'quick' ? (
            <button
              onClick={handleQuickScan}
              disabled={isScanning}
              className="w-full py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold flex items-center justify-center space-x-2"
            >
              {isScanning ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Escaneando...</span>
                </>
              ) : (
                <>
                  <ScanIcon size={16} />
                  <span>Iniciar Scan Rápido</span>
                </>
              )}
            </button>
          ) : (
            <div className="space-y-3">
              <input
                type="text"
                value={targetIp}
                onChange={(e) => setTargetIp(e.target.value)}
                placeholder="Digite o IP do alvo (ex: *************)"
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
              />
              <button
                onClick={handleAdvancedScan}
                disabled={isScanning || !targetIp.trim()}
                className="w-full py-3 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white rounded-lg font-semibold flex items-center justify-center space-x-2"
              >
                {isScanning ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Escaneando...</span>
                  </>
                ) : (
                  <>
                    <TargetIcon size={16} />
                    <span>Scan por IP</span>
                  </>
                )}
              </button>
            </div>
          )}

          {/* Erros */}
          {(scanError || exploitError) && (
            <div className="mt-4 p-3 bg-red-900/50 border border-red-700 rounded-lg">
              <p className="text-red-300 text-sm">{scanError || exploitError}</p>
            </div>
          )}
        </div>

        {/* Resultados do Scan */}
        {scanTargets.length > 0 && (
          <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50">
            <h2 className="text-lg font-semibold mb-4 text-white">
              Alvos Encontrados ({scanTargets.length})
            </h2>
            
            <div className="space-y-3">
              {scanTargets.map((target, index) => {
                const canExploitTarget = canExploit(target);
                const playerCPU = currentPlayer?.cpu || 1;
                
                return (
                  <div
                    key={index}
                    className="bg-gray-800/50 rounded-lg p-4 border border-gray-600/50"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className="font-semibold text-white">{target.nick}</h3>
                        <p className="text-sm text-gray-400 font-mono">{target.ip}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-blue-400">Nível {target.nivel}</div>
                        <div className="text-xs text-gray-500">${target.dinheiro?.toLocaleString() || '0'}</div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3 mb-3 text-xs">
                      <div className="bg-gray-700/50 rounded p-2">
                        <span className="text-gray-400">Firewall:</span>
                        <span className="text-red-400 ml-1 font-semibold">{target.firewall || 1}</span>
                      </div>
                      <div className="bg-gray-700/50 rounded p-2">
                        <span className="text-gray-400">Seu CPU:</span>
                        <span className="text-blue-400 ml-1 font-semibold">{playerCPU}</span>
                      </div>
                    </div>
                    
                    <button
                      onClick={() => handleExploit(target)}
                      disabled={!canExploitTarget || isExploiting}
                      className={`w-full py-2 rounded-lg font-semibold flex items-center justify-center space-x-2 ${
                        canExploitTarget
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : 'bg-red-600/50 text-red-300 cursor-not-allowed'
                      }`}
                    >
                      {isExploiting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          <span>Explorando...</span>
                        </>
                      ) : canExploitTarget ? (
                        <>
                          <ExploitIcon size={16} />
                          <span>Explorar Sistema</span>
                        </>
                      ) : (
                        <span>CPU Insuficiente (precisa {(target.firewall || 1) + 1}+)</span>
                      )}
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Estado vazio */}
        {scanTargets.length === 0 && !isScanning && (
          <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700/50 text-center">
            <ScanIcon size={48} className="text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-400 mb-2">Nenhum Alvo Encontrado</h3>
            <p className="text-gray-500">Execute um scan para encontrar sistemas vulneráveis na rede</p>
          </div>
        )}
      </div>

      {/* Footer */}
      <GameFooter currentPage="scanner" />
    </div>
  );
};

export default ScannerPage;
