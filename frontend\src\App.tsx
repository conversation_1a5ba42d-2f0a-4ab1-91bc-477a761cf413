import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Componentes
import AuthGuard from './components/auth/AuthGuard';
import LoginPage from './pages/LoginPage';
import GamePage from './pages/GamePage';
import NotificationSystem from './components/common/NotificationSystem';

// Estilos
import './styles/globals.css';

// Configuração do React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutos
    },
  },
});

function App() {
  console.log('App - Renderizando...');

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-bg-primary text-text-primary">
          {/* Sistema de notificações global */}
          <NotificationSystem />

          {/* Rotas da aplicação */}
          <Routes>
            {/* Rota de login */}
            <Route path="/login" element={<LoginPage />} />

            {/* Rotas protegidas do jogo */}
            <Route
              path="/game/*"
              element={
                <AuthGuard>
                  <GamePage />
                </AuthGuard>
              }
            />

            {/* Rota raiz - redireciona para o jogo */}
            <Route
              path="/"
              element={
                <AuthGuard>
                  <Navigate to="/game" replace />
                </AuthGuard>
              }
            />

            {/* Rota 404 - redireciona para o jogo */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
