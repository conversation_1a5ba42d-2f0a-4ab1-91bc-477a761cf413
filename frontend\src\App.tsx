import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Componentes
import TestPage from './pages/TestPage';
import HackGamePage from './pages/HackGamePage';
import { useSimpleAuth } from './stores/simpleAuthStore';

// Estilos
import './styles/globals.css';

// Componente para roteamento baseado em autenticação
const AppRouter: React.FC = () => {
  const { isAuthenticated, isLoading, checkAuth } = useSimpleAuth();

  // Verificar autenticação na inicialização
  useEffect(() => {
    console.log('AppRouter - Verificando autenticação inicial...');
    checkAuth();
  }, [checkAuth]);

  console.log('AppRouter - Estado:', { isAuthenticated, isLoading });

  // Mostrar loading se necessário
  if (isLoading) {
    return (
      <div className="min-h-screen bg-bg-primary text-text-primary flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Rota principal do jogo - sempre mostra HackGamePage */}
      <Route path="/game" element={<HackGamePage />} />

      {/* Rota de teste */}
      <Route path="/test" element={<TestPage />} />

      {/* Rota raiz redireciona para o jogo */}
      <Route path="/" element={<Navigate to="/game" replace />} />

      {/* 404 */}
      <Route path="*" element={<Navigate to="/game" replace />} />
    </Routes>
  );
};

function App() {
  console.log('App - Renderizando com autenticação simples...');

  return (
    <Router>
      <div className="min-h-screen bg-bg-primary text-text-primary">
        <AppRouter />
      </div>
    </Router>
  );
}

export default App;
