import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Componentes
import TestPage from './pages/TestPage';
import SimpleLoginPage from './pages/SimpleLoginPage';
import HackGamePage from './pages/HackGamePage';
import { useSimpleAuth } from './stores/simpleAuthStore';

// Estilos
import './styles/globals.css';

// Componente para roteamento baseado em autenticação
const AppRouter: React.FC = () => {
  const { isAuthenticated, isLoading, checkAuth } = useSimpleAuth();

  // Verificar autenticação na inicialização
  useEffect(() => {
    console.log('AppRouter - Verificando autenticação inicial...');
    checkAuth();
  }, [checkAuth]);

  console.log('AppRouter - Estado:', { isAuthenticated, isLoading });

  // Mostrar loading se necessário
  if (isLoading) {
    return (
      <div className="min-h-screen bg-bg-primary text-text-primary flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Rota de login */}
      <Route path="/login" element={<SimpleLoginPage />} />

      {/* Rota de teste */}
      <Route path="/test" element={<TestPage />} />

      {/* Rota do jogo (protegida) */}
      <Route
        path="/game/*"
        element={
          isAuthenticated ? (
            <HackGamePage />
          ) : (
            <Navigate to="/login" replace />
          )
        }
      />

      {/* Rota raiz */}
      <Route
        path="/"
        element={
          isAuthenticated ? (
            <Navigate to="/game" replace />
          ) : (
            <Navigate to="/login" replace />
          )
        }
      />

      {/* 404 */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

function App() {
  console.log('App - Renderizando com autenticação simples...');

  return (
    <Router>
      <div className="min-h-screen bg-bg-primary text-text-primary">
        <AppRouter />
      </div>
    </Router>
  );
}

export default App;
