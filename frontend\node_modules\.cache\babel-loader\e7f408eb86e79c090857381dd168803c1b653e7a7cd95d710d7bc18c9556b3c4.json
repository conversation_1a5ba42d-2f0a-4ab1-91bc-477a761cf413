{"ast": null, "code": "// src/removable.ts\nimport { isServer, isValidTimeout } from \"./utils.js\";\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(this.gcTime || 0, newGcTime ?? (isServer ? Infinity : 5 * 60 * 1e3));\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\nexport { Removable };", "map": {"version": 3, "names": ["isServer", "isValidTimeout", "Removable", "gcTimeout", "destroy", "clearGcTimeout", "scheduleGc", "gcTime", "setTimeout", "optionalRemove", "updateGcTime", "newGcTime", "Math", "max", "Infinity", "clearTimeout"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\query-core\\src\\removable.ts"], "sourcesContent": ["import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  gcTime!: number\n  #gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.gcTime)\n    }\n  }\n\n  protected updateGcTime(newGcTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no gcTime is set\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout)\n      this.#gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n"], "mappings": ";AAAA,SAASA,QAAA,EAAUC,cAAA,QAAsB;AAElC,IAAeC,SAAA,GAAf,MAAyB;EAE9B,CAAAC,SAAA;EAEAC,QAAA,EAAgB;IACd,KAAKC,cAAA,CAAe;EACtB;EAEUC,WAAA,EAAmB;IAC3B,KAAKD,cAAA,CAAe;IAEpB,IAAIJ,cAAA,CAAe,KAAKM,MAAM,GAAG;MAC/B,KAAK,CAAAJ,SAAA,GAAaK,UAAA,CAAW,MAAM;QACjC,KAAKC,cAAA,CAAe;MACtB,GAAG,KAAKF,MAAM;IAChB;EACF;EAEUG,aAAaC,SAAA,EAAqC;IAE1D,KAAKJ,MAAA,GAASK,IAAA,CAAKC,GAAA,CACjB,KAAKN,MAAA,IAAU,GACfI,SAAA,KAAcX,QAAA,GAAWc,QAAA,GAAW,IAAI,KAAK,IAC/C;EACF;EAEUT,eAAA,EAAiB;IACzB,IAAI,KAAK,CAAAF,SAAA,EAAY;MACnBY,YAAA,CAAa,KAAK,CAAAZ,SAAU;MAC5B,KAAK,CAAAA,SAAA,GAAa;IACpB;EACF;AAGF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}