-- Schema completo para o jogo SHACK no Supabase
-- Execute este script no SQL Editor do Supabase

-- Extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tabela de usuários/jogadores
CREATE TABLE usuarios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    uid VARCHAR(255) UNIQUE NOT NULL, -- Firebase UID (para compatibilidade)
    nick VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL, -- Hash da senha para autenticação
    ip VARCHAR(15) NOT NULL,
    
    -- Recursos do jogador
    dinheiro BIGINT DEFAULT 1000,
    shack BIGINT DEFAULT 100,
    
    -- Equipamentos
    cpu INTEGER DEFAULT 1,
    firewall INTEGER DEFAULT 1,
    antivirus INTEGER DEFAULT 1,
    malware_kit INTEGER DEFAULT 1,
    
    -- Progressão
    nivel INTEGER DEFAULT 1,
    xp BIGINT DEFAULT 0,
    pontos BIGINT DEFAULT 0, -- Pontos totais para ranking
    
    -- Mineração
    nivel_mineradora INTEGER DEFAULT 1,
    ultimo_recurso_coletado_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Grupos e torneios
    grupo_id UUID,
    deface_points_individual INTEGER DEFAULT 0,
    tournament_points_individual INTEGER DEFAULT 0,
    last_deface_timestamp BIGINT DEFAULT 0,
    
    -- Habilidades NFT (JSONB para flexibilidade)
    habilidades_adquiridas JSONB DEFAULT '{}',
    
    -- Sistema de cooldowns (JSONB para flexibilidade)
    cooldown_roubo_ips JSONB DEFAULT '{}',
    
    -- Logs e histórico (JSONB para arrays)
    historico JSONB DEFAULT '[]',
    log JSONB DEFAULT '[]',
    
    -- Metadados
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE
);

-- Tabela de grupos
CREATE TABLE grupos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nome VARCHAR(100) UNIQUE NOT NULL,
    lider_uid VARCHAR(255) NOT NULL,
    
    -- Membros (array de UIDs)
    membros JSONB DEFAULT '[]',
    max_membros INTEGER DEFAULT 5,
    
    -- Pontuação
    deface_points INTEGER DEFAULT 0,
    tournament_points INTEGER DEFAULT 0,
    
    -- Metadados
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Tabela de habilidades NFT
CREATE TABLE habilidades_nft (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    skill_id VARCHAR(100) UNIQUE NOT NULL, -- ID único da habilidade
    nome VARCHAR(200) NOT NULL,
    descricao TEXT NOT NULL,
    efeito DECIMAL(5,3) NOT NULL, -- Valor do efeito (ex: 0.05 para 5%)
    preco_shack INTEGER NOT NULL,
    estoque_maximo INTEGER NOT NULL,
    estoque_atual INTEGER NOT NULL,
    
    -- Metadados
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Tabela de notícias
CREATE TABLE noticias (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    priority BOOLEAN DEFAULT FALSE,
    
    -- Metadados
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Tabela de logs de atividade (para auditoria)
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_uid VARCHAR(255),
    action VARCHAR(100) NOT NULL,
    details JSONB,
    ip_address VARCHAR(15),
    user_agent TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de vitimas/alvos (NPCs)
CREATE TABLE vitimas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ip VARCHAR(15) UNIQUE NOT NULL,
    nome VARCHAR(100) NOT NULL,
    dinheiro BIGINT DEFAULT 1000,
    firewall INTEGER DEFAULT 1,
    antivirus INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX idx_usuarios_uid ON usuarios(uid);
CREATE INDEX idx_usuarios_nick ON usuarios(nick);
CREATE INDEX idx_usuarios_grupo_id ON usuarios(grupo_id);
CREATE INDEX idx_usuarios_nivel ON usuarios(nivel);
CREATE INDEX idx_grupos_nome ON grupos(nome);
CREATE INDEX idx_grupos_lider ON grupos(lider_uid);
CREATE INDEX idx_habilidades_skill_id ON habilidades_nft(skill_id);
CREATE INDEX idx_activity_logs_user_uid ON activity_logs(user_uid);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);
CREATE INDEX idx_vitimas_ip ON vitimas(ip);

-- Foreign Keys
ALTER TABLE usuarios ADD CONSTRAINT fk_usuarios_grupo 
    FOREIGN KEY (grupo_id) REFERENCES grupos(id);

-- Triggers para updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_usuarios_updated_at BEFORE UPDATE ON usuarios
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_grupos_updated_at BEFORE UPDATE ON grupos
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_habilidades_nft_updated_at BEFORE UPDATE ON habilidades_nft
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vitimas_updated_at BEFORE UPDATE ON vitimas
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Inserir dados iniciais
-- Habilidades NFT padrão
INSERT INTO habilidades_nft (skill_id, nome, descricao, efeito, preco_shack, estoque_maximo, estoque_atual) VALUES
('the_killer', 'The Killer', 'Aumenta o poder do seu firewall em 5%', 0.05, 2500, 5, 5),
('firewall_reverso', 'Firewall Reverso', 'Ignora 5% do firewall adversário em ataques.', 0.05, 2000, 5, 5),
('speed_hacker', 'Speed Hacker', 'Reduz tempo de cooldown entre ataques em 10%', 0.10, 3000, 3, 3),
('crypto_miner', 'Crypto Miner', 'Aumenta geração de Shack da mineradora em 15%', 0.15, 4000, 2, 2);

-- Vítimas/NPCs iniciais
INSERT INTO vitimas (ip, nome, dinheiro, firewall, antivirus) VALUES
('*************', 'Servidor Corporativo', 5000, 2, 2),
('*********', 'Banco Local', 15000, 5, 4),
('***********', 'Loja Online', 8000, 3, 2),
('***********', 'Roteador Doméstico', 500, 1, 1),
('***********', 'Servidor de Jogos', 12000, 4, 3);

-- Notícias iniciais
INSERT INTO noticias (title, content, priority) VALUES
('🎮 Bem-vindo ao SHACK!', 'O sistema de hacking mais avançado está online. Prepare-se para dominar a rede!', true),
('⚡ Habilidades NFT Disponíveis', 'Novas habilidades NFT foram adicionadas à loja. Melhore suas capacidades de hacking!', false),
('🏆 Torneio Mensal', 'Participe do torneio mensal e ganhe prêmios exclusivos. Forme seu grupo e domine!', false);

-- Tabela de cooldowns para roubo (1 hora por IP)
CREATE TABLE cooldown_roubo (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    atacante_uid VARCHAR(255) NOT NULL,
    alvo_ip VARCHAR(15) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    UNIQUE(atacante_uid, alvo_ip),
    FOREIGN KEY (atacante_uid) REFERENCES usuarios(uid) ON DELETE CASCADE
);

-- Tabela de cooldowns para deface (30 minutos por jogador)
CREATE TABLE cooldown_deface (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    jogador_uid VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    FOREIGN KEY (jogador_uid) REFERENCES usuarios(uid) ON DELETE CASCADE
);

-- Índices para performance
CREATE INDEX idx_cooldown_roubo_atacante ON cooldown_roubo(atacante_uid);
CREATE INDEX idx_cooldown_roubo_expires ON cooldown_roubo(expires_at);
CREATE INDEX idx_cooldown_deface_expires ON cooldown_deface(expires_at);

-- Função para limpar cooldowns expirados automaticamente
CREATE OR REPLACE FUNCTION limpar_cooldowns_expirados()
RETURNS void AS $$
BEGIN
    DELETE FROM cooldown_roubo WHERE expires_at < NOW();
    DELETE FROM cooldown_deface WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;
