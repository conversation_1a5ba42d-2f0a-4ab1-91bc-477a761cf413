{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\HackGamePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useHackGameStore } from '../stores/hackGameStore';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport MobilePhone from '../components/game/MobilePhone';\nimport SimpleLoginPage from './SimpleLoginPage';\nimport GameHomePage from './GameHomePage';\nimport GameAppsPage from './GameAppsPage';\nimport DebugPanel from '../components/DebugPanel';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HackGamePage = () => {\n  _s();\n  console.log('🎮 HackGamePage - Componente iniciado');\n  const {\n    player,\n    currentScreen,\n    setCurrentScreen,\n    syncWithServer,\n    initializeFromAuth\n  } = useHackGameStore();\n  const {\n    isAuthenticated,\n    user\n  } = useSimpleAuth();\n  console.log('🎮 HackGamePage - Estado atual:', {\n    isAuthenticated,\n    user: user === null || user === void 0 ? void 0 : user.nick,\n    player: player === null || player === void 0 ? void 0 : player.nick,\n    currentScreen\n  });\n\n  // Verificar se tem token e tentar carregar jogador\n  useEffect(() => {\n    const token = localStorage.getItem('auth_token');\n    if (token && !player) {\n      syncWithServer();\n    }\n  }, [player, syncWithServer]);\n\n  // Se está autenticado mas não tem player carregado, tentar carregar\n  useEffect(() => {\n    if (isAuthenticated && user && !player) {\n      console.log('Usuário autenticado, carregando dados do jogo...');\n      initializeFromAuth();\n    }\n  }, [isAuthenticated, user, player, initializeFromAuth]);\n\n  // Se não está autenticado, mostrar tela de login\n  if (!isAuthenticated || !user) {\n    return /*#__PURE__*/_jsxDEV(MobilePhone, {\n      children: /*#__PURE__*/_jsxDEV(SimpleLoginPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Se está autenticado mas ainda não carregou o player, mostrar loading\n  if (!player) {\n    return /*#__PURE__*/_jsxDEV(MobilePhone, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400\",\n            children: \"Carregando dados do jogador...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Renderizar tela baseada no currentScreen\n  const renderCurrentScreen = () => {\n    switch (currentScreen) {\n      case 'home':\n        return /*#__PURE__*/_jsxDEV(GameHomePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 16\n        }, this);\n      case 'apps':\n        return /*#__PURE__*/_jsxDEV(GameAppsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 16\n        }, this);\n      case 'scanner':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Encontre alvos na rede para hackear\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this);\n      case 'terminal':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDCBB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Terminal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Interface de hacking avan\\xE7ada\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this);\n      case 'profile':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Perfil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Estat\\xEDsticas e conquistas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this);\n      case 'settings':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Configura\\xE7\\xF5es\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Ajustes do sistema\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this);\n      case 'shop':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDED2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Loja\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Compre itens especiais\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this);\n      case 'ranking':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Ranking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Top hackers do servidor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this);\n      case 'logs':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Logs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Hist\\xF3rico de atividades\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this);\n      case 'chat':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Chat Global\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Converse com outros hackers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(GameHomePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(MobilePhone, {\n      children: renderCurrentScreen()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DebugPanel, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(HackGamePage, \"I8+h8Pxyef15qHJYCv5/UlLP3zg=\", false, function () {\n  return [useHackGameStore, useSimpleAuth];\n});\n_c = HackGamePage;\nexport default HackGamePage;\nvar _c;\n$RefreshReg$(_c, \"HackGamePage\");", "map": {"version": 3, "names": ["React", "useEffect", "useHackGameStore", "useSimpleAuth", "MobilePhone", "SimpleLoginPage", "GameHomePage", "GameAppsPage", "DebugPanel", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HackGamePage", "_s", "console", "log", "player", "currentScreen", "setCurrentScreen", "syncWithServer", "initializeFromAuth", "isAuthenticated", "user", "nick", "token", "localStorage", "getItem", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "renderCurrentScreen", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/HackGamePage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useHackGameStore } from '../stores/hackGameStore';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport MobilePhone from '../components/game/MobilePhone';\nimport SimpleLoginPage from './SimpleLoginPage';\nimport GameHomePage from './GameHomePage';\nimport GameAppsPage from './GameAppsPage';\nimport DebugPanel from '../components/DebugPanel';\n\nconst HackGamePage: React.FC = () => {\n  console.log('🎮 HackGamePage - Componente iniciado');\n\n  const { player, currentScreen, setCurrentScreen, syncWithServer, initializeFromAuth } = useHackGameStore();\n  const { isAuthenticated, user } = useSimpleAuth();\n\n  console.log('🎮 HackGamePage - Estado atual:', {\n    isAuthenticated,\n    user: user?.nick,\n    player: player?.nick,\n    currentScreen\n  });\n\n  // Verificar se tem token e tentar carregar jogador\n  useEffect(() => {\n    const token = localStorage.getItem('auth_token');\n    if (token && !player) {\n      syncWithServer();\n    }\n  }, [player, syncWithServer]);\n\n  // Se está autenticado mas não tem player carregado, tentar carregar\n  useEffect(() => {\n    if (isAuthenticated && user && !player) {\n      console.log('Usuário autenticado, carregando dados do jogo...');\n      initializeFromAuth();\n    }\n  }, [isAuthenticated, user, player, initializeFromAuth]);\n\n  // Se não está autenticado, mostrar tela de login\n  if (!isAuthenticated || !user) {\n    return (\n      <MobilePhone>\n        <SimpleLoginPage />\n      </MobilePhone>\n    );\n  }\n\n  // Se está autenticado mas ainda não carregou o player, mostrar loading\n  if (!player) {\n    return (\n      <MobilePhone>\n        <div className=\"flex items-center justify-center h-full\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n            <p className=\"text-gray-400\">Carregando dados do jogador...</p>\n          </div>\n        </div>\n      </MobilePhone>\n    );\n  }\n\n  // Renderizar tela baseada no currentScreen\n  const renderCurrentScreen = () => {\n    switch (currentScreen) {\n      case 'home':\n        return <GameHomePage />;\n      case 'apps':\n        return <GameAppsPage />;\n      case 'scanner':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🔍</div>\n              <p className=\"text-lg font-semibold mb-2\">Scanner</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Encontre alvos na rede para hackear</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'terminal':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">💻</div>\n              <p className=\"text-lg font-semibold mb-2\">Terminal</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Interface de hacking avançada</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'profile':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">👤</div>\n              <p className=\"text-lg font-semibold mb-2\">Perfil</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Estatísticas e conquistas</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'settings':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">⚙️</div>\n              <p className=\"text-lg font-semibold mb-2\">Configurações</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Ajustes do sistema</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'shop':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🛒</div>\n              <p className=\"text-lg font-semibold mb-2\">Loja</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Compre itens especiais</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'ranking':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🏆</div>\n              <p className=\"text-lg font-semibold mb-2\">Ranking</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Top hackers do servidor</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'logs':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">📊</div>\n              <p className=\"text-lg font-semibold mb-2\">Logs</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Histórico de atividades</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'chat':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">💬</div>\n              <p className=\"text-lg font-semibold mb-2\">Chat Global</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Converse com outros hackers</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      default:\n        return <GameHomePage />;\n    }\n  };\n\n  return (\n    <>\n      <MobilePhone>\n        {renderCurrentScreen()}\n      </MobilePhone>\n      <DebugPanel />\n    </>\n  );\n};\n\nexport default HackGamePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,UAAU,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnCC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;EAEpD,MAAM;IAAEC,MAAM;IAAEC,aAAa;IAAEC,gBAAgB;IAAEC,cAAc;IAAEC;EAAmB,CAAC,GAAGnB,gBAAgB,CAAC,CAAC;EAC1G,MAAM;IAAEoB,eAAe;IAAEC;EAAK,CAAC,GAAGpB,aAAa,CAAC,CAAC;EAEjDY,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;IAC7CM,eAAe;IACfC,IAAI,EAAEA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI;IAChBP,MAAM,EAAEA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,IAAI;IACpBN;EACF,CAAC,CAAC;;EAEF;EACAjB,SAAS,CAAC,MAAM;IACd,MAAMwB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,IAAIF,KAAK,IAAI,CAACR,MAAM,EAAE;MACpBG,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACH,MAAM,EAAEG,cAAc,CAAC,CAAC;;EAE5B;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIqB,eAAe,IAAIC,IAAI,IAAI,CAACN,MAAM,EAAE;MACtCF,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/DK,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACC,eAAe,EAAEC,IAAI,EAAEN,MAAM,EAAEI,kBAAkB,CAAC,CAAC;;EAEvD;EACA,IAAI,CAACC,eAAe,IAAI,CAACC,IAAI,EAAE;IAC7B,oBACEb,OAAA,CAACN,WAAW;MAAAwB,QAAA,eACVlB,OAAA,CAACL,eAAe;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAElB;;EAEA;EACA,IAAI,CAACf,MAAM,EAAE;IACX,oBACEP,OAAA,CAACN,WAAW;MAAAwB,QAAA,eACVlB,OAAA;QAAKuB,SAAS,EAAC,yCAAyC;QAAAL,QAAA,eACtDlB,OAAA;UAAKuB,SAAS,EAAC,aAAa;UAAAL,QAAA,gBAC1BlB,OAAA;YAAKuB,SAAS,EAAC;UAA2E;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjGtB,OAAA;YAAGuB,SAAS,EAAC,eAAe;YAAAL,QAAA,EAAC;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAElB;;EAEA;EACA,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC,QAAQhB,aAAa;MACnB,KAAK,MAAM;QACT,oBAAOR,OAAA,CAACJ,YAAY;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,MAAM;QACT,oBAAOtB,OAAA,CAACH,YAAY;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,SAAS;QACZ,oBACEtB,OAAA;UAAKuB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjElB,OAAA;YAAKuB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1BlB,OAAA;cAAKuB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrDtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjFtB,OAAA;cAAKuB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,UAAU;QACb,oBACEtB,OAAA;UAAKuB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjElB,OAAA;YAAKuB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1BlB,OAAA;cAAKuB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtDtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3EtB,OAAA;cAAKuB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,SAAS;QACZ,oBACEtB,OAAA;UAAKuB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjElB,OAAA;YAAKuB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1BlB,OAAA;cAAKuB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpDtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvEtB,OAAA;cAAKuB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,UAAU;QACb,oBACEtB,OAAA;UAAKuB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjElB,OAAA;YAAKuB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1BlB,OAAA;cAAKuB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3DtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChEtB,OAAA;cAAKuB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,MAAM;QACT,oBACEtB,OAAA;UAAKuB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjElB,OAAA;YAAKuB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1BlB,OAAA;cAAKuB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClDtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEtB,OAAA;cAAKuB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,SAAS;QACZ,oBACEtB,OAAA;UAAKuB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjElB,OAAA;YAAKuB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1BlB,OAAA;cAAKuB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrDtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrEtB,OAAA;cAAKuB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,MAAM;QACT,oBACEtB,OAAA;UAAKuB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjElB,OAAA;YAAKuB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1BlB,OAAA;cAAKuB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClDtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrEtB,OAAA;cAAKuB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,MAAM;QACT,oBACEtB,OAAA;UAAKuB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjElB,OAAA;YAAKuB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1BlB,OAAA;cAAKuB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzDtB,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzEtB,OAAA;cAAKuB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;QACE,oBAAOtB,OAAA,CAACJ,YAAY;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC3B;EACF,CAAC;EAED,oBACEtB,OAAA,CAAAE,SAAA;IAAAgB,QAAA,gBACElB,OAAA,CAACN,WAAW;MAAAwB,QAAA,EACTM,mBAAmB,CAAC;IAAC;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACdtB,OAAA,CAACF,UAAU;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACd,CAAC;AAEP,CAAC;AAAClB,EAAA,CAhKID,YAAsB;EAAA,QAG8DX,gBAAgB,EACtEC,aAAa;AAAA;AAAAgC,EAAA,GAJ3CtB,YAAsB;AAkK5B,eAAeA,YAAY;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}