{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleLoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleLoginPage = () => {\n  _s();\n  const {\n    isLoading,\n    error,\n    simulateLogin,\n    isAuthenticated,\n    user\n  } = useSimpleAuth();\n  console.log('SimpleLoginPage - Estado:', {\n    isLoading,\n    isAuthenticated,\n    user: !!user\n  });\n\n  // Se já está autenticado, mostrar sucesso\n  if (isAuthenticated && user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card max-w-md w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-green-400 mb-4\",\n            children: \"\\u2705 Login Realizado!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-muted mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Bem-vindo, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: user.nick\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 29\n              }, this), \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: [\"UID: \", user.uid]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: [\"Email: \", user.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-green-300 mb-4\",\n              children: \"\\uD83C\\uDFAE Agora voc\\xEA pode acessar o jogo!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.href = '/game',\n              className: \"btn-primary\",\n              children: \"Ir para o Jogo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card max-w-md w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-2\",\n          children: \"\\uD83C\\uDFAE SHACK Web Game\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-muted\",\n          children: \"Login Simplificado (Modo Teste)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 text-red-100 p-4 rounded-lg mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card bg-bg-tertiary\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"\\uD83E\\uDDEA Modo de Teste\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-text-muted text-sm mb-4\",\n            children: \"Este \\xE9 um login simulado para testar o React sem o Flask. Clique no bot\\xE3o abaixo para simular um login bem-sucedido.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: simulateLogin,\n            disabled: isLoading,\n            className: `w-full py-3 px-4 rounded-lg font-medium transition-colors ${isLoading ? 'bg-gray-600 text-gray-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'}`,\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 19\n              }, this), \"Simulando Login...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this) : '🚀 Simular Login'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card bg-yellow-900 border-yellow-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-yellow-100 mb-2\",\n            children: \"\\u26A0\\uFE0F Aviso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-yellow-200 text-sm\",\n            children: \"Esta \\xE9 uma vers\\xE3o de teste. O Flask backend ser\\xE1 conectado posteriormente.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 text-center text-text-muted text-xs\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Vers\\xE3o de teste - React funcionando \\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleLoginPage, \"t2PfGeDPWHuzbdncO4qUB5aIHFo=\", false, function () {\n  return [useSimpleAuth];\n});\n_c = SimpleLoginPage;\nexport default SimpleLoginPage;\nvar _c;\n$RefreshReg$(_c, \"SimpleLoginPage\");", "map": {"version": 3, "names": ["React", "useSimpleAuth", "jsxDEV", "_jsxDEV", "SimpleLoginPage", "_s", "isLoading", "error", "simulateLogin", "isAuthenticated", "user", "console", "log", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "uid", "email", "onClick", "window", "location", "href", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleLoginPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\n\nconst SimpleLoginPage: React.FC = () => {\n  const { isLoading, error, simulateLogin, isAuthenticated, user } = useSimpleAuth();\n\n  console.log('SimpleLoginPage - Estado:', { isLoading, isAuthenticated, user: !!user });\n\n  // Se já está autenticado, mostrar sucesso\n  if (isAuthenticated && user) {\n    return (\n      <div className=\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\">\n        <div className=\"card max-w-md w-full\">\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-bold text-green-400 mb-4\">\n              ✅ Login Realizado!\n            </h1>\n            <div className=\"text-text-muted mb-6\">\n              <p>Bem-vindo, <strong>{user.nick}</strong>!</p>\n              <p className=\"text-sm\">UID: {user.uid}</p>\n              <p className=\"text-sm\">Email: {user.email}</p>\n            </div>\n            <div className=\"text-center\">\n              <p className=\"text-sm text-green-300 mb-4\">\n                🎮 Agora você pode acessar o jogo!\n              </p>\n              <button \n                onClick={() => window.location.href = '/game'}\n                className=\"btn-primary\"\n              >\n                Ir para o Jogo\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\">\n      <div className=\"card max-w-md w-full\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold mb-2\">\n            🎮 SHACK Web Game\n          </h1>\n          <p className=\"text-text-muted\">\n            Login Simplificado (Modo Teste)\n          </p>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900 border border-red-500 text-red-100 p-4 rounded-lg mb-6\">\n            <p className=\"text-sm\">{error}</p>\n          </div>\n        )}\n\n        <div className=\"space-y-6\">\n          <div className=\"card bg-bg-tertiary\">\n            <h3 className=\"text-lg font-semibold mb-4\">\n              🧪 Modo de Teste\n            </h3>\n            <p className=\"text-text-muted text-sm mb-4\">\n              Este é um login simulado para testar o React sem o Flask.\n              Clique no botão abaixo para simular um login bem-sucedido.\n            </p>\n            \n            <button\n              onClick={simulateLogin}\n              disabled={isLoading}\n              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${\n                isLoading\n                  ? 'bg-gray-600 text-gray-300 cursor-not-allowed'\n                  : 'bg-blue-600 hover:bg-blue-700 text-white'\n              }`}\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center\">\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                  Simulando Login...\n                </div>\n              ) : (\n                '🚀 Simular Login'\n              )}\n            </button>\n          </div>\n\n          <div className=\"card bg-yellow-900 border-yellow-500\">\n            <h3 className=\"text-lg font-semibold text-yellow-100 mb-2\">\n              ⚠️ Aviso\n            </h3>\n            <p className=\"text-yellow-200 text-sm\">\n              Esta é uma versão de teste. O Flask backend será conectado posteriormente.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"mt-8 text-center text-text-muted text-xs\">\n          <p>Versão de teste - React funcionando ✅</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleLoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC,SAAS;IAAEC,KAAK;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGT,aAAa,CAAC,CAAC;EAElFU,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;IAAEN,SAAS;IAAEG,eAAe;IAAEC,IAAI,EAAE,CAAC,CAACA;EAAK,CAAC,CAAC;;EAEtF;EACA,IAAID,eAAe,IAAIC,IAAI,EAAE;IAC3B,oBACEP,OAAA;MAAKU,SAAS,EAAC,+EAA+E;MAAAC,QAAA,eAC5FX,OAAA;QAAKU,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnCX,OAAA;UAAKU,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BX,OAAA;YAAIU,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLf,OAAA;YAAKU,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCX,OAAA;cAAAW,QAAA,GAAG,aAAW,eAAAX,OAAA;gBAAAW,QAAA,EAASJ,IAAI,CAACS;cAAI;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/Cf,OAAA;cAAGU,SAAS,EAAC,SAAS;cAAAC,QAAA,GAAC,OAAK,EAACJ,IAAI,CAACU,GAAG;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1Cf,OAAA;cAAGU,SAAS,EAAC,SAAS;cAAAC,QAAA,GAAC,SAAO,EAACJ,IAAI,CAACW,KAAK;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNf,OAAA;YAAKU,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BX,OAAA;cAAGU,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAE3C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJf,OAAA;cACEmB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAQ;cAC9CZ,SAAS,EAAC,aAAa;cAAAC,QAAA,EACxB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEf,OAAA;IAAKU,SAAS,EAAC,+EAA+E;IAAAC,QAAA,eAC5FX,OAAA;MAAKU,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCX,OAAA;QAAKU,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BX,OAAA;UAAIU,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLf,OAAA;UAAGU,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAELX,KAAK,iBACJJ,OAAA;QAAKU,SAAS,EAAC,mEAAmE;QAAAC,QAAA,eAChFX,OAAA;UAAGU,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAEP;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CACN,eAEDf,OAAA;QAAKU,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBX,OAAA;UAAKU,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCX,OAAA;YAAIU,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLf,OAAA;YAAGU,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAG5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJf,OAAA;YACEmB,OAAO,EAAEd,aAAc;YACvBkB,QAAQ,EAAEpB,SAAU;YACpBO,SAAS,EAAE,6DACTP,SAAS,GACL,8CAA8C,GAC9C,0CAA0C,EAC7C;YAAAQ,QAAA,EAEFR,SAAS,gBACRH,OAAA;cAAKU,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CX,OAAA;gBAAKU,SAAS,EAAC;cAAgE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,sBAExF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENf,OAAA;UAAKU,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACnDX,OAAA;YAAIU,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAE3D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLf,OAAA;YAAGU,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENf,OAAA;QAAKU,SAAS,EAAC,0CAA0C;QAAAC,QAAA,eACvDX,OAAA;UAAAW,QAAA,EAAG;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACb,EAAA,CApGID,eAAyB;EAAA,QACsCH,aAAa;AAAA;AAAA0B,EAAA,GAD5EvB,eAAyB;AAsG/B,eAAeA,eAAe;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}