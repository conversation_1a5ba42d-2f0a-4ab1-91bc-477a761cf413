import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../stores/authStore';
import { usePlayer } from '../stores/playerStore';
import gameApi from '../services/gameApi';

interface TerminalLine {
  id: string;
  type: 'input' | 'output' | 'error' | 'system';
  content: string;
  timestamp: Date;
}

const TerminalPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { currentPlayer } = usePlayer();
  
  const [terminalLines, setTerminalLines] = useState<TerminalLine[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [bruteforceStatus, setBruteforceStatus] = useState<any>(null);
  
  const terminalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isAuthenticated) {
      initializeTerminal();
      loadBruteforceStatus();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    // Auto-scroll para o final
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [terminalLines]);

  const initializeTerminal = () => {
    const welcomeLines: TerminalLine[] = [
      {
        id: '1',
        type: 'system',
        content: '=== SHACK TERMINAL v2.0 ===',
        timestamp: new Date(),
      },
      {
        id: '2',
        type: 'system',
        content: `Bem-vindo, ${user?.nick || 'Hacker'}`,
        timestamp: new Date(),
      },
      {
        id: '3',
        type: 'system',
        content: 'Digite "help" para ver comandos disponíveis',
        timestamp: new Date(),
      },
      {
        id: '4',
        type: 'output',
        content: 'root@shack:~$ ',
        timestamp: new Date(),
      },
    ];

    setTerminalLines(welcomeLines);
  };

  const loadBruteforceStatus = async () => {
    try {
      const response = await gameApi.getBruteforceStatus();
      if (response.sucesso) {
        setBruteforceStatus(response.status);
      }
    } catch (error) {
      console.error('Erro ao carregar status do bruteforce:', error);
    }
  };

  const addLine = (type: TerminalLine['type'], content: string) => {
    const newLine: TerminalLine = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date(),
    };

    setTerminalLines(prev => [...prev, newLine]);
  };

  const executeCommand = async (command: string) => {
    // Adicionar comando digitado
    addLine('input', `root@shack:~$ ${command}`);

    const cmd = command.trim().toLowerCase();
    const args = cmd.split(' ');

    setIsExecuting(true);

    try {
      switch (args[0]) {
        case 'help':
          addLine('output', 'Comandos disponíveis:');
          addLine('output', '  help - Mostra esta ajuda');
          addLine('output', '  clear - Limpa o terminal');
          addLine('output', '  status - Mostra status do sistema');
          addLine('output', '  bruteforce <target> - Inicia ataque de força bruta');
          addLine('output', '  scan - Escaneia rede local');
          addLine('output', '  whoami - Mostra informações do usuário');
          addLine('output', '  ps - Lista processos ativos');
          break;

        case 'clear':
          setTerminalLines([]);
          addLine('output', 'root@shack:~$ ');
          break;

        case 'status':
          addLine('output', `Sistema: SHACK OS v2.0`);
          addLine('output', `Usuário: ${user?.nick || 'Unknown'}`);
          addLine('output', `IP: ${user?.ip || currentPlayer?.ip || 'Unknown'}`);
          addLine('output', `CPU: Nível ${currentPlayer?.cpu || 1}`);
          addLine('output', `RAM: Nível ${currentPlayer?.ram || 1}`);
          addLine('output', `Firewall: Nível ${currentPlayer?.firewall || 1}`);
          addLine('output', `Dinheiro: $${currentPlayer?.dinheiro?.toLocaleString() || '0'}`);
          break;

        case 'whoami':
          addLine('output', `${user?.nick || 'Unknown'}`);
          addLine('output', `UID: ${user?.uid || 'Unknown'}`);
          addLine('output', `Email: ${user?.email || 'Unknown'}`);
          addLine('output', `Nível: ${currentPlayer?.nivel || 1}`);
          break;

        case 'ps':
          addLine('output', 'PID  COMMAND');
          addLine('output', '1    systemd');
          addLine('output', '2    kthreadd');
          addLine('output', '3    shack-daemon');
          addLine('output', '4    network-scanner');
          if (bruteforceStatus?.ativo) {
            addLine('output', '5    bruteforce-engine');
          }
          break;

        case 'bruteforce':
          if (args.length < 2) {
            addLine('error', 'Uso: bruteforce <target>');
            break;
          }

          const target = args[1];
          addLine('output', `Iniciando ataque de força bruta contra ${target}...`);
          
          try {
            const response = await gameApi.executeBruteforce(target, 'default');
            
            if (response.sucesso) {
              addLine('output', `✅ Bruteforce iniciado com sucesso!`);
              addLine('output', `Target: ${target}`);
              addLine('output', `Wordlist: default.txt`);
              addLine('output', `Threads: ${currentPlayer?.bruteforce || 1}`);
              
              // Simular progresso
              setTimeout(() => {
                addLine('output', 'Testando senhas...');
              }, 1000);
              
              setTimeout(() => {
                addLine('output', response.mensagem || 'Ataque concluído');
              }, 3000);
            } else {
              addLine('error', response.mensagem || 'Falha no bruteforce');
            }
          } catch (error: any) {
            addLine('error', `Erro: ${error.message}`);
          }
          break;

        case 'scan':
          addLine('output', 'Escaneando rede local...');
          
          try {
            const response = await gameApi.scanTargets();
            
            if (response.sucesso && response.alvos) {
              addLine('output', `Encontrados ${response.alvos.length} alvos:`);
              response.alvos.slice(0, 5).forEach((alvo: any) => {
                addLine('output', `  ${alvo.ip} - ${alvo.nick} (Nível ${alvo.nivel})`);
              });
            } else {
              addLine('output', 'Nenhum alvo encontrado');
            }
          } catch (error: any) {
            addLine('error', `Erro no scan: ${error.message}`);
          }
          break;

        case 'exit':
          addLine('output', 'Saindo do terminal...');
          setTimeout(() => {
            window.history.back();
          }, 1000);
          break;

        default:
          if (cmd.trim()) {
            addLine('error', `Comando não encontrado: ${args[0]}`);
            addLine('output', 'Digite "help" para ver comandos disponíveis');
          }
          break;
      }
    } catch (error: any) {
      addLine('error', `Erro: ${error.message}`);
    } finally {
      setIsExecuting(false);
      addLine('output', 'root@shack:~$ ');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (currentInput.trim() && !isExecuting) {
      executeCommand(currentInput);
      setCurrentInput('');
    }
  };

  const getLineColor = (type: TerminalLine['type']) => {
    switch (type) {
      case 'input':
        return 'text-white';
      case 'output':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      case 'system':
        return 'text-blue-400';
      default:
        return 'text-gray-300';
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">🔒 Acesso Negado</h1>
          <p className="text-gray-400">Você precisa estar logado para acessar o terminal</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-black text-green-400 flex flex-col font-mono">
      {/* Header */}
      <div className="bg-gray-900 border-b border-green-400 p-2 flex-shrink-0">
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => window.history.back()}
            className="w-6 h-6 bg-gray-800 rounded flex items-center justify-center hover:bg-gray-700 text-green-400"
          >
            <span className="text-sm">←</span>
          </button>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">●</span>
            <span className="text-sm">SHACK Terminal</span>
            <span className="text-xs text-gray-500">- {user?.nick}@{user?.ip}</span>
          </div>
        </div>
      </div>

      {/* Terminal */}
      <div 
        ref={terminalRef}
        className="flex-1 p-4 overflow-y-auto bg-black"
      >
        <div className="space-y-1">
          {terminalLines.map((line) => (
            <div 
              key={line.id} 
              className={`${getLineColor(line.type)} text-sm leading-relaxed`}
            >
              {line.content}
            </div>
          ))}
          
          {/* Input line */}
          <form onSubmit={handleSubmit} className="flex items-center">
            <span className="text-green-400 mr-2">root@shack:~$</span>
            <input
              ref={inputRef}
              type="text"
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              className="flex-1 bg-transparent text-green-400 outline-none"
              disabled={isExecuting}
              autoFocus
            />
            {isExecuting && (
              <span className="text-yellow-400 ml-2 animate-pulse">⏳</span>
            )}
          </form>
        </div>
      </div>

      {/* Status bar */}
      <div className="bg-gray-900 border-t border-green-400 p-2 flex-shrink-0">
        <div className="flex justify-between items-center text-xs">
          <div className="flex space-x-4">
            <span>CPU: {currentPlayer?.cpu || 1}</span>
            <span>RAM: {currentPlayer?.ram || 1}</span>
            <span>BruteForce: {currentPlayer?.bruteforce || 1}</span>
          </div>
          <div className="flex space-x-4">
            {bruteforceStatus?.ativo && (
              <span className="text-yellow-400">🔨 BruteForce Ativo</span>
            )}
            <span>{new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TerminalPage;
