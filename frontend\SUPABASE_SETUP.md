# 🔧 CONFIGURAÇÃO DO SUPABASE PARA SHACK GAME

## 📋 Pré-requisitos

1. Conta no [Supabase](https://supabase.com)
2. Node.js instalado
3. Projeto React configurado

## 🚀 Passo a Passo

### 1. Criar Projeto no Supabase

1. Acesse [supabase.com](https://supabase.com)
2. Clique em "Start your project"
3. Crie uma nova organização (se necessário)
4. Clique em "New Project"
5. Preencha:
   - **Name**: `shack-game`
   - **Database Password**: Crie uma senha forte
   - **Region**: Escolha a região mais próxima
6. Clique em "Create new project"

### 2. Configurar Banco de Dados

1. No painel do Supabase, vá para **SQL Editor**
2. Clique em "New query"
3. Copie e cole todo o conteúdo do arquivo `supabase-schema.sql`
4. Clique em "Run" para executar o script
5. Verifique se todas as tabelas foram criadas em **Table Editor**

### 3. Configurar Variáveis de Ambiente

1. No painel do Supabase, vá para **Settings** > **API**
2. Copie as seguintes informações:
   - **Project URL**
   - **anon public key**

3. Crie o arquivo `.env` na pasta `frontend/`:

```env
# Supabase Configuration
REACT_APP_SUPABASE_URL=https://seu-projeto.supabase.co
REACT_APP_SUPABASE_ANON_KEY=sua-chave-anonima-aqui

# Game Configuration
REACT_APP_GAME_NAME=SHACK
REACT_APP_GAME_VERSION=1.0.0
```

### 4. Configurar Autenticação (Opcional)

Para autenticação mais robusta:

1. Vá para **Authentication** > **Settings**
2. Configure provedores de login (Email, Google, etc.)
3. Ajuste as políticas de RLS conforme necessário

### 5. Testar Conexão

1. Inicie o projeto: `npm start`
2. Acesse `/game`
3. Tente criar um novo jogador
4. Verifique se os dados aparecem no **Table Editor** do Supabase

## 📊 Estrutura do Banco

### Tabelas Principais

- **players**: Dados dos jogadores
- **player_apps**: Níveis dos aplicativos
- **notifications**: Notificações do jogo
- **chat_messages**: Mensagens do chat global
- **hack_logs**: Histórico de hacks
- **scan_targets**: Alvos escaneados

### Relacionamentos

```
players (1) ←→ (1) player_apps
players (1) ←→ (N) notifications
players (1) ←→ (N) chat_messages
players (1) ←→ (N) hack_logs (como atacante)
players (1) ←→ (N) hack_logs (como alvo)
players (1) ←→ (N) scan_targets
```

## 🔒 Segurança (RLS)

O Row Level Security está configurado para:

- **Jogadores**: Podem ver todos, editar apenas próprios dados
- **Apps**: Apenas próprios dados
- **Notificações**: Apenas próprias notificações
- **Chat**: Todos podem ler, apenas próprias mensagens
- **Logs**: Apenas logs que envolvem o jogador

## 🧪 Dados de Teste

O script inclui 3 jogadores de teste:

1. **Admin** (Nível 50, todos apps nível 10)
2. **Hacker01** (Nível 15, apps variados)
3. **NoobPlayer** (Nível 3, apps básicos)

## 🔧 Troubleshooting

### Erro de Conexão
- Verifique se as variáveis de ambiente estão corretas
- Confirme se o projeto Supabase está ativo

### Erro de Permissão
- Verifique se as políticas RLS estão configuradas
- Confirme se a chave anon está correta

### Tabelas não Criadas
- Execute o script SQL novamente
- Verifique se há erros no SQL Editor

## 📈 Monitoramento

No painel do Supabase você pode:

- **Database**: Ver dados em tempo real
- **Auth**: Gerenciar usuários
- **Storage**: Arquivos (se necessário)
- **Edge Functions**: Funções serverless
- **Logs**: Monitorar atividade

## 🚀 Deploy

Para produção:

1. Configure domínio personalizado no Supabase
2. Ajuste políticas de segurança
3. Configure backup automático
4. Monitore performance

## 📞 Suporte

- [Documentação Supabase](https://supabase.com/docs)
- [Discord Supabase](https://discord.supabase.com)
- [GitHub Issues](https://github.com/supabase/supabase/issues)

---

✅ **Configuração Completa!** O jogo agora está conectado ao Supabase e funcionando online! 🎮
