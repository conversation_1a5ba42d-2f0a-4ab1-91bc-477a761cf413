import React from 'react';
import { useSimpleAuth } from '../stores/simpleAuthStore';

const DebugPanel: React.FC = () => {
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    error, 
    login, 
    register, 
    simulateLogin,
    clearError 
  } = useSimpleAuth();

  const handleTestLogin = async () => {
    console.log('[DebugPanel] Testando login...');
    try {
      await login({
        email: '<EMAIL>',
        password: 'test123'
      });
    } catch (err) {
      console.error('[DebugPanel] Erro no login:', err);
    }
  };

  const handleTestRegister = async () => {
    console.log('[DebugPanel] Testando registro...');
    try {
      await register({
        nick: 'TestPlayer',
        email: '<EMAIL>',
        password: 'test123'
      });
    } catch (err) {
      console.error('[DebugPanel] Erro no registro:', err);
    }
  };

  const handleSimulateLogin = () => {
    console.log('[DebugPanel] Simulando login...');
    simulateLogin();
  };

  return (
    <div className="fixed top-4 right-4 bg-gray-800 border border-gray-600 rounded-lg p-4 text-white text-sm max-w-xs z-50">
      <h3 className="font-bold mb-2">🔧 Debug Panel</h3>
      
      <div className="space-y-2 mb-4">
        <div>Autenticado: {isAuthenticated ? '✅' : '❌'}</div>
        <div>Loading: {isLoading ? '⏳' : '✅'}</div>
        <div>Usuário: {user?.nick || 'Nenhum'}</div>
        {error && <div className="text-red-400">Erro: {error}</div>}
      </div>

      <div className="space-y-2">
        <button
          onClick={handleTestLogin}
          disabled={isLoading}
          className="w-full py-1 px-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded text-xs"
        >
          Teste Login
        </button>
        
        <button
          onClick={handleTestRegister}
          disabled={isLoading}
          className="w-full py-1 px-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded text-xs"
        >
          Teste Registro
        </button>
        
        <button
          onClick={handleSimulateLogin}
          disabled={isLoading}
          className="w-full py-1 px-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 rounded text-xs"
        >
          Simular Login
        </button>

        {error && (
          <button
            onClick={clearError}
            className="w-full py-1 px-2 bg-red-600 hover:bg-red-700 rounded text-xs"
          >
            Limpar Erro
          </button>
        )}
      </div>
    </div>
  );
};

export default DebugPanel;
