import React from 'react';

interface PlayerProfileProps {
  nick: string;
  ip: string;
  level: number;
  cash: number;
  shack: number;
  exp: number;
  maxExp: number;
}

const PlayerProfile: React.FC<PlayerProfileProps> = ({
  nick,
  ip,
  level,
  cash,
  shack,
  exp,
  maxExp
}) => {
  const expPercentage = (exp / maxExp) * 100;

  return (
    <div className="bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-sm rounded-xl p-4 border border-gray-700/50 shadow-xl">
      {/* Header com nick e status */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
            <span className="text-white font-bold text-lg">{nick.charAt(0).toUpperCase()}</span>
          </div>
          <div>
            <h2 className="text-xl font-bold text-white">{nick}</h2>
            <p className="text-xs text-gray-400 font-mono">IP: {ip}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="flex items-center space-x-1 text-green-400 text-xs">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>Atualizado</span>
          </div>
        </div>
      </div>

      {/* Stats principais */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <span className="text-yellow-400">💰</span>
            <span className="text-xs text-gray-400">Cash:</span>
          </div>
          <div className="text-lg font-bold text-yellow-400">${cash.toLocaleString()}</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <span className="text-blue-400">⭐</span>
            <span className="text-xs text-gray-400">Nível:</span>
          </div>
          <div className="text-lg font-bold text-blue-400">{level}</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <span className="text-purple-400">🧠</span>
            <span className="text-xs text-gray-400">Shack:</span>
          </div>
          <div className="text-lg font-bold text-purple-400">{shack}</div>
        </div>
      </div>

      {/* Barra de experiência */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-400">EXP: {exp} / {maxExp}</span>
          <span className="text-xs text-blue-400 font-semibold">{expPercentage.toFixed(1)}%</span>
        </div>
        
        <div className="relative">
          {/* Background da barra */}
          <div className="w-full h-3 bg-gray-700 rounded-full overflow-hidden shadow-inner">
            {/* Barra de progresso */}
            <div 
              className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-500 ease-out shadow-lg"
              style={{ width: `${Math.min(expPercentage, 100)}%` }}
            >
              {/* Efeito de brilho */}
              <div className="w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
            </div>
          </div>
          
          {/* Marcadores de nível */}
          <div className="absolute top-0 left-0 w-full h-full flex justify-between items-center px-1">
            {[...Array(5)].map((_, i) => (
              <div 
                key={i}
                className="w-0.5 h-2 bg-gray-600 rounded-full"
                style={{ opacity: i * 20 <= expPercentage ? 0.3 : 0.6 }}
              ></div>
            ))}
          </div>
        </div>
        
        {/* Próximo nível */}
        <div className="text-center">
          <span className="text-xs text-gray-500">
            {exp >= maxExp ? 'Nível máximo!' : `${maxExp - exp} EXP para o próximo nível`}
          </span>
        </div>
      </div>
    </div>
  );
};

export default PlayerProfile;
