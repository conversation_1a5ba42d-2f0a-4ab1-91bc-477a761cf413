import React from 'react';

interface PlayerProfileProps {
  nick: string;
  ip: string;
  level: number;
  cash: number;
  shack: number;
  exp: number;
  maxExp: number;
}

const PlayerProfile: React.FC<PlayerProfileProps> = ({
  nick,
  ip,
  level,
  cash,
  shack,
  exp,
  maxExp
}) => {
  const expPercentage = (exp / maxExp) * 100;

  return (
    <div className="bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 shadow-xl">
      {/* Nick do jogador */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
            <span className="text-white font-bold text-sm">{nick.charAt(0).toUpperCase()}</span>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">{nick}</h2>
            <p className="text-xs text-gray-400 font-mono">IP: {ip}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="flex items-center space-x-1 text-green-400 text-xs">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>Atualizado</span>
          </div>
        </div>
      </div>

      {/* Stats em linha horizontal */}
      <div className="flex justify-between items-center mb-6">
        {/* CPU RANK */}
        <div className="text-center">
          <div className="text-xs text-gray-400 mb-1">CPU RANK</div>
          <div className="text-lg font-bold text-orange-400">EXPERT</div>
          <div className="text-xs text-gray-500">4</div>
        </div>

        {/* Círculo central com nível */}
        <div className="relative">
          <div className="w-20 h-20 rounded-full border-4 border-blue-500 flex items-center justify-center bg-gray-800">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{level}</div>
              <div className="text-xs text-gray-400">LEVEL</div>
            </div>
          </div>
          {/* Círculo de progresso */}
          <svg className="absolute top-0 left-0 w-20 h-20 transform -rotate-90">
            <circle
              cx="40"
              cy="40"
              r="36"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
              className="text-gray-700"
            />
            <circle
              cx="40"
              cy="40"
              r="36"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
              strokeLinecap="round"
              className="text-blue-500"
              style={{
                strokeDasharray: `${2 * Math.PI * 36}`,
                strokeDashoffset: `${2 * Math.PI * 36 * (1 - expPercentage / 100)}`
              }}
            />
          </svg>
        </div>

        {/* IP SERVICES */}
        <div className="text-center">
          <div className="text-xs text-gray-400 mb-1">IP SERVICES</div>
          <div className="text-lg font-bold text-cyan-400">*************</div>
          <div className="text-xs text-gray-500">PING</div>
        </div>
      </div>

      {/* Cash e Shack */}
      <div className="flex justify-between items-center">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <span className="text-yellow-400">💰</span>
            <span className="text-xs text-gray-400">Cash:</span>
          </div>
          <div className="text-xl font-bold text-yellow-400">${cash.toLocaleString()}</div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <span className="text-purple-400">🧠</span>
            <span className="text-xs text-gray-400">Shack:</span>
          </div>
          <div className="text-xl font-bold text-purple-400">{shack}</div>
        </div>
      </div>
    </div>
  );
};

export default PlayerProfile;
