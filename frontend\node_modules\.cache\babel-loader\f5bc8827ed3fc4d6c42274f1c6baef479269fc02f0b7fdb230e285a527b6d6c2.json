{"ast": null, "code": "const reportWebVitals=onPerfEntry=>{if(onPerfEntry&&onPerfEntry instanceof Function){import('web-vitals').then(_ref=>{let{getCLS,getFID,getFCP,getLCP,getTTFB}=_ref;getCLS(onPerfEntry);getFID(onPerfEntry);getFCP(onPerfEntry);getLCP(onPerfEntry);getTTFB(onPerfEntry);});}};export default reportWebVitals;", "map": {"version": 3, "names": ["reportWebVitals", "onPerfEntry", "Function", "then", "_ref", "getCLS", "getFID", "getFCP", "getLCP", "getTTFB"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/reportWebVitals.ts"], "sourcesContent": ["import { ReportHandler } from 'web-vitals';\n\nconst reportWebVitals = (onPerfEntry?: ReportHandler) => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\nexport default reportWebVitals;\n"], "mappings": "AAEA,KAAM,CAAAA,eAAe,CAAIC,WAA2B,EAAK,CACvD,GAAIA,WAAW,EAAIA,WAAW,WAAY,CAAAC,QAAQ,CAAE,CAClD,MAAM,CAAC,YAAY,CAAC,CAACC,IAAI,CAACC,IAAA,EAAiD,IAAhD,CAAEC,MAAM,CAAEC,MAAM,CAAEC,MAAM,CAAEC,MAAM,CAAEC,OAAQ,CAAC,CAAAL,IAAA,CACpEC,MAAM,CAACJ,WAAW,CAAC,CACnBK,MAAM,CAACL,WAAW,CAAC,CACnBM,MAAM,CAACN,WAAW,CAAC,CACnBO,MAAM,CAACP,WAAW,CAAC,CACnBQ,OAAO,CAACR,WAAW,CAAC,CACtB,CAAC,CAAC,CACJ,CACF,CAAC,CAED,cAAe,CAAAD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}