from flask import request, abort
import time
import threading
from collections import defaultdict

class RateLimiter:
    """
    Simple rate limiting implementation to prevent brute force attacks
    """
    
    def __init__(self, app=None):
        self.app = app
        # Track requests per IP address
        self.request_history = defaultdict(list)
        self.lock = threading.Lock()
        
        # Default rate limits
        self.default_rate_limits = {
            'general': {'count': 100, 'per_seconds': 60},  # 100 requests per minute
            'login': {'count': 5, 'per_seconds': 60},      # 5 login attempts per minute
            'api': {'count': 30, 'per_seconds': 60}        # 30 API calls per minute
        }
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize with Flask app"""
        self.app = app
        
        # Register a before_request handler to check rate limits
        @app.before_request
        def check_rate_limit():
            # Get client IP address
            ip = request.remote_addr
            
            # Skip rate limiting for local development
            if ip in ('127.0.0.1', 'localhost', '::1') and app.debug:
                return None
            
            # Determine which rate limit to apply
            if 'login' in request.path.lower() or 'auth' in request.path.lower():
                limit_key = 'login'
            elif '/api/' in request.path.lower():
                limit_key = 'api'
            else:
                limit_key = 'general'
            
            # Apply the rate limit
            if not self._is_within_rate_limit(ip, limit_key):
                print(f"⚠️ Rate limit exceeded for IP: {ip} on {limit_key} endpoint")
                abort(429, description="Too many requests, please try again later")
        
        print("✅ Rate limiting middleware initialized")
        
        # Start cleanup thread to remove old request records
        cleanup_thread = threading.Thread(
            target=self._cleanup_request_history, 
            daemon=True
        )
        cleanup_thread.start()
    
    def _is_within_rate_limit(self, ip, limit_key):
        """Check if the request is within the rate limit"""
        with self.lock:
            # Get the applicable rate limit
            rate_limit = self.default_rate_limits.get(limit_key, self.default_rate_limits['general'])
            count = rate_limit['count']
            per_seconds = rate_limit['per_seconds']
            
            # Record the current request time
            current_time = time.time()
            self.request_history[ip].append(current_time)
            
            # Check the number of requests within the time window
            cutoff_time = current_time - per_seconds
            recent_requests = [t for t in self.request_history[ip] if t > cutoff_time]
            
            # Update the history with only recent requests
            self.request_history[ip] = recent_requests
            
            # Check if the client has exceeded the rate limit
            return len(recent_requests) <= count
    
    def _cleanup_request_history(self):
        """Periodically clean up old request history to prevent memory leaks"""
        while True:
            time.sleep(60)  # Run cleanup every minute
            try:
                current_time = time.time()
                with self.lock:
                    # Find the oldest time window we care about across all rate limits
                    max_window = max(limit['per_seconds'] for limit in self.default_rate_limits.values())
                    cutoff_time = current_time - max_window
                    
                    # Remove records older than the cutoff time
                    for ip in list(self.request_history.keys()):
                        self.request_history[ip] = [t for t in self.request_history[ip] if t > cutoff_time]
                        
                        # Remove empty lists to save memory
                        if not self.request_history[ip]:
                            del self.request_history[ip]
            except Exception as e:
                print(f"Error in rate limiter cleanup: {str(e)}")
                
    def set_rate_limit(self, key, count, per_seconds):
        """Set a custom rate limit"""
        with self.lock:
            self.default_rate_limits[key] = {'count': count, 'per_seconds': per_seconds}