import axios from 'axios';

// Configuração do cliente HTTP para o backend Flask
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar token de autenticação
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Interceptor para tratar respostas
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expirado ou inválido
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export interface BackendPlayer {
  uid: string;
  nick: string;
  email: string;
  ip: string;
  nivel: number;
  xp: number;
  dinheiro: number;
  // Apps
  cpu: number;
  firewall: number;
  antivirus: number;
  malware_kit: number;
  bruteforce: number;
  bankguard: number;
  proxyvpn: number;
  // Timestamps
  created_at: string;
  last_login: string;
}

export interface UpgradeResponse {
  sucesso: boolean;
  mensagem: string;
  nivel_novo?: number;
  custo_dinheiro?: number;
  custo_shacks?: number;
  xp_ganho?: number;
  nivel_jogador?: number;
  level_up?: boolean;
}

export class BackendService {
  private isBackendAvailable = true;

  // Verificar se o backend está disponível
  private async checkBackendHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/`, {
        method: 'GET',
        timeout: 5000
      } as any);
      return response.ok;
    } catch (error) {
      console.warn('Backend não disponível, usando dados mock');
      this.isBackendAvailable = false;
      return false;
    }
  }

  // ==================== AUTENTICAÇÃO ====================
  
  async login(email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {
    try {
      // Verificar se backend está disponível
      const backendOk = await this.checkBackendHealth();

      if (!backendOk) {
        // Fallback: usar dados mock
        return this.mockLogin(email, password);
      }

      const response = await apiClient.post('/api/auth/login', {
        email,
        password,
      });

      if (response.data.sucesso && response.data.token) {
        const token = response.data.token;

        // Salvar token para próximas requisições
        localStorage.setItem('auth_token', token);

        // Buscar dados do jogador após login
        const playerResult = await this.getPlayer();

        if (playerResult.success && playerResult.player) {
          return {
            success: true,
            token,
            player: playerResult.player
          };
        }
      }

      return { success: false, error: response.data.mensagem || 'Erro no login' };
    } catch (error: any) {
      console.warn('Erro no backend, usando fallback mock');
      return this.mockLogin(email, password);
    }
  }

  async register(nick: string, email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {
    try {
      // Verificar se backend está disponível
      const backendOk = await this.checkBackendHealth();

      if (!backendOk) {
        // Fallback: usar dados mock
        return this.mockRegister(nick, email, password);
      }

      const response = await apiClient.post('/api/auth/register', {
        nick,
        email,
        password,
      });

      if (response.data.sucesso && response.data.token) {
        const token = response.data.token;

        // Salvar token para próximas requisições
        localStorage.setItem('auth_token', token);

        // Buscar dados do jogador após registro
        const playerResult = await this.getPlayer();

        if (playerResult.success && playerResult.player) {
          return {
            success: true,
            token,
            player: playerResult.player
          };
        }
      }

      return {
        success: false,
        error: response.data.mensagem || 'Erro no registro'
      };
    } catch (error: any) {
      console.warn('Erro no backend, usando fallback mock para registro');
      return this.mockRegister(nick, email, password);
    }
  }

  // ==================== JOGADOR ====================

  async getPlayer(): Promise<{ success: boolean; player?: BackendPlayer; error?: string }> {
    try {
      // Se não há token, retornar erro
      const token = localStorage.getItem('auth_token');
      if (!token) {
        return { success: false, error: 'Token não encontrado' };
      }

      // Se é um token mock, retornar dados mock
      if (token.startsWith('mock-token-')) {
        return this.getMockPlayer();
      }

      const response = await apiClient.get('/api/jogador');

      if (response.data.sucesso) {
        return { success: true, player: response.data.jogador };
      }

      return { success: false, error: response.data.mensagem };
    } catch (error: any) {
      // Fallback para dados mock se houver erro
      const token = localStorage.getItem('auth_token');
      if (token) {
        console.warn('Erro ao carregar do backend, usando dados mock');
        return this.getMockPlayer();
      }

      return { success: false, error: error.response?.data?.mensagem || 'Erro ao carregar jogador' };
    }
  }

  // ==================== UPGRADES ====================

  async upgradeApp(appName: string, quantity: number = 1): Promise<{ success: boolean; data?: UpgradeResponse; error?: string }> {
    try {
      // Mapear nomes dos apps do React para o backend
      const appMapping: { [key: string]: string } = {
        antivirus: 'antivirus',
        bankguard: 'bankguard',
        bruteforce: 'bruteforce',
        sdk: 'cpu', // SDK no React = CPU no backend
        firewall: 'firewall',
        malwarekit: 'malware_kit',
        proxyvpn: 'proxyvpn',
        mineradora: 'mineradora', // Novo app
      };

      const backendAppName = appMapping[appName] || appName;

      const response = await apiClient.post('/api/appstore/comprar', {
        item: backendAppName,
        quantidade: quantity,
      });

      return { 
        success: response.data.sucesso,
        data: response.data,
        error: response.data.sucesso ? undefined : response.data.mensagem
      };
    } catch (error: any) {
      return { success: false, error: error.response?.data?.mensagem || 'Erro ao fazer upgrade' };
    }
  }

  // ==================== SCANNER ====================

  async scanTargets(): Promise<{ success: boolean; targets?: any[]; error?: string }> {
    try {
      const response = await apiClient.get('/api/scan');
      
      return { 
        success: response.data.sucesso,
        targets: response.data.alvos || [],
        error: response.data.sucesso ? undefined : response.data.mensagem
      };
    } catch (error: any) {
      return { success: false, error: error.response?.data?.mensagem || 'Erro ao escanear' };
    }
  }

  // ==================== CHAT ====================

  async getChatMessages(): Promise<{ success: boolean; messages?: any[]; error?: string }> {
    try {
      const response = await apiClient.get('/api/chat/messages');
      
      return { 
        success: response.data.sucesso,
        messages: response.data.mensagens || [],
        error: response.data.sucesso ? undefined : response.data.mensagem
      };
    } catch (error: any) {
      return { success: false, error: error.response?.data?.mensagem || 'Erro ao carregar chat' };
    }
  }

  async sendChatMessage(message: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await apiClient.post('/api/chat/send', {
        mensagem: message,
      });

      return { 
        success: response.data.sucesso,
        error: response.data.sucesso ? undefined : response.data.mensagem
      };
    } catch (error: any) {
      return { success: false, error: error.response?.data?.mensagem || 'Erro ao enviar mensagem' };
    }
  }

  // ==================== HACKING ====================

  async exploitTarget(targetData: any): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await apiClient.post('/api/transferir', targetData);
      
      return { 
        success: response.data.sucesso,
        data: response.data,
        error: response.data.sucesso ? undefined : response.data.mensagem
      };
    } catch (error: any) {
      return { success: false, error: error.response?.data?.mensagem || 'Erro ao exploitar alvo' };
    }
  }

  // ==================== UTILITÁRIOS ====================

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await apiClient.get('/api/status');
      return { success: true };
    } catch (error: any) {
      return { success: false, error: 'Erro de conexão com o servidor' };
    }
  }

  // Converter jogador do backend para formato do React
  convertBackendPlayer(backendPlayer: BackendPlayer): any {
    return {
      uid: backendPlayer.uid,
      nick: backendPlayer.nick,
      email: backendPlayer.email,
      ip: backendPlayer.ip,
      level: backendPlayer.nivel,
      xp: backendPlayer.xp,
      xpToNextLevel: 100, // Será calculado depois
      cash: backendPlayer.dinheiro,
      shack: (backendPlayer as any).shack || 0, // Nova moeda
      createdAt: backendPlayer.created_at,
      lastLogin: backendPlayer.last_login,
    };
  }

  // Converter apps do backend para formato do React
  convertBackendApps(backendPlayer: BackendPlayer): any {
    return {
      antivirus: backendPlayer.antivirus || 1,
      bankguard: backendPlayer.bankguard || 1,
      bruteforce: backendPlayer.bruteforce || 1,
      sdk: backendPlayer.cpu || 1, // CPU no backend = SDK no React
      firewall: backendPlayer.firewall || 1,
      malware_kit: backendPlayer.malware_kit || 1,
      proxyvpn: backendPlayer.proxyvpn || 1,
      mineradora: (backendPlayer as any).mineradora || 1, // Novo app
    };
  }
  // ==================== MÉTODOS MOCK (FALLBACK) ====================

  private async mockLogin(email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {
    // Simular delay de rede
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Dados mock do jogador
    const mockPlayer: BackendPlayer = {
      uid: 'test-user-123',
      nick: 'TestPlayer',
      email: email,
      ip: '*************',
      nivel: 5,
      xp: 250,
      dinheiro: 150,
      antivirus: 3,
      bankguard: 2,
      bruteforce: 4,
      cpu: 2,
      firewall: 3,
      malware_kit: 2,
      proxyvpn: 1,
      created_at: new Date().toISOString(),
      last_login: new Date().toISOString(),
    };

    const token = 'mock-token-' + Date.now();
    localStorage.setItem('auth_token', token);

    return {
      success: true,
      token,
      player: mockPlayer
    };
  }

  private async mockRegister(nick: string, email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {
    // Simular delay de rede
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Dados mock do novo jogador
    const mockPlayer: BackendPlayer = {
      uid: 'new-user-' + Date.now(),
      nick: nick,
      email: email,
      ip: '192.168.1.' + Math.floor(Math.random() * 255),
      nivel: 1,
      xp: 0,
      dinheiro: 10,
      antivirus: 1,
      bankguard: 1,
      bruteforce: 1,
      cpu: 1,
      firewall: 1,
      malware_kit: 1,
      proxyvpn: 1,
      created_at: new Date().toISOString(),
      last_login: new Date().toISOString(),
    };

    const token = 'mock-token-' + Date.now();
    localStorage.setItem('auth_token', token);

    return {
      success: true,
      token,
      player: mockPlayer
    };
  }

  private async getMockPlayer(): Promise<{ success: boolean; player?: BackendPlayer; error?: string }> {
    // Dados mock persistentes baseados no token
    const token = localStorage.getItem('auth_token') || '';
    const isNewUser = token.includes('new-user');

    const mockPlayer: BackendPlayer = {
      uid: isNewUser ? 'new-user-123' : 'test-user-123',
      nick: isNewUser ? 'NewPlayer' : 'TestPlayer',
      email: isNewUser ? '<EMAIL>' : '<EMAIL>',
      ip: '*************',
      nivel: isNewUser ? 1 : 5,
      xp: isNewUser ? 0 : 250,
      dinheiro: isNewUser ? 10 : 150,
      antivirus: isNewUser ? 1 : 3,
      bankguard: isNewUser ? 1 : 2,
      bruteforce: isNewUser ? 1 : 4,
      cpu: isNewUser ? 1 : 2,
      firewall: isNewUser ? 1 : 3,
      malware_kit: isNewUser ? 1 : 2,
      proxyvpn: isNewUser ? 1 : 1,
      created_at: new Date().toISOString(),
      last_login: new Date().toISOString(),
    };

    return {
      success: true,
      player: mockPlayer
    };
  }
}

export const backendService = new BackendService();
