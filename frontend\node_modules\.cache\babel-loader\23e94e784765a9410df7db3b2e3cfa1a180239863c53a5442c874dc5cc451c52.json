{"ast": null, "code": "var _s = $RefreshSig$();\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nexport const useSimpleAuthStore = create()(persist((set, get) => ({\n  // Estado inicial\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n  // Definir usuário\n  setUser: user => {\n    console.log('SimpleAuth - Definindo usuário:', user);\n    set({\n      user,\n      isAuthenticated: true,\n      error: null\n    });\n  },\n  // Definir token\n  setToken: token => {\n    console.log('SimpleAuth - Definindo token');\n    set({\n      token,\n      error: null\n    });\n  },\n  // Logout\n  logout: () => {\n    console.log('SimpleAuth - Fazendo logout');\n    set({\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null\n    });\n  },\n  // Limpar erro\n  clearError: () => {\n    set({\n      error: null\n    });\n  },\n  // Simulação de login para teste\n  simulateLogin: () => {\n    console.log('SimpleAuth - Simulando login...');\n    set({\n      isLoading: true\n    });\n\n    // Simular delay de API\n    setTimeout(() => {\n      const mockUser = {\n        uid: 'test-user-123',\n        nick: 'TestPlayer',\n        email: '<EMAIL>'\n      };\n      const mockToken = 'mock-jwt-token-123';\n      set({\n        user: mockUser,\n        token: mockToken,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      });\n      console.log('SimpleAuth - Login simulado com sucesso!');\n    }, 1000);\n  }\n}), {\n  name: 'simple-auth-storage',\n  partialize: state => ({\n    user: state.user,\n    token: state.token,\n    isAuthenticated: state.isAuthenticated\n  })\n}));\n\n// Hook simplificado\nexport const useSimpleAuth = () => {\n  _s();\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    setUser,\n    setToken,\n    logout,\n    clearError,\n    simulateLogin\n  } = useSimpleAuthStore();\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    // Ações\n    setUser,\n    setToken,\n    logout,\n    clearError,\n    simulateLogin,\n    // Computed\n    isLoggedIn: isAuthenticated && !!user\n  };\n};\n_s(useSimpleAuth, \"ITTFgxX7pD230Yg5gwD9uznIvDU=\", false, function () {\n  return [useSimpleAuthStore];\n});", "map": {"version": 3, "names": ["create", "persist", "useSimpleAuthStore", "set", "get", "user", "token", "isAuthenticated", "isLoading", "error", "setUser", "console", "log", "setToken", "logout", "clearError", "simulateLogin", "setTimeout", "mockUser", "uid", "nick", "email", "mockToken", "name", "partialize", "state", "useSimpleAuth", "_s", "isLoggedIn"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/simpleAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport mockApiService from '../services/mockApi';\n\nexport interface User {\n  uid: string;\n  nick: string;\n  email: string;\n}\n\ninterface SimpleAuthState {\n  // Estado\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n\n  // Ações simplificadas (sem API calls)\n  setUser: (user: User) => void;\n  setToken: (token: string) => void;\n  logout: () => void;\n  clearError: () => void;\n  \n  // Simulação de login para teste\n  simulateLogin: () => void;\n}\n\nexport const useSimpleAuthStore = create<SimpleAuthState>()(\n  persist(\n    (set, get) => ({\n      // Estado inicial\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // Definir usuário\n      setUser: (user: User) => {\n        console.log('SimpleAuth - Definindo usuário:', user);\n        set({\n          user,\n          isAuthenticated: true,\n          error: null,\n        });\n      },\n\n      // Definir token\n      setToken: (token: string) => {\n        console.log('SimpleAuth - Definindo token');\n        set({\n          token,\n          error: null,\n        });\n      },\n\n      // Logout\n      logout: () => {\n        console.log('SimpleAuth - Fazendo logout');\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: null,\n        });\n      },\n\n      // Limpar erro\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // Simulação de login para teste\n      simulateLogin: () => {\n        console.log('SimpleAuth - Simulando login...');\n        set({ isLoading: true });\n        \n        // Simular delay de API\n        setTimeout(() => {\n          const mockUser: User = {\n            uid: 'test-user-123',\n            nick: 'TestPlayer',\n            email: '<EMAIL>'\n          };\n          \n          const mockToken = 'mock-jwt-token-123';\n          \n          set({\n            user: mockUser,\n            token: mockToken,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n          \n          console.log('SimpleAuth - Login simulado com sucesso!');\n        }, 1000);\n      },\n    }),\n    {\n      name: 'simple-auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// Hook simplificado\nexport const useSimpleAuth = () => {\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    setUser,\n    setToken,\n    logout,\n    clearError,\n    simulateLogin,\n  } = useSimpleAuthStore();\n\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    \n    // Ações\n    setUser,\n    setToken,\n    logout,\n    clearError,\n    simulateLogin,\n    \n    // Computed\n    isLoggedIn: isAuthenticated && !!user,\n  };\n};\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AA2B5C,OAAO,MAAMC,kBAAkB,GAAGF,MAAM,CAAkB,CAAC,CACzDC,OAAO,CACL,CAACE,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEX;EACAC,OAAO,EAAGL,IAAU,IAAK;IACvBM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEP,IAAI,CAAC;IACpDF,GAAG,CAAC;MACFE,IAAI;MACJE,eAAe,EAAE,IAAI;MACrBE,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED;EACAI,QAAQ,EAAGP,KAAa,IAAK;IAC3BK,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CT,GAAG,CAAC;MACFG,KAAK;MACLG,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED;EACAK,MAAM,EAAEA,CAAA,KAAM;IACZH,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1CT,GAAG,CAAC;MACFE,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE,KAAK;MACtBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED;EACAM,UAAU,EAAEA,CAAA,KAAM;IAChBZ,GAAG,CAAC;MAAEM,KAAK,EAAE;IAAK,CAAC,CAAC;EACtB,CAAC;EAED;EACAO,aAAa,EAAEA,CAAA,KAAM;IACnBL,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CT,GAAG,CAAC;MAAEK,SAAS,EAAE;IAAK,CAAC,CAAC;;IAExB;IACAS,UAAU,CAAC,MAAM;MACf,MAAMC,QAAc,GAAG;QACrBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE;MACT,CAAC;MAED,MAAMC,SAAS,GAAG,oBAAoB;MAEtCnB,GAAG,CAAC;QACFE,IAAI,EAAEa,QAAQ;QACdZ,KAAK,EAAEgB,SAAS;QAChBf,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;MAEFE,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC,CAAC,EACF;EACEW,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAGC,KAAK,KAAM;IACtBpB,IAAI,EAAEoB,KAAK,CAACpB,IAAI;IAChBC,KAAK,EAAEmB,KAAK,CAACnB,KAAK;IAClBC,eAAe,EAAEkB,KAAK,CAAClB;EACzB,CAAC;AACH,CACF,CACF,CAAC;;AAED;AACA,OAAO,MAAMmB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IACJtB,IAAI;IACJC,KAAK;IACLC,eAAe;IACfC,SAAS;IACTC,KAAK;IACLC,OAAO;IACPG,QAAQ;IACRC,MAAM;IACNC,UAAU;IACVC;EACF,CAAC,GAAGd,kBAAkB,CAAC,CAAC;EAExB,OAAO;IACL;IACAG,IAAI;IACJC,KAAK;IACLC,eAAe;IACfC,SAAS;IACTC,KAAK;IAEL;IACAC,OAAO;IACPG,QAAQ;IACRC,MAAM;IACNC,UAAU;IACVC,aAAa;IAEb;IACAY,UAAU,EAAErB,eAAe,IAAI,CAAC,CAACF;EACnC,CAAC;AACH,CAAC;AAACsB,EAAA,CAhCWD,aAAa;EAAA,QAYpBxB,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}