import React, { forwardRef } from 'react';

interface ModernInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  variant?: 'default' | 'glass' | 'neon';
  helperText?: string;
}

const ModernInput = forwardRef<HTMLInputElement, ModernInputProps>(({
  label,
  error,
  icon,
  iconPosition = 'left',
  variant = 'default',
  helperText,
  className = '',
  ...props
}, ref) => {
  const baseClasses = 'w-full px-4 py-3 rounded-lg transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed';

  const variantClasses = {
    default: 'bg-gray-800/80 backdrop-blur-sm border border-gray-700/50 text-white placeholder-gray-400 focus:bg-gray-800/90 focus:border-blue-500/70 focus:ring-blue-500/20',
    glass: 'bg-white/5 backdrop-blur-md border border-white/10 text-white placeholder-gray-400 focus:bg-white/10 focus:border-white/30 focus:ring-white/20',
    neon: 'bg-gray-900/90 backdrop-blur-sm border border-cyan-500/50 text-cyan-100 placeholder-cyan-400/60 focus:border-cyan-400/70 focus:ring-cyan-500/20 shadow-lg shadow-cyan-500/10',
  };

  const errorClasses = error ? 'border-red-500/70 focus:border-red-500 focus:ring-red-500/20' : '';

  const inputClasses = [
    baseClasses,
    variantClasses[variant],
    errorClasses,
    icon ? (iconPosition === 'left' ? 'pl-12' : 'pr-12') : '',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-gray-300">
          {label}
        </label>
      )}
      
      <div className="relative">
        {icon && (
          <div className={`absolute inset-y-0 ${iconPosition === 'left' ? 'left-0 pl-3' : 'right-0 pr-3'} flex items-center pointer-events-none`}>
            <span className="text-gray-400">{icon}</span>
          </div>
        )}
        
        <input
          ref={ref}
          className={inputClasses}
          {...props}
        />
      </div>
      
      {error && (
        <p className="text-sm text-red-400 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="text-sm text-gray-400">{helperText}</p>
      )}
    </div>
  );
});

ModernInput.displayName = 'ModernInput';

export default ModernInput;
