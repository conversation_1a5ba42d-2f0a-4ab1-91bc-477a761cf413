{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\App.simple.tsx\";\nimport React from 'react';\nimport './styles/globals.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-bg-primary text-text-primary p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold text-center mb-8\",\n        children: \"\\uD83C\\uDFAE SHACK Web Game\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-semibold mb-4\",\n          children: \"React App Funcionando!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-secondary mb-6\",\n          children: \"Se voc\\xEA est\\xE1 vendo esta mensagem, o React est\\xE1 funcionando corretamente.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card bg-green-900 border-green-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-green-100 mb-2\",\n              children: \"\\u2705 React Carregado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-200 text-sm\",\n              children: \"Aplica\\xE7\\xE3o React inicializada com sucesso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card bg-blue-900 border-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-blue-100 mb-2\",\n              children: \"\\uD83C\\uDFA8 Tailwind CSS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-200 text-sm\",\n              children: \"Estilos carregados e funcionando\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => alert('React funcionando!'),\n            className: \"btn-primary\",\n            children: \"Testar Intera\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 text-center text-text-muted\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Pr\\xF3ximo passo: Ativar autentica\\xE7\\xE3o e componentes completos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "alert", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/App.simple.tsx"], "sourcesContent": ["import React from 'react';\nimport './styles/globals.css';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"text-4xl font-bold text-center mb-8\">\n          🎮 SHACK Web Game\n        </h1>\n        \n        <div className=\"card text-center\">\n          <h2 className=\"text-2xl font-semibold mb-4\">\n            React App Funcionando!\n          </h2>\n          <p className=\"text-text-secondary mb-6\">\n            Se você está vendo esta mensagem, o React está funcionando corretamente.\n          </p>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"card bg-green-900 border-green-500\">\n              <h3 className=\"text-lg font-semibold text-green-100 mb-2\">\n                ✅ React Carregado\n              </h3>\n              <p className=\"text-green-200 text-sm\">\n                Aplicação React inicializada com sucesso\n              </p>\n            </div>\n            \n            <div className=\"card bg-blue-900 border-blue-500\">\n              <h3 className=\"text-lg font-semibold text-blue-100 mb-2\">\n                🎨 Tailwind CSS\n              </h3>\n              <p className=\"text-blue-200 text-sm\">\n                Estilos carregados e funcionando\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"mt-6\">\n            <button \n              onClick={() => alert('React funcionando!')}\n              className=\"btn-primary\"\n            >\n              Testar Interação\n            </button>\n          </div>\n        </div>\n        \n        <div className=\"mt-8 text-center text-text-muted\">\n          <p>Próximo passo: Ativar autenticação e componentes completos</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,kDAAkD;IAAAC,QAAA,eAC/DH,OAAA;MAAKE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCH,OAAA;QAAIE,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAEpD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELP,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BH,OAAA;UAAIE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAE5C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLP,OAAA;UAAGE,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJP,OAAA;UAAKE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDH,OAAA;YAAKE,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACjDH,OAAA;cAAIE,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAE1D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLP,OAAA;cAAGE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENP,OAAA;YAAKE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CH,OAAA;cAAIE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLP,OAAA;cAAGE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBH,OAAA;YACEQ,OAAO,EAAEA,CAAA,KAAMC,KAAK,CAAC,oBAAoB,CAAE;YAC3CP,SAAS,EAAC,aAAa;YAAAC,QAAA,EACxB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/CH,OAAA;UAAAG,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACG,EAAA,GApDQT,GAAG;AAsDZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}