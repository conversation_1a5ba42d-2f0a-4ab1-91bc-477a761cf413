import { useState, useEffect, useCallback, useRef } from 'react';
import gameService, { ScanTarget, ExploitedTarget, ConnectionStatus, BruteforceStatus } from '../services/gameService';

interface GameSystemState {
  // Player data
  currentPlayer: any;
  isLoadingPlayer: boolean;
  playerError: string | null;
  
  // Scan system
  scanTargets: ScanTarget[];
  isScanning: boolean;
  scanError: string | null;
  
  // Exploit system
  exploitedTarget: ExploitedTarget | null;
  isExploiting: boolean;
  exploitError: string | null;
  
  // Terminal system
  activeConnections: ConnectionStatus[];
  isLoadingConnections: boolean;
  connectionError: string | null;
  
  // Bruteforce system
  activeBruteforces: Map<string, BruteforceStatus>;
  
  // Transfer system
  isTransferring: boolean;
  transferError: string | null;
}

export const useGameSystem = () => {
  const [state, setState] = useState<GameSystemState>({
    currentPlayer: null,
    isLoadingPlayer: false,
    playerError: null,
    scanTargets: [],
    isScanning: false,
    scanError: null,
    exploitedTarget: null,
    isExploiting: false,
    exploitError: null,
    activeConnections: [],
    isLoadingConnections: false,
    connectionError: null,
    activeBruteforces: new Map(),
    isTransferring: false,
    transferError: null,
  });

  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // === PLAYER DATA ===
  const loadPlayerData = useCallback(async (forceRefresh = false) => {
    setState(prev => ({ ...prev, isLoadingPlayer: true, playerError: null }));
    
    try {
      const playerData = await gameService.loadPlayerData(forceRefresh);
      setState(prev => ({ 
        ...prev, 
        currentPlayer: playerData, 
        isLoadingPlayer: false 
      }));
      return playerData;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        playerError: error instanceof Error ? error.message : 'Erro ao carregar dados',
        isLoadingPlayer: false 
      }));
      throw error;
    }
  }, []);

  // === SCAN SYSTEM ===
  const performQuickScan = useCallback(async () => {
    setState(prev => ({ ...prev, isScanning: true, scanError: null }));
    
    try {
      const targets = await gameService.performQuickScan();
      setState(prev => ({ 
        ...prev, 
        scanTargets: targets, 
        isScanning: false 
      }));
      return targets;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        scanError: error instanceof Error ? error.message : 'Erro no scan',
        isScanning: false 
      }));
      throw error;
    }
  }, []);

  const performAdvancedScan = useCallback(async (targetIp: string) => {
    setState(prev => ({ ...prev, isScanning: true, scanError: null }));
    
    try {
      const targets = await gameService.performAdvancedScan(targetIp);
      setState(prev => ({ 
        ...prev, 
        scanTargets: targets, 
        isScanning: false 
      }));
      return targets;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        scanError: error instanceof Error ? error.message : 'Erro no scan',
        isScanning: false 
      }));
      throw error;
    }
  }, []);

  // === EXPLOIT SYSTEM ===
  const performExploit = useCallback(async (target: ScanTarget) => {
    setState(prev => ({ ...prev, isExploiting: true, exploitError: null }));
    
    try {
      const exploitedTarget = await gameService.performExploit(target);
      setState(prev => ({ 
        ...prev, 
        exploitedTarget, 
        isExploiting: false 
      }));
      
      // Atualizar conexões ativas
      await loadActiveConnections();
      
      return exploitedTarget;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        exploitError: error instanceof Error ? error.message : 'Erro no exploit',
        isExploiting: false 
      }));
      throw error;
    }
  }, []);

  // === TERMINAL SYSTEM ===
  const loadActiveConnections = useCallback(async () => {
    setState(prev => ({ ...prev, isLoadingConnections: true, connectionError: null }));
    
    try {
      const connections = await gameService.getActiveConnections();
      setState(prev => ({ 
        ...prev, 
        activeConnections: connections, 
        isLoadingConnections: false 
      }));
      return connections;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        connectionError: error instanceof Error ? error.message : 'Erro ao carregar conexões',
        isLoadingConnections: false 
      }));
      throw error;
    }
  }, []);

  const startBruteforce = useCallback(async (connectionId: string, targetIp: string) => {
    try {
      const bruteforceStatus = await gameService.startBruteforce(connectionId, targetIp);
      
      setState(prev => {
        const newBruteforces = new Map(prev.activeBruteforces);
        newBruteforces.set(targetIp, bruteforceStatus);
        return { ...prev, activeBruteforces: newBruteforces };
      });
      
      return bruteforceStatus;
    } catch (error) {
      console.error('Erro ao iniciar bruteforce:', error);
      throw error;
    }
  }, []);

  const closeConnection = useCallback(async (connectionId: string, targetIp: string) => {
    try {
      await gameService.closeConnection(connectionId, targetIp);
      
      // Atualizar estado local
      setState(prev => {
        const newConnections = prev.activeConnections.filter(conn => conn.targetIp !== targetIp);
        const newBruteforces = new Map(prev.activeBruteforces);
        newBruteforces.delete(targetIp);
        
        return {
          ...prev,
          activeConnections: newConnections,
          activeBruteforces: newBruteforces,
          exploitedTarget: prev.exploitedTarget?.ip === targetIp ? null : prev.exploitedTarget
        };
      });
    } catch (error) {
      console.error('Erro ao fechar conexão:', error);
      throw error;
    }
  }, []);

  // === TRANSFER SYSTEM ===
  const performBankTransfer = useCallback(async (targetIp: string, percentage: number) => {
    setState(prev => ({ ...prev, isTransferring: true, transferError: null }));
    
    try {
      const result = await gameService.performBankTransfer(targetIp, percentage);
      setState(prev => ({ ...prev, isTransferring: false }));
      
      // Atualizar dados do jogador após transferência
      await loadPlayerData(true);
      
      return result;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        transferError: error instanceof Error ? error.message : 'Erro na transferência',
        isTransferring: false 
      }));
      throw error;
    }
  }, [loadPlayerData]);

  // === AUTO UPDATE SYSTEM ===
  const startAutoUpdate = useCallback(() => {
    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current);
    }
    
    updateIntervalRef.current = setInterval(async () => {
      try {
        // Atualizar dados do jogador silenciosamente
        await loadPlayerData();
        
        // Atualizar conexões ativas se houver alguma
        if (state.activeConnections.length > 0) {
          await loadActiveConnections();
        }
      } catch (error) {
        console.error('Erro na atualização automática:', error);
      }
    }, 30000); // 30 segundos
  }, [loadPlayerData, loadActiveConnections, state.activeConnections.length]);

  const stopAutoUpdate = useCallback(() => {
    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current);
      updateIntervalRef.current = null;
    }
  }, []);

  // === CLEAR ERRORS ===
  const clearErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      playerError: null,
      scanError: null,
      exploitError: null,
      connectionError: null,
      transferError: null
    }));
  }, []);

  // === EFFECTS ===
  useEffect(() => {
    // Carregar dados iniciais
    loadPlayerData();
    loadActiveConnections();
    
    // Iniciar atualização automática
    startAutoUpdate();
    
    // Cleanup
    return () => {
      stopAutoUpdate();
      gameService.cleanup();
    };
  }, []);

  // Atualizar bruteforces em tempo real
  useEffect(() => {
    const interval = setInterval(() => {
      setState(prev => {
        const newBruteforces = new Map(prev.activeBruteforces);
        let hasChanges = false;
        
        newBruteforces.forEach((bruteforce, targetIp) => {
          if (bruteforce.isRunning && bruteforce.timeRemaining > 0) {
            bruteforce.timeRemaining--;
            bruteforce.progress = ((bruteforce.totalTime - bruteforce.timeRemaining) / bruteforce.totalTime) * 100;
            hasChanges = true;
            
            if (bruteforce.timeRemaining <= 0) {
              bruteforce.isRunning = false;
              // Atualizar conexão para liberar banco
              const connectionIndex = prev.activeConnections.findIndex(conn => conn.targetIp === targetIp);
              if (connectionIndex >= 0) {
                prev.activeConnections[connectionIndex].bankAccess = true;
              }
            }
          }
        });
        
        return hasChanges ? { ...prev, activeBruteforces: newBruteforces } : prev;
      });
    }, 1000);
    
    return () => clearInterval(interval);
  }, []);

  return {
    // State
    ...state,
    
    // Actions
    loadPlayerData,
    performQuickScan,
    performAdvancedScan,
    performExploit,
    loadActiveConnections,
    startBruteforce,
    closeConnection,
    performBankTransfer,
    clearErrors,
    
    // Utils
    startAutoUpdate,
    stopAutoUpdate
  };
};
