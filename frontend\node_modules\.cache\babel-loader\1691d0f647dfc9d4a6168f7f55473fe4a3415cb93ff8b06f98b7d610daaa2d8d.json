{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\ConfigPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfigPage = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const {\n    currentPlayer,\n    loadPlayerData\n  } = usePlayer();\n\n  // Estados para configurações\n  const [notifications, setNotifications] = useState(true);\n  const [soundEffects, setSoundEffects] = useState(true);\n  const [autoSave, setAutoSave] = useState(true);\n  const [theme, setTheme] = useState('dark');\n  const [language, setLanguage] = useState('pt-BR');\n\n  // Estados para mudança de senha\n  const [currentPassword, setCurrentPassword] = useState('');\n  const [newPassword, setNewPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [isChangingPassword, setIsChangingPassword] = useState(false);\n\n  // Estados para mensagens\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [errorMessage, setErrorMessage] = useState(null);\n  useEffect(() => {\n    loadSettings();\n  }, []);\n  const loadSettings = () => {\n    // Carregar configurações do localStorage\n    const savedSettings = localStorage.getItem('shack-settings');\n    if (savedSettings) {\n      var _settings$notificatio, _settings$soundEffect, _settings$autoSave, _settings$theme, _settings$language;\n      const settings = JSON.parse(savedSettings);\n      setNotifications((_settings$notificatio = settings.notifications) !== null && _settings$notificatio !== void 0 ? _settings$notificatio : true);\n      setSoundEffects((_settings$soundEffect = settings.soundEffects) !== null && _settings$soundEffect !== void 0 ? _settings$soundEffect : true);\n      setAutoSave((_settings$autoSave = settings.autoSave) !== null && _settings$autoSave !== void 0 ? _settings$autoSave : true);\n      setTheme((_settings$theme = settings.theme) !== null && _settings$theme !== void 0 ? _settings$theme : 'dark');\n      setLanguage((_settings$language = settings.language) !== null && _settings$language !== void 0 ? _settings$language : 'pt-BR');\n    }\n  };\n  const saveSettings = () => {\n    const settings = {\n      notifications,\n      soundEffects,\n      autoSave,\n      theme,\n      language\n    };\n    localStorage.setItem('shack-settings', JSON.stringify(settings));\n    setSuccessMessage('Configurações salvas com sucesso!');\n    setTimeout(() => setSuccessMessage(null), 3000);\n  };\n  const handlePasswordChange = async e => {\n    e.preventDefault();\n    if (newPassword !== confirmPassword) {\n      setErrorMessage('As senhas não coincidem');\n      return;\n    }\n    if (newPassword.length < 6) {\n      setErrorMessage('A nova senha deve ter pelo menos 6 caracteres');\n      return;\n    }\n    setIsChangingPassword(true);\n    setErrorMessage(null);\n    try {\n      const response = await gameApi.changePassword({\n        currentPassword,\n        newPassword\n      });\n      if (response.sucesso) {\n        setSuccessMessage('Senha alterada com sucesso!');\n        setCurrentPassword('');\n        setNewPassword('');\n        setConfirmPassword('');\n      } else {\n        setErrorMessage(response.mensagem || 'Erro ao alterar senha');\n      }\n    } catch (error) {\n      setErrorMessage(error.message || 'Erro de conexão');\n    } finally {\n      setIsChangingPassword(false);\n    }\n  };\n  const handleLogout = async () => {\n    if (confirm('Tem certeza que deseja sair?')) {\n      await logout();\n      window.location.href = '/';\n    }\n  };\n  const clearCache = () => {\n    if (confirm('Isso irá limpar todos os dados salvos localmente. Continuar?')) {\n      localStorage.clear();\n      sessionStorage.clear();\n      setSuccessMessage('Cache limpo com sucesso!');\n      setTimeout(() => {\n        window.location.reload();\n      }, 1000);\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-900 text-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"\\uD83D\\uDD12 Acesso Negado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Voc\\xEA precisa estar logado para acessar as configura\\xE7\\xF5es\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold\",\n            children: \"\\u2699\\uFE0F Configura\\xE7\\xF5es\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Prefer\\xEAncias do Sistema\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n      children: [successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-900 border border-green-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-300 text-sm\",\n          children: [\"\\u2705 \", successMessage]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-300 text-sm\",\n          children: [\"\\u274C \", errorMessage]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"\\uD83D\\uDC64 Informa\\xE7\\xF5es da Conta\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"Nick:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white\",\n              children: user === null || user === void 0 ? void 0 : user.nick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"Email:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white\",\n              children: user === null || user === void 0 ? void 0 : user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"IP:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white\",\n              children: (user === null || user === void 0 ? void 0 : user.ip) || (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.ip)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"N\\xEDvel:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white\",\n              children: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.nivel) || 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"\\uD83D\\uDD27 Configura\\xE7\\xF5es Gerais\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-300\",\n              children: \"Notifica\\xE7\\xF5es\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setNotifications(!notifications),\n              className: `w-12 h-6 rounded-full ${notifications ? 'bg-blue-600' : 'bg-gray-600'} relative transition-colors`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${notifications ? 'translate-x-7' : 'translate-x-1'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-300\",\n              children: \"Efeitos Sonoros\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSoundEffects(!soundEffects),\n              className: `w-12 h-6 rounded-full ${soundEffects ? 'bg-blue-600' : 'bg-gray-600'} relative transition-colors`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${soundEffects ? 'translate-x-7' : 'translate-x-1'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-300\",\n              children: \"Salvamento Autom\\xE1tico\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setAutoSave(!autoSave),\n              className: `w-12 h-6 rounded-full ${autoSave ? 'bg-blue-600' : 'bg-gray-600'} relative transition-colors`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${autoSave ? 'translate-x-7' : 'translate-x-1'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-300\",\n              children: \"Tema\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: theme,\n              onChange: e => setTheme(e.target.value),\n              className: \"bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"dark\",\n                children: \"Escuro\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"light\",\n                children: \"Claro\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"auto\",\n                children: \"Autom\\xE1tico\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-300\",\n              children: \"Idioma\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: language,\n              onChange: e => setLanguage(e.target.value),\n              className: \"bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"pt-BR\",\n                children: \"Portugu\\xEAs (BR)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"en-US\",\n                children: \"English (US)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"es-ES\",\n                children: \"Espa\\xF1ol\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: saveSettings,\n            className: \"w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold\",\n            children: \"Salvar Configura\\xE7\\xF5es\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"\\uD83D\\uDD12 Alterar Senha\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handlePasswordChange,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Senha Atual\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              value: currentPassword,\n              onChange: e => setCurrentPassword(e.target.value),\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isChangingPassword\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Nova Senha\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              value: newPassword,\n              onChange: e => setNewPassword(e.target.value),\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isChangingPassword\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Confirmar Nova Senha\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              value: confirmPassword,\n              onChange: e => setConfirmPassword(e.target.value),\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isChangingPassword\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isChangingPassword || !currentPassword || !newPassword || !confirmPassword,\n            className: \"w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n            children: isChangingPassword ? 'Alterando...' : 'Alterar Senha'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"\\uD83D\\uDEE0\\uFE0F A\\xE7\\xF5es Avan\\xE7adas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearCache,\n            className: \"w-full py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-semibold\",\n            children: \"Limpar Cache\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleLogout,\n            className: \"w-full py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold\",\n            children: \"Sair da Conta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"\\u2139\\uFE0F Informa\\xE7\\xF5es do Sistema\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"Vers\\xE3o:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white\",\n              children: \"1.0.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"Build:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white\",\n              children: \"React-2024\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"Servidor:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white\",\n              children: \"Online\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"Voltar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(ConfigPage, \"RJCP09oFabAObkWZHL9MLtpmrXU=\", false, function () {\n  return [useAuth, usePlayer];\n});\n_c = ConfigPage;\nexport default ConfigPage;\nvar _c;\n$RefreshReg$(_c, \"ConfigPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "usePlayer", "gameApi", "jsxDEV", "_jsxDEV", "ConfigPage", "_s", "user", "isAuthenticated", "logout", "currentPlayer", "loadPlayerData", "notifications", "setNotifications", "soundEffects", "setSoundEffects", "autoSave", "setAutoSave", "theme", "setTheme", "language", "setLanguage", "currentPassword", "setCurrentPassword", "newPassword", "setNewPassword", "confirmPassword", "setConfirmPassword", "isChangingPassword", "setIsChangingPassword", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "loadSettings", "savedSettings", "localStorage", "getItem", "_settings$notificatio", "_settings$soundEffect", "_settings$autoSave", "_settings$theme", "_settings$language", "settings", "JSON", "parse", "saveSettings", "setItem", "stringify", "setTimeout", "handlePasswordChange", "e", "preventDefault", "length", "response", "changePassword", "sucesso", "mensagem", "error", "message", "handleLogout", "confirm", "window", "location", "href", "clearCache", "clear", "sessionStorage", "reload", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "history", "back", "nick", "email", "ip", "nivel", "value", "onChange", "target", "onSubmit", "type", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/ConfigPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\nimport GameFooter from '../components/common/GameFooter';\n\nconst ConfigPage: React.FC = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const { currentPlayer, loadPlayerData } = usePlayer();\n  \n  // Estados para configurações\n  const [notifications, setNotifications] = useState(true);\n  const [soundEffects, setSoundEffects] = useState(true);\n  const [autoSave, setAutoSave] = useState(true);\n  const [theme, setTheme] = useState('dark');\n  const [language, setLanguage] = useState('pt-BR');\n  \n  // Estados para mudança de senha\n  const [currentPassword, setCurrentPassword] = useState('');\n  const [newPassword, setNewPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [isChangingPassword, setIsChangingPassword] = useState(false);\n  \n  // Estados para mensagens\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [errorMessage, setErrorMessage] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadSettings();\n  }, []);\n\n  const loadSettings = () => {\n    // Carregar configurações do localStorage\n    const savedSettings = localStorage.getItem('shack-settings');\n    if (savedSettings) {\n      const settings = JSON.parse(savedSettings);\n      setNotifications(settings.notifications ?? true);\n      setSoundEffects(settings.soundEffects ?? true);\n      setAutoSave(settings.autoSave ?? true);\n      setTheme(settings.theme ?? 'dark');\n      setLanguage(settings.language ?? 'pt-BR');\n    }\n  };\n\n  const saveSettings = () => {\n    const settings = {\n      notifications,\n      soundEffects,\n      autoSave,\n      theme,\n      language,\n    };\n    \n    localStorage.setItem('shack-settings', JSON.stringify(settings));\n    setSuccessMessage('Configurações salvas com sucesso!');\n    setTimeout(() => setSuccessMessage(null), 3000);\n  };\n\n  const handlePasswordChange = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (newPassword !== confirmPassword) {\n      setErrorMessage('As senhas não coincidem');\n      return;\n    }\n\n    if (newPassword.length < 6) {\n      setErrorMessage('A nova senha deve ter pelo menos 6 caracteres');\n      return;\n    }\n\n    setIsChangingPassword(true);\n    setErrorMessage(null);\n\n    try {\n      const response = await gameApi.changePassword({\n        currentPassword,\n        newPassword,\n      });\n\n      if (response.sucesso) {\n        setSuccessMessage('Senha alterada com sucesso!');\n        setCurrentPassword('');\n        setNewPassword('');\n        setConfirmPassword('');\n      } else {\n        setErrorMessage(response.mensagem || 'Erro ao alterar senha');\n      }\n    } catch (error: any) {\n      setErrorMessage(error.message || 'Erro de conexão');\n    } finally {\n      setIsChangingPassword(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    if (confirm('Tem certeza que deseja sair?')) {\n      await logout();\n      window.location.href = '/';\n    }\n  };\n\n  const clearCache = () => {\n    if (confirm('Isso irá limpar todos os dados salvos localmente. Continuar?')) {\n      localStorage.clear();\n      sessionStorage.clear();\n      setSuccessMessage('Cache limpo com sucesso!');\n      setTimeout(() => {\n        window.location.reload();\n      }, 1000);\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"h-screen bg-gray-900 text-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-4\">🔒 Acesso Negado</h1>\n          <p className=\"text-gray-400\">Você precisa estar logado para acessar as configurações</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n          >\n            <span className=\"text-lg\">←</span>\n          </button>\n          <div>\n            <h1 className=\"text-lg font-bold\">⚙️ Configurações</h1>\n            <p className=\"text-xs text-gray-400\">Preferências do Sistema</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-4\">\n        {/* Mensagens */}\n        {successMessage && (\n          <div className=\"bg-green-900 border border-green-500 rounded-lg p-3\">\n            <p className=\"text-green-300 text-sm\">✅ {successMessage}</p>\n          </div>\n        )}\n\n        {errorMessage && (\n          <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n            <p className=\"text-red-300 text-sm\">❌ {errorMessage}</p>\n          </div>\n        )}\n\n        {/* Informações da conta */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-4 text-white\">👤 Informações da Conta</h3>\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Nick:</span>\n              <span className=\"text-white\">{user?.nick}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Email:</span>\n              <span className=\"text-white\">{user?.email}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">IP:</span>\n              <span className=\"text-white\">{user?.ip || currentPlayer?.ip}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Nível:</span>\n              <span className=\"text-white\">{currentPlayer?.nivel || 1}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Configurações gerais */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-4 text-white\">🔧 Configurações Gerais</h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-300\">Notificações</span>\n              <button\n                onClick={() => setNotifications(!notifications)}\n                className={`w-12 h-6 rounded-full ${\n                  notifications ? 'bg-blue-600' : 'bg-gray-600'\n                } relative transition-colors`}\n              >\n                <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${\n                  notifications ? 'translate-x-7' : 'translate-x-1'\n                }`}></div>\n              </button>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-300\">Efeitos Sonoros</span>\n              <button\n                onClick={() => setSoundEffects(!soundEffects)}\n                className={`w-12 h-6 rounded-full ${\n                  soundEffects ? 'bg-blue-600' : 'bg-gray-600'\n                } relative transition-colors`}\n              >\n                <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${\n                  soundEffects ? 'translate-x-7' : 'translate-x-1'\n                }`}></div>\n              </button>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-300\">Salvamento Automático</span>\n              <button\n                onClick={() => setAutoSave(!autoSave)}\n                className={`w-12 h-6 rounded-full ${\n                  autoSave ? 'bg-blue-600' : 'bg-gray-600'\n                } relative transition-colors`}\n              >\n                <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${\n                  autoSave ? 'translate-x-7' : 'translate-x-1'\n                }`}></div>\n              </button>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-300\">Tema</span>\n              <select\n                value={theme}\n                onChange={(e) => setTheme(e.target.value)}\n                className=\"bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white\"\n              >\n                <option value=\"dark\">Escuro</option>\n                <option value=\"light\">Claro</option>\n                <option value=\"auto\">Automático</option>\n              </select>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-300\">Idioma</span>\n              <select\n                value={language}\n                onChange={(e) => setLanguage(e.target.value)}\n                className=\"bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white\"\n              >\n                <option value=\"pt-BR\">Português (BR)</option>\n                <option value=\"en-US\">English (US)</option>\n                <option value=\"es-ES\">Español</option>\n              </select>\n            </div>\n\n            <button\n              onClick={saveSettings}\n              className=\"w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold\"\n            >\n              Salvar Configurações\n            </button>\n          </div>\n        </div>\n\n        {/* Mudança de senha */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-4 text-white\">🔒 Alterar Senha</h3>\n          <form onSubmit={handlePasswordChange} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Senha Atual\n              </label>\n              <input\n                type=\"password\"\n                value={currentPassword}\n                onChange={(e) => setCurrentPassword(e.target.value)}\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isChangingPassword}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Nova Senha\n              </label>\n              <input\n                type=\"password\"\n                value={newPassword}\n                onChange={(e) => setNewPassword(e.target.value)}\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isChangingPassword}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Confirmar Nova Senha\n              </label>\n              <input\n                type=\"password\"\n                value={confirmPassword}\n                onChange={(e) => setConfirmPassword(e.target.value)}\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isChangingPassword}\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isChangingPassword || !currentPassword || !newPassword || !confirmPassword}\n              className=\"w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n            >\n              {isChangingPassword ? 'Alterando...' : 'Alterar Senha'}\n            </button>\n          </form>\n        </div>\n\n        {/* Ações avançadas */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-4 text-white\">🛠️ Ações Avançadas</h3>\n          <div className=\"space-y-3\">\n            <button\n              onClick={clearCache}\n              className=\"w-full py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-semibold\"\n            >\n              Limpar Cache\n            </button>\n\n            <button\n              onClick={handleLogout}\n              className=\"w-full py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold\"\n            >\n              Sair da Conta\n            </button>\n          </div>\n        </div>\n\n        {/* Informações do sistema */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-4 text-white\">ℹ️ Informações do Sistema</h3>\n          <div className=\"space-y-2 text-sm\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Versão:</span>\n              <span className=\"text-white\">1.0.0</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Build:</span>\n              <span className=\"text-white\">React-2024</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Servidor:</span>\n              <span className=\"text-white\">Online</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-center\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\"\n          >\n            <span className=\"text-lg\">←</span>\n            <span className=\"text-sm\">Voltar</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConfigPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1C,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEU,aAAa;IAAEC;EAAe,CAAC,GAAGV,SAAS,CAAC,CAAC;;EAErD;EACA,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,MAAM,CAAC;EAC1C,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,OAAO,CAAC;;EAEjD;EACA,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAgB,IAAI,CAAC;EAErEC,SAAS,CAAC,MAAM;IACdmC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC5D,IAAIF,aAAa,EAAE;MAAA,IAAAG,qBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,eAAA,EAAAC,kBAAA;MACjB,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACV,aAAa,CAAC;MAC1CtB,gBAAgB,EAAAyB,qBAAA,GAACK,QAAQ,CAAC/B,aAAa,cAAA0B,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAAC;MAChDvB,eAAe,EAAAwB,qBAAA,GAACI,QAAQ,CAAC7B,YAAY,cAAAyB,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAAC;MAC9CtB,WAAW,EAAAuB,kBAAA,GAACG,QAAQ,CAAC3B,QAAQ,cAAAwB,kBAAA,cAAAA,kBAAA,GAAI,IAAI,CAAC;MACtCrB,QAAQ,EAAAsB,eAAA,GAACE,QAAQ,CAACzB,KAAK,cAAAuB,eAAA,cAAAA,eAAA,GAAI,MAAM,CAAC;MAClCpB,WAAW,EAAAqB,kBAAA,GAACC,QAAQ,CAACvB,QAAQ,cAAAsB,kBAAA,cAAAA,kBAAA,GAAI,OAAO,CAAC;IAC3C;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMH,QAAQ,GAAG;MACf/B,aAAa;MACbE,YAAY;MACZE,QAAQ;MACRE,KAAK;MACLE;IACF,CAAC;IAEDgB,YAAY,CAACW,OAAO,CAAC,gBAAgB,EAAEH,IAAI,CAACI,SAAS,CAACL,QAAQ,CAAC,CAAC;IAChEZ,iBAAiB,CAAC,mCAAmC,CAAC;IACtDkB,UAAU,CAAC,MAAMlB,iBAAiB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EACjD,CAAC;EAED,MAAMmB,oBAAoB,GAAG,MAAOC,CAAkB,IAAK;IACzDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI5B,WAAW,KAAKE,eAAe,EAAE;MACnCO,eAAe,CAAC,yBAAyB,CAAC;MAC1C;IACF;IAEA,IAAIT,WAAW,CAAC6B,MAAM,GAAG,CAAC,EAAE;MAC1BpB,eAAe,CAAC,+CAA+C,CAAC;MAChE;IACF;IAEAJ,qBAAqB,CAAC,IAAI,CAAC;IAC3BI,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMpD,OAAO,CAACqD,cAAc,CAAC;QAC5CjC,eAAe;QACfE;MACF,CAAC,CAAC;MAEF,IAAI8B,QAAQ,CAACE,OAAO,EAAE;QACpBzB,iBAAiB,CAAC,6BAA6B,CAAC;QAChDR,kBAAkB,CAAC,EAAE,CAAC;QACtBE,cAAc,CAAC,EAAE,CAAC;QAClBE,kBAAkB,CAAC,EAAE,CAAC;MACxB,CAAC,MAAM;QACLM,eAAe,CAACqB,QAAQ,CAACG,QAAQ,IAAI,uBAAuB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBzB,eAAe,CAACyB,KAAK,CAACC,OAAO,IAAI,iBAAiB,CAAC;IACrD,CAAC,SAAS;MACR9B,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAM+B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIC,OAAO,CAAC,8BAA8B,CAAC,EAAE;MAC3C,MAAMpD,MAAM,CAAC,CAAC;MACdqD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;IAC5B;EACF,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIJ,OAAO,CAAC,8DAA8D,CAAC,EAAE;MAC3EzB,YAAY,CAAC8B,KAAK,CAAC,CAAC;MACpBC,cAAc,CAACD,KAAK,CAAC,CAAC;MACtBnC,iBAAiB,CAAC,0BAA0B,CAAC;MAC7CkB,UAAU,CAAC,MAAM;QACfa,MAAM,CAACC,QAAQ,CAACK,MAAM,CAAC,CAAC;MAC1B,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,IAAI,CAAC5D,eAAe,EAAE;IACpB,oBACEJ,OAAA;MAAKiE,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/ElE,OAAA;QAAKiE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlE,OAAA;UAAIiE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DtE,OAAA;UAAGiE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtE,OAAA;IAAKiE,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DlE,OAAA;MAAKiE,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrElE,OAAA;QAAKiE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClE,OAAA;UACEuE,OAAO,EAAEA,CAAA,KAAMb,MAAM,CAACc,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCR,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7FlE,OAAA;YAAMiE,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACTtE,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAIiE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDtE,OAAA;YAAGiE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtE,OAAA;MAAKiE,SAAS,EAAC,sCAAsC;MAAAC,QAAA,GAElDxC,cAAc,iBACb1B,OAAA;QAAKiE,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClElE,OAAA;UAAGiE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,SAAE,EAACxC,cAAc;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CACN,EAEA1C,YAAY,iBACX5B,OAAA;QAAKiE,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DlE,OAAA;UAAGiE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,SAAE,EAACtC,YAAY;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CACN,eAGDtE,OAAA;QAAKiE,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChElE,OAAA;UAAIiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFtE,OAAA;UAAKiE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlE,OAAA;YAAKiE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CtE,OAAA;cAAMiE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE/D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNtE,OAAA;YAAKiE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CtE,OAAA;cAAMiE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE/D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE;YAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNtE,OAAA;YAAKiE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1CtE,OAAA;cAAMiE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE,CAAA/D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,EAAE,MAAItE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsE,EAAE;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACNtE,OAAA;YAAKiE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CtE,OAAA;cAAMiE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE,CAAA5D,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuE,KAAK,KAAI;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtE,OAAA;QAAKiE,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChElE,OAAA;UAAIiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFtE,OAAA;UAAKiE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlE,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDtE,OAAA;cACEuE,OAAO,EAAEA,CAAA,KAAM9D,gBAAgB,CAAC,CAACD,aAAa,CAAE;cAChDyD,SAAS,EAAE,yBACTzD,aAAa,GAAG,aAAa,GAAG,aAAa,6BACjB;cAAA0D,QAAA,eAE9BlE,OAAA;gBAAKiE,SAAS,EAAE,qEACdzD,aAAa,GAAG,eAAe,GAAG,eAAe;cAChD;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDtE,OAAA;cACEuE,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CuD,SAAS,EAAE,yBACTvD,YAAY,GAAG,aAAa,GAAG,aAAa,6BAChB;cAAAwD,QAAA,eAE9BlE,OAAA;gBAAKiE,SAAS,EAAE,qEACdvD,YAAY,GAAG,eAAe,GAAG,eAAe;cAC/C;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5DtE,OAAA;cACEuE,OAAO,EAAEA,CAAA,KAAM1D,WAAW,CAAC,CAACD,QAAQ,CAAE;cACtCqD,SAAS,EAAE,yBACTrD,QAAQ,GAAG,aAAa,GAAG,aAAa,6BACZ;cAAAsD,QAAA,eAE9BlE,OAAA;gBAAKiE,SAAS,EAAE,qEACdrD,QAAQ,GAAG,eAAe,GAAG,eAAe;cAC3C;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CtE,OAAA;cACE8E,KAAK,EAAEhE,KAAM;cACbiE,QAAQ,EAAGhC,CAAC,IAAKhC,QAAQ,CAACgC,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;cAC1Cb,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAE3ElE,OAAA;gBAAQ8E,KAAK,EAAC,MAAM;gBAAAZ,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCtE,OAAA;gBAAQ8E,KAAK,EAAC,OAAO;gBAAAZ,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCtE,OAAA;gBAAQ8E,KAAK,EAAC,MAAM;gBAAAZ,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CtE,OAAA;cACE8E,KAAK,EAAE9D,QAAS;cAChB+D,QAAQ,EAAGhC,CAAC,IAAK9B,WAAW,CAAC8B,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;cAC7Cb,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAE3ElE,OAAA;gBAAQ8E,KAAK,EAAC,OAAO;gBAAAZ,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7CtE,OAAA;gBAAQ8E,KAAK,EAAC,OAAO;gBAAAZ,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3CtE,OAAA;gBAAQ8E,KAAK,EAAC,OAAO;gBAAAZ,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtE,OAAA;YACEuE,OAAO,EAAE7B,YAAa;YACtBuB,SAAS,EAAC,+EAA+E;YAAAC,QAAA,EAC1F;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtE,OAAA;QAAKiE,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChElE,OAAA;UAAIiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EtE,OAAA;UAAMiF,QAAQ,EAAEnC,oBAAqB;UAACmB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACzDlE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAOiE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACEkF,IAAI,EAAC,UAAU;cACfJ,KAAK,EAAE5D,eAAgB;cACvB6D,QAAQ,EAAGhC,CAAC,IAAK5B,kBAAkB,CAAC4B,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;cACpDb,SAAS,EAAC,wEAAwE;cAClFkB,QAAQ,EAAE3D;YAAmB;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAOiE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACEkF,IAAI,EAAC,UAAU;cACfJ,KAAK,EAAE1D,WAAY;cACnB2D,QAAQ,EAAGhC,CAAC,IAAK1B,cAAc,CAAC0B,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;cAChDb,SAAS,EAAC,wEAAwE;cAClFkB,QAAQ,EAAE3D;YAAmB;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAOiE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACEkF,IAAI,EAAC,UAAU;cACfJ,KAAK,EAAExD,eAAgB;cACvByD,QAAQ,EAAGhC,CAAC,IAAKxB,kBAAkB,CAACwB,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;cACpDb,SAAS,EAAC,wEAAwE;cAClFkB,QAAQ,EAAE3D;YAAmB;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtE,OAAA;YACEkF,IAAI,EAAC,QAAQ;YACbC,QAAQ,EAAE3D,kBAAkB,IAAI,CAACN,eAAe,IAAI,CAACE,WAAW,IAAI,CAACE,eAAgB;YACrF2C,SAAS,EAAC,wGAAwG;YAAAC,QAAA,EAEjH1C,kBAAkB,GAAG,cAAc,GAAG;UAAe;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNtE,OAAA;QAAKiE,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChElE,OAAA;UAAIiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EtE,OAAA;UAAKiE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlE,OAAA;YACEuE,OAAO,EAAEV,UAAW;YACpBI,SAAS,EAAC,mFAAmF;YAAAC,QAAA,EAC9F;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETtE,OAAA;YACEuE,OAAO,EAAEf,YAAa;YACtBS,SAAS,EAAC,6EAA6E;YAAAC,QAAA,EACxF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtE,OAAA;QAAKiE,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChElE,OAAA;UAAIiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpFtE,OAAA;UAAKiE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClE,OAAA;YAAKiE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9CtE,OAAA;cAAMiE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACNtE,OAAA;YAAKiE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CtE,OAAA;cAAMiE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNtE,OAAA;YAAKiE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClE,OAAA;cAAMiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDtE,OAAA;cAAMiE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtE,OAAA;MAAKiE,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrElE,OAAA;QAAKiE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClClE,OAAA;UACEuE,OAAO,EAAEA,CAAA,KAAMb,MAAM,CAACc,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCR,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExFlE,OAAA;YAAMiE,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCtE,OAAA;YAAMiE,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpE,EAAA,CA1WID,UAAoB;EAAA,QACkBL,OAAO,EACPC,SAAS;AAAA;AAAAuF,EAAA,GAF/CnF,UAAoB;AA4W1B,eAAeA,UAAU;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}