import React, { useState, useEffect } from 'react';
import { useAuth } from '../stores/authStore';
import { usePlayer } from '../stores/playerStore';
import gameApi from '../services/gameApi';

interface MiningStatus {
  nivel_mineradora: number;
  shacks_disponiveis: number;
  dinheiro_gerado: number;
  ultimo_coleta: string;
  geracao_por_segundo: {
    shacks: number;
    dinheiro: number;
  };
  custo_upgrade: number;
}

const MiningPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { currentPlayer, loadPlayerData } = usePlayer();
  
  const [miningStatus, setMiningStatus] = useState<MiningStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCollecting, setIsCollecting] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [actionError, setActionError] = useState<string | null>(null);
  const [actionSuccess, setActionSuccess] = useState<string | null>(null);
  
  // Timer para atualização automática
  const [timeLeft, setTimeLeft] = useState<string>('');

  useEffect(() => {
    if (isAuthenticated) {
      loadMiningStatus();
      
      // Atualizar a cada 30 segundos
      const interval = setInterval(() => {
        loadMiningStatus();
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    // Timer para mostrar tempo até próxima coleta
    const timer = setInterval(() => {
      if (miningStatus?.ultimo_coleta) {
        const lastCollect = new Date(miningStatus.ultimo_coleta);
        const nextCollect = new Date(lastCollect.getTime() + 60 * 60 * 1000); // +1 hora
        const now = new Date();
        
        if (now >= nextCollect) {
          setTimeLeft('Disponível para coleta!');
        } else {
          const diff = nextCollect.getTime() - now.getTime();
          const minutes = Math.floor(diff / 60000);
          const seconds = Math.floor((diff % 60000) / 1000);
          setTimeLeft(`${minutes}m ${seconds}s`);
        }
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [miningStatus]);

  const loadMiningStatus = async () => {
    setIsLoading(true);
    try {
      const response = await gameApi.getMiningStatus();
      if (response.sucesso) {
        setMiningStatus(response.status);
      } else {
        // Fallback para dados mockados
        loadMockMiningStatus();
      }
    } catch (error) {
      console.error('Erro ao carregar status de mineração:', error);
      loadMockMiningStatus();
    } finally {
      setIsLoading(false);
    }
  };

  const loadMockMiningStatus = () => {
    if (!currentPlayer) return;

    const mockStatus: MiningStatus = {
      nivel_mineradora: currentPlayer.nivel_mineradora || 1,
      shacks_disponiveis: Math.floor(Math.random() * 100) + 10,
      dinheiro_gerado: Math.floor(Math.random() * 5000) + 1000,
      ultimo_coleta: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 min atrás
      geracao_por_segundo: {
        shacks: (currentPlayer.nivel_mineradora || 1) * 0.01,
        dinheiro: (currentPlayer.nivel_mineradora || 1) * 0.5,
      },
      custo_upgrade: Math.floor(1000 * Math.pow(1.5, (currentPlayer.nivel_mineradora || 1) - 1)),
    };

    setMiningStatus(mockStatus);
  };

  const handleCollectShacks = async () => {
    setIsCollecting(true);
    setActionError(null);
    setActionSuccess(null);

    try {
      const response = await gameApi.collectShacks();
      
      if (response.sucesso) {
        setActionSuccess(`Coletados ${response.shacks_coletados} Shacks!`);
        
        // Recarregar dados
        await loadPlayerData();
        await loadMiningStatus();
      } else {
        setActionError(response.mensagem || 'Erro ao coletar Shacks');
      }
    } catch (error: any) {
      setActionError(error.message || 'Erro de conexão');
    } finally {
      setIsCollecting(false);
    }
  };

  const handleUpgradeMiner = async () => {
    if (!miningStatus || !currentPlayer) return;

    if (currentPlayer.dinheiro < miningStatus.custo_upgrade) {
      setActionError('Dinheiro insuficiente para upgrade');
      return;
    }

    setIsUpgrading(true);
    setActionError(null);
    setActionSuccess(null);

    try {
      const response = await gameApi.upgradeMiner();
      
      if (response.sucesso) {
        setActionSuccess(`Mineradora atualizada para nível ${miningStatus.nivel_mineradora + 1}!`);
        
        // Recarregar dados
        await loadPlayerData();
        await loadMiningStatus();
      } else {
        setActionError(response.mensagem || 'Erro ao fazer upgrade');
      }
    } catch (error: any) {
      setActionError(error.message || 'Erro de conexão');
    } finally {
      setIsUpgrading(false);
    }
  };

  const calculateHourlyGeneration = () => {
    if (!miningStatus) return { shacks: 0, dinheiro: 0 };
    
    return {
      shacks: Math.floor(miningStatus.geracao_por_segundo.shacks * 3600),
      dinheiro: Math.floor(miningStatus.geracao_por_segundo.dinheiro * 3600),
    };
  };

  if (!isAuthenticated) {
    return (
      <div className="h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">🔒 Acesso Negado</h1>
          <p className="text-gray-400">Você precisa estar logado para acessar a mineração</p>
        </div>
      </div>
    );
  }

  const hourlyGen = calculateHourlyGeneration();

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => window.history.back()}
            className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600"
          >
            <span className="text-lg">←</span>
          </button>
          <div>
            <h1 className="text-lg font-bold">⛏️ Mineração</h1>
            <p className="text-xs text-gray-400">Sistema de Geração de Recursos</p>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {/* Mensagens de erro/sucesso */}
        {actionError && (
          <div className="bg-red-900 border border-red-500 rounded-lg p-3">
            <p className="text-red-300 text-sm">❌ {actionError}</p>
          </div>
        )}

        {actionSuccess && (
          <div className="bg-green-900 border border-green-500 rounded-lg p-3">
            <p className="text-green-300 text-sm">✅ {actionSuccess}</p>
          </div>
        )}

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-gray-400">Carregando status de mineração...</p>
          </div>
        ) : miningStatus ? (
          <>
            {/* Status da Mineradora */}
            <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
              <h3 className="text-lg font-semibold mb-4 text-white">🏭 Status da Mineradora</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">
                    Nível {miningStatus.nivel_mineradora}
                  </div>
                  <div className="text-xs text-gray-400">Mineradora</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {hourlyGen.shacks}/h
                  </div>
                  <div className="text-xs text-gray-400">Shacks por Hora</div>
                </div>
              </div>
            </div>

            {/* Recursos Disponíveis */}
            <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
              <h3 className="text-lg font-semibold mb-4 text-white">💎 Recursos Disponíveis</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-bold text-blue-400">
                      {miningStatus.shacks_disponiveis} Shacks
                    </div>
                    <div className="text-xs text-gray-400">Prontos para coleta</div>
                  </div>
                  <button
                    onClick={handleCollectShacks}
                    disabled={isCollecting || miningStatus.shacks_disponiveis <= 0}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold"
                  >
                    {isCollecting ? 'Coletando...' : 'Coletar Shacks'}
                  </button>
                </div>

                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-bold text-green-400">
                      ${miningStatus.dinheiro_gerado.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-400">Dinheiro gerado automaticamente</div>
                  </div>
                  <div className="text-xs text-gray-400">
                    {timeLeft}
                  </div>
                </div>
              </div>
            </div>

            {/* Estatísticas de Geração */}
            <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
              <h3 className="text-lg font-semibold mb-4 text-white">📊 Estatísticas de Geração</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-400">
                    {miningStatus.geracao_por_segundo.shacks.toFixed(3)}/s
                  </div>
                  <div className="text-xs text-gray-400">Shacks por Segundo</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-400">
                    ${miningStatus.geracao_por_segundo.dinheiro.toFixed(2)}/s
                  </div>
                  <div className="text-xs text-gray-400">Dinheiro por Segundo</div>
                </div>
              </div>
            </div>

            {/* Upgrade da Mineradora */}
            <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
              <h3 className="text-lg font-semibold mb-4 text-white">🔧 Upgrade da Mineradora</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-bold text-white">
                      Nível {miningStatus.nivel_mineradora} → {miningStatus.nivel_mineradora + 1}
                    </div>
                    <div className="text-xs text-gray-400">
                      Aumenta geração de recursos
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-yellow-400">
                      ${miningStatus.custo_upgrade.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-400">Custo</div>
                  </div>
                </div>

                <div className="text-sm text-gray-300">
                  <div>Após upgrade:</div>
                  <div>• Shacks: {((miningStatus.nivel_mineradora + 1) * 0.01 * 3600).toFixed(0)}/h</div>
                  <div>• Dinheiro: ${((miningStatus.nivel_mineradora + 1) * 0.5 * 3600).toFixed(0)}/h</div>
                </div>

                <button
                  onClick={handleUpgradeMiner}
                  disabled={isUpgrading || !currentPlayer || currentPlayer.dinheiro < miningStatus.custo_upgrade}
                  className="w-full py-3 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white rounded-lg font-semibold"
                >
                  {isUpgrading ? 'Fazendo Upgrade...' : 
                   !currentPlayer || currentPlayer.dinheiro < miningStatus.custo_upgrade ? 'Dinheiro Insuficiente' : 
                   'Fazer Upgrade'}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <div className="text-4xl mb-4">⛏️</div>
            <p className="text-gray-400">Erro ao carregar dados de mineração</p>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0">
        <div className="flex justify-center">
          <button 
            onClick={() => window.history.back()}
            className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
          >
            <span className="text-lg">←</span>
            <span className="text-sm">Voltar</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default MiningPage;
