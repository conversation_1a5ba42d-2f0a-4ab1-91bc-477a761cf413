{"ast": null, "code": "// src/queryClient.ts\nimport { functionalUpdate, hashKey, hashQueryKeyByOptions, noop, partialMatchKey, resolveStaleTime, skipToken } from \"./utils.js\";\nimport { QueryCache } from \"./queryCache.js\";\nimport { MutationCache } from \"./mutationCache.js\";\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new QueryCache();\n    this.#mutationCache = config.mutationCache || new MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */new Map();\n    this.#mutationDefaults = /* @__PURE__ */new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = focusManager.subscribe(async focused => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = onlineManager.subscribe(async online => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({\n      ...filters,\n      fetchStatus: \"fetching\"\n    }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({\n      ...filters,\n      status: \"pending\"\n    }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({\n      queryKey\n    });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({\n      queryKey,\n      state\n    }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({\n      queryKey\n    });\n    const query = this.#queryCache.get(defaultedOptions.queryHash);\n    const prevData = query?.state.data;\n    const data = functionalUpdate(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, {\n      ...options,\n      manual: true\n    });\n  }\n  setQueriesData(filters, updater, options) {\n    return notifyManager.batch(() => this.#queryCache.findAll(filters).map(({\n      queryKey\n    }) => [queryKey, this.setQueryData(queryKey, updater, options)]));\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({\n      queryKey\n    });\n    return this.#queryCache.get(options.queryHash)?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        query.reset();\n      });\n      return this.refetchQueries({\n        type: \"active\",\n        ...filters\n      }, options);\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = {\n      revert: true,\n      ...cancelOptions\n    };\n    const promises = notifyManager.batch(() => this.#queryCache.findAll(filters).map(query => query.cancel(defaultedCancelOptions)));\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach(query => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries({\n        ...filters,\n        type: filters?.refetchType ?? filters?.type ?? \"active\"\n      }, options);\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = notifyManager.batch(() => this.#queryCache.findAll(filters).filter(query => !query.isDisabled() && !query.isStatic()).map(query => {\n      let promise = query.fetch(void 0, fetchOptions);\n      if (!fetchOptions.throwOnError) {\n        promise = promise.catch(noop);\n      }\n      return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n    }));\n    return Promise.all(promises).then(noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query)) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(noop).catch(noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach(queryDefault => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach(queryDefault => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(defaultedOptions.queryKey, defaultedOptions);\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...(options?.mutationKey && this.getMutationDefaults(options.mutationKey)),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\nexport { QueryClient };", "map": {"version": 3, "names": ["functionalUpdate", "hash<PERSON><PERSON>", "hashQueryKeyByOptions", "noop", "partialMatchKey", "resolveStaleTime", "skipToken", "Query<PERSON>ache", "MutationCache", "focusManager", "onlineManager", "notify<PERSON><PERSON>ger", "infiniteQueryBehavior", "QueryClient", "queryCache", "mutationCache", "defaultOptions", "queryDefaults", "mutationDefaults", "mountCount", "unsubscribeFocus", "unsubscribeOnline", "constructor", "config", "Map", "mount", "subscribe", "focused", "resumePausedMutations", "onFocus", "online", "onOnline", "unmount", "isFetching", "filters", "findAll", "fetchStatus", "length", "isMutating", "status", "getQueryData", "query<PERSON><PERSON>", "options", "defaultQueryOptions", "get", "queryHash", "state", "data", "ensureQueryData", "defaultedOptions", "query", "build", "cachedData", "<PERSON><PERSON><PERSON><PERSON>", "revalidateIfStale", "isStaleByTime", "staleTime", "prefetch<PERSON><PERSON>y", "Promise", "resolve", "getQueriesData", "map", "setQueryData", "updater", "prevData", "setData", "manual", "setQueriesData", "batch", "getQueryState", "removeQueries", "for<PERSON>ach", "remove", "resetQueries", "reset", "refetchQueries", "type", "cancelQueries", "cancelOptions", "defaultedCancelOptions", "revert", "promises", "cancel", "all", "then", "catch", "invalidateQueries", "invalidate", "refetchType", "fetchOptions", "cancelRefetch", "filter", "isDisabled", "isStatic", "promise", "fetch", "throwOnError", "retry", "fetchInfiniteQuery", "behavior", "pages", "prefetchInfiniteQuery", "ensureInfiniteQueryData", "isOnline", "get<PERSON><PERSON><PERSON><PERSON>ache", "getMutationCache", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaults", "values", "result", "query<PERSON><PERSON><PERSON>", "Object", "assign", "setMutationDefaults", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "_defaulted", "queries", "refetchOnReconnect", "networkMode", "suspense", "persister", "queryFn", "enabled", "defaultMutationOptions", "mutations", "clear"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\node_modules\\@tanstack\\query-core\\src\\queryClient.ts"], "sourcesContent": ["import {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport type {\n  CancelOptions,\n  DefaultError,\n  DefaultOptions,\n  DefaultedQueryObserverOptions,\n  EnsureInfiniteQueryDataOptions,\n  EnsureQueryDataOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InferDataFromTag,\n  InferErrorFromTag,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  NoInfer,\n  OmitKeyof,\n  QueryClientConfig,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  SetDataOptions,\n} from './types'\nimport type { QueryState } from './query'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: OmitKeyof<QueryOptions<any, any, any>, 'queryKey'>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  #queryCache: QueryCache\n  #mutationCache: MutationCache\n  #defaultOptions: DefaultOptions\n  #queryDefaults: Map<string, QueryDefaults>\n  #mutationDefaults: Map<string, MutationDefaults>\n  #mountCount: number\n  #unsubscribeFocus?: () => void\n  #unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.#queryCache = config.queryCache || new QueryCache()\n    this.#mutationCache = config.mutationCache || new MutationCache()\n    this.#defaultOptions = config.defaultOptions || {}\n    this.#queryDefaults = new Map()\n    this.#mutationDefaults = new Map()\n    this.#mountCount = 0\n  }\n\n  mount(): void {\n    this.#mountCount++\n    if (this.#mountCount !== 1) return\n\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations()\n        this.#queryCache.onFocus()\n      }\n    })\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations()\n        this.#queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.#mountCount--\n    if (this.#mountCount !== 0) return\n\n    this.#unsubscribeFocus?.()\n    this.#unsubscribeFocus = undefined\n\n    this.#unsubscribeOnline?.()\n    this.#unsubscribeOnline = undefined\n  }\n\n  isFetching<TQueryFilters extends QueryFilters<any> = QueryFilters>(\n    filters?: TQueryFilters,\n  ): number {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: 'fetching' })\n      .length\n  }\n\n  isMutating<\n    TMutationFilters extends MutationFilters<any, any> = MutationFilters,\n  >(filters?: TMutationFilters): number {\n    return this.#mutationCache.findAll({ ...filters, status: 'pending' }).length\n  }\n\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData<\n    TQueryFnData = unknown,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n  >(queryKey: TTaggedQueryKey): TInferredQueryFnData | undefined {\n    const options = this.defaultQueryOptions({ queryKey })\n\n    return this.#queryCache.get<TInferredQueryFnData>(options.queryHash)?.state\n      .data\n  }\n\n  ensureQueryData<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: EnsureQueryDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const defaultedOptions = this.defaultQueryOptions(options)\n    const query = this.#queryCache.build(this, defaultedOptions)\n    const cachedData = query.state.data\n\n    if (cachedData === undefined) {\n      return this.fetchQuery(options)\n    }\n\n    if (\n      options.revalidateIfStale &&\n      query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))\n    ) {\n      void this.prefetchQuery(defaultedOptions)\n    }\n\n    return Promise.resolve(cachedData)\n  }\n\n  getQueriesData<\n    TQueryFnData = unknown,\n    TQueryFilters extends QueryFilters<any> = QueryFilters,\n  >(filters: TQueryFilters): Array<[QueryKey, TQueryFnData | undefined]> {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data as TQueryFnData | undefined\n      return [queryKey, data]\n    })\n  }\n\n  setQueryData<\n    TQueryFnData = unknown,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n  >(\n    queryKey: TTaggedQueryKey,\n    updater: Updater<\n      NoInfer<TInferredQueryFnData> | undefined,\n      NoInfer<TInferredQueryFnData> | undefined\n    >,\n    options?: SetDataOptions,\n  ): NoInfer<TInferredQueryFnData> | undefined {\n    const defaultedOptions = this.defaultQueryOptions<\n      any,\n      any,\n      unknown,\n      any,\n      QueryKey\n    >({ queryKey })\n\n    const query = this.#queryCache.get<TInferredQueryFnData>(\n      defaultedOptions.queryHash,\n    )\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (data === undefined) {\n      return undefined\n    }\n\n    return this.#queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<\n    TQueryFnData,\n    TQueryFilters extends QueryFilters<any> = QueryFilters,\n  >(\n    filters: TQueryFilters,\n    updater: Updater<\n      NoInfer<TQueryFnData> | undefined,\n      NoInfer<TQueryFnData> | undefined\n    >,\n    options?: SetDataOptions,\n  ): Array<[QueryKey, TQueryFnData | undefined]> {\n    return notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n    TInferredError = InferErrorFromTag<TError, TTaggedQueryKey>,\n  >(\n    queryKey: TTaggedQueryKey,\n  ): QueryState<TInferredQueryFnData, TInferredError> | undefined {\n    const options = this.defaultQueryOptions({ queryKey })\n    return this.#queryCache.get<TInferredQueryFnData, TInferredError>(\n      options.queryHash,\n    )?.state\n  }\n\n  removeQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n  ): void {\n    const queryCache = this.#queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n    options?: ResetOptions,\n  ): Promise<void> {\n    const queryCache = this.#queryCache\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(\n        {\n          type: 'active',\n          ...filters,\n        },\n        options,\n      )\n    })\n  }\n\n  cancelQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n    cancelOptions: CancelOptions = {},\n  ): Promise<void> {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions }\n\n    const promises = notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(defaultedCancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: InvalidateQueryFilters<TTaggedQueryKey>,\n    options: InvalidateOptions = {},\n  ): Promise<void> {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters?.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? 'active',\n        },\n        options,\n      )\n    })\n  }\n\n  refetchQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: RefetchQueryFilters<TTaggedQueryKey>,\n    options: RefetchOptions = {},\n  ): Promise<void> {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true,\n    }\n    const promises = notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled() && !query.isStatic())\n        .map((query) => {\n          let promise = query.fetch(undefined, fetchOptions)\n          if (!fetchOptions.throwOnError) {\n            promise = promise.catch(noop)\n          }\n          return query.state.fetchStatus === 'paused'\n            ? Promise.resolve()\n            : promise\n        }),\n    )\n\n    return Promise.all(promises).then(noop)\n  }\n\n  fetchQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = never,\n  >(\n    options: FetchQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<TData> {\n    const defaultedOptions = this.defaultQueryOptions(options)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (defaultedOptions.retry === undefined) {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.#queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query),\n    )\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void> {\n    return this.fetchQuery(options).then(noop).catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: FetchInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<InfiniteData<TData, TPageParam>> {\n    options.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData,\n      TPageParam\n    >(options.pages)\n    return this.fetchQuery(options as any)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: FetchInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop)\n  }\n\n  ensureInfiniteQueryData<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: EnsureInfiniteQueryDataOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<InfiniteData<TData, TPageParam>> {\n    options.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData,\n      TPageParam\n    >(options.pages)\n\n    return this.ensureQueryData(options as any)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations()\n    }\n    return Promise.resolve()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.#queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.#mutationCache\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.#defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.#defaultOptions = options\n  }\n\n  setQueryDefaults<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryData = TQueryFnData,\n  >(\n    queryKey: QueryKey,\n    options: Partial<\n      OmitKeyof<\n        QueryObserverOptions<TQueryFnData, TError, TData, TQueryData>,\n        'queryKey'\n      >\n    >,\n  ): void {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options,\n    })\n  }\n\n  getQueryDefaults(\n    queryKey: QueryKey,\n  ): OmitKeyof<QueryObserverOptions<any, any, any, any, any>, 'queryKey'> {\n    const defaults = [...this.#queryDefaults.values()]\n\n    const result: OmitKeyof<\n      QueryObserverOptions<any, any, any, any, any>,\n      'queryKey'\n    > = {}\n\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions)\n      }\n    })\n    return result\n  }\n\n  setMutationDefaults<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = void,\n    TContext = unknown,\n  >(\n    mutationKey: MutationKey,\n    options: OmitKeyof<\n      MutationObserverOptions<TData, TError, TVariables, TContext>,\n      'mutationKey'\n    >,\n  ): void {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options,\n    })\n  }\n\n  getMutationDefaults(\n    mutationKey: MutationKey,\n  ): OmitKeyof<MutationObserverOptions<any, any, any, any>, 'mutationKey'> {\n    const defaults = [...this.#mutationDefaults.values()]\n\n    const result: OmitKeyof<\n      MutationObserverOptions<any, any, any, any>,\n      'mutationKey'\n    > = {}\n\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions)\n      }\n    })\n\n    return result\n  }\n\n  defaultQueryOptions<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = never,\n  >(\n    options:\n      | QueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey,\n          TPageParam\n        >\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (defaultedOptions.refetchOnReconnect === undefined) {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (defaultedOptions.throwOnError === undefined) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense\n    }\n\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = 'offlineFirst'\n    }\n\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...(options?.mutationKey &&\n        this.getMutationDefaults(options.mutationKey)),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.#queryCache.clear()\n    this.#mutationCache.clear()\n  }\n}\n"], "mappings": ";AAAA,SACEA,gBAAA,EACAC,OAAA,EACAC,qBAAA,EACAC,IAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,SAAA,QACK;AACP,SAASC,UAAA,QAAkB;AAC3B,SAASC,aAAA,QAAqB;AAC9B,SAASC,YAAA,QAAoB;AAC7B,SAASC,aAAA,QAAqB;AAC9B,SAASC,aAAA,QAAqB;AAC9B,SAASC,qBAAA,QAA6B;AA8C/B,IAAMC,WAAA,GAAN,MAAkB;EACvB,CAAAC,UAAA;EACA,CAAAC,aAAA;EACA,CAAAC,cAAA;EACA,CAAAC,aAAA;EACA,CAAAC,gBAAA;EACA,CAAAC,UAAA;EACA,CAAAC,gBAAA;EACA,CAAAC,iBAAA;EAEAC,YAAYC,MAAA,GAA4B,CAAC,GAAG;IAC1C,KAAK,CAAAT,UAAA,GAAcS,MAAA,CAAOT,UAAA,IAAc,IAAIP,UAAA,CAAW;IACvD,KAAK,CAAAQ,aAAA,GAAiBQ,MAAA,CAAOR,aAAA,IAAiB,IAAIP,aAAA,CAAc;IAChE,KAAK,CAAAQ,cAAA,GAAkBO,MAAA,CAAOP,cAAA,IAAkB,CAAC;IACjD,KAAK,CAAAC,aAAA,GAAiB,mBAAIO,GAAA,CAAI;IAC9B,KAAK,CAAAN,gBAAA,GAAoB,mBAAIM,GAAA,CAAI;IACjC,KAAK,CAAAL,UAAA,GAAc;EACrB;EAEAM,MAAA,EAAc;IACZ,KAAK,CAAAN,UAAA;IACL,IAAI,KAAK,CAAAA,UAAA,KAAgB,GAAG;IAE5B,KAAK,CAAAC,gBAAA,GAAoBX,YAAA,CAAaiB,SAAA,CAAU,MAAOC,OAAA,IAAY;MACjE,IAAIA,OAAA,EAAS;QACX,MAAM,KAAKC,qBAAA,CAAsB;QACjC,KAAK,CAAAd,UAAA,CAAYe,OAAA,CAAQ;MAC3B;IACF,CAAC;IACD,KAAK,CAAAR,iBAAA,GAAqBX,aAAA,CAAcgB,SAAA,CAAU,MAAOI,MAAA,IAAW;MAClE,IAAIA,MAAA,EAAQ;QACV,MAAM,KAAKF,qBAAA,CAAsB;QACjC,KAAK,CAAAd,UAAA,CAAYiB,QAAA,CAAS;MAC5B;IACF,CAAC;EACH;EAEAC,QAAA,EAAgB;IACd,KAAK,CAAAb,UAAA;IACL,IAAI,KAAK,CAAAA,UAAA,KAAgB,GAAG;IAE5B,KAAK,CAAAC,gBAAA,GAAoB;IACzB,KAAK,CAAAA,gBAAA,GAAoB;IAEzB,KAAK,CAAAC,iBAAA,GAAqB;IAC1B,KAAK,CAAAA,iBAAA,GAAqB;EAC5B;EAEAY,WACEC,OAAA,EACQ;IACR,OAAO,KAAK,CAAApB,UAAA,CAAYqB,OAAA,CAAQ;MAAE,GAAGD,OAAA;MAASE,WAAA,EAAa;IAAW,CAAC,EACpEC,MAAA;EACL;EAEAC,WAEEJ,OAAA,EAAoC;IACpC,OAAO,KAAK,CAAAnB,aAAA,CAAeoB,OAAA,CAAQ;MAAE,GAAGD,OAAA;MAASK,MAAA,EAAQ;IAAU,CAAC,EAAEF,MAAA;EACxE;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAG,aAIEC,QAAA,EAA6D;IAC7D,MAAMC,OAAA,GAAU,KAAKC,mBAAA,CAAoB;MAAEF;IAAS,CAAC;IAErD,OAAO,KAAK,CAAA3B,UAAA,CAAY8B,GAAA,CAA0BF,OAAA,CAAQG,SAAS,GAAGC,KAAA,CACnEC,IAAA;EACL;EAEAC,gBAMEN,OAAA,EACgB;IAChB,MAAMO,gBAAA,GAAmB,KAAKN,mBAAA,CAAoBD,OAAO;IACzD,MAAMQ,KAAA,GAAQ,KAAK,CAAApC,UAAA,CAAYqC,KAAA,CAAM,MAAMF,gBAAgB;IAC3D,MAAMG,UAAA,GAAaF,KAAA,CAAMJ,KAAA,CAAMC,IAAA;IAE/B,IAAIK,UAAA,KAAe,QAAW;MAC5B,OAAO,KAAKC,UAAA,CAAWX,OAAO;IAChC;IAEA,IACEA,OAAA,CAAQY,iBAAA,IACRJ,KAAA,CAAMK,aAAA,CAAclD,gBAAA,CAAiB4C,gBAAA,CAAiBO,SAAA,EAAWN,KAAK,CAAC,GACvE;MACA,KAAK,KAAKO,aAAA,CAAcR,gBAAgB;IAC1C;IAEA,OAAOS,OAAA,CAAQC,OAAA,CAAQP,UAAU;EACnC;EAEAQ,eAGE1B,OAAA,EAAqE;IACrE,OAAO,KAAK,CAAApB,UAAA,CAAYqB,OAAA,CAAQD,OAAO,EAAE2B,GAAA,CAAI,CAAC;MAAEpB,QAAA;MAAUK;IAAM,MAAM;MACpE,MAAMC,IAAA,GAAOD,KAAA,CAAMC,IAAA;MACnB,OAAO,CAACN,QAAA,EAAUM,IAAI;IACxB,CAAC;EACH;EAEAe,aAKErB,QAAA,EACAsB,OAAA,EAIArB,OAAA,EAC2C;IAC3C,MAAMO,gBAAA,GAAmB,KAAKN,mBAAA,CAM5B;MAAEF;IAAS,CAAC;IAEd,MAAMS,KAAA,GAAQ,KAAK,CAAApC,UAAA,CAAY8B,GAAA,CAC7BK,gBAAA,CAAiBJ,SACnB;IACA,MAAMmB,QAAA,GAAWd,KAAA,EAAOJ,KAAA,CAAMC,IAAA;IAC9B,MAAMA,IAAA,GAAO/C,gBAAA,CAAiB+D,OAAA,EAASC,QAAQ;IAE/C,IAAIjB,IAAA,KAAS,QAAW;MACtB,OAAO;IACT;IAEA,OAAO,KAAK,CAAAjC,UAAA,CACTqC,KAAA,CAAM,MAAMF,gBAAgB,EAC5BgB,OAAA,CAAQlB,IAAA,EAAM;MAAE,GAAGL,OAAA;MAASwB,MAAA,EAAQ;IAAK,CAAC;EAC/C;EAEAC,eAIEjC,OAAA,EACA6B,OAAA,EAIArB,OAAA,EAC6C;IAC7C,OAAO/B,aAAA,CAAcyD,KAAA,CAAM,MACzB,KAAK,CAAAtD,UAAA,CACFqB,OAAA,CAAQD,OAAO,EACf2B,GAAA,CAAI,CAAC;MAAEpB;IAAS,MAAM,CACrBA,QAAA,EACA,KAAKqB,YAAA,CAA2BrB,QAAA,EAAUsB,OAAA,EAASrB,OAAO,EAC3D,CACL;EACF;EAEA2B,cAOE5B,QAAA,EAC8D;IAC9D,MAAMC,OAAA,GAAU,KAAKC,mBAAA,CAAoB;MAAEF;IAAS,CAAC;IACrD,OAAO,KAAK,CAAA3B,UAAA,CAAY8B,GAAA,CACtBF,OAAA,CAAQG,SACV,GAAGC,KAAA;EACL;EAEAwB,cACEpC,OAAA,EACM;IACN,MAAMpB,UAAA,GAAa,KAAK,CAAAA,UAAA;IACxBH,aAAA,CAAcyD,KAAA,CAAM,MAAM;MACxBtD,UAAA,CAAWqB,OAAA,CAAQD,OAAO,EAAEqC,OAAA,CAASrB,KAAA,IAAU;QAC7CpC,UAAA,CAAW0D,MAAA,CAAOtB,KAAK;MACzB,CAAC;IACH,CAAC;EACH;EAEAuB,aACEvC,OAAA,EACAQ,OAAA,EACe;IACf,MAAM5B,UAAA,GAAa,KAAK,CAAAA,UAAA;IAExB,OAAOH,aAAA,CAAcyD,KAAA,CAAM,MAAM;MAC/BtD,UAAA,CAAWqB,OAAA,CAAQD,OAAO,EAAEqC,OAAA,CAASrB,KAAA,IAAU;QAC7CA,KAAA,CAAMwB,KAAA,CAAM;MACd,CAAC;MACD,OAAO,KAAKC,cAAA,CACV;QACEC,IAAA,EAAM;QACN,GAAG1C;MACL,GACAQ,OACF;IACF,CAAC;EACH;EAEAmC,cACE3C,OAAA,EACA4C,aAAA,GAA+B,CAAC,GACjB;IACf,MAAMC,sBAAA,GAAyB;MAAEC,MAAA,EAAQ;MAAM,GAAGF;IAAc;IAEhE,MAAMG,QAAA,GAAWtE,aAAA,CAAcyD,KAAA,CAAM,MACnC,KAAK,CAAAtD,UAAA,CACFqB,OAAA,CAAQD,OAAO,EACf2B,GAAA,CAAKX,KAAA,IAAUA,KAAA,CAAMgC,MAAA,CAAOH,sBAAsB,CAAC,CACxD;IAEA,OAAOrB,OAAA,CAAQyB,GAAA,CAAIF,QAAQ,EAAEG,IAAA,CAAKjF,IAAI,EAAEkF,KAAA,CAAMlF,IAAI;EACpD;EAEAmF,kBACEpD,OAAA,EACAQ,OAAA,GAA6B,CAAC,GACf;IACf,OAAO/B,aAAA,CAAcyD,KAAA,CAAM,MAAM;MAC/B,KAAK,CAAAtD,UAAA,CAAYqB,OAAA,CAAQD,OAAO,EAAEqC,OAAA,CAASrB,KAAA,IAAU;QACnDA,KAAA,CAAMqC,UAAA,CAAW;MACnB,CAAC;MAED,IAAIrD,OAAA,EAASsD,WAAA,KAAgB,QAAQ;QACnC,OAAO9B,OAAA,CAAQC,OAAA,CAAQ;MACzB;MACA,OAAO,KAAKgB,cAAA,CACV;QACE,GAAGzC,OAAA;QACH0C,IAAA,EAAM1C,OAAA,EAASsD,WAAA,IAAetD,OAAA,EAAS0C,IAAA,IAAQ;MACjD,GACAlC,OACF;IACF,CAAC;EACH;EAEAiC,eACEzC,OAAA,EACAQ,OAAA,GAA0B,CAAC,GACZ;IACf,MAAM+C,YAAA,GAAe;MACnB,GAAG/C,OAAA;MACHgD,aAAA,EAAehD,OAAA,CAAQgD,aAAA,IAAiB;IAC1C;IACA,MAAMT,QAAA,GAAWtE,aAAA,CAAcyD,KAAA,CAAM,MACnC,KAAK,CAAAtD,UAAA,CACFqB,OAAA,CAAQD,OAAO,EACfyD,MAAA,CAAQzC,KAAA,IAAU,CAACA,KAAA,CAAM0C,UAAA,CAAW,KAAK,CAAC1C,KAAA,CAAM2C,QAAA,CAAS,CAAC,EAC1DhC,GAAA,CAAKX,KAAA,IAAU;MACd,IAAI4C,OAAA,GAAU5C,KAAA,CAAM6C,KAAA,CAAM,QAAWN,YAAY;MACjD,IAAI,CAACA,YAAA,CAAaO,YAAA,EAAc;QAC9BF,OAAA,GAAUA,OAAA,CAAQT,KAAA,CAAMlF,IAAI;MAC9B;MACA,OAAO+C,KAAA,CAAMJ,KAAA,CAAMV,WAAA,KAAgB,WAC/BsB,OAAA,CAAQC,OAAA,CAAQ,IAChBmC,OAAA;IACN,CAAC,CACL;IAEA,OAAOpC,OAAA,CAAQyB,GAAA,CAAIF,QAAQ,EAAEG,IAAA,CAAKjF,IAAI;EACxC;EAEAkD,WAOEX,OAAA,EAOgB;IAChB,MAAMO,gBAAA,GAAmB,KAAKN,mBAAA,CAAoBD,OAAO;IAGzD,IAAIO,gBAAA,CAAiBgD,KAAA,KAAU,QAAW;MACxChD,gBAAA,CAAiBgD,KAAA,GAAQ;IAC3B;IAEA,MAAM/C,KAAA,GAAQ,KAAK,CAAApC,UAAA,CAAYqC,KAAA,CAAM,MAAMF,gBAAgB;IAE3D,OAAOC,KAAA,CAAMK,aAAA,CACXlD,gBAAA,CAAiB4C,gBAAA,CAAiBO,SAAA,EAAWN,KAAK,CACpD,IACIA,KAAA,CAAM6C,KAAA,CAAM9C,gBAAgB,IAC5BS,OAAA,CAAQC,OAAA,CAAQT,KAAA,CAAMJ,KAAA,CAAMC,IAAa;EAC/C;EAEAU,cAMEf,OAAA,EACe;IACf,OAAO,KAAKW,UAAA,CAAWX,OAAO,EAAE0C,IAAA,CAAKjF,IAAI,EAAEkF,KAAA,CAAMlF,IAAI;EACvD;EAEA+F,mBAOExD,OAAA,EAO0C;IAC1CA,OAAA,CAAQyD,QAAA,GAAWvF,qBAAA,CAKjB8B,OAAA,CAAQ0D,KAAK;IACf,OAAO,KAAK/C,UAAA,CAAWX,OAAc;EACvC;EAEA2D,sBAOE3D,OAAA,EAOe;IACf,OAAO,KAAKwD,kBAAA,CAAmBxD,OAAO,EAAE0C,IAAA,CAAKjF,IAAI,EAAEkF,KAAA,CAAMlF,IAAI;EAC/D;EAEAmG,wBAOE5D,OAAA,EAO0C;IAC1CA,OAAA,CAAQyD,QAAA,GAAWvF,qBAAA,CAKjB8B,OAAA,CAAQ0D,KAAK;IAEf,OAAO,KAAKpD,eAAA,CAAgBN,OAAc;EAC5C;EAEAd,sBAAA,EAA0C;IACxC,IAAIlB,aAAA,CAAc6F,QAAA,CAAS,GAAG;MAC5B,OAAO,KAAK,CAAAxF,aAAA,CAAea,qBAAA,CAAsB;IACnD;IACA,OAAO8B,OAAA,CAAQC,OAAA,CAAQ;EACzB;EAEA6C,cAAA,EAA4B;IAC1B,OAAO,KAAK,CAAA1F,UAAA;EACd;EAEA2F,iBAAA,EAAkC;IAChC,OAAO,KAAK,CAAA1F,aAAA;EACd;EAEA2F,kBAAA,EAAoC;IAClC,OAAO,KAAK,CAAA1F,cAAA;EACd;EAEA2F,kBAAkBjE,OAAA,EAA+B;IAC/C,KAAK,CAAA1B,cAAA,GAAkB0B,OAAA;EACzB;EAEAkE,iBAMEnE,QAAA,EACAC,OAAA,EAMM;IACN,KAAK,CAAAzB,aAAA,CAAe4F,GAAA,CAAI5G,OAAA,CAAQwC,QAAQ,GAAG;MACzCA,QAAA;MACAzB,cAAA,EAAgB0B;IAClB,CAAC;EACH;EAEAoE,iBACErE,QAAA,EACsE;IACtE,MAAMsE,QAAA,GAAW,CAAC,GAAG,KAAK,CAAA9F,aAAA,CAAe+F,MAAA,CAAO,CAAC;IAEjD,MAAMC,MAAA,GAGF,CAAC;IAELF,QAAA,CAASxC,OAAA,CAAS2C,YAAA,IAAiB;MACjC,IAAI9G,eAAA,CAAgBqC,QAAA,EAAUyE,YAAA,CAAazE,QAAQ,GAAG;QACpD0E,MAAA,CAAOC,MAAA,CAAOH,MAAA,EAAQC,YAAA,CAAalG,cAAc;MACnD;IACF,CAAC;IACD,OAAOiG,MAAA;EACT;EAEAI,oBAMEC,WAAA,EACA5E,OAAA,EAIM;IACN,KAAK,CAAAxB,gBAAA,CAAkB2F,GAAA,CAAI5G,OAAA,CAAQqH,WAAW,GAAG;MAC/CA,WAAA;MACAtG,cAAA,EAAgB0B;IAClB,CAAC;EACH;EAEA6E,oBACED,WAAA,EACuE;IACvE,MAAMP,QAAA,GAAW,CAAC,GAAG,KAAK,CAAA7F,gBAAA,CAAkB8F,MAAA,CAAO,CAAC;IAEpD,MAAMC,MAAA,GAGF,CAAC;IAELF,QAAA,CAASxC,OAAA,CAAS2C,YAAA,IAAiB;MACjC,IAAI9G,eAAA,CAAgBkH,WAAA,EAAaJ,YAAA,CAAaI,WAAW,GAAG;QAC1DH,MAAA,CAAOC,MAAA,CAAOH,MAAA,EAAQC,YAAA,CAAalG,cAAc;MACnD;IACF,CAAC;IAED,OAAOiG,MAAA;EACT;EAEAtE,oBAQED,OAAA,EAsBA;IACA,IAAIA,OAAA,CAAQ8E,UAAA,EAAY;MACtB,OAAO9E,OAAA;IAOT;IAEA,MAAMO,gBAAA,GAAmB;MACvB,GAAG,KAAK,CAAAjC,cAAA,CAAgByG,OAAA;MACxB,GAAG,KAAKX,gBAAA,CAAiBpE,OAAA,CAAQD,QAAQ;MACzC,GAAGC,OAAA;MACH8E,UAAA,EAAY;IACd;IAEA,IAAI,CAACvE,gBAAA,CAAiBJ,SAAA,EAAW;MAC/BI,gBAAA,CAAiBJ,SAAA,GAAY3C,qBAAA,CAC3B+C,gBAAA,CAAiBR,QAAA,EACjBQ,gBACF;IACF;IAGA,IAAIA,gBAAA,CAAiByE,kBAAA,KAAuB,QAAW;MACrDzE,gBAAA,CAAiByE,kBAAA,GACfzE,gBAAA,CAAiB0E,WAAA,KAAgB;IACrC;IACA,IAAI1E,gBAAA,CAAiB+C,YAAA,KAAiB,QAAW;MAC/C/C,gBAAA,CAAiB+C,YAAA,GAAe,CAAC,CAAC/C,gBAAA,CAAiB2E,QAAA;IACrD;IAEA,IAAI,CAAC3E,gBAAA,CAAiB0E,WAAA,IAAe1E,gBAAA,CAAiB4E,SAAA,EAAW;MAC/D5E,gBAAA,CAAiB0E,WAAA,GAAc;IACjC;IAEA,IAAI1E,gBAAA,CAAiB6E,OAAA,KAAYxH,SAAA,EAAW;MAC1C2C,gBAAA,CAAiB8E,OAAA,GAAU;IAC7B;IAEA,OAAO9E,gBAAA;EAOT;EAEA+E,uBACEtF,OAAA,EACG;IACH,IAAIA,OAAA,EAAS8E,UAAA,EAAY;MACvB,OAAO9E,OAAA;IACT;IACA,OAAO;MACL,GAAG,KAAK,CAAA1B,cAAA,CAAgBiH,SAAA;MACxB,IAAIvF,OAAA,EAAS4E,WAAA,IACX,KAAKC,mBAAA,CAAoB7E,OAAA,CAAQ4E,WAAW;MAC9C,GAAG5E,OAAA;MACH8E,UAAA,EAAY;IACd;EACF;EAEAU,MAAA,EAAc;IACZ,KAAK,CAAApH,UAAA,CAAYoH,KAAA,CAAM;IACvB,KAAK,CAAAnH,aAAA,CAAemH,KAAA,CAAM;EAC5B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}