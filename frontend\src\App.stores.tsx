import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuth } from './stores/authStore';
import { useGame } from './stores/gameStore';
import { useChat } from './stores/chatStore';
import './styles/globals.css';

// Configuração do React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutos
    },
  },
});

// Componente para testar stores
const StoreTestPage = ({ title }: { title: string }) => {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { currentPlayer, isLoadingPlayer } = useGame();
  const { messages, isLoading: chatLoading } = useChat();

  return (
    <div className="min-h-screen bg-bg-primary text-text-primary p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <Link to="/" className="text-accent-blue hover:text-primary-light">
            ← Voltar ao início
          </Link>
        </div>
        
        <div className="card">
          <h1 className="text-3xl font-bold mb-6">{title}</h1>
          
          {/* Status dos Stores */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Auth Store */}
            <div className="card bg-blue-900 border-blue-500">
              <h3 className="text-lg font-semibold text-blue-100 mb-2">
                🔐 Auth Store
              </h3>
              <div className="text-blue-200 text-sm space-y-1">
                <p>Autenticado: {isAuthenticated ? '✅' : '❌'}</p>
                <p>Loading: {authLoading ? '⏳' : '✅'}</p>
                <p>Usuário: {user ? user.nick : 'Nenhum'}</p>
              </div>
            </div>

            {/* Game Store */}
            <div className="card bg-green-900 border-green-500">
              <h3 className="text-lg font-semibold text-green-100 mb-2">
                🎮 Game Store
              </h3>
              <div className="text-green-200 text-sm space-y-1">
                <p>Jogador: {currentPlayer ? currentPlayer.nick : 'Nenhum'}</p>
                <p>Loading: {isLoadingPlayer ? '⏳' : '✅'}</p>
                <p>Dinheiro: {currentPlayer ? `$${currentPlayer.dinheiro}` : 'N/A'}</p>
              </div>
            </div>

            {/* Chat Store */}
            <div className="card bg-purple-900 border-purple-500">
              <h3 className="text-lg font-semibold text-purple-100 mb-2">
                💬 Chat Store
              </h3>
              <div className="text-purple-200 text-sm space-y-1">
                <p>Mensagens: {messages.length}</p>
                <p>Loading: {chatLoading ? '⏳' : '✅'}</p>
                <p>Status: Conectado</p>
              </div>
            </div>
          </div>

          {/* Teste de Ações */}
          <div className="card bg-bg-tertiary">
            <h3 className="text-lg font-semibold text-text-primary mb-4">
              🧪 Teste de Ações dos Stores
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button 
                onClick={() => {
                  console.log('Auth Store - Estado atual:', { user, isAuthenticated, authLoading });
                  alert('Verifique o console para ver o estado do Auth Store');
                }}
                className="btn-primary"
              >
                Testar Auth Store
              </button>
              
              <button 
                onClick={() => {
                  console.log('Game Store - Estado atual:', { currentPlayer, isLoadingPlayer });
                  alert('Verifique o console para ver o estado do Game Store');
                }}
                className="btn-secondary"
              >
                Testar Game Store
              </button>
              
              <button 
                onClick={() => {
                  console.log('Chat Store - Estado atual:', { messages, chatLoading });
                  alert('Verifique o console para ver o estado do Chat Store');
                }}
                className="btn-accent"
              >
                Testar Chat Store
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Página inicial
const HomePage = () => (
  <div className="min-h-screen bg-bg-primary text-text-primary p-8">
    <div className="max-w-4xl mx-auto">
      <h1 className="text-4xl font-bold text-center mb-8">
        🎮 SHACK Web Game - Teste Zustand Stores
      </h1>
      
      <div className="card text-center">
        <h2 className="text-2xl font-semibold mb-6">
          Zustand Stores Adicionados!
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Link to="/login" className="card bg-blue-900 border-blue-500 hover:bg-blue-800 transition-colors">
            <h3 className="text-lg font-semibold text-blue-100 mb-2">
              🔐 Teste Auth Store
            </h3>
            <p className="text-blue-200 text-sm">
              Testar store de autenticação
            </p>
          </Link>
          
          <Link to="/game" className="card bg-green-900 border-green-500 hover:bg-green-800 transition-colors">
            <h3 className="text-lg font-semibold text-green-100 mb-2">
              🎮 Teste Game Store
            </h3>
            <p className="text-green-200 text-sm">
              Testar store do jogo
            </p>
          </Link>
        </div>
        
        <div className="bg-bg-tertiary border border-border-color rounded p-4">
          <div className="text-text-muted text-sm space-y-1">
            <p>✅ React funcionando</p>
            <p>✅ React Router funcionando</p>
            <p>✅ Tailwind CSS funcionando</p>
            <p>✅ React Query Provider ativo</p>
            <p>✅ Zustand Stores carregados</p>
          </div>
        </div>
      </div>
    </div>
  </div>
);

function App() {
  console.log('App Stores - Renderizando com Zustand Stores...');
  
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/login" element={<StoreTestPage title="Teste Auth Store" />} />
          <Route path="/game" element={<StoreTestPage title="Teste Game Store" />} />
          <Route path="*" element={<StoreTestPage title="Teste 404" />} />
        </Routes>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
