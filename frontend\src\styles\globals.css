@tailwind base;
@tailwind components;
@tailwind utilities;

/* Importar efeitos modernos */
@import './modern-effects.css';

/* === TEMA MODERNO PRETO E AZUL === */
:root {
  --primary: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary: #64748b;
  --accent: #0ea5e9;
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border: #334155;
  --shadow: rgba(37, 99, 235, 0.1);
  --shadow-lg: rgba(37, 99, 235, 0.2);
  --cyber-primary: #00ff41;
  --cyber-secondary: #0080ff;
  --cyber-accent: #ff0080;
  --cyber-glow: #00ff41;
}

/* Reset e Base */
* {
  box-sizing: border-box;
}

body {
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

/* Gradient de fundo moderno */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.03) 0%, transparent 50%, rgba(13, 17, 23, 0.05) 100%);
  z-index: -1;
}

/* Animações personalizadas */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}

@keyframes glow {
  0% {
    text-shadow: 0 0 5px var(--cyber-glow), 0 0 10px var(--cyber-glow), 0 0 15px var(--cyber-glow);
  }
  100% {
    text-shadow: 0 0 10px var(--cyber-glow), 0 0 20px var(--cyber-glow), 0 0 30px var(--cyber-glow);
  }
}

/* Componentes customizados */
.game-section {
  animation: fadeInUp 0.5s ease-out;
}

.cyber-text {
  color: var(--cyber-primary);
  text-shadow: 0 0 10px var(--cyber-glow);
  font-family: 'Courier New', monospace;
}

.cyber-border {
  border: 1px solid var(--cyber-primary);
  box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

.status-online {
  width: 12px;
  height: 12px;
  background-color: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-light);
}

/* Botões personalizados */
.btn-primary {
  @apply bg-primary hover:bg-primary-dark text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-bg-secondary hover:bg-bg-tertiary text-text-primary border border-border-color font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-cyber {
  @apply bg-transparent border-2 border-cyber-primary text-cyber-primary hover:bg-cyber-primary hover:text-black font-mono font-bold py-2 px-4 rounded transition-all duration-300;
  text-shadow: 0 0 10px var(--cyber-glow);
  box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

.btn-cyber:hover {
  box-shadow: 0 0 20px rgba(0, 255, 65, 0.6);
}

/* Cards personalizados */
.card {
  @apply bg-bg-secondary border border-border-color rounded-lg p-4 shadow-lg;
}

.card-hover {
  @apply card hover:border-accent hover:shadow-xl transition-all duration-300;
}

/* Inputs personalizados */
.input-primary {
  @apply w-full p-3 rounded-lg bg-surface-elevated text-text-primary border border-border-color focus:border-accent-blue focus:outline-none transition-colors duration-200;
}

/* Notificações */
.notification {
  @apply fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm;
}

.notification-success {
  @apply notification bg-green-600 text-white;
}

.notification-error {
  @apply notification bg-red-600 text-white;
}

.notification-info {
  @apply notification bg-blue-600 text-white;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-b-2 border-primary;
}
