import os
import hmac
import hashlib
import time
import secrets
from functools import wraps
from flask import request, abort, session, current_app, g

class CSRFProtection:
    """
    Class for CSRF protection using tokens - Versão melhorada para APIs
    """
    
    def __init__(self, app=None):
        self.app = app
        self.token_lifetime = 3600  # 1 hora de validade
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize with Flask app"""
        self.app = app
        
        # Configurações mais permissivas para sessões
        app.config['SESSION_COOKIE_SECURE'] = False  # HTTP ok para desenvolvimento
        app.config['SESSION_COOKIE_HTTPONLY'] = True
        app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
        
        # Make sure we have a secret key
        if not app.config.get('SECRET_KEY'):
            app.config['SECRET_KEY'] = 'shack-game-secret-key-2024-csrf-protection-enabled'
        
        print("✅ CSRF protection middleware initialized")
    
    def generate_csrf_token(self):
        """Generate a new CSRF token - Versão melhorada"""
        current_time = int(time.time())
        
        # Gerar token com timestamp para validade
        random_bytes = secrets.token_hex(16)
        token_data = f"{current_time}:{random_bytes}"
        
        # Criar HMAC do token
        signature = hmac.new(
            current_app.config['SECRET_KEY'].encode('utf-8'),
            token_data.encode('utf-8'),
            digestmod=hashlib.sha256
        ).hexdigest()
        
        # Token final: timestamp:random:signature
        token = f"{current_time}:{random_bytes}:{signature}"
        
        # Armazenar na sessão também para fallback
        session['csrf_token'] = token
        session.permanent = True
        
        return token
    
    def validate_csrf_token(self, token):
        """Validate the provided CSRF token - Versão mais flexível"""
        if not token or token == 'dummy_token':
            print("⚠️ Token CSRF vazio ou dummy")
            return False
            
        try:
            # Tentar validar token com timestamp
            if ':' in token and len(token.split(':')) == 3:
                timestamp_str, random_part, provided_signature = token.split(':', 2)
                
                # Verificar se não expirou
                timestamp = int(timestamp_str)
                current_time = int(time.time())
                
                if current_time - timestamp > self.token_lifetime:
                    print(f"⚠️ Token CSRF expirado: {current_time - timestamp}s ago")
                    return False
                
                # Recriar assinatura esperada
                token_data = f"{timestamp_str}:{random_part}"
                expected_signature = hmac.new(
                    current_app.config['SECRET_KEY'].encode('utf-8'),
                    token_data.encode('utf-8'),
                    digestmod=hashlib.sha256
                ).hexdigest()
                
                # Comparar assinaturas
                if hmac.compare_digest(expected_signature, provided_signature):
                    return True
            
            # Fallback: verificar com token da sessão
            stored_token = session.get('csrf_token')
            if stored_token and hmac.compare_digest(stored_token, token):
                return True
                
            print(f"⚠️ Token CSRF inválido: {token[:20]}...")
            return False
            
        except Exception as e:
            print(f"❌ Erro na validação CSRF: {e}")
            return False
    
    def csrf_exempt(self, f):
        """Decorator para isentar rota da verificação CSRF"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            g.csrf_exempt = True
            return f(*args, **kwargs)
        return decorated_function
    
    def csrf_required(self, f):
        """Decorator to enforce CSRF token validation on routes"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Skip validation for GET requests
            if request.method == 'GET':
                return f(*args, **kwargs)
                
            # Skip se marcado como exempt
            if getattr(g, 'csrf_exempt', False):
                return f(*args, **kwargs)
                
            token = self._extract_token_from_request()
            
            if not self.validate_csrf_token(token):
                print("⚠️ CSRF token validation failed")
                abort(403, description="CSRF token validation failed")
                
            return f(*args, **kwargs)
        return decorated_function
    
    def _extract_token_from_request(self):
        """Extrair token de várias fontes possíveis"""
        token = None
        
        # 1. Tentar header X-CSRF-TOKEN primeiro (recomendado para APIs)
        token = request.headers.get('X-CSRF-TOKEN')
        if token:
            return token
            
        # 2. Tentar no body JSON
        if request.is_json:
            try:
                json_data = request.get_json(silent=True)
                if json_data and isinstance(json_data, dict):
                    token = json_data.get('csrf_token')
                    if token:
                        return token
            except:
                pass
        
        # 3. Tentar em form data
        if request.form:
            token = request.form.get('csrf_token')
            if token:
                return token
                
        # 4. Tentar query params
        token = request.args.get('csrf_token')
        if token:
            return token
            
        return None