{"ast": null, "code": "import { supportsLocalStorage } from './helpers';\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nexport const localStorageAdapter = {\n  getItem: key => {\n    if (!supportsLocalStorage()) {\n      return null;\n    }\n    return globalThis.localStorage.getItem(key);\n  },\n  setItem: (key, value) => {\n    if (!supportsLocalStorage()) {\n      return;\n    }\n    globalThis.localStorage.setItem(key, value);\n  },\n  removeItem: key => {\n    if (!supportsLocalStorage()) {\n      return;\n    }\n    globalThis.localStorage.removeItem(key);\n  }\n};\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store = {}) {\n  return {\n    getItem: key => {\n      return store[key] || null;\n    },\n    setItem: (key, value) => {\n      store[key] = value;\n    },\n    removeItem: key => {\n      delete store[key];\n    }\n  };\n}", "map": {"version": 3, "names": ["supportsLocalStorage", "localStorageAdapter", "getItem", "key", "globalThis", "localStorage", "setItem", "value", "removeItem", "memoryLocalStorageAdapter", "store"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\node_modules\\@supabase\\auth-js\\src\\lib\\local-storage.ts"], "sourcesContent": ["import { supportsLocalStorage } from './helpers'\nimport { SupportedStorage } from './types'\n\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nexport const localStorageAdapter: SupportedStorage = {\n  getItem: (key) => {\n    if (!supportsLocalStorage()) {\n      return null\n    }\n\n    return globalThis.localStorage.getItem(key)\n  },\n  setItem: (key, value) => {\n    if (!supportsLocalStorage()) {\n      return\n    }\n\n    globalThis.localStorage.setItem(key, value)\n  },\n  removeItem: (key) => {\n    if (!supportsLocalStorage()) {\n      return\n    }\n\n    globalThis.localStorage.removeItem(key)\n  },\n}\n\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store: { [key: string]: string } = {}): SupportedStorage {\n  return {\n    getItem: (key) => {\n      return store[key] || null\n    },\n\n    setItem: (key, value) => {\n      store[key] = value\n    },\n\n    removeItem: (key) => {\n      delete store[key]\n    },\n  }\n}\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,WAAW;AAGhD;;;AAGA,OAAO,MAAMC,mBAAmB,GAAqB;EACnDC,OAAO,EAAGC,GAAG,IAAI;IACf,IAAI,CAACH,oBAAoB,EAAE,EAAE;MAC3B,OAAO,IAAI;;IAGb,OAAOI,UAAU,CAACC,YAAY,CAACH,OAAO,CAACC,GAAG,CAAC;EAC7C,CAAC;EACDG,OAAO,EAAEA,CAACH,GAAG,EAAEI,KAAK,KAAI;IACtB,IAAI,CAACP,oBAAoB,EAAE,EAAE;MAC3B;;IAGFI,UAAU,CAACC,YAAY,CAACC,OAAO,CAACH,GAAG,EAAEI,KAAK,CAAC;EAC7C,CAAC;EACDC,UAAU,EAAGL,GAAG,IAAI;IAClB,IAAI,CAACH,oBAAoB,EAAE,EAAE;MAC3B;;IAGFI,UAAU,CAACC,YAAY,CAACG,UAAU,CAACL,GAAG,CAAC;EACzC;CACD;AAED;;;;AAIA,OAAM,SAAUM,yBAAyBA,CAACC,KAAA,GAAmC,EAAE;EAC7E,OAAO;IACLR,OAAO,EAAGC,GAAG,IAAI;MACf,OAAOO,KAAK,CAACP,GAAG,CAAC,IAAI,IAAI;IAC3B,CAAC;IAEDG,OAAO,EAAEA,CAACH,GAAG,EAAEI,KAAK,KAAI;MACtBG,KAAK,CAACP,GAAG,CAAC,GAAGI,KAAK;IACpB,CAAC;IAEDC,UAAU,EAAGL,GAAG,IAAI;MAClB,OAAOO,KAAK,CAACP,GAAG,CAAC;IACnB;GACD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}