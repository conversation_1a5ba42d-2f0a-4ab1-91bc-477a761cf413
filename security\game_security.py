from flask import request, abort, g
import time
import json
import hmac
import hashlib
import base64
from functools import wraps

class GameSecurity:
    """
    Security specific to game mechanics and anti-cheat
    """
    
    def __init__(self, app=None):
        self.app = app
        
        # Track transactions to prevent duplicates
        self.processed_transactions = {}
        self.max_transaction_history = 1000
        
        # Set up transaction verification key
        self.transaction_key = None
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize with Flask app"""
        self.app = app
        
        # Use the app's secret key for transaction verification
        self.transaction_key = app.config.get('SECRET_KEY', None)
        if not self.transaction_key:
            raise ValueError("App must have SECRET_KEY configured for GameSecurity")
        
        print("✅ Game security middleware initialized")
    
    def verify_game_transaction(self, f):
        """
        Decorator to verify and prevent duplicate game transactions
        
        This prevents replay attacks and duplicate transactions
        """
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Skip for GET requests
            if request.method == 'GET':
                return f(*args, **kwargs)
            
            # Get transaction data
            if request.is_json:
                data = request.get_json(silent=True) or {}
            else:
                data = request.form.to_dict() or {}
            
            # Check for transaction ID
            transaction_id = data.get('transaction_id')
            if not transaction_id:
                print("⚠️ Missing transaction ID")
                abort(400, description="Missing transaction ID")
            
            # Check if this transaction has already been processed
            if transaction_id in self.processed_transactions:
                print(f"⚠️ Duplicate transaction detected: {transaction_id}")
                abort(409, description="Transaction already processed")
            
            # Verify transaction timestamp to prevent very old transactions
            timestamp = data.get('timestamp')
            if timestamp:
                try:
                    tx_time = float(timestamp)
                    current_time = time.time()
                    
                    # Transaction should not be from the future or too old (> 5 minutes)
                    if tx_time > current_time or tx_time < (current_time - 300):
                        print(f"⚠️ Invalid transaction timestamp: {timestamp}")
                        abort(400, description="Invalid transaction timestamp")
                except ValueError:
                    print(f"⚠️ Invalid timestamp format: {timestamp}")
                    abort(400, description="Invalid timestamp format")
            
            # Record this transaction ID
            self.processed_transactions[transaction_id] = time.time()
            
            # Trim transaction history if needed
            if len(self.processed_transactions) > self.max_transaction_history:
                # Sort by timestamp and keep only recent transactions
                sorted_transactions = sorted(
                    self.processed_transactions.items(), 
                    key=lambda x: x[1]
                )
                
                # Keep only the most recent transactions
                keep_count = self.max_transaction_history // 2
                self.processed_transactions = dict(sorted_transactions[-keep_count:])
            
            # Continue with the request
            return f(*args, **kwargs)
        return decorated_function
    
    def sign_transaction(self, transaction_data):
        """
        Generate a cryptographic signature for game transactions
        
        Use this to sign important game state changes that will be
        verified on the server side
        """
        if not isinstance(transaction_data, dict):
            raise ValueError("Transaction data must be a dictionary")
            
        # Convert to JSON string
        json_data = json.dumps(transaction_data, sort_keys=True)
        
        # Create HMAC signature
        signature = hmac.new(
            self.transaction_key.encode('utf-8'),
            json_data.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        
        # Return base64 encoded signature
        return base64.b64encode(signature).decode('utf-8')
    
    def verify_signature(self, transaction_data, signature):
        """Verify a transaction signature"""
        if not isinstance(transaction_data, dict):
            return False
            
        # Convert to JSON string
        json_data = json.dumps(transaction_data, sort_keys=True)
        
        # Decode the provided signature
        try:
            signature_bytes = base64.b64decode(signature)
        except Exception:
            return False
        
        # Calculate expected signature
        expected_signature = hmac.new(
            self.transaction_key.encode('utf-8'),
            json_data.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        
        # Compare signatures
        return hmac.compare_digest(signature_bytes, expected_signature)
    
    def verify_resource_ownership(self, user_id, resource_id, resource_type):
        """
        Verify a user owns a specific game resource
        
        This should be implemented according to your game's data model
        """
        # This is a placeholder - implement based on your game's data model
        # Example implementation:
        # 
        # db = firestore.client()
        # 
        # if resource_type == 'skill':
        #     # Check if skill is in user's inventory
        #     user_ref = db.collection('users').document(user_id)
        #     user_data = user_ref.get().to_dict()
        #     
        #     if not user_data or 'skills' not in user_data:
        #         return False
        #     
        #     return resource_id in user_data['skills']
        # 
        # return False
        
        # Default implementation - override this!
        print("⚠️ verify_resource_ownership not fully implemented!")
        return True
    
    def enforce_game_limits(self):
        """
        Apply game-specific rate limits and constraints
        
        This can be used for things like:
        - Limiting purchases per day
        - Enforcing cooldown periods
        - Preventing transaction spam
        """
        # This is a placeholder - implement based on your game mechanics
        pass