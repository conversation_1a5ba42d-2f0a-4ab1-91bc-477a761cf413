{"name": "shack-frontend", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@supabase/supabase-js": "^2.50.5", "@tanstack/react-query": "^5.8.4", "@types/node": "^20.8.10", "@types/react": "^18.2.33", "@types/react-dom": "^18.2.14", "ajv": "^6.12.6", "axios": "^1.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.17.0", "react-scripts": "5.0.1", "typescript": "^5.2.2", "web-vitals": "^3.5.0", "zustand": "^4.4.6"}, "devDependencies": {"@tailwindcss/forms": "^0.5.6", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}