{"ast": null, "code": "// src/queryCache.ts\nimport { hashQueryKeyByOptions, matchQuery } from \"./utils.js\";\nimport { Query } from \"./query.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar QueryCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({\n        type: \"removed\",\n        query\n      });\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.getAll().forEach(query => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = {\n      exact: true,\n      ...filters\n    };\n    return this.getAll().find(query => matchQuery(defaultedFilters, query));\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter(query => matchQuery(filters, query)) : queries;\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(listener => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    notifyManager.batch(() => {\n      this.getAll().forEach(query => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    notifyManager.batch(() => {\n      this.getAll().forEach(query => {\n        query.onOnline();\n      });\n    });\n  }\n};\nexport { QueryCache };", "map": {"version": 3, "names": ["hashQueryKeyByOptions", "matchQuery", "Query", "notify<PERSON><PERSON>ger", "Subscribable", "Query<PERSON>ache", "constructor", "config", "queries", "Map", "build", "client", "options", "state", "query<PERSON><PERSON>", "queryHash", "query", "get", "defaultQueryOptions", "defaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "has", "set", "notify", "type", "remove", "queryInMap", "destroy", "delete", "clear", "batch", "getAll", "for<PERSON>ach", "values", "find", "filters", "defaultedFilters", "exact", "findAll", "Object", "keys", "length", "filter", "event", "listeners", "listener", "onFocus", "onOnline"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\node_modules\\@tanstack\\query-core\\src\\queryCache.ts"], "sourcesContent": ["import { hashQueryKeyByOptions, matchQuery } from './utils'\nimport { Query } from './query'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport type { QueryFilters } from './utils'\nimport type { Action, QueryState } from './query'\nimport type {\n  DefaultError,\n  NotifyEvent,\n  QueryKey,\n  QueryOptions,\n  WithRequired,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (\n    error: DefaultError,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: DefaultError | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\nexport type QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\nexport interface QueryStore {\n  has: (queryHash: string) => boolean\n  set: (queryHash: string, query: Query) => void\n  get: (queryHash: string) => Query | undefined\n  delete: (queryHash: string) => void\n  values: () => IterableIterator<Query>\n}\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  #queries: QueryStore\n\n  constructor(public config: QueryCacheConfig = {}) {\n    super()\n    this.#queries = new Map<string, Query>()\n  }\n\n  build<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    client: QueryClient,\n    options: WithRequired<\n      QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query)\n\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.#queries.get(query.queryHash)\n\n    if (queryInMap) {\n      query.destroy()\n\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash)\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.#queries.get(queryHash) as\n      | Query<TQueryFnData, TError, TData, TQueryKey>\n      | undefined\n  }\n\n  getAll(): Array<Query> {\n    return [...this.#queries.values()]\n  }\n\n  find<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData>(\n    filters: WithRequired<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const defaultedFilters = { exact: true, ...filters }\n\n    return this.getAll().find((query) =>\n      matchQuery(defaultedFilters, query),\n    ) as Query<TQueryFnData, TError, TData> | undefined\n  }\n\n  findAll(filters: QueryFilters<any> = {}): Array<Query> {\n    const queries = this.getAll()\n    return Object.keys(filters).length > 0\n      ? queries.filter((query) => matchQuery(filters, query))\n      : queries\n  }\n\n  notify(event: QueryCacheNotifyEvent): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n"], "mappings": ";AAAA,SAASA,qBAAA,EAAuBC,UAAA,QAAkB;AAClD,SAASC,KAAA,QAAa;AACtB,SAASC,aAAA,QAAqB;AAC9B,SAASC,YAAA,QAAoB;AAwFtB,IAAMC,UAAA,GAAN,cAAyBD,YAAA,CAAiC;EAG/DE,YAAmBC,MAAA,GAA2B,CAAC,GAAG;IAChD,MAAM;IADW,KAAAA,MAAA,GAAAA,MAAA;IAEjB,KAAK,CAAAC,OAAA,GAAW,mBAAIC,GAAA,CAAmB;EACzC;EALA,CAAAD,OAAA;EAOAE,MAMEC,MAAA,EACAC,OAAA,EAIAC,KAAA,EAC+C;IAC/C,MAAMC,QAAA,GAAWF,OAAA,CAAQE,QAAA;IACzB,MAAMC,SAAA,GACJH,OAAA,CAAQG,SAAA,IAAaf,qBAAA,CAAsBc,QAAA,EAAUF,OAAO;IAC9D,IAAII,KAAA,GAAQ,KAAKC,GAAA,CAA4CF,SAAS;IAEtE,IAAI,CAACC,KAAA,EAAO;MACVA,KAAA,GAAQ,IAAId,KAAA,CAAM;QAChBS,MAAA;QACAG,QAAA;QACAC,SAAA;QACAH,OAAA,EAASD,MAAA,CAAOO,mBAAA,CAAoBN,OAAO;QAC3CC,KAAA;QACAM,cAAA,EAAgBR,MAAA,CAAOS,gBAAA,CAAiBN,QAAQ;MAClD,CAAC;MACD,KAAKO,GAAA,CAAIL,KAAK;IAChB;IAEA,OAAOA,KAAA;EACT;EAEAK,IAAIL,KAAA,EAAwC;IAC1C,IAAI,CAAC,KAAK,CAAAR,OAAA,CAASc,GAAA,CAAIN,KAAA,CAAMD,SAAS,GAAG;MACvC,KAAK,CAAAP,OAAA,CAASe,GAAA,CAAIP,KAAA,CAAMD,SAAA,EAAWC,KAAK;MAExC,KAAKQ,MAAA,CAAO;QACVC,IAAA,EAAM;QACNT;MACF,CAAC;IACH;EACF;EAEAU,OAAOV,KAAA,EAAwC;IAC7C,MAAMW,UAAA,GAAa,KAAK,CAAAnB,OAAA,CAASS,GAAA,CAAID,KAAA,CAAMD,SAAS;IAEpD,IAAIY,UAAA,EAAY;MACdX,KAAA,CAAMY,OAAA,CAAQ;MAEd,IAAID,UAAA,KAAeX,KAAA,EAAO;QACxB,KAAK,CAAAR,OAAA,CAASqB,MAAA,CAAOb,KAAA,CAAMD,SAAS;MACtC;MAEA,KAAKS,MAAA,CAAO;QAAEC,IAAA,EAAM;QAAWT;MAAM,CAAC;IACxC;EACF;EAEAc,MAAA,EAAc;IACZ3B,aAAA,CAAc4B,KAAA,CAAM,MAAM;MACxB,KAAKC,MAAA,CAAO,EAAEC,OAAA,CAASjB,KAAA,IAAU;QAC/B,KAAKU,MAAA,CAAOV,KAAK;MACnB,CAAC;IACH,CAAC;EACH;EAEAC,IAMEF,SAAA,EAC2D;IAC3D,OAAO,KAAK,CAAAP,OAAA,CAASS,GAAA,CAAIF,SAAS;EAGpC;EAEAiB,OAAA,EAAuB;IACrB,OAAO,CAAC,GAAG,KAAK,CAAAxB,OAAA,CAAS0B,MAAA,CAAO,CAAC;EACnC;EAEAC,KACEC,OAAA,EACgD;IAChD,MAAMC,gBAAA,GAAmB;MAAEC,KAAA,EAAO;MAAM,GAAGF;IAAQ;IAEnD,OAAO,KAAKJ,MAAA,CAAO,EAAEG,IAAA,CAAMnB,KAAA,IACzBf,UAAA,CAAWoC,gBAAA,EAAkBrB,KAAK,CACpC;EACF;EAEAuB,QAAQH,OAAA,GAA6B,CAAC,GAAiB;IACrD,MAAM5B,OAAA,GAAU,KAAKwB,MAAA,CAAO;IAC5B,OAAOQ,MAAA,CAAOC,IAAA,CAAKL,OAAO,EAAEM,MAAA,GAAS,IACjClC,OAAA,CAAQmC,MAAA,CAAQ3B,KAAA,IAAUf,UAAA,CAAWmC,OAAA,EAASpB,KAAK,CAAC,IACpDR,OAAA;EACN;EAEAgB,OAAOoB,KAAA,EAAoC;IACzCzC,aAAA,CAAc4B,KAAA,CAAM,MAAM;MACxB,KAAKc,SAAA,CAAUZ,OAAA,CAASa,QAAA,IAAa;QACnCA,QAAA,CAASF,KAAK;MAChB,CAAC;IACH,CAAC;EACH;EAEAG,QAAA,EAAgB;IACd5C,aAAA,CAAc4B,KAAA,CAAM,MAAM;MACxB,KAAKC,MAAA,CAAO,EAAEC,OAAA,CAASjB,KAAA,IAAU;QAC/BA,KAAA,CAAM+B,OAAA,CAAQ;MAChB,CAAC;IACH,CAAC;EACH;EAEAC,SAAA,EAAiB;IACf7C,aAAA,CAAc4B,KAAA,CAAM,MAAM;MACxB,KAAKC,MAAA,CAAO,EAAEC,OAAA,CAASjB,KAAA,IAAU;QAC/BA,KAAA,CAAMgC,QAAA,CAAS;MACjB,CAAC;IACH,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}