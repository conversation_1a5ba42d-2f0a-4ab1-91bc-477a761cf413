{"ast": null, "code": "// src/infiniteQueryObserver.ts\nimport { QueryObserver } from \"./queryObserver.js\";\nimport { hasNextPage, hasPreviousPage, infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar InfiniteQueryObserver = class extends QueryObserver {\n  constructor(client, options) {\n    super(client, options);\n  }\n  bindMethods() {\n    super.bindMethods();\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  }\n  setOptions(options) {\n    super.setOptions({\n      ...options,\n      behavior: infiniteQueryBehavior()\n    });\n  }\n  getOptimisticResult(options) {\n    options.behavior = infiniteQueryBehavior();\n    return super.getOptimisticResult(options);\n  }\n  fetchNextPage(options) {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: {\n          direction: \"forward\"\n        }\n      }\n    });\n  }\n  fetchPreviousPage(options) {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: {\n          direction: \"backward\"\n        }\n      }\n    });\n  }\n  createResult(query, options) {\n    const {\n      state\n    } = query;\n    const parentResult = super.createResult(query, options);\n    const {\n      isFetching,\n      isRefetching,\n      isError,\n      isRefetchError\n    } = parentResult;\n    const fetchDirection = state.fetchMeta?.fetchMore?.direction;\n    const isFetchNextPageError = isError && fetchDirection === \"forward\";\n    const isFetchingNextPage = isFetching && fetchDirection === \"forward\";\n    const isFetchPreviousPageError = isError && fetchDirection === \"backward\";\n    const isFetchingPreviousPage = isFetching && fetchDirection === \"backward\";\n    const result = {\n      ...parentResult,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data),\n      hasPreviousPage: hasPreviousPage(options, state.data),\n      isFetchNextPageError,\n      isFetchingNextPage,\n      isFetchPreviousPageError,\n      isFetchingPreviousPage,\n      isRefetchError: isRefetchError && !isFetchNextPageError && !isFetchPreviousPageError,\n      isRefetching: isRefetching && !isFetchingNextPage && !isFetchingPreviousPage\n    };\n    return result;\n  }\n};\nexport { InfiniteQueryObserver };", "map": {"version": 3, "names": ["QueryObserver", "hasNextPage", "hasPreviousPage", "infiniteQueryBehavior", "InfiniteQueryObserver", "constructor", "client", "options", "bindMethods", "fetchNextPage", "bind", "fetchPreviousPage", "setOptions", "behavior", "getOptimisticResult", "fetch", "meta", "fetchMore", "direction", "createResult", "query", "state", "parentResult", "isFetching", "isRefetching", "isError", "isRefetchError", "fetchDirection", "fetchMeta", "isFetchNextPageError", "isFetchingNextPage", "isFetchPreviousPageError", "isFetchingPreviousPage", "result", "data"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\query-core\\src\\infiniteQueryObserver.ts"], "sourcesContent": ["import { QueryObserver } from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport type { Subscribable } from './subscribable'\nimport type {\n  DefaultError,\n  DefaultedInfiniteQueryObserverOptions,\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverBaseResult,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  QueryKey,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>,\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  T<PERSON><PERSON>y<PERSON><PERSON> extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  TData,\n  InfiniteData<TQueryFnData, TPageParam>,\n  TQueryKey\n> {\n  // Type override\n  subscribe!: Subscribable<\n    InfiniteQueryObserverListener<TData, TError>\n  >['subscribe']\n\n  // Type override\n  getCurrentResult!: ReplaceReturnType<\n    QueryObserver<\n      TQueryFnData,\n      TError,\n      TData,\n      InfiniteData<TQueryFnData, TPageParam>,\n      TQueryKey\n    >['getCurrentResult'],\n    InfiniteQueryObserverResult<TData, TError>\n  >\n\n  // Type override\n  protected fetch!: ReplaceReturnType<\n    QueryObserver<\n      TQueryFnData,\n      TError,\n      TData,\n      InfiniteData<TQueryFnData, TPageParam>,\n      TQueryKey\n    >['fetch'],\n    Promise<InfiniteQueryObserverResult<TData, TError>>\n  >\n\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): void {\n    super.setOptions({\n      ...options,\n      behavior: infiniteQueryBehavior(),\n    })\n  }\n\n  getOptimisticResult(\n    options: DefaultedInfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage(\n    options?: FetchNextPageOptions,\n  ): Promise<InfiniteQueryObserverResult<TData, TError>> {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'forward' },\n      },\n    })\n  }\n\n  fetchPreviousPage(\n    options?: FetchPreviousPageOptions,\n  ): Promise<InfiniteQueryObserverResult<TData, TError>> {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'backward' },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<\n      TQueryFnData,\n      TError,\n      InfiniteData<TQueryFnData, TPageParam>,\n      TQueryKey\n    >,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const parentResult = super.createResult(query, options)\n\n    const { isFetching, isRefetching, isError, isRefetchError } = parentResult\n    const fetchDirection = state.fetchMeta?.fetchMore?.direction\n\n    const isFetchNextPageError = isError && fetchDirection === 'forward'\n    const isFetchingNextPage = isFetching && fetchDirection === 'forward'\n\n    const isFetchPreviousPageError = isError && fetchDirection === 'backward'\n    const isFetchingPreviousPage = isFetching && fetchDirection === 'backward'\n\n    const result: InfiniteQueryObserverBaseResult<TData, TError> = {\n      ...parentResult,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data),\n      hasPreviousPage: hasPreviousPage(options, state.data),\n      isFetchNextPageError,\n      isFetchingNextPage,\n      isFetchPreviousPageError,\n      isFetchingPreviousPage,\n      isRefetchError:\n        isRefetchError && !isFetchNextPageError && !isFetchPreviousPageError,\n      isRefetching:\n        isRefetching && !isFetchingNextPage && !isFetchingPreviousPage,\n    }\n\n    return result as InfiniteQueryObserverResult<TData, TError>\n  }\n}\n\ntype ReplaceReturnType<\n  TFunction extends (...args: Array<any>) => unknown,\n  TReturn,\n> = (...args: Parameters<TFunction>) => TReturn\n"], "mappings": ";AAAA,SAASA,aAAA,QAAqB;AAC9B,SACEC,WAAA,EACAC,eAAA,EACAC,qBAAA,QACK;AAoBA,IAAMC,qBAAA,GAAN,cAMGJ,aAAA,CAMR;EA8BAK,YACEC,MAAA,EACAC,OAAA,EAOA;IACA,MAAMD,MAAA,EAAQC,OAAO;EACvB;EAEUC,YAAA,EAAoB;IAC5B,MAAMA,WAAA,CAAY;IAClB,KAAKC,aAAA,GAAgB,KAAKA,aAAA,CAAcC,IAAA,CAAK,IAAI;IACjD,KAAKC,iBAAA,GAAoB,KAAKA,iBAAA,CAAkBD,IAAA,CAAK,IAAI;EAC3D;EAEAE,WACEL,OAAA,EAOM;IACN,MAAMK,UAAA,CAAW;MACf,GAAGL,OAAA;MACHM,QAAA,EAAUV,qBAAA,CAAsB;IAClC,CAAC;EACH;EAEAW,oBACEP,OAAA,EAO4C;IAC5CA,OAAA,CAAQM,QAAA,GAAWV,qBAAA,CAAsB;IACzC,OAAO,MAAMW,mBAAA,CAAoBP,OAAO;EAI1C;EAEAE,cACEF,OAAA,EACqD;IACrD,OAAO,KAAKQ,KAAA,CAAM;MAChB,GAAGR,OAAA;MACHS,IAAA,EAAM;QACJC,SAAA,EAAW;UAAEC,SAAA,EAAW;QAAU;MACpC;IACF,CAAC;EACH;EAEAP,kBACEJ,OAAA,EACqD;IACrD,OAAO,KAAKQ,KAAA,CAAM;MAChB,GAAGR,OAAA;MACHS,IAAA,EAAM;QACJC,SAAA,EAAW;UAAEC,SAAA,EAAW;QAAW;MACrC;IACF,CAAC;EACH;EAEUC,aACRC,KAAA,EAMAb,OAAA,EAO4C;IAC5C,MAAM;MAAEc;IAAM,IAAID,KAAA;IAClB,MAAME,YAAA,GAAe,MAAMH,YAAA,CAAaC,KAAA,EAAOb,OAAO;IAEtD,MAAM;MAAEgB,UAAA;MAAYC,YAAA;MAAcC,OAAA;MAASC;IAAe,IAAIJ,YAAA;IAC9D,MAAMK,cAAA,GAAiBN,KAAA,CAAMO,SAAA,EAAWX,SAAA,EAAWC,SAAA;IAEnD,MAAMW,oBAAA,GAAuBJ,OAAA,IAAWE,cAAA,KAAmB;IAC3D,MAAMG,kBAAA,GAAqBP,UAAA,IAAcI,cAAA,KAAmB;IAE5D,MAAMI,wBAAA,GAA2BN,OAAA,IAAWE,cAAA,KAAmB;IAC/D,MAAMK,sBAAA,GAAyBT,UAAA,IAAcI,cAAA,KAAmB;IAEhE,MAAMM,MAAA,GAAyD;MAC7D,GAAGX,YAAA;MACHb,aAAA,EAAe,KAAKA,aAAA;MACpBE,iBAAA,EAAmB,KAAKA,iBAAA;MACxBV,WAAA,EAAaA,WAAA,CAAYM,OAAA,EAASc,KAAA,CAAMa,IAAI;MAC5ChC,eAAA,EAAiBA,eAAA,CAAgBK,OAAA,EAASc,KAAA,CAAMa,IAAI;MACpDL,oBAAA;MACAC,kBAAA;MACAC,wBAAA;MACAC,sBAAA;MACAN,cAAA,EACEA,cAAA,IAAkB,CAACG,oBAAA,IAAwB,CAACE,wBAAA;MAC9CP,YAAA,EACEA,YAAA,IAAgB,CAACM,kBAAA,IAAsB,CAACE;IAC5C;IAEA,OAAOC,MAAA;EACT;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}