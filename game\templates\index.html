{% extends "base.html" %}
{% block content %}

<div id="auth-screen">
  <div id="login-view">
    <h2 class="text-xl font-semibold mb-6 text-center text-primary-text"><PERSON><PERSON><PERSON></h2>
    <form id="form-login" class="space-y-4">
      <input name="email" placeholder="Email" type="email" class="w-full p-3 rounded-lg bg-surface-elevated text-primary-text border border-border-color focus:border-accent-blue focus:outline-none transition-colors" required>
      <input name="password" type="password" placeholder="Senha" class="w-full p-3 rounded-lg bg-surface-elevated text-primary-text border border-border-color focus:border-accent-blue focus:outline-none transition-colors" required>
      <button type="submit" class="w-full bg-accent-blue hover:bg-blue-600 text-white py-3 rounded-lg font-medium transition-colors">Entrar</button>
    </form>
    <p class="text-sm text-secondary-text mt-6 text-center">Não tem conta?
      <a href="#" onclick="toggleAuthView()" class="text-accent-blue hover:underline">Cadastre-se</a>
    </p>
  </div>
  <div id="register-view" style="display: none;">
    <h2 class="text-xl font-semibold mb-6 text-center text-primary-text">Criar Conta</h2>
    <form id="form-cadastro" class="space-y-4">
      <input name="nick" placeholder="Nickname" class="w-full p-3 rounded-lg bg-surface-elevated text-primary-text border border-border-color focus:border-accent-blue focus:outline-none transition-colors" required>
      <input name="email" placeholder="Email" type="email" class="w-full p-3 rounded-lg bg-surface-elevated text-primary-text border border-border-color focus:border-accent-blue focus:outline-none transition-colors" required>
      <input name="password" type="password" placeholder="Senha" class="w-full p-3 rounded-lg bg-surface-elevated text-primary-text border border-border-color focus:border-accent-blue focus:outline-none transition-colors" required>
      <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-medium transition-colors">Criar Conta</button>
    </form>
    <p class="text-sm text-secondary-text mt-6 text-center">Já tem conta? <a href="#" onclick="toggleAuthView()" class="text-accent-blue hover:underline">Entrar</a></p>
  </div>
</div>


<div id="game-screen" style="display: none;">

  <div id="main-dashboard" class="game-section">
    <h2 class="text-2xl font-semibold mb-4 flex items-center text-primary-text">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6 mr-3 text-accent-blue">
        <path d="M18.375 2.25c-1.035 0-1.875.84-1.875 1.875v15.75c0 1.035.84 1.875 1.875 1.875h.75c1.035 0 1.875-.84 1.875-1.875V4.125c0-1.036-.84-1.875-1.875-1.875h-.75ZM9.75 8.625c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-.75a1.875 1.875 0 0 1-1.875-1.875V8.625ZM3 13.125c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v6.75c0 1.035-.84 1.875-1.875 1.875h-.75A1.875 1.875 0 0 1 3 19.875v-6.75Z" />
      </svg>
      <span id="player-nick" class="text-primary-text"></span>
    </h2>
    
    <div class="p-4 rounded-lg mb-6 text-sm bg-surface-elevated border border-border-color shadow-lg">
        <div class="text-center mb-3 text-secondary-text">
          IP: <strong class="text-accent-blue" id="player-ip">?.?.?.?</strong>
        </div>
        <div class="flex justify-between items-center text-center">
            <div class="w-1/3 text-left">💰 Cash: <span class="text-green-400 font-semibold" id="player-money"></span></div>
            <div class="w-1/3">⭐ Nível: <span class="text-yellow-400 font-semibold" id="player-level"></span></div>
            <div class="w-1/3 text-right">🧠 Shack: <span class="text-blue-400 font-semibold" id="player-shack"></span></div>
        </div>
        <p class="text-xs mt-3 text-center text-secondary-text">
          EXP: <span id="player-xp"></span> / <span id="player-xp-needed"></span>
        </p>
        <div class="rounded-lg h-3 w-full mt-1 overflow-hidden bg-surface-default border border-border-color">
            <div id="xp-bar" class="h-full transition-all duration-300 bg-gradient-to-r from-blue-500 to-blue-600" style="width: 0%;"></div>
        </div>
    </div>

<div id="dashboard-buttons-grid" class="space-y-4 p-4 rounded-lg bg-surface-elevated border border-border-color shadow-lg">

    <!-- Linha 1: Scan, Apps, Grupo -->
    <div class="grid grid-cols-3 gap-4">
        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-accent-blue hover:bg-surface-hover" data-section-id="scan-section">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-8 h-8 mb-2 text-accent-blue">
                <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM8.547 4.505a8.25 8.25 0 1 0 11.672 8.214l-.46-.46a2.252 2.252 0 0 1-.422-.586l-1.08-2.16a.414.414 0 0 0-.663-.107.827.827 0 0 1-.812.21l-1.273-.363a.89.89 0 0 0-.738 1.595l.587.39c.59.395.674 1.23.172 1.732l-.2.2c-.211.212-.33.498-.33.796v.41c0 .409-.11.809-.32 1.158l-1.315 2.191a2.11 2.11 0 0 1-1.81 1.025 1.055 1.055 0 0 1-1.055-1.055v-1.172c0-.92-.56-1.747-1.414-2.089l-.654-.261a2.25 2.25 0 0 1-1.384-2.46l.007-.042a2.25 2.25 0 0 1 .29-.787l.09-.15a2.25 2.25 0 0 1 2.37-1.048l1.178.236a1.125 1.125 0 0 0 1.302-.795l.208-.73a1.125 1.125 0 0 0-.578-1.315l-.665-.332-.091.091a2.25 2.25 0 0 1-1.591.659h-.18c-.249 0-.487.1-.662.274a.931.931 0 0 1-1.458-1.137l1.279-2.132Z" clip-rule="evenodd" />
            </svg>
            <span class="font-medium text-primary-text">Scan</span>
        </button>
        
        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-accent-blue hover:bg-surface-hover" data-section-id="appstore-section">
            <svg class="w-8 h-8 mb-2 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5"></path>
            </svg>
            <span class="font-medium text-primary-text">Apps</span>
        </button>
        
        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-accent-blue hover:bg-surface-hover" data-section-id="group-section">
            <svg class="w-8 h-8 mb-2 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path fill-rule="evenodd" d="M8.25 6.75a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0ZM15.75 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM2.25 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM6.31 15.117A6.745 6.745 0 0 1 12 12a6.745 6.745 0 0 1 6.709 7.498.75.75 0 0 1-.372.568A12.696 12.696 0 0 1 12 21.75c-2.305 0-4.47-.612-6.337-1.684a.75.75 0 0 1-.372-.568 6.787 6.787 0 0 1 1.019-4.38Z" clip-rule="evenodd"></path>
            </svg>
            <span class="font-medium text-primary-text">Grupo</span>
        </button>
    </div>

    <!-- Linha 2: Mineração, Mercado, Habilidades -->
    <div class="grid grid-cols-3 gap-4">
        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-accent-blue hover:bg-surface-hover" data-section-id="mineradora-section">
            <svg class="w-8 h-8 mb-2 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.816H7.905a3.375 3.375 0 0 0-3.285 2.816L2.37 16.02a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Z"></path>
            </svg>
            <span class="font-medium text-primary-text">Mineração</span>
        </button>

        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-accent-blue hover:bg-surface-hover" data-section-id="mercado-negro-section">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 mb-2 text-accent-blue">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z" />
            </svg>
            <span class="font-medium text-primary-text">Mercado</span>
        </button>

        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-accent-blue hover:bg-surface-hover" data-section-id="habilidades-section">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 mb-2 text-accent-blue">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z" />
            </svg>
            <span class="font-medium text-primary-text">Habilidades</span>
        </button>
    </div>

    <!-- Linha 3: Logs, Ranking, Banco -->
    <div class="grid grid-cols-3 gap-4">
        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-accent-blue hover:bg-surface-hover" data-section-id="log-section">
            <svg class="w-8 h-8 mb-2 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6v-.75c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v10.5c0 .621.504 1.125 1.125 1.125h10.5c.621 0 1.125-.504 1.125-1.125v-1.5" />
            </svg>
            <span class="font-medium text-primary-text">Logs</span>
        </button>

        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-accent-blue hover:bg-surface-hover" data-section-id="ranking-section">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 mb-2 text-accent-blue">
                <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0" />
            </svg>
            <span class="font-medium text-primary-text">Ranking</span>
        </button>

        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-accent-blue hover:bg-surface-hover" data-section-id="banco-section">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 mb-2 text-accent-blue">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0 0 12 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75Z" />
            </svg>
            <span class="font-mono text-primary-text">BANK</span>
        </button>
    </div>

    <!-- Linha 4: Config e 2 espaços vazios -->
    <div class="grid grid-cols-3 gap-4">
        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105" data-section-id="configuracoes-section" style="background: linear-gradient(135deg, var(--cyber-bg-dark), var(--cyber-bg-medium)); color: var(--cyber-text); border: 1px solid var(--cyber-secondary); box-shadow: 0 0 10px var(--cyber-glow-soft);">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 mb-2" style="color: var(--cyber-secondary);">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a6.759 6.759 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
            </svg>
            <span class="font-mono">CONFIG</span>
        </button>

        <!-- Terminal -->
        <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-accent-blue hover:bg-surface-hover" data-section-id="terminal-section">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 mb-2 text-accent-blue">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 7.5l3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0021 18V6a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 6v12a2.25 2.25 0 002.25 2.25z" />
            </svg>
            <span class="font-medium text-primary-text">Terminal</span>
        </button>

        <!-- Segurança -->
        <button class="dashboard-btn relative p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-default border border-border-color hover:border-red-500 hover:bg-surface-hover" data-section-id="security-section">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 mb-2 text-red-500">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
            </svg>
            <span class="font-medium text-primary-text">Segurança</span>
            <div id="security-alert-badge" class="absolute top-1 right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center hidden shadow-lg border-2 border-background-primary">
                <span id="security-alert-count">0</span>
            </div>
        </button>
    </div>
</div>
  </div>

  <!-- Seção de Segurança -->
  <div id="security-section" class="game-section">
    <div class="max-w-4xl mx-auto p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-primary-text flex items-center gap-3">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-red-500">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
          </svg>
          Centro de Segurança
        </h2>
        <div class="flex items-center gap-3">
          <div id="security-last-update" class="text-sm text-secondary-text">
            Última atualização: --:--
          </div>
          <button id="refresh-security-btn" onclick="refreshSecurityData()" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2">
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
            </svg>
            Atualizar
          </button>
        </div>
      </div>

      <!-- Status de Segurança -->
      <div class="bg-surface-elevated border border-border-color rounded-xl p-6 mb-6">
        <h3 class="text-lg font-semibold text-primary-text mb-4 flex items-center gap-2">
          <svg class="w-6 h-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Status de Segurança
        </h3>
        <div id="security-status" class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Conteúdo será carregado dinamicamente -->
        </div>
      </div>

      <!-- Invasões Detectadas -->
      <div class="bg-surface-elevated border border-border-color rounded-xl p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-primary-text flex items-center gap-2">
            <svg class="w-6 h-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
            </svg>
            Invasões Detectadas
            <span id="invasions-count" class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">0</span>
          </h3>
          <button id="block-all-btn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
            Bloquear Todas
          </button>
        </div>
        <div id="invasions-list" class="space-y-3">
          <!-- Lista de invasões será carregada aqui -->
        </div>
      </div>

      <!-- Ações de Defesa -->
      <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
        <h3 class="text-lg font-semibold text-primary-text mb-4 flex items-center gap-2">
          <svg class="w-6 h-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
          </svg>
          Ações de Defesa
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button id="change-ip-btn" onclick="changePlayerIP()" class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg font-medium transition-colors flex items-center gap-3">
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
            </svg>
            <div class="text-left">
              <div>Trocar de IP</div>
              <div class="text-xs opacity-75">Custa 100 shacks</div>
            </div>
          </button>
          <button id="emergency-lockdown-btn" class="bg-red-600 hover:bg-red-700 text-white p-4 rounded-lg font-medium transition-colors flex items-center gap-3">
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
            </svg>
            <div class="text-left">
              <div>Bloqueio de Emergência</div>
              <div class="text-xs opacity-75">Bloqueia invasões + troca IP</div>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>

 <div id="log-section" class="game-section">
    <div class="flex items-center justify-between mt-8 mb-4 max-w-3xl mx-auto">
      <h3 class="text-lg font-bold text-white text-center">Log de Atividade do Sistema</h3>
      <button onclick="limparLogsUsuario()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-2">
        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v3M4 7h16" />
        </svg>
        Limpar Logs
      </button>
    </div>
    <div id="log-container" class="max-w-3xl mx-auto bg-gray-800 rounded-lg p-4 space-y-3"></div>
  </div>
  <div id="invaded-screen" class="game-section"></div>
  <div id="appstore-section" class="game-section h-full">
    <div class="h-full flex flex-col">
      <h3 class="text-lg font-bold py-4 text-center font-mono flex-shrink-0" style="color: var(--cyber-primary); text-shadow: 0 0 10px var(--cyber-glow);">SHACK STORE</h3>
      <div id="appstore-items-container" class="flex-1 overflow-hidden"></div>
    </div>
  </div>
  <div id="scan-section" class="game-section">
    <div id="scan-content"></div>
  </div>
  <div id="group-section" class="game-section"><h3 class="text-lg font-bold text-white mt-8 mb-4 text-center">Grupo</h3><div id="group-info-container"></div></div>
  <div id="ranking-section" class="game-section"><h3 class="text-lg font-bold text-white mt-8 mb-4 text-center">Ranking</h3><div id="ranking-container"></div></div>
  <div id="mineradora-section" class="game-section h-full"><h3 class="text-lg font-bold text-white mt-8 mb-4 text-center"></h3><div id="mineradora-container" class="h-full overflow-y-auto custom-scrollbar"></div></div>
  <div id="mercado-negro-section" class="game-section"><h3 class="text-lg font-bold text-white mt-8 mb-4 text-center">Mercado Negro</h3><div id="mercado-negro-container" class="max-w-4xl mx-auto"></div></div>
<div id="habilidades-section" class="game-section">
    <h3 class="text-lg font-bold text-white mt-8 mb-4 text-center">Habilidades</h3>
    <div id="habilidades-container" class="max-w-4xl mx-auto"></div>
</div>
<div id="banco-section" class="game-section">
    <h3 class="text-lg font-bold text-white mt-8 mb-4 text-center">Banco Digital</h3>
    <div id="banco-container" class="max-w-3xl mx-auto"></div>
</div>

<!-- Nova seção de Configurações -->
<div id="configuracoes-section" class="game-section">
    <h3 class="text-lg font-bold text-white mt-8 mb-4 text-center">Configurações</h3>
    <div id="configuracoes-container" class="max-w-2xl mx-auto"></div>
</div>

<!-- Seção de Notícias -->
<div id="news-section" class="game-section">
    <div id="news-container" class="max-w-4xl mx-auto"></div>
</div>

<!-- Seção do Terminal -->
<div id="terminal-section" class="game-section">
    <div id="terminal-container" class="max-w-4xl mx-auto"></div>
</div>

<!-- Seção do Shop para Supporters -->
<div id="shop-section" class="game-section h-full">
    <div class="h-full flex flex-col">
        <h3 class="text-lg font-bold py-4 text-center font-mono flex-shrink-0" style="color: var(--cyber-primary); text-shadow: 0 0 10px var(--cyber-glow);">SUPPORTER SHOP</h3>
        <div id="shop-container" class="flex-1 overflow-hidden"></div>
    </div>
</div>

</div>

<style>
    .dashboard-btn:hover {
        background: var(--surface-hover) !important;
        border-color: var(--accent-blue) !important;
        box-shadow: 0 4px 20px rgba(0, 123, 255, 0.2) !important;
        color: var(--primary-text) !important;
        transform: scale(1.05) !important;
    }
    
    .dashboard-btn:hover svg {
        color: var(--accent-blue) !important;
    }
    
    .dashboard-btn:hover span {
        color: var(--accent-blue) !important;
        font-weight: 600;
    }
</style>

{% endblock %}