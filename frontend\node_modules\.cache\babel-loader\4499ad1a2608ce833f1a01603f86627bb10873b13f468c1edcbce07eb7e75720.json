{"ast": null, "code": "import axios from 'axios';\n\n// Tipos para as respostas da API\n\nclass ApiService {\n  constructor() {\n    this.client = void 0;\n    this.client = axios.create({\n      baseURL: process.env.REACT_APP_API_URL || '',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      timeout: 10000\n    });\n    this.setupInterceptors();\n  }\n  setupInterceptors() {\n    // Request interceptor - adiciona token de autenticação\n    this.client.interceptors.request.use(config => {\n      const token = localStorage.getItem('shack_token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor - trata erros de autenticação\n    this.client.interceptors.response.use(response => response, error => {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        // Token inválido ou expirado\n        localStorage.removeItem('shack_token');\n        localStorage.removeItem('shack_user');\n        window.location.href = '/login';\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  // === MÉTODOS DE AUTENTICAÇÃO ===\n  async login(credentials) {\n    try {\n      const response = await this.client.post('/api/auth/login', credentials);\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      throw new Error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.mensagem) || 'Erro ao fazer login');\n    }\n  }\n  async register(userData) {\n    try {\n      const response = await this.client.post('/api/auth/register', userData);\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.mensagem) || 'Erro ao criar conta');\n    }\n  }\n  async verifyToken() {\n    try {\n      const response = await this.client.get('/api/auth/check');\n      return response.data.sucesso;\n    } catch (error) {\n      console.log('Erro na verificação de token:', error);\n      return false;\n    }\n  }\n\n  // === MÉTODOS DO JOGO ===\n  async getPlayerData() {\n    try {\n      const response = await this.client.get('/api/jogador');\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.mensagem) || 'Erro ao carregar dados do jogador');\n    }\n  }\n  async scanTargets() {\n    try {\n      const response = await this.client.get('/api/scan');\n      return response.data;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      throw new Error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.mensagem) || 'Erro ao escanear alvos');\n    }\n  }\n  async exploitTarget(targetIp, percentage) {\n    try {\n      const response = await this.client.post('/api/transferir', {\n        alvo_ip: targetIp,\n        porcentagem: percentage\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      throw new Error(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.mensagem) || 'Erro ao exploitar alvo');\n    }\n  }\n\n  // === MÉTODOS DE CHAT ===\n  async getChatMessages() {\n    try {\n      const response = await this.client.get('/api/chat/messages');\n      return response.data;\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      throw new Error(((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.mensagem) || 'Erro ao carregar mensagens');\n    }\n  }\n  async sendChatMessage(message) {\n    try {\n      const response = await this.client.post('/api/chat/send', {\n        message\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      throw new Error(((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.mensagem) || 'Erro ao enviar mensagem');\n    }\n  }\n\n  // === MÉTODOS DE APPS ===\n  async getAppStore() {\n    try {\n      const response = await this.client.get('/api/appstore');\n      return response.data;\n    } catch (error) {\n      var _error$response9, _error$response9$data;\n      throw new Error(((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.mensagem) || 'Erro ao carregar loja');\n    }\n  }\n  async purchaseApp(appName, quantity) {\n    try {\n      const response = await this.client.post('/api/appstore/comprar', {\n        item: appName,\n        quantidade: quantity\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response0, _error$response0$data;\n      throw new Error(((_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : (_error$response0$data = _error$response0.data) === null || _error$response0$data === void 0 ? void 0 : _error$response0$data.mensagem) || 'Erro ao comprar app');\n    }\n  }\n\n  // === MÉTODOS UTILITÁRIOS ===\n  async getCsrfToken() {\n    try {\n      const response = await this.client.get('/api/csrf-token');\n      return response.data.csrf_token;\n    } catch (error) {\n      return '';\n    }\n  }\n\n  // Método para fazer requisições customizadas\n  async customRequest(method, endpoint, data) {\n    try {\n      const response = await this.client.request({\n        method,\n        url: endpoint,\n        data\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response1, _error$response1$data;\n      throw new Error(((_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : (_error$response1$data = _error$response1.data) === null || _error$response1$data === void 0 ? void 0 : _error$response1$data.mensagem) || 'Erro na requisição');\n    }\n  }\n}\n\n// Instância singleton do serviço de API\nconst apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "ApiService", "constructor", "client", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "headers", "timeout", "setupInterceptors", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "login", "credentials", "post", "data", "_error$response2", "_error$response2$data", "Error", "mensagem", "register", "userData", "_error$response3", "_error$response3$data", "verifyToken", "get", "sucesso", "console", "log", "getPlayerData", "_error$response4", "_error$response4$data", "scanTargets", "_error$response5", "_error$response5$data", "exploitTarget", "targetIp", "percentage", "alvo_ip", "porcentagem", "_error$response6", "_error$response6$data", "getChatMessages", "_error$response7", "_error$response7$data", "sendChatMessage", "message", "_error$response8", "_error$response8$data", "getAppStore", "_error$response9", "_error$response9$data", "purchaseApp", "appName", "quantity", "item", "quantidade", "_error$response0", "_error$response0$data", "getCsrfToken", "csrf_token", "customRequest", "method", "endpoint", "url", "_error$response1", "_error$response1$data", "apiService"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance } from 'axios';\n\n// Tipos para as respostas da API\nexport interface ApiResponse<T = any> {\n  sucesso: boolean;\n  mensagem?: string;\n  data?: T;\n  [key: string]: any;\n}\n\nexport interface LoginResponse {\n  sucesso: boolean;\n  mensagem?: string;\n  user?: {\n    uid: string;\n    nick: string;\n    email: string;\n  };\n  token?: string;\n}\n\nexport interface PlayerData {\n  uid: string;\n  nick: string;\n  email: string;\n  dinheiro: number;\n  cpu: number;\n  firewall: number;\n  bankguard: number;\n  bruteforce: number;\n  proxyvpn: number;\n  ip: string;\n  last_seen: string;\n  created_at: string;\n}\n\nexport interface ChatMessage {\n  id: string;\n  uid: string;\n  nick: string;\n  message: string;\n  timestamp: string;\n}\n\nexport interface ScanTarget {\n  ip: string;\n  nick: string;\n  dinheiro: number;\n  cpu: number;\n  firewall: number;\n  last_seen: string;\n}\n\nclass ApiService {\n  private client: AxiosInstance;\n\n  constructor() {\n    this.client = axios.create({\n      baseURL: process.env.REACT_APP_API_URL || '',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      timeout: 10000,\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor - adiciona token de autenticação\n    this.client.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('shack_token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor - trata erros de autenticação\n    this.client.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401) {\n          // Token inválido ou expirado\n          localStorage.removeItem('shack_token');\n          localStorage.removeItem('shack_user');\n          window.location.href = '/login';\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // === MÉTODOS DE AUTENTICAÇÃO ===\n  async login(credentials: { email: string; password: string }): Promise<LoginResponse> {\n    try {\n      const response = await this.client.post('/api/auth/login', credentials);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.mensagem || 'Erro ao fazer login');\n    }\n  }\n\n  async register(userData: { email: string; password: string; nick: string }): Promise<LoginResponse> {\n    try {\n      const response = await this.client.post('/api/auth/register', userData);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.mensagem || 'Erro ao criar conta');\n    }\n  }\n\n  async verifyToken(): Promise<boolean> {\n    try {\n      const response = await this.client.get('/api/auth/check');\n      return response.data.sucesso;\n    } catch (error) {\n      console.log('Erro na verificação de token:', error);\n      return false;\n    }\n  }\n\n  // === MÉTODOS DO JOGO ===\n  async getPlayerData(): Promise<ApiResponse<PlayerData>> {\n    try {\n      const response = await this.client.get('/api/jogador');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.mensagem || 'Erro ao carregar dados do jogador');\n    }\n  }\n\n  async scanTargets(): Promise<ApiResponse<{ alvos: ScanTarget[] }>> {\n    try {\n      const response = await this.client.get('/api/scan');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.mensagem || 'Erro ao escanear alvos');\n    }\n  }\n\n  async exploitTarget(targetIp: string, percentage: number): Promise<ApiResponse> {\n    try {\n      const response = await this.client.post('/api/transferir', {\n        alvo_ip: targetIp,\n        porcentagem: percentage,\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.mensagem || 'Erro ao exploitar alvo');\n    }\n  }\n\n  // === MÉTODOS DE CHAT ===\n  async getChatMessages(): Promise<ApiResponse<{ messages: ChatMessage[] }>> {\n    try {\n      const response = await this.client.get('/api/chat/messages');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.mensagem || 'Erro ao carregar mensagens');\n    }\n  }\n\n  async sendChatMessage(message: string): Promise<ApiResponse> {\n    try {\n      const response = await this.client.post('/api/chat/send', { message });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.mensagem || 'Erro ao enviar mensagem');\n    }\n  }\n\n  // === MÉTODOS DE APPS ===\n  async getAppStore(): Promise<ApiResponse> {\n    try {\n      const response = await this.client.get('/api/appstore');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.mensagem || 'Erro ao carregar loja');\n    }\n  }\n\n  async purchaseApp(appName: string, quantity: number): Promise<ApiResponse> {\n    try {\n      const response = await this.client.post('/api/appstore/comprar', {\n        item: appName,\n        quantidade: quantity,\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.mensagem || 'Erro ao comprar app');\n    }\n  }\n\n  // === MÉTODOS UTILITÁRIOS ===\n  async getCsrfToken(): Promise<string> {\n    try {\n      const response = await this.client.get('/api/csrf-token');\n      return response.data.csrf_token;\n    } catch (error) {\n      return '';\n    }\n  }\n\n  // Método para fazer requisições customizadas\n  async customRequest<T = any>(\n    method: 'GET' | 'POST' | 'PUT' | 'DELETE',\n    endpoint: string,\n    data?: any\n  ): Promise<T> {\n    try {\n      const response = await this.client.request({\n        method,\n        url: endpoint,\n        data,\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.mensagem || 'Erro na requisição');\n    }\n  }\n}\n\n// Instância singleton do serviço de API\nconst apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAyB,OAAO;;AAE5C;;AAmDA,MAAMC,UAAU,CAAC;EAGfC,WAAWA,CAAA,EAAG;IAAA,KAFNC,MAAM;IAGZ,IAAI,CAACA,MAAM,GAAGH,KAAK,CAACI,MAAM,CAAC;MACzBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,EAAE;MAC5CC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEQA,iBAAiBA,CAAA,EAAG;IAC1B;IACA,IAAI,CAACR,MAAM,CAACS,YAAY,CAACC,OAAO,CAACC,GAAG,CACjCC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACjD,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACN,OAAO,CAACU,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACjB,MAAM,CAACS,YAAY,CAACW,QAAQ,CAACT,GAAG,CAClCS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClC;QACAR,YAAY,CAACS,UAAU,CAAC,aAAa,CAAC;QACtCT,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;QACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC;MACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACA,MAAMU,KAAKA,CAACC,WAAgD,EAA0B;IACpF,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAAC6B,IAAI,CAAC,iBAAiB,EAAED,WAAW,CAAC;MACvE,OAAOR,QAAQ,CAACU,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIC,KAAK,CAAC,EAAAF,gBAAA,GAAAd,KAAK,CAACG,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBD,IAAI,cAAAE,qBAAA,uBAApBA,qBAAA,CAAsBE,QAAQ,KAAI,qBAAqB,CAAC;IAC1E;EACF;EAEA,MAAMC,QAAQA,CAACC,QAA2D,EAA0B;IAClG,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAAC6B,IAAI,CAAC,oBAAoB,EAAEO,QAAQ,CAAC;MACvE,OAAOhB,QAAQ,CAACU,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIL,KAAK,CAAC,EAAAI,gBAAA,GAAApB,KAAK,CAACG,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBP,IAAI,cAAAQ,qBAAA,uBAApBA,qBAAA,CAAsBJ,QAAQ,KAAI,qBAAqB,CAAC;IAC1E;EACF;EAEA,MAAMK,WAAWA,CAAA,EAAqB;IACpC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAACwC,GAAG,CAAC,iBAAiB,CAAC;MACzD,OAAOpB,QAAQ,CAACU,IAAI,CAACW,OAAO;IAC9B,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdyB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE1B,KAAK,CAAC;MACnD,OAAO,KAAK;IACd;EACF;;EAEA;EACA,MAAM2B,aAAaA,CAAA,EAAqC;IACtD,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAACwC,GAAG,CAAC,cAAc,CAAC;MACtD,OAAOpB,QAAQ,CAACU,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAA4B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIb,KAAK,CAAC,EAAAY,gBAAA,GAAA5B,KAAK,CAACG,QAAQ,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBZ,QAAQ,KAAI,mCAAmC,CAAC;IACxF;EACF;EAEA,MAAMa,WAAWA,CAAA,EAAkD;IACjE,IAAI;MACF,MAAM3B,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAACwC,GAAG,CAAC,WAAW,CAAC;MACnD,OAAOpB,QAAQ,CAACU,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAA+B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIhB,KAAK,CAAC,EAAAe,gBAAA,GAAA/B,KAAK,CAACG,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlB,IAAI,cAAAmB,qBAAA,uBAApBA,qBAAA,CAAsBf,QAAQ,KAAI,wBAAwB,CAAC;IAC7E;EACF;EAEA,MAAMgB,aAAaA,CAACC,QAAgB,EAAEC,UAAkB,EAAwB;IAC9E,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAAC6B,IAAI,CAAC,iBAAiB,EAAE;QACzDwB,OAAO,EAAEF,QAAQ;QACjBG,WAAW,EAAEF;MACf,CAAC,CAAC;MACF,OAAOhC,QAAQ,CAACU,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAAsC,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIvB,KAAK,CAAC,EAAAsB,gBAAA,GAAAtC,KAAK,CAACG,QAAQ,cAAAmC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzB,IAAI,cAAA0B,qBAAA,uBAApBA,qBAAA,CAAsBtB,QAAQ,KAAI,wBAAwB,CAAC;IAC7E;EACF;;EAEA;EACA,MAAMuB,eAAeA,CAAA,EAAsD;IACzE,IAAI;MACF,MAAMrC,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAACwC,GAAG,CAAC,oBAAoB,CAAC;MAC5D,OAAOpB,QAAQ,CAACU,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAAyC,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAI1B,KAAK,CAAC,EAAAyB,gBAAA,GAAAzC,KAAK,CAACG,QAAQ,cAAAsC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBzB,QAAQ,KAAI,4BAA4B,CAAC;IACjF;EACF;EAEA,MAAM0B,eAAeA,CAACC,OAAe,EAAwB;IAC3D,IAAI;MACF,MAAMzC,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAAC6B,IAAI,CAAC,gBAAgB,EAAE;QAAEgC;MAAQ,CAAC,CAAC;MACtE,OAAOzC,QAAQ,CAACU,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAA6C,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAI9B,KAAK,CAAC,EAAA6B,gBAAA,GAAA7C,KAAK,CAACG,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsB7B,QAAQ,KAAI,yBAAyB,CAAC;IAC9E;EACF;;EAEA;EACA,MAAM8B,WAAWA,CAAA,EAAyB;IACxC,IAAI;MACF,MAAM5C,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAACwC,GAAG,CAAC,eAAe,CAAC;MACvD,OAAOpB,QAAQ,CAACU,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAAgD,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIjC,KAAK,CAAC,EAAAgC,gBAAA,GAAAhD,KAAK,CAACG,QAAQ,cAAA6C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnC,IAAI,cAAAoC,qBAAA,uBAApBA,qBAAA,CAAsBhC,QAAQ,KAAI,uBAAuB,CAAC;IAC5E;EACF;EAEA,MAAMiC,WAAWA,CAACC,OAAe,EAAEC,QAAgB,EAAwB;IACzE,IAAI;MACF,MAAMjD,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAAC6B,IAAI,CAAC,uBAAuB,EAAE;QAC/DyC,IAAI,EAAEF,OAAO;QACbG,UAAU,EAAEF;MACd,CAAC,CAAC;MACF,OAAOjD,QAAQ,CAACU,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAAuD,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIxC,KAAK,CAAC,EAAAuC,gBAAA,GAAAvD,KAAK,CAACG,QAAQ,cAAAoD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1C,IAAI,cAAA2C,qBAAA,uBAApBA,qBAAA,CAAsBvC,QAAQ,KAAI,qBAAqB,CAAC;IAC1E;EACF;;EAEA;EACA,MAAMwC,YAAYA,CAAA,EAAoB;IACpC,IAAI;MACF,MAAMtD,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAACwC,GAAG,CAAC,iBAAiB,CAAC;MACzD,OAAOpB,QAAQ,CAACU,IAAI,CAAC6C,UAAU;IACjC,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACd,OAAO,EAAE;IACX;EACF;;EAEA;EACA,MAAM2D,aAAaA,CACjBC,MAAyC,EACzCC,QAAgB,EAChBhD,IAAU,EACE;IACZ,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAM,IAAI,CAACpB,MAAM,CAACU,OAAO,CAAC;QACzCmE,MAAM;QACNE,GAAG,EAAED,QAAQ;QACbhD;MACF,CAAC,CAAC;MACF,OAAOV,QAAQ,CAACU,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAA+D,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIhD,KAAK,CAAC,EAAA+C,gBAAA,GAAA/D,KAAK,CAACG,QAAQ,cAAA4D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlD,IAAI,cAAAmD,qBAAA,uBAApBA,qBAAA,CAAsB/C,QAAQ,KAAI,oBAAoB,CAAC;IACzE;EACF;AACF;;AAEA;AACA,MAAMgD,UAAU,GAAG,IAAIpF,UAAU,CAAC,CAAC;AACnC,eAAeoF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}