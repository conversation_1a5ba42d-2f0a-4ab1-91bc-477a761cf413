{"ast": null, "code": "// src/usePrefetchQuery.tsx\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nfunction usePrefetchQuery(options, queryClient) {\n  const client = useQueryClient(queryClient);\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchQuery(options);\n  }\n}\nexport { usePrefetchQuery };", "map": {"version": 3, "names": ["useQueryClient", "usePrefetchQuery", "options", "queryClient", "client", "getQueryState", "query<PERSON><PERSON>", "prefetch<PERSON><PERSON>y"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\react-query\\src\\usePrefetchQuery.tsx"], "sourcesContent": ["import { useQueryClient } from './QueryClientProvider'\nimport type { DefaultError, QueryClient, QueryKey } from '@tanstack/query-core'\nimport type { UsePrefetchQueryOptions } from './types'\n\nexport function usePrefetchQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UsePrefetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n) {\n  const client = useQueryClient(queryClient)\n\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchQuery(options)\n  }\n}\n"], "mappings": ";AAAA,SAASA,cAAA,QAAsB;AAIxB,SAASC,iBAMdC,OAAA,EACAC,WAAA,EACA;EACA,MAAMC,MAAA,GAASJ,cAAA,CAAeG,WAAW;EAEzC,IAAI,CAACC,MAAA,CAAOC,aAAA,CAAcH,OAAA,CAAQI,QAAQ,GAAG;IAC3CF,MAAA,CAAOG,aAAA,CAAcL,OAAO;EAC9B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}