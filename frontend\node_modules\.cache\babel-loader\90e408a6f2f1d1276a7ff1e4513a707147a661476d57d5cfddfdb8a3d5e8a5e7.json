{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\common\\\\GameFooter.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { ScanIcon, HomeIcon, ConfigIcon, ShopIcon, NewsIcon } from '../ui/GameIcons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameFooter = ({\n  currentPage = ''\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const handleNavigation = path => {\n    navigate(path);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-900/95 backdrop-blur-sm border-t border-gray-700/50 p-3 flex-shrink-0\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-around items-center max-w-md mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `flex flex-col items-center space-y-1 transition-all duration-200 ${currentPage === 'scanner' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'}`,\n        onClick: () => handleNavigation('/game/scanner'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(ScanIcon, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs font-medium\",\n          children: \"Scan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `flex flex-col items-center space-y-1 transition-all duration-200 ${currentPage === 'shop' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'}`,\n        onClick: () => handleNavigation('/game/shop'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(ShopIcon, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs font-medium\",\n          children: \"Shop\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `flex flex-col items-center space-y-1 transition-all duration-200 ${currentPage === 'home' ? 'text-white scale-110' : 'text-blue-400 hover:text-white hover:scale-105'}`,\n        onClick: () => handleNavigation('/game'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-10 h-10 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 ${currentPage === 'home' ? 'bg-gradient-to-br from-blue-500 to-purple-600 shadow-blue-500/50' : 'bg-gradient-to-br from-blue-600 to-purple-700 hover:from-blue-500 hover:to-purple-600'}`,\n          children: /*#__PURE__*/_jsxDEV(HomeIcon, {\n            size: 24,\n            className: \"text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs font-semibold\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `flex flex-col items-center space-y-1 transition-all duration-200 ${currentPage === 'apps' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'}`,\n        onClick: () => handleNavigation('/game/apps'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(NewsIcon, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs font-medium\",\n          children: \"Apps\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `flex flex-col items-center space-y-1 transition-all duration-200 ${currentPage === 'config' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'}`,\n        onClick: () => handleNavigation('/game/config'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(ConfigIcon, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs font-medium\",\n          children: \"Config\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(GameFooter, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = GameFooter;\nexport default GameFooter;\nvar _c;\n$RefreshReg$(_c, \"GameFooter\");", "map": {"version": 3, "names": ["React", "useNavigate", "ScanIcon", "HomeIcon", "ConfigIcon", "ShopIcon", "NewsIcon", "jsxDEV", "_jsxDEV", "GameFooter", "currentPage", "_s", "navigate", "handleNavigation", "path", "className", "children", "onClick", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/common/GameFooter.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { ScanIcon, ChatIcon, HomeIcon, TerminalIcon, ConfigIcon, ShopIcon, NewsIcon } from '../ui/GameIcons';\n\ninterface GameFooterProps {\n  currentPage?: string;\n}\n\nconst GameFooter: React.FC<GameFooterProps> = ({ currentPage = '' }) => {\n  const navigate = useNavigate();\n\n  const handleNavigation = (path: string) => {\n    navigate(path);\n  };\n\n  return (\n    <div className=\"bg-gray-900/95 backdrop-blur-sm border-t border-gray-700/50 p-3 flex-shrink-0\">\n      <div className=\"flex justify-around items-center max-w-md mx-auto\">\n        <button\n          className={`flex flex-col items-center space-y-1 transition-all duration-200 ${\n            currentPage === 'scanner' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'\n          }`}\n          onClick={() => handleNavigation('/game/scanner')}\n        >\n          <div className=\"w-6 h-6 flex items-center justify-center\">\n            <ScanIcon size={20} />\n          </div>\n          <span className=\"text-xs font-medium\">Scan</span>\n        </button>\n\n        <button\n          className={`flex flex-col items-center space-y-1 transition-all duration-200 ${\n            currentPage === 'shop' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'\n          }`}\n          onClick={() => handleNavigation('/game/shop')}\n        >\n          <div className=\"w-6 h-6 flex items-center justify-center\">\n            <ShopIcon size={20} />\n          </div>\n          <span className=\"text-xs font-medium\">Shop</span>\n        </button>\n\n        {/* Botão Home no centro */}\n        <button\n          className={`flex flex-col items-center space-y-1 transition-all duration-200 ${\n            currentPage === 'home' ? 'text-white scale-110' : 'text-blue-400 hover:text-white hover:scale-105'\n          }`}\n          onClick={() => handleNavigation('/game')}\n        >\n          <div className={`w-10 h-10 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 ${\n            currentPage === 'home'\n              ? 'bg-gradient-to-br from-blue-500 to-purple-600 shadow-blue-500/50'\n              : 'bg-gradient-to-br from-blue-600 to-purple-700 hover:from-blue-500 hover:to-purple-600'\n          }`}>\n            <HomeIcon size={24} className=\"text-white\" />\n          </div>\n          <span className=\"text-xs font-semibold\">Home</span>\n        </button>\n\n        <button\n          className={`flex flex-col items-center space-y-1 transition-all duration-200 ${\n            currentPage === 'apps' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'\n          }`}\n          onClick={() => handleNavigation('/game/apps')}\n        >\n          <div className=\"w-6 h-6 flex items-center justify-center\">\n            <NewsIcon size={20} />\n          </div>\n          <span className=\"text-xs font-medium\">Apps</span>\n        </button>\n\n        <button\n          className={`flex flex-col items-center space-y-1 transition-all duration-200 ${\n            currentPage === 'config' ? 'text-blue-400 scale-110' : 'text-gray-400 hover:text-white hover:scale-105'\n          }`}\n          onClick={() => handleNavigation('/game/config')}\n        >\n          <div className=\"w-6 h-6 flex items-center justify-center\">\n            <ConfigIcon size={20} />\n          </div>\n          <span className=\"text-xs font-medium\">Config</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default GameFooter;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,EAAYC,QAAQ,EAAgBC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7G,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,WAAW,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,gBAAgB,GAAIC,IAAY,IAAK;IACzCF,QAAQ,CAACE,IAAI,CAAC;EAChB,CAAC;EAED,oBACEN,OAAA;IAAKO,SAAS,EAAC,+EAA+E;IAAAC,QAAA,eAC5FR,OAAA;MAAKO,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAChER,OAAA;QACEO,SAAS,EAAE,oEACTL,WAAW,KAAK,SAAS,GAAG,yBAAyB,GAAG,gDAAgD,EACvG;QACHO,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAAC,eAAe,CAAE;QAAAG,QAAA,gBAEjDR,OAAA;UAAKO,SAAS,EAAC,0CAA0C;UAAAC,QAAA,eACvDR,OAAA,CAACN,QAAQ;YAACgB,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNd,OAAA;UAAMO,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAETd,OAAA;QACEO,SAAS,EAAE,oEACTL,WAAW,KAAK,MAAM,GAAG,yBAAyB,GAAG,gDAAgD,EACpG;QACHO,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAAC,YAAY,CAAE;QAAAG,QAAA,gBAE9CR,OAAA;UAAKO,SAAS,EAAC,0CAA0C;UAAAC,QAAA,eACvDR,OAAA,CAACH,QAAQ;YAACa,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNd,OAAA;UAAMO,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAGTd,OAAA;QACEO,SAAS,EAAE,oEACTL,WAAW,KAAK,MAAM,GAAG,sBAAsB,GAAG,gDAAgD,EACjG;QACHO,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAAC,OAAO,CAAE;QAAAG,QAAA,gBAEzCR,OAAA;UAAKO,SAAS,EAAE,iGACdL,WAAW,KAAK,MAAM,GAClB,kEAAkE,GAClE,uFAAuF,EAC1F;UAAAM,QAAA,eACDR,OAAA,CAACL,QAAQ;YAACe,IAAI,EAAE,EAAG;YAACH,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNd,OAAA;UAAMO,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAETd,OAAA;QACEO,SAAS,EAAE,oEACTL,WAAW,KAAK,MAAM,GAAG,yBAAyB,GAAG,gDAAgD,EACpG;QACHO,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAAC,YAAY,CAAE;QAAAG,QAAA,gBAE9CR,OAAA;UAAKO,SAAS,EAAC,0CAA0C;UAAAC,QAAA,eACvDR,OAAA,CAACF,QAAQ;YAACY,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNd,OAAA;UAAMO,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAETd,OAAA;QACEO,SAAS,EAAE,oEACTL,WAAW,KAAK,QAAQ,GAAG,yBAAyB,GAAG,gDAAgD,EACtG;QACHO,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAAC,cAAc,CAAE;QAAAG,QAAA,gBAEhDR,OAAA;UAAKO,SAAS,EAAC,0CAA0C;UAAAC,QAAA,eACvDR,OAAA,CAACJ,UAAU;YAACc,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACNd,OAAA;UAAMO,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CA7EIF,UAAqC;EAAA,QACxBR,WAAW;AAAA;AAAAsB,EAAA,GADxBd,UAAqC;AA+E3C,eAAeA,UAAU;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}