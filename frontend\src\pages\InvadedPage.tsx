import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useGameSystem } from '../hooks/useGameSystem';
import GameFooter from '../components/common/GameFooter';
import { TerminalIcon, BankIcon, TransferIcon, SecurityIcon } from '../components/ui/GameIcons';

const InvadedPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const target = location.state?.target;
  
  const {
    activeConnections,
    activeBruteforces,
    startBruteforce,
    performBankTransfer,
    isTransferring,
    transferError
  } = useGameSystem();

  const [selectedPercentage, setSelectedPercentage] = useState<number | null>(null);
  const [showTransferModal, setShowTransferModal] = useState(false);

  useEffect(() => {
    if (!target) {
      navigate('/game/scanner');
    }
  }, [target, navigate]);

  if (!target) {
    return null;
  }

  const connection = activeConnections.find(conn => conn.targetIp === target.ip);
  const bruteforceStatus = activeBruteforces.get(target.ip);
  const canAccessBank = connection?.bankAccess || false;

  const handleStartBruteforce = async () => {
    if (!connection) return;
    
    try {
      await startBruteforce(connection.id, target.ip);
    } catch (error) {
      console.error('Erro ao iniciar bruteforce:', error);
    }
  };

  const handleBankAccess = () => {
    if (canAccessBank) {
      setShowTransferModal(true);
    } else {
      alert('Execute um ataque BruteForce primeiro para quebrar a segurança bancária.');
    }
  };

  const handleTransfer = async () => {
    if (!selectedPercentage) {
      alert('Selecione uma porcentagem primeiro');
      return;
    }

    try {
      await performBankTransfer(target.ip, selectedPercentage);
      setShowTransferModal(false);
      setSelectedPercentage(null);
      alert('Transferência realizada com sucesso!');
    } catch (error) {
      console.error('Erro na transferência:', error);
    }
  };

  const calculateTransferAmount = (percentage: number) => {
    return Math.floor((target.dinheiro * percentage) / 100);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-gray-700/50 p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => navigate('/game/scanner')}
              className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors"
            >
              <span className="text-lg">←</span>
            </button>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent">
                Sistema Invadido
              </h1>
              <p className="text-xs text-gray-400">Acesso Remoto Ativo</p>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-1 text-green-400 text-xs">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>Conectado</span>
            </div>
          </div>
        </div>
      </div>

      {/* Informações do Alvo */}
      <div className="p-4">
        <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 mb-6">
          <h2 className="text-lg font-semibold mb-4 text-white">Informações do Sistema</h2>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <span className="text-gray-400 text-sm">Usuário:</span>
              <div className="text-white font-semibold">{target.nick}</div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">IP:</span>
              <div className="text-cyan-400 font-mono">{target.ip}</div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Nível:</span>
              <div className="text-blue-400 font-semibold">{target.nivel}</div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Saldo:</span>
              <div className="text-green-400 font-semibold">${target.dinheiro?.toLocaleString() || '0'}</div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="bg-gray-700/50 rounded p-2 text-center">
              <div className="text-gray-400">Firewall</div>
              <div className="text-red-400 font-semibold">{target.firewall || 1}</div>
            </div>
            <div className="bg-gray-700/50 rounded p-2 text-center">
              <div className="text-gray-400">CPU</div>
              <div className="text-blue-400 font-semibold">{target.cpu || 1}</div>
            </div>
            <div className="bg-gray-700/50 rounded p-2 text-center">
              <div className="text-gray-400">RAM</div>
              <div className="text-purple-400 font-semibold">{target.ram || 1}</div>
            </div>
          </div>
        </div>

        {/* Status do Bruteforce */}
        {bruteforceStatus && (
          <div className="bg-gradient-to-br from-yellow-900/50 to-orange-900/50 backdrop-blur-sm rounded-xl p-6 border border-yellow-700/50 mb-6">
            <h3 className="text-lg font-semibold mb-4 text-yellow-400">Ataque BruteForce em Andamento</h3>
            
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Progresso:</span>
                <span className="text-yellow-400">{bruteforceStatus.progress.toFixed(1)}%</span>
              </div>
              
              <div className="w-full bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-yellow-500 to-orange-500 h-3 rounded-full transition-all duration-1000"
                  style={{ width: `${bruteforceStatus.progress}%` }}
                ></div>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Tempo restante:</span>
                <span className="text-yellow-400">{bruteforceStatus.timeRemaining}s</span>
              </div>
            </div>
          </div>
        )}

        {/* Ações Disponíveis */}
        <div className="space-y-4">
          {/* Terminal/Bruteforce */}
          <button
            onClick={handleStartBruteforce}
            disabled={!!bruteforceStatus || canAccessBank}
            className={`w-full p-4 rounded-xl border transition-all ${
              bruteforceStatus
                ? 'bg-yellow-900/20 border-yellow-700/50 text-yellow-400'
                : canAccessBank
                ? 'bg-green-900/20 border-green-700/50 text-green-400'
                : 'bg-gray-800/50 border-gray-600/50 hover:border-cyan-500/50 text-white hover:bg-gray-700/50'
            }`}
          >
            <div className="flex items-center space-x-3">
              <TerminalIcon size={24} className={bruteforceStatus ? 'text-yellow-400' : canAccessBank ? 'text-green-400' : 'text-cyan-400'} />
              <div className="text-left">
                <div className="font-semibold">
                  {bruteforceStatus ? 'BruteForce em Andamento' : canAccessBank ? 'BruteForce Concluído' : 'Iniciar BruteForce'}
                </div>
                <div className="text-sm text-gray-400">
                  {bruteforceStatus ? 'Quebrando segurança bancária...' : canAccessBank ? 'Segurança bancária quebrada' : 'Quebrar segurança do sistema bancário'}
                </div>
              </div>
            </div>
          </button>

          {/* Banco */}
          <button
            onClick={handleBankAccess}
            disabled={!canAccessBank}
            className={`w-full p-4 rounded-xl border transition-all ${
              canAccessBank
                ? 'bg-green-800/50 border-green-600/50 hover:border-green-500/50 text-white hover:bg-green-700/50'
                : 'bg-gray-800/20 border-gray-700/50 text-gray-500 cursor-not-allowed'
            }`}
          >
            <div className="flex items-center space-x-3">
              <BankIcon size={24} className={canAccessBank ? 'text-green-400' : 'text-gray-500'} />
              <div className="text-left">
                <div className="font-semibold">
                  {canAccessBank ? 'Acessar Banco' : 'Banco Bloqueado'}
                </div>
                <div className="text-sm text-gray-400">
                  {canAccessBank ? 'Realizar transferências bancárias' : 'Execute BruteForce primeiro'}
                </div>
              </div>
            </div>
          </button>
        </div>
      </div>

      {/* Modal de Transferência */}
      {showTransferModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-xl p-6 w-full max-w-md border border-gray-700">
            <h3 className="text-lg font-semibold mb-4 text-white">Transferência Bancária</h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-400 mb-2">Saldo disponível: ${target.dinheiro?.toLocaleString()}</p>
              
              <div className="grid grid-cols-4 gap-2">
                {[20, 40, 60, 80].map(percentage => (
                  <button
                    key={percentage}
                    onClick={() => setSelectedPercentage(percentage)}
                    className={`py-2 px-3 rounded-lg text-sm font-semibold transition-all ${
                      selectedPercentage === percentage
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    {percentage}%
                  </button>
                ))}
              </div>
              
              {selectedPercentage && (
                <div className="mt-3 p-3 bg-gray-700/50 rounded-lg">
                  <p className="text-sm text-gray-400">Valor a transferir:</p>
                  <p className="text-lg font-semibold text-green-400">
                    ${calculateTransferAmount(selectedPercentage).toLocaleString()}
                  </p>
                </div>
              )}
            </div>

            {transferError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg">
                <p className="text-red-300 text-sm">{transferError}</p>
              </div>
            )}

            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowTransferModal(false);
                  setSelectedPercentage(null);
                }}
                className="flex-1 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg"
              >
                Cancelar
              </button>
              <button
                onClick={handleTransfer}
                disabled={!selectedPercentage || isTransferring}
                className="flex-1 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg"
              >
                {isTransferring ? 'Transferindo...' : 'Confirmar'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <GameFooter currentPage="scanner" />
    </div>
  );
};

export default InvadedPage;
