{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\HackGamePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useHackGameStore } from '../stores/hackGameStore';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport MobilePhone from '../components/game/MobilePhone';\nimport SimpleLoginPage from './SimpleLoginPage';\nimport GameHomePage from './GameHomePage';\nimport GameAppsPage from './GameAppsPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HackGamePage = () => {\n  _s();\n  const {\n    player,\n    currentScreen,\n    setCurrentScreen,\n    syncWithServer,\n    initializeFromAuth\n  } = useHackGameStore();\n  const {\n    isAuthenticated,\n    user\n  } = useSimpleAuth();\n\n  // Verificar se tem token e tentar carregar jogador\n  useEffect(() => {\n    const token = localStorage.getItem('auth_token');\n    if (token && !player) {\n      syncWithServer();\n    }\n  }, [player, syncWithServer]);\n\n  // Se está autenticado mas não tem player carregado, tentar carregar\n  useEffect(() => {\n    if (isAuthenticated && user && !player) {\n      console.log('Usuário autenticado, carregando dados do jogo...');\n      initializeFromAuth();\n    }\n  }, [isAuthenticated, user, player, initializeFromAuth]);\n\n  // Se não está autenticado, mostrar tela de login\n  if (!isAuthenticated || !user) {\n    return /*#__PURE__*/_jsxDEV(MobilePhone, {\n      children: /*#__PURE__*/_jsxDEV(SimpleLoginPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Se está autenticado mas ainda não carregou o player, mostrar loading\n  if (!player) {\n    return /*#__PURE__*/_jsxDEV(MobilePhone, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400\",\n            children: \"Carregando dados do jogador...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Renderizar tela baseada no currentScreen\n  const renderCurrentScreen = () => {\n    switch (currentScreen) {\n      case 'home':\n        return /*#__PURE__*/_jsxDEV(GameHomePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 16\n        }, this);\n      case 'apps':\n        return /*#__PURE__*/_jsxDEV(GameAppsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 16\n        }, this);\n      case 'scanner':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Encontre alvos na rede para hackear\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this);\n      case 'terminal':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDCBB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Terminal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Interface de hacking avan\\xE7ada\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this);\n      case 'profile':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Perfil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Estat\\xEDsticas e conquistas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this);\n      case 'settings':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Configura\\xE7\\xF5es\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Ajustes do sistema\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this);\n      case 'shop':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDED2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Loja\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Compre itens especiais\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this);\n      case 'ranking':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Ranking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Top hackers do servidor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this);\n      case 'logs':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Logs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Hist\\xF3rico de atividades\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this);\n      case 'chat':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Chat Global\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mb-4\",\n              children: \"Converse com outros hackers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Em desenvolvimento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(GameHomePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(MobilePhone, {\n    children: renderCurrentScreen()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n};\n_s(HackGamePage, \"I8+h8Pxyef15qHJYCv5/UlLP3zg=\", false, function () {\n  return [useHackGameStore, useSimpleAuth];\n});\n_c = HackGamePage;\nexport default HackGamePage;\nvar _c;\n$RefreshReg$(_c, \"HackGamePage\");", "map": {"version": 3, "names": ["React", "useEffect", "useHackGameStore", "useSimpleAuth", "MobilePhone", "SimpleLoginPage", "GameHomePage", "GameAppsPage", "jsxDEV", "_jsxDEV", "HackGamePage", "_s", "player", "currentScreen", "setCurrentScreen", "syncWithServer", "initializeFromAuth", "isAuthenticated", "user", "token", "localStorage", "getItem", "console", "log", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "renderCurrentScreen", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/HackGamePage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useHackGameStore } from '../stores/hackGameStore';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport MobilePhone from '../components/game/MobilePhone';\nimport SimpleLoginPage from './SimpleLoginPage';\nimport GameHomePage from './GameHomePage';\nimport GameAppsPage from './GameAppsPage';\n\nconst HackGamePage: React.FC = () => {\n  const { player, currentScreen, setCurrentScreen, syncWithServer, initializeFromAuth } = useHackGameStore();\n  const { isAuthenticated, user } = useSimpleAuth();\n\n  // Verificar se tem token e tentar carregar jogador\n  useEffect(() => {\n    const token = localStorage.getItem('auth_token');\n    if (token && !player) {\n      syncWithServer();\n    }\n  }, [player, syncWithServer]);\n\n  // Se está autenticado mas não tem player carregado, tentar carregar\n  useEffect(() => {\n    if (isAuthenticated && user && !player) {\n      console.log('Usuário autenticado, carregando dados do jogo...');\n      initializeFromAuth();\n    }\n  }, [isAuthenticated, user, player, initializeFromAuth]);\n\n  // Se não está autenticado, mostrar tela de login\n  if (!isAuthenticated || !user) {\n    return (\n      <MobilePhone>\n        <SimpleLoginPage />\n      </MobilePhone>\n    );\n  }\n\n  // Se está autenticado mas ainda não carregou o player, mostrar loading\n  if (!player) {\n    return (\n      <MobilePhone>\n        <div className=\"flex items-center justify-center h-full\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n            <p className=\"text-gray-400\">Carregando dados do jogador...</p>\n          </div>\n        </div>\n      </MobilePhone>\n    );\n  }\n\n  // Renderizar tela baseada no currentScreen\n  const renderCurrentScreen = () => {\n    switch (currentScreen) {\n      case 'home':\n        return <GameHomePage />;\n      case 'apps':\n        return <GameAppsPage />;\n      case 'scanner':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🔍</div>\n              <p className=\"text-lg font-semibold mb-2\">Scanner</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Encontre alvos na rede para hackear</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'terminal':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">💻</div>\n              <p className=\"text-lg font-semibold mb-2\">Terminal</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Interface de hacking avançada</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'profile':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">👤</div>\n              <p className=\"text-lg font-semibold mb-2\">Perfil</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Estatísticas e conquistas</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'settings':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">⚙️</div>\n              <p className=\"text-lg font-semibold mb-2\">Configurações</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Ajustes do sistema</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'shop':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🛒</div>\n              <p className=\"text-lg font-semibold mb-2\">Loja</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Compre itens especiais</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'ranking':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🏆</div>\n              <p className=\"text-lg font-semibold mb-2\">Ranking</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Top hackers do servidor</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'logs':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">📊</div>\n              <p className=\"text-lg font-semibold mb-2\">Logs</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Histórico de atividades</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      case 'chat':\n        return (\n          <div className=\"h-full flex items-center justify-center text-white\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">💬</div>\n              <p className=\"text-lg font-semibold mb-2\">Chat Global</p>\n              <p className=\"text-sm text-gray-400 mb-4\">Converse com outros hackers</p>\n              <div className=\"text-xs text-gray-500\">Em desenvolvimento...</div>\n            </div>\n          </div>\n        );\n      default:\n        return <GameHomePage />;\n    }\n  };\n\n  return (\n    <MobilePhone>\n      {renderCurrentScreen()}\n    </MobilePhone>\n  );\n};\n\nexport default HackGamePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AAExC,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC,MAAM;IAAEC,aAAa;IAAEC,gBAAgB;IAAEC,cAAc;IAAEC;EAAmB,CAAC,GAAGd,gBAAgB,CAAC,CAAC;EAC1G,MAAM;IAAEe,eAAe;IAAEC;EAAK,CAAC,GAAGf,aAAa,CAAC,CAAC;;EAEjD;EACAF,SAAS,CAAC,MAAM;IACd,MAAMkB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,IAAIF,KAAK,IAAI,CAACP,MAAM,EAAE;MACpBG,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACH,MAAM,EAAEG,cAAc,CAAC,CAAC;;EAE5B;EACAd,SAAS,CAAC,MAAM;IACd,IAAIgB,eAAe,IAAIC,IAAI,IAAI,CAACN,MAAM,EAAE;MACtCU,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/DP,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACC,eAAe,EAAEC,IAAI,EAAEN,MAAM,EAAEI,kBAAkB,CAAC,CAAC;;EAEvD;EACA,IAAI,CAACC,eAAe,IAAI,CAACC,IAAI,EAAE;IAC7B,oBACET,OAAA,CAACL,WAAW;MAAAoB,QAAA,eACVf,OAAA,CAACJ,eAAe;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAElB;;EAEA;EACA,IAAI,CAAChB,MAAM,EAAE;IACX,oBACEH,OAAA,CAACL,WAAW;MAAAoB,QAAA,eACVf,OAAA;QAAKoB,SAAS,EAAC,yCAAyC;QAAAL,QAAA,eACtDf,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAL,QAAA,gBAC1Bf,OAAA;YAAKoB,SAAS,EAAC;UAA2E;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjGnB,OAAA;YAAGoB,SAAS,EAAC,eAAe;YAAAL,QAAA,EAAC;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAElB;;EAEA;EACA,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC,QAAQjB,aAAa;MACnB,KAAK,MAAM;QACT,oBAAOJ,OAAA,CAACH,YAAY;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,MAAM;QACT,oBAAOnB,OAAA,CAACF,YAAY;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,SAAS;QACZ,oBACEnB,OAAA;UAAKoB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjEf,OAAA;YAAKoB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1Bf,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrDnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjFnB,OAAA;cAAKoB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,UAAU;QACb,oBACEnB,OAAA;UAAKoB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjEf,OAAA;YAAKoB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1Bf,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtDnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3EnB,OAAA;cAAKoB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,SAAS;QACZ,oBACEnB,OAAA;UAAKoB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjEf,OAAA;YAAKoB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1Bf,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpDnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvEnB,OAAA;cAAKoB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,UAAU;QACb,oBACEnB,OAAA;UAAKoB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjEf,OAAA;YAAKoB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1Bf,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3DnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChEnB,OAAA;cAAKoB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,MAAM;QACT,oBACEnB,OAAA;UAAKoB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjEf,OAAA;YAAKoB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1Bf,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClDnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEnB,OAAA;cAAKoB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,SAAS;QACZ,oBACEnB,OAAA;UAAKoB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjEf,OAAA;YAAKoB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1Bf,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrDnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrEnB,OAAA;cAAKoB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,MAAM;QACT,oBACEnB,OAAA;UAAKoB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjEf,OAAA;YAAKoB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1Bf,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClDnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrEnB,OAAA;cAAKoB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,MAAM;QACT,oBACEnB,OAAA;UAAKoB,SAAS,EAAC,oDAAoD;UAAAL,QAAA,eACjEf,OAAA;YAAKoB,SAAS,EAAC,aAAa;YAAAL,QAAA,gBAC1Bf,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAL,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzDnB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAL,QAAA,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzEnB,OAAA;cAAKoB,SAAS,EAAC,uBAAuB;cAAAL,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;QACE,oBAAOnB,OAAA,CAACH,YAAY;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC3B;EACF,CAAC;EAED,oBACEnB,OAAA,CAACL,WAAW;IAAAoB,QAAA,EACTM,mBAAmB,CAAC;EAAC;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAElB,CAAC;AAACjB,EAAA,CApJID,YAAsB;EAAA,QAC8DR,gBAAgB,EACtEC,aAAa;AAAA;AAAA4B,EAAA,GAF3CrB,YAAsB;AAsJ5B,eAAeA,YAAY;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}