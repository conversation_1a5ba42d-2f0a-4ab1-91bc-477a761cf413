{"ast": null, "code": "var _s = $RefreshSig$();\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport mockApiService from '../services/mockApi';\nexport const useSimpleAuthStore = create()(persist((set, get) => ({\n  // Estado inicial\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n  // Login com API mock\n  login: async credentials => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      console.log('SimpleAuth - Fazendo login...');\n      const response = await mockApiService.login(credentials);\n      if (response.sucesso && response.user && response.token) {\n        set({\n          user: response.user,\n          token: response.token,\n          isAuthenticated: true,\n          isLoading: false,\n          error: null\n        });\n        console.log('SimpleAuth - Login realizado com sucesso!');\n      } else {\n        set({\n          isLoading: false,\n          error: response.mensagem || 'Erro no login'\n        });\n      }\n    } catch (error) {\n      console.error('SimpleAuth - Erro no login:', error);\n      set({\n        isLoading: false,\n        error: '<PERSON>rro de conexão'\n      });\n    }\n  },\n  // Registro com API mock\n  register: async userData => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      console.log('SimpleAuth - Criando conta...');\n      const response = await mockApiService.register(userData);\n      if (response.sucesso && response.user && response.token) {\n        set({\n          user: response.user,\n          token: response.token,\n          isAuthenticated: true,\n          isLoading: false,\n          error: null\n        });\n        console.log('SimpleAuth - Conta criada com sucesso!');\n      } else {\n        set({\n          isLoading: false,\n          error: response.mensagem || 'Erro no registro'\n        });\n      }\n    } catch (error) {\n      console.error('SimpleAuth - Erro no registro:', error);\n      set({\n        isLoading: false,\n        error: 'Erro de conexão'\n      });\n    }\n  },\n  // Logout\n  logout: () => {\n    console.log('SimpleAuth - Fazendo logout');\n    set({\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null\n    });\n  },\n  // Limpar erro\n  clearError: () => {\n    set({\n      error: null\n    });\n  },\n  // Simulação de login para teste\n  simulateLogin: () => {\n    console.log('SimpleAuth - Simulando login...');\n    set({\n      isLoading: true\n    });\n\n    // Simular delay de API\n    setTimeout(() => {\n      const mockUser = {\n        uid: 'test-user-123',\n        nick: 'TestPlayer',\n        email: '<EMAIL>'\n      };\n      const mockToken = 'mock-jwt-token-123';\n      set({\n        user: mockUser,\n        token: mockToken,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      });\n      console.log('SimpleAuth - Login simulado com sucesso!');\n    }, 1000);\n  }\n}), {\n  name: 'simple-auth-storage',\n  partialize: state => ({\n    user: state.user,\n    token: state.token,\n    isAuthenticated: state.isAuthenticated\n  })\n}));\n\n// Hook simplificado\nexport const useSimpleAuth = () => {\n  _s();\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    setUser,\n    setToken,\n    logout,\n    clearError,\n    simulateLogin\n  } = useSimpleAuthStore();\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    // Ações\n    setUser,\n    setToken,\n    logout,\n    clearError,\n    simulateLogin,\n    // Computed\n    isLoggedIn: isAuthenticated && !!user\n  };\n};\n_s(useSimpleAuth, \"ITTFgxX7pD230Yg5gwD9uznIvDU=\", false, function () {\n  return [useSimpleAuthStore];\n});", "map": {"version": 3, "names": ["create", "persist", "mockApiService", "useSimpleAuthStore", "set", "get", "user", "token", "isAuthenticated", "isLoading", "error", "login", "credentials", "console", "log", "response", "sucesso", "mensagem", "register", "userData", "logout", "clearError", "simulateLogin", "setTimeout", "mockUser", "uid", "nick", "email", "mockToken", "name", "partialize", "state", "useSimpleAuth", "_s", "setUser", "setToken", "isLoggedIn"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/simpleAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport mockApiService from '../services/mockApi';\n\nexport interface User {\n  uid: string;\n  nick: string;\n  email: string;\n}\n\ninterface SimpleAuthState {\n  // Estado\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n\n  // Ações com API mock\n  login: (credentials: { email: string; password: string }) => Promise<void>;\n  register: (userData: { email: string; password: string; nick: string }) => Promise<void>;\n  logout: () => void;\n  clearError: () => void;\n  checkAuth: () => Promise<void>;\n\n  // Simulação de login para teste\n  simulateLogin: () => void;\n}\n\nexport const useSimpleAuthStore = create<SimpleAuthState>()(\n  persist(\n    (set, get) => ({\n      // Estado inicial\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // Login com API mock\n      login: async (credentials) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          console.log('SimpleAuth - Fazendo login...');\n          const response = await mockApiService.login(credentials);\n\n          if (response.sucesso && response.user && response.token) {\n            set({\n              user: response.user,\n              token: response.token,\n              isAuthenticated: true,\n              isLoading: false,\n              error: null,\n            });\n            console.log('SimpleAuth - Login realizado com sucesso!');\n          } else {\n            set({\n              isLoading: false,\n              error: response.mensagem || 'Erro no login',\n            });\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro no login:', error);\n          set({\n            isLoading: false,\n            error: 'Erro de conexão',\n          });\n        }\n      },\n\n      // Registro com API mock\n      register: async (userData) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          console.log('SimpleAuth - Criando conta...');\n          const response = await mockApiService.register(userData);\n\n          if (response.sucesso && response.user && response.token) {\n            set({\n              user: response.user,\n              token: response.token,\n              isAuthenticated: true,\n              isLoading: false,\n              error: null,\n            });\n            console.log('SimpleAuth - Conta criada com sucesso!');\n          } else {\n            set({\n              isLoading: false,\n              error: response.mensagem || 'Erro no registro',\n            });\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro no registro:', error);\n          set({\n            isLoading: false,\n            error: 'Erro de conexão',\n          });\n        }\n      },\n\n      // Logout\n      logout: () => {\n        console.log('SimpleAuth - Fazendo logout');\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: null,\n        });\n      },\n\n      // Limpar erro\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // Simulação de login para teste\n      simulateLogin: () => {\n        console.log('SimpleAuth - Simulando login...');\n        set({ isLoading: true });\n        \n        // Simular delay de API\n        setTimeout(() => {\n          const mockUser: User = {\n            uid: 'test-user-123',\n            nick: 'TestPlayer',\n            email: '<EMAIL>'\n          };\n          \n          const mockToken = 'mock-jwt-token-123';\n          \n          set({\n            user: mockUser,\n            token: mockToken,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n          \n          console.log('SimpleAuth - Login simulado com sucesso!');\n        }, 1000);\n      },\n    }),\n    {\n      name: 'simple-auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// Hook simplificado\nexport const useSimpleAuth = () => {\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    setUser,\n    setToken,\n    logout,\n    clearError,\n    simulateLogin,\n  } = useSimpleAuthStore();\n\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    \n    // Ações\n    setUser,\n    setToken,\n    logout,\n    clearError,\n    simulateLogin,\n    \n    // Computed\n    isLoggedIn: isAuthenticated && !!user,\n  };\n};\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,cAAc,MAAM,qBAAqB;AA2BhD,OAAO,MAAMC,kBAAkB,GAAGH,MAAM,CAAkB,CAAC,CACzDC,OAAO,CACL,CAACG,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEX;EACAC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5BR,GAAG,CAAC;MAAEK,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACFG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,MAAMC,QAAQ,GAAG,MAAMb,cAAc,CAACS,KAAK,CAACC,WAAW,CAAC;MAExD,IAAIG,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACT,IAAI,IAAIS,QAAQ,CAACR,KAAK,EAAE;QACvDH,GAAG,CAAC;UACFE,IAAI,EAAES,QAAQ,CAACT,IAAI;UACnBC,KAAK,EAAEQ,QAAQ,CAACR,KAAK;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;QACFG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAC1D,CAAC,MAAM;QACLV,GAAG,CAAC;UACFK,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAEK,QAAQ,CAACE,QAAQ,IAAI;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDN,GAAG,CAAC;QACFK,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAQ,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5Bf,GAAG,CAAC;MAAEK,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACFG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,MAAMC,QAAQ,GAAG,MAAMb,cAAc,CAACgB,QAAQ,CAACC,QAAQ,CAAC;MAExD,IAAIJ,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACT,IAAI,IAAIS,QAAQ,CAACR,KAAK,EAAE;QACvDH,GAAG,CAAC;UACFE,IAAI,EAAES,QAAQ,CAACT,IAAI;UACnBC,KAAK,EAAEQ,QAAQ,CAACR,KAAK;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;QACFG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD,CAAC,MAAM;QACLV,GAAG,CAAC;UACFK,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAEK,QAAQ,CAACE,QAAQ,IAAI;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDN,GAAG,CAAC;QACFK,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAU,MAAM,EAAEA,CAAA,KAAM;IACZP,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1CV,GAAG,CAAC;MACFE,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE,KAAK;MACtBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED;EACAW,UAAU,EAAEA,CAAA,KAAM;IAChBjB,GAAG,CAAC;MAAEM,KAAK,EAAE;IAAK,CAAC,CAAC;EACtB,CAAC;EAED;EACAY,aAAa,EAAEA,CAAA,KAAM;IACnBT,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CV,GAAG,CAAC;MAAEK,SAAS,EAAE;IAAK,CAAC,CAAC;;IAExB;IACAc,UAAU,CAAC,MAAM;MACf,MAAMC,QAAc,GAAG;QACrBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE;MACT,CAAC;MAED,MAAMC,SAAS,GAAG,oBAAoB;MAEtCxB,GAAG,CAAC;QACFE,IAAI,EAAEkB,QAAQ;QACdjB,KAAK,EAAEqB,SAAS;QAChBpB,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;MAEFG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC,CAAC,EACF;EACEe,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAGC,KAAK,KAAM;IACtBzB,IAAI,EAAEyB,KAAK,CAACzB,IAAI;IAChBC,KAAK,EAAEwB,KAAK,CAACxB,KAAK;IAClBC,eAAe,EAAEuB,KAAK,CAACvB;EACzB,CAAC;AACH,CACF,CACF,CAAC;;AAED;AACA,OAAO,MAAMwB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IACJ3B,IAAI;IACJC,KAAK;IACLC,eAAe;IACfC,SAAS;IACTC,KAAK;IACLwB,OAAO;IACPC,QAAQ;IACRf,MAAM;IACNC,UAAU;IACVC;EACF,CAAC,GAAGnB,kBAAkB,CAAC,CAAC;EAExB,OAAO;IACL;IACAG,IAAI;IACJC,KAAK;IACLC,eAAe;IACfC,SAAS;IACTC,KAAK;IAEL;IACAwB,OAAO;IACPC,QAAQ;IACRf,MAAM;IACNC,UAAU;IACVC,aAAa;IAEb;IACAc,UAAU,EAAE5B,eAAe,IAAI,CAAC,CAACF;EACnC,CAAC;AACH,CAAC;AAAC2B,EAAA,CAhCWD,aAAa;EAAA,QAYpB7B,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}