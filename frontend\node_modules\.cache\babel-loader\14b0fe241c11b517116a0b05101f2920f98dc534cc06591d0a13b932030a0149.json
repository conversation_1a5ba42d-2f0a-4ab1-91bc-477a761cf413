{"ast": null, "code": "import{create}from'zustand';import{persist}from'zustand/middleware';import mockApiService from'../services/mockApi';export const useSimpleAuthStore=create()(persist((set,get)=>({// Estado inicial\nuser:null,token:null,isAuthenticated:false,isLoading:false,error:null,// Login com API mock\nlogin:async credentials=>{set({isLoading:true,error:null});try{console.log('SimpleAuth - Fazendo login...');const response=await mockApiService.login(credentials);if(response.sucesso&&response.user&&response.token){set({user:response.user,token:response.token,isAuthenticated:true,isLoading:false,error:null});console.log('SimpleAuth - Login realizado com sucesso!');}else{set({isLoading:false,error:response.mensagem||'Erro no login'});}}catch(error){console.error('SimpleAuth - Erro no login:',error);set({isLoading:false,error:'Erro de conexão'});}},// Registro com API mock\nregister:async userData=>{set({isLoading:true,error:null});try{console.log('SimpleAuth - Criando conta...');const response=await mockApiService.register(userData);if(response.sucesso&&response.user&&response.token){set({user:response.user,token:response.token,isAuthenticated:true,isLoading:false,error:null});console.log('SimpleAuth - Conta criada com sucesso!');}else{set({isLoading:false,error:response.mensagem||'Erro no registro'});}}catch(error){console.error('SimpleAuth - Erro no registro:',error);set({isLoading:false,error:'Erro de conexão'});}},// Logout\nlogout:()=>{console.log('SimpleAuth - Fazendo logout');set({user:null,token:null,isAuthenticated:false,isLoading:false,error:null});},// Verificar autenticação\ncheckAuth:async()=>{const{token,isLoading}=get();if(isLoading){console.log('SimpleAuth - Verificação já em andamento');return;}if(!token){console.log('SimpleAuth - Sem token');set({isAuthenticated:false,isLoading:false});return;}console.log('SimpleAuth - Verificando token...');set({isLoading:true});try{const isValid=await mockApiService.verifyToken();if(isValid){console.log('SimpleAuth - Token válido');set({isAuthenticated:true,isLoading:false});}else{console.log('SimpleAuth - Token inválido');get().logout();}}catch(error){console.error('SimpleAuth - Erro na verificação:',error);get().logout();}},// Limpar erro\nclearError:()=>{set({error:null});},// Simulação de login para teste\nsimulateLogin:()=>{console.log('SimpleAuth - Simulando login...');set({isLoading:true});// Simular delay de API\nsetTimeout(()=>{const mockUser={uid:'test-user-123',nick:'TestPlayer',email:'<EMAIL>'};const mockToken='mock-jwt-token-123';set({user:mockUser,token:mockToken,isAuthenticated:true,isLoading:false,error:null});console.log('SimpleAuth - Login simulado com sucesso!');},1000);}}),{name:'simple-auth-storage',partialize:state=>({user:state.user,token:state.token,isAuthenticated:state.isAuthenticated})}));// Hook simplificado\nexport const useSimpleAuth=()=>{const{user,token,isAuthenticated,isLoading,error,login,register,logout,clearError,checkAuth,simulateLogin}=useSimpleAuthStore();return{// Estado\nuser,token,isAuthenticated,isLoading,error,// Ações\nlogin,register,logout,clearError,checkAuth,simulateLogin,// Computed\nisLoggedIn:isAuthenticated&&!!user};};", "map": {"version": 3, "names": ["create", "persist", "mockApiService", "useSimpleAuthStore", "set", "get", "user", "token", "isAuthenticated", "isLoading", "error", "login", "credentials", "console", "log", "response", "sucesso", "mensagem", "register", "userData", "logout", "checkAuth", "<PERSON><PERSON><PERSON><PERSON>", "verifyToken", "clearError", "simulateLogin", "setTimeout", "mockUser", "uid", "nick", "email", "mockToken", "name", "partialize", "state", "useSimpleAuth", "isLoggedIn"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/simpleAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport mockApiService from '../services/mockApi';\n\nexport interface User {\n  uid: string;\n  nick: string;\n  email: string;\n}\n\ninterface SimpleAuthState {\n  // Estado\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n\n  // Ações com API mock\n  login: (credentials: { email: string; password: string }) => Promise<void>;\n  register: (userData: { email: string; password: string; nick: string }) => Promise<void>;\n  logout: () => void;\n  clearError: () => void;\n  checkAuth: () => Promise<void>;\n\n  // Simulação de login para teste\n  simulateLogin: () => void;\n}\n\nexport const useSimpleAuthStore = create<SimpleAuthState>()(\n  persist(\n    (set, get) => ({\n      // Estado inicial\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // Login com API mock\n      login: async (credentials) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          console.log('SimpleAuth - Fazendo login...');\n          const response = await mockApiService.login(credentials);\n\n          if (response.sucesso && response.user && response.token) {\n            set({\n              user: response.user,\n              token: response.token,\n              isAuthenticated: true,\n              isLoading: false,\n              error: null,\n            });\n            console.log('SimpleAuth - Login realizado com sucesso!');\n          } else {\n            set({\n              isLoading: false,\n              error: response.mensagem || 'Erro no login',\n            });\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro no login:', error);\n          set({\n            isLoading: false,\n            error: 'Erro de conexão',\n          });\n        }\n      },\n\n      // Registro com API mock\n      register: async (userData) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          console.log('SimpleAuth - Criando conta...');\n          const response = await mockApiService.register(userData);\n\n          if (response.sucesso && response.user && response.token) {\n            set({\n              user: response.user,\n              token: response.token,\n              isAuthenticated: true,\n              isLoading: false,\n              error: null,\n            });\n            console.log('SimpleAuth - Conta criada com sucesso!');\n          } else {\n            set({\n              isLoading: false,\n              error: response.mensagem || 'Erro no registro',\n            });\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro no registro:', error);\n          set({\n            isLoading: false,\n            error: 'Erro de conexão',\n          });\n        }\n      },\n\n      // Logout\n      logout: () => {\n        console.log('SimpleAuth - Fazendo logout');\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: null,\n        });\n      },\n\n      // Verificar autenticação\n      checkAuth: async () => {\n        const { token, isLoading } = get();\n\n        if (isLoading) {\n          console.log('SimpleAuth - Verificação já em andamento');\n          return;\n        }\n\n        if (!token) {\n          console.log('SimpleAuth - Sem token');\n          set({ isAuthenticated: false, isLoading: false });\n          return;\n        }\n\n        console.log('SimpleAuth - Verificando token...');\n        set({ isLoading: true });\n\n        try {\n          const isValid = await mockApiService.verifyToken();\n\n          if (isValid) {\n            console.log('SimpleAuth - Token válido');\n            set({ isAuthenticated: true, isLoading: false });\n          } else {\n            console.log('SimpleAuth - Token inválido');\n            get().logout();\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro na verificação:', error);\n          get().logout();\n        }\n      },\n\n      // Limpar erro\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // Simulação de login para teste\n      simulateLogin: () => {\n        console.log('SimpleAuth - Simulando login...');\n        set({ isLoading: true });\n        \n        // Simular delay de API\n        setTimeout(() => {\n          const mockUser: User = {\n            uid: 'test-user-123',\n            nick: 'TestPlayer',\n            email: '<EMAIL>'\n          };\n          \n          const mockToken = 'mock-jwt-token-123';\n          \n          set({\n            user: mockUser,\n            token: mockToken,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n          \n          console.log('SimpleAuth - Login simulado com sucesso!');\n        }, 1000);\n      },\n    }),\n    {\n      name: 'simple-auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// Hook simplificado\nexport const useSimpleAuth = () => {\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    register,\n    logout,\n    clearError,\n    checkAuth,\n    simulateLogin,\n  } = useSimpleAuthStore();\n\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n\n    // Ações\n    login,\n    register,\n    logout,\n    clearError,\n    checkAuth,\n    simulateLogin,\n\n    // Computed\n    isLoggedIn: isAuthenticated && !!user,\n  };\n};\n"], "mappings": "AAAA,OAASA,MAAM,KAAQ,SAAS,CAChC,OAASC,OAAO,KAAQ,oBAAoB,CAC5C,MAAO,CAAAC,cAAc,KAAM,qBAAqB,CA2BhD,MAAO,MAAM,CAAAC,kBAAkB,CAAGH,MAAM,CAAkB,CAAC,CACzDC,OAAO,CACL,CAACG,GAAG,CAAEC,GAAG,IAAM,CACb;AACAC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IAAI,CAEX;AACAC,KAAK,CAAE,KAAO,CAAAC,WAAW,EAAK,CAC5BR,GAAG,CAAC,CAAEK,SAAS,CAAE,IAAI,CAAEC,KAAK,CAAE,IAAK,CAAC,CAAC,CAErC,GAAI,CACFG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC,CAC5C,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAb,cAAc,CAACS,KAAK,CAACC,WAAW,CAAC,CAExD,GAAIG,QAAQ,CAACC,OAAO,EAAID,QAAQ,CAACT,IAAI,EAAIS,QAAQ,CAACR,KAAK,CAAE,CACvDH,GAAG,CAAC,CACFE,IAAI,CAAES,QAAQ,CAACT,IAAI,CACnBC,KAAK,CAAEQ,QAAQ,CAACR,KAAK,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IACT,CAAC,CAAC,CACFG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC,CAC1D,CAAC,IAAM,CACLV,GAAG,CAAC,CACFK,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAEK,QAAQ,CAACE,QAAQ,EAAI,eAC9B,CAAC,CAAC,CACJ,CACF,CAAE,MAAOP,KAAK,CAAE,CACdG,OAAO,CAACH,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDN,GAAG,CAAC,CACFK,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,iBACT,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACAQ,QAAQ,CAAE,KAAO,CAAAC,QAAQ,EAAK,CAC5Bf,GAAG,CAAC,CAAEK,SAAS,CAAE,IAAI,CAAEC,KAAK,CAAE,IAAK,CAAC,CAAC,CAErC,GAAI,CACFG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC,CAC5C,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAb,cAAc,CAACgB,QAAQ,CAACC,QAAQ,CAAC,CAExD,GAAIJ,QAAQ,CAACC,OAAO,EAAID,QAAQ,CAACT,IAAI,EAAIS,QAAQ,CAACR,KAAK,CAAE,CACvDH,GAAG,CAAC,CACFE,IAAI,CAAES,QAAQ,CAACT,IAAI,CACnBC,KAAK,CAAEQ,QAAQ,CAACR,KAAK,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IACT,CAAC,CAAC,CACFG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CACvD,CAAC,IAAM,CACLV,GAAG,CAAC,CACFK,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAEK,QAAQ,CAACE,QAAQ,EAAI,kBAC9B,CAAC,CAAC,CACJ,CACF,CAAE,MAAOP,KAAK,CAAE,CACdG,OAAO,CAACH,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtDN,GAAG,CAAC,CACFK,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,iBACT,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACAU,MAAM,CAAEA,CAAA,GAAM,CACZP,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAC1CV,GAAG,CAAC,CACFE,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IACT,CAAC,CAAC,CACJ,CAAC,CAED;AACAW,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,KAAM,CAAEd,KAAK,CAAEE,SAAU,CAAC,CAAGJ,GAAG,CAAC,CAAC,CAElC,GAAII,SAAS,CAAE,CACbI,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACvD,OACF,CAEA,GAAI,CAACP,KAAK,CAAE,CACVM,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CACrCV,GAAG,CAAC,CAAEI,eAAe,CAAE,KAAK,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CACjD,OACF,CAEAI,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAChDV,GAAG,CAAC,CAAEK,SAAS,CAAE,IAAK,CAAC,CAAC,CAExB,GAAI,CACF,KAAM,CAAAa,OAAO,CAAG,KAAM,CAAApB,cAAc,CAACqB,WAAW,CAAC,CAAC,CAElD,GAAID,OAAO,CAAE,CACXT,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CACxCV,GAAG,CAAC,CAAEI,eAAe,CAAE,IAAI,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAClD,CAAC,IAAM,CACLI,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAC1CT,GAAG,CAAC,CAAC,CAACe,MAAM,CAAC,CAAC,CAChB,CACF,CAAE,MAAOV,KAAK,CAAE,CACdG,OAAO,CAACH,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzDL,GAAG,CAAC,CAAC,CAACe,MAAM,CAAC,CAAC,CAChB,CACF,CAAC,CAED;AACAI,UAAU,CAAEA,CAAA,GAAM,CAChBpB,GAAG,CAAC,CAAEM,KAAK,CAAE,IAAK,CAAC,CAAC,CACtB,CAAC,CAED;AACAe,aAAa,CAAEA,CAAA,GAAM,CACnBZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAC9CV,GAAG,CAAC,CAAEK,SAAS,CAAE,IAAK,CAAC,CAAC,CAExB;AACAiB,UAAU,CAAC,IAAM,CACf,KAAM,CAAAC,QAAc,CAAG,CACrBC,GAAG,CAAE,eAAe,CACpBC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,kBACT,CAAC,CAED,KAAM,CAAAC,SAAS,CAAG,oBAAoB,CAEtC3B,GAAG,CAAC,CACFE,IAAI,CAAEqB,QAAQ,CACdpB,KAAK,CAAEwB,SAAS,CAChBvB,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IACT,CAAC,CAAC,CAEFG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACzD,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CAAC,CAAC,CACF,CACEkB,IAAI,CAAE,qBAAqB,CAC3BC,UAAU,CAAGC,KAAK,GAAM,CACtB5B,IAAI,CAAE4B,KAAK,CAAC5B,IAAI,CAChBC,KAAK,CAAE2B,KAAK,CAAC3B,KAAK,CAClBC,eAAe,CAAE0B,KAAK,CAAC1B,eACzB,CAAC,CACH,CACF,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAA2B,aAAa,CAAGA,CAAA,GAAM,CACjC,KAAM,CACJ7B,IAAI,CACJC,KAAK,CACLC,eAAe,CACfC,SAAS,CACTC,KAAK,CACLC,KAAK,CACLO,QAAQ,CACRE,MAAM,CACNI,UAAU,CACVH,SAAS,CACTI,aACF,CAAC,CAAGtB,kBAAkB,CAAC,CAAC,CAExB,MAAO,CACL;AACAG,IAAI,CACJC,KAAK,CACLC,eAAe,CACfC,SAAS,CACTC,KAAK,CAEL;AACAC,KAAK,CACLO,QAAQ,CACRE,MAAM,CACNI,UAAU,CACVH,SAAS,CACTI,aAAa,CAEb;AACAW,UAAU,CAAE5B,eAAe,EAAI,CAAC,CAACF,IACnC,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}