-- Tabela para mensagens do chat
CREATE TABLE IF NOT EXISTS chat_messages (
    id BIGSERIAL PRIMARY KEY,
    uid TEXT NOT NULL,
    nick TEXT NOT NULL,
    message TEXT NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_chat_messages_timestamp ON chat_messages(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_chat_messages_uid ON chat_messages(uid);

-- Adicionar campo password_hash na tabela usuarios se não existir
ALTER TABLE usuarios ADD COLUMN IF NOT EXISTS password_hash TEXT;

-- Coment<PERSON><PERSON>s
COMMENT ON TABLE chat_messages IS 'Mensagens do chat global do jogo';
COMMENT ON COLUMN chat_messages.uid IS 'UID do usuário que enviou a mensagem';
COMMENT ON COLUMN chat_messages.nick IS 'Nickname do usuário';
COMMENT ON COLUMN chat_messages.message IS 'Conteúdo da mensagem';
COMMENT ON COLUMN chat_messages.timestamp IS 'Timestamp da mensagem';

-- Inserir algumas mensagens de exemplo
INSERT INTO chat_messages (uid, nick, message, timestamp) VALUES
('system', 'Sistema', 'Bem-vindos ao SHACK Web Game!', NOW() - INTERVAL '1 hour'),
('system', 'Sistema', 'Chat migrado para React com sucesso!', NOW() - INTERVAL '30 minutes'),
('system', 'Sistema', 'Divirtam-se hackeando!', NOW() - INTERVAL '15 minutes')
ON CONFLICT DO NOTHING;
