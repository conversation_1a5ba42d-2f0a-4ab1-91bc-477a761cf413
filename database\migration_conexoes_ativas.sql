-- Migração para criar tabela conexoes_ativas
-- Necessária para sistema de expiração de conexões

-- Criação da tabela conexoes_ativas
CREATE TABLE IF NOT EXISTS conexoes_ativas (
    id BIGSERIAL PRIMARY KEY,
    atacante_uid TEXT NOT NULL,  -- UUID do atacante
    alvo_ip TEXT NOT NULL,       -- IP do alvo
    alvo_nick TEXT,              -- Nick do alvo
    status TEXT DEFAULT 'ativa', -- Status da conexão (ativa, expirada)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Índices para melhor performance
    UNIQUE(atacante_uid, alvo_ip)
);

-- Criação de índices
CREATE INDEX IF NOT EXISTS idx_conexoes_ativas_atacante ON conexoes_ativas(atacante_uid);
CREATE INDEX IF NOT EXISTS idx_conexoes_ativas_status ON conexoes_ativas(status);
CREATE INDEX IF NOT EXISTS idx_conexoes_ativas_expires ON conexoes_ativas(expires_at);

-- Comentários para documentação
COMMENT ON TABLE conexoes_ativas IS 'Tabela para gerenciar conexões ativas no sistema de exploits';
COMMENT ON COLUMN conexoes_ativas.atacante_uid IS 'UUID do usuário que criou a conexão';
COMMENT ON COLUMN conexoes_ativas.alvo_ip IS 'Endereço IP do alvo da conexão';
COMMENT ON COLUMN conexoes_ativas.alvo_nick IS 'Nome do usuário alvo';
COMMENT ON COLUMN conexoes_ativas.status IS 'Status da conexão: ativa, expirada';
COMMENT ON COLUMN conexoes_ativas.expires_at IS 'Data/hora de expiração da conexão';

-- Para compatibilidade, também precisamos da tabela bruteforce_ataques
CREATE TABLE IF NOT EXISTS bruteforce_ataques (
    id BIGSERIAL PRIMARY KEY,
    usuario_id TEXT NOT NULL,    -- UUID do usuário que executou
    alvo_ip TEXT NOT NULL,       -- IP do alvo
    alvo_nick TEXT,              -- Nick do alvo
    status TEXT DEFAULT 'iniciado', -- Status: iniciado, processando, completado, expirado
    alvo_dados JSONB,            -- Dados do banco do alvo (quando completado)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Índices para melhor performance
    UNIQUE(usuario_id, alvo_ip)
);

-- Criação de índices para bruteforce_ataques
CREATE INDEX IF NOT EXISTS idx_bruteforce_usuario ON bruteforce_ataques(usuario_id);
CREATE INDEX IF NOT EXISTS idx_bruteforce_status ON bruteforce_ataques(status);
CREATE INDEX IF NOT EXISTS idx_bruteforce_alvo_ip ON bruteforce_ataques(alvo_ip);

-- Comentários para documentação
COMMENT ON TABLE bruteforce_ataques IS 'Tabela para gerenciar ataques de bruteforce ao sistema bancário';
COMMENT ON COLUMN bruteforce_ataques.usuario_id IS 'UUID do usuário que executou o ataque';
COMMENT ON COLUMN bruteforce_ataques.alvo_ip IS 'Endereço IP do alvo do ataque';
COMMENT ON COLUMN bruteforce_ataques.status IS 'Status do ataque: iniciado, processando, completado, expirado';
COMMENT ON COLUMN bruteforce_ataques.alvo_dados IS 'Dados JSON do banco do alvo (quando completado)';

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_bruteforce_ataques_updated_at 
    BEFORE UPDATE ON bruteforce_ataques 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Verificação final
DO $$
BEGIN
    RAISE NOTICE 'Migração concluída com sucesso!';
    RAISE NOTICE 'Tabelas criadas: conexoes_ativas, bruteforce_ataques';
END $$;
