{"ast": null, "code": "import { createLocalStorage, THEME_PREFERENCE, QueryDevtoolsContext, PiPProvider, ThemeContext, Devtools } from '../chunk/L7Z3HDK6.js';\nimport { getPreferredColorScheme, createMemo, createComponent } from '../chunk/V5T5VJKG.js';\n\n// src/DevtoolsComponent.tsx\nvar DevtoolsComponent = props => {\n  const [localStore, setLocalStore] = createLocalStorage({\n    prefix: \"TanstackQueryDevtools\"\n  });\n  const colorScheme = getPreferredColorScheme();\n  const theme = createMemo(() => {\n    const preference = localStore.theme_preference || THEME_PREFERENCE;\n    if (preference !== \"system\") return preference;\n    return colorScheme();\n  });\n  return createComponent(QueryDevtoolsContext.Provider, {\n    value: props,\n    get children() {\n      return createComponent(PiPProvider, {\n        localStore,\n        setLocalStore,\n        get children() {\n          return createComponent(ThemeContext.Provider, {\n            value: theme,\n            get children() {\n              return createComponent(Devtools, {\n                localStore,\n                setLocalStore\n              });\n            }\n          });\n        }\n      });\n    }\n  });\n};\nvar DevtoolsComponent_default = DevtoolsComponent;\nexport { DevtoolsComponent_default as default };", "map": {"version": 3, "names": ["createLocalStorage", "THEME_PREFERENCE", "QueryDevtoolsContext", "<PERSON><PERSON><PERSON><PERSON>", "ThemeContext", "Devtools", "getPreferredColorScheme", "createMemo", "createComponent", "DevtoolsComponent", "props", "localStore", "setLocalStore", "prefix", "colorScheme", "theme", "preference", "theme_preference", "Provider", "value", "children", "DevtoolsComponent_default", "default"], "sources": ["C:/Users/<USER>/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js"], "sourcesContent": ["import { createLocalStorage, THEME_PREFERENCE, QueryDevtoolsContext, PiPProvider, ThemeContext, Devtools } from '../chunk/L7Z3HDK6.js';\nimport { getPreferredColorScheme, createMemo, createComponent } from '../chunk/V5T5VJKG.js';\n\n// src/DevtoolsComponent.tsx\nvar DevtoolsComponent = (props) => {\n  const [localStore, setLocalStore] = createLocalStorage({\n    prefix: \"TanstackQueryDevtools\"\n  });\n  const colorScheme = getPreferredColorScheme();\n  const theme = createMemo(() => {\n    const preference = localStore.theme_preference || THEME_PREFERENCE;\n    if (preference !== \"system\") return preference;\n    return colorScheme();\n  });\n  return createComponent(QueryDevtoolsContext.Provider, {\n    value: props,\n    get children() {\n      return createComponent(PiPProvider, {\n        localStore,\n        setLocalStore,\n        get children() {\n          return createComponent(ThemeContext.Provider, {\n            value: theme,\n            get children() {\n              return createComponent(Devtools, {\n                localStore,\n                setLocalStore\n              });\n            }\n          });\n        }\n      });\n    }\n  });\n};\nvar DevtoolsComponent_default = DevtoolsComponent;\n\nexport { DevtoolsComponent_default as default };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,sBAAsB;AACtI,SAASC,uBAAuB,EAAEC,UAAU,EAAEC,eAAe,QAAQ,sBAAsB;;AAE3F;AACA,IAAIC,iBAAiB,GAAIC,KAAK,IAAK;EACjC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGZ,kBAAkB,CAAC;IACrDa,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAMC,WAAW,GAAGR,uBAAuB,CAAC,CAAC;EAC7C,MAAMS,KAAK,GAAGR,UAAU,CAAC,MAAM;IAC7B,MAAMS,UAAU,GAAGL,UAAU,CAACM,gBAAgB,IAAIhB,gBAAgB;IAClE,IAAIe,UAAU,KAAK,QAAQ,EAAE,OAAOA,UAAU;IAC9C,OAAOF,WAAW,CAAC,CAAC;EACtB,CAAC,CAAC;EACF,OAAON,eAAe,CAACN,oBAAoB,CAACgB,QAAQ,EAAE;IACpDC,KAAK,EAAET,KAAK;IACZ,IAAIU,QAAQA,CAAA,EAAG;MACb,OAAOZ,eAAe,CAACL,WAAW,EAAE;QAClCQ,UAAU;QACVC,aAAa;QACb,IAAIQ,QAAQA,CAAA,EAAG;UACb,OAAOZ,eAAe,CAACJ,YAAY,CAACc,QAAQ,EAAE;YAC5CC,KAAK,EAAEJ,KAAK;YACZ,IAAIK,QAAQA,CAAA,EAAG;cACb,OAAOZ,eAAe,CAACH,QAAQ,EAAE;gBAC/BM,UAAU;gBACVC;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAIS,yBAAyB,GAAGZ,iBAAiB;AAEjD,SAASY,yBAAyB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}