{"ast": null, "code": "import { supabase, supabaseUtils } from '../lib/supabase';\nimport { GAME_CONFIG } from '../types/game';\nexport class GameService {\n  // ==================== PLAYER OPERATIONS ====================\n\n  async createPlayer(nick, email) {\n    try {\n      // Verificar se nick já existe\n      const nickExists = await supabaseUtils.checkNickExists(nick);\n      if (nickExists) {\n        return {\n          success: false,\n          error: 'Nick já está em uso'\n        };\n      }\n\n      // Verificar se email já existe\n      const emailExists = await supabaseUtils.checkEmailExists(email);\n      if (emailExists) {\n        return {\n          success: false,\n          error: 'Email já está em uso'\n        };\n      }\n\n      // Gerar IP único\n      const ip = await supabaseUtils.generateUniqueIP();\n\n      // Criar player\n      const {\n        data: playerData,\n        error: playerError\n      } = await supabase.from('players').insert({\n        nick,\n        email,\n        ip,\n        level: 1,\n        xp: 0,\n        cash: GAME_CONFIG.INITIAL_CASH,\n        last_login: new Date().toISOString()\n      }).select().single();\n      if (playerError) {\n        return {\n          success: false,\n          error: playerError.message\n        };\n      }\n\n      // Criar apps iniciais\n      const {\n        error: appsError\n      } = await supabase.from('player_apps').insert({\n        player_id: playerData.id,\n        ...GAME_CONFIG.INITIAL_APPS\n      });\n      if (appsError) {\n        return {\n          success: false,\n          error: appsError.message\n        };\n      }\n\n      // Criar notificação de boas-vindas\n      await this.createNotification(playerData.id, {\n        type: 'system',\n        title: 'Bem-vindo ao SHACK!',\n        message: `Seu IP é ${ip}. Comece fazendo upgrades nos seus apps!`\n      });\n      return {\n        success: true,\n        player: playerData\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n  async getPlayer(playerId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('players').select('*').eq('id', playerId).single();\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n      return {\n        success: true,\n        player: data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n  async updatePlayer(playerId, updates) {\n    try {\n      const {\n        error\n      } = await supabase.from('players').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n      }).eq('id', playerId);\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  // ==================== PLAYER APPS OPERATIONS ====================\n\n  async getPlayerApps(playerId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('player_apps').select('*').eq('player_id', playerId).single();\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n      return {\n        success: true,\n        apps: data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n  async updatePlayerApps(playerId, apps) {\n    try {\n      const {\n        error\n      } = await supabase.from('player_apps').update({\n        ...apps,\n        updated_at: new Date().toISOString()\n      }).eq('player_id', playerId);\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  // ==================== NOTIFICATIONS ====================\n\n  async createNotification(playerId, notification) {\n    try {\n      const {\n        error\n      } = await supabase.from('notifications').insert({\n        player_id: playerId,\n        ...notification,\n        read: false\n      });\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n  async getNotifications(playerId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('notifications').select('*').eq('player_id', playerId).order('created_at', {\n        ascending: false\n      }).limit(50);\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n      return {\n        success: true,\n        notifications: data || []\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n  async markNotificationRead(notificationId) {\n    try {\n      const {\n        error\n      } = await supabase.from('notifications').update({\n        read: true\n      }).eq('id', notificationId);\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  // ==================== CHAT ====================\n\n  async sendChatMessage(playerId, playerNick, message) {\n    try {\n      const {\n        error\n      } = await supabase.from('chat_messages').insert({\n        player_id: playerId,\n        player_nick: playerNick,\n        message\n      });\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n  async getChatMessages(limit = 50) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('chat_messages').select('*').order('created_at', {\n        ascending: false\n      }).limit(limit);\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n      return {\n        success: true,\n        messages: (data === null || data === void 0 ? void 0 : data.reverse()) || []\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  // ==================== SCANNING & HACKING ====================\n\n  async scanForTargets(playerId) {\n    try {\n      // Buscar outros jogadores online (exceto o próprio)\n      const {\n        data,\n        error\n      } = await supabase.from('players').select('id, nick, ip, level, cash').neq('id', playerId).order('last_login', {\n        ascending: false\n      }).limit(10);\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n\n      // Converter para formato de targets\n      const targets = (data === null || data === void 0 ? void 0 : data.map(player => ({\n        id: player.id,\n        player_id: playerId,\n        target_ip: player.ip,\n        target_nick: player.nick,\n        target_level: player.level,\n        target_cash: player.cash,\n        firewall_level: Math.floor(Math.random() * 5) + 1,\n        // Simular nível de firewall\n        last_seen: new Date().toISOString()\n      }))) || [];\n      return {\n        success: true,\n        targets\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  // ==================== RANKING ====================\n\n  async getTopPlayers(limit = 20) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('players').select('nick, level, xp, cash').order('level', {\n        ascending: false\n      }).order('xp', {\n        ascending: false\n      }).limit(limit);\n      if (error) {\n        return {\n          success: false,\n          error: error.message\n        };\n      }\n      return {\n        success: true,\n        players: data || []\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  // ==================== UTILITY ====================\n\n  async testConnection() {\n    return await supabaseUtils.testConnection();\n  }\n}\nexport const gameService = new GameService();", "map": {"version": 3, "names": ["supabase", "supabaseUtils", "GAME_CONFIG", "GameService", "createPlayer", "nick", "email", "nickExists", "checkNickExists", "success", "error", "emailExists", "checkEmailExists", "ip", "generateUniqueIP", "data", "player<PERSON><PERSON>", "playerError", "from", "insert", "level", "xp", "cash", "INITIAL_CASH", "last_login", "Date", "toISOString", "select", "single", "message", "appsError", "player_id", "id", "INITIAL_APPS", "createNotification", "type", "title", "player", "getPlayer", "playerId", "eq", "updatePlayer", "updates", "update", "updated_at", "getPlayerApps", "apps", "updatePlayerApps", "notification", "read", "getNotifications", "order", "ascending", "limit", "notifications", "markNotificationRead", "notificationId", "sendChatMessage", "<PERSON><PERSON><PERSON>", "player_nick", "getChatMessages", "messages", "reverse", "scanForTargets", "neq", "targets", "map", "target_ip", "target_nick", "target_level", "target_cash", "firewall_level", "Math", "floor", "random", "last_seen", "getTopPlayers", "players", "testConnection", "gameService"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/gameService.ts"], "sourcesContent": ["import { supabase, supabaseUtils, Player, PlayerApps, GameNotification, HackTarget, ChatMessage } from '../lib/supabase';\nimport { GAME_CONFIG } from '../types/game';\n\nexport class GameService {\n  // ==================== PLAYER OPERATIONS ====================\n  \n  async createPlayer(nick: string, email: string): Promise<{ success: boolean; player?: Player; error?: string }> {\n    try {\n      // Verificar se nick já existe\n      const nickExists = await supabaseUtils.checkNickExists(nick);\n      if (nickExists) {\n        return { success: false, error: 'Nick já está em uso' };\n      }\n\n      // Verificar se email já existe\n      const emailExists = await supabaseUtils.checkEmailExists(email);\n      if (emailExists) {\n        return { success: false, error: 'Email já está em uso' };\n      }\n\n      // Gerar IP único\n      const ip = await supabaseUtils.generateUniqueIP();\n\n      // Criar player\n      const { data: playerData, error: playerError } = await supabase\n        .from('players')\n        .insert({\n          nick,\n          email,\n          ip,\n          level: 1,\n          xp: 0,\n          cash: GAME_CONFIG.INITIAL_CASH,\n          last_login: new Date().toISOString()\n        })\n        .select()\n        .single();\n\n      if (playerError) {\n        return { success: false, error: playerError.message };\n      }\n\n      // Criar apps iniciais\n      const { error: appsError } = await supabase\n        .from('player_apps')\n        .insert({\n          player_id: playerData.id,\n          ...GAME_CONFIG.INITIAL_APPS\n        });\n\n      if (appsError) {\n        return { success: false, error: appsError.message };\n      }\n\n      // Criar notificação de boas-vindas\n      await this.createNotification(playerData.id, {\n        type: 'system',\n        title: 'Bem-vindo ao SHACK!',\n        message: `Seu IP é ${ip}. Comece fazendo upgrades nos seus apps!`\n      });\n\n      return { success: true, player: playerData };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  async getPlayer(playerId: string): Promise<{ success: boolean; player?: Player; error?: string }> {\n    try {\n      const { data, error } = await supabase\n        .from('players')\n        .select('*')\n        .eq('id', playerId)\n        .single();\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      return { success: true, player: data };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  async updatePlayer(playerId: string, updates: Partial<Player>): Promise<{ success: boolean; error?: string }> {\n    try {\n      const { error } = await supabase\n        .from('players')\n        .update({ ...updates, updated_at: new Date().toISOString() })\n        .eq('id', playerId);\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      return { success: true };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  // ==================== PLAYER APPS OPERATIONS ====================\n\n  async getPlayerApps(playerId: string): Promise<{ success: boolean; apps?: PlayerApps; error?: string }> {\n    try {\n      const { data, error } = await supabase\n        .from('player_apps')\n        .select('*')\n        .eq('player_id', playerId)\n        .single();\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      return { success: true, apps: data };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  async updatePlayerApps(playerId: string, apps: Partial<PlayerApps>): Promise<{ success: boolean; error?: string }> {\n    try {\n      const { error } = await supabase\n        .from('player_apps')\n        .update({ ...apps, updated_at: new Date().toISOString() })\n        .eq('player_id', playerId);\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      return { success: true };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  // ==================== NOTIFICATIONS ====================\n\n  async createNotification(playerId: string, notification: Omit<GameNotification, 'id' | 'player_id' | 'created_at' | 'read'>): Promise<{ success: boolean; error?: string }> {\n    try {\n      const { error } = await supabase\n        .from('notifications')\n        .insert({\n          player_id: playerId,\n          ...notification,\n          read: false\n        });\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      return { success: true };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  async getNotifications(playerId: string): Promise<{ success: boolean; notifications?: GameNotification[]; error?: string }> {\n    try {\n      const { data, error } = await supabase\n        .from('notifications')\n        .select('*')\n        .eq('player_id', playerId)\n        .order('created_at', { ascending: false })\n        .limit(50);\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      return { success: true, notifications: data || [] };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  async markNotificationRead(notificationId: string): Promise<{ success: boolean; error?: string }> {\n    try {\n      const { error } = await supabase\n        .from('notifications')\n        .update({ read: true })\n        .eq('id', notificationId);\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      return { success: true };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  // ==================== CHAT ====================\n\n  async sendChatMessage(playerId: string, playerNick: string, message: string): Promise<{ success: boolean; error?: string }> {\n    try {\n      const { error } = await supabase\n        .from('chat_messages')\n        .insert({\n          player_id: playerId,\n          player_nick: playerNick,\n          message\n        });\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      return { success: true };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  async getChatMessages(limit: number = 50): Promise<{ success: boolean; messages?: ChatMessage[]; error?: string }> {\n    try {\n      const { data, error } = await supabase\n        .from('chat_messages')\n        .select('*')\n        .order('created_at', { ascending: false })\n        .limit(limit);\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      return { success: true, messages: data?.reverse() || [] };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  // ==================== SCANNING & HACKING ====================\n\n  async scanForTargets(playerId: string): Promise<{ success: boolean; targets?: HackTarget[]; error?: string }> {\n    try {\n      // Buscar outros jogadores online (exceto o próprio)\n      const { data, error } = await supabase\n        .from('players')\n        .select('id, nick, ip, level, cash')\n        .neq('id', playerId)\n        .order('last_login', { ascending: false })\n        .limit(10);\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      // Converter para formato de targets\n      const targets: HackTarget[] = data?.map(player => ({\n        id: player.id,\n        player_id: playerId,\n        target_ip: player.ip,\n        target_nick: player.nick,\n        target_level: player.level,\n        target_cash: player.cash,\n        firewall_level: Math.floor(Math.random() * 5) + 1, // Simular nível de firewall\n        last_seen: new Date().toISOString()\n      })) || [];\n\n      return { success: true, targets };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  // ==================== RANKING ====================\n\n  async getTopPlayers(limit: number = 20): Promise<{ success: boolean; players?: Player[]; error?: string }> {\n    try {\n      const { data, error } = await supabase\n        .from('players')\n        .select('nick, level, xp, cash')\n        .order('level', { ascending: false })\n        .order('xp', { ascending: false })\n        .limit(limit);\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      return { success: true, players: data || [] };\n    } catch (error: any) {\n      return { success: false, error: error.message };\n    }\n  }\n\n  // ==================== UTILITY ====================\n\n  async testConnection(): Promise<{ success: boolean; error?: string }> {\n    return await supabaseUtils.testConnection();\n  }\n}\n\nexport const gameService = new GameService();\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,aAAa,QAAuE,iBAAiB;AACxH,SAASC,WAAW,QAAQ,eAAe;AAE3C,OAAO,MAAMC,WAAW,CAAC;EACvB;;EAEA,MAAMC,YAAYA,CAACC,IAAY,EAAEC,KAAa,EAAkE;IAC9G,IAAI;MACF;MACA,MAAMC,UAAU,GAAG,MAAMN,aAAa,CAACO,eAAe,CAACH,IAAI,CAAC;MAC5D,IAAIE,UAAU,EAAE;QACd,OAAO;UAAEE,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAsB,CAAC;MACzD;;MAEA;MACA,MAAMC,WAAW,GAAG,MAAMV,aAAa,CAACW,gBAAgB,CAACN,KAAK,CAAC;MAC/D,IAAIK,WAAW,EAAE;QACf,OAAO;UAAEF,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAuB,CAAC;MAC1D;;MAEA;MACA,MAAMG,EAAE,GAAG,MAAMZ,aAAa,CAACa,gBAAgB,CAAC,CAAC;;MAEjD;MACA,MAAM;QAAEC,IAAI,EAAEC,UAAU;QAAEN,KAAK,EAAEO;MAAY,CAAC,GAAG,MAAMjB,QAAQ,CAC5DkB,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC;QACNd,IAAI;QACJC,KAAK;QACLO,EAAE;QACFO,KAAK,EAAE,CAAC;QACRC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAEpB,WAAW,CAACqB,YAAY;QAC9BC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,CAAC,CACDC,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC;MAEX,IAAIX,WAAW,EAAE;QACf,OAAO;UAAER,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEO,WAAW,CAACY;QAAQ,CAAC;MACvD;;MAEA;MACA,MAAM;QAAEnB,KAAK,EAAEoB;MAAU,CAAC,GAAG,MAAM9B,QAAQ,CACxCkB,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC;QACNY,SAAS,EAAEf,UAAU,CAACgB,EAAE;QACxB,GAAG9B,WAAW,CAAC+B;MACjB,CAAC,CAAC;MAEJ,IAAIH,SAAS,EAAE;QACb,OAAO;UAAErB,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEoB,SAAS,CAACD;QAAQ,CAAC;MACrD;;MAEA;MACA,MAAM,IAAI,CAACK,kBAAkB,CAAClB,UAAU,CAACgB,EAAE,EAAE;QAC3CG,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,qBAAqB;QAC5BP,OAAO,EAAE,YAAYhB,EAAE;MACzB,CAAC,CAAC;MAEF,OAAO;QAAEJ,OAAO,EAAE,IAAI;QAAE4B,MAAM,EAAErB;MAAW,CAAC;IAC9C,CAAC,CAAC,OAAON,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;EAEA,MAAMS,SAASA,CAACC,QAAgB,EAAkE;IAChG,IAAI;MACF,MAAM;QAAExB,IAAI;QAAEL;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCkB,IAAI,CAAC,SAAS,CAAC,CACfS,MAAM,CAAC,GAAG,CAAC,CACXa,EAAE,CAAC,IAAI,EAAED,QAAQ,CAAC,CAClBX,MAAM,CAAC,CAAC;MAEX,IAAIlB,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;MAEA,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAE4B,MAAM,EAAEtB;MAAK,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;EAEA,MAAMY,YAAYA,CAACF,QAAgB,EAAEG,OAAwB,EAAiD;IAC5G,IAAI;MACF,MAAM;QAAEhC;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BkB,IAAI,CAAC,SAAS,CAAC,CACfyB,MAAM,CAAC;QAAE,GAAGD,OAAO;QAAEE,UAAU,EAAE,IAAInB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC,CAAC,CAC5Dc,EAAE,CAAC,IAAI,EAAED,QAAQ,CAAC;MAErB,IAAI7B,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;MAEA,OAAO;QAAEpB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;;EAEA;;EAEA,MAAMgB,aAAaA,CAACN,QAAgB,EAAoE;IACtG,IAAI;MACF,MAAM;QAAExB,IAAI;QAAEL;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCkB,IAAI,CAAC,aAAa,CAAC,CACnBS,MAAM,CAAC,GAAG,CAAC,CACXa,EAAE,CAAC,WAAW,EAAED,QAAQ,CAAC,CACzBX,MAAM,CAAC,CAAC;MAEX,IAAIlB,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;MAEA,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAEqC,IAAI,EAAE/B;MAAK,CAAC;IACtC,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;EAEA,MAAMkB,gBAAgBA,CAACR,QAAgB,EAAEO,IAAyB,EAAiD;IACjH,IAAI;MACF,MAAM;QAAEpC;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BkB,IAAI,CAAC,aAAa,CAAC,CACnByB,MAAM,CAAC;QAAE,GAAGG,IAAI;QAAEF,UAAU,EAAE,IAAInB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC,CAAC,CACzDc,EAAE,CAAC,WAAW,EAAED,QAAQ,CAAC;MAE5B,IAAI7B,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;MAEA,OAAO;QAAEpB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;;EAEA;;EAEA,MAAMK,kBAAkBA,CAACK,QAAgB,EAAES,YAAgF,EAAiD;IAC1K,IAAI;MACF,MAAM;QAAEtC;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BkB,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC;QACNY,SAAS,EAAEQ,QAAQ;QACnB,GAAGS,YAAY;QACfC,IAAI,EAAE;MACR,CAAC,CAAC;MAEJ,IAAIvC,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;MAEA,OAAO;QAAEpB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;EAEA,MAAMqB,gBAAgBA,CAACX,QAAgB,EAAqF;IAC1H,IAAI;MACF,MAAM;QAAExB,IAAI;QAAEL;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCkB,IAAI,CAAC,eAAe,CAAC,CACrBS,MAAM,CAAC,GAAG,CAAC,CACXa,EAAE,CAAC,WAAW,EAAED,QAAQ,CAAC,CACzBY,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC,CACzCC,KAAK,CAAC,EAAE,CAAC;MAEZ,IAAI3C,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;MAEA,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAE6C,aAAa,EAAEvC,IAAI,IAAI;MAAG,CAAC;IACrD,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;EAEA,MAAM0B,oBAAoBA,CAACC,cAAsB,EAAiD;IAChG,IAAI;MACF,MAAM;QAAE9C;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BkB,IAAI,CAAC,eAAe,CAAC,CACrByB,MAAM,CAAC;QAAEM,IAAI,EAAE;MAAK,CAAC,CAAC,CACtBT,EAAE,CAAC,IAAI,EAAEgB,cAAc,CAAC;MAE3B,IAAI9C,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;MAEA,OAAO;QAAEpB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;;EAEA;;EAEA,MAAM4B,eAAeA,CAAClB,QAAgB,EAAEmB,UAAkB,EAAE7B,OAAe,EAAiD;IAC1H,IAAI;MACF,MAAM;QAAEnB;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BkB,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC;QACNY,SAAS,EAAEQ,QAAQ;QACnBoB,WAAW,EAAED,UAAU;QACvB7B;MACF,CAAC,CAAC;MAEJ,IAAInB,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;MAEA,OAAO;QAAEpB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;EAEA,MAAM+B,eAAeA,CAACP,KAAa,GAAG,EAAE,EAA2E;IACjH,IAAI;MACF,MAAM;QAAEtC,IAAI;QAAEL;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCkB,IAAI,CAAC,eAAe,CAAC,CACrBS,MAAM,CAAC,GAAG,CAAC,CACXwB,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC,CACzCC,KAAK,CAACA,KAAK,CAAC;MAEf,IAAI3C,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;MAEA,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAEoD,QAAQ,EAAE,CAAA9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,OAAO,CAAC,CAAC,KAAI;MAAG,CAAC;IAC3D,CAAC,CAAC,OAAOpD,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;;EAEA;;EAEA,MAAMkC,cAAcA,CAACxB,QAAgB,EAAyE;IAC5G,IAAI;MACF;MACA,MAAM;QAAExB,IAAI;QAAEL;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCkB,IAAI,CAAC,SAAS,CAAC,CACfS,MAAM,CAAC,2BAA2B,CAAC,CACnCqC,GAAG,CAAC,IAAI,EAAEzB,QAAQ,CAAC,CACnBY,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC,CACzCC,KAAK,CAAC,EAAE,CAAC;MAEZ,IAAI3C,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;;MAEA;MACA,MAAMoC,OAAqB,GAAG,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,GAAG,CAAC7B,MAAM,KAAK;QACjDL,EAAE,EAAEK,MAAM,CAACL,EAAE;QACbD,SAAS,EAAEQ,QAAQ;QACnB4B,SAAS,EAAE9B,MAAM,CAACxB,EAAE;QACpBuD,WAAW,EAAE/B,MAAM,CAAChC,IAAI;QACxBgE,YAAY,EAAEhC,MAAM,CAACjB,KAAK;QAC1BkD,WAAW,EAAEjC,MAAM,CAACf,IAAI;QACxBiD,cAAc,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QAAE;QACnDC,SAAS,EAAE,IAAIlD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CAAC,CAAC,KAAI,EAAE;MAET,OAAO;QAAEjB,OAAO,EAAE,IAAI;QAAEwD;MAAQ,CAAC;IACnC,CAAC,CAAC,OAAOvD,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;;EAEA;;EAEA,MAAM+C,aAAaA,CAACvB,KAAa,GAAG,EAAE,EAAqE;IACzG,IAAI;MACF,MAAM;QAAEtC,IAAI;QAAEL;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCkB,IAAI,CAAC,SAAS,CAAC,CACfS,MAAM,CAAC,uBAAuB,CAAC,CAC/BwB,KAAK,CAAC,OAAO,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC,CACpCD,KAAK,CAAC,IAAI,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC,CACjCC,KAAK,CAACA,KAAK,CAAC;MAEf,IAAI3C,KAAK,EAAE;QACT,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEA,KAAK,CAACmB;QAAQ,CAAC;MACjD;MAEA,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAEoE,OAAO,EAAE9D,IAAI,IAAI;MAAG,CAAC;IAC/C,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF;;EAEA;;EAEA,MAAMiD,cAAcA,CAAA,EAAkD;IACpE,OAAO,MAAM7E,aAAa,CAAC6E,cAAc,CAAC,CAAC;EAC7C;AACF;AAEA,OAAO,MAAMC,WAAW,GAAG,IAAI5E,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}