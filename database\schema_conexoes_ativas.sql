-- SQL para criar tabela de conexões ativas separada
-- Execute este SQL no Supabase SQL Editor

CREATE TABLE IF NOT EXISTS conexoes_ativas (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    atacante_uid TEXT NOT NULL,
    alvo_ip TEXT NOT NULL,
    alvo_nick TEXT NOT NULL,
    alvo_bankguard INTEGER DEFAULT 1,
    alvo_dinheiro INTEGER DEFAULT 0,
    status TEXT DEFAULT 'ativa',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Índices para performance
    CONSTRAINT unique_atacante_alvo UNIQUE (atacante_uid, alvo_ip)
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_conexoes_atacante ON conexoes_ativas (atacante_uid);
CREATE INDEX IF NOT EXISTS idx_conexoes_expires ON conexoes_ativas (expires_at);
CREATE INDEX IF NOT EXISTS idx_conexoes_status ON conexoes_ativas (status);

-- Função para limpar conexões expiradas automaticamente
CREATE OR REPLACE FUNCTION limpar_conexoes_expiradas()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM conexoes_ativas 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Trigger para limpar conexões expiradas automaticamente
CREATE OR REPLACE FUNCTION trigger_limpar_conexoes_expiradas()
RETURNS TRIGGER AS $$
BEGIN
    -- Limpa conexões expiradas antes de qualquer operação
    PERFORM limpar_conexoes_expiradas();
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Cria trigger para limpeza automática (apenas em INSERT)
DROP TRIGGER IF EXISTS auto_limpar_conexoes ON conexoes_ativas;
CREATE TRIGGER auto_limpar_conexoes
    BEFORE INSERT ON conexoes_ativas
    FOR EACH ROW
    EXECUTE FUNCTION trigger_limpar_conexoes_expiradas();
