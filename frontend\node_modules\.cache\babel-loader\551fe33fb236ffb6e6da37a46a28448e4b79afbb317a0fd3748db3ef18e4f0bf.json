{"ast": null, "code": "var _s = $RefreshSig$();\nimport { create } from 'zustand';\nimport apiService from '../services/api';\nlet pollIntervalId = null;\nexport const useChatStore = create((set, get) => ({\n  // Estado inicial\n  messages: [],\n  isLoading: false,\n  error: null,\n  isSending: false,\n  sendError: null,\n  isPolling: false,\n  pollInterval: 2000,\n  // 2 segundos\n  lastMessageTime: null,\n  unreadCount: 0,\n  isOpen: false,\n  isMinimized: false,\n  // === AÇÕES DE MENSAGENS ===\n  loadMessages: async () => {\n    const state = get();\n    if (state.isLoading) return; // Evitar múltiplas requisições simultâneas\n\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const response = await apiService.getChatMessages();\n      if (response.sucesso && response.messages) {\n        const newMessages = response.messages;\n        const currentMessages = state.messages;\n\n        // Verificar se há mensagens novas\n        const lastCurrentMessageTime = currentMessages.length > 0 ? currentMessages[currentMessages.length - 1].timestamp : null;\n        const newUnreadCount = lastCurrentMessageTime ? newMessages.filter(msg => msg.timestamp > lastCurrentMessageTime).length : newMessages.length;\n        set({\n          messages: newMessages,\n          isLoading: false,\n          error: null,\n          lastMessageTime: newMessages.length > 0 ? newMessages[newMessages.length - 1].timestamp : state.lastMessageTime,\n          unreadCount: state.isOpen ? 0 : state.unreadCount + newUnreadCount\n        });\n      } else {\n        throw new Error(response.mensagem || 'Erro ao carregar mensagens');\n      }\n    } catch (error) {\n      set({\n        isLoading: false,\n        error: error.message || 'Erro ao carregar mensagens'\n      });\n    }\n  },\n  sendMessage: async message => {\n    if (!message.trim()) return;\n    set({\n      isSending: true,\n      sendError: null\n    });\n    try {\n      const response = await apiService.sendChatMessage(message.trim());\n      if (response.sucesso) {\n        set({\n          isSending: false,\n          sendError: null\n        });\n\n        // Recarregar mensagens após enviar\n        setTimeout(() => {\n          get().loadMessages();\n        }, 500);\n      } else {\n        throw new Error(response.mensagem || 'Erro ao enviar mensagem');\n      }\n    } catch (error) {\n      set({\n        isSending: false,\n        sendError: error.message || 'Erro ao enviar mensagem'\n      });\n      throw error;\n    }\n  },\n  // === AÇÕES DE POLLING ===\n  startPolling: () => {\n    const state = get();\n    if (state.isPolling) return;\n    set({\n      isPolling: true\n    });\n\n    // Carregar mensagens imediatamente\n    get().loadMessages();\n\n    // Configurar polling\n    pollIntervalId = setInterval(() => {\n      get().loadMessages();\n    }, state.pollInterval);\n  },\n  stopPolling: () => {\n    set({\n      isPolling: false\n    });\n    if (pollIntervalId) {\n      clearInterval(pollIntervalId);\n      pollIntervalId = null;\n    }\n  },\n  markAsRead: () => {\n    set({\n      unreadCount: 0\n    });\n  },\n  // === AÇÕES DE UI ===\n  openChat: () => {\n    set({\n      isOpen: true,\n      isMinimized: false,\n      unreadCount: 0\n    });\n\n    // Iniciar polling quando abrir o chat\n    if (!get().isPolling) {\n      get().startPolling();\n    }\n  },\n  closeChat: () => {\n    set({\n      isOpen: false,\n      isMinimized: false\n    });\n\n    // Parar polling quando fechar o chat\n    get().stopPolling();\n  },\n  toggleChat: () => {\n    const state = get();\n    if (state.isOpen) {\n      state.closeChat();\n    } else {\n      state.openChat();\n    }\n  },\n  minimizeChat: () => {\n    set({\n      isMinimized: true\n    });\n  },\n  maximizeChat: () => {\n    set({\n      isMinimized: false\n    });\n  },\n  // === AÇÕES UTILITÁRIAS ===\n  clearError: () => {\n    set({\n      error: null,\n      sendError: null\n    });\n  },\n  clearMessages: () => {\n    set({\n      messages: [],\n      lastMessageTime: null,\n      unreadCount: 0\n    });\n  }\n}));\n\n// Hook personalizado para usar o chat\nexport const useChat = () => {\n  _s();\n  const store = useChatStore();\n  return {\n    ...store,\n    // Computed values\n    hasMessages: store.messages.length > 0,\n    hasUnread: store.unreadCount > 0,\n    canSend: !store.isSending && !store.isLoading,\n    // Utility functions\n    formatTime: timestamp => {\n      const date = new Date(timestamp);\n      return date.toLocaleTimeString('pt-BR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n    formatDate: timestamp => {\n      const date = new Date(timestamp);\n      const today = new Date();\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === today.toDateString()) {\n        return 'Hoje';\n      } else if (date.toDateString() === yesterday.toDateString()) {\n        return 'Ontem';\n      } else {\n        return date.toLocaleDateString('pt-BR');\n      }\n    },\n    isMessageFromToday: timestamp => {\n      const date = new Date(timestamp);\n      const today = new Date();\n      return date.toDateString() === today.toDateString();\n    }\n  };\n};\n_s(useChat, \"eBttsSSr/1EDYbiaHkm0/Ogdl38=\", false, function () {\n  return [useChatStore];\n});", "map": {"version": 3, "names": ["create", "apiService", "pollIntervalId", "useChatStore", "set", "get", "messages", "isLoading", "error", "isSending", "sendError", "isPolling", "pollInterval", "lastMessageTime", "unreadCount", "isOpen", "isMinimized", "loadMessages", "state", "response", "getChatMessages", "sucesso", "newMessages", "currentMessages", "lastCurrentMessageTime", "length", "timestamp", "newUnreadCount", "filter", "msg", "Error", "mensagem", "message", "sendMessage", "trim", "sendChatMessage", "setTimeout", "startPolling", "setInterval", "stopPolling", "clearInterval", "mark<PERSON><PERSON><PERSON>", "openChat", "closeChat", "toggleChat", "minimizeChat", "maximizeChat", "clearError", "clearMessages", "useChat", "_s", "store", "hasMessages", "hasUnread", "canSend", "formatTime", "date", "Date", "toLocaleTimeString", "hour", "minute", "formatDate", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "isMessageFromToday"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/chatStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport apiService, { ChatMessage } from '../services/api';\n\ninterface ChatState {\n  // Estado das mensagens\n  messages: ChatMessage[];\n  isLoading: boolean;\n  error: string | null;\n  \n  // Estado de envio\n  isSending: boolean;\n  sendError: string | null;\n  \n  // Configurações\n  isPolling: boolean;\n  pollInterval: number;\n  lastMessageTime: string | null;\n  unreadCount: number;\n  \n  // Estado da UI\n  isOpen: boolean;\n  isMinimized: boolean;\n  \n  // Ações\n  loadMessages: () => Promise<void>;\n  sendMessage: (message: string) => Promise<void>;\n  startPolling: () => void;\n  stopPolling: () => void;\n  markAsRead: () => void;\n  \n  // UI Actions\n  openChat: () => void;\n  closeChat: () => void;\n  toggleChat: () => void;\n  minimizeChat: () => void;\n  maximizeChat: () => void;\n  \n  // Utility actions\n  clearError: () => void;\n  clearMessages: () => void;\n}\n\nlet pollIntervalId: NodeJS.Timeout | null = null;\n\nexport const useChatStore = create<ChatState>((set, get) => ({\n  // Estado inicial\n  messages: [],\n  isLoading: false,\n  error: null,\n  \n  isSending: false,\n  sendError: null,\n  \n  isPolling: false,\n  pollInterval: 2000, // 2 segundos\n  lastMessageTime: null,\n  unreadCount: 0,\n  \n  isOpen: false,\n  isMinimized: false,\n\n  // === AÇÕES DE MENSAGENS ===\n  loadMessages: async () => {\n    const state = get();\n    if (state.isLoading) return; // Evitar múltiplas requisições simultâneas\n    \n    set({ isLoading: true, error: null });\n\n    try {\n      const response = await apiService.getChatMessages();\n      \n      if (response.sucesso && response.messages) {\n        const newMessages = response.messages;\n        const currentMessages = state.messages;\n        \n        // Verificar se há mensagens novas\n        const lastCurrentMessageTime = currentMessages.length > 0 \n          ? currentMessages[currentMessages.length - 1].timestamp \n          : null;\n        \n        const newUnreadCount = lastCurrentMessageTime\n          ? newMessages.filter((msg: ChatMessage) => msg.timestamp > lastCurrentMessageTime).length\n          : newMessages.length;\n        \n        set({\n          messages: newMessages,\n          isLoading: false,\n          error: null,\n          lastMessageTime: newMessages.length > 0 \n            ? newMessages[newMessages.length - 1].timestamp \n            : state.lastMessageTime,\n          unreadCount: state.isOpen ? 0 : state.unreadCount + newUnreadCount,\n        });\n      } else {\n        throw new Error(response.mensagem || 'Erro ao carregar mensagens');\n      }\n    } catch (error: any) {\n      set({\n        isLoading: false,\n        error: error.message || 'Erro ao carregar mensagens',\n      });\n    }\n  },\n\n  sendMessage: async (message: string) => {\n    if (!message.trim()) return;\n    \n    set({ isSending: true, sendError: null });\n\n    try {\n      const response = await apiService.sendChatMessage(message.trim());\n      \n      if (response.sucesso) {\n        set({\n          isSending: false,\n          sendError: null,\n        });\n        \n        // Recarregar mensagens após enviar\n        setTimeout(() => {\n          get().loadMessages();\n        }, 500);\n      } else {\n        throw new Error(response.mensagem || 'Erro ao enviar mensagem');\n      }\n    } catch (error: any) {\n      set({\n        isSending: false,\n        sendError: error.message || 'Erro ao enviar mensagem',\n      });\n      throw error;\n    }\n  },\n\n  // === AÇÕES DE POLLING ===\n  startPolling: () => {\n    const state = get();\n    if (state.isPolling) return;\n    \n    set({ isPolling: true });\n    \n    // Carregar mensagens imediatamente\n    get().loadMessages();\n    \n    // Configurar polling\n    pollIntervalId = setInterval(() => {\n      get().loadMessages();\n    }, state.pollInterval);\n  },\n\n  stopPolling: () => {\n    set({ isPolling: false });\n    \n    if (pollIntervalId) {\n      clearInterval(pollIntervalId);\n      pollIntervalId = null;\n    }\n  },\n\n  markAsRead: () => {\n    set({ unreadCount: 0 });\n  },\n\n  // === AÇÕES DE UI ===\n  openChat: () => {\n    set({ \n      isOpen: true, \n      isMinimized: false,\n      unreadCount: 0,\n    });\n    \n    // Iniciar polling quando abrir o chat\n    if (!get().isPolling) {\n      get().startPolling();\n    }\n  },\n\n  closeChat: () => {\n    set({ isOpen: false, isMinimized: false });\n    \n    // Parar polling quando fechar o chat\n    get().stopPolling();\n  },\n\n  toggleChat: () => {\n    const state = get();\n    if (state.isOpen) {\n      state.closeChat();\n    } else {\n      state.openChat();\n    }\n  },\n\n  minimizeChat: () => {\n    set({ isMinimized: true });\n  },\n\n  maximizeChat: () => {\n    set({ isMinimized: false });\n  },\n\n  // === AÇÕES UTILITÁRIAS ===\n  clearError: () => {\n    set({ error: null, sendError: null });\n  },\n\n  clearMessages: () => {\n    set({ messages: [], lastMessageTime: null, unreadCount: 0 });\n  },\n}));\n\n// Hook personalizado para usar o chat\nexport const useChat = () => {\n  const store = useChatStore();\n  \n  return {\n    ...store,\n    \n    // Computed values\n    hasMessages: store.messages.length > 0,\n    hasUnread: store.unreadCount > 0,\n    canSend: !store.isSending && !store.isLoading,\n    \n    // Utility functions\n    formatTime: (timestamp: string) => {\n      const date = new Date(timestamp);\n      return date.toLocaleTimeString('pt-BR', {\n        hour: '2-digit',\n        minute: '2-digit',\n      });\n    },\n    \n    formatDate: (timestamp: string) => {\n      const date = new Date(timestamp);\n      const today = new Date();\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      \n      if (date.toDateString() === today.toDateString()) {\n        return 'Hoje';\n      } else if (date.toDateString() === yesterday.toDateString()) {\n        return 'Ontem';\n      } else {\n        return date.toLocaleDateString('pt-BR');\n      }\n    },\n    \n    isMessageFromToday: (timestamp: string) => {\n      const date = new Date(timestamp);\n      const today = new Date();\n      return date.toDateString() === today.toDateString();\n    },\n  };\n};\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,OAAOC,UAAU,MAAuB,iBAAiB;AAyCzD,IAAIC,cAAqC,GAAG,IAAI;AAEhD,OAAO,MAAMC,YAAY,GAAGH,MAAM,CAAY,CAACI,GAAG,EAAEC,GAAG,MAAM;EAC3D;EACAC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEXC,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE,IAAI;EAEfC,SAAS,EAAE,KAAK;EAChBC,YAAY,EAAE,IAAI;EAAE;EACpBC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,CAAC;EAEdC,MAAM,EAAE,KAAK;EACbC,WAAW,EAAE,KAAK;EAElB;EACAC,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,MAAMC,KAAK,GAAGb,GAAG,CAAC,CAAC;IACnB,IAAIa,KAAK,CAACX,SAAS,EAAE,OAAO,CAAC;;IAE7BH,GAAG,CAAC;MAAEG,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMlB,UAAU,CAACmB,eAAe,CAAC,CAAC;MAEnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACb,QAAQ,EAAE;QACzC,MAAMgB,WAAW,GAAGH,QAAQ,CAACb,QAAQ;QACrC,MAAMiB,eAAe,GAAGL,KAAK,CAACZ,QAAQ;;QAEtC;QACA,MAAMkB,sBAAsB,GAAGD,eAAe,CAACE,MAAM,GAAG,CAAC,GACrDF,eAAe,CAACA,eAAe,CAACE,MAAM,GAAG,CAAC,CAAC,CAACC,SAAS,GACrD,IAAI;QAER,MAAMC,cAAc,GAAGH,sBAAsB,GACzCF,WAAW,CAACM,MAAM,CAAEC,GAAgB,IAAKA,GAAG,CAACH,SAAS,GAAGF,sBAAsB,CAAC,CAACC,MAAM,GACvFH,WAAW,CAACG,MAAM;QAEtBrB,GAAG,CAAC;UACFE,QAAQ,EAAEgB,WAAW;UACrBf,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE,IAAI;UACXK,eAAe,EAAES,WAAW,CAACG,MAAM,GAAG,CAAC,GACnCH,WAAW,CAACA,WAAW,CAACG,MAAM,GAAG,CAAC,CAAC,CAACC,SAAS,GAC7CR,KAAK,CAACL,eAAe;UACzBC,WAAW,EAAEI,KAAK,CAACH,MAAM,GAAG,CAAC,GAAGG,KAAK,CAACJ,WAAW,GAAGa;QACtD,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAACX,QAAQ,CAACY,QAAQ,IAAI,4BAA4B,CAAC;MACpE;IACF,CAAC,CAAC,OAAOvB,KAAU,EAAE;MACnBJ,GAAG,CAAC;QACFG,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEA,KAAK,CAACwB,OAAO,IAAI;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC;EAEDC,WAAW,EAAE,MAAOD,OAAe,IAAK;IACtC,IAAI,CAACA,OAAO,CAACE,IAAI,CAAC,CAAC,EAAE;IAErB9B,GAAG,CAAC;MAAEK,SAAS,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAEzC,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMlB,UAAU,CAACkC,eAAe,CAACH,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC;MAEjE,IAAIf,QAAQ,CAACE,OAAO,EAAE;QACpBjB,GAAG,CAAC;UACFK,SAAS,EAAE,KAAK;UAChBC,SAAS,EAAE;QACb,CAAC,CAAC;;QAEF;QACA0B,UAAU,CAAC,MAAM;UACf/B,GAAG,CAAC,CAAC,CAACY,YAAY,CAAC,CAAC;QACtB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACL,MAAM,IAAIa,KAAK,CAACX,QAAQ,CAACY,QAAQ,IAAI,yBAAyB,CAAC;MACjE;IACF,CAAC,CAAC,OAAOvB,KAAU,EAAE;MACnBJ,GAAG,CAAC;QACFK,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAEF,KAAK,CAACwB,OAAO,IAAI;MAC9B,CAAC,CAAC;MACF,MAAMxB,KAAK;IACb;EACF,CAAC;EAED;EACA6B,YAAY,EAAEA,CAAA,KAAM;IAClB,MAAMnB,KAAK,GAAGb,GAAG,CAAC,CAAC;IACnB,IAAIa,KAAK,CAACP,SAAS,EAAE;IAErBP,GAAG,CAAC;MAAEO,SAAS,EAAE;IAAK,CAAC,CAAC;;IAExB;IACAN,GAAG,CAAC,CAAC,CAACY,YAAY,CAAC,CAAC;;IAEpB;IACAf,cAAc,GAAGoC,WAAW,CAAC,MAAM;MACjCjC,GAAG,CAAC,CAAC,CAACY,YAAY,CAAC,CAAC;IACtB,CAAC,EAAEC,KAAK,CAACN,YAAY,CAAC;EACxB,CAAC;EAED2B,WAAW,EAAEA,CAAA,KAAM;IACjBnC,GAAG,CAAC;MAAEO,SAAS,EAAE;IAAM,CAAC,CAAC;IAEzB,IAAIT,cAAc,EAAE;MAClBsC,aAAa,CAACtC,cAAc,CAAC;MAC7BA,cAAc,GAAG,IAAI;IACvB;EACF,CAAC;EAEDuC,UAAU,EAAEA,CAAA,KAAM;IAChBrC,GAAG,CAAC;MAAEU,WAAW,EAAE;IAAE,CAAC,CAAC;EACzB,CAAC;EAED;EACA4B,QAAQ,EAAEA,CAAA,KAAM;IACdtC,GAAG,CAAC;MACFW,MAAM,EAAE,IAAI;MACZC,WAAW,EAAE,KAAK;MAClBF,WAAW,EAAE;IACf,CAAC,CAAC;;IAEF;IACA,IAAI,CAACT,GAAG,CAAC,CAAC,CAACM,SAAS,EAAE;MACpBN,GAAG,CAAC,CAAC,CAACgC,YAAY,CAAC,CAAC;IACtB;EACF,CAAC;EAEDM,SAAS,EAAEA,CAAA,KAAM;IACfvC,GAAG,CAAC;MAAEW,MAAM,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAM,CAAC,CAAC;;IAE1C;IACAX,GAAG,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC;EACrB,CAAC;EAEDK,UAAU,EAAEA,CAAA,KAAM;IAChB,MAAM1B,KAAK,GAAGb,GAAG,CAAC,CAAC;IACnB,IAAIa,KAAK,CAACH,MAAM,EAAE;MAChBG,KAAK,CAACyB,SAAS,CAAC,CAAC;IACnB,CAAC,MAAM;MACLzB,KAAK,CAACwB,QAAQ,CAAC,CAAC;IAClB;EACF,CAAC;EAEDG,YAAY,EAAEA,CAAA,KAAM;IAClBzC,GAAG,CAAC;MAAEY,WAAW,EAAE;IAAK,CAAC,CAAC;EAC5B,CAAC;EAED8B,YAAY,EAAEA,CAAA,KAAM;IAClB1C,GAAG,CAAC;MAAEY,WAAW,EAAE;IAAM,CAAC,CAAC;EAC7B,CAAC;EAED;EACA+B,UAAU,EAAEA,CAAA,KAAM;IAChB3C,GAAG,CAAC;MAAEI,KAAK,EAAE,IAAI;MAAEE,SAAS,EAAE;IAAK,CAAC,CAAC;EACvC,CAAC;EAEDsC,aAAa,EAAEA,CAAA,KAAM;IACnB5C,GAAG,CAAC;MAAEE,QAAQ,EAAE,EAAE;MAAEO,eAAe,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAE,CAAC,CAAC;EAC9D;AACF,CAAC,CAAC,CAAC;;AAEH;AACA,OAAO,MAAMmC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,KAAK,GAAGhD,YAAY,CAAC,CAAC;EAE5B,OAAO;IACL,GAAGgD,KAAK;IAER;IACAC,WAAW,EAAED,KAAK,CAAC7C,QAAQ,CAACmB,MAAM,GAAG,CAAC;IACtC4B,SAAS,EAAEF,KAAK,CAACrC,WAAW,GAAG,CAAC;IAChCwC,OAAO,EAAE,CAACH,KAAK,CAAC1C,SAAS,IAAI,CAAC0C,KAAK,CAAC5C,SAAS;IAE7C;IACAgD,UAAU,EAAG7B,SAAiB,IAAK;MACjC,MAAM8B,IAAI,GAAG,IAAIC,IAAI,CAAC/B,SAAS,CAAC;MAChC,OAAO8B,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IAEDC,UAAU,EAAGnC,SAAiB,IAAK;MACjC,MAAM8B,IAAI,GAAG,IAAIC,IAAI,CAAC/B,SAAS,CAAC;MAChC,MAAMoC,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC;MACxB,MAAMM,SAAS,GAAG,IAAIN,IAAI,CAACK,KAAK,CAAC;MACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MAE1C,IAAIT,IAAI,CAACU,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;QAChD,OAAO,MAAM;MACf,CAAC,MAAM,IAAIV,IAAI,CAACU,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;QAC3D,OAAO,OAAO;MAChB,CAAC,MAAM;QACL,OAAOV,IAAI,CAACW,kBAAkB,CAAC,OAAO,CAAC;MACzC;IACF,CAAC;IAEDC,kBAAkB,EAAG1C,SAAiB,IAAK;MACzC,MAAM8B,IAAI,GAAG,IAAIC,IAAI,CAAC/B,SAAS,CAAC;MAChC,MAAMoC,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC;MACxB,OAAOD,IAAI,CAACU,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC;IACrD;EACF,CAAC;AACH,CAAC;AAAChB,EAAA,CAzCWD,OAAO;EAAA,QACJ9C,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}