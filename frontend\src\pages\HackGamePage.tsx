import React, { useEffect } from 'react';
import { useHackGameStore } from '../stores/hackGameStore';
import { useSimpleAuth } from '../stores/simpleAuthStore';
import MobilePhone from '../components/game/MobilePhone';
import SimpleLoginPage from './SimpleLoginPage';
import GameHomePage from './GameHomePage';
import GameAppsPage from './GameAppsPage';
import DebugPanel from '../components/DebugPanel';

const HackGamePage: React.FC = () => {
  const { player, currentScreen, setCurrentScreen, syncWithServer, initializeFromAuth } = useHackGameStore();
  const { isAuthenticated, user } = useSimpleAuth();

  // Verificar se tem token e tentar carregar jogador
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (token && !player) {
      syncWithServer();
    }
  }, [player, syncWithServer]);

  // Se está autenticado mas não tem player carregado, tentar carregar
  useEffect(() => {
    if (isAuthenticated && user && !player) {
      console.log('Usuário autenticado, carregando dados do jogo...');
      initializeFromAuth();
    }
  }, [isAuthenticated, user, player, initializeFromAuth]);

  // Se não está autenticado, mostrar tela de login
  if (!isAuthenticated || !user) {
    return (
      <MobilePhone>
        <SimpleLoginPage />
      </MobilePhone>
    );
  }

  // Se está autenticado mas ainda não carregou o player, mostrar loading
  if (!player) {
    return (
      <MobilePhone>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Carregando dados do jogador...</p>
          </div>
        </div>
      </MobilePhone>
    );
  }

  // Renderizar tela baseada no currentScreen
  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'home':
        return <GameHomePage />;
      case 'apps':
        return <GameAppsPage />;
      case 'scanner':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">🔍</div>
              <p className="text-lg font-semibold mb-2">Scanner</p>
              <p className="text-sm text-gray-400 mb-4">Encontre alvos na rede para hackear</p>
              <div className="text-xs text-gray-500">Em desenvolvimento...</div>
            </div>
          </div>
        );
      case 'terminal':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">💻</div>
              <p className="text-lg font-semibold mb-2">Terminal</p>
              <p className="text-sm text-gray-400 mb-4">Interface de hacking avançada</p>
              <div className="text-xs text-gray-500">Em desenvolvimento...</div>
            </div>
          </div>
        );
      case 'profile':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">👤</div>
              <p className="text-lg font-semibold mb-2">Perfil</p>
              <p className="text-sm text-gray-400 mb-4">Estatísticas e conquistas</p>
              <div className="text-xs text-gray-500">Em desenvolvimento...</div>
            </div>
          </div>
        );
      case 'settings':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">⚙️</div>
              <p className="text-lg font-semibold mb-2">Configurações</p>
              <p className="text-sm text-gray-400 mb-4">Ajustes do sistema</p>
              <div className="text-xs text-gray-500">Em desenvolvimento...</div>
            </div>
          </div>
        );
      case 'shop':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">🛒</div>
              <p className="text-lg font-semibold mb-2">Loja</p>
              <p className="text-sm text-gray-400 mb-4">Compre itens especiais</p>
              <div className="text-xs text-gray-500">Em desenvolvimento...</div>
            </div>
          </div>
        );
      case 'ranking':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">🏆</div>
              <p className="text-lg font-semibold mb-2">Ranking</p>
              <p className="text-sm text-gray-400 mb-4">Top hackers do servidor</p>
              <div className="text-xs text-gray-500">Em desenvolvimento...</div>
            </div>
          </div>
        );
      case 'logs':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">📊</div>
              <p className="text-lg font-semibold mb-2">Logs</p>
              <p className="text-sm text-gray-400 mb-4">Histórico de atividades</p>
              <div className="text-xs text-gray-500">Em desenvolvimento...</div>
            </div>
          </div>
        );
      case 'chat':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">💬</div>
              <p className="text-lg font-semibold mb-2">Chat Global</p>
              <p className="text-sm text-gray-400 mb-4">Converse com outros hackers</p>
              <div className="text-xs text-gray-500">Em desenvolvimento...</div>
            </div>
          </div>
        );
      default:
        return <GameHomePage />;
    }
  };

  return (
    <>
      <MobilePhone>
        {renderCurrentScreen()}
      </MobilePhone>
      <DebugPanel />
    </>
  );
};

export default HackGamePage;
