import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { useHackGameStore } from '../stores/hackGameStore';
import MobilePhone from '../components/game/MobilePhone';
import GameSetupPage from './GameSetupPage';
import GameHomePage from './GameHomePage';
import GameAppsPage from './GameAppsPage';

const HackGamePage: React.FC = () => {
  const { player, currentScreen, setCurrentScreen } = useHackGameStore();

  // Se não tem jogador, mostrar tela de setup
  if (!player) {
    return (
      <MobilePhone>
        <GameSetupPage />
      </MobilePhone>
    );
  }

  // Renderizar tela baseada no currentScreen
  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'home':
        return <GameHomePage />;
      case 'apps':
        return <GameAppsPage />;
      case 'scanner':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">🔍</div>
              <p>Scanner - Em desenvolvimento</p>
              <button 
                onClick={() => setCurrentScreen('home')}
                className="mt-4 px-4 py-2 bg-blue-600 rounded-lg"
              >
                Voltar
              </button>
            </div>
          </div>
        );
      case 'terminal':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">💻</div>
              <p>Terminal - Em desenvolvimento</p>
              <button 
                onClick={() => setCurrentScreen('home')}
                className="mt-4 px-4 py-2 bg-blue-600 rounded-lg"
              >
                Voltar
              </button>
            </div>
          </div>
        );
      case 'profile':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">👤</div>
              <p>Perfil - Em desenvolvimento</p>
              <button 
                onClick={() => setCurrentScreen('home')}
                className="mt-4 px-4 py-2 bg-blue-600 rounded-lg"
              >
                Voltar
              </button>
            </div>
          </div>
        );
      case 'settings':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">⚙️</div>
              <p>Configurações - Em desenvolvimento</p>
              <button
                onClick={() => setCurrentScreen('home')}
                className="mt-4 px-4 py-2 bg-blue-600 rounded-lg"
              >
                Voltar
              </button>
            </div>
          </div>
        );
      case 'shop':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">🛒</div>
              <p>Loja - Em desenvolvimento</p>
              <button
                onClick={() => setCurrentScreen('home')}
                className="mt-4 px-4 py-2 bg-blue-600 rounded-lg"
              >
                Voltar
              </button>
            </div>
          </div>
        );
      case 'ranking':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">🏆</div>
              <p>Ranking - Em desenvolvimento</p>
              <button
                onClick={() => setCurrentScreen('home')}
                className="mt-4 px-4 py-2 bg-blue-600 rounded-lg"
              >
                Voltar
              </button>
            </div>
          </div>
        );
      case 'logs':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">📊</div>
              <p>Logs - Em desenvolvimento</p>
              <button
                onClick={() => setCurrentScreen('home')}
                className="mt-4 px-4 py-2 bg-blue-600 rounded-lg"
              >
                Voltar
              </button>
            </div>
          </div>
        );
      case 'chat':
        return (
          <div className="h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">💬</div>
              <p>Chat Global - Em desenvolvimento</p>
              <button
                onClick={() => setCurrentScreen('home')}
                className="mt-4 px-4 py-2 bg-blue-600 rounded-lg"
              >
                Voltar
              </button>
            </div>
          </div>
        );
      default:
        return <GameHomePage />;
    }
  };

  return (
    <Routes>
      <Route path="/*" element={
        <MobilePhone>
          {renderCurrentScreen()}
        </MobilePhone>
      } />
    </Routes>
  );
};

export default HackGamePage;
