{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\GameMainPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\n\n// Layout principal do jogo - Estilo celular antigo\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameMainPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    loadPlayerData,\n    hasPlayerData\n  } = usePlayer();\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('GameMainPage - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 1,\n    dinheiro: 0,\n    ip: '**************'\n  };\n  const handleIconClick = route => {\n    navigate(route);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-3 h-3 bg-green-400 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-mono\",\n            children: (user === null || user === void 0 ? void 0 : user.nick) || 'Ivo77'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm font-mono\",\n            children: [\"IP: \", playerData.ip]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-400\",\n            children: [\"N\\xEDvel: \", playerData.nivel]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-400\",\n            children: [\"$\", playerData.dinheiro || 0, \" / 500\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-8 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-3 gap-8 max-w-2xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/scanner'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/apps'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDCBB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Apps\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/gears'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Gears\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/chat'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDCE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Mensagem\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/transfer'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Transfer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/upgrades'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDD27\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Upgrades\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/logs'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/ranking'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Ranking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/shop'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDED2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Loja\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/mining'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\u26CF\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Minera\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/terminal'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDCBB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Terminal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\",\n          onClick: () => handleIconClick('/game/supporters'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\u2764\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"Supporters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-around items-center max-w-md mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83C\\uDFE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/chat'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/scanner'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/transfer'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Transfer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/shop'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83D\\uDED2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Loja\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(GameMainPage, \"Gzh4cCW8/G5MAMmfC9xUDiJ/DA0=\", false, function () {\n  return [useNavigate, useSimpleAuth, usePlayer];\n});\n_c = GameMainPage;\nexport default GameMainPage;\nvar _c;\n$RefreshReg$(_c, \"GameMainPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useSimpleAuth", "usePlayer", "jsxDEV", "_jsxDEV", "GameMainPage", "_s", "navigate", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "<PERSON><PERSON><PERSON>", "ip", "handleIconClick", "route", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/GameMainPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\n\n// Layout principal do jogo - Estilo celular antigo\nconst GameMainPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('GameMainPage - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 1,\n    dinheiro: 0,\n    ip: '**************'\n  };\n\n  const handleIconClick = (route: string) => {\n    navigate(route);\n  };\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header com informações do jogador */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-3 h-3 bg-green-400 rounded-full\"></div>\n            <span className=\"text-sm font-mono\">{user?.nick || 'Ivo77'}</span>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-sm font-mono\">IP: {playerData.ip}</div>\n            <div className=\"text-xs text-gray-400\">Nível: {playerData.nivel}</div>\n            <div className=\"text-xs text-gray-400\">${playerData.dinheiro || 0} / 500</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Grid principal de ícones */}\n      <div className=\"flex-1 p-8 overflow-y-auto\">\n        <div className=\"grid grid-cols-3 gap-8 max-w-2xl mx-auto\">\n          {/* Primeira linha */}\n          <div \n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/scanner')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">🔍</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Scan</span>\n          </div>\n          \n          <div \n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/apps')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">💻</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Apps</span>\n          </div>\n          \n          <div \n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/gears')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">⚙️</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Gears</span>\n          </div>\n\n          {/* Segunda linha */}\n          <div \n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/chat')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">📧</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Mensagem</span>\n          </div>\n          \n          <div\n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/transfer')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">💰</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Transfer</span>\n          </div>\n\n          <div\n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/upgrades')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">🔧</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Upgrades</span>\n          </div>\n\n          {/* Terceira linha */}\n          <div\n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/logs')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">📊</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Logs</span>\n          </div>\n          \n          <div\n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/ranking')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">🏆</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Ranking</span>\n          </div>\n\n          <div\n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/shop')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">🛒</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Loja</span>\n          </div>\n\n          {/* Quarta linha */}\n          <div\n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/mining')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">⛏️</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Mineração</span>\n          </div>\n\n          <div\n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/terminal')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">💻</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Terminal</span>\n          </div>\n          \n          <div \n            className=\"flex flex-col items-center space-y-2 cursor-pointer hover:bg-gray-800 p-4 rounded-lg transition-colors\"\n            onClick={() => handleIconClick('/game/supporters')}\n          >\n            <div className=\"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-2xl\">❤️</span>\n            </div>\n            <span className=\"text-sm text-gray-300\">Supporters</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer com botões de navegação */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-around items-center max-w-md mx-auto\">\n          <button \n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">🏠</span>\n            </div>\n            <span className=\"text-xs\">Home</span>\n          </button>\n          \n          <button \n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/chat')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">💬</span>\n            </div>\n            <span className=\"text-xs\">Chat</span>\n          </button>\n          \n          <button \n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/scanner')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">🔍</span>\n            </div>\n            <span className=\"text-xs\">Scan</span>\n          </button>\n          \n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/transfer')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">💰</span>\n            </div>\n            <span className=\"text-xs\">Transfer</span>\n          </button>\n          \n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/shop')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">🛒</span>\n            </div>\n            <span className=\"text-xs\">Loja</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GameMainPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAK,CAAC,GAAGP,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEQ,aAAa;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGV,SAAS,CAAC,CAAC;EAErFH,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,aAAa,IAAI,CAACF,eAAe,EAAE;MACtCG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DH,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACC,aAAa,EAAEF,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAEpD;EACA,MAAMI,UAAU,GAAGN,aAAa,IAAI;IAClCO,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,eAAe,GAAIC,KAAa,IAAK;IACzCd,QAAQ,CAACc,KAAK,CAAC;EACjB,CAAC;EAED,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DnB,OAAA;MAAKkB,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEnB,OAAA;QAAKkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnB,OAAA;UAAKkB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CnB,OAAA;YAAKkB,SAAS,EAAC;UAAmC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzDvB,OAAA;YAAMkB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,KAAI;UAAO;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACNvB,OAAA;UAAKkB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBnB,OAAA;YAAKkB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAAC,MAAI,EAACR,UAAU,CAACI,EAAE;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5DvB,OAAA;YAAKkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,YAAO,EAACR,UAAU,CAACE,KAAK;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtEvB,OAAA;YAAKkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,GAAC,EAACR,UAAU,CAACG,QAAQ,IAAI,CAAC,EAAC,QAAM;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCnB,OAAA;QAAKkB,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBAEvDnB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,eAAe,CAAE;UAAAG,QAAA,gBAEhDnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,aAAa,CAAE;UAAAG,QAAA,gBAE9CnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eAGNvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eAGNvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,eAAe,CAAE;UAAAG,QAAA,gBAEhDnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAGNvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,cAAc,CAAE;UAAAG,QAAA,gBAE/CnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,wGAAwG;UAClHO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,kBAAkB,CAAE;UAAAG,QAAA,gBAEnDnB,OAAA;YAAKkB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFnB,OAAA;cAAMkB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEnB,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEnB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,OAAO,CAAE;UAAAG,QAAA,gBAExCnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,eAAe,CAAE;UAAAG,QAAA,gBAEhDnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNvB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CAlOID,YAAsB;EAAA,QACTL,WAAW,EACXC,aAAa,EAC4CC,SAAS;AAAA;AAAA4B,EAAA,GAH/EzB,YAAsB;AAoO5B,eAAeA,YAAY;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}