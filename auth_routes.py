"""
Rotas de autenticação simples para Supabase
Substitui Firebase Auth
"""

from flask import Blueprint, request, jsonify, session, render_template
import hashlib
import secrets
import uuid
import jwt
import os
from datetime import datetime, timezone
from game.new_models import criar_jogador, get_jogador_por_email, get_jogador

auth_bp = Blueprint('auth', __name__)

# Chave secreta para JWT (mesma do react_api_routes.py)
JWT_SECRET = os.environ.get('SECRET_KEY', 'shack-game-secret-key-2024-csrf-protection-enabled')

def generate_jwt_token(user_data):
    """Gera token JWT para o usuário"""
    from datetime import timedelta

    payload = {
        'uid': user_data['uid'],
        'nick': user_data.get('nick', ''),
        'exp': datetime.now(timezone.utc) + timedelta(hours=24)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

@auth_bp.route('/auth')
def auth_page():
    """Página de login/registro"""
    return render_template('auth.html')

def hash_password(password: str) -> str:
    """Hash da senha com salt"""
    salt = secrets.token_hex(16)
    password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
    return f"{salt}:{password_hash.hex()}"

def verify_password(password: str, hashed: str) -> bool:
    """Verifica senha hashada"""
    try:
        salt, password_hash = hashed.split(':')
        expected_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return password_hash == expected_hash.hex()
    except:
        return False

def generate_token(user_data) -> str:
    """Gera token JWT para o usuário"""
    if isinstance(user_data, str):
        # Se receber apenas UID, buscar dados do usuário
        user = get_jogador(user_data)
        if user:
            user_data = user
        else:
            user_data = {'uid': user_data, 'nick': ''}

    return generate_jwt_token(user_data)

@auth_bp.route('/api/auth/register', methods=['POST'])
def register():
    """Registro de usuário"""
    try:
        print("[DEBUG] Iniciando registro de usuário...")
        
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        nick = data.get('nick')
        
        print(f"[DEBUG] Dados recebidos - Email: {email}, Nick: {nick}")
        
        if not email or not password or not nick:
            print("[DEBUG] Dados obrigatórios faltando")
            return jsonify({"sucesso": False, "mensagem": "Email, senha e nick são obrigatórios"}), 400
        
        # Verifica se email já existe
        print("[DEBUG] Verificando se email já existe...")
        existing_user = get_jogador_por_email(email)
        if existing_user:
            print(f"[DEBUG] Email {email} já existe")
            return jsonify({"sucesso": False, "mensagem": "Email já cadastrado"}), 400
        
        # Cria novo usuário
        print("[DEBUG] Criando novo usuário...")
        uid = str(uuid.uuid4())
        password_hash = hash_password(password)
        
        # Gera IP aleatório para o jogador
        import random
        ip = f"192.168.{random.randint(1,254)}.{random.randint(1,254)}"
        
        print(f"[DEBUG] Tentando criar jogador com UID: {uid}")
        result = criar_jogador(uid, nick, email)
        
        print(f"[DEBUG] Resultado do criar_jogador: {result}")
        
        if result['sucesso']:
            # Atualiza o jogador com o hash da senha e IP personalizado
            from game.new_models import atualizar_jogador
            update_result = atualizar_jogador(uid, {
                'password_hash': password_hash,
                'ip': ip
            })
            print(f"[DEBUG] Resultado da atualização da senha: {update_result}")
            
            # Gera token JWT e salva na sessão
            user_data = {
                "uid": uid,
                "nick": nick,
                "email": email
            }
            token = generate_token(user_data)
            session.permanent = True  # Torna a sessão permanente
            session['user_uid'] = uid
            session['user_token'] = token
            
            print(f"[DEBUG] Usuário criado com sucesso! UID: {uid}")
            
            return jsonify({
                "sucesso": True,
                "user": {
                    "uid": uid,
                    "email": email,
                    "nick": nick,
                    "ip": ip
                },
                "token": token
            })
        else:
            print(f"[DEBUG] Falha ao criar usuário: {result}")
            return jsonify({"sucesso": False, "mensagem": f"Erro ao criar usuário: {result.get('erro', 'Erro desconhecido')}"}), 500
            
    except Exception as e:
        print(f"[DEBUG] Exceção no registro: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@auth_bp.route('/api/auth/login', methods=['POST'])  
def login():
    """Login de usuário"""
    try:
        print("[DEBUG] Iniciando processo de login...")
        
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        print(f"[DEBUG] Tentativa de login para email: {email}")
        
        if not email or not password:
            return jsonify({"sucesso": False, "mensagem": "Email e senha são obrigatórios"}), 400
        
        # Busca usuário por email
        user = get_jogador_por_email(email)
        if not user:
            print(f"[DEBUG] Usuário não encontrado: {email}")
            return jsonify({"sucesso": False, "mensagem": "Email ou senha incorretos"}), 401
        
        print(f"[DEBUG] Usuário encontrado: {user.get('nick', 'Sem nick')}")
        
        # Verifica senha
        if not verify_password(password, user.get('password_hash', '')):
            print("[DEBUG] Senha incorreta")
            return jsonify({"sucesso": False, "mensagem": "Email ou senha incorretos"}), 401
        
        print("[DEBUG] Senha correta, criando sessão...")
        
        # Gera token JWT e salva na sessão
        token = generate_token(user)
        session.permanent = True  # Torna a sessão permanente
        session['user_uid'] = user['uid']
        session['user_token'] = token
        
        print(f"[DEBUG] Sessão criada: user_uid={user['uid']}")
        print(f"[DEBUG] Sessão após login: {dict(session)}")
        
        return jsonify({
            "sucesso": True,
            "user": {
                "uid": user['uid'],
                "email": user['email'],
                "nick": user['nick'],
                "ip": user['ip']
            },
            "token": token
        })
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@auth_bp.route('/api/auth/auto-login', methods=['GET'])
def auto_login():
    """Endpoint para login automático de teste"""
    try:
        # Tenta encontrar ou criar usuário de teste
        email = '<EMAIL>'
        password = '123456'
        nick = 'TesteUser'
        
        # Primeiro tenta buscar o usuário
        user = get_jogador_por_email(email)
        
        if not user:
            print("[DEBUG] Usuário teste não existe, criando...")
            # Cria usuário de teste
            password_hash = hash_password(password)
            uid = str(uuid.uuid4())
            
            user_data = {
                'uid': uid,
                'nick': nick,
                'email': email,
                'password_hash': password_hash,
                'ip': request.remote_addr or '127.0.0.1',
                'dinheiro': 1000,
                'shack': 100,
                'cpu': 1,
                'firewall': 1,
                'antivirus': 1,
                'malware_kit': 1,
                'bruteforce': 1,
                'bankguard': 1,
                'proxyvpn': 1,
                'nivel': 1,
                'xp': 0,
                'nivel_mineradora': 1,
                'habilidades_adquiridas': {},
                'historico': [],
                'log': [],
                'is_admin': False,
                'is_active': True
            }
            
            result = criar_jogador(user_data)
            if not result['sucesso']:
                return jsonify({
                    "sucesso": False,
                    "mensagem": f"Erro ao criar usuário: {result.get('mensagem', 'Erro desconhecido')}"
                }), 500
            
            user = get_jogador_por_email(email)
        
        # Faz login automático
        token = generate_token(user['uid'])
        session.permanent = True  # Torna a sessão permanente
        session['user_uid'] = user['uid']
        session['user_token'] = token
        
        print(f"[DEBUG] Login automático realizado: {user['uid']}")
        print(f"[DEBUG] Sessão após auto-login: {dict(session)}")
        
        return jsonify({
            "sucesso": True,
            "mensagem": "Login automático realizado",
            "user": {
                "uid": user['uid'],
                "nick": user['nick'],
                "email": user['email']
            },
            "session": dict(session)
        })
        
    except Exception as e:
        print(f"[DEBUG] Erro no auto-login: {e}")
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro interno: {str(e)}"
        }), 500

@auth_bp.route('/api/auth/test-create', methods=['POST'])
def test_create():
    """Endpoint de teste para criar usuário"""
    try:
        data = {
            'email': '<EMAIL>',
            'password': '123456',
            'nick': 'TestUser'
        }
        
        # Tenta criar usuário
        password_hash = hash_password(data['password'])
        uid = str(uuid.uuid4())
        
        from game.new_models import criar_jogador
        user_data = {
            'uid': uid,
            'nick': data['nick'],
            'email': data['email'],
            'password_hash': password_hash,
            'ip': request.remote_addr or '127.0.0.1',
            'dinheiro': 1000,
            'shack': 100,
            'cpu': 1,
            'firewall': 1,
            'antivirus': 1,
            'malware_kit': 1,
            'bruteforce': 1,
            'bankguard': 1,
            'proxyvpn': 1,
            'nivel': 1,
            'xp': 0,
            'nivel_mineradora': 1,
            'habilidades_adquiridas': {},
            'historico': [],
            'log': [],
            'is_admin': False,
            'is_active': True
        }
        
        result = criar_jogador(user_data)
        
        return jsonify({
            "sucesso": True,
            "result": result,
            "uid_criado": uid
        })
        
    except Exception as e:
        return jsonify({
            "sucesso": False,
            "erro": str(e)
        }), 500

@auth_bp.route('/api/auth/session-test', methods=['GET'])
def session_test():
    """Endpoint para testar sessão atual"""
    print(f"[DEBUG] Teste de sessão - Conteúdo: {dict(session)}")
    return jsonify({
        "sucesso": True,
        "session": dict(session),
        "user_uid": session.get('user_uid'),
        "user_token": session.get('user_token')
    })

@auth_bp.route('/test-login-and-data')
def test_login_and_data():
    """Testa login e carregamento de dados em uma única página"""
    try:
        # Faz auto-login
        email = '<EMAIL>'
        user = get_jogador_por_email(email)
        
        if user:
            # Faz login automático
            token = generate_token(user['uid'])
            session.permanent = True
            session['user_uid'] = user['uid']
            session['user_token'] = token
            
            print(f"[DEBUG] Login realizado: {user['uid']}")
            
            # Tenta carregar dados do jogador usando o mesmo sistema das rotas
            from game.routes import token_required
            from game import new_models as models
            
            try:
                jogador = models.get_jogador(user['uid'])
                print(f"[DEBUG] Dados do jogador carregados: {jogador.get('nick', 'Sem nick')}")
                
                return f"""
                <h1>Teste de Login e Dados - SUCESSO!</h1>
                <p><strong>Sessão:</strong> {dict(session)}</p>
                <p><strong>Usuário:</strong> {user['nick']} ({user['email']})</p>
                <p><strong>Dados carregados:</strong> {jogador is not None}</p>
                <p><strong>Shack:</strong> {jogador.get('shack', 'N/A')}</p>
                <p><strong>Dinheiro:</strong> {jogador.get('dinheiro', 'N/A')}</p>
                <p><strong>Nível:</strong> {jogador.get('nivel', 'N/A')}</p>
                <br>
                <p><strong>SISTEMA FUNCIONANDO!</strong></p>
                <p>O erro 'get_usuario_por_uid' foi corrigido!</p>
                <p>A autenticação e carregamento de dados estão funcionais!</p>
                <br>
                <a href="/" style="padding: 10px; background: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">▶️ Ir para página principal</a>
                <br><br>
                <p><em>Nota: Se a página principal ainda mostrar erro 401, é um problema de compartilhamento de sessão entre abas.</em></p>
                """
                
            except Exception as e:
                return f"""
                <h1>Erro ao carregar dados</h1>
                <p><strong>Erro:</strong> {str(e)}</p>
                <p><strong>Sessão:</strong> {dict(session)}</p>
                """
        else:
            return "<h1>Usuário teste não encontrado</h1>"
            
    except Exception as e:
        return f"<h1>Erro: {str(e)}</h1>"

@auth_bp.route('/api/auth/logout', methods=['POST'])
def logout():
    """Logout do usuário"""
    session.clear()
    return jsonify({"sucesso": True, "mensagem": "Logout realizado com sucesso"})

@auth_bp.route('/api/auth/check', methods=['GET'])
def check_auth():
    """Verifica se o usuário está autenticado"""
    try:
        user_uid = session.get('user_uid')
        if not user_uid:
            return jsonify({"sucesso": False, "mensagem": "Não autenticado"}), 401

        # Buscar dados do usuário
        jogador = get_jogador(user_uid)
        if not jogador:
            session.clear()  # Limpar sessão inválida
            return jsonify({"sucesso": False, "mensagem": "Usuário não encontrado"}), 401

        return jsonify({
            "sucesso": True,
            "user": {
                "uid": user_uid,
                "nick": jogador.get('nick'),
                "email": jogador.get('email')
            }
        })
    except Exception as e:
        print(f"[ERROR] Erro na verificação de autenticação: {str(e)}")
        return jsonify({"sucesso": False, "mensagem": "Erro interno"}), 500

@auth_bp.route('/api/auth/me', methods=['GET'])
def me():
    """Retorna dados do usuário logado"""
    try:
        user_uid = session.get('user_uid')
        if not user_uid:
            return jsonify({"sucesso": False, "mensagem": "Não autenticado"}), 401
        
        user = get_jogador(user_uid)
        if not user:
            return jsonify({"sucesso": False, "mensagem": "Usuário não encontrado"}), 404
        
        return jsonify({
            "sucesso": True,
            "user": {
                "uid": user['uid'],
                "email": user['email'],
                "nick": user['nick'],
                "ip": user['ip']
            }
        })
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500
