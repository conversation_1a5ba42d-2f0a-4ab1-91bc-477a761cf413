{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleLoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleLoginPage = () => {\n  _s();\n  const {\n    isLoading,\n    error,\n    login,\n    register,\n    simulateLogin,\n    isAuthenticated,\n    user,\n    clearError\n  } = useSimpleAuth();\n  const [isRegisterMode, setIsRegisterMode] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    nick: ''\n  });\n  console.log('SimpleLoginPage - Estado:', {\n    isLoading,\n    isAuthenticated,\n    user: !!user\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Limpar erro quando usuário digita\n    if (error) clearError();\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    console.log('[SimpleLoginPage] Formulário enviado:', {\n      isRegisterMode,\n      formData\n    });\n    if (isRegisterMode) {\n      var _formData$nick, _formData$email, _formData$password;\n      // Validação para registro\n      if (!((_formData$nick = formData.nick) !== null && _formData$nick !== void 0 && _formData$nick.trim())) {\n        console.error('[SimpleLoginPage] Nome do hacker é obrigatório');\n        return;\n      }\n      if (!((_formData$email = formData.email) !== null && _formData$email !== void 0 && _formData$email.trim())) {\n        console.error('[SimpleLoginPage] Email é obrigatório');\n        return;\n      }\n      if (!((_formData$password = formData.password) !== null && _formData$password !== void 0 && _formData$password.trim())) {\n        console.error('[SimpleLoginPage] Senha é obrigatória');\n        return;\n      }\n      console.log('[SimpleLoginPage] Chamando register...');\n      await register({\n        email: formData.email.trim(),\n        password: formData.password,\n        nick: formData.nick.trim()\n      });\n    } else {\n      var _formData$email2, _formData$password2;\n      // Validação para login\n      if (!((_formData$email2 = formData.email) !== null && _formData$email2 !== void 0 && _formData$email2.trim())) {\n        console.error('[SimpleLoginPage] Email é obrigatório');\n        return;\n      }\n      if (!((_formData$password2 = formData.password) !== null && _formData$password2 !== void 0 && _formData$password2.trim())) {\n        console.error('[SimpleLoginPage] Senha é obrigatória');\n        return;\n      }\n      console.log('[SimpleLoginPage] Chamando login...');\n      await login({\n        email: formData.email.trim(),\n        password: formData.password\n      });\n    }\n  };\n  const toggleMode = () => {\n    setIsRegisterMode(!isRegisterMode);\n    setFormData({\n      email: '',\n      password: '',\n      nick: ''\n    });\n    clearError();\n  };\n\n  // Se já está autenticado, mostrar sucesso e aguardar carregamento\n  if (isAuthenticated && user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-green-400 mb-4\",\n          children: \"Login Realizado!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-300 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Bem-vindo, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: user.nick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 27\n            }, this), \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: [\"UID: \", user.uid]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: [\"Email: \", user.email]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-green-400 mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-green-300\",\n            children: \"\\uD83C\\uDFAE Carregando o jogo...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-6xl mb-4\",\n        children: \"\\uD83D\\uDD12\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold mb-2\",\n        children: \"SHACK\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-400\",\n        children: \"Simulador de Hacking Online\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 px-6 py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800/50 rounded-2xl p-6 border border-gray-700/50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsRegisterMode(false),\n            className: `flex-1 py-2 px-4 rounded-l-lg font-semibold transition-colors ${!isRegisterMode ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsRegisterMode(true),\n            className: `flex-1 py-2 px-4 rounded-r-lg font-semibold transition-colors ${isRegisterMode ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,\n            children: \"Registrar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-300 text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-4\",\n          children: [isRegisterMode && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Nome do Hacker\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"nick\",\n              value: formData.nick,\n              onChange: handleInputChange,\n              placeholder: \"Digite seu nick\",\n              maxLength: 20,\n              disabled: isLoading,\n              className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleInputChange,\n              placeholder: \"<EMAIL>\",\n              disabled: isLoading,\n              className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Senha\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              name: \"password\",\n              value: formData.password,\n              onChange: handleInputChange,\n              placeholder: \"Digite sua senha\",\n              disabled: isLoading,\n              className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading,\n            className: \"w-full mt-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isRegisterMode ? 'Criando conta...' : 'Entrando...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this) : isRegisterMode ? 'Criar Conta' : 'Entrar'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold mb-2 flex items-center\",\n          children: \"\\uD83E\\uDDEA Teste R\\xE1pido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-300 mb-4\",\n          children: \"Para testar rapidamente sem preencher formul\\xE1rio:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: simulateLogin,\n          disabled: isLoading,\n          className: \"w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors\",\n          children: \"\\u26A1 Login Instant\\xE2neo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold mb-2 flex items-center\",\n          children: \"\\uD83C\\uDFAE Sobre o SHACK\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"text-sm text-gray-300 space-y-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Simulador de hacking online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Fa\\xE7a upgrades nos seus aplicativos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Invada outros jogadores\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Chat global em tempo real\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Sistema de ranking competitivo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 py-2 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500\",\n        children: \"SHACK v2.0 - Simulador de Hacking Online\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleLoginPage, \"roLM9JythJepd9gq3OU2o3qxVa4=\", false, function () {\n  return [useSimpleAuth];\n});\n_c = SimpleLoginPage;\nexport default SimpleLoginPage;\nvar _c;\n$RefreshReg$(_c, \"SimpleLoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useSimpleAuth", "jsxDEV", "_jsxDEV", "SimpleLoginPage", "_s", "isLoading", "error", "login", "register", "simulateLogin", "isAuthenticated", "user", "clearError", "isRegisterMode", "setIsRegisterMode", "formData", "setFormData", "email", "password", "nick", "console", "log", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "_formData$nick", "_formData$email", "_formData$password", "trim", "_formData$email2", "_formData$password2", "toggleMode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "uid", "onClick", "onSubmit", "type", "onChange", "placeholder", "max<PERSON><PERSON><PERSON>", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleLoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\n\nconst SimpleLoginPage: React.FC = () => {\n  const { isLoading, error, login, register, simulateLogin, isAuthenticated, user, clearError } = useSimpleAuth();\n\n  const [isRegisterMode, setIsRegisterMode] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    nick: ''\n  });\n\n  console.log('SimpleLoginPage - Estado:', { isLoading, isAuthenticated, user: !!user });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Limpar erro quando usuário digita\n    if (error) clearError();\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    console.log('[SimpleLoginPage] Formulário enviado:', { isRegisterMode, formData });\n\n    if (isRegisterMode) {\n      // Validação para registro\n      if (!formData.nick?.trim()) {\n        console.error('[SimpleLoginPage] Nome do hacker é obrigatório');\n        return;\n      }\n      if (!formData.email?.trim()) {\n        console.error('[SimpleLoginPage] Email é obrigatório');\n        return;\n      }\n      if (!formData.password?.trim()) {\n        console.error('[SimpleLoginPage] Senha é obrigatória');\n        return;\n      }\n\n      console.log('[SimpleLoginPage] Chamando register...');\n      await register({\n        email: formData.email.trim(),\n        password: formData.password,\n        nick: formData.nick.trim()\n      });\n    } else {\n      // Validação para login\n      if (!formData.email?.trim()) {\n        console.error('[SimpleLoginPage] Email é obrigatório');\n        return;\n      }\n      if (!formData.password?.trim()) {\n        console.error('[SimpleLoginPage] Senha é obrigatória');\n        return;\n      }\n\n      console.log('[SimpleLoginPage] Chamando login...');\n      await login({\n        email: formData.email.trim(),\n        password: formData.password\n      });\n    }\n  };\n\n  const toggleMode = () => {\n    setIsRegisterMode(!isRegisterMode);\n    setFormData({ email: '', password: '', nick: '' });\n    clearError();\n  };\n\n  // Se já está autenticado, mostrar sucesso e aguardar carregamento\n  if (isAuthenticated && user) {\n    return (\n      <div className=\"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">✅</div>\n          <h1 className=\"text-2xl font-bold text-green-400 mb-4\">\n            Login Realizado!\n          </h1>\n          <div className=\"text-gray-300 mb-6\">\n            <p>Bem-vindo, <strong>{user.nick}</strong>!</p>\n            <p className=\"text-sm\">UID: {user.uid}</p>\n            <p className=\"text-sm\">Email: {user.email}</p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-green-400 mx-auto mb-2\"></div>\n            <p className=\"text-sm text-green-300\">\n              🎮 Carregando o jogo...\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"p-6 text-center\">\n        <div className=\"text-6xl mb-4\">🔒</div>\n        <h1 className=\"text-2xl font-bold mb-2\">SHACK</h1>\n        <p className=\"text-sm text-gray-400\">Simulador de Hacking Online</p>\n      </div>\n\n      {/* Formulário */}\n      <div className=\"flex-1 px-6 py-4\">\n        <div className=\"bg-gray-800/50 rounded-2xl p-6 border border-gray-700/50\">\n          <div className=\"flex mb-6\">\n            <button\n              onClick={() => setIsRegisterMode(false)}\n              className={`flex-1 py-2 px-4 rounded-l-lg font-semibold transition-colors ${\n                !isRegisterMode\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n              }`}\n            >\n              Login\n            </button>\n            <button\n              onClick={() => setIsRegisterMode(true)}\n              className={`flex-1 py-2 px-4 rounded-r-lg font-semibold transition-colors ${\n                isRegisterMode\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n              }`}\n            >\n              Registrar\n            </button>\n          </div>\n\n          {/* Erro */}\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg\">\n              <p className=\"text-red-300 text-sm\">{error}</p>\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {isRegisterMode && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Nome do Hacker\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"nick\"\n                  value={formData.nick}\n                  onChange={handleInputChange}\n                  placeholder=\"Digite seu nick\"\n                  maxLength={20}\n                  disabled={isLoading}\n                  className=\"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n                />\n              </div>\n            )}\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Email\n              </label>\n              <input\n                type=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                placeholder=\"<EMAIL>\"\n                disabled={isLoading}\n                className=\"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Senha\n              </label>\n              <input\n                type=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                placeholder=\"Digite sua senha\"\n                disabled={isLoading}\n                className=\"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50\"\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full mt-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center space-x-2\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  <span>{isRegisterMode ? 'Criando conta...' : 'Entrando...'}</span>\n                </div>\n              ) : (\n                isRegisterMode ? 'Criar Conta' : 'Entrar'\n              )}\n            </button>\n          </form>\n        </div>\n\n        {/* Teste Rápido */}\n        <div className=\"mt-6 bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\">\n          <h3 className=\"font-semibold mb-2 flex items-center\">\n            🧪 Teste Rápido\n          </h3>\n          <p className=\"text-sm text-gray-300 mb-4\">\n            Para testar rapidamente sem preencher formulário:\n          </p>\n          <button\n            onClick={simulateLogin}\n            disabled={isLoading}\n            className=\"w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors\"\n          >\n            ⚡ Login Instantâneo\n          </button>\n        </div>\n\n        {/* Informações do jogo */}\n        <div className=\"mt-6 bg-gray-800/30 rounded-xl p-4 border border-gray-700/30\">\n          <h3 className=\"font-semibold mb-2 flex items-center\">\n            🎮 Sobre o SHACK\n          </h3>\n          <ul className=\"text-sm text-gray-300 space-y-1\">\n            <li>• Simulador de hacking online</li>\n            <li>• Faça upgrades nos seus aplicativos</li>\n            <li>• Invada outros jogadores</li>\n            <li>• Chat global em tempo real</li>\n            <li>• Sistema de ranking competitivo</li>\n          </ul>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"px-4 py-2 text-center\">\n        <p className=\"text-xs text-gray-500\">\n          SHACK v2.0 - Simulador de Hacking Online\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleLoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC,SAAS;IAAEC,KAAK;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,aAAa;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGZ,aAAa,CAAC,CAAC;EAE/G,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;EAEFC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;IAAEhB,SAAS;IAAEK,eAAe;IAAEC,IAAI,EAAE,CAAC,CAACA;EAAK,CAAC,CAAC;EAEtF,MAAMW,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCV,WAAW,CAACW,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAInB,KAAK,EAAEM,UAAU,CAAC,CAAC;EACzB,CAAC;EAED,MAAMgB,YAAY,GAAG,MAAOL,CAAkB,IAAK;IACjDA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBT,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;MAAER,cAAc;MAAEE;IAAS,CAAC,CAAC;IAElF,IAAIF,cAAc,EAAE;MAAA,IAAAiB,cAAA,EAAAC,eAAA,EAAAC,kBAAA;MAClB;MACA,IAAI,GAAAF,cAAA,GAACf,QAAQ,CAACI,IAAI,cAAAW,cAAA,eAAbA,cAAA,CAAeG,IAAI,CAAC,CAAC,GAAE;QAC1Bb,OAAO,CAACd,KAAK,CAAC,gDAAgD,CAAC;QAC/D;MACF;MACA,IAAI,GAAAyB,eAAA,GAAChB,QAAQ,CAACE,KAAK,cAAAc,eAAA,eAAdA,eAAA,CAAgBE,IAAI,CAAC,CAAC,GAAE;QAC3Bb,OAAO,CAACd,KAAK,CAAC,uCAAuC,CAAC;QACtD;MACF;MACA,IAAI,GAAA0B,kBAAA,GAACjB,QAAQ,CAACG,QAAQ,cAAAc,kBAAA,eAAjBA,kBAAA,CAAmBC,IAAI,CAAC,CAAC,GAAE;QAC9Bb,OAAO,CAACd,KAAK,CAAC,uCAAuC,CAAC;QACtD;MACF;MAEAc,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMb,QAAQ,CAAC;QACbS,KAAK,EAAEF,QAAQ,CAACE,KAAK,CAACgB,IAAI,CAAC,CAAC;QAC5Bf,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BC,IAAI,EAAEJ,QAAQ,CAACI,IAAI,CAACc,IAAI,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,MAAM;MAAA,IAAAC,gBAAA,EAAAC,mBAAA;MACL;MACA,IAAI,GAAAD,gBAAA,GAACnB,QAAQ,CAACE,KAAK,cAAAiB,gBAAA,eAAdA,gBAAA,CAAgBD,IAAI,CAAC,CAAC,GAAE;QAC3Bb,OAAO,CAACd,KAAK,CAAC,uCAAuC,CAAC;QACtD;MACF;MACA,IAAI,GAAA6B,mBAAA,GAACpB,QAAQ,CAACG,QAAQ,cAAAiB,mBAAA,eAAjBA,mBAAA,CAAmBF,IAAI,CAAC,CAAC,GAAE;QAC9Bb,OAAO,CAACd,KAAK,CAAC,uCAAuC,CAAC;QACtD;MACF;MAEAc,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,MAAMd,KAAK,CAAC;QACVU,KAAK,EAAEF,QAAQ,CAACE,KAAK,CAACgB,IAAI,CAAC,CAAC;QAC5Bf,QAAQ,EAAEH,QAAQ,CAACG;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMkB,UAAU,GAAGA,CAAA,KAAM;IACvBtB,iBAAiB,CAAC,CAACD,cAAc,CAAC;IAClCG,WAAW,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC;IAClDP,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA,IAAIF,eAAe,IAAIC,IAAI,EAAE;IAC3B,oBACET,OAAA;MAAKmC,SAAS,EAAC,mHAAmH;MAAAC,QAAA,eAChIpC,OAAA;QAAKmC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpC,OAAA;UAAKmC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtCxC,OAAA;UAAImC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxC,OAAA;UAAKmC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCpC,OAAA;YAAAoC,QAAA,GAAG,aAAW,eAAApC,OAAA;cAAAoC,QAAA,EAAS3B,IAAI,CAACQ;YAAI;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/CxC,OAAA;YAAGmC,SAAS,EAAC,SAAS;YAAAC,QAAA,GAAC,OAAK,EAAC3B,IAAI,CAACgC,GAAG;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1CxC,OAAA;YAAGmC,SAAS,EAAC,SAAS;YAAAC,QAAA,GAAC,SAAO,EAAC3B,IAAI,CAACM,KAAK;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNxC,OAAA;UAAKmC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BpC,OAAA;YAAKmC,SAAS,EAAC;UAA4E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClGxC,OAAA;YAAGmC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAEtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExC,OAAA;IAAKmC,SAAS,EAAC,uFAAuF;IAAAC,QAAA,gBAEpGpC,OAAA;MAAKmC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BpC,OAAA;QAAKmC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvCxC,OAAA;QAAImC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClDxC,OAAA;QAAGmC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAGNxC,OAAA;MAAKmC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BpC,OAAA;QAAKmC,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEpC,OAAA;UAAKmC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpC,OAAA;YACE0C,OAAO,EAAEA,CAAA,KAAM9B,iBAAiB,CAAC,KAAK,CAAE;YACxCuB,SAAS,EAAE,iEACT,CAACxB,cAAc,GACX,wBAAwB,GACxB,6CAA6C,EAChD;YAAAyB,QAAA,EACJ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxC,OAAA;YACE0C,OAAO,EAAEA,CAAA,KAAM9B,iBAAiB,CAAC,IAAI,CAAE;YACvCuB,SAAS,EAAE,iEACTxB,cAAc,GACV,wBAAwB,GACxB,6CAA6C,EAChD;YAAAyB,QAAA,EACJ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLpC,KAAK,iBACJJ,OAAA;UAAKmC,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eACtEpC,OAAA;YAAGmC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEhC;UAAK;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACN,eAEDxC,OAAA;UAAM2C,QAAQ,EAAEjB,YAAa;UAACS,SAAS,EAAC,WAAW;UAAAC,QAAA,GAChDzB,cAAc,iBACbX,OAAA;YAAAoC,QAAA,gBACEpC,OAAA;cAAOmC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxC,OAAA;cACE4C,IAAI,EAAC,MAAM;cACXtB,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEV,QAAQ,CAACI,IAAK;cACrB4B,QAAQ,EAAEzB,iBAAkB;cAC5B0B,WAAW,EAAC,iBAAiB;cAC7BC,SAAS,EAAE,EAAG;cACdC,QAAQ,EAAE7C,SAAU;cACpBgC,SAAS,EAAC;YAAuJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAEDxC,OAAA;YAAAoC,QAAA,gBACEpC,OAAA;cAAOmC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxC,OAAA;cACE4C,IAAI,EAAC,OAAO;cACZtB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEV,QAAQ,CAACE,KAAM;cACtB8B,QAAQ,EAAEzB,iBAAkB;cAC5B0B,WAAW,EAAC,eAAe;cAC3BE,QAAQ,EAAE7C,SAAU;cACpBgC,SAAS,EAAC;YAAuJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxC,OAAA;YAAAoC,QAAA,gBACEpC,OAAA;cAAOmC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxC,OAAA;cACE4C,IAAI,EAAC,UAAU;cACftB,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEV,QAAQ,CAACG,QAAS;cACzB6B,QAAQ,EAAEzB,iBAAkB;cAC5B0B,WAAW,EAAC,kBAAkB;cAC9BE,QAAQ,EAAE7C,SAAU;cACpBgC,SAAS,EAAC;YAAuJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxC,OAAA;YACE4C,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAE7C,SAAU;YACpBgC,SAAS,EAAC,2HAA2H;YAAAC,QAAA,EAEpIjC,SAAS,gBACRH,OAAA;cAAKmC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDpC,OAAA;gBAAKmC,SAAS,EAAC;cAA2D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjFxC,OAAA;gBAAAoC,QAAA,EAAOzB,cAAc,GAAG,kBAAkB,GAAG;cAAa;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,GAEN7B,cAAc,GAAG,aAAa,GAAG;UAClC;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNxC,OAAA;QAAKmC,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3EpC,OAAA;UAAImC,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxC,OAAA;UAAGmC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJxC,OAAA;UACE0C,OAAO,EAAEnC,aAAc;UACvByC,QAAQ,EAAE7C,SAAU;UACpBgC,SAAS,EAAC,0HAA0H;UAAAC,QAAA,EACrI;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNxC,OAAA;QAAKmC,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3EpC,OAAA;UAAImC,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxC,OAAA;UAAImC,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC7CpC,OAAA;YAAAoC,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCxC,OAAA;YAAAoC,QAAA,EAAI;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7CxC,OAAA;YAAAoC,QAAA,EAAI;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClCxC,OAAA;YAAAoC,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpCxC,OAAA;YAAAoC,QAAA,EAAI;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxC,OAAA;MAAKmC,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCpC,OAAA;QAAGmC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CArPID,eAAyB;EAAA,QACmEH,aAAa;AAAA;AAAAmD,EAAA,GADzGhD,eAAyB;AAuP/B,eAAeA,eAAe;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}