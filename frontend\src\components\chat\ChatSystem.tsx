import React, { useState, useRef, useEffect } from 'react';
import { useChat } from '../../stores/chatStore';
import ChatMessage from './ChatMessage';
import LoadingSpinner from '../common/LoadingSpinner';
import {
  ChatBubbleLeftRightIcon,
  XMarkIcon,
  MinusIcon,
  PlusIcon,
  PaperAirplaneIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface ChatSystemProps {
  isOpen?: boolean;
  onToggle?: () => void;
}

const ChatSystem: React.FC<ChatSystemProps> = ({ isOpen, onToggle }) => {
  const {
    messages,
    isLoading,
    error,
    isSending,
    sendError,
    unreadCount,
    hasUnread,
    canSend,
    isMinimized,
    loadMessages,
    sendMessage,
    closeChat,
    toggleChat,
    minimizeChat,
    maximizeChat,
    markAsRead,
    clearError,
    formatDate,
  } = useChat();

  const [messageInput, setMessageInput] = useState('');
  const [lastMessageDate, setLastMessageDate] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll para a última mensagem
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages]);

  // Focar no input quando o chat abrir
  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, isMinimized]);

  // Marcar como lido quando abrir
  useEffect(() => {
    if (isOpen && hasUnread) {
      markAsRead();
    }
  }, [isOpen, hasUnread, markAsRead]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!messageInput.trim() || !canSend) return;

    const message = messageInput.trim();
    setMessageInput('');

    try {
      await sendMessage(message);
    } catch (error) {
      // Erro já está sendo tratado no store
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e as any);
    }
  };

  // Renderizar separador de data
  const renderDateSeparator = (date: string) => (
    <div className="flex items-center justify-center py-2">
      <div className="bg-bg-tertiary px-3 py-1 rounded-full">
        <span className="text-xs text-text-muted font-medium">
          {formatDate(date)}
        </span>
      </div>
    </div>
  );

  // Chat minimizado (apenas ícone)
  if (!isOpen) {
    return (
      <button
        onClick={onToggle || toggleChat}
        className={`
          fixed bottom-4 right-4 z-50 p-4 rounded-full shadow-lg transition-all duration-300
          ${hasUnread ? 'bg-accent animate-pulse' : 'bg-primary hover:bg-primary-dark'}
          text-white
        `}
      >
        <div className="relative">
          <ChatBubbleLeftRightIcon className="h-6 w-6" />
          {hasUnread && (
            <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
              {unreadCount > 99 ? '99+' : unreadCount}
            </div>
          )}
        </div>
      </button>
    );
  }

  return (
    <div className={`
      fixed bottom-4 right-4 z-50 bg-bg-secondary border border-border-color rounded-lg shadow-xl
      transition-all duration-300 transform
      ${isMinimized ? 'w-80 h-12' : 'w-80 h-96 md:w-96 md:h-[500px]'}
    `}>
      {/* Header do chat */}
      <div className="flex items-center justify-between p-3 border-b border-border-color bg-bg-tertiary rounded-t-lg">
        <div className="flex items-center space-x-2">
          <ChatBubbleLeftRightIcon className="h-5 w-5 text-accent-blue" />
          <span className="font-medium text-text-primary">Chat Global</span>
          {hasUnread && !isMinimized && (
            <div className="bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-bold">
              {unreadCount}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-1">
          <button
            onClick={isMinimized ? maximizeChat : minimizeChat}
            className="p-1 rounded hover:bg-bg-secondary transition-colors"
          >
            {isMinimized ? (
              <PlusIcon className="h-4 w-4 text-text-muted" />
            ) : (
              <MinusIcon className="h-4 w-4 text-text-muted" />
            )}
          </button>
          
          <button
            onClick={onToggle || closeChat}
            className="p-1 rounded hover:bg-bg-secondary transition-colors"
          >
            <XMarkIcon className="h-4 w-4 text-text-muted" />
          </button>
        </div>
      </div>

      {/* Conteúdo do chat (oculto quando minimizado) */}
      {!isMinimized && (
        <>
          {/* Área de mensagens */}
          <div className="flex-1 overflow-y-auto h-80 md:h-96">
            {isLoading && messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <LoadingSpinner size="md" text="Carregando mensagens..." />
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full p-4">
                <div className="text-center">
                  <ExclamationTriangleIcon className="h-8 w-8 text-red-400 mx-auto mb-2" />
                  <p className="text-sm text-red-400 mb-2">{error}</p>
                  <button
                    onClick={() => {
                      clearError();
                      loadMessages();
                    }}
                    className="text-xs text-accent-blue hover:text-primary-light"
                  >
                    Tentar novamente
                  </button>
                </div>
              </div>
            ) : messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-text-muted">
                  <ChatBubbleLeftRightIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Nenhuma mensagem ainda</p>
                  <p className="text-xs">Seja o primeiro a conversar!</p>
                </div>
              </div>
            ) : (
              <div className="space-y-1">
                {messages.map((message, index) => {
                  const messageDate = message.timestamp.split('T')[0];
                  const showDateSeparator = messageDate !== lastMessageDate;
                  
                  if (showDateSeparator) {
                    setLastMessageDate(messageDate);
                  }

                  return (
                    <React.Fragment key={message.id}>
                      {showDateSeparator && renderDateSeparator(message.timestamp)}
                      <ChatMessage
                        message={message}
                        showAvatar={true}
                        showTimestamp={true}
                      />
                    </React.Fragment>
                  );
                })}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>

          {/* Área de input */}
          <div className="p-3 border-t border-border-color">
            {sendError && (
              <div className="mb-2 p-2 bg-red-900 border border-red-500 rounded text-xs text-red-100">
                {sendError}
              </div>
            )}
            
            <form onSubmit={handleSendMessage} className="flex space-x-2">
              <input
                ref={inputRef}
                type="text"
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Digite sua mensagem..."
                disabled={!canSend}
                className="
                  flex-1 px-3 py-2 text-sm rounded-lg
                  bg-bg-primary text-text-primary
                  border border-border-color
                  focus:border-accent-blue focus:outline-none
                  disabled:opacity-50 disabled:cursor-not-allowed
                "
                maxLength={500}
              />
              
              <button
                type="submit"
                disabled={!canSend || !messageInput.trim()}
                className="
                  p-2 rounded-lg bg-accent-blue hover:bg-primary-dark
                  text-white transition-colors
                  disabled:opacity-50 disabled:cursor-not-allowed
                  flex items-center justify-center
                "
              >
                {isSending ? (
                  <LoadingSpinner size="sm" color="white" />
                ) : (
                  <PaperAirplaneIcon className="h-4 w-4" />
                )}
              </button>
            </form>
            
            <div className="flex items-center justify-between mt-2 text-xs text-text-muted">
              <span>{messageInput.length}/500</span>
              <span>Enter para enviar</span>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ChatSystem;
