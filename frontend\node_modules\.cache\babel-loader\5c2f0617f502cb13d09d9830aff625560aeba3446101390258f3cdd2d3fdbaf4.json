{"ast": null, "code": "import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { GAME_CONFIG, calculateUpgradeCost, calculateMineradoraProduction } from '../types/game';\nimport { backendService } from '../services/backendService';\nconst initialPlayerApps = {\n  ...GAME_CONFIG.INITIAL_APPS,\n  mineradora: 1 // Garantir que a mineradora está incluída\n};\nexport const useHackGameStore = create()(persist((set, get) => ({\n  // Initial state\n  player: null,\n  playerApps: initialPlayerApps,\n  isOnline: false,\n  notifications: [],\n  availableTargets: [],\n  hackHistory: [],\n  currentScreen: 'home',\n  isLoading: false,\n  error: null,\n  // Actions\n  loginPlayer: async (email, password) => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const result = await backendService.login(email, password);\n      if (result.success) {\n        // Carregar dados do jogador após login\n        const success = await get().loadPlayer();\n        return success;\n      }\n      set({\n        error: result.error || 'Erro ao fazer login',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  registerPlayer: async (nick, email, password) => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const result = await backendService.register(nick, email, password);\n      if (result.success) {\n        set({\n          isLoading: false\n        });\n        return true;\n      }\n      set({\n        error: result.error || 'Erro ao registrar jogador',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  loadPlayer: async () => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const result = await backendService.getPlayer();\n      if (result.success && result.player) {\n        const player = backendService.convertBackendPlayer(result.player);\n        const playerApps = backendService.convertBackendApps(result.player);\n        set({\n          player,\n          playerApps,\n          isOnline: true,\n          isLoading: false\n        });\n        return true;\n      }\n      set({\n        error: result.error || 'Erro ao carregar jogador',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  upgradeApp: async appId => {\n    const state = get();\n    const {\n      player,\n      playerApps\n    } = state;\n    if (!player) return false;\n    const currentLevel = playerApps[appId];\n    const upgradeCost = calculateUpgradeCost(currentLevel);\n\n    // Verificar se tem dinheiro suficiente\n    if (player.cash < upgradeCost.cash) {\n      set({\n        error: 'Dinheiro insuficiente para upgrade!'\n      });\n      return false;\n    }\n\n    // Verificar se tem shack suficiente (para upgrades nível 10+)\n    if (upgradeCost.shack > 0 && (player.shack || 0) < upgradeCost.shack) {\n      set({\n        error: 'Shack insuficiente para upgrade!'\n      });\n      return false;\n    }\n    try {\n      set({\n        isLoading: true\n      });\n\n      // Fazer upgrade no backend\n      const result = await backendService.upgradeApp(appId, 1);\n      if (result.success && result.data) {\n        // Recarregar dados do jogador para ter os valores atualizados\n        await get().loadPlayer();\n\n        // Adicionar notificação local\n        const costMessage = upgradeCost.shack > 0 ? `Custo: $${upgradeCost.cash} + ${upgradeCost.shack} shack` : `Custo: $${upgradeCost.cash}`;\n        get().addNotification({\n          type: 'upgrade',\n          title: 'Upgrade Concluído!',\n          message: `${appId} foi atualizado para nível ${currentLevel + 1}! ${costMessage}`,\n          read: false\n        });\n\n        // Verificar se subiu de nível\n        if (result.data.level_up) {\n          get().addNotification({\n            type: 'level',\n            title: 'Level Up!',\n            message: `Parabéns! Você atingiu o nível ${result.data.nivel_jogador}!`\n          });\n        }\n        set({\n          isLoading: false\n        });\n        return true;\n      }\n      set({\n        error: result.error || 'Erro ao realizar upgrade',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  collectMineradoraProduction: () => {\n    const state = get();\n    if (!state.player || !state.playerApps.mineradora) return;\n    const mineradoraLevel = state.playerApps.mineradora;\n    const production = calculateMineradoraProduction(mineradoraLevel, 1); // 1 hora de produção\n\n    if (production.cash > 0 || production.shack > 0) {\n      set(state => ({\n        player: state.player ? {\n          ...state.player,\n          cash: state.player.cash + production.cash,\n          shack: (state.player.shack || 0) + production.shack\n        } : null\n      }));\n\n      // Adicionar notificação\n      get().addNotification({\n        type: 'system',\n        title: 'Mineradora Coletada!',\n        message: `+$${production.cash}${production.shack > 0 ? ` +${production.shack} ⛏️` : ''}`\n      });\n    }\n  },\n  setCurrentScreen: screen => {\n    set({\n      currentScreen: screen\n    });\n  },\n  addNotification: notification => {\n    const newNotification = {\n      ...notification,\n      id: `notif_${Date.now()}_${Math.random()}`,\n      timestamp: new Date().toISOString()\n    };\n    set(state => ({\n      notifications: [newNotification, ...state.notifications].slice(0, 50) // Manter apenas 50 notificações\n    }));\n  },\n  markNotificationRead: id => {\n    set(state => ({\n      notifications: state.notifications.map(notif => notif.id === id ? {\n        ...notif,\n        read: true\n      } : notif)\n    }));\n  },\n  clearNotifications: () => {\n    set({\n      notifications: []\n    });\n  },\n  setError: error => {\n    set({\n      error\n    });\n    if (error) {\n      // Limpar erro após 5 segundos\n      setTimeout(() => {\n        set({\n          error: null\n        });\n      }, 5000);\n    }\n  },\n  setLoading: loading => {\n    set({\n      isLoading: loading\n    });\n  },\n  syncWithServer: async () => {\n    try {\n      set({\n        isLoading: true\n      });\n\n      // Recarregar dados do jogador\n      await get().loadPlayer();\n      set({\n        isLoading: false\n      });\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n    }\n  },\n  logout: () => {\n    localStorage.removeItem('auth_token');\n    get().reset();\n  },\n  reset: () => {\n    set({\n      player: null,\n      playerApps: initialPlayerApps,\n      isOnline: false,\n      notifications: [],\n      availableTargets: [],\n      hackHistory: [],\n      currentScreen: 'home',\n      isLoading: false,\n      error: null\n    });\n  }\n}), {\n  name: 'shack-game-storage',\n  partialize: state => ({\n    currentScreen: state.currentScreen,\n    notifications: state.notifications\n  }),\n  // Recarregar dados do servidor quando a store for hidratada\n  onRehydrateStorage: () => state => {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      // Recarregar dados do servidor em background\n      setTimeout(() => {\n        state === null || state === void 0 ? void 0 : state.syncWithServer();\n      }, 1000);\n    }\n  }\n}));", "map": {"version": 3, "names": ["create", "persist", "GAME_CONFIG", "calculateUpgradeCost", "calculateMineradoraProduction", "backendService", "initialPlayerApps", "INITIAL_APPS", "mineradora", "useHackGameStore", "set", "get", "player", "playerApps", "isOnline", "notifications", "availableTargets", "hack<PERSON><PERSON><PERSON>", "currentScreen", "isLoading", "error", "loginPlayer", "email", "password", "result", "login", "success", "loadPlayer", "message", "registerPlayer", "nick", "register", "getPlayer", "convertBackendPlayer", "convertBackendApps", "upgradeApp", "appId", "state", "currentLevel", "upgradeCost", "cash", "shack", "data", "costMessage", "addNotification", "type", "title", "read", "level_up", "nivel_jogador", "collectMineradoraProduction", "mineradoraLevel", "production", "setCurrentScreen", "screen", "notification", "newNotification", "id", "Date", "now", "Math", "random", "timestamp", "toISOString", "slice", "markNotificationRead", "map", "notif", "clearNotifications", "setError", "setTimeout", "setLoading", "loading", "syncWithServer", "logout", "localStorage", "removeItem", "reset", "name", "partialize", "onRehydrateStorage", "token", "getItem"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/hackGameStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport {\n  GAME_CONFIG,\n  getPlayerLevel,\n  getXpForNextLevel,\n  calculateUpgradeCost,\n  calculateXpReward,\n  calculateMineradoraProduction,\n  Player,\n  PlayerApps,\n  GameNotification,\n  HackTarget\n} from '../types/game';\nimport { backendService, BackendPlayer } from '../services/backendService';\n\ninterface GameState {\n  // Player data\n  player: Player | null;\n  playerApps: PlayerApps;\n  \n  // Game state\n  isOnline: boolean;\n  notifications: GameNotification[];\n  \n  // Targets and hacking\n  availableTargets: HackTarget[];\n  hackHistory: any[];\n  \n  // UI state\n  currentScreen: 'home' | 'apps' | 'scanner' | 'terminal' | 'profile' | 'settings' | 'shop' | 'ranking' | 'logs' | 'chat';\n  isLoading: boolean;\n  error: string | null;\n  \n  // Actions\n  loginPlayer: (email: string, password: string) => Promise<boolean>;\n  registerPlayer: (nick: string, email: string, password: string) => Promise<boolean>;\n  loadPlayer: () => Promise<boolean>;\n  upgradeApp: (appId: keyof PlayerApps) => Promise<boolean>;\n  collectMineradoraProduction: () => void;\n  setCurrentScreen: (screen: GameState['currentScreen']) => void;\n  addNotification: (notification: Omit<GameNotification, 'id' | 'timestamp'>) => void;\n  markNotificationRead: (id: string) => void;\n  clearNotifications: () => void;\n  setError: (error: string | null) => void;\n  setLoading: (loading: boolean) => void;\n  syncWithServer: () => Promise<void>;\n  logout: () => void;\n  reset: () => void;\n}\n\nconst initialPlayerApps: PlayerApps = {\n  ...GAME_CONFIG.INITIAL_APPS,\n  mineradora: 1, // Garantir que a mineradora está incluída\n};\n\nexport const useHackGameStore = create<GameState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      player: null,\n      playerApps: initialPlayerApps,\n      isOnline: false,\n      notifications: [],\n      availableTargets: [],\n      hackHistory: [],\n      currentScreen: 'home',\n      isLoading: false,\n      error: null,\n\n      // Actions\n      loginPlayer: async (email: string, password: string) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const result = await backendService.login(email, password);\n\n          if (result.success) {\n            // Carregar dados do jogador após login\n            const success = await get().loadPlayer();\n            return success;\n          }\n\n          set({ error: result.error || 'Erro ao fazer login', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      registerPlayer: async (nick: string, email: string, password: string) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const result = await backendService.register(nick, email, password);\n\n          if (result.success) {\n            set({ isLoading: false });\n            return true;\n          }\n\n          set({ error: result.error || 'Erro ao registrar jogador', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      loadPlayer: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const result = await backendService.getPlayer();\n\n          if (result.success && result.player) {\n            const player = backendService.convertBackendPlayer(result.player);\n            const playerApps = backendService.convertBackendApps(result.player);\n\n            set({\n              player,\n              playerApps,\n              isOnline: true,\n              isLoading: false,\n            });\n\n            return true;\n          }\n\n          set({ error: result.error || 'Erro ao carregar jogador', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      upgradeApp: async (appId: keyof PlayerApps) => {\n        const state = get();\n        const { player, playerApps } = state;\n\n        if (!player) return false;\n\n        const currentLevel = playerApps[appId];\n        const upgradeCost = calculateUpgradeCost(currentLevel);\n\n        // Verificar se tem dinheiro suficiente\n        if (player.cash < upgradeCost.cash) {\n          set({ error: 'Dinheiro insuficiente para upgrade!' });\n          return false;\n        }\n\n        // Verificar se tem shack suficiente (para upgrades nível 10+)\n        if (upgradeCost.shack > 0 && (player.shack || 0) < upgradeCost.shack) {\n          set({ error: 'Shack insuficiente para upgrade!' });\n          return false;\n        }\n\n        try {\n          set({ isLoading: true });\n\n          // Fazer upgrade no backend\n          const result = await backendService.upgradeApp(appId, 1);\n\n          if (result.success && result.data) {\n            // Recarregar dados do jogador para ter os valores atualizados\n            await get().loadPlayer();\n\n            // Adicionar notificação local\n            const costMessage = upgradeCost.shack > 0\n              ? `Custo: $${upgradeCost.cash} + ${upgradeCost.shack} shack`\n              : `Custo: $${upgradeCost.cash}`;\n\n            get().addNotification({\n              type: 'upgrade',\n              title: 'Upgrade Concluído!',\n              message: `${appId} foi atualizado para nível ${currentLevel + 1}! ${costMessage}`,\n              read: false,\n            });\n\n            // Verificar se subiu de nível\n            if (result.data.level_up) {\n              get().addNotification({\n                type: 'level',\n                title: 'Level Up!',\n                message: `Parabéns! Você atingiu o nível ${result.data.nivel_jogador}!`,\n              });\n            }\n\n            set({ isLoading: false });\n            return true;\n          }\n\n          set({ error: result.error || 'Erro ao realizar upgrade', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      collectMineradoraProduction: () => {\n        const state = get();\n        if (!state.player || !state.playerApps.mineradora) return;\n\n        const mineradoraLevel = state.playerApps.mineradora;\n        const production = calculateMineradoraProduction(mineradoraLevel, 1); // 1 hora de produção\n\n        if (production.cash > 0 || production.shack > 0) {\n          set((state) => ({\n            player: state.player ? {\n              ...state.player,\n              cash: state.player.cash + production.cash,\n              shack: (state.player.shack || 0) + production.shack,\n            } : null\n          }));\n\n          // Adicionar notificação\n          get().addNotification({\n            type: 'system',\n            title: 'Mineradora Coletada!',\n            message: `+$${production.cash}${production.shack > 0 ? ` +${production.shack} ⛏️` : ''}`,\n          });\n        }\n      },\n\n      setCurrentScreen: (screen: GameState['currentScreen']) => {\n        set({ currentScreen: screen });\n      },\n\n      addNotification: (notification: Omit<GameNotification, 'id' | 'timestamp'>) => {\n        const newNotification: GameNotification = {\n          ...notification,\n          id: `notif_${Date.now()}_${Math.random()}`,\n          timestamp: new Date().toISOString(),\n        };\n\n        set((state) => ({\n          notifications: [newNotification, ...state.notifications].slice(0, 50) // Manter apenas 50 notificações\n        }));\n      },\n\n      markNotificationRead: (id: string) => {\n        set((state) => ({\n          notifications: state.notifications.map(notif =>\n            notif.id === id ? { ...notif, read: true } : notif\n          )\n        }));\n      },\n\n      clearNotifications: () => {\n        set({ notifications: [] });\n      },\n\n      setError: (error: string | null) => {\n        set({ error });\n        if (error) {\n          // Limpar erro após 5 segundos\n          setTimeout(() => {\n            set({ error: null });\n          }, 5000);\n        }\n      },\n\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n\n      syncWithServer: async () => {\n        try {\n          set({ isLoading: true });\n\n          // Recarregar dados do jogador\n          await get().loadPlayer();\n\n          set({ isLoading: false });\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n        }\n      },\n\n      logout: () => {\n        localStorage.removeItem('auth_token');\n        get().reset();\n      },\n\n      reset: () => {\n        set({\n          player: null,\n          playerApps: initialPlayerApps,\n          isOnline: false,\n          notifications: [],\n          availableTargets: [],\n          hackHistory: [],\n          currentScreen: 'home',\n          isLoading: false,\n          error: null,\n        });\n      },\n    }),\n    {\n      name: 'shack-game-storage',\n      partialize: (state) => ({\n        currentScreen: state.currentScreen,\n        notifications: state.notifications,\n      }),\n      // Recarregar dados do servidor quando a store for hidratada\n      onRehydrateStorage: () => (state) => {\n        const token = localStorage.getItem('auth_token');\n        if (token) {\n          // Recarregar dados do servidor em background\n          setTimeout(() => {\n            state?.syncWithServer();\n          }, 1000);\n        }\n      },\n    }\n  )\n);\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SACEC,WAAW,EAGXC,oBAAoB,EAEpBC,6BAA6B,QAKxB,eAAe;AACtB,SAASC,cAAc,QAAuB,4BAA4B;AAqC1E,MAAMC,iBAA6B,GAAG;EACpC,GAAGJ,WAAW,CAACK,YAAY;EAC3BC,UAAU,EAAE,CAAC,CAAE;AACjB,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGT,MAAM,CAAY,CAAC,CACjDC,OAAO,CACL,CAACS,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAEP,iBAAiB;EAC7BQ,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE,EAAE;EACjBC,gBAAgB,EAAE,EAAE;EACpBC,WAAW,EAAE,EAAE;EACfC,aAAa,EAAE,MAAM;EACrBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEX;EACAC,WAAW,EAAE,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAK;IACtDb,GAAG,CAAC;MAAES,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMnB,cAAc,CAACoB,KAAK,CAACH,KAAK,EAAEC,QAAQ,CAAC;MAE1D,IAAIC,MAAM,CAACE,OAAO,EAAE;QAClB;QACA,MAAMA,OAAO,GAAG,MAAMf,GAAG,CAAC,CAAC,CAACgB,UAAU,CAAC,CAAC;QACxC,OAAOD,OAAO;MAChB;MAEAhB,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI,qBAAqB;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MACvE,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ,OAAO;QAAET,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAEDU,cAAc,EAAE,MAAAA,CAAOC,IAAY,EAAER,KAAa,EAAEC,QAAgB,KAAK;IACvEb,GAAG,CAAC;MAAES,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMnB,cAAc,CAAC0B,QAAQ,CAACD,IAAI,EAAER,KAAK,EAAEC,QAAQ,CAAC;MAEnE,IAAIC,MAAM,CAACE,OAAO,EAAE;QAClBhB,GAAG,CAAC;UAAES,SAAS,EAAE;QAAM,CAAC,CAAC;QACzB,OAAO,IAAI;MACb;MAEAT,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI,2BAA2B;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MAC7E,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ,OAAO;QAAET,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAEDQ,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtBjB,GAAG,CAAC;MAAES,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMnB,cAAc,CAAC2B,SAAS,CAAC,CAAC;MAE/C,IAAIR,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACZ,MAAM,EAAE;QACnC,MAAMA,MAAM,GAAGP,cAAc,CAAC4B,oBAAoB,CAACT,MAAM,CAACZ,MAAM,CAAC;QACjE,MAAMC,UAAU,GAAGR,cAAc,CAAC6B,kBAAkB,CAACV,MAAM,CAACZ,MAAM,CAAC;QAEnEF,GAAG,CAAC;UACFE,MAAM;UACNC,UAAU;UACVC,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;QACb,CAAC,CAAC;QAEF,OAAO,IAAI;MACb;MAEAT,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI,0BAA0B;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MAC5E,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ,OAAO;QAAET,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAEDgB,UAAU,EAAE,MAAOC,KAAuB,IAAK;IAC7C,MAAMC,KAAK,GAAG1B,GAAG,CAAC,CAAC;IACnB,MAAM;MAAEC,MAAM;MAAEC;IAAW,CAAC,GAAGwB,KAAK;IAEpC,IAAI,CAACzB,MAAM,EAAE,OAAO,KAAK;IAEzB,MAAM0B,YAAY,GAAGzB,UAAU,CAACuB,KAAK,CAAC;IACtC,MAAMG,WAAW,GAAGpC,oBAAoB,CAACmC,YAAY,CAAC;;IAEtD;IACA,IAAI1B,MAAM,CAAC4B,IAAI,GAAGD,WAAW,CAACC,IAAI,EAAE;MAClC9B,GAAG,CAAC;QAAEU,KAAK,EAAE;MAAsC,CAAC,CAAC;MACrD,OAAO,KAAK;IACd;;IAEA;IACA,IAAImB,WAAW,CAACE,KAAK,GAAG,CAAC,IAAI,CAAC7B,MAAM,CAAC6B,KAAK,IAAI,CAAC,IAAIF,WAAW,CAACE,KAAK,EAAE;MACpE/B,GAAG,CAAC;QAAEU,KAAK,EAAE;MAAmC,CAAC,CAAC;MAClD,OAAO,KAAK;IACd;IAEA,IAAI;MACFV,GAAG,CAAC;QAAES,SAAS,EAAE;MAAK,CAAC,CAAC;;MAExB;MACA,MAAMK,MAAM,GAAG,MAAMnB,cAAc,CAAC8B,UAAU,CAACC,KAAK,EAAE,CAAC,CAAC;MAExD,IAAIZ,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACkB,IAAI,EAAE;QACjC;QACA,MAAM/B,GAAG,CAAC,CAAC,CAACgB,UAAU,CAAC,CAAC;;QAExB;QACA,MAAMgB,WAAW,GAAGJ,WAAW,CAACE,KAAK,GAAG,CAAC,GACrC,WAAWF,WAAW,CAACC,IAAI,MAAMD,WAAW,CAACE,KAAK,QAAQ,GAC1D,WAAWF,WAAW,CAACC,IAAI,EAAE;QAEjC7B,GAAG,CAAC,CAAC,CAACiC,eAAe,CAAC;UACpBC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,oBAAoB;UAC3BlB,OAAO,EAAE,GAAGQ,KAAK,8BAA8BE,YAAY,GAAG,CAAC,KAAKK,WAAW,EAAE;UACjFI,IAAI,EAAE;QACR,CAAC,CAAC;;QAEF;QACA,IAAIvB,MAAM,CAACkB,IAAI,CAACM,QAAQ,EAAE;UACxBrC,GAAG,CAAC,CAAC,CAACiC,eAAe,CAAC;YACpBC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,WAAW;YAClBlB,OAAO,EAAE,kCAAkCJ,MAAM,CAACkB,IAAI,CAACO,aAAa;UACtE,CAAC,CAAC;QACJ;QAEAvC,GAAG,CAAC;UAAES,SAAS,EAAE;QAAM,CAAC,CAAC;QACzB,OAAO,IAAI;MACb;MAEAT,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI,0BAA0B;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MAC5E,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ,OAAO;QAAET,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAED+B,2BAA2B,EAAEA,CAAA,KAAM;IACjC,MAAMb,KAAK,GAAG1B,GAAG,CAAC,CAAC;IACnB,IAAI,CAAC0B,KAAK,CAACzB,MAAM,IAAI,CAACyB,KAAK,CAACxB,UAAU,CAACL,UAAU,EAAE;IAEnD,MAAM2C,eAAe,GAAGd,KAAK,CAACxB,UAAU,CAACL,UAAU;IACnD,MAAM4C,UAAU,GAAGhD,6BAA6B,CAAC+C,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEtE,IAAIC,UAAU,CAACZ,IAAI,GAAG,CAAC,IAAIY,UAAU,CAACX,KAAK,GAAG,CAAC,EAAE;MAC/C/B,GAAG,CAAE2B,KAAK,KAAM;QACdzB,MAAM,EAAEyB,KAAK,CAACzB,MAAM,GAAG;UACrB,GAAGyB,KAAK,CAACzB,MAAM;UACf4B,IAAI,EAAEH,KAAK,CAACzB,MAAM,CAAC4B,IAAI,GAAGY,UAAU,CAACZ,IAAI;UACzCC,KAAK,EAAE,CAACJ,KAAK,CAACzB,MAAM,CAAC6B,KAAK,IAAI,CAAC,IAAIW,UAAU,CAACX;QAChD,CAAC,GAAG;MACN,CAAC,CAAC,CAAC;;MAEH;MACA9B,GAAG,CAAC,CAAC,CAACiC,eAAe,CAAC;QACpBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,sBAAsB;QAC7BlB,OAAO,EAAE,KAAKwB,UAAU,CAACZ,IAAI,GAAGY,UAAU,CAACX,KAAK,GAAG,CAAC,GAAG,KAAKW,UAAU,CAACX,KAAK,KAAK,GAAG,EAAE;MACxF,CAAC,CAAC;IACJ;EACF,CAAC;EAEDY,gBAAgB,EAAGC,MAAkC,IAAK;IACxD5C,GAAG,CAAC;MAAEQ,aAAa,EAAEoC;IAAO,CAAC,CAAC;EAChC,CAAC;EAEDV,eAAe,EAAGW,YAAwD,IAAK;IAC7E,MAAMC,eAAiC,GAAG;MACxC,GAAGD,YAAY;MACfE,EAAE,EAAE,SAASC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;MAC1CC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACpC,CAAC;IAEDrD,GAAG,CAAE2B,KAAK,KAAM;MACdtB,aAAa,EAAE,CAACyC,eAAe,EAAE,GAAGnB,KAAK,CAACtB,aAAa,CAAC,CAACiD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;EACL,CAAC;EAEDC,oBAAoB,EAAGR,EAAU,IAAK;IACpC/C,GAAG,CAAE2B,KAAK,KAAM;MACdtB,aAAa,EAAEsB,KAAK,CAACtB,aAAa,CAACmD,GAAG,CAACC,KAAK,IAC1CA,KAAK,CAACV,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGU,KAAK;QAAEpB,IAAI,EAAE;MAAK,CAAC,GAAGoB,KAC/C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAEDC,kBAAkB,EAAEA,CAAA,KAAM;IACxB1D,GAAG,CAAC;MAAEK,aAAa,EAAE;IAAG,CAAC,CAAC;EAC5B,CAAC;EAEDsD,QAAQ,EAAGjD,KAAoB,IAAK;IAClCV,GAAG,CAAC;MAAEU;IAAM,CAAC,CAAC;IACd,IAAIA,KAAK,EAAE;MACT;MACAkD,UAAU,CAAC,MAAM;QACf5D,GAAG,CAAC;UAAEU,KAAK,EAAE;QAAK,CAAC,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAEDmD,UAAU,EAAGC,OAAgB,IAAK;IAChC9D,GAAG,CAAC;MAAES,SAAS,EAAEqD;IAAQ,CAAC,CAAC;EAC7B,CAAC;EAEDC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF/D,GAAG,CAAC;QAAES,SAAS,EAAE;MAAK,CAAC,CAAC;;MAExB;MACA,MAAMR,GAAG,CAAC,CAAC,CAACgB,UAAU,CAAC,CAAC;MAExBjB,GAAG,CAAC;QAAES,SAAS,EAAE;MAAM,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACQ,OAAO;QAAET,SAAS,EAAE;MAAM,CAAC,CAAC;IACjD;EACF,CAAC;EAEDuD,MAAM,EAAEA,CAAA,KAAM;IACZC,YAAY,CAACC,UAAU,CAAC,YAAY,CAAC;IACrCjE,GAAG,CAAC,CAAC,CAACkE,KAAK,CAAC,CAAC;EACf,CAAC;EAEDA,KAAK,EAAEA,CAAA,KAAM;IACXnE,GAAG,CAAC;MACFE,MAAM,EAAE,IAAI;MACZC,UAAU,EAAEP,iBAAiB;MAC7BQ,QAAQ,EAAE,KAAK;MACfC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,EAAE;MACpBC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,EACF;EACE0D,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAG1C,KAAK,KAAM;IACtBnB,aAAa,EAAEmB,KAAK,CAACnB,aAAa;IAClCH,aAAa,EAAEsB,KAAK,CAACtB;EACvB,CAAC,CAAC;EACF;EACAiE,kBAAkB,EAAEA,CAAA,KAAO3C,KAAK,IAAK;IACnC,MAAM4C,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC;IAChD,IAAID,KAAK,EAAE;MACT;MACAX,UAAU,CAAC,MAAM;QACfjC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoC,cAAc,CAAC,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;IACV;EACF;AACF,CACF,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}