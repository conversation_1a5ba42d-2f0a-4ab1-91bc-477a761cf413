{"ast": null, "code": "// src/infiniteQueryBehavior.ts\nimport { addToEnd, addToStart, ensureQueryFn } from \"./utils.js\";\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = {\n        pages: [],\n        pageParams: []\n      };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = object => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const createQueryFnContext = () => {\n            const queryFnContext2 = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? \"backward\" : \"forward\",\n              meta: context.options.meta\n            };\n            addSignalProperty(queryFnContext2);\n            return queryFnContext2;\n          };\n          const queryFnContext = createQueryFnContext();\n          const page = await queryFn(queryFnContext);\n          const {\n            maxPages\n          } = context.options;\n          const addTo = previous ? addToStart : addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(fetchFn, {\n            client: context.client,\n            queryKey: context.queryKey,\n            meta: context.options.meta,\n            signal: context.signal\n          }, query);\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, {\n  pages,\n  pageParams\n}) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(pages[lastIndex], pages, pageParams[lastIndex], pageParams) : void 0;\n}\nfunction getPreviousPageParam(options, {\n  pages,\n  pageParams\n}) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\nexport { hasNextPage, hasPreviousPage, infiniteQueryBehavior };", "map": {"version": 3, "names": ["addToEnd", "addToStart", "ensureQueryFn", "infiniteQueryBehavior", "pages", "onFetch", "context", "query", "options", "direction", "fetchOptions", "meta", "fetchMore", "oldPages", "state", "data", "oldPageParams", "pageParams", "result", "currentPage", "fetchFn", "cancelled", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "aborted", "addEventListener", "queryFn", "fetchPage", "param", "previous", "Promise", "reject", "length", "resolve", "createQueryFnContext", "queryFnContext2", "client", "query<PERSON><PERSON>", "pageParam", "queryFnContext", "page", "maxPages", "addTo", "pageParamFn", "getPreviousPageParam", "getNextPageParam", "oldData", "remainingPages", "initialPageParam", "persister", "lastIndex", "hasNextPage", "hasPreviousPage"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\node_modules\\@tanstack\\query-core\\src\\infiniteQueryBehavior.ts"], "sourcesContent": ["import { addToEnd, addToStart, ensureQueryFn } from './utils'\nimport type { QueryBehavior } from './query'\nimport type {\n  InfiniteData,\n  InfiniteQueryPageParamsOptions,\n  OmitKeyof,\n  QueryFunctionContext,\n  QueryKey,\n} from './types'\n\nexport function infiniteQueryBehavior<TQueryFnData, TError, TData, TPageParam>(\n  pages?: number,\n): QueryBehavior<TQueryFnData, TError, InfiniteData<TData, TPageParam>> {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options as InfiniteQueryPageParamsOptions<TData>\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction\n      const oldPages = context.state.data?.pages || []\n      const oldPageParams = context.state.data?.pageParams || []\n      let result: InfiniteData<unknown> = { pages: [], pageParams: [] }\n      let currentPage = 0\n\n      const fetchFn = async () => {\n        let cancelled = false\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true\n              } else {\n                context.signal.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions)\n\n        // Create function to fetch a page\n        const fetchPage = async (\n          data: InfiniteData<unknown>,\n          param: unknown,\n          previous?: boolean,\n        ): Promise<InfiniteData<unknown>> => {\n          if (cancelled) {\n            return Promise.reject()\n          }\n\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data)\n          }\n\n          const createQueryFnContext = () => {\n            const queryFnContext: OmitKeyof<\n              QueryFunctionContext<QueryKey, unknown>,\n              'signal'\n            > = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? 'backward' : 'forward',\n              meta: context.options.meta,\n            }\n            addSignalProperty(queryFnContext)\n            return queryFnContext as QueryFunctionContext<QueryKey, unknown>\n          }\n\n          const queryFnContext = createQueryFnContext()\n\n          const page = await queryFn(queryFnContext)\n\n          const { maxPages } = context.options\n          const addTo = previous ? addToStart : addToEnd\n\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages),\n          }\n        }\n\n        // fetch next / previous page?\n        if (direction && oldPages.length) {\n          const previous = direction === 'backward'\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams,\n          }\n          const param = pageParamFn(options, oldData)\n\n          result = await fetchPage(oldData, param, previous)\n        } else {\n          const remainingPages = pages ?? oldPages.length\n\n          // Fetch all pages\n          do {\n            const param =\n              currentPage === 0\n                ? (oldPageParams[0] ?? options.initialPageParam)\n                : getNextPageParam(options, result)\n            if (currentPage > 0 && param == null) {\n              break\n            }\n            result = await fetchPage(result, param)\n            currentPage++\n          } while (currentPage < remainingPages)\n        }\n\n        return result\n      }\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn as any,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal,\n            },\n            query,\n          )\n        }\n      } else {\n        context.fetchFn = fetchFn\n      }\n    },\n  }\n}\n\nfunction getNextPageParam(\n  options: InfiniteQueryPageParamsOptions<any>,\n  { pages, pageParams }: InfiniteData<unknown>,\n): unknown | undefined {\n  const lastIndex = pages.length - 1\n  return pages.length > 0\n    ? options.getNextPageParam(\n        pages[lastIndex],\n        pages,\n        pageParams[lastIndex],\n        pageParams,\n      )\n    : undefined\n}\n\nfunction getPreviousPageParam(\n  options: InfiniteQueryPageParamsOptions<any>,\n  { pages, pageParams }: InfiniteData<unknown>,\n): unknown | undefined {\n  return pages.length > 0\n    ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams)\n    : undefined\n}\n\n/**\n * Checks if there is a next page.\n */\nexport function hasNextPage(\n  options: InfiniteQueryPageParamsOptions<any, any>,\n  data?: InfiniteData<unknown>,\n): boolean {\n  if (!data) return false\n  return getNextPageParam(options, data) != null\n}\n\n/**\n * Checks if there is a previous page.\n */\nexport function hasPreviousPage(\n  options: InfiniteQueryPageParamsOptions<any, any>,\n  data?: InfiniteData<unknown>,\n): boolean {\n  if (!data || !options.getPreviousPageParam) return false\n  return getPreviousPageParam(options, data) != null\n}\n"], "mappings": ";AAAA,SAASA,QAAA,EAAUC,UAAA,EAAYC,aAAA,QAAqB;AAU7C,SAASC,sBACdC,KAAA,EACsE;EACtE,OAAO;IACLC,OAAA,EAASA,CAACC,OAAA,EAASC,KAAA,KAAU;MAC3B,MAAMC,OAAA,GAAUF,OAAA,CAAQE,OAAA;MACxB,MAAMC,SAAA,GAAYH,OAAA,CAAQI,YAAA,EAAcC,IAAA,EAAMC,SAAA,EAAWH,SAAA;MACzD,MAAMI,QAAA,GAAWP,OAAA,CAAQQ,KAAA,CAAMC,IAAA,EAAMX,KAAA,IAAS,EAAC;MAC/C,MAAMY,aAAA,GAAgBV,OAAA,CAAQQ,KAAA,CAAMC,IAAA,EAAME,UAAA,IAAc,EAAC;MACzD,IAAIC,MAAA,GAAgC;QAAEd,KAAA,EAAO,EAAC;QAAGa,UAAA,EAAY;MAAG;MAChE,IAAIE,WAAA,GAAc;MAElB,MAAMC,OAAA,GAAU,MAAAA,CAAA,KAAY;QAC1B,IAAIC,SAAA,GAAY;QAChB,MAAMC,iBAAA,GAAqBC,MAAA,IAAoB;UAC7CC,MAAA,CAAOC,cAAA,CAAeF,MAAA,EAAQ,UAAU;YACtCG,UAAA,EAAY;YACZC,GAAA,EAAKA,CAAA,KAAM;cACT,IAAIrB,OAAA,CAAQsB,MAAA,CAAOC,OAAA,EAAS;gBAC1BR,SAAA,GAAY;cACd,OAAO;gBACLf,OAAA,CAAQsB,MAAA,CAAOE,gBAAA,CAAiB,SAAS,MAAM;kBAC7CT,SAAA,GAAY;gBACd,CAAC;cACH;cACA,OAAOf,OAAA,CAAQsB,MAAA;YACjB;UACF,CAAC;QACH;QAEA,MAAMG,OAAA,GAAU7B,aAAA,CAAcI,OAAA,CAAQE,OAAA,EAASF,OAAA,CAAQI,YAAY;QAGnE,MAAMsB,SAAA,GAAY,MAAAA,CAChBjB,IAAA,EACAkB,KAAA,EACAC,QAAA,KACmC;UACnC,IAAIb,SAAA,EAAW;YACb,OAAOc,OAAA,CAAQC,MAAA,CAAO;UACxB;UAEA,IAAIH,KAAA,IAAS,QAAQlB,IAAA,CAAKX,KAAA,CAAMiC,MAAA,EAAQ;YACtC,OAAOF,OAAA,CAAQG,OAAA,CAAQvB,IAAI;UAC7B;UAEA,MAAMwB,oBAAA,GAAuBA,CAAA,KAAM;YACjC,MAAMC,eAAA,GAGF;cACFC,MAAA,EAAQnC,OAAA,CAAQmC,MAAA;cAChBC,QAAA,EAAUpC,OAAA,CAAQoC,QAAA;cAClBC,SAAA,EAAWV,KAAA;cACXxB,SAAA,EAAWyB,QAAA,GAAW,aAAa;cACnCvB,IAAA,EAAML,OAAA,CAAQE,OAAA,CAAQG;YACxB;YACAW,iBAAA,CAAkBkB,eAAc;YAChC,OAAOA,eAAA;UACT;UAEA,MAAMI,cAAA,GAAiBL,oBAAA,CAAqB;UAE5C,MAAMM,IAAA,GAAO,MAAMd,OAAA,CAAQa,cAAc;UAEzC,MAAM;YAAEE;UAAS,IAAIxC,OAAA,CAAQE,OAAA;UAC7B,MAAMuC,KAAA,GAAQb,QAAA,GAAWjC,UAAA,GAAaD,QAAA;UAEtC,OAAO;YACLI,KAAA,EAAO2C,KAAA,CAAMhC,IAAA,CAAKX,KAAA,EAAOyC,IAAA,EAAMC,QAAQ;YACvC7B,UAAA,EAAY8B,KAAA,CAAMhC,IAAA,CAAKE,UAAA,EAAYgB,KAAA,EAAOa,QAAQ;UACpD;QACF;QAGA,IAAIrC,SAAA,IAAaI,QAAA,CAASwB,MAAA,EAAQ;UAChC,MAAMH,QAAA,GAAWzB,SAAA,KAAc;UAC/B,MAAMuC,WAAA,GAAcd,QAAA,GAAWe,oBAAA,GAAuBC,gBAAA;UACtD,MAAMC,OAAA,GAAU;YACd/C,KAAA,EAAOS,QAAA;YACPI,UAAA,EAAYD;UACd;UACA,MAAMiB,KAAA,GAAQe,WAAA,CAAYxC,OAAA,EAAS2C,OAAO;UAE1CjC,MAAA,GAAS,MAAMc,SAAA,CAAUmB,OAAA,EAASlB,KAAA,EAAOC,QAAQ;QACnD,OAAO;UACL,MAAMkB,cAAA,GAAiBhD,KAAA,IAASS,QAAA,CAASwB,MAAA;UAGzC,GAAG;YACD,MAAMJ,KAAA,GACJd,WAAA,KAAgB,IACXH,aAAA,CAAc,CAAC,KAAKR,OAAA,CAAQ6C,gBAAA,GAC7BH,gBAAA,CAAiB1C,OAAA,EAASU,MAAM;YACtC,IAAIC,WAAA,GAAc,KAAKc,KAAA,IAAS,MAAM;cACpC;YACF;YACAf,MAAA,GAAS,MAAMc,SAAA,CAAUd,MAAA,EAAQe,KAAK;YACtCd,WAAA;UACF,SAASA,WAAA,GAAciC,cAAA;QACzB;QAEA,OAAOlC,MAAA;MACT;MACA,IAAIZ,OAAA,CAAQE,OAAA,CAAQ8C,SAAA,EAAW;QAC7BhD,OAAA,CAAQc,OAAA,GAAU,MAAM;UACtB,OAAOd,OAAA,CAAQE,OAAA,CAAQ8C,SAAA,GACrBlC,OAAA,EACA;YACEqB,MAAA,EAAQnC,OAAA,CAAQmC,MAAA;YAChBC,QAAA,EAAUpC,OAAA,CAAQoC,QAAA;YAClB/B,IAAA,EAAML,OAAA,CAAQE,OAAA,CAAQG,IAAA;YACtBiB,MAAA,EAAQtB,OAAA,CAAQsB;UAClB,GACArB,KACF;QACF;MACF,OAAO;QACLD,OAAA,CAAQc,OAAA,GAAUA,OAAA;MACpB;IACF;EACF;AACF;AAEA,SAAS8B,iBACP1C,OAAA,EACA;EAAEJ,KAAA;EAAOa;AAAW,GACC;EACrB,MAAMsC,SAAA,GAAYnD,KAAA,CAAMiC,MAAA,GAAS;EACjC,OAAOjC,KAAA,CAAMiC,MAAA,GAAS,IAClB7B,OAAA,CAAQ0C,gBAAA,CACN9C,KAAA,CAAMmD,SAAS,GACfnD,KAAA,EACAa,UAAA,CAAWsC,SAAS,GACpBtC,UACF,IACA;AACN;AAEA,SAASgC,qBACPzC,OAAA,EACA;EAAEJ,KAAA;EAAOa;AAAW,GACC;EACrB,OAAOb,KAAA,CAAMiC,MAAA,GAAS,IAClB7B,OAAA,CAAQyC,oBAAA,GAAuB7C,KAAA,CAAM,CAAC,GAAGA,KAAA,EAAOa,UAAA,CAAW,CAAC,GAAGA,UAAU,IACzE;AACN;AAKO,SAASuC,YACdhD,OAAA,EACAO,IAAA,EACS;EACT,IAAI,CAACA,IAAA,EAAM,OAAO;EAClB,OAAOmC,gBAAA,CAAiB1C,OAAA,EAASO,IAAI,KAAK;AAC5C;AAKO,SAAS0C,gBACdjD,OAAA,EACAO,IAAA,EACS;EACT,IAAI,CAACA,IAAA,IAAQ,CAACP,OAAA,CAAQyC,oBAAA,EAAsB,OAAO;EACnD,OAAOA,oBAAA,CAAqBzC,OAAA,EAASO,IAAI,KAAK;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}