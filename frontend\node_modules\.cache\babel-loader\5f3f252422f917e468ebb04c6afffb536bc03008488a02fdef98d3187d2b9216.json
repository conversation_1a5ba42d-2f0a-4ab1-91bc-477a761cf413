{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleGamePage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Componente do Dashboard Simplificado\nconst SimpleDashboard = () => {\n  _s();\n  const {\n    user\n  } = useSimpleAuth();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold mb-2\",\n          children: \"\\uD83C\\uDFAE Dashboard do Jogo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-muted\",\n          children: [\"Bem-vindo de volta, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: (user === null || user === void 0 ? void 0 : user.nick) || 'Jogador'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 33\n          }, this), \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-semibold mb-6\",\n              children: \"\\uD83D\\uDCCA Estat\\xEDsticas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-blue-900 border-blue-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-blue-100\",\n                    children: \"1,250\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 31,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-300\",\n                    children: \"Pontos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 32,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-green-900 border-green-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-green-100\",\n                    children: \"15\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 37,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-green-300\",\n                    children: \"N\\xEDvel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 38,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-purple-900 border-purple-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-purple-100\",\n                    children: \"42\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 43,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-purple-300\",\n                    children: \"Conquistas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 44,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-orange-900 border-orange-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-orange-100\",\n                    children: \"7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 49,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-orange-300\",\n                    children: \"Ranking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 48,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"\\u26A1 A\\xE7\\xF5es R\\xE1pidas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-primary\",\n                children: \"\\uD83D\\uDD0D Scanner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-secondary\",\n                children: \"\\uD83D\\uDCAC Chat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-accent\",\n                children: \"\\uD83C\\uDFC6 Loja\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-outline\",\n                children: \"\\u2699\\uFE0F Configura\\xE7\\xF5es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-semibold mb-6\",\n            children: \"\\uD83D\\uDCC8 Atividade Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Pontos ganhos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-text-muted\",\n                    children: \"H\\xE1 2 horas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-green-400 font-bold\",\n                children: \"+150 pts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold\",\n                    children: \"\\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Nova conquista\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-text-muted\",\n                    children: \"H\\xE1 1 dia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-400 font-bold\",\n                children: \"Explorador\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold\",\n                    children: \"\\u2B06\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Subiu de n\\xEDvel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 116,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-text-muted\",\n                    children: \"H\\xE1 3 dias\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-purple-400 font-bold\",\n                children: \"N\\xEDvel 15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n\n// Componentes de placeholder para outras páginas\n_s(SimpleDashboard, \"R1Uyte2H/ewqGaISUsQngZLWh00=\", false, function () {\n  return [useSimpleAuth];\n});\n_c = SimpleDashboard;\nconst SimpleScanner = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-4\",\n        children: \"\\uD83D\\uDD0D Scanner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-muted mb-6\",\n        children: \"Funcionalidade do scanner ser\\xE1 implementada aqui.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-bg-tertiary p-8 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDEA7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Em desenvolvimento...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 132,\n  columnNumber: 3\n}, this);\n_c2 = SimpleScanner;\nconst SimpleChat = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-4\",\n        children: \"\\uD83D\\uDCAC Chat\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-muted mb-6\",\n        children: \"Sistema de chat ser\\xE1 implementado aqui.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-bg-tertiary p-8 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDEA7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Em desenvolvimento...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 147,\n  columnNumber: 3\n}, this);\n\n// Navegação Simples\n_c3 = SimpleChat;\nconst SimpleNavigation = () => {\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-bg-secondary border-b border-border-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl font-bold\",\n            children: \"\\uD83C\\uDFAE SHACK\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/game\",\n              className: \"nav-link\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/game/scanner\",\n              className: \"nav-link\",\n              children: \"Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/game/chat\",\n              className: \"nav-link\",\n              children: \"Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-text-muted\",\n            children: \"Modo Teste\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n\n// Página Principal do Jogo Simplificada\n_c4 = SimpleNavigation;\nconst SimpleGamePage = () => {\n  console.log('SimpleGamePage - Renderizando...');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-bg-primary text-text-primary\",\n    children: [/*#__PURE__*/_jsxDEV(SimpleNavigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(SimpleDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/scanner\",\n        element: /*#__PURE__*/_jsxDEV(SimpleScanner, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/chat\",\n        element: /*#__PURE__*/_jsxDEV(SimpleChat, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(SimpleDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_c5 = SimpleGamePage;\nexport default SimpleGamePage;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"SimpleDashboard\");\n$RefreshReg$(_c2, \"SimpleScanner\");\n$RefreshReg$(_c3, \"SimpleChat\");\n$RefreshReg$(_c4, \"SimpleNavigation\");\n$RefreshReg$(_c5, \"SimpleGamePage\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "useSimpleAuth", "jsxDEV", "_jsxDEV", "SimpleDashboard", "_s", "user", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "_c", "SimpleScanner", "_c2", "SimpleChat", "_c3", "SimpleNavigation", "href", "_c4", "SimpleGamePage", "console", "log", "path", "element", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleGamePage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useChat } from '../stores/chatStore';\n\n// Componente do Dashboard Simplificado\nconst SimpleDashboard: React.FC = () => {\n  const { user } = useSimpleAuth();\n\n  return (\n    <div className=\"p-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold mb-2\">\n            🎮 Dashboard do Jogo\n          </h1>\n          <p className=\"text-text-muted\">\n            Bem-vindo de volta, <strong>{user?.nick || 'Jogador'}</strong>!\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Stats do Jogador */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"card\">\n              <h2 className=\"text-2xl font-semibold mb-6\">📊 Estatísticas</h2>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"card bg-blue-900 border-blue-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-blue-100\">1,250</div>\n                    <div className=\"text-sm text-blue-300\">Pontos</div>\n                  </div>\n                </div>\n                <div className=\"card bg-green-900 border-green-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-green-100\">15</div>\n                    <div className=\"text-sm text-green-300\">Nível</div>\n                  </div>\n                </div>\n                <div className=\"card bg-purple-900 border-purple-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-purple-100\">42</div>\n                    <div className=\"text-sm text-purple-300\">Conquistas</div>\n                  </div>\n                </div>\n                <div className=\"card bg-orange-900 border-orange-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-orange-100\">7</div>\n                    <div className=\"text-sm text-orange-300\">Ranking</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Ações Rápidas */}\n          <div>\n            <div className=\"card\">\n              <h2 className=\"text-xl font-semibold mb-4\">⚡ Ações Rápidas</h2>\n              <div className=\"space-y-3\">\n                <button className=\"w-full btn-primary\">\n                  🔍 Scanner\n                </button>\n                <button className=\"w-full btn-secondary\">\n                  💬 Chat\n                </button>\n                <button className=\"w-full btn-accent\">\n                  🏆 Loja\n                </button>\n                <button className=\"w-full btn-outline\">\n                  ⚙️ Configurações\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Atividade Recente */}\n        <div className=\"mt-8\">\n          <div className=\"card\">\n            <h2 className=\"text-2xl font-semibold mb-6\">📈 Atividade Recente</h2>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold\">+</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium\">Pontos ganhos</div>\n                    <div className=\"text-sm text-text-muted\">Há 2 horas</div>\n                  </div>\n                </div>\n                <div className=\"text-green-400 font-bold\">+150 pts</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold\">🏆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium\">Nova conquista</div>\n                    <div className=\"text-sm text-text-muted\">Há 1 dia</div>\n                  </div>\n                </div>\n                <div className=\"text-blue-400 font-bold\">Explorador</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold\">⬆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium\">Subiu de nível</div>\n                    <div className=\"text-sm text-text-muted\">Há 3 dias</div>\n                  </div>\n                </div>\n                <div className=\"text-purple-400 font-bold\">Nível 15</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Componentes de placeholder para outras páginas\nconst SimpleScanner: React.FC = () => (\n  <div className=\"p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"card text-center\">\n        <h1 className=\"text-3xl font-bold mb-4\">🔍 Scanner</h1>\n        <p className=\"text-text-muted mb-6\">Funcionalidade do scanner será implementada aqui.</p>\n        <div className=\"bg-bg-tertiary p-8 rounded-lg\">\n          <div className=\"text-6xl mb-4\">🚧</div>\n          <p>Em desenvolvimento...</p>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nconst SimpleChat: React.FC = () => (\n  <div className=\"p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"card text-center\">\n        <h1 className=\"text-3xl font-bold mb-4\">💬 Chat</h1>\n        <p className=\"text-text-muted mb-6\">Sistema de chat será implementado aqui.</p>\n        <div className=\"bg-bg-tertiary p-8 rounded-lg\">\n          <div className=\"text-6xl mb-4\">🚧</div>\n          <p>Em desenvolvimento...</p>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\n// Navegação Simples\nconst SimpleNavigation: React.FC = () => {\n  return (\n    <nav className=\"bg-bg-secondary border-b border-border-primary\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-8\">\n            <div className=\"text-xl font-bold\">🎮 SHACK</div>\n            <div className=\"flex space-x-4\">\n              <a href=\"/game\" className=\"nav-link\">Dashboard</a>\n              <a href=\"/game/scanner\" className=\"nav-link\">Scanner</a>\n              <a href=\"/game/chat\" className=\"nav-link\">Chat</a>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-sm text-text-muted\">Modo Teste</span>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\n// Página Principal do Jogo Simplificada\nconst SimpleGamePage: React.FC = () => {\n  console.log('SimpleGamePage - Renderizando...');\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary\">\n      <SimpleNavigation />\n      \n      <Routes>\n        <Route path=\"/\" element={<SimpleDashboard />} />\n        <Route path=\"/scanner\" element={<SimpleScanner />} />\n        <Route path=\"/chat\" element={<SimpleChat />} />\n        <Route path=\"*\" element={<SimpleDashboard />} />\n      </Routes>\n    </div>\n  );\n};\n\nexport default SimpleGamePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAI1D;AACA,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGL,aAAa,CAAC,CAAC;EAEhC,oBACEE,OAAA;IAAKI,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBL,OAAA;MAAKI,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCL,OAAA;QAAKI,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBL,OAAA;UAAII,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLT,OAAA;UAAGI,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,sBACT,eAAAL,OAAA;YAAAK,QAAA,EAAS,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,IAAI,KAAI;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,KAChE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENT,OAAA;QAAKI,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDL,OAAA;UAAKI,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BL,OAAA;YAAKI,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBL,OAAA;cAAII,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChET,OAAA;cAAKI,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDL,OAAA;gBAAKI,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,eAC/CL,OAAA;kBAAKI,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BL,OAAA;oBAAKI,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7DT,OAAA;oBAAKI,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKI,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDL,OAAA;kBAAKI,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BL,OAAA;oBAAKI,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3DT,OAAA;oBAAKI,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKI,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,eACnDL,OAAA;kBAAKI,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BL,OAAA;oBAAKI,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DT,OAAA;oBAAKI,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKI,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,eACnDL,OAAA;kBAAKI,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BL,OAAA;oBAAKI,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3DT,OAAA;oBAAKI,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNT,OAAA;UAAAK,QAAA,eACEL,OAAA;YAAKI,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBL,OAAA;cAAII,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DT,OAAA;cAAKI,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBL,OAAA;gBAAQI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTT,OAAA;gBAAQI,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTT,OAAA;gBAAQI,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAEtC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTT,OAAA;gBAAQI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNT,OAAA;QAAKI,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBL,OAAA;UAAKI,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBL,OAAA;YAAII,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrET,OAAA;YAAKI,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBL,OAAA;cAAKI,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EL,OAAA;gBAAKI,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCL,OAAA;kBAAKI,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,eACxFL,OAAA;oBAAMI,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNT,OAAA;kBAAAK,QAAA,gBACEL,OAAA;oBAAKI,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChDT,OAAA;oBAAKI,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKI,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eAENT,OAAA;cAAKI,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EL,OAAA;gBAAKI,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCL,OAAA;kBAAKI,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,eACvFL,OAAA;oBAAMI,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNT,OAAA;kBAAAK,QAAA,gBACEL,OAAA;oBAAKI,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjDT,OAAA;oBAAKI,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKI,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eAENT,OAAA;cAAKI,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EL,OAAA;gBAAKI,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCL,OAAA;kBAAKI,SAAS,EAAC,4EAA4E;kBAAAC,QAAA,eACzFL,OAAA;oBAAMI,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNT,OAAA;kBAAAK,QAAA,gBACEL,OAAA;oBAAKI,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjDT,OAAA;oBAAKI,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKI,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAP,EAAA,CA1HMD,eAAyB;EAAA,QACZH,aAAa;AAAA;AAAAa,EAAA,GAD1BV,eAAyB;AA2H/B,MAAMW,aAAuB,GAAGA,CAAA,kBAC9BZ,OAAA;EAAKI,SAAS,EAAC,KAAK;EAAAC,QAAA,eAClBL,OAAA;IAAKI,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChCL,OAAA;MAAKI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BL,OAAA;QAAII,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvDT,OAAA;QAAGI,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAiD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACzFT,OAAA;QAAKI,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CL,OAAA;UAAKI,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCT,OAAA;UAAAK,QAAA,EAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACI,GAAA,GAbID,aAAuB;AAe7B,MAAME,UAAoB,GAAGA,CAAA,kBAC3Bd,OAAA;EAAKI,SAAS,EAAC,KAAK;EAAAC,QAAA,eAClBL,OAAA;IAAKI,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChCL,OAAA;MAAKI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BL,OAAA;QAAII,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDT,OAAA;QAAGI,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC/ET,OAAA;QAAKI,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CL,OAAA;UAAKI,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCT,OAAA;UAAAK,QAAA,EAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;;AAED;AAAAM,GAAA,GAfMD,UAAoB;AAgB1B,MAAME,gBAA0B,GAAGA,CAAA,KAAM;EACvC,oBACEhB,OAAA;IAAKI,SAAS,EAAC,gDAAgD;IAAAC,QAAA,eAC7DL,OAAA;MAAKI,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCL,OAAA;QAAKI,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDL,OAAA;UAAKI,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CL,OAAA;YAAKI,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjDT,OAAA;YAAKI,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BL,OAAA;cAAGiB,IAAI,EAAC,OAAO;cAACb,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClDT,OAAA;cAAGiB,IAAI,EAAC,eAAe;cAACb,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDT,OAAA;cAAGiB,IAAI,EAAC,YAAY;cAACb,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNT,OAAA;UAAKI,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1CL,OAAA;YAAMI,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAS,GAAA,GAtBMF,gBAA0B;AAuBhC,MAAMG,cAAwB,GAAGA,CAAA,KAAM;EACrCC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAE/C,oBACErB,OAAA;IAAKI,SAAS,EAAC,8CAA8C;IAAAC,QAAA,gBAC3DL,OAAA,CAACgB,gBAAgB;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpBT,OAAA,CAACJ,MAAM;MAAAS,QAAA,gBACLL,OAAA,CAACH,KAAK;QAACyB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEvB,OAAA,CAACC,eAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChDT,OAAA,CAACH,KAAK;QAACyB,IAAI,EAAC,UAAU;QAACC,OAAO,eAAEvB,OAAA,CAACY,aAAa;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDT,OAAA,CAACH,KAAK;QAACyB,IAAI,EAAC,OAAO;QAACC,OAAO,eAAEvB,OAAA,CAACc,UAAU;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CT,OAAA,CAACH,KAAK;QAACyB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEvB,OAAA,CAACC,eAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACe,GAAA,GAfIL,cAAwB;AAiB9B,eAAeA,cAAc;AAAC,IAAAR,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAM,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}