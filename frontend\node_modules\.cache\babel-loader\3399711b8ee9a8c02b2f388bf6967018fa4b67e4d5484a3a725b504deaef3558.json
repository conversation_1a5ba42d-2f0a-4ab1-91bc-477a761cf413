{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\ui\\\\ModernCard.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernCard = ({\n  children,\n  className = '',\n  variant = 'default',\n  hover = true,\n  glow = false,\n  onClick\n}) => {\n  const baseClasses = 'rounded-xl transition-all duration-300 ease-in-out';\n  const variantClasses = {\n    default: 'bg-gray-800/80 backdrop-blur-sm border border-gray-700/50',\n    glass: 'bg-white/5 backdrop-blur-md border border-white/10',\n    gradient: 'bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm border border-blue-500/20',\n    neon: 'bg-gray-900/90 backdrop-blur-sm border border-cyan-500/50 shadow-lg shadow-cyan-500/20'\n  };\n  const hoverClasses = hover ? {\n    default: 'hover:bg-gray-800/90 hover:border-gray-600/70 hover:shadow-lg hover:shadow-gray-900/20 hover:-translate-y-1',\n    glass: 'hover:bg-white/10 hover:border-white/20 hover:shadow-xl hover:shadow-white/5 hover:-translate-y-1',\n    gradient: 'hover:from-blue-900/30 hover:to-purple-900/30 hover:border-blue-400/40 hover:shadow-xl hover:shadow-blue-500/10 hover:-translate-y-1',\n    neon: 'hover:border-cyan-400/70 hover:shadow-xl hover:shadow-cyan-500/30 hover:-translate-y-1'\n  } : {};\n  const glowClasses = glow ? {\n    default: 'shadow-lg shadow-gray-500/10',\n    glass: 'shadow-xl shadow-white/5',\n    gradient: 'shadow-xl shadow-blue-500/20',\n    neon: 'shadow-xl shadow-cyan-500/30'\n  } : {};\n  const classes = [baseClasses, variantClasses[variant], hover ? hoverClasses[variant] : '', glow ? glowClasses[variant] : '', onClick ? 'cursor-pointer' : '', className].filter(Boolean).join(' ');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: classes,\n    onClick: onClick,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_c = ModernCard;\nexport default ModernCard;\nvar _c;\n$RefreshReg$(_c, \"ModernCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ModernCard", "children", "className", "variant", "hover", "glow", "onClick", "baseClasses", "variantClasses", "default", "glass", "gradient", "neon", "hoverClasses", "glowClasses", "classes", "filter", "Boolean", "join", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/ui/ModernCard.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ModernCardProps {\n  children: React.ReactNode;\n  className?: string;\n  variant?: 'default' | 'glass' | 'gradient' | 'neon';\n  hover?: boolean;\n  glow?: boolean;\n  onClick?: () => void;\n}\n\nconst ModernCard: React.FC<ModernCardProps> = ({\n  children,\n  className = '',\n  variant = 'default',\n  hover = true,\n  glow = false,\n  onClick,\n}) => {\n  const baseClasses = 'rounded-xl transition-all duration-300 ease-in-out';\n  \n  const variantClasses = {\n    default: 'bg-gray-800/80 backdrop-blur-sm border border-gray-700/50',\n    glass: 'bg-white/5 backdrop-blur-md border border-white/10',\n    gradient: 'bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm border border-blue-500/20',\n    neon: 'bg-gray-900/90 backdrop-blur-sm border border-cyan-500/50 shadow-lg shadow-cyan-500/20',\n  };\n\n  const hoverClasses = hover ? {\n    default: 'hover:bg-gray-800/90 hover:border-gray-600/70 hover:shadow-lg hover:shadow-gray-900/20 hover:-translate-y-1',\n    glass: 'hover:bg-white/10 hover:border-white/20 hover:shadow-xl hover:shadow-white/5 hover:-translate-y-1',\n    gradient: 'hover:from-blue-900/30 hover:to-purple-900/30 hover:border-blue-400/40 hover:shadow-xl hover:shadow-blue-500/10 hover:-translate-y-1',\n    neon: 'hover:border-cyan-400/70 hover:shadow-xl hover:shadow-cyan-500/30 hover:-translate-y-1',\n  } : {};\n\n  const glowClasses = glow ? {\n    default: 'shadow-lg shadow-gray-500/10',\n    glass: 'shadow-xl shadow-white/5',\n    gradient: 'shadow-xl shadow-blue-500/20',\n    neon: 'shadow-xl shadow-cyan-500/30',\n  } : {};\n\n  const classes = [\n    baseClasses,\n    variantClasses[variant],\n    hover ? hoverClasses[variant] : '',\n    glow ? glowClasses[variant] : '',\n    onClick ? 'cursor-pointer' : '',\n    className,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div className={classes} onClick={onClick}>\n      {children}\n    </div>\n  );\n};\n\nexport default ModernCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW1B,MAAMC,UAAqC,GAAGA,CAAC;EAC7CC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,OAAO,GAAG,SAAS;EACnBC,KAAK,GAAG,IAAI;EACZC,IAAI,GAAG,KAAK;EACZC;AACF,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG,oDAAoD;EAExE,MAAMC,cAAc,GAAG;IACrBC,OAAO,EAAE,2DAA2D;IACpEC,KAAK,EAAE,oDAAoD;IAC3DC,QAAQ,EAAE,gGAAgG;IAC1GC,IAAI,EAAE;EACR,CAAC;EAED,MAAMC,YAAY,GAAGT,KAAK,GAAG;IAC3BK,OAAO,EAAE,6GAA6G;IACtHC,KAAK,EAAE,mGAAmG;IAC1GC,QAAQ,EAAE,sIAAsI;IAChJC,IAAI,EAAE;EACR,CAAC,GAAG,CAAC,CAAC;EAEN,MAAME,WAAW,GAAGT,IAAI,GAAG;IACzBI,OAAO,EAAE,8BAA8B;IACvCC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,8BAA8B;IACxCC,IAAI,EAAE;EACR,CAAC,GAAG,CAAC,CAAC;EAEN,MAAMG,OAAO,GAAG,CACdR,WAAW,EACXC,cAAc,CAACL,OAAO,CAAC,EACvBC,KAAK,GAAGS,YAAY,CAACV,OAAO,CAAC,GAAG,EAAE,EAClCE,IAAI,GAAGS,WAAW,CAACX,OAAO,CAAC,GAAG,EAAE,EAChCG,OAAO,GAAG,gBAAgB,GAAG,EAAE,EAC/BJ,SAAS,CACV,CAACc,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAE3B,oBACEnB,OAAA;IAAKG,SAAS,EAAEa,OAAQ;IAACT,OAAO,EAAEA,OAAQ;IAAAL,QAAA,EACvCA;EAAQ;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACC,EAAA,GA7CIvB,UAAqC;AA+C3C,eAAeA,UAAU;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}