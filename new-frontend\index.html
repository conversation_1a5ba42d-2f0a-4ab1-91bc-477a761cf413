<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHACK - Simulador de Hacking Online</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/mobile.css">
    <link rel="stylesheet" href="styles/components.css">
</head>
<body>
    <!-- Container principal do jogo (interface mobile) -->
    <div id="mobile-container" class="mobile-phone">
        <!-- Tela do dispositivo -->
        <div class="mobile-screen">
            <!-- Header com status -->
            <div class="mobile-header">
                <div class="status-bar">
                    <span class="time" id="current-time">12:34</span>
                    <div class="status-icons">
                        <span class="signal">📶</span>
                        <span class="battery">🔋</span>
                    </div>
                </div>
            </div>

            <!-- Conteúdo principal -->
            <div class="mobile-content" id="main-content">
                <!-- Tel<PERSON> de <PERSON> (inicial) -->
                <div id="login-screen" class="screen active">
                    <div class="login-container">
                        <div class="app-logo">
                            <div class="logo-icon">🔒</div>
                            <h1>SHACK</h1>
                            <p>Simulador de Hacking Online</p>
                        </div>

                        <div class="login-form">
                            <div class="form-tabs">
                                <button class="tab-btn active" data-tab="login">Login</button>
                                <button class="tab-btn" data-tab="register">Registrar</button>
                            </div>

                            <!-- Formulário de Login -->
                            <form id="login-form" class="tab-content active">
                                <div class="input-group">
                                    <label>Email</label>
                                    <input type="email" id="login-email" placeholder="<EMAIL>" required>
                                </div>
                                <div class="input-group">
                                    <label>Senha</label>
                                    <input type="password" id="login-password" placeholder="Sua senha" required>
                                </div>
                                <button type="submit" class="btn-primary">
                                    <span class="btn-text">Entrar</span>
                                    <span class="btn-loading hidden">Entrando...</span>
                                </button>
                            </form>

                            <!-- Formulário de Registro -->
                            <form id="register-form" class="tab-content">
                                <div class="input-group">
                                    <label>Nome do Hacker</label>
                                    <input type="text" id="register-nick" placeholder="Seu nick" maxlength="20" required>
                                </div>
                                <div class="input-group">
                                    <label>Email</label>
                                    <input type="email" id="register-email" placeholder="<EMAIL>" required>
                                </div>
                                <div class="input-group">
                                    <label>Senha</label>
                                    <input type="password" id="register-password" placeholder="Crie uma senha" required>
                                </div>
                                <button type="submit" class="btn-primary">
                                    <span class="btn-text">Criar Conta</span>
                                    <span class="btn-loading hidden">Criando...</span>
                                </button>
                            </form>

                            <!-- Teste Rápido -->
                            <div class="quick-test">
                                <h3>🧪 Teste Rápido</h3>
                                <p>Para testar sem preencher formulário:</p>
                                <button id="quick-login" class="btn-secondary">⚡ Login Instantâneo</button>
                            </div>

                            <!-- Mensagem de erro -->
                            <div id="error-message" class="error-message hidden"></div>
                        </div>
                    </div>
                </div>

                <!-- Tela Principal do Jogo -->
                <div id="game-screen" class="screen">
                    <!-- Home -->
                    <div id="home-tab" class="tab-content active">
                        <div class="home-container">
                            <!-- Perfil do Jogador -->
                            <div class="player-profile">
                                <div class="profile-header">
                                    <div class="avatar">👤</div>
                                    <div class="player-info">
                                        <h2 id="player-nick">Carregando...</h2>
                                        <p id="player-level">Nível 1</p>
                                    </div>
                                </div>
                                
                                <!-- Barras de Progresso -->
                                <div class="progress-bars">
                                    <div class="progress-item">
                                        <label>XP</label>
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="xp-progress"></div>
                                        </div>
                                        <span id="xp-text">0 / 100</span>
                                    </div>
                                </div>

                                <!-- Recursos -->
                                <div class="resources">
                                    <div class="resource-item">
                                        <span class="resource-icon">💰</span>
                                        <span class="resource-label">Dinheiro</span>
                                        <span class="resource-value" id="player-money">$0</span>
                                    </div>
                                    <div class="resource-item">
                                        <span class="resource-icon">⛏️</span>
                                        <span class="resource-label">Shack</span>
                                        <span class="resource-value" id="player-shack">0</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Notificações -->
                            <div class="notifications" id="notifications">
                                <!-- Notificações serão inseridas aqui -->
                            </div>

                            <!-- Ações Rápidas -->
                            <div class="quick-actions">
                                <button class="action-btn" onclick="switchTab('apps')">
                                    <span class="action-icon">📱</span>
                                    <span>Apps</span>
                                </button>
                                <button class="action-btn" onclick="switchTab('scanner')">
                                    <span class="action-icon">🔍</span>
                                    <span>Scanner</span>
                                </button>
                                <button class="action-btn" onclick="switchTab('terminal')">
                                    <span class="action-icon">💻</span>
                                    <span>Terminal</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Apps -->
                    <div id="apps-tab" class="tab-content">
                        <div class="apps-container">
                            <h2>Aplicativos</h2>
                            <div class="apps-grid" id="apps-grid">
                                <!-- Apps serão inseridos aqui -->
                            </div>
                        </div>
                    </div>

                    <!-- Scanner -->
                    <div id="scanner-tab" class="tab-content">
                        <div class="scanner-container">
                            <h2>Scanner de Rede</h2>
                            <p>Em desenvolvimento...</p>
                        </div>
                    </div>

                    <!-- Terminal -->
                    <div id="terminal-tab" class="tab-content">
                        <div class="terminal-container">
                            <h2>Terminal</h2>
                            <p>Em desenvolvimento...</p>
                        </div>
                    </div>

                    <!-- Chat -->
                    <div id="chat-tab" class="tab-content">
                        <div class="chat-container">
                            <h2>Chat Global</h2>
                            <p>Em desenvolvimento...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer com navegação -->
            <div class="mobile-footer">
                <div class="nav-bar">
                    <button class="nav-btn active" data-tab="home" onclick="switchTab('home')">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-label">Home</span>
                    </button>
                    <button class="nav-btn" data-tab="apps" onclick="switchTab('apps')">
                        <span class="nav-icon">📱</span>
                        <span class="nav-label">Apps</span>
                    </button>
                    <button class="nav-btn" data-tab="scanner" onclick="switchTab('scanner')">
                        <span class="nav-icon">🔍</span>
                        <span class="nav-label">Scanner</span>
                    </button>
                    <button class="nav-btn" data-tab="terminal" onclick="switchTab('terminal')">
                        <span class="nav-icon">💻</span>
                        <span class="nav-label">Terminal</span>
                    </button>
                    <button class="nav-btn" data-tab="chat" onclick="switchTab('chat')">
                        <span class="nav-icon">💬</span>
                        <span class="nav-label">Chat</span>
                    </button>
                </div>
                
                <!-- Botão Home Central -->
                <div class="home-button" onclick="switchTab('home')">
                    <div class="home-btn-inner">🏠</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner"></div>
        <p>Carregando...</p>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/game.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
