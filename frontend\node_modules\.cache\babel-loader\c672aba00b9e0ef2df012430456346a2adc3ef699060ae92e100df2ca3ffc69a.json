{"ast": null, "code": "import React,{useEffect}from'react';import{Routes,Route}from'react-router-dom';import{useSimpleAuth}from'../stores/simpleAuthStore';import{usePlayer}from'../stores/playerStore';import{useChat}from'../stores/chatStore';// Componente do Dashboard Simplificado\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SimpleDashboard=()=>{const{user}=useSimpleAuth();const{currentPlayer,isLoadingPlayer,loadPlayerData,hasPlayerData}=usePlayer();useEffect(()=>{if(!hasPlayerData&&!isLoadingPlayer){console.log('SimpleDashboard - Carregando dados do jogador...');loadPlayerData();}},[hasPlayerData,isLoadingPlayer,loadPlayerData]);// Usar dados do player se disponível, senão usar dados mockados\nconst playerData=currentPlayer||{pontos:1250,nivel:15,conquistas:42,ranking:7};return/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold cyber-text\",children:\"Terminal Principal\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-text-muted\",children:[\"Operador: \",/*#__PURE__*/_jsx(\"span\",{className:\"text-cyber-primary\",children:(user===null||user===void 0?void 0:user.nick)||'Jogador'})]})]}),isLoadingPlayer&&/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold mb-4 cyber-text\",children:\"\\uD83D\\uDCCA Status do Sistema\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-bg-tertiary rounded-lg p-3 border border-blue-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold text-blue-400\",children:isLoadingPlayer?'...':playerData.pontos.toLocaleString()}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-blue-300\",children:\"PONTOS\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-bg-tertiary rounded-lg p-3 border border-green-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold text-green-400\",children:isLoadingPlayer?'...':playerData.nivel}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-green-300\",children:\"N\\xCDVEL\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-bg-tertiary rounded-lg p-3 border border-purple-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold text-purple-400\",children:isLoadingPlayer?'...':playerData.conquistas}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-purple-300\",children:\"CONQUISTAS\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-bg-tertiary rounded-lg p-3 border border-orange-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold text-orange-400\",children:isLoadingPlayer?'...':playerData.ranking}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-orange-300\",children:\"RANKING\"})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold mb-4 cyber-text\",children:\"\\u26A1 Acesso R\\xE1pido\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-3\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>window.location.href='/game/scanner',className:\"btn-cyber text-sm py-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83D\\uDD0D\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Scanner\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>window.location.href='/game/chat',className:\"btn-cyber text-sm py-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83D\\uDCAC\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Chat\"})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"btn-cyber text-sm py-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83C\\uDFC6\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Loja\"})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"btn-cyber text-sm py-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\u2699\\uFE0F\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Config\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold mb-4 cyber-text\",children:\"\\uD83D\\uDCC8 Log do Sistema\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-sm\",children:\"+\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:\"Pontos ganhos\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-text-muted\",children:\"02:15:33\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-green-400 font-bold text-sm\",children:\"+150\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-sm\",children:\"\\uD83C\\uDFC6\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:\"Nova conquista\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-text-muted\",children:\"01:22:15\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-blue-400 font-bold text-sm\",children:\"HACK\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-sm\",children:\"\\u2191\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:\"Level UP\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-text-muted\",children:\"00:45:22\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-purple-400 font-bold text-sm\",children:\"LV.15\"})]})]})]})]})]});};// Componente Scanner Completo\nconst SimpleScanner=()=>{const{user}=useSimpleAuth();const{currentPlayer}=usePlayer();const[isScanning,setIsScanning]=React.useState(false);const[scanTargets,setScanTargets]=React.useState([]);const[scanError,setScanError]=React.useState(null);const[specificIP,setSpecificIP]=React.useState('');const[isAdvancedScan,setIsAdvancedScan]=React.useState(false);const handleQuickScan=async()=>{setIsScanning(true);setScanError(null);try{const response=await fetch('/api/scan',{method:'GET',headers:{'Authorization':`Bearer ${localStorage.getItem('token')}`,'Content-Type':'application/json'}});const data=await response.json();if(data.sucesso&&data.alvos){setScanTargets(data.alvos);}else{setScanError(data.mensagem||'Erro ao escanear alvos');}}catch(error){setScanError(error.message||'Erro de conexão');}finally{setIsScanning(false);}};const handleAdvancedScan=async()=>{if(!specificIP.trim()){setScanError('Digite um IP válido');return;}setIsAdvancedScan(true);setScanError(null);try{const response=await fetch(`/api/scan/ip/${specificIP}`,{method:'GET',headers:{'Authorization':`Bearer ${localStorage.getItem('token')}`,'Content-Type':'application/json'}});const data=await response.json();if(data.sucesso&&data.alvo){setScanTargets([data.alvo]);}else{setScanError(data.mensagem||'IP não encontrado');}}catch(error){setScanError(error.message||'Erro de conexão');}finally{setIsAdvancedScan(false);}};const handleExploit=async target=>{try{const response=await fetch(`/api/alvo/${target.ip}/exploit`,{method:'POST',headers:{'Authorization':`Bearer ${localStorage.getItem('token')}`,'Content-Type':'application/json'}});const data=await response.json();if(data.sucesso){alert(`Exploit bem-sucedido! ${data.mensagem}`);}else{alert(`Exploit falhou: ${data.mensagem}`);}}catch(error){alert(`Erro no exploit: ${error.message}`);}};const playerCPU=(currentPlayer===null||currentPlayer===void 0?void 0:currentPlayer.cpu)||1;return/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border-b border-cyber-primary\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-xl font-bold cyber-text\",children:\"\\uD83D\\uDD0D Scanner de Rede\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-text-muted\",children:\"Sistema de Reconhecimento - Criptografado\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-3 cyber-text\",children:\"\\u26A1 Scan R\\xE1pido\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-text-muted mb-4\",children:\"Encontra alvos aleat\\xF3rios na rede\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleQuickScan,disabled:isScanning,className:\"btn-cyber w-full py-3\",children:isScanning?'Escaneando...':'Iniciar Scan Rápido'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-3 cyber-text\",children:\"\\uD83C\\uDFAF Scan Avan\\xE7ado\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-text-muted mb-4\",children:\"Busca por IP espec\\xEDfico\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:specificIP,onChange:e=>setSpecificIP(e.target.value),placeholder:\"*************\",className:\"flex-1 bg-bg-tertiary border border-cyber-primary rounded px-3 py-2 text-white font-mono text-sm\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleAdvancedScan,disabled:isAdvancedScan,className:\"btn-cyber px-6\",children:isAdvancedScan?'...':'Scan'})]})]}),scanError&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-900 border border-red-500 rounded-lg p-3\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-red-300 text-sm font-mono\",children:[\"\\u274C \",scanError]})}),scanTargets.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4 cyber-text\",children:\"\\uD83D\\uDCCA Alvos Encontrados\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:scanTargets.map((target,index)=>{const canExploit=playerCPU>(target.firewall||1);return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-bg-tertiary rounded-lg p-4 border border-blue-500\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-start mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-bold text-blue-400\",children:target.nick}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-text-muted font-mono\",children:[\"IP: \",target.ip]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-text-muted\",children:[\"N\\xEDvel: \",target.nivel]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-text-muted\",children:[\"Firewall: \",target.firewall||1]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-text-muted\",children:[\"Dinheiro: $\",(target.dinheiro||0).toLocaleString()]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs\",children:/*#__PURE__*/_jsx(\"span\",{className:`px-2 py-1 rounded ${canExploit?'bg-green-600 text-green-100':'bg-red-600 text-red-100'}`,children:canExploit?'VULNERÁVEL':'PROTEGIDO'})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleExploit(target),disabled:!canExploit,className:`px-4 py-2 rounded font-semibold text-sm ${canExploit?'bg-green-600 hover:bg-green-700 text-white':'bg-gray-600 text-gray-400 cursor-not-allowed'}`,children:canExploit?'EXPLOITAR':'CPU INSUFICIENTE'})]})]},index);})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-semibold mb-2 cyber-text\",children:\"\\uD83D\\uDCBB Seu Sistema\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-2 text-xs\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"CPU: \",/*#__PURE__*/_jsx(\"span\",{className:\"text-cyber-primary\",children:playerCPU})]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"Status: \",/*#__PURE__*/_jsx(\"span\",{className:\"text-green-400\",children:\"ONLINE\"})]})]})]})]})]})});};const SimpleChat=()=>{const{messages,isLoading:isLoadingMessages,loadMessages,sendMessage,isSending}=useChat();const[newMessage,setNewMessage]=React.useState('');useEffect(()=>{if(messages.length===0&&!isLoadingMessages){console.log('SimpleChat - Carregando mensagens...');loadMessages();}},[messages.length,isLoadingMessages,loadMessages]);const handleSendMessage=async e=>{e.preventDefault();if(newMessage.trim()&&!isSending){await sendMessage(newMessage.trim());setNewMessage('');}};return/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border-b border-cyber-primary\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-xl font-bold cyber-text\",children:\"\\uD83D\\uDCAC Terminal de Chat\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-text-muted\",children:\"Canal Global - Criptografado\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-black rounded-lg p-3 h-80 overflow-y-auto mb-4 border border-cyber-primary\",children:isLoadingMessages?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-cyber-primary\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary mx-auto mb-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-mono\",children:\"CARREGANDO DADOS...\"})]}):messages.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-cyber-primary\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl mb-2\",children:\"\\uD83D\\uDCAC\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs font-mono\",children:\"CANAL VAZIO - AGUARDANDO TRANSMISS\\xC3O\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:messages.map(message=>/*#__PURE__*/_jsxs(\"div\",{className:\"border-l-2 border-cyber-primary pl-3 py-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-1\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-cyber-primary font-mono text-xs\",children:[\"[\",new Date(message.timestamp).toLocaleTimeString(),\"]\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-blue-400 font-mono text-xs font-bold\",children:[message.usuario,\":\"]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-mono text-sm pl-2\",children:message.mensagem})]},message.id))})}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSendMessage,className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:newMessage,onChange:e=>setNewMessage(e.target.value),placeholder:\"> Digite comando...\",className:\"flex-1 px-3 py-2 bg-black border border-cyber-primary rounded font-mono text-cyber-primary text-sm focus:outline-none focus:border-cyber-secondary placeholder-gray-500\",disabled:isSending}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:!newMessage.trim()||isSending,className:`px-4 py-2 rounded font-mono text-sm transition-all ${!newMessage.trim()||isSending?'bg-gray-800 text-gray-500 cursor-not-allowed':'bg-cyber-primary text-black hover:bg-cyber-secondary font-bold'}`,children:isSending?'...':'SEND'})]})]})]})});};// Navegação Estilo Celular/Jogo\nconst SimpleNavigation=()=>{const currentPath=window.location.pathname;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-bg-primary\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-900 to-purple-900 p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-black font-bold text-lg\",children:\"S\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-xl font-bold cyber-text\",children:\"SHACK\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-text-muted\",children:\"Web Terminal\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"status-online\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-green-400\",children:\"ONLINE\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-bg-secondary border-b border-cyber-primary\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsxs(\"a\",{href:\"/game\",className:`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath==='/game'||currentPath==='/game/'?'bg-cyber-primary text-black border-b-2 border-cyber-primary':'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83C\\uDFE0\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Dashboard\"})]}),/*#__PURE__*/_jsxs(\"a\",{href:\"/game/scanner\",className:`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath.includes('/scanner')?'bg-cyber-primary text-black border-b-2 border-cyber-primary':'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83D\\uDD0D\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Scanner\"})]}),/*#__PURE__*/_jsxs(\"a\",{href:\"/game/chat\",className:`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${currentPath.includes('/chat')?'bg-cyber-primary text-black border-b-2 border-cyber-primary':'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'}`,children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg mb-1\",children:\"\\uD83D\\uDCAC\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Chat\"})]})]})})]});};// Página Principal do Jogo Simplificada\nconst SimpleGamePage=()=>{console.log('SimpleGamePage - Renderizando...');return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-bg-primary text-text-primary\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-md mx-auto bg-bg-primary min-h-screen relative\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-b from-bg-primary to-bg-secondary min-h-screen\",children:[/*#__PURE__*/_jsx(SimpleNavigation,{}),/*#__PURE__*/_jsx(\"div\",{className:\"pb-4\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(SimpleDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/scanner\",element:/*#__PURE__*/_jsx(SimpleScanner,{})}),/*#__PURE__*/_jsx(Route,{path:\"/chat\",element:/*#__PURE__*/_jsx(SimpleChat,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(SimpleDashboard,{})})]})})]})})});};export default SimpleGamePage;", "map": {"version": 3, "names": ["React", "useEffect", "Routes", "Route", "useSimpleAuth", "usePlayer", "useChat", "jsx", "_jsx", "jsxs", "_jsxs", "SimpleDashboard", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "conquistas", "ranking", "className", "children", "nick", "toLocaleString", "onClick", "window", "location", "href", "SimpleScanner", "isScanning", "setIsScanning", "useState", "scanTargets", "setScanTargets", "scanError", "setScanError", "specificIP", "setSpecificIP", "isAdvancedScan", "setIsAdvancedScan", "handleQuickScan", "response", "fetch", "method", "headers", "localStorage", "getItem", "data", "json", "sucesso", "alvos", "mensagem", "error", "message", "handleAdvancedScan", "trim", "alvo", "handleExploit", "target", "ip", "alert", "playerCPU", "cpu", "disabled", "type", "value", "onChange", "e", "placeholder", "length", "map", "index", "canExploit", "firewall", "<PERSON><PERSON><PERSON>", "SimpleChat", "messages", "isLoading", "isLoadingMessages", "loadMessages", "sendMessage", "isSending", "newMessage", "setNewMessage", "handleSendMessage", "preventDefault", "Date", "timestamp", "toLocaleTimeString", "usuario", "id", "onSubmit", "SimpleNavigation", "currentPath", "pathname", "includes", "SimpleGamePage", "path", "element"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleGamePage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useChat } from '../stores/chatStore';\n\n// Componente do Dashboard Simplificado\nconst SimpleDashboard: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n\n  return (\n    <div className=\"p-4\">\n      {/* Header compacto */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold cyber-text\">\n              Terminal Principal\n            </h1>\n            <p className=\"text-sm text-text-muted\">\n              Operador: <span className=\"text-cyber-primary\">{user?.nick || 'Jogador'}</span>\n            </p>\n          </div>\n          {isLoadingPlayer && (\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary\"></div>\n          )}\n        </div>\n      </div>\n\n      {/* Stats do Jogador - Layout Celular */}\n      <div className=\"space-y-4\">\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">📊 Status do Sistema</h2>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-blue-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-blue-400\">\n                    {isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()}\n                  </div>\n                  <div className=\"text-xs text-blue-300\">PONTOS</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-green-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-green-400\">\n                    {isLoadingPlayer ? '...' : playerData.nivel}\n                  </div>\n                  <div className=\"text-xs text-green-300\">NÍVEL</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-purple-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-purple-400\">\n                    {isLoadingPlayer ? '...' : playerData.conquistas}\n                  </div>\n                  <div className=\"text-xs text-purple-300\">CONQUISTAS</div>\n                </div>\n              </div>\n              <div className=\"bg-bg-tertiary rounded-lg p-3 border border-orange-500\">\n                <div className=\"text-center\">\n                  <div className=\"text-xl font-bold text-orange-400\">\n                    {isLoadingPlayer ? '...' : playerData.ranking}\n                  </div>\n                  <div className=\"text-xs text-orange-300\">RANKING</div>\n                </div>\n              </div>\n            </div>\n        </div>\n\n        {/* Ações Rápidas - Estilo Celular */}\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">⚡ Acesso Rápido</h2>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <button\n                onClick={() => window.location.href = '/game/scanner'}\n                className=\"btn-cyber text-sm py-3\"\n              >\n                <div className=\"text-lg mb-1\">🔍</div>\n                <div>Scanner</div>\n              </button>\n              <button\n                onClick={() => window.location.href = '/game/chat'}\n                className=\"btn-cyber text-sm py-3\"\n              >\n                <div className=\"text-lg mb-1\">💬</div>\n                <div>Chat</div>\n              </button>\n              <button className=\"btn-cyber text-sm py-3\">\n                <div className=\"text-lg mb-1\">🏆</div>\n                <div>Loja</div>\n              </button>\n              <button className=\"btn-cyber text-sm py-3\">\n                <div className=\"text-lg mb-1\">⚙️</div>\n                <div>Config</div>\n              </button>\n            </div>\n        </div>\n\n        {/* Log de Atividades - Estilo Terminal */}\n        <div className=\"cyber-border rounded-lg p-4 bg-gradient-to-r from-bg-secondary to-bg-tertiary\">\n            <h2 className=\"text-lg font-semibold mb-4 cyber-text\">📈 Log do Sistema</h2>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-green-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">+</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Pontos ganhos</div>\n                    <div className=\"text-xs text-text-muted\">02:15:33</div>\n                  </div>\n                </div>\n                <div className=\"text-green-400 font-bold text-sm\">+150</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-blue-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">🏆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Nova conquista</div>\n                    <div className=\"text-xs text-text-muted\">01:22:15</div>\n                  </div>\n                </div>\n                <div className=\"text-blue-400 font-bold text-sm\">HACK</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-bg-primary rounded border border-purple-500\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold text-sm\">↑</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-sm\">Level UP</div>\n                    <div className=\"text-xs text-text-muted\">00:45:22</div>\n                  </div>\n                </div>\n                <div className=\"text-purple-400 font-bold text-sm\">LV.15</div>\n              </div>\n            </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Componente Scanner Completo\nconst SimpleScanner: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer } = usePlayer();\n  const [isScanning, setIsScanning] = React.useState(false);\n  const [scanTargets, setScanTargets] = React.useState<any[]>([]);\n  const [scanError, setScanError] = React.useState<string | null>(null);\n  const [specificIP, setSpecificIP] = React.useState('');\n  const [isAdvancedScan, setIsAdvancedScan] = React.useState(false);\n\n  const handleQuickScan = async () => {\n    setIsScanning(true);\n    setScanError(null);\n\n    try {\n      const response = await fetch('/api/scan', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      const data = await response.json();\n\n      if (data.sucesso && data.alvos) {\n        setScanTargets(data.alvos);\n      } else {\n        setScanError(data.mensagem || 'Erro ao escanear alvos');\n      }\n    } catch (error: any) {\n      setScanError(error.message || 'Erro de conexão');\n    } finally {\n      setIsScanning(false);\n    }\n  };\n\n  const handleAdvancedScan = async () => {\n    if (!specificIP.trim()) {\n      setScanError('Digite um IP válido');\n      return;\n    }\n\n    setIsAdvancedScan(true);\n    setScanError(null);\n\n    try {\n      const response = await fetch(`/api/scan/ip/${specificIP}`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      const data = await response.json();\n\n      if (data.sucesso && data.alvo) {\n        setScanTargets([data.alvo]);\n      } else {\n        setScanError(data.mensagem || 'IP não encontrado');\n      }\n    } catch (error: any) {\n      setScanError(error.message || 'Erro de conexão');\n    } finally {\n      setIsAdvancedScan(false);\n    }\n  };\n\n  const handleExploit = async (target: any) => {\n    try {\n      const response = await fetch(`/api/alvo/${target.ip}/exploit`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      const data = await response.json();\n\n      if (data.sucesso) {\n        alert(`Exploit bem-sucedido! ${data.mensagem}`);\n      } else {\n        alert(`Exploit falhou: ${data.mensagem}`);\n      }\n    } catch (error: any) {\n      alert(`Erro no exploit: ${error.message}`);\n    }\n  };\n\n  const playerCPU = currentPlayer?.cpu || 1;\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary\">\n        <div className=\"p-4 border-b border-cyber-primary\">\n          <h1 className=\"text-xl font-bold cyber-text\">🔍 Scanner de Rede</h1>\n          <p className=\"text-xs text-text-muted\">Sistema de Reconhecimento - Criptografado</p>\n        </div>\n\n        <div className=\"p-4 space-y-4\">\n          {/* Scan Rápido */}\n          <div className=\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\">\n            <h3 className=\"text-lg font-semibold mb-3 cyber-text\">⚡ Scan Rápido</h3>\n            <p className=\"text-sm text-text-muted mb-4\">Encontra alvos aleatórios na rede</p>\n            <button\n              onClick={handleQuickScan}\n              disabled={isScanning}\n              className=\"btn-cyber w-full py-3\"\n            >\n              {isScanning ? 'Escaneando...' : 'Iniciar Scan Rápido'}\n            </button>\n          </div>\n\n          {/* Scan Avançado */}\n          <div className=\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\">\n            <h3 className=\"text-lg font-semibold mb-3 cyber-text\">🎯 Scan Avançado</h3>\n            <p className=\"text-sm text-text-muted mb-4\">Busca por IP específico</p>\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                value={specificIP}\n                onChange={(e) => setSpecificIP(e.target.value)}\n                placeholder=\"*************\"\n                className=\"flex-1 bg-bg-tertiary border border-cyber-primary rounded px-3 py-2 text-white font-mono text-sm\"\n              />\n              <button\n                onClick={handleAdvancedScan}\n                disabled={isAdvancedScan}\n                className=\"btn-cyber px-6\"\n              >\n                {isAdvancedScan ? '...' : 'Scan'}\n              </button>\n            </div>\n          </div>\n\n          {/* Erro */}\n          {scanError && (\n            <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n              <p className=\"text-red-300 text-sm font-mono\">❌ {scanError}</p>\n            </div>\n          )}\n\n          {/* Resultados */}\n          {scanTargets.length > 0 && (\n            <div className=\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\">\n              <h3 className=\"text-lg font-semibold mb-4 cyber-text\">📊 Alvos Encontrados</h3>\n              <div className=\"space-y-3\">\n                {scanTargets.map((target, index) => {\n                  const canExploit = playerCPU > (target.firewall || 1);\n\n                  return (\n                    <div key={index} className=\"bg-bg-tertiary rounded-lg p-4 border border-blue-500\">\n                      <div className=\"flex justify-between items-start mb-3\">\n                        <div>\n                          <h4 className=\"font-bold text-blue-400\">{target.nick}</h4>\n                          <p className=\"text-xs text-text-muted font-mono\">IP: {target.ip}</p>\n                          <p className=\"text-xs text-text-muted\">Nível: {target.nivel}</p>\n                        </div>\n                        <div className=\"text-right\">\n                          <p className=\"text-xs text-text-muted\">Firewall: {target.firewall || 1}</p>\n                          <p className=\"text-xs text-text-muted\">Dinheiro: ${(target.dinheiro || 0).toLocaleString()}</p>\n                        </div>\n                      </div>\n\n                      <div className=\"flex justify-between items-center\">\n                        <div className=\"text-xs\">\n                          <span className={`px-2 py-1 rounded ${\n                            canExploit ? 'bg-green-600 text-green-100' : 'bg-red-600 text-red-100'\n                          }`}>\n                            {canExploit ? 'VULNERÁVEL' : 'PROTEGIDO'}\n                          </span>\n                        </div>\n\n                        <button\n                          onClick={() => handleExploit(target)}\n                          disabled={!canExploit}\n                          className={`px-4 py-2 rounded font-semibold text-sm ${\n                            canExploit\n                              ? 'bg-green-600 hover:bg-green-700 text-white'\n                              : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                          }`}\n                        >\n                          {canExploit ? 'EXPLOITAR' : 'CPU INSUFICIENTE'}\n                        </button>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          )}\n\n          {/* Info do Jogador */}\n          <div className=\"bg-bg-primary rounded-lg p-4 border border-cyber-primary\">\n            <h3 className=\"text-sm font-semibold mb-2 cyber-text\">💻 Seu Sistema</h3>\n            <div className=\"grid grid-cols-2 gap-2 text-xs\">\n              <div>CPU: <span className=\"text-cyber-primary\">{playerCPU}</span></div>\n              <div>Status: <span className=\"text-green-400\">ONLINE</span></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nconst SimpleChat: React.FC = () => {\n  const { messages, isLoading: isLoadingMessages, loadMessages, sendMessage, isSending } = useChat();\n  const [newMessage, setNewMessage] = React.useState('');\n\n  useEffect(() => {\n    if (messages.length === 0 && !isLoadingMessages) {\n      console.log('SimpleChat - Carregando mensagens...');\n      loadMessages();\n    }\n  }, [messages.length, isLoadingMessages, loadMessages]);\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (newMessage.trim() && !isSending) {\n      await sendMessage(newMessage.trim());\n      setNewMessage('');\n    }\n  };\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"cyber-border rounded-lg bg-gradient-to-b from-bg-secondary to-bg-tertiary\">\n        <div className=\"p-4 border-b border-cyber-primary\">\n          <h1 className=\"text-xl font-bold cyber-text\">💬 Terminal de Chat</h1>\n          <p className=\"text-xs text-text-muted\">Canal Global - Criptografado</p>\n        </div>\n\n        {/* Área de mensagens - Estilo Terminal */}\n        <div className=\"p-4\">\n          <div className=\"bg-black rounded-lg p-3 h-80 overflow-y-auto mb-4 border border-cyber-primary\">\n            {isLoadingMessages ? (\n              <div className=\"text-center text-cyber-primary\">\n                <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-primary mx-auto mb-2\"></div>\n                <span className=\"text-xs font-mono\">CARREGANDO DADOS...</span>\n              </div>\n            ) : messages.length === 0 ? (\n              <div className=\"text-center text-cyber-primary\">\n                <div className=\"text-2xl mb-2\">💬</div>\n                <p className=\"text-xs font-mono\">CANAL VAZIO - AGUARDANDO TRANSMISSÃO</p>\n              </div>\n            ) : (\n              <div className=\"space-y-2\">\n                {messages.map((message) => (\n                  <div key={message.id} className=\"border-l-2 border-cyber-primary pl-3 py-1\">\n                    <div className=\"flex items-center space-x-2 mb-1\">\n                      <span className=\"text-cyber-primary font-mono text-xs\">\n                        [{new Date(message.timestamp).toLocaleTimeString()}]\n                      </span>\n                      <span className=\"text-blue-400 font-mono text-xs font-bold\">\n                        {message.usuario}:\n                      </span>\n                    </div>\n                    <p className=\"text-white font-mono text-sm pl-2\">{message.mensagem}</p>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Formulário de envio - Estilo Terminal */}\n          <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n            <input\n              type=\"text\"\n              value={newMessage}\n              onChange={(e) => setNewMessage(e.target.value)}\n              placeholder=\"> Digite comando...\"\n              className=\"flex-1 px-3 py-2 bg-black border border-cyber-primary rounded font-mono text-cyber-primary text-sm focus:outline-none focus:border-cyber-secondary placeholder-gray-500\"\n              disabled={isSending}\n            />\n            <button\n              type=\"submit\"\n              disabled={!newMessage.trim() || isSending}\n              className={`px-4 py-2 rounded font-mono text-sm transition-all ${\n                !newMessage.trim() || isSending\n                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'\n                  : 'bg-cyber-primary text-black hover:bg-cyber-secondary font-bold'\n              }`}\n            >\n              {isSending ? '...' : 'SEND'}\n            </button>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Navegação Estilo Celular/Jogo\nconst SimpleNavigation: React.FC = () => {\n  const currentPath = window.location.pathname;\n\n  return (\n    <div className=\"bg-bg-primary\">\n      {/* Header estilo celular */}\n      <div className=\"bg-gradient-to-r from-blue-900 to-purple-900 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-cyber-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-black font-bold text-lg\">S</span>\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold cyber-text\">SHACK</h1>\n              <p className=\"text-xs text-text-muted\">Web Terminal</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"status-online\"></div>\n            <span className=\"text-xs text-green-400\">ONLINE</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Menu de navegação estilo celular */}\n      <div className=\"bg-bg-secondary border-b border-cyber-primary\">\n        <div className=\"flex\">\n          <a\n            href=\"/game\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath === '/game' || currentPath === '/game/'\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">🏠</div>\n            <div>Dashboard</div>\n          </a>\n          <a\n            href=\"/game/scanner\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath.includes('/scanner')\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">🔍</div>\n            <div>Scanner</div>\n          </a>\n          <a\n            href=\"/game/chat\"\n            className={`flex-1 py-3 px-4 text-center text-sm font-medium transition-all ${\n              currentPath.includes('/chat')\n                ? 'bg-cyber-primary text-black border-b-2 border-cyber-primary'\n                : 'text-text-muted hover:text-cyber-primary hover:bg-bg-tertiary'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">💬</div>\n            <div>Chat</div>\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Página Principal do Jogo Simplificada\nconst SimpleGamePage: React.FC = () => {\n  console.log('SimpleGamePage - Renderizando...');\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary\">\n      {/* Container estilo celular */}\n      <div className=\"max-w-md mx-auto bg-bg-primary min-h-screen relative\">\n        {/* Simulação de tela de celular */}\n        <div className=\"bg-gradient-to-b from-bg-primary to-bg-secondary min-h-screen\">\n          <SimpleNavigation />\n\n          {/* Conteúdo principal */}\n          <div className=\"pb-4\">\n            <Routes>\n              <Route path=\"/\" element={<SimpleDashboard />} />\n              <Route path=\"/scanner\" element={<SimpleScanner />} />\n              <Route path=\"/chat\" element={<SimpleChat />} />\n              <Route path=\"*\" element={<SimpleDashboard />} />\n            </Routes>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleGamePage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CAChD,OAASC,aAAa,KAAQ,2BAA2B,CACzD,OAASC,SAAS,KAAQ,uBAAuB,CACjD,OAASC,OAAO,KAAQ,qBAAqB,CAE7C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAAEC,IAAK,CAAC,CAAGR,aAAa,CAAC,CAAC,CAChC,KAAM,CAAES,aAAa,CAAEC,eAAe,CAAEC,cAAc,CAAEC,aAAc,CAAC,CAAGX,SAAS,CAAC,CAAC,CAErFJ,SAAS,CAAC,IAAM,CACd,GAAI,CAACe,aAAa,EAAI,CAACF,eAAe,CAAE,CACtCG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC,CAC/DH,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAACC,aAAa,CAAEF,eAAe,CAAEC,cAAc,CAAC,CAAC,CAEpD;AACA,KAAM,CAAAI,UAAU,CAAGN,aAAa,EAAI,CAClCO,MAAM,CAAE,IAAI,CACZC,KAAK,CAAE,EAAE,CACTC,UAAU,CAAE,EAAE,CACdC,OAAO,CAAE,CACX,CAAC,CAED,mBACEb,KAAA,QAAKc,SAAS,CAAC,KAAK,CAAAC,QAAA,eAElBjB,IAAA,QAAKgB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBf,KAAA,QAAKc,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDf,KAAA,QAAAe,QAAA,eACEjB,IAAA,OAAIgB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,oBAE9C,CAAI,CAAC,cACLf,KAAA,MAAGc,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EAAC,YAC3B,cAAAjB,IAAA,SAAMgB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAE,CAAAb,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEc,IAAI,GAAI,SAAS,CAAO,CAAC,EAC9E,CAAC,EACD,CAAC,CACLZ,eAAe,eACdN,IAAA,QAAKgB,SAAS,CAAC,mEAAmE,CAAM,CACzF,EACE,CAAC,CACH,CAAC,cAGNd,KAAA,QAAKc,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBf,KAAA,QAAKc,SAAS,CAAC,+EAA+E,CAAAC,QAAA,eAC1FjB,IAAA,OAAIgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,gCAAoB,CAAI,CAAC,cAC/Ef,KAAA,QAAKc,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCjB,IAAA,QAAKgB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cACnEf,KAAA,QAAKc,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjB,IAAA,QAAKgB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC7CX,eAAe,CAAG,KAAK,CAAGK,UAAU,CAACC,MAAM,CAACO,cAAc,CAAC,CAAC,CAC1D,CAAC,cACNnB,IAAA,QAAKgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,QAAM,CAAK,CAAC,EAChD,CAAC,CACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,uDAAuD,CAAAC,QAAA,cACpEf,KAAA,QAAKc,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjB,IAAA,QAAKgB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC9CX,eAAe,CAAG,KAAK,CAAGK,UAAU,CAACE,KAAK,CACxC,CAAC,cACNb,IAAA,QAAKgB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,UAAK,CAAK,CAAC,EAChD,CAAC,CACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cACrEf,KAAA,QAAKc,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjB,IAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/CX,eAAe,CAAG,KAAK,CAAGK,UAAU,CAACG,UAAU,CAC7C,CAAC,cACNd,IAAA,QAAKgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,YAAU,CAAK,CAAC,EACtD,CAAC,CACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cACrEf,KAAA,QAAKc,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjB,IAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/CX,eAAe,CAAG,KAAK,CAAGK,UAAU,CAACI,OAAO,CAC1C,CAAC,cACNf,IAAA,QAAKgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,SAAO,CAAK,CAAC,EACnD,CAAC,CACH,CAAC,EACH,CAAC,EACL,CAAC,cAGNf,KAAA,QAAKc,SAAS,CAAC,+EAA+E,CAAAC,QAAA,eAC1FjB,IAAA,OAAIgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,yBAAe,CAAI,CAAC,cAC1Ef,KAAA,QAAKc,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCf,KAAA,WACEkB,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,eAAgB,CACtDP,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAElCjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,SAAO,CAAK,CAAC,EACZ,CAAC,cACTf,KAAA,WACEkB,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,YAAa,CACnDP,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAElCjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,MAAI,CAAK,CAAC,EACT,CAAC,cACTf,KAAA,WAAQc,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACxCjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,MAAI,CAAK,CAAC,EACT,CAAC,cACTf,KAAA,WAAQc,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACxCjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,QAAM,CAAK,CAAC,EACX,CAAC,EACN,CAAC,EACL,CAAC,cAGNf,KAAA,QAAKc,SAAS,CAAC,+EAA+E,CAAAC,QAAA,eAC1FjB,IAAA,OAAIgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,6BAAiB,CAAI,CAAC,cAC5Ef,KAAA,QAAKc,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBf,KAAA,QAAKc,SAAS,CAAC,qFAAqF,CAAAC,QAAA,eAClGf,KAAA,QAAKc,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjB,IAAA,QAAKgB,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cACjFjB,IAAA,SAAMgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,CACpD,CAAC,cACNf,KAAA,QAAAe,QAAA,eACEjB,IAAA,QAAKgB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,eAAa,CAAK,CAAC,cACxDjB,IAAA,QAAKgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,EACpD,CAAC,EACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,MAAI,CAAK,CAAC,EACzD,CAAC,cAENf,KAAA,QAAKc,SAAS,CAAC,oFAAoF,CAAAC,QAAA,eACjGf,KAAA,QAAKc,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjB,IAAA,QAAKgB,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChFjB,IAAA,SAAMgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,CACrD,CAAC,cACNf,KAAA,QAAAe,QAAA,eACEjB,IAAA,QAAKgB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,gBAAc,CAAK,CAAC,cACzDjB,IAAA,QAAKgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,EACpD,CAAC,EACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAC,MAAI,CAAK,CAAC,EACxD,CAAC,cAENf,KAAA,QAAKc,SAAS,CAAC,sFAAsF,CAAAC,QAAA,eACnGf,KAAA,QAAKc,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjB,IAAA,QAAKgB,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cAClFjB,IAAA,SAAMgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,CACpD,CAAC,cACNf,KAAA,QAAAe,QAAA,eACEjB,IAAA,QAAKgB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,cACnDjB,IAAA,QAAKgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,EACpD,CAAC,EACH,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,OAAK,CAAK,CAAC,EAC3D,CAAC,EACH,CAAC,EACL,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAO,aAAuB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAAEpB,IAAK,CAAC,CAAGR,aAAa,CAAC,CAAC,CAChC,KAAM,CAAES,aAAc,CAAC,CAAGR,SAAS,CAAC,CAAC,CACrC,KAAM,CAAC4B,UAAU,CAAEC,aAAa,CAAC,CAAGlC,KAAK,CAACmC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGrC,KAAK,CAACmC,QAAQ,CAAQ,EAAE,CAAC,CAC/D,KAAM,CAACG,SAAS,CAAEC,YAAY,CAAC,CAAGvC,KAAK,CAACmC,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAACK,UAAU,CAAEC,aAAa,CAAC,CAAGzC,KAAK,CAACmC,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACO,cAAc,CAAEC,iBAAiB,CAAC,CAAG3C,KAAK,CAACmC,QAAQ,CAAC,KAAK,CAAC,CAEjE,KAAM,CAAAS,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClCV,aAAa,CAAC,IAAI,CAAC,CACnBK,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,WAAW,CAAE,CACxCC,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,eAAe,CAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE,CAC1D,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,EAAIF,IAAI,CAACG,KAAK,CAAE,CAC9BjB,cAAc,CAACc,IAAI,CAACG,KAAK,CAAC,CAC5B,CAAC,IAAM,CACLf,YAAY,CAACY,IAAI,CAACI,QAAQ,EAAI,wBAAwB,CAAC,CACzD,CACF,CAAE,MAAOC,KAAU,CAAE,CACnBjB,YAAY,CAACiB,KAAK,CAACC,OAAO,EAAI,iBAAiB,CAAC,CAClD,CAAC,OAAS,CACRvB,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAwB,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CAAClB,UAAU,CAACmB,IAAI,CAAC,CAAC,CAAE,CACtBpB,YAAY,CAAC,qBAAqB,CAAC,CACnC,OACF,CAEAI,iBAAiB,CAAC,IAAI,CAAC,CACvBJ,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,gBAAgBN,UAAU,EAAE,CAAE,CACzDO,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,eAAe,CAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE,CAC1D,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,EAAIF,IAAI,CAACS,IAAI,CAAE,CAC7BvB,cAAc,CAAC,CAACc,IAAI,CAACS,IAAI,CAAC,CAAC,CAC7B,CAAC,IAAM,CACLrB,YAAY,CAACY,IAAI,CAACI,QAAQ,EAAI,mBAAmB,CAAC,CACpD,CACF,CAAE,MAAOC,KAAU,CAAE,CACnBjB,YAAY,CAACiB,KAAK,CAACC,OAAO,EAAI,iBAAiB,CAAC,CAClD,CAAC,OAAS,CACRd,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CACF,CAAC,CAED,KAAM,CAAAkB,aAAa,CAAG,KAAO,CAAAC,MAAW,EAAK,CAC3C,GAAI,CACF,KAAM,CAAAjB,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,aAAagB,MAAM,CAACC,EAAE,UAAU,CAAE,CAC7DhB,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,eAAe,CAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE,CAC1D,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,CAAE,CAChBW,KAAK,CAAC,yBAAyBb,IAAI,CAACI,QAAQ,EAAE,CAAC,CACjD,CAAC,IAAM,CACLS,KAAK,CAAC,mBAAmBb,IAAI,CAACI,QAAQ,EAAE,CAAC,CAC3C,CACF,CAAE,MAAOC,KAAU,CAAE,CACnBQ,KAAK,CAAC,oBAAoBR,KAAK,CAACC,OAAO,EAAE,CAAC,CAC5C,CACF,CAAC,CAED,KAAM,CAAAQ,SAAS,CAAG,CAAApD,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEqD,GAAG,GAAI,CAAC,CAEzC,mBACE1D,IAAA,QAAKgB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBf,KAAA,QAAKc,SAAS,CAAC,2EAA2E,CAAAC,QAAA,eACxFf,KAAA,QAAKc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDjB,IAAA,OAAIgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,8BAAkB,CAAI,CAAC,cACpEjB,IAAA,MAAGgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,2CAAyC,CAAG,CAAC,EACjF,CAAC,cAENf,KAAA,QAAKc,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE5Bf,KAAA,QAAKc,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEjB,IAAA,OAAIgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,uBAAa,CAAI,CAAC,cACxEjB,IAAA,MAAGgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,sCAAiC,CAAG,CAAC,cACjFjB,IAAA,WACEoB,OAAO,CAAEgB,eAAgB,CACzBuB,QAAQ,CAAElC,UAAW,CACrBT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAEhCQ,UAAU,CAAG,eAAe,CAAG,qBAAqB,CAC/C,CAAC,EACN,CAAC,cAGNvB,KAAA,QAAKc,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEjB,IAAA,OAAIgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,+BAAgB,CAAI,CAAC,cAC3EjB,IAAA,MAAGgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,4BAAuB,CAAG,CAAC,cACvEf,KAAA,QAAKc,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjB,IAAA,UACE4D,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE7B,UAAW,CAClB8B,QAAQ,CAAGC,CAAC,EAAK9B,aAAa,CAAC8B,CAAC,CAACT,MAAM,CAACO,KAAK,CAAE,CAC/CG,WAAW,CAAC,eAAe,CAC3BhD,SAAS,CAAC,kGAAkG,CAC7G,CAAC,cACFhB,IAAA,WACEoB,OAAO,CAAE8B,kBAAmB,CAC5BS,QAAQ,CAAEzB,cAAe,CACzBlB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAEzBiB,cAAc,CAAG,KAAK,CAAG,MAAM,CAC1B,CAAC,EACN,CAAC,EACH,CAAC,CAGLJ,SAAS,eACR9B,IAAA,QAAKgB,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC9Df,KAAA,MAAGc,SAAS,CAAC,gCAAgC,CAAAC,QAAA,EAAC,SAAE,CAACa,SAAS,EAAI,CAAC,CAC5D,CACN,CAGAF,WAAW,CAACqC,MAAM,CAAG,CAAC,eACrB/D,KAAA,QAAKc,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEjB,IAAA,OAAIgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,gCAAoB,CAAI,CAAC,cAC/EjB,IAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBW,WAAW,CAACsC,GAAG,CAAC,CAACZ,MAAM,CAAEa,KAAK,GAAK,CAClC,KAAM,CAAAC,UAAU,CAAGX,SAAS,EAAIH,MAAM,CAACe,QAAQ,EAAI,CAAC,CAAC,CAErD,mBACEnE,KAAA,QAAiBc,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eAC/Ef,KAAA,QAAKc,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDf,KAAA,QAAAe,QAAA,eACEjB,IAAA,OAAIgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEqC,MAAM,CAACpC,IAAI,CAAK,CAAC,cAC1DhB,KAAA,MAAGc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,MAAI,CAACqC,MAAM,CAACC,EAAE,EAAI,CAAC,cACpErD,KAAA,MAAGc,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EAAC,YAAO,CAACqC,MAAM,CAACzC,KAAK,EAAI,CAAC,EAC7D,CAAC,cACNX,KAAA,QAAKc,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBf,KAAA,MAAGc,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EAAC,YAAU,CAACqC,MAAM,CAACe,QAAQ,EAAI,CAAC,EAAI,CAAC,cAC3EnE,KAAA,MAAGc,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EAAC,aAAW,CAAC,CAACqC,MAAM,CAACgB,QAAQ,EAAI,CAAC,EAAEnD,cAAc,CAAC,CAAC,EAAI,CAAC,EAC5F,CAAC,EACH,CAAC,cAENjB,KAAA,QAAKc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDjB,IAAA,QAAKgB,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtBjB,IAAA,SAAMgB,SAAS,CAAE,qBACfoD,UAAU,CAAG,6BAA6B,CAAG,yBAAyB,EACrE,CAAAnD,QAAA,CACAmD,UAAU,CAAG,YAAY,CAAG,WAAW,CACpC,CAAC,CACJ,CAAC,cAENpE,IAAA,WACEoB,OAAO,CAAEA,CAAA,GAAMiC,aAAa,CAACC,MAAM,CAAE,CACrCK,QAAQ,CAAE,CAACS,UAAW,CACtBpD,SAAS,CAAE,2CACToD,UAAU,CACN,4CAA4C,CAC5C,8CAA8C,EACjD,CAAAnD,QAAA,CAEFmD,UAAU,CAAG,WAAW,CAAG,kBAAkB,CACxC,CAAC,EACN,CAAC,GAjCED,KAkCL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,EACH,CACN,cAGDjE,KAAA,QAAKc,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEjB,IAAA,OAAIgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,0BAAc,CAAI,CAAC,cACzEf,KAAA,QAAKc,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7Cf,KAAA,QAAAe,QAAA,EAAK,OAAK,cAAAjB,IAAA,SAAMgB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEwC,SAAS,CAAO,CAAC,EAAK,CAAC,cACvEvD,KAAA,QAAAe,QAAA,EAAK,UAAQ,cAAAjB,IAAA,SAAMgB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,EAAK,CAAC,EAC9D,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,KAAM,CAAAsD,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAEC,QAAQ,CAAEC,SAAS,CAAEC,iBAAiB,CAAEC,YAAY,CAAEC,WAAW,CAAEC,SAAU,CAAC,CAAG/E,OAAO,CAAC,CAAC,CAClG,KAAM,CAACgF,UAAU,CAAEC,aAAa,CAAC,CAAGvF,KAAK,CAACmC,QAAQ,CAAC,EAAE,CAAC,CAEtDlC,SAAS,CAAC,IAAM,CACd,GAAI+E,QAAQ,CAACP,MAAM,GAAK,CAAC,EAAI,CAACS,iBAAiB,CAAE,CAC/CjE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC,CACnDiE,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAAE,CAACH,QAAQ,CAACP,MAAM,CAAES,iBAAiB,CAAEC,YAAY,CAAC,CAAC,CAEtD,KAAM,CAAAK,iBAAiB,CAAG,KAAO,CAAAjB,CAAkB,EAAK,CACtDA,CAAC,CAACkB,cAAc,CAAC,CAAC,CAClB,GAAIH,UAAU,CAAC3B,IAAI,CAAC,CAAC,EAAI,CAAC0B,SAAS,CAAE,CACnC,KAAM,CAAAD,WAAW,CAACE,UAAU,CAAC3B,IAAI,CAAC,CAAC,CAAC,CACpC4B,aAAa,CAAC,EAAE,CAAC,CACnB,CACF,CAAC,CAED,mBACE/E,IAAA,QAAKgB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBf,KAAA,QAAKc,SAAS,CAAC,2EAA2E,CAAAC,QAAA,eACxFf,KAAA,QAAKc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDjB,IAAA,OAAIgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,+BAAmB,CAAI,CAAC,cACrEjB,IAAA,MAAGgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,8BAA4B,CAAG,CAAC,EACpE,CAAC,cAGNf,KAAA,QAAKc,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBjB,IAAA,QAAKgB,SAAS,CAAC,+EAA+E,CAAAC,QAAA,CAC3FyD,iBAAiB,cAChBxE,KAAA,QAAKc,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CjB,IAAA,QAAKgB,SAAS,CAAC,gFAAgF,CAAM,CAAC,cACtGhB,IAAA,SAAMgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,EAC3D,CAAC,CACJuD,QAAQ,CAACP,MAAM,GAAK,CAAC,cACvB/D,KAAA,QAAKc,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CjB,IAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACvCjB,IAAA,MAAGgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,yCAAoC,CAAG,CAAC,EACtE,CAAC,cAENjB,IAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBuD,QAAQ,CAACN,GAAG,CAAEjB,OAAO,eACpB/C,KAAA,QAAsBc,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACzEf,KAAA,QAAKc,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/Cf,KAAA,SAAMc,SAAS,CAAC,sCAAsC,CAAAC,QAAA,EAAC,GACpD,CAAC,GAAI,CAAAiE,IAAI,CAACjC,OAAO,CAACkC,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAC,GACrD,EAAM,CAAC,cACPlF,KAAA,SAAMc,SAAS,CAAC,2CAA2C,CAAAC,QAAA,EACxDgC,OAAO,CAACoC,OAAO,CAAC,GACnB,EAAM,CAAC,EACJ,CAAC,cACNrF,IAAA,MAAGgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEgC,OAAO,CAACF,QAAQ,CAAI,CAAC,GAT/DE,OAAO,CAACqC,EAUb,CACN,CAAC,CACC,CACN,CACE,CAAC,cAGNpF,KAAA,SAAMqF,QAAQ,CAAEP,iBAAkB,CAAChE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC3DjB,IAAA,UACE4D,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEiB,UAAW,CAClBhB,QAAQ,CAAGC,CAAC,EAAKgB,aAAa,CAAChB,CAAC,CAACT,MAAM,CAACO,KAAK,CAAE,CAC/CG,WAAW,CAAC,qBAAqB,CACjChD,SAAS,CAAC,yKAAyK,CACnL2C,QAAQ,CAAEkB,SAAU,CACrB,CAAC,cACF7E,IAAA,WACE4D,IAAI,CAAC,QAAQ,CACbD,QAAQ,CAAE,CAACmB,UAAU,CAAC3B,IAAI,CAAC,CAAC,EAAI0B,SAAU,CAC1C7D,SAAS,CAAE,sDACT,CAAC8D,UAAU,CAAC3B,IAAI,CAAC,CAAC,EAAI0B,SAAS,CAC3B,8CAA8C,CAC9C,gEAAgE,EACnE,CAAA5D,QAAA,CAEF4D,SAAS,CAAG,KAAK,CAAG,MAAM,CACrB,CAAC,EACL,CAAC,EACJ,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAW,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAAAC,WAAW,CAAGpE,MAAM,CAACC,QAAQ,CAACoE,QAAQ,CAE5C,mBACExF,KAAA,QAAKc,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE5BjB,IAAA,QAAKgB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAC/Df,KAAA,QAAKc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDf,KAAA,QAAKc,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CjB,IAAA,QAAKgB,SAAS,CAAC,wEAAwE,CAAAC,QAAA,cACrFjB,IAAA,SAAMgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,CACpD,CAAC,cACNf,KAAA,QAAAe,QAAA,eACEjB,IAAA,OAAIgB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,cACvDjB,IAAA,MAAGgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,EACpD,CAAC,EACH,CAAC,cACNf,KAAA,QAAKc,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CjB,IAAA,QAAKgB,SAAS,CAAC,eAAe,CAAM,CAAC,cACrChB,IAAA,SAAMgB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,EACnD,CAAC,EACH,CAAC,CACH,CAAC,cAGNjB,IAAA,QAAKgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5Df,KAAA,QAAKc,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBf,KAAA,MACEqB,IAAI,CAAC,OAAO,CACZP,SAAS,CAAE,mEACTyE,WAAW,GAAK,OAAO,EAAIA,WAAW,GAAK,QAAQ,CAC/C,6DAA6D,CAC7D,+DAA+D,EAClE,CAAAxE,QAAA,eAEHjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,WAAS,CAAK,CAAC,EACnB,CAAC,cACJf,KAAA,MACEqB,IAAI,CAAC,eAAe,CACpBP,SAAS,CAAE,mEACTyE,WAAW,CAACE,QAAQ,CAAC,UAAU,CAAC,CAC5B,6DAA6D,CAC7D,+DAA+D,EAClE,CAAA1E,QAAA,eAEHjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,SAAO,CAAK,CAAC,EACjB,CAAC,cACJf,KAAA,MACEqB,IAAI,CAAC,YAAY,CACjBP,SAAS,CAAE,mEACTyE,WAAW,CAACE,QAAQ,CAAC,OAAO,CAAC,CACzB,6DAA6D,CAC7D,+DAA+D,EAClE,CAAA1E,QAAA,eAEHjB,IAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCjB,IAAA,QAAAiB,QAAA,CAAK,MAAI,CAAK,CAAC,EACd,CAAC,EACD,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA2E,cAAwB,CAAGA,CAAA,GAAM,CACrCnF,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAE/C,mBACEV,IAAA,QAAKgB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,cAE3DjB,IAAA,QAAKgB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cAEnEf,KAAA,QAAKc,SAAS,CAAC,+DAA+D,CAAAC,QAAA,eAC5EjB,IAAA,CAACwF,gBAAgB,GAAE,CAAC,cAGpBxF,IAAA,QAAKgB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBf,KAAA,CAACR,MAAM,EAAAuB,QAAA,eACLjB,IAAA,CAACL,KAAK,EAACkG,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE9F,IAAA,CAACG,eAAe,GAAE,CAAE,CAAE,CAAC,cAChDH,IAAA,CAACL,KAAK,EAACkG,IAAI,CAAC,UAAU,CAACC,OAAO,cAAE9F,IAAA,CAACwB,aAAa,GAAE,CAAE,CAAE,CAAC,cACrDxB,IAAA,CAACL,KAAK,EAACkG,IAAI,CAAC,OAAO,CAACC,OAAO,cAAE9F,IAAA,CAACuE,UAAU,GAAE,CAAE,CAAE,CAAC,cAC/CvE,IAAA,CAACL,KAAK,EAACkG,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE9F,IAAA,CAACG,eAAe,GAAE,CAAE,CAAE,CAAC,EAC1C,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAyF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}