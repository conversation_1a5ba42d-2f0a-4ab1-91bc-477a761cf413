#!/usr/bin/env python3
"""
Teste para verificar se o JWT está funcionando corretamente
"""

import jwt
import datetime
import os
from datetime import timezone, timedelta

# Mesma chave usada no sistema
JWT_SECRET = os.environ.get('SECRET_KEY', 'shack-game-secret-key-2024-csrf-protection-enabled')

def test_jwt():
    print("=== TESTE JWT ===")
    print(f"Secret Key: {JWT_SECRET}")
    
    # Dados de teste
    user_data = {
        'uid': '6c85a9cf-760c-4e6b-86f0-e74d57e61ce4',
        'nick': 'zakedev'
    }
    
    # Gerar token (como no auth_routes.py)
    print("\n1. Gerando token...")
    payload = {
        'uid': user_data['uid'],
        'nick': user_data.get('nick', ''),
        'exp': datetime.datetime.now(timezone.utc) + timedelta(hours=24)
    }
    
    token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')
    print(f"Token gerado: {token}")
    print(f"Tipo do token: {type(token)}")
    
    # Validar token (como no react_api_routes.py)
    print("\n2. Validando token...")
    try:
        decoded = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        print(f"Token válido! Dados: {decoded}")
        print(f"UID extraído: {decoded['uid']}")
        return True
    except jwt.ExpiredSignatureError:
        print("❌ Token expirado")
        return False
    except jwt.InvalidTokenError as e:
        print(f"❌ Token inválido: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

def test_token_from_backend():
    """Testa o token que foi gerado pelo backend"""
    print("\n=== TESTE TOKEN DO BACKEND ===")
    
    # Token que apareceu nos logs do backend
    backend_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiI2Yzg1YTljZi03NjBjLTRlNmItODZmMC1lNzRkNTdlNjFjZTQiLCJuaWNrIjoiemFrZWRldiIsImV4cCI6MTc1MjMzMzgzNn0.WrPkmzyJAC9WF9ReUX9zsRHaFDoHLaxfQGmy2Dmt9zg"
    
    print(f"Token do backend: {backend_token}")
    
    try:
        decoded = jwt.decode(backend_token, JWT_SECRET, algorithms=['HS256'])
        print(f"✅ Token do backend é válido! Dados: {decoded}")
        
        # Verificar expiração
        exp_timestamp = decoded['exp']
        exp_datetime = datetime.datetime.fromtimestamp(exp_timestamp, tz=timezone.utc)
        now = datetime.datetime.now(timezone.utc)
        
        print(f"Expira em: {exp_datetime}")
        print(f"Agora: {now}")
        print(f"Válido por mais: {exp_datetime - now}")
        
        return True
    except jwt.ExpiredSignatureError:
        print("❌ Token do backend expirado")
        return False
    except jwt.InvalidTokenError as e:
        print(f"❌ Token do backend inválido: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

if __name__ == "__main__":
    print("Testando geração e validação de JWT...")
    
    # Teste 1: Gerar e validar novo token
    success1 = test_jwt()
    
    # Teste 2: Validar token do backend
    success2 = test_token_from_backend()
    
    print(f"\n=== RESULTADO ===")
    print(f"Teste geração: {'✅ OK' if success1 else '❌ FALHOU'}")
    print(f"Teste backend: {'✅ OK' if success2 else '❌ FALHOU'}")
    
    if success1 and success2:
        print("🎉 JWT está funcionando corretamente!")
    else:
        print("⚠️ Há problemas com o JWT")
