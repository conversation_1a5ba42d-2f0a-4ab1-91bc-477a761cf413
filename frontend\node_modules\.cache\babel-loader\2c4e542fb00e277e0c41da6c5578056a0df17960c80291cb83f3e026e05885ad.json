{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\BankPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BankPage = () => {\n  _s();\n  var _currentPlayer$dinhei;\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    currentPlayer,\n    loadPlayerData\n  } = usePlayer();\n  const [bankAccount, setBankAccount] = useState(null);\n  const [transactions, setTransactions] = useState([]);\n  const [selectedTab, setSelectedTab] = useState('deposito');\n\n  // Estados para operações\n  const [amount, setAmount] = useState('');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [actionError, setActionError] = useState(null);\n  const [actionSuccess, setActionSuccess] = useState(null);\n\n  // Estados para carregamento\n  const [isLoading, setIsLoading] = useState(false);\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadBankData();\n      loadTransactions();\n    }\n  }, [isAuthenticated]);\n  const loadBankData = async () => {\n    setIsLoading(true);\n    try {\n      const response = await gameApi.getBankAccount();\n      if (response.sucesso) {\n        setBankAccount(response.conta);\n      } else {\n        // Fallback para dados mockados\n        loadMockBankData();\n      }\n    } catch (error) {\n      console.error('Erro ao carregar dados bancários:', error);\n      loadMockBankData();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const loadMockBankData = () => {\n    if (!currentPlayer) return;\n    const mockAccount = {\n      saldo: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.banco_saldo) || 0,\n      juros_diarios: 0.05,\n      // 5% ao dia\n      ultimo_juros: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n      // 12h atrás\n      nivel_conta: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.banco_nivel) || 1,\n      limite_deposito: 100000 * ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.banco_nivel) || 1),\n      limite_saque: 50000 * ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.banco_nivel) || 1)\n    };\n    setBankAccount(mockAccount);\n  };\n  const loadTransactions = async () => {\n    try {\n      const response = await gameApi.getBankTransactions();\n      if (response.sucesso) {\n        setTransactions(response.transacoes || []);\n      } else {\n        // Fallback para transações mockadas\n        loadMockTransactions();\n      }\n    } catch (error) {\n      console.error('Erro ao carregar transações:', error);\n      loadMockTransactions();\n    }\n  };\n  const loadMockTransactions = () => {\n    const now = new Date();\n    const mockTransactions = [{\n      id: '1',\n      tipo: 'deposito',\n      valor: 10000,\n      descricao: 'Depósito via terminal',\n      timestamp: new Date(now.getTime() - 60 * 60000).toISOString(),\n      saldo_anterior: 5000,\n      saldo_posterior: 15000\n    }, {\n      id: '2',\n      tipo: 'juros',\n      valor: 750,\n      descricao: 'Juros diários (5%)',\n      timestamp: new Date(now.getTime() - 24 * 60 * 60000).toISOString(),\n      saldo_anterior: 15000,\n      saldo_posterior: 15750\n    }, {\n      id: '3',\n      tipo: 'saque',\n      valor: 5000,\n      descricao: 'Saque para carteira',\n      timestamp: new Date(now.getTime() - 48 * 60 * 60000).toISOString(),\n      saldo_anterior: 15750,\n      saldo_posterior: 10750\n    }];\n    setTransactions(mockTransactions);\n  };\n  const handleDeposit = async e => {\n    e.preventDefault();\n    if (!amount.trim() || !bankAccount || !currentPlayer) {\n      setActionError('Valor inválido');\n      return;\n    }\n    const depositAmount = parseInt(amount);\n    if (isNaN(depositAmount) || depositAmount <= 0) {\n      setActionError('Valor deve ser um número positivo');\n      return;\n    }\n    if (depositAmount > ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.dinheiro) || 0)) {\n      setActionError('Saldo insuficiente na carteira');\n      return;\n    }\n    if (depositAmount > bankAccount.limite_deposito) {\n      setActionError(`Limite de depósito: $${bankAccount.limite_deposito.toLocaleString()}`);\n      return;\n    }\n    setIsProcessing(true);\n    setActionError(null);\n    setActionSuccess(null);\n    try {\n      const response = await gameApi.bankDeposit(depositAmount);\n      if (response.sucesso) {\n        setActionSuccess(`Depósito de $${depositAmount.toLocaleString()} realizado com sucesso!`);\n        setAmount('');\n\n        // Recarregar dados\n        await loadPlayerData();\n        await loadBankData();\n        await loadTransactions();\n      } else {\n        setActionError(response.mensagem || 'Erro no depósito');\n      }\n    } catch (error) {\n      setActionError(error.message || 'Erro de conexão');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n  const handleWithdraw = async e => {\n    e.preventDefault();\n    if (!amount.trim() || !bankAccount) {\n      setActionError('Valor inválido');\n      return;\n    }\n    const withdrawAmount = parseInt(amount);\n    if (isNaN(withdrawAmount) || withdrawAmount <= 0) {\n      setActionError('Valor deve ser um número positivo');\n      return;\n    }\n    if (withdrawAmount > bankAccount.saldo) {\n      setActionError('Saldo insuficiente no banco');\n      return;\n    }\n    if (withdrawAmount > bankAccount.limite_saque) {\n      setActionError(`Limite de saque: $${bankAccount.limite_saque.toLocaleString()}`);\n      return;\n    }\n    setIsProcessing(true);\n    setActionError(null);\n    setActionSuccess(null);\n    try {\n      const response = await gameApi.bankWithdraw(withdrawAmount);\n      if (response.sucesso) {\n        setActionSuccess(`Saque de $${withdrawAmount.toLocaleString()} realizado com sucesso!`);\n        setAmount('');\n\n        // Recarregar dados\n        await loadPlayerData();\n        await loadBankData();\n        await loadTransactions();\n      } else {\n        setActionError(response.mensagem || 'Erro no saque');\n      }\n    } catch (error) {\n      setActionError(error.message || 'Erro de conexão');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n  const calculateNextInterest = () => {\n    if (!bankAccount) return {\n      amount: 0,\n      timeLeft: ''\n    };\n    const lastInterest = new Date(bankAccount.ultimo_juros);\n    const nextInterest = new Date(lastInterest.getTime() + 24 * 60 * 60 * 1000); // +24h\n    const now = new Date();\n    const amount = Math.floor(bankAccount.saldo * bankAccount.juros_diarios);\n    if (now >= nextInterest) {\n      return {\n        amount,\n        timeLeft: 'Disponível agora!'\n      };\n    }\n    const diff = nextInterest.getTime() - now.getTime();\n    const hours = Math.floor(diff / (60 * 60 * 1000));\n    const minutes = Math.floor(diff % (60 * 60 * 1000) / (60 * 1000));\n    return {\n      amount,\n      timeLeft: `${hours}h ${minutes}m`\n    };\n  };\n  const getTransactionIcon = tipo => {\n    switch (tipo) {\n      case 'deposito':\n        return '📥';\n      case 'saque':\n        return '📤';\n      case 'juros':\n        return '💰';\n      case 'transferencia':\n        return '🔄';\n      default:\n        return '📝';\n    }\n  };\n  const getTransactionColor = tipo => {\n    switch (tipo) {\n      case 'deposito':\n        return 'text-green-400';\n      case 'saque':\n        return 'text-red-400';\n      case 'juros':\n        return 'text-yellow-400';\n      case 'transferencia':\n        return 'text-blue-400';\n      default:\n        return 'text-gray-400';\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-900 text-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"\\uD83D\\uDD12 Acesso Negado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Voc\\xEA precisa estar logado para acessar o banco\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this);\n  }\n  const nextInterest = calculateNextInterest();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold\",\n            children: \"\\uD83C\\uDFE6 Banco Shack\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Sistema Banc\\xE1rio Seguro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n      children: [actionError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-300 text-sm\",\n          children: [\"\\u274C \", actionError]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this), actionSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-900 border border-green-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-300 text-sm\",\n          children: [\"\\u2705 \", actionSuccess]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Carregando dados banc\\xE1rios...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this) : bankAccount ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4 text-white\",\n            children: \"\\uD83D\\uDCB3 Informa\\xE7\\xF5es da Conta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-green-400\",\n                children: [\"$\", bankAccount.saldo.toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Saldo no Banco\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-blue-400\",\n                children: [\"N\\xEDvel \", bankAccount.nivel_conta]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Conta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 grid grid-cols-2 gap-4 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-400\",\n                children: \"Carteira:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white\",\n                children: [\"$\", (currentPlayer === null || currentPlayer === void 0 ? void 0 : (_currentPlayer$dinhei = currentPlayer.dinheiro) === null || _currentPlayer$dinhei === void 0 ? void 0 : _currentPlayer$dinhei.toLocaleString()) || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-400\",\n                children: \"Juros Di\\xE1rios:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-yellow-400\",\n                children: [(bankAccount.juros_diarios * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 border border-yellow-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-3 text-yellow-400\",\n            children: \"\\uD83D\\uDCB0 Pr\\xF3ximos Juros\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-lg font-bold text-yellow-400\",\n                children: [\"+$\", nextInterest.amount.toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Valor dos juros\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-white\",\n                children: nextInterest.timeLeft\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Tempo restante\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg border border-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex border-b border-gray-700\",\n            children: [{\n              id: 'deposito',\n              name: 'Depósito',\n              icon: '📥'\n            }, {\n              id: 'saque',\n              name: 'Saque',\n              icon: '📤'\n            }, {\n              id: 'historico',\n              name: 'Histórico',\n              icon: '📊'\n            }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedTab(tab.id),\n              className: `flex-1 p-3 text-sm font-medium ${selectedTab === tab.id ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-white'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-1\",\n                children: tab.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 21\n              }, this), tab.name]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4\",\n            children: [selectedTab === 'deposito' && /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleDeposit,\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-300 mb-2\",\n                  children: \"Valor do Dep\\xF3sito\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: amount,\n                  onChange: e => setAmount(e.target.value),\n                  placeholder: \"0\",\n                  min: \"1\",\n                  max: Math.min((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.dinheiro) || 0, bankAccount.limite_deposito),\n                  className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n                  disabled: isProcessing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-400 mt-1\",\n                  children: [\"Limite: $\", bankAccount.limite_deposito.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: isProcessing || !amount.trim(),\n                className: \"w-full py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n                children: isProcessing ? 'Processando...' : 'Depositar'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 19\n            }, this), selectedTab === 'saque' && /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleWithdraw,\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-300 mb-2\",\n                  children: \"Valor do Saque\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: amount,\n                  onChange: e => setAmount(e.target.value),\n                  placeholder: \"0\",\n                  min: \"1\",\n                  max: Math.min(bankAccount.saldo, bankAccount.limite_saque),\n                  className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n                  disabled: isProcessing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-400 mt-1\",\n                  children: [\"Limite: $\", bankAccount.limite_saque.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: isProcessing || !amount.trim(),\n                className: \"w-full py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n                children: isProcessing ? 'Processando...' : 'Sacar'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 19\n            }, this), selectedTab === 'historico' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: transactions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-4xl mb-4\",\n                  children: \"\\uD83D\\uDCCA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-400\",\n                  children: \"Nenhuma transa\\xE7\\xE3o encontrada\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 23\n              }, this) : transactions.map(transaction => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-700 rounded-lg p-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xl\",\n                      children: getTransactionIcon(transaction.tipo)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `font-bold ${getTransactionColor(transaction.tipo)}`,\n                        children: transaction.tipo.charAt(0).toUpperCase() + transaction.tipo.slice(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 472,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: transaction.descricao\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 475,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `font-bold ${getTransactionColor(transaction.tipo)}`,\n                      children: [transaction.tipo === 'saque' ? '-' : '+', \"$\", transaction.valor.toLocaleString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-400\",\n                      children: new Date(transaction.timestamp).toLocaleDateString('pt-BR')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 27\n                }, this)\n              }, transaction.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-4xl mb-4\",\n          children: \"\\uD83C\\uDFE6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Erro ao carregar dados banc\\xE1rios\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"Voltar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 284,\n    columnNumber: 5\n  }, this);\n};\n_s(BankPage, \"zUJvNhBvlUzN9h5iwhroT659oJM=\", false, function () {\n  return [useAuth, usePlayer];\n});\n_c = BankPage;\nexport default BankPage;\nvar _c;\n$RefreshReg$(_c, \"BankPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "usePlayer", "gameApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BankPage", "_s", "_currentPlayer$dinhei", "user", "isAuthenticated", "currentPlayer", "loadPlayerData", "bankAccount", "set<PERSON>ankAccount", "transactions", "setTransactions", "selectedTab", "setSelectedTab", "amount", "setAmount", "isProcessing", "setIsProcessing", "actionError", "setActionError", "actionSuccess", "setActionSuccess", "isLoading", "setIsLoading", "loadBankData", "loadTransactions", "response", "getBankAccount", "sucesso", "conta", "loadMockBankData", "error", "console", "mockAccount", "saldo", "banco_saldo", "juros_diarios", "ultimo_juros", "Date", "now", "toISOString", "nivel_conta", "banco_nivel", "limite_deposito", "limite_saque", "getBankTransactions", "transacoes", "loadMockTransactions", "mockTransactions", "id", "tipo", "valor", "descricao", "timestamp", "getTime", "saldo_anterior", "saldo_posterior", "handleDeposit", "e", "preventDefault", "trim", "depositAmount", "parseInt", "isNaN", "<PERSON><PERSON><PERSON>", "toLocaleString", "bankDeposit", "mensagem", "message", "handleWithdraw", "withdrawAmount", "bankWithdraw", "calculateNextInterest", "timeLeft", "lastInterest", "nextInterest", "Math", "floor", "diff", "hours", "minutes", "getTransactionIcon", "getTransactionColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "history", "back", "toFixed", "name", "icon", "map", "tab", "onSubmit", "type", "value", "onChange", "target", "placeholder", "min", "max", "disabled", "length", "transaction", "char<PERSON>t", "toUpperCase", "slice", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/BankPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\n\ninterface BankAccount {\n  saldo: number;\n  juros_diarios: number;\n  ultimo_juros: string;\n  nivel_conta: number;\n  limite_deposito: number;\n  limite_saque: number;\n}\n\ninterface Transaction {\n  id: string;\n  tipo: 'deposito' | 'saque' | 'juros' | 'transferencia';\n  valor: number;\n  descricao: string;\n  timestamp: string;\n  saldo_anterior: number;\n  saldo_posterior: number;\n}\n\nconst BankPage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const { currentPlayer, loadPlayerData } = usePlayer();\n  \n  const [bankAccount, setBankAccount] = useState<BankAccount | null>(null);\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [selectedTab, setSelectedTab] = useState<'deposito' | 'saque' | 'historico'>('deposito');\n  \n  // Estados para operações\n  const [amount, setAmount] = useState('');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [actionError, setActionError] = useState<string | null>(null);\n  const [actionSuccess, setActionSuccess] = useState<string | null>(null);\n  \n  // Estados para carregamento\n  const [isLoading, setIsLoading] = useState(false);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadBankData();\n      loadTransactions();\n    }\n  }, [isAuthenticated]);\n\n  const loadBankData = async () => {\n    setIsLoading(true);\n    try {\n      const response = await gameApi.getBankAccount();\n      if (response.sucesso) {\n        setBankAccount(response.conta);\n      } else {\n        // Fallback para dados mockados\n        loadMockBankData();\n      }\n    } catch (error) {\n      console.error('Erro ao carregar dados bancários:', error);\n      loadMockBankData();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loadMockBankData = () => {\n    if (!currentPlayer) return;\n\n    const mockAccount: BankAccount = {\n      saldo: currentPlayer?.banco_saldo || 0,\n      juros_diarios: 0.05, // 5% ao dia\n      ultimo_juros: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12h atrás\n      nivel_conta: currentPlayer?.banco_nivel || 1,\n      limite_deposito: 100000 * (currentPlayer?.banco_nivel || 1),\n      limite_saque: 50000 * (currentPlayer?.banco_nivel || 1),\n    };\n\n    setBankAccount(mockAccount);\n  };\n\n  const loadTransactions = async () => {\n    try {\n      const response = await gameApi.getBankTransactions();\n      if (response.sucesso) {\n        setTransactions(response.transacoes || []);\n      } else {\n        // Fallback para transações mockadas\n        loadMockTransactions();\n      }\n    } catch (error) {\n      console.error('Erro ao carregar transações:', error);\n      loadMockTransactions();\n    }\n  };\n\n  const loadMockTransactions = () => {\n    const now = new Date();\n    const mockTransactions: Transaction[] = [\n      {\n        id: '1',\n        tipo: 'deposito',\n        valor: 10000,\n        descricao: 'Depósito via terminal',\n        timestamp: new Date(now.getTime() - 60 * 60000).toISOString(),\n        saldo_anterior: 5000,\n        saldo_posterior: 15000,\n      },\n      {\n        id: '2',\n        tipo: 'juros',\n        valor: 750,\n        descricao: 'Juros diários (5%)',\n        timestamp: new Date(now.getTime() - 24 * 60 * 60000).toISOString(),\n        saldo_anterior: 15000,\n        saldo_posterior: 15750,\n      },\n      {\n        id: '3',\n        tipo: 'saque',\n        valor: 5000,\n        descricao: 'Saque para carteira',\n        timestamp: new Date(now.getTime() - 48 * 60 * 60000).toISOString(),\n        saldo_anterior: 15750,\n        saldo_posterior: 10750,\n      },\n    ];\n\n    setTransactions(mockTransactions);\n  };\n\n  const handleDeposit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!amount.trim() || !bankAccount || !currentPlayer) {\n      setActionError('Valor inválido');\n      return;\n    }\n\n    const depositAmount = parseInt(amount);\n    if (isNaN(depositAmount) || depositAmount <= 0) {\n      setActionError('Valor deve ser um número positivo');\n      return;\n    }\n\n    if (depositAmount > (currentPlayer?.dinheiro || 0)) {\n      setActionError('Saldo insuficiente na carteira');\n      return;\n    }\n\n    if (depositAmount > bankAccount.limite_deposito) {\n      setActionError(`Limite de depósito: $${bankAccount.limite_deposito.toLocaleString()}`);\n      return;\n    }\n\n    setIsProcessing(true);\n    setActionError(null);\n    setActionSuccess(null);\n\n    try {\n      const response = await gameApi.bankDeposit(depositAmount);\n      \n      if (response.sucesso) {\n        setActionSuccess(`Depósito de $${depositAmount.toLocaleString()} realizado com sucesso!`);\n        setAmount('');\n        \n        // Recarregar dados\n        await loadPlayerData();\n        await loadBankData();\n        await loadTransactions();\n      } else {\n        setActionError(response.mensagem || 'Erro no depósito');\n      }\n    } catch (error: any) {\n      setActionError(error.message || 'Erro de conexão');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const handleWithdraw = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!amount.trim() || !bankAccount) {\n      setActionError('Valor inválido');\n      return;\n    }\n\n    const withdrawAmount = parseInt(amount);\n    if (isNaN(withdrawAmount) || withdrawAmount <= 0) {\n      setActionError('Valor deve ser um número positivo');\n      return;\n    }\n\n    if (withdrawAmount > bankAccount.saldo) {\n      setActionError('Saldo insuficiente no banco');\n      return;\n    }\n\n    if (withdrawAmount > bankAccount.limite_saque) {\n      setActionError(`Limite de saque: $${bankAccount.limite_saque.toLocaleString()}`);\n      return;\n    }\n\n    setIsProcessing(true);\n    setActionError(null);\n    setActionSuccess(null);\n\n    try {\n      const response = await gameApi.bankWithdraw(withdrawAmount);\n      \n      if (response.sucesso) {\n        setActionSuccess(`Saque de $${withdrawAmount.toLocaleString()} realizado com sucesso!`);\n        setAmount('');\n        \n        // Recarregar dados\n        await loadPlayerData();\n        await loadBankData();\n        await loadTransactions();\n      } else {\n        setActionError(response.mensagem || 'Erro no saque');\n      }\n    } catch (error: any) {\n      setActionError(error.message || 'Erro de conexão');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const calculateNextInterest = () => {\n    if (!bankAccount) return { amount: 0, timeLeft: '' };\n\n    const lastInterest = new Date(bankAccount.ultimo_juros);\n    const nextInterest = new Date(lastInterest.getTime() + 24 * 60 * 60 * 1000); // +24h\n    const now = new Date();\n\n    const amount = Math.floor(bankAccount.saldo * bankAccount.juros_diarios);\n    \n    if (now >= nextInterest) {\n      return { amount, timeLeft: 'Disponível agora!' };\n    }\n\n    const diff = nextInterest.getTime() - now.getTime();\n    const hours = Math.floor(diff / (60 * 60 * 1000));\n    const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000));\n    \n    return { amount, timeLeft: `${hours}h ${minutes}m` };\n  };\n\n  const getTransactionIcon = (tipo: string) => {\n    switch (tipo) {\n      case 'deposito': return '📥';\n      case 'saque': return '📤';\n      case 'juros': return '💰';\n      case 'transferencia': return '🔄';\n      default: return '📝';\n    }\n  };\n\n  const getTransactionColor = (tipo: string) => {\n    switch (tipo) {\n      case 'deposito': return 'text-green-400';\n      case 'saque': return 'text-red-400';\n      case 'juros': return 'text-yellow-400';\n      case 'transferencia': return 'text-blue-400';\n      default: return 'text-gray-400';\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"h-screen bg-gray-900 text-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-4\">🔒 Acesso Negado</h1>\n          <p className=\"text-gray-400\">Você precisa estar logado para acessar o banco</p>\n        </div>\n      </div>\n    );\n  }\n\n  const nextInterest = calculateNextInterest();\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n          >\n            <span className=\"text-lg\">←</span>\n          </button>\n          <div>\n            <h1 className=\"text-lg font-bold\">🏦 Banco Shack</h1>\n            <p className=\"text-xs text-gray-400\">Sistema Bancário Seguro</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-4\">\n        {/* Mensagens de erro/sucesso */}\n        {actionError && (\n          <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n            <p className=\"text-red-300 text-sm\">❌ {actionError}</p>\n          </div>\n        )}\n\n        {actionSuccess && (\n          <div className=\"bg-green-900 border border-green-500 rounded-lg p-3\">\n            <p className=\"text-green-300 text-sm\">✅ {actionSuccess}</p>\n          </div>\n        )}\n\n        {isLoading ? (\n          <div className=\"text-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4\"></div>\n            <p className=\"text-gray-400\">Carregando dados bancários...</p>\n          </div>\n        ) : bankAccount ? (\n          <>\n            {/* Saldo e informações da conta */}\n            <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n              <h3 className=\"text-lg font-semibold mb-4 text-white\">💳 Informações da Conta</h3>\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">\n                    ${bankAccount.saldo.toLocaleString()}\n                  </div>\n                  <div className=\"text-xs text-gray-400\">Saldo no Banco</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">\n                    Nível {bankAccount.nivel_conta}\n                  </div>\n                  <div className=\"text-xs text-gray-400\">Conta</div>\n                </div>\n              </div>\n              \n              <div className=\"mt-4 grid grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <div className=\"text-gray-400\">Carteira:</div>\n                  <div className=\"text-white\">${currentPlayer?.dinheiro?.toLocaleString() || '0'}</div>\n                </div>\n                <div>\n                  <div className=\"text-gray-400\">Juros Diários:</div>\n                  <div className=\"text-yellow-400\">{(bankAccount.juros_diarios * 100).toFixed(1)}%</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Próximos juros */}\n            <div className=\"bg-gray-800 rounded-lg p-4 border border-yellow-600\">\n              <h3 className=\"text-lg font-semibold mb-3 text-yellow-400\">💰 Próximos Juros</h3>\n              <div className=\"flex justify-between items-center\">\n                <div>\n                  <div className=\"text-lg font-bold text-yellow-400\">\n                    +${nextInterest.amount.toLocaleString()}\n                  </div>\n                  <div className=\"text-xs text-gray-400\">Valor dos juros</div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-sm text-white\">{nextInterest.timeLeft}</div>\n                  <div className=\"text-xs text-gray-400\">Tempo restante</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Tabs */}\n            <div className=\"bg-gray-800 rounded-lg border border-gray-600\">\n              <div className=\"flex border-b border-gray-700\">\n                {[\n                  { id: 'deposito', name: 'Depósito', icon: '📥' },\n                  { id: 'saque', name: 'Saque', icon: '📤' },\n                  { id: 'historico', name: 'Histórico', icon: '📊' },\n                ].map((tab) => (\n                  <button\n                    key={tab.id}\n                    onClick={() => setSelectedTab(tab.id as any)}\n                    className={`flex-1 p-3 text-sm font-medium ${\n                      selectedTab === tab.id\n                        ? 'text-blue-400 border-b-2 border-blue-400'\n                        : 'text-gray-400 hover:text-white'\n                    }`}\n                  >\n                    <span className=\"mr-1\">{tab.icon}</span>\n                    {tab.name}\n                  </button>\n                ))}\n              </div>\n\n              <div className=\"p-4\">\n                {selectedTab === 'deposito' && (\n                  <form onSubmit={handleDeposit} className=\"space-y-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                        Valor do Depósito\n                      </label>\n                      <input\n                        type=\"number\"\n                        value={amount}\n                        onChange={(e) => setAmount(e.target.value)}\n                        placeholder=\"0\"\n                        min=\"1\"\n                        max={Math.min(currentPlayer?.dinheiro || 0, bankAccount.limite_deposito)}\n                        className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                        disabled={isProcessing}\n                      />\n                      <div className=\"text-xs text-gray-400 mt-1\">\n                        Limite: ${bankAccount.limite_deposito.toLocaleString()}\n                      </div>\n                    </div>\n\n                    <button\n                      type=\"submit\"\n                      disabled={isProcessing || !amount.trim()}\n                      className=\"w-full py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n                    >\n                      {isProcessing ? 'Processando...' : 'Depositar'}\n                    </button>\n                  </form>\n                )}\n\n                {selectedTab === 'saque' && (\n                  <form onSubmit={handleWithdraw} className=\"space-y-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                        Valor do Saque\n                      </label>\n                      <input\n                        type=\"number\"\n                        value={amount}\n                        onChange={(e) => setAmount(e.target.value)}\n                        placeholder=\"0\"\n                        min=\"1\"\n                        max={Math.min(bankAccount.saldo, bankAccount.limite_saque)}\n                        className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                        disabled={isProcessing}\n                      />\n                      <div className=\"text-xs text-gray-400 mt-1\">\n                        Limite: ${bankAccount.limite_saque.toLocaleString()}\n                      </div>\n                    </div>\n\n                    <button\n                      type=\"submit\"\n                      disabled={isProcessing || !amount.trim()}\n                      className=\"w-full py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n                    >\n                      {isProcessing ? 'Processando...' : 'Sacar'}\n                    </button>\n                  </form>\n                )}\n\n                {selectedTab === 'historico' && (\n                  <div className=\"space-y-3\">\n                    {transactions.length === 0 ? (\n                      <div className=\"text-center py-8\">\n                        <div className=\"text-4xl mb-4\">📊</div>\n                        <p className=\"text-gray-400\">Nenhuma transação encontrada</p>\n                      </div>\n                    ) : (\n                      transactions.map((transaction) => (\n                        <div key={transaction.id} className=\"bg-gray-700 rounded-lg p-3\">\n                          <div className=\"flex justify-between items-start\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"text-xl\">\n                                {getTransactionIcon(transaction.tipo)}\n                              </div>\n                              <div>\n                                <div className={`font-bold ${getTransactionColor(transaction.tipo)}`}>\n                                  {transaction.tipo.charAt(0).toUpperCase() + transaction.tipo.slice(1)}\n                                </div>\n                                <div className=\"text-xs text-gray-400\">\n                                  {transaction.descricao}\n                                </div>\n                              </div>\n                            </div>\n                            <div className=\"text-right\">\n                              <div className={`font-bold ${getTransactionColor(transaction.tipo)}`}>\n                                {transaction.tipo === 'saque' ? '-' : '+'}${transaction.valor.toLocaleString()}\n                              </div>\n                              <div className=\"text-xs text-gray-400\">\n                                {new Date(transaction.timestamp).toLocaleDateString('pt-BR')}\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      ))\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          </>\n        ) : (\n          <div className=\"text-center py-8\">\n            <div className=\"text-4xl mb-4\">🏦</div>\n            <p className=\"text-gray-400\">Erro ao carregar dados bancários</p>\n          </div>\n        )}\n      </div>\n\n      {/* Footer */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-center\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\"\n          >\n            <span className=\"text-lg\">←</span>\n            <span className=\"text-sm\">Voltar</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BankPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqB1C,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC/B,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEY,aAAa;IAAEC;EAAe,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAErD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAqC,UAAU,CAAC;;EAE9F;EACA,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAgB,IAAI,CAAC;;EAEvE;EACA,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd,IAAIY,eAAe,EAAE;MACnBmB,YAAY,CAAC,CAAC;MACdC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACpB,eAAe,CAAC,CAAC;EAErB,MAAMmB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BD,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAM9B,OAAO,CAAC+B,cAAc,CAAC,CAAC;MAC/C,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBnB,cAAc,CAACiB,QAAQ,CAACG,KAAK,CAAC;MAChC,CAAC,MAAM;QACL;QACAC,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDD,gBAAgB,CAAC,CAAC;IACpB,CAAC,SAAS;MACRP,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMO,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACxB,aAAa,EAAE;IAEpB,MAAM2B,WAAwB,GAAG;MAC/BC,KAAK,EAAE,CAAA5B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6B,WAAW,KAAI,CAAC;MACtCC,aAAa,EAAE,IAAI;MAAE;MACrBC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;MAAE;MACxEC,WAAW,EAAE,CAAAnC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoC,WAAW,KAAI,CAAC;MAC5CC,eAAe,EAAE,MAAM,IAAI,CAAArC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoC,WAAW,KAAI,CAAC,CAAC;MAC3DE,YAAY,EAAE,KAAK,IAAI,CAAAtC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoC,WAAW,KAAI,CAAC;IACxD,CAAC;IAEDjC,cAAc,CAACwB,WAAW,CAAC;EAC7B,CAAC;EAED,MAAMR,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9B,OAAO,CAACiD,mBAAmB,CAAC,CAAC;MACpD,IAAInB,QAAQ,CAACE,OAAO,EAAE;QACpBjB,eAAe,CAACe,QAAQ,CAACoB,UAAU,IAAI,EAAE,CAAC;MAC5C,CAAC,MAAM;QACL;QACAC,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDgB,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC;EAED,MAAMA,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMR,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMU,gBAA+B,GAAG,CACtC;MACEC,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,KAAK;MACZC,SAAS,EAAE,uBAAuB;MAClCC,SAAS,EAAE,IAAIf,IAAI,CAACC,GAAG,CAACe,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAACd,WAAW,CAAC,CAAC;MAC7De,cAAc,EAAE,IAAI;MACpBC,eAAe,EAAE;IACnB,CAAC,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,GAAG;MACVC,SAAS,EAAE,oBAAoB;MAC/BC,SAAS,EAAE,IAAIf,IAAI,CAACC,GAAG,CAACe,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,CAACd,WAAW,CAAC,CAAC;MAClEe,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE;IACnB,CAAC,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,qBAAqB;MAChCC,SAAS,EAAE,IAAIf,IAAI,CAACC,GAAG,CAACe,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,CAACd,WAAW,CAAC,CAAC;MAClEe,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE;IACnB,CAAC,CACF;IAED7C,eAAe,CAACqC,gBAAgB,CAAC;EACnC,CAAC;EAED,MAAMS,aAAa,GAAG,MAAOC,CAAkB,IAAK;IAClDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC7C,MAAM,CAAC8C,IAAI,CAAC,CAAC,IAAI,CAACpD,WAAW,IAAI,CAACF,aAAa,EAAE;MACpDa,cAAc,CAAC,gBAAgB,CAAC;MAChC;IACF;IAEA,MAAM0C,aAAa,GAAGC,QAAQ,CAAChD,MAAM,CAAC;IACtC,IAAIiD,KAAK,CAACF,aAAa,CAAC,IAAIA,aAAa,IAAI,CAAC,EAAE;MAC9C1C,cAAc,CAAC,mCAAmC,CAAC;MACnD;IACF;IAEA,IAAI0C,aAAa,IAAI,CAAAvD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0D,QAAQ,KAAI,CAAC,CAAC,EAAE;MAClD7C,cAAc,CAAC,gCAAgC,CAAC;MAChD;IACF;IAEA,IAAI0C,aAAa,GAAGrD,WAAW,CAACmC,eAAe,EAAE;MAC/CxB,cAAc,CAAC,wBAAwBX,WAAW,CAACmC,eAAe,CAACsB,cAAc,CAAC,CAAC,EAAE,CAAC;MACtF;IACF;IAEAhD,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAM9B,OAAO,CAACsE,WAAW,CAACL,aAAa,CAAC;MAEzD,IAAInC,QAAQ,CAACE,OAAO,EAAE;QACpBP,gBAAgB,CAAC,gBAAgBwC,aAAa,CAACI,cAAc,CAAC,CAAC,yBAAyB,CAAC;QACzFlD,SAAS,CAAC,EAAE,CAAC;;QAEb;QACA,MAAMR,cAAc,CAAC,CAAC;QACtB,MAAMiB,YAAY,CAAC,CAAC;QACpB,MAAMC,gBAAgB,CAAC,CAAC;MAC1B,CAAC,MAAM;QACLN,cAAc,CAACO,QAAQ,CAACyC,QAAQ,IAAI,kBAAkB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOpC,KAAU,EAAE;MACnBZ,cAAc,CAACY,KAAK,CAACqC,OAAO,IAAI,iBAAiB,CAAC;IACpD,CAAC,SAAS;MACRnD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMoD,cAAc,GAAG,MAAOX,CAAkB,IAAK;IACnDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC7C,MAAM,CAAC8C,IAAI,CAAC,CAAC,IAAI,CAACpD,WAAW,EAAE;MAClCW,cAAc,CAAC,gBAAgB,CAAC;MAChC;IACF;IAEA,MAAMmD,cAAc,GAAGR,QAAQ,CAAChD,MAAM,CAAC;IACvC,IAAIiD,KAAK,CAACO,cAAc,CAAC,IAAIA,cAAc,IAAI,CAAC,EAAE;MAChDnD,cAAc,CAAC,mCAAmC,CAAC;MACnD;IACF;IAEA,IAAImD,cAAc,GAAG9D,WAAW,CAAC0B,KAAK,EAAE;MACtCf,cAAc,CAAC,6BAA6B,CAAC;MAC7C;IACF;IAEA,IAAImD,cAAc,GAAG9D,WAAW,CAACoC,YAAY,EAAE;MAC7CzB,cAAc,CAAC,qBAAqBX,WAAW,CAACoC,YAAY,CAACqB,cAAc,CAAC,CAAC,EAAE,CAAC;MAChF;IACF;IAEAhD,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAM9B,OAAO,CAAC2E,YAAY,CAACD,cAAc,CAAC;MAE3D,IAAI5C,QAAQ,CAACE,OAAO,EAAE;QACpBP,gBAAgB,CAAC,aAAaiD,cAAc,CAACL,cAAc,CAAC,CAAC,yBAAyB,CAAC;QACvFlD,SAAS,CAAC,EAAE,CAAC;;QAEb;QACA,MAAMR,cAAc,CAAC,CAAC;QACtB,MAAMiB,YAAY,CAAC,CAAC;QACpB,MAAMC,gBAAgB,CAAC,CAAC;MAC1B,CAAC,MAAM;QACLN,cAAc,CAACO,QAAQ,CAACyC,QAAQ,IAAI,eAAe,CAAC;MACtD;IACF,CAAC,CAAC,OAAOpC,KAAU,EAAE;MACnBZ,cAAc,CAACY,KAAK,CAACqC,OAAO,IAAI,iBAAiB,CAAC;IACpD,CAAC,SAAS;MACRnD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMuD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAChE,WAAW,EAAE,OAAO;MAAEM,MAAM,EAAE,CAAC;MAAE2D,QAAQ,EAAE;IAAG,CAAC;IAEpD,MAAMC,YAAY,GAAG,IAAIpC,IAAI,CAAC9B,WAAW,CAAC6B,YAAY,CAAC;IACvD,MAAMsC,YAAY,GAAG,IAAIrC,IAAI,CAACoC,YAAY,CAACpB,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAC7E,MAAMf,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IAEtB,MAAMxB,MAAM,GAAG8D,IAAI,CAACC,KAAK,CAACrE,WAAW,CAAC0B,KAAK,GAAG1B,WAAW,CAAC4B,aAAa,CAAC;IAExE,IAAIG,GAAG,IAAIoC,YAAY,EAAE;MACvB,OAAO;QAAE7D,MAAM;QAAE2D,QAAQ,EAAE;MAAoB,CAAC;IAClD;IAEA,MAAMK,IAAI,GAAGH,YAAY,CAACrB,OAAO,CAAC,CAAC,GAAGf,GAAG,CAACe,OAAO,CAAC,CAAC;IACnD,MAAMyB,KAAK,GAAGH,IAAI,CAACC,KAAK,CAACC,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACjD,MAAME,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAAEC,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAK,EAAE,GAAG,IAAI,CAAC,CAAC;IAEnE,OAAO;MAAEhE,MAAM;MAAE2D,QAAQ,EAAE,GAAGM,KAAK,KAAKC,OAAO;IAAI,CAAC;EACtD,CAAC;EAED,MAAMC,kBAAkB,GAAI/B,IAAY,IAAK;IAC3C,QAAQA,IAAI;MACV,KAAK,UAAU;QAAE,OAAO,IAAI;MAC5B,KAAK,OAAO;QAAE,OAAO,IAAI;MACzB,KAAK,OAAO;QAAE,OAAO,IAAI;MACzB,KAAK,eAAe;QAAE,OAAO,IAAI;MACjC;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMgC,mBAAmB,GAAIhC,IAAY,IAAK;IAC5C,QAAQA,IAAI;MACV,KAAK,UAAU;QAAE,OAAO,gBAAgB;MACxC,KAAK,OAAO;QAAE,OAAO,cAAc;MACnC,KAAK,OAAO;QAAE,OAAO,iBAAiB;MACtC,KAAK,eAAe;QAAE,OAAO,eAAe;MAC5C;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,IAAI,CAAC7C,eAAe,EAAE;IACpB,oBACEP,OAAA;MAAKqF,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/EtF,OAAA;QAAKqF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtF,OAAA;UAAIqF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7D1F,OAAA;UAAGqF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMb,YAAY,GAAGH,qBAAqB,CAAC,CAAC;EAE5C,oBACE1E,OAAA;IAAKqF,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DtF,OAAA;MAAKqF,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEtF,OAAA;QAAKqF,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CtF,OAAA;UACE2F,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCT,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7FtF,OAAA;YAAMqF,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACT1F,OAAA;UAAAsF,QAAA,gBACEtF,OAAA;YAAIqF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrD1F,OAAA;YAAGqF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1F,OAAA;MAAKqF,SAAS,EAAC,sCAAsC;MAAAC,QAAA,GAElDlE,WAAW,iBACVpB,OAAA;QAAKqF,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DtF,OAAA;UAAGqF,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,SAAE,EAAClE,WAAW;QAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACN,EAEApE,aAAa,iBACZtB,OAAA;QAAKqF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClEtF,OAAA;UAAGqF,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,SAAE,EAAChE,aAAa;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CACN,EAEAlE,SAAS,gBACRxB,OAAA;QAAKqF,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BtF,OAAA;UAAKqF,SAAS,EAAC;QAA2E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjG1F,OAAA;UAAGqF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,GACJhF,WAAW,gBACbV,OAAA,CAAAE,SAAA;QAAAoF,QAAA,gBAEEtF,OAAA;UAAKqF,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChEtF,OAAA;YAAIqF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClF1F,OAAA;YAAKqF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCtF,OAAA;cAAKqF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtF,OAAA;gBAAKqF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,GAChD,EAAC5E,WAAW,CAAC0B,KAAK,CAAC+B,cAAc,CAAC,CAAC;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACN1F,OAAA;gBAAKqF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACN1F,OAAA;cAAKqF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtF,OAAA;gBAAKqF,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAC,WAC1C,EAAC5E,WAAW,CAACiC,WAAW;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACN1F,OAAA;gBAAKqF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAKqF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBAClDtF,OAAA;cAAAsF,QAAA,gBACEtF,OAAA;gBAAKqF,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9C1F,OAAA;gBAAKqF,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,GAAC,EAAC,CAAA9E,aAAa,aAAbA,aAAa,wBAAAH,qBAAA,GAAbG,aAAa,CAAE0D,QAAQ,cAAA7D,qBAAA,uBAAvBA,qBAAA,CAAyB8D,cAAc,CAAC,CAAC,KAAI,GAAG;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,eACN1F,OAAA;cAAAsF,QAAA,gBACEtF,OAAA;gBAAKqF,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnD1F,OAAA;gBAAKqF,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAE,CAAC5E,WAAW,CAAC4B,aAAa,GAAG,GAAG,EAAEyD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1F,OAAA;UAAKqF,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBAClEtF,OAAA;YAAIqF,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjF1F,OAAA;YAAKqF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDtF,OAAA;cAAAsF,QAAA,gBACEtF,OAAA;gBAAKqF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,IAC/C,EAACT,YAAY,CAAC7D,MAAM,CAACmD,cAAc,CAAC,CAAC;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACN1F,OAAA;gBAAKqF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACN1F,OAAA;cAAKqF,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtF,OAAA;gBAAKqF,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAET,YAAY,CAACF;cAAQ;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjE1F,OAAA;gBAAKqF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1F,OAAA;UAAKqF,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DtF,OAAA;YAAKqF,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAC3C,CACC;cAAEnC,EAAE,EAAE,UAAU;cAAE6C,IAAI,EAAE,UAAU;cAAEC,IAAI,EAAE;YAAK,CAAC,EAChD;cAAE9C,EAAE,EAAE,OAAO;cAAE6C,IAAI,EAAE,OAAO;cAAEC,IAAI,EAAE;YAAK,CAAC,EAC1C;cAAE9C,EAAE,EAAE,WAAW;cAAE6C,IAAI,EAAE,WAAW;cAAEC,IAAI,EAAE;YAAK,CAAC,CACnD,CAACC,GAAG,CAAEC,GAAG,iBACRnG,OAAA;cAEE2F,OAAO,EAAEA,CAAA,KAAM5E,cAAc,CAACoF,GAAG,CAAChD,EAAS,CAAE;cAC7CkC,SAAS,EAAE,kCACTvE,WAAW,KAAKqF,GAAG,CAAChD,EAAE,GAClB,0CAA0C,GAC1C,gCAAgC,EACnC;cAAAmC,QAAA,gBAEHtF,OAAA;gBAAMqF,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEa,GAAG,CAACF;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACvCS,GAAG,CAACH,IAAI;YAAA,GATJG,GAAG,CAAChD,EAAE;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUL,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1F,OAAA;YAAKqF,SAAS,EAAC,KAAK;YAAAC,QAAA,GACjBxE,WAAW,KAAK,UAAU,iBACzBd,OAAA;cAAMoG,QAAQ,EAAEzC,aAAc;cAAC0B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAClDtF,OAAA;gBAAAsF,QAAA,gBACEtF,OAAA;kBAAOqF,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR1F,OAAA;kBACEqG,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAEtF,MAAO;kBACduF,QAAQ,EAAG3C,CAAC,IAAK3C,SAAS,CAAC2C,CAAC,CAAC4C,MAAM,CAACF,KAAK,CAAE;kBAC3CG,WAAW,EAAC,GAAG;kBACfC,GAAG,EAAC,GAAG;kBACPC,GAAG,EAAE7B,IAAI,CAAC4B,GAAG,CAAC,CAAAlG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0D,QAAQ,KAAI,CAAC,EAAExD,WAAW,CAACmC,eAAe,CAAE;kBACzEwC,SAAS,EAAC,wEAAwE;kBAClFuB,QAAQ,EAAE1F;gBAAa;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACF1F,OAAA;kBAAKqF,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,WACjC,EAAC5E,WAAW,CAACmC,eAAe,CAACsB,cAAc,CAAC,CAAC;gBAAA;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1F,OAAA;gBACEqG,IAAI,EAAC,QAAQ;gBACbO,QAAQ,EAAE1F,YAAY,IAAI,CAACF,MAAM,CAAC8C,IAAI,CAAC,CAAE;gBACzCuB,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,EAE/GpE,YAAY,GAAG,gBAAgB,GAAG;cAAW;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACP,EAEA5E,WAAW,KAAK,OAAO,iBACtBd,OAAA;cAAMoG,QAAQ,EAAE7B,cAAe;cAACc,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACnDtF,OAAA;gBAAAsF,QAAA,gBACEtF,OAAA;kBAAOqF,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR1F,OAAA;kBACEqG,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAEtF,MAAO;kBACduF,QAAQ,EAAG3C,CAAC,IAAK3C,SAAS,CAAC2C,CAAC,CAAC4C,MAAM,CAACF,KAAK,CAAE;kBAC3CG,WAAW,EAAC,GAAG;kBACfC,GAAG,EAAC,GAAG;kBACPC,GAAG,EAAE7B,IAAI,CAAC4B,GAAG,CAAChG,WAAW,CAAC0B,KAAK,EAAE1B,WAAW,CAACoC,YAAY,CAAE;kBAC3DuC,SAAS,EAAC,wEAAwE;kBAClFuB,QAAQ,EAAE1F;gBAAa;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACF1F,OAAA;kBAAKqF,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,WACjC,EAAC5E,WAAW,CAACoC,YAAY,CAACqB,cAAc,CAAC,CAAC;gBAAA;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1F,OAAA;gBACEqG,IAAI,EAAC,QAAQ;gBACbO,QAAQ,EAAE1F,YAAY,IAAI,CAACF,MAAM,CAAC8C,IAAI,CAAC,CAAE;gBACzCuB,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,EAE3GpE,YAAY,GAAG,gBAAgB,GAAG;cAAO;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACP,EAEA5E,WAAW,KAAK,WAAW,iBAC1Bd,OAAA;cAAKqF,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB1E,YAAY,CAACiG,MAAM,KAAK,CAAC,gBACxB7G,OAAA;gBAAKqF,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtF,OAAA;kBAAKqF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvC1F,OAAA;kBAAGqF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,GAEN9E,YAAY,CAACsF,GAAG,CAAEY,WAAW,iBAC3B9G,OAAA;gBAA0BqF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,eAC9DtF,OAAA;kBAAKqF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/CtF,OAAA;oBAAKqF,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CtF,OAAA;sBAAKqF,SAAS,EAAC,SAAS;sBAAAC,QAAA,EACrBH,kBAAkB,CAAC2B,WAAW,CAAC1D,IAAI;oBAAC;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,eACN1F,OAAA;sBAAAsF,QAAA,gBACEtF,OAAA;wBAAKqF,SAAS,EAAE,aAAaD,mBAAmB,CAAC0B,WAAW,CAAC1D,IAAI,CAAC,EAAG;wBAAAkC,QAAA,EAClEwB,WAAW,CAAC1D,IAAI,CAAC2D,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,WAAW,CAAC1D,IAAI,CAAC6D,KAAK,CAAC,CAAC;sBAAC;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE,CAAC,eACN1F,OAAA;wBAAKqF,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACnCwB,WAAW,CAACxD;sBAAS;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1F,OAAA;oBAAKqF,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBtF,OAAA;sBAAKqF,SAAS,EAAE,aAAaD,mBAAmB,CAAC0B,WAAW,CAAC1D,IAAI,CAAC,EAAG;sBAAAkC,QAAA,GAClEwB,WAAW,CAAC1D,IAAI,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG,EAAC,GAAC,EAAC0D,WAAW,CAACzD,KAAK,CAACc,cAAc,CAAC,CAAC;oBAAA;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3E,CAAC,eACN1F,OAAA;sBAAKqF,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACnC,IAAI9C,IAAI,CAACsE,WAAW,CAACvD,SAAS,CAAC,CAAC2D,kBAAkB,CAAC,OAAO;oBAAC;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAvBEoB,WAAW,CAAC3D,EAAE;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBnB,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CAAC,gBAEH1F,OAAA;QAAKqF,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BtF,OAAA;UAAKqF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC1F,OAAA;UAAGqF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1F,OAAA;MAAKqF,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEtF,OAAA;QAAKqF,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClCtF,OAAA;UACE2F,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCT,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExFtF,OAAA;YAAMqF,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClC1F,OAAA;YAAMqF,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtF,EAAA,CA9eID,QAAkB;EAAA,QACYP,OAAO,EACCC,SAAS;AAAA;AAAAsH,EAAA,GAF/ChH,QAAkB;AAgfxB,eAAeA,QAAQ;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}