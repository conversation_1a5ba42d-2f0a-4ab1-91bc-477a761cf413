{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\n\n// Componentes\nimport TestPage from './pages/TestPage';\nimport HackGamePage from './pages/HackGamePage';\nimport { useSimpleAuth } from './stores/simpleAuthStore';\n\n// Estilos\nimport './styles/globals.css';\n\n// Componente para roteamento baseado em autenticação\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppRouter = () => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading,\n    checkAuth\n  } = useSimpleAuth();\n\n  // Verificar autenticação na inicialização\n  useEffect(() => {\n    console.log('AppRouter - Verificando autenticação inicial...');\n    checkAuth();\n  }, [checkAuth]);\n  console.log('AppRouter - Estado:', {\n    isAuthenticated,\n    isLoading\n  });\n\n  // Mostrar loading se necessário\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Carregando...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/game\",\n      element: /*#__PURE__*/_jsxDEV(HackGamePage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 36\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/test\",\n      element: /*#__PURE__*/_jsxDEV(TestPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 36\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/game\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/game\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(AppRouter, \"A77NKHCEYyODGLzC2svQOfQk5ic=\", false, function () {\n  return [useSimpleAuth];\n});\n_c = AppRouter;\nfunction App() {\n  console.log('App - Renderizando com autenticação simples...');\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-bg-primary text-text-primary\",\n      children: /*#__PURE__*/_jsxDEV(AppRouter, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppRouter\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "TestPage", "HackGamePage", "useSimpleAuth", "jsxDEV", "_jsxDEV", "AppRouter", "_s", "isAuthenticated", "isLoading", "checkAuth", "console", "log", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/App.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\n\n// Componentes\nimport TestPage from './pages/TestPage';\nimport SimpleLoginPage from './pages/SimpleLoginPage';\nimport HackGamePage from './pages/HackGamePage';\nimport { useSimpleAuth } from './stores/simpleAuthStore';\n\n// Estilos\nimport './styles/globals.css';\n\n// Componente para roteamento baseado em autenticação\nconst AppRouter: React.FC = () => {\n  const { isAuthenticated, isLoading, checkAuth } = useSimpleAuth();\n\n  // Verificar autenticação na inicialização\n  useEffect(() => {\n    console.log('AppRouter - Verificando autenticação inicial...');\n    checkAuth();\n  }, [checkAuth]);\n\n  console.log('AppRouter - Estado:', { isAuthenticated, isLoading });\n\n  // Mostrar loading se necessário\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n          <p>Carregando...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <Routes>\n      {/* Rota principal do jogo - sempre mostra HackGamePage */}\n      <Route path=\"/game\" element={<HackGamePage />} />\n\n      {/* Rota de teste */}\n      <Route path=\"/test\" element={<TestPage />} />\n\n      {/* Rota raiz redireciona para o jogo */}\n      <Route path=\"/\" element={<Navigate to=\"/game\" replace />} />\n\n      {/* 404 */}\n      <Route path=\"*\" element={<Navigate to=\"/game\" replace />} />\n    </Routes>\n  );\n};\n\nfunction App() {\n  console.log('App - Renderizando com autenticação simples...');\n\n  return (\n    <Router>\n      <div className=\"min-h-screen bg-bg-primary text-text-primary\">\n        <AppRouter />\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;;AAEnF;AACA,OAAOC,QAAQ,MAAM,kBAAkB;AAEvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SAASC,aAAa,QAAQ,0BAA0B;;AAExD;AACA,OAAO,sBAAsB;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,eAAe;IAAEC,SAAS;IAAEC;EAAU,CAAC,GAAGP,aAAa,CAAC,CAAC;;EAEjE;EACAR,SAAS,CAAC,MAAM;IACdgB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9DF,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEfC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;IAAEJ,eAAe;IAAEC;EAAU,CAAC,CAAC;;EAElE;EACA,IAAIA,SAAS,EAAE;IACb,oBACEJ,OAAA;MAAKQ,SAAS,EAAC,+EAA+E;MAAAC,QAAA,eAC5FT,OAAA;QAAKQ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BT,OAAA;UAAKQ,SAAS,EAAC;QAA2E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGb,OAAA;UAAAS,QAAA,EAAG;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEb,OAAA,CAACP,MAAM;IAAAgB,QAAA,gBAELT,OAAA,CAACN,KAAK;MAACoB,IAAI,EAAC,OAAO;MAACC,OAAO,eAAEf,OAAA,CAACH,YAAY;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGjDb,OAAA,CAACN,KAAK;MAACoB,IAAI,EAAC,OAAO;MAACC,OAAO,eAAEf,OAAA,CAACJ,QAAQ;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG7Cb,OAAA,CAACN,KAAK;MAACoB,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEf,OAAA,CAACL,QAAQ;QAACqB,EAAE,EAAC,OAAO;QAACC,OAAO;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG5Db,OAAA,CAACN,KAAK;MAACoB,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEf,OAAA,CAACL,QAAQ;QAACqB,EAAE,EAAC,OAAO;QAACC,OAAO;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtD,CAAC;AAEb,CAAC;AAACX,EAAA,CAtCID,SAAmB;EAAA,QAC2BH,aAAa;AAAA;AAAAoB,EAAA,GAD3DjB,SAAmB;AAwCzB,SAASkB,GAAGA,CAAA,EAAG;EACbb,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;EAE7D,oBACEP,OAAA,CAACR,MAAM;IAAAiB,QAAA,eACLT,OAAA;MAAKQ,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3DT,OAAA,CAACC,SAAS;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACO,GAAA,GAVQD,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}