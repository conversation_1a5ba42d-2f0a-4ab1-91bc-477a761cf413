{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\game\\\\MobilePhone.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useHackGameStore } from '../../stores/hackGameStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MobilePhone = ({\n  children\n}) => {\n  _s();\n  const {\n    player,\n    notifications\n  } = useHackGameStore();\n  const unreadCount = notifications.filter(n => !n.read).length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-[375px] h-[812px] bg-black rounded-[3rem] p-2 shadow-2xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-full bg-gray-900 rounded-[2.5rem] overflow-hidden relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black rounded-b-2xl z-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center px-6 pt-3 pb-1 text-white text-sm relative z-40\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs\",\n                children: \"9:41\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1\",\n              children: [unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-red-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1 h-3 bg-white rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1 h-3 bg-white rounded-full opacity-60\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1 h-3 bg-white rounded-full opacity-30\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-3 border border-white rounded-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-1 bg-green-500 rounded-sm m-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 38,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 h-full pt-2\",\n            children: children\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white rounded-full opacity-30\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-0 top-20 w-1 h-12 bg-gray-700 rounded-l-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-0 top-36 w-1 h-8 bg-gray-700 rounded-l-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-0 top-48 w-1 h-8 bg-gray-700 rounded-l-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-0 top-32 w-1 h-16 bg-gray-700 rounded-r-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_s(MobilePhone, \"FQ/zXJVETXnmw7E1NDGa4n9rLMY=\", false, function () {\n  return [useHackGameStore];\n});\n_c = MobilePhone;\nexport default MobilePhone;\nvar _c;\n$RefreshReg$(_c, \"MobilePhone\");", "map": {"version": 3, "names": ["React", "useHackGameStore", "jsxDEV", "_jsxDEV", "MobilePhone", "children", "_s", "player", "notifications", "unreadCount", "filter", "n", "read", "length", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/game/MobilePhone.tsx"], "sourcesContent": ["import React from 'react';\nimport { useHackGameStore } from '../../stores/hackGameStore';\n\ninterface MobilePhoneProps {\n  children: React.ReactNode;\n}\n\nconst MobilePhone: React.FC<MobilePhoneProps> = ({ children }) => {\n  const { player, notifications } = useHackGameStore();\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black p-4\">\n      {/* Moldura do celular */}\n      <div className=\"relative\">\n        {/* Corpo do celular */}\n        <div className=\"w-[375px] h-[812px] bg-black rounded-[3rem] p-2 shadow-2xl\">\n          {/* Tela do celular */}\n          <div className=\"w-full h-full bg-gray-900 rounded-[2.5rem] overflow-hidden relative\">\n            {/* Notch */}\n            <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black rounded-b-2xl z-50\"></div>\n            \n            {/* Status bar */}\n            <div className=\"flex justify-between items-center px-6 pt-3 pb-1 text-white text-sm relative z-40\">\n              <div className=\"flex items-center space-x-1\">\n                <span className=\"text-xs\">9:41</span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                {unreadCount > 0 && (\n                  <div className=\"w-2 h-2 bg-red-500 rounded-full\"></div>\n                )}\n                <div className=\"flex space-x-1\">\n                  <div className=\"w-1 h-3 bg-white rounded-full\"></div>\n                  <div className=\"w-1 h-3 bg-white rounded-full opacity-60\"></div>\n                  <div className=\"w-1 h-3 bg-white rounded-full opacity-30\"></div>\n                </div>\n                <div className=\"w-6 h-3 border border-white rounded-sm\">\n                  <div className=\"w-4 h-1 bg-green-500 rounded-sm m-0.5\"></div>\n                </div>\n              </div>\n            </div>\n\n            {/* Conteúdo da tela */}\n            <div className=\"flex-1 h-full pt-2\">\n              {children}\n            </div>\n\n            {/* Indicador home */}\n            <div className=\"absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white rounded-full opacity-30\"></div>\n          </div>\n        </div>\n\n        {/* Botões laterais */}\n        <div className=\"absolute left-0 top-20 w-1 h-12 bg-gray-700 rounded-l-lg\"></div>\n        <div className=\"absolute left-0 top-36 w-1 h-8 bg-gray-700 rounded-l-lg\"></div>\n        <div className=\"absolute left-0 top-48 w-1 h-8 bg-gray-700 rounded-l-lg\"></div>\n        <div className=\"absolute right-0 top-32 w-1 h-16 bg-gray-700 rounded-r-lg\"></div>\n      </div>\n    </div>\n  );\n};\n\nexport default MobilePhone;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM9D,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM;IAAEC,MAAM;IAAEC;EAAc,CAAC,GAAGP,gBAAgB,CAAC,CAAC;EACpD,MAAMQ,WAAW,GAAGD,aAAa,CAACE,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,CAAC,CAACC,MAAM;EAE7D,oBACEV,OAAA;IAAKW,SAAS,EAAC,yGAAyG;IAAAT,QAAA,eAEtHF,OAAA;MAAKW,SAAS,EAAC,UAAU;MAAAT,QAAA,gBAEvBF,OAAA;QAAKW,SAAS,EAAC,4DAA4D;QAAAT,QAAA,eAEzEF,OAAA;UAAKW,SAAS,EAAC,qEAAqE;UAAAT,QAAA,gBAElFF,OAAA;YAAKW,SAAS,EAAC;UAAyF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAG/Gf,OAAA;YAAKW,SAAS,EAAC,mFAAmF;YAAAT,QAAA,gBAChGF,OAAA;cAAKW,SAAS,EAAC,6BAA6B;cAAAT,QAAA,eAC1CF,OAAA;gBAAMW,SAAS,EAAC,SAAS;gBAAAT,QAAA,EAAC;cAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNf,OAAA;cAAKW,SAAS,EAAC,6BAA6B;cAAAT,QAAA,GACzCI,WAAW,GAAG,CAAC,iBACdN,OAAA;gBAAKW,SAAS,EAAC;cAAiC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACvD,eACDf,OAAA;gBAAKW,SAAS,EAAC,gBAAgB;gBAAAT,QAAA,gBAC7BF,OAAA;kBAAKW,SAAS,EAAC;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDf,OAAA;kBAAKW,SAAS,EAAC;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChEf,OAAA;kBAAKW,SAAS,EAAC;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNf,OAAA;gBAAKW,SAAS,EAAC,wCAAwC;gBAAAT,QAAA,eACrDF,OAAA;kBAAKW,SAAS,EAAC;gBAAuC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNf,OAAA;YAAKW,SAAS,EAAC,oBAAoB;YAAAT,QAAA,EAChCA;UAAQ;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNf,OAAA;YAAKW,SAAS,EAAC;UAAiG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNf,OAAA;QAAKW,SAAS,EAAC;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChFf,OAAA;QAAKW,SAAS,EAAC;MAAyD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/Ef,OAAA;QAAKW,SAAS,EAAC;MAAyD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/Ef,OAAA;QAAKW,SAAS,EAAC;MAA2D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9E;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACZ,EAAA,CArDIF,WAAuC;EAAA,QACTH,gBAAgB;AAAA;AAAAkB,EAAA,GAD9Cf,WAAuC;AAuD7C,eAAeA,WAAW;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}