import hashlib
import hmac
import json
import time
import threading
from datetime import datetime, timedelta
from flask import request
from functools import wraps

class CacheSecurity:
    """
    Sistema de segurança para cache com assinatura criptográfica
    Previne cache poisoning e garante integridade dos dados
    """
    
    def __init__(self, app=None):
        self.app = app
        self.cache_signing_key = None
        self.cache_storage = {}  # Em produção, usar Redis ou Memcached
        self.cache_metadata = {}  # Metadados para TTL e verificação
        self.MAX_REQUESTS_PER_MINUTE = 60
        self.rate_limit_storage = {}
        
        # Configurações de rate limiting específico por operação
        self.cache_operations = {}
        self.cache_rate_limits = {
            'get': {'count': 100, 'per_seconds': 60},     # 100 gets por minuto
            'set': {'count': 50, 'per_seconds': 60},      # 50 sets por minuto
            'delete': {'count': 20, 'per_seconds': 60}    # 20 deletes por minuto
        }
        
        # Lock para thread safety
        self.lock = threading.Lock()
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize with Flask app"""
        self.app = app
        
        # Derivar chave de assinatura da SECRET_KEY do app
        secret_key = app.config.get('SECRET_KEY')
        if secret_key:
            self.cache_signing_key = hashlib.sha256(
                f"cache_security_{secret_key}".encode('utf-8')
            ).digest()
        else:
            raise ValueError("App must have SECRET_KEY configured for CacheSecurity")
        
        print("✅ Cache security system initialized")
        
        # Start cleanup thread
        cleanup_thread = threading.Thread(target=self._cleanup_expired_cache, daemon=True)
        cleanup_thread.start()
    
    def _check_cache_rate_limit(self, client_ip, operation_type):
        """Verificar se o IP está dentro do rate limit para a operação"""
        current_time = time.time()
        operation_key = f"{client_ip}:{operation_type}"
        
        # Limpar operações antigas
        cutoff_time = current_time - self.cache_rate_limits[operation_type]['per_seconds']
        
        if operation_key in self.cache_operations:
            # Filtrar operações recentes
            recent_ops = [
                op_time for op_time in self.cache_operations[operation_key]
                if op_time > cutoff_time
            ]
            self.cache_operations[operation_key] = recent_ops
            
            # Verificar limite
            if len(recent_ops) >= self.cache_rate_limits[operation_type]['count']:
                return False
        
        return True
    
    def _record_cache_operation(self, client_ip, operation_type):
        """Registrar operação de cache"""
        current_time = time.time()
        operation_key = f"{client_ip}:{operation_type}"
        
        if operation_key not in self.cache_operations:
            self.cache_operations[operation_key] = []
        
        self.cache_operations[operation_key].append(current_time)
    
    def _generate_cache_signature(self, cache_key, data, timestamp):
        """Gerar assinatura HMAC para dados do cache"""
        if not self.cache_signing_key:
            raise ValueError("Cache signing key not initialized")
        
        # Serializar dados
        data_string = json.dumps(data, sort_keys=True, separators=(',', ':'))
        
        # Criar payload para assinatura
        payload = f"{cache_key}:{data_string}:{timestamp}"
        
        # Gerar HMAC-SHA256
        signature = hmac.new(
            self.cache_signing_key,
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def _verify_cache_signature(self, cache_key, data, timestamp, signature):
        """Verificar assinatura HMAC dos dados do cache"""
        try:
            expected_signature = self._generate_cache_signature(cache_key, data, timestamp)
            return hmac.compare_digest(signature, expected_signature)
        except Exception as e:
            if self.app:
                self.app.logger.error(f"Cache signature verification error: {e}")
            return False
    
    def _is_cache_expired(self, timestamp, ttl):
        """Verificar se entrada do cache expirou"""
        current_time = time.time()
        return current_time > (timestamp + ttl)
    
    def _cleanup_expired_cache(self):
        """Thread background para limpeza automática do cache"""
        while True:
            try:
                time.sleep(300)  # Executar a cada 5 minutos
                self.cleanup_expired_entries()
            except Exception as e:
                if self.app:
                    self.app.logger.error(f"Cache cleanup error: {e}")
    
    def cleanup_expired_entries(self):
        """Remover entradas expiradas do cache"""
        with self.lock:
            expired_keys = []
            current_time = time.time()
            
            for key, metadata in self.cache_metadata.items():
                if current_time > (metadata['timestamp'] + metadata['ttl']):
                    expired_keys.append(key)
            
            for key in expired_keys:
                if key in self.cache_storage:
                    del self.cache_storage[key]
                if key in self.cache_metadata:
                    del self.cache_metadata[key]
            
            if expired_keys and self.app:
                self.app.logger.info(f"Cache cleanup: removed {len(expired_keys)} expired entries")
            
            return len(expired_keys)
    
    def get_cache_stats(self):
        """Obter estatísticas do cache"""
        with self.lock:
            current_time = time.time()
            total_entries = len(self.cache_storage)
            expired_entries = sum(
                1 for metadata in self.cache_metadata.values()
                if current_time > (metadata['timestamp'] + metadata['ttl'])
            )
            
            return {
                'total_entries': total_entries,
                'active_entries': total_entries - expired_entries,
                'expired_entries': expired_entries,
                'operation_counts': dict(self.cache_operations)
            }
    
    def secure_cache_get(self, cache_key):
        """
        Recuperar dados do cache seguro
        
        Args:
            cache_key: Chave do cache
            
        Returns:
            dict|None: Dados se válidos, None se não encontrados ou inválidos
        """
        try:
            # Verificar rate limit
            client_ip = request.remote_addr if request else 'internal'
            if not self._check_cache_rate_limit(client_ip, 'get'):
                print(f"⚠️ Cache Security: Rate limit exceeded for get from {client_ip}")
                return None
            
            self._record_cache_operation(client_ip, 'get')
            
            with self.lock:
                # Verificar se existe
                if cache_key not in self.cache_storage:
                    return None
                
                cached_entry = self.cache_storage[cache_key]
                metadata = self.cache_metadata[cache_key]
                
                # Verificar TTL
                if self._is_cache_expired(metadata['timestamp'], metadata['ttl']):
                    del self.cache_storage[cache_key]
                    del self.cache_metadata[cache_key]
                    return None
                
                # Verificar assinatura
                if not self._verify_cache_signature(
                    cache_key, 
                    cached_entry['data'], 
                    cached_entry['timestamp'], 
                    cached_entry['signature']
                ):
                    # Dados comprometidos, remover
                    del self.cache_storage[cache_key]
                    del self.cache_metadata[cache_key]
                    if self.app:
                        self.app.logger.error(f"Cache signature verification failed for key: {cache_key}")
                    return None
                
                return cached_entry['data']
                
        except Exception as e:
            if self.app:
                self.app.logger.error(f"Cache get error for key {cache_key}: {e}")
            return None
    
    def secure_cache_set(self, cache_key, data, ttl=3600):
        """
        Armazenar dados no cache seguro
        
        Args:
            cache_key: Chave do cache
            data: Dados para armazenar
            ttl: Time to live em segundos (padrão: 1 hora)
            
        Returns:
            bool: True se armazenado com sucesso
        """
        try:
            # Verificar rate limit
            client_ip = request.remote_addr if request else 'internal'
            if not self._check_cache_rate_limit(client_ip, 'set'):
                print(f"⚠️ Cache Security: Rate limit exceeded for set from {client_ip}")
                return False
            
            self._record_cache_operation(client_ip, 'set')
            
            timestamp = time.time()
            
            # Gerar assinatura
            signature = self._generate_cache_signature(cache_key, data, timestamp)
            
            # Armazenar com assinatura
            cache_entry = {
                'data': data,
                'timestamp': timestamp,
                'signature': signature
            }
            
            with self.lock:
                self.cache_storage[cache_key] = cache_entry
                self.cache_metadata[cache_key] = {
                    'timestamp': timestamp,
                    'ttl': ttl
                }
            
            return True
            
        except Exception as e:
            if self.app:
                self.app.logger.error(f"Cache set error for key {cache_key}: {e}")
            return False
    
    def secure_cache_delete(self, cache_key):
        """
        Remover entrada do cache
        
        Args:
            cache_key: Chave do cache para remover
            
        Returns:
            bool: True se removido com sucesso
        """
        try:
            # Verificar rate limit
            client_ip = request.remote_addr if request else 'internal'
            if not self._check_cache_rate_limit(client_ip, 'delete'):
                print(f"⚠️ Cache Security: Rate limit exceeded for delete from {client_ip}")
                return False
            
            self._record_cache_operation(client_ip, 'delete')
            
            with self.lock:
                deleted = False
                if cache_key in self.cache_storage:
                    del self.cache_storage[cache_key]
                    deleted = True
                if cache_key in self.cache_metadata:
                    del self.cache_metadata[cache_key]
                    deleted = True
                return deleted
                
        except Exception as e:
            if self.app:
                self.app.logger.error(f"Cache delete error for key {cache_key}: {e}")
            return False
    
    def invalidate_cache_pattern(self, pattern):
        """
        Invalidar entradas do cache que correspondem a um padrão
        
        Args:
            pattern: Padrão de string (simples matching)
            
        Returns:
            int: Número de entradas removidas
        """
        try:
            with self.lock:
                keys_to_remove = [
                    key for key in self.cache_storage.keys()
                    if pattern in key
                ]
                
                for key in keys_to_remove:
                    if key in self.cache_storage:
                        del self.cache_storage[key]
                    if key in self.cache_metadata:
                        del self.cache_metadata[key]
                
                return len(keys_to_remove)
                
        except Exception as e:
            if self.app:
                self.app.logger.error(f"Cache pattern invalidation error: {e}")
            return 0
    
    # Métodos de conveniência que combinam com o sistema existente
    def get(self, cache_key):
        """Método de conveniência compatível com cache simples"""
        return self.secure_cache_get(cache_key)
    
    def set(self, cache_key, data, ttl=3600):
        """Método de conveniência compatível com cache simples"""
        return self.secure_cache_set(cache_key, data, ttl)
    
    def delete(self, cache_key):
        """Método de conveniência compatível com cache simples"""
        return self.secure_cache_delete(cache_key)
    
    def clear_pattern(self, pattern):
        """Método de conveniência para limpar por padrão"""
        return self.invalidate_cache_pattern(pattern)
