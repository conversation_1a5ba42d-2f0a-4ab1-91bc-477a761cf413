#!/usr/bin/env python3
"""
Teste do novo backend SHACK Game
"""

import requests
import json
import time

BASE_URL = 'http://localhost:5000'

def test_health():
    """Testar health check"""
    print("🔍 Testando health check...")
    try:
        response = requests.get(f'{BASE_URL}/api/health', timeout=5)
        print(f"Status: {response.status_code}")
        print(f"Resposta: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_register():
    """Testar registro"""
    print("\n📝 Testando registro...")
    try:
        data = {
            'nick': 'TestPlayer',
            'email': '<EMAIL>',
            'password': 'teste123'
        }
        
        response = requests.post(f'{BASE_URL}/api/auth/register', json=data, timeout=10)
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"Resposta: {result}")
        
        if response.status_code == 200:
            return result.get('token')
        return None
    except Exception as e:
        print(f"❌ Erro: {e}")
        return None

def test_login():
    """Testar login"""
    print("\n🔐 Testando login...")
    try:
        data = {
            'email': '<EMAIL>',
            'password': 'teste123'
        }
        
        response = requests.post(f'{BASE_URL}/api/auth/login', json=data, timeout=10)
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"Resposta: {result}")
        
        if response.status_code == 200:
            return result.get('token')
        return None
    except Exception as e:
        print(f"❌ Erro: {e}")
        return None

def test_player_data(token):
    """Testar dados do jogador"""
    print("\n👤 Testando dados do jogador...")
    try:
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(f'{BASE_URL}/api/jogador', headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"Resposta: {json.dumps(result, indent=2)}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_upgrade(token):
    """Testar upgrade"""
    print("\n⬆️ Testando upgrade...")
    try:
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'app_name': 'antivirus',
            'quantity': 1
        }
        
        response = requests.post(f'{BASE_URL}/api/upgrade', json=data, headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"Resposta: {result}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def main():
    print("=== TESTE DO BACKEND SHACK GAME ===")
    print("Certifique-se de que o backend está rodando em http://localhost:5000")
    print()
    
    # Aguardar um pouco
    time.sleep(2)
    
    # Teste 1: Health check
    if not test_health():
        print("❌ Backend não está respondendo. Verifique se está rodando.")
        return
    
    # Teste 2: Registro (pode falhar se usuário já existe)
    token = test_register()
    
    # Teste 3: Login
    if not token:
        token = test_login()
    
    if not token:
        print("❌ Não foi possível obter token de autenticação")
        return
    
    print(f"\n✅ Token obtido: {token[:50]}...")
    
    # Teste 4: Dados do jogador
    if not test_player_data(token):
        print("❌ Erro ao carregar dados do jogador")
        return
    
    # Teste 5: Upgrade
    if not test_upgrade(token):
        print("❌ Erro no upgrade")
        return
    
    print("\n🎉 Todos os testes passaram! Backend funcionando corretamente.")

if __name__ == '__main__':
    main()
