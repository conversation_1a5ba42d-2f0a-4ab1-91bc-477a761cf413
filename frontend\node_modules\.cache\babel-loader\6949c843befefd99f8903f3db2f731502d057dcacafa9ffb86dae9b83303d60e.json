{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\TerminalPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useGameSystem } from '../hooks/useGameSystem';\nimport gameApi from '../services/gameApi';\nimport GameFooter from '../components/common/GameFooter';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TerminalPage = () => {\n  _s();\n  var _bruteforceStatus2;\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    currentPlayer\n  } = usePlayer();\n  const {\n    activeConnections,\n    activeBruteforces,\n    loadActiveConnections,\n    startBruteforce,\n    closeConnection\n  } = useGameSystem();\n  const [terminalLines, setTerminalLines] = useState([]);\n  const [currentInput, setCurrentInput] = useState('');\n  const [isExecuting, setIsExecuting] = useState(false);\n  const terminalRef = useRef(null);\n  const inputRef = useRef(null);\n  useEffect(() => {\n    if (isAuthenticated) {\n      initializeTerminal();\n      loadActiveConnections();\n    }\n  }, [isAuthenticated, loadActiveConnections]);\n  useEffect(() => {\n    // Auto-scroll para o final\n    if (terminalRef.current) {\n      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;\n    }\n  }, [terminalLines]);\n  const initializeTerminal = () => {\n    const welcomeLines = [{\n      id: '1',\n      type: 'system',\n      content: '=== SHACK TERMINAL v2.0 ===',\n      timestamp: new Date()\n    }, {\n      id: '2',\n      type: 'system',\n      content: `Bem-vindo, ${(user === null || user === void 0 ? void 0 : user.nick) || 'Hacker'}`,\n      timestamp: new Date()\n    }, {\n      id: '3',\n      type: 'system',\n      content: 'Digite \"help\" para ver comandos disponíveis',\n      timestamp: new Date()\n    }, {\n      id: '4',\n      type: 'output',\n      content: 'root@shack:~$ ',\n      timestamp: new Date()\n    }];\n    setTerminalLines(welcomeLines);\n  };\n  const addLine = (type, content) => {\n    const newLine = {\n      id: Date.now().toString(),\n      type,\n      content,\n      timestamp: new Date()\n    };\n    setTerminalLines(prev => [...prev, newLine]);\n  };\n  const executeCommand = async command => {\n    var _currentPlayer$dinhei, _bruteforceStatus;\n    // Adicionar comando digitado\n    addLine('input', `root@shack:~$ ${command}`);\n    const cmd = command.trim().toLowerCase();\n    const args = cmd.split(' ');\n    setIsExecuting(true);\n    try {\n      switch (args[0]) {\n        case 'help':\n          addLine('output', 'Comandos disponíveis:');\n          addLine('output', '  help - Mostra esta ajuda');\n          addLine('output', '  clear - Limpa o terminal');\n          addLine('output', '  status - Mostra status do sistema');\n          addLine('output', '  bruteforce <target> - Inicia ataque de força bruta');\n          addLine('output', '  scan - Escaneia rede local');\n          addLine('output', '  whoami - Mostra informações do usuário');\n          addLine('output', '  ps - Lista processos ativos');\n          break;\n        case 'clear':\n          setTerminalLines([]);\n          addLine('output', 'root@shack:~$ ');\n          break;\n        case 'status':\n          addLine('output', `Sistema: SHACK OS v2.0`);\n          addLine('output', `Usuário: ${(user === null || user === void 0 ? void 0 : user.nick) || 'Unknown'}`);\n          addLine('output', `IP: ${(user === null || user === void 0 ? void 0 : user.ip) || (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.ip) || 'Unknown'}`);\n          addLine('output', `CPU: Nível ${(currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.cpu) || 1}`);\n          addLine('output', `RAM: Nível ${(currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.ram) || 1}`);\n          addLine('output', `Firewall: Nível ${(currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.firewall) || 1}`);\n          addLine('output', `Dinheiro: $${(currentPlayer === null || currentPlayer === void 0 ? void 0 : (_currentPlayer$dinhei = currentPlayer.dinheiro) === null || _currentPlayer$dinhei === void 0 ? void 0 : _currentPlayer$dinhei.toLocaleString()) || '0'}`);\n          break;\n        case 'whoami':\n          addLine('output', `${(user === null || user === void 0 ? void 0 : user.nick) || 'Unknown'}`);\n          addLine('output', `UID: ${(user === null || user === void 0 ? void 0 : user.uid) || 'Unknown'}`);\n          addLine('output', `Email: ${(user === null || user === void 0 ? void 0 : user.email) || 'Unknown'}`);\n          addLine('output', `Nível: ${(currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.nivel) || 1}`);\n          break;\n        case 'ps':\n          addLine('output', 'PID  COMMAND');\n          addLine('output', '1    systemd');\n          addLine('output', '2    kthreadd');\n          addLine('output', '3    shack-daemon');\n          addLine('output', '4    network-scanner');\n          if ((_bruteforceStatus = bruteforceStatus) !== null && _bruteforceStatus !== void 0 && _bruteforceStatus.ativo) {\n            addLine('output', '5    bruteforce-engine');\n          }\n          break;\n        case 'bruteforce':\n          if (args.length < 2) {\n            addLine('error', 'Uso: bruteforce <target>');\n            break;\n          }\n          const target = args[1];\n          addLine('output', `Iniciando ataque de força bruta contra ${target}...`);\n          try {\n            const response = await gameApi.executeBruteforce(target, 'default');\n            if (response.sucesso) {\n              addLine('output', `✅ Bruteforce iniciado com sucesso!`);\n              addLine('output', `Target: ${target}`);\n              addLine('output', `Wordlist: default.txt`);\n              addLine('output', `Threads: ${(currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.bruteforce) || 1}`);\n\n              // Simular progresso\n              setTimeout(() => {\n                addLine('output', 'Testando senhas...');\n              }, 1000);\n              setTimeout(() => {\n                addLine('output', response.mensagem || 'Ataque concluído');\n              }, 3000);\n            } else {\n              addLine('error', response.mensagem || 'Falha no bruteforce');\n            }\n          } catch (error) {\n            addLine('error', `Erro: ${error.message}`);\n          }\n          break;\n        case 'scan':\n          addLine('output', 'Escaneando rede local...');\n          try {\n            const response = await gameApi.scanTargets();\n            if (response.sucesso && response.alvos) {\n              addLine('output', `Encontrados ${response.alvos.length} alvos:`);\n              response.alvos.slice(0, 5).forEach(alvo => {\n                addLine('output', `  ${alvo.ip} - ${alvo.nick} (Nível ${alvo.nivel})`);\n              });\n            } else {\n              addLine('output', 'Nenhum alvo encontrado');\n            }\n          } catch (error) {\n            addLine('error', `Erro no scan: ${error.message}`);\n          }\n          break;\n        case 'exit':\n          addLine('output', 'Saindo do terminal...');\n          setTimeout(() => {\n            window.history.back();\n          }, 1000);\n          break;\n        default:\n          if (cmd.trim()) {\n            addLine('error', `Comando não encontrado: ${args[0]}`);\n            addLine('output', 'Digite \"help\" para ver comandos disponíveis');\n          }\n          break;\n      }\n    } catch (error) {\n      addLine('error', `Erro: ${error.message}`);\n    } finally {\n      setIsExecuting(false);\n      addLine('output', 'root@shack:~$ ');\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (currentInput.trim() && !isExecuting) {\n      executeCommand(currentInput);\n      setCurrentInput('');\n    }\n  };\n  const getLineColor = type => {\n    switch (type) {\n      case 'input':\n        return 'text-white';\n      case 'output':\n        return 'text-green-400';\n      case 'error':\n        return 'text-red-400';\n      case 'system':\n        return 'text-blue-400';\n      default:\n        return 'text-gray-300';\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-900 text-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"\\uD83D\\uDD12 Acesso Negado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Voc\\xEA precisa estar logado para acessar o terminal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-black text-green-400 flex flex-col font-mono\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-900 border-b border-green-400 p-2 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-6 h-6 bg-gray-800 rounded flex items-center justify-center hover:bg-gray-700 text-green-400\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-green-400\",\n            children: \"\\u25CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"SHACK Terminal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500\",\n            children: [\"- \", user === null || user === void 0 ? void 0 : user.nick, \"@\", user === null || user === void 0 ? void 0 : user.ip]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: terminalRef,\n      className: \"flex-1 p-4 overflow-y-auto bg-black\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: [terminalLines.map(line => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${getLineColor(line.type)} text-sm leading-relaxed`,\n          children: line.content\n        }, line.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-green-400 mr-2\",\n            children: \"root@shack:~$\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            ref: inputRef,\n            type: \"text\",\n            value: currentInput,\n            onChange: e => setCurrentInput(e.target.value),\n            className: \"flex-1 bg-transparent text-green-400 outline-none\",\n            disabled: isExecuting,\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), isExecuting && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-yellow-400 ml-2 animate-pulse\",\n            children: \"\\u23F3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-900 border-t border-green-400 p-2 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"CPU: \", (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.cpu) || 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"RAM: \", (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.ram) || 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"BruteForce: \", (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.bruteforce) || 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-4\",\n          children: [((_bruteforceStatus2 = bruteforceStatus) === null || _bruteforceStatus2 === void 0 ? void 0 : _bruteforceStatus2.ativo) && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-yellow-400\",\n            children: \"\\uD83D\\uDD28 BruteForce Ativo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: new Date().toLocaleTimeString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(GameFooter, {\n      currentPage: \"terminal\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 258,\n    columnNumber: 5\n  }, this);\n};\n_s(TerminalPage, \"6/7Y299v+jXG/gPK9EiybhUkNe4=\", false, function () {\n  return [useAuth, usePlayer, useGameSystem];\n});\n_c = TerminalPage;\nexport default TerminalPage;\nvar _c;\n$RefreshReg$(_c, \"TerminalPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "usePlayer", "useGameSystem", "gameApi", "GameFooter", "jsxDEV", "_jsxDEV", "TerminalPage", "_s", "_bruteforceStatus2", "user", "isAuthenticated", "currentPlayer", "activeConnections", "activeBruteforces", "loadActiveConnections", "startBruteforce", "closeConnection", "terminalLines", "setTerminalLines", "currentInput", "setCurrentInput", "isExecuting", "setIsExecuting", "terminalRef", "inputRef", "initializeTerminal", "current", "scrollTop", "scrollHeight", "welcomeLines", "id", "type", "content", "timestamp", "Date", "nick", "addLine", "newLine", "now", "toString", "prev", "executeCommand", "command", "_currentPlayer$dinhei", "_bruteforceStatus", "cmd", "trim", "toLowerCase", "args", "split", "ip", "cpu", "ram", "firewall", "<PERSON><PERSON><PERSON>", "toLocaleString", "uid", "email", "nivel", "bruteforceStatus", "ativo", "length", "target", "response", "executeBruteforce", "sucesso", "bruteforce", "setTimeout", "mensagem", "error", "message", "scanTargets", "alvos", "slice", "for<PERSON>ach", "alvo", "window", "history", "back", "handleSubmit", "e", "preventDefault", "getLineColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "ref", "map", "line", "onSubmit", "value", "onChange", "disabled", "autoFocus", "toLocaleTimeString", "currentPage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/TerminalPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useGameSystem } from '../hooks/useGameSystem';\nimport gameApi from '../services/gameApi';\nimport GameFooter from '../components/common/GameFooter';\nimport { TerminalIcon, CloseIcon, BruteforceIcon } from '../components/ui/GameIcons';\n\ninterface TerminalLine {\n  id: string;\n  type: 'input' | 'output' | 'error' | 'system';\n  content: string;\n  timestamp: Date;\n}\n\nconst TerminalPage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const { currentPlayer } = usePlayer();\n  const {\n    activeConnections,\n    activeBruteforces,\n    loadActiveConnections,\n    startBruteforce,\n    closeConnection\n  } = useGameSystem();\n\n  const [terminalLines, setTerminalLines] = useState<TerminalLine[]>([]);\n  const [currentInput, setCurrentInput] = useState('');\n  const [isExecuting, setIsExecuting] = useState(false);\n  \n  const terminalRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      initializeTerminal();\n      loadActiveConnections();\n    }\n  }, [isAuthenticated, loadActiveConnections]);\n\n  useEffect(() => {\n    // Auto-scroll para o final\n    if (terminalRef.current) {\n      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;\n    }\n  }, [terminalLines]);\n\n  const initializeTerminal = () => {\n    const welcomeLines: TerminalLine[] = [\n      {\n        id: '1',\n        type: 'system',\n        content: '=== SHACK TERMINAL v2.0 ===',\n        timestamp: new Date(),\n      },\n      {\n        id: '2',\n        type: 'system',\n        content: `Bem-vindo, ${user?.nick || 'Hacker'}`,\n        timestamp: new Date(),\n      },\n      {\n        id: '3',\n        type: 'system',\n        content: 'Digite \"help\" para ver comandos disponíveis',\n        timestamp: new Date(),\n      },\n      {\n        id: '4',\n        type: 'output',\n        content: 'root@shack:~$ ',\n        timestamp: new Date(),\n      },\n    ];\n\n    setTerminalLines(welcomeLines);\n  };\n\n\n\n  const addLine = (type: TerminalLine['type'], content: string) => {\n    const newLine: TerminalLine = {\n      id: Date.now().toString(),\n      type,\n      content,\n      timestamp: new Date(),\n    };\n\n    setTerminalLines(prev => [...prev, newLine]);\n  };\n\n  const executeCommand = async (command: string) => {\n    // Adicionar comando digitado\n    addLine('input', `root@shack:~$ ${command}`);\n\n    const cmd = command.trim().toLowerCase();\n    const args = cmd.split(' ');\n\n    setIsExecuting(true);\n\n    try {\n      switch (args[0]) {\n        case 'help':\n          addLine('output', 'Comandos disponíveis:');\n          addLine('output', '  help - Mostra esta ajuda');\n          addLine('output', '  clear - Limpa o terminal');\n          addLine('output', '  status - Mostra status do sistema');\n          addLine('output', '  bruteforce <target> - Inicia ataque de força bruta');\n          addLine('output', '  scan - Escaneia rede local');\n          addLine('output', '  whoami - Mostra informações do usuário');\n          addLine('output', '  ps - Lista processos ativos');\n          break;\n\n        case 'clear':\n          setTerminalLines([]);\n          addLine('output', 'root@shack:~$ ');\n          break;\n\n        case 'status':\n          addLine('output', `Sistema: SHACK OS v2.0`);\n          addLine('output', `Usuário: ${user?.nick || 'Unknown'}`);\n          addLine('output', `IP: ${user?.ip || currentPlayer?.ip || 'Unknown'}`);\n          addLine('output', `CPU: Nível ${currentPlayer?.cpu || 1}`);\n          addLine('output', `RAM: Nível ${currentPlayer?.ram || 1}`);\n          addLine('output', `Firewall: Nível ${currentPlayer?.firewall || 1}`);\n          addLine('output', `Dinheiro: $${currentPlayer?.dinheiro?.toLocaleString() || '0'}`);\n          break;\n\n        case 'whoami':\n          addLine('output', `${user?.nick || 'Unknown'}`);\n          addLine('output', `UID: ${user?.uid || 'Unknown'}`);\n          addLine('output', `Email: ${user?.email || 'Unknown'}`);\n          addLine('output', `Nível: ${currentPlayer?.nivel || 1}`);\n          break;\n\n        case 'ps':\n          addLine('output', 'PID  COMMAND');\n          addLine('output', '1    systemd');\n          addLine('output', '2    kthreadd');\n          addLine('output', '3    shack-daemon');\n          addLine('output', '4    network-scanner');\n          if (bruteforceStatus?.ativo) {\n            addLine('output', '5    bruteforce-engine');\n          }\n          break;\n\n        case 'bruteforce':\n          if (args.length < 2) {\n            addLine('error', 'Uso: bruteforce <target>');\n            break;\n          }\n\n          const target = args[1];\n          addLine('output', `Iniciando ataque de força bruta contra ${target}...`);\n          \n          try {\n            const response = await gameApi.executeBruteforce(target, 'default');\n            \n            if (response.sucesso) {\n              addLine('output', `✅ Bruteforce iniciado com sucesso!`);\n              addLine('output', `Target: ${target}`);\n              addLine('output', `Wordlist: default.txt`);\n              addLine('output', `Threads: ${currentPlayer?.bruteforce || 1}`);\n              \n              // Simular progresso\n              setTimeout(() => {\n                addLine('output', 'Testando senhas...');\n              }, 1000);\n              \n              setTimeout(() => {\n                addLine('output', response.mensagem || 'Ataque concluído');\n              }, 3000);\n            } else {\n              addLine('error', response.mensagem || 'Falha no bruteforce');\n            }\n          } catch (error: any) {\n            addLine('error', `Erro: ${error.message}`);\n          }\n          break;\n\n        case 'scan':\n          addLine('output', 'Escaneando rede local...');\n          \n          try {\n            const response = await gameApi.scanTargets();\n            \n            if (response.sucesso && response.alvos) {\n              addLine('output', `Encontrados ${response.alvos.length} alvos:`);\n              response.alvos.slice(0, 5).forEach((alvo: any) => {\n                addLine('output', `  ${alvo.ip} - ${alvo.nick} (Nível ${alvo.nivel})`);\n              });\n            } else {\n              addLine('output', 'Nenhum alvo encontrado');\n            }\n          } catch (error: any) {\n            addLine('error', `Erro no scan: ${error.message}`);\n          }\n          break;\n\n        case 'exit':\n          addLine('output', 'Saindo do terminal...');\n          setTimeout(() => {\n            window.history.back();\n          }, 1000);\n          break;\n\n        default:\n          if (cmd.trim()) {\n            addLine('error', `Comando não encontrado: ${args[0]}`);\n            addLine('output', 'Digite \"help\" para ver comandos disponíveis');\n          }\n          break;\n      }\n    } catch (error: any) {\n      addLine('error', `Erro: ${error.message}`);\n    } finally {\n      setIsExecuting(false);\n      addLine('output', 'root@shack:~$ ');\n    }\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (currentInput.trim() && !isExecuting) {\n      executeCommand(currentInput);\n      setCurrentInput('');\n    }\n  };\n\n  const getLineColor = (type: TerminalLine['type']) => {\n    switch (type) {\n      case 'input':\n        return 'text-white';\n      case 'output':\n        return 'text-green-400';\n      case 'error':\n        return 'text-red-400';\n      case 'system':\n        return 'text-blue-400';\n      default:\n        return 'text-gray-300';\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"h-screen bg-gray-900 text-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-4\">🔒 Acesso Negado</h1>\n          <p className=\"text-gray-400\">Você precisa estar logado para acessar o terminal</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-black text-green-400 flex flex-col font-mono\">\n      {/* Header */}\n      <div className=\"bg-gray-900 border-b border-green-400 p-2 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"w-6 h-6 bg-gray-800 rounded flex items-center justify-center hover:bg-gray-700 text-green-400\"\n          >\n            <span className=\"text-sm\">←</span>\n          </button>\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-green-400\">●</span>\n            <span className=\"text-sm\">SHACK Terminal</span>\n            <span className=\"text-xs text-gray-500\">- {user?.nick}@{user?.ip}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Terminal */}\n      <div \n        ref={terminalRef}\n        className=\"flex-1 p-4 overflow-y-auto bg-black\"\n      >\n        <div className=\"space-y-1\">\n          {terminalLines.map((line) => (\n            <div \n              key={line.id} \n              className={`${getLineColor(line.type)} text-sm leading-relaxed`}\n            >\n              {line.content}\n            </div>\n          ))}\n          \n          {/* Input line */}\n          <form onSubmit={handleSubmit} className=\"flex items-center\">\n            <span className=\"text-green-400 mr-2\">root@shack:~$</span>\n            <input\n              ref={inputRef}\n              type=\"text\"\n              value={currentInput}\n              onChange={(e) => setCurrentInput(e.target.value)}\n              className=\"flex-1 bg-transparent text-green-400 outline-none\"\n              disabled={isExecuting}\n              autoFocus\n            />\n            {isExecuting && (\n              <span className=\"text-yellow-400 ml-2 animate-pulse\">⏳</span>\n            )}\n          </form>\n        </div>\n      </div>\n\n      {/* Status bar */}\n      <div className=\"bg-gray-900 border-t border-green-400 p-2 flex-shrink-0\">\n        <div className=\"flex justify-between items-center text-xs\">\n          <div className=\"flex space-x-4\">\n            <span>CPU: {currentPlayer?.cpu || 1}</span>\n            <span>RAM: {currentPlayer?.ram || 1}</span>\n            <span>BruteForce: {currentPlayer?.bruteforce || 1}</span>\n          </div>\n          <div className=\"flex space-x-4\">\n            {bruteforceStatus?.ativo && (\n              <span className=\"text-yellow-400\">🔨 BruteForce Ativo</span>\n            )}\n            <span>{new Date().toLocaleTimeString()}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer com navegação */}\n      <GameFooter currentPage=\"terminal\" />\n    </div>\n  );\n};\n\nexport default TerminalPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,UAAU,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUzD,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACnC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEY;EAAc,CAAC,GAAGX,SAAS,CAAC,CAAC;EACrC,MAAM;IACJY,iBAAiB;IACjBC,iBAAiB;IACjBC,qBAAqB;IACrBC,eAAe;IACfC;EACF,CAAC,GAAGf,aAAa,CAAC,CAAC;EAEnB,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM2B,WAAW,GAAGzB,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAM0B,QAAQ,GAAG1B,MAAM,CAAmB,IAAI,CAAC;EAE/CD,SAAS,CAAC,MAAM;IACd,IAAIa,eAAe,EAAE;MACnBe,kBAAkB,CAAC,CAAC;MACpBX,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACJ,eAAe,EAAEI,qBAAqB,CAAC,CAAC;EAE5CjB,SAAS,CAAC,MAAM;IACd;IACA,IAAI0B,WAAW,CAACG,OAAO,EAAE;MACvBH,WAAW,CAACG,OAAO,CAACC,SAAS,GAAGJ,WAAW,CAACG,OAAO,CAACE,YAAY;IAClE;EACF,CAAC,EAAE,CAACX,aAAa,CAAC,CAAC;EAEnB,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMI,YAA4B,GAAG,CACnC;MACEC,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,6BAA6B;MACtCC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,EACD;MACEJ,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,cAAc,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,KAAI,QAAQ,EAAE;MAC/CF,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,EACD;MACEJ,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,6CAA6C;MACtDC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,EACD;MACEJ,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,gBAAgB;MACzBC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,CACF;IAEDhB,gBAAgB,CAACW,YAAY,CAAC;EAChC,CAAC;EAID,MAAMO,OAAO,GAAGA,CAACL,IAA0B,EAAEC,OAAe,KAAK;IAC/D,MAAMK,OAAqB,GAAG;MAC5BP,EAAE,EAAEI,IAAI,CAACI,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBR,IAAI;MACJC,OAAO;MACPC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDhB,gBAAgB,CAACsB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,OAAO,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMI,cAAc,GAAG,MAAOC,OAAe,IAAK;IAAA,IAAAC,qBAAA,EAAAC,iBAAA;IAChD;IACAR,OAAO,CAAC,OAAO,EAAE,iBAAiBM,OAAO,EAAE,CAAC;IAE5C,MAAMG,GAAG,GAAGH,OAAO,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACxC,MAAMC,IAAI,GAAGH,GAAG,CAACI,KAAK,CAAC,GAAG,CAAC;IAE3B3B,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,QAAQ0B,IAAI,CAAC,CAAC,CAAC;QACb,KAAK,MAAM;UACTZ,OAAO,CAAC,QAAQ,EAAE,uBAAuB,CAAC;UAC1CA,OAAO,CAAC,QAAQ,EAAE,4BAA4B,CAAC;UAC/CA,OAAO,CAAC,QAAQ,EAAE,4BAA4B,CAAC;UAC/CA,OAAO,CAAC,QAAQ,EAAE,qCAAqC,CAAC;UACxDA,OAAO,CAAC,QAAQ,EAAE,sDAAsD,CAAC;UACzEA,OAAO,CAAC,QAAQ,EAAE,8BAA8B,CAAC;UACjDA,OAAO,CAAC,QAAQ,EAAE,0CAA0C,CAAC;UAC7DA,OAAO,CAAC,QAAQ,EAAE,+BAA+B,CAAC;UAClD;QAEF,KAAK,OAAO;UACVlB,gBAAgB,CAAC,EAAE,CAAC;UACpBkB,OAAO,CAAC,QAAQ,EAAE,gBAAgB,CAAC;UACnC;QAEF,KAAK,QAAQ;UACXA,OAAO,CAAC,QAAQ,EAAE,wBAAwB,CAAC;UAC3CA,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,KAAI,SAAS,EAAE,CAAC;UACxDC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,EAAE,MAAIvC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuC,EAAE,KAAI,SAAS,EAAE,CAAC;UACtEd,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAAzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwC,GAAG,KAAI,CAAC,EAAE,CAAC;UAC1Df,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAAzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyC,GAAG,KAAI,CAAC,EAAE,CAAC;UAC1DhB,OAAO,CAAC,QAAQ,EAAE,mBAAmB,CAAAzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0C,QAAQ,KAAI,CAAC,EAAE,CAAC;UACpEjB,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAAzB,aAAa,aAAbA,aAAa,wBAAAgC,qBAAA,GAAbhC,aAAa,CAAE2C,QAAQ,cAAAX,qBAAA,uBAAvBA,qBAAA,CAAyBY,cAAc,CAAC,CAAC,KAAI,GAAG,EAAE,CAAC;UACnF;QAEF,KAAK,QAAQ;UACXnB,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,KAAI,SAAS,EAAE,CAAC;UAC/CC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,GAAG,KAAI,SAAS,EAAE,CAAC;UACnDpB,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgD,KAAK,KAAI,SAAS,EAAE,CAAC;UACvDrB,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAAzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+C,KAAK,KAAI,CAAC,EAAE,CAAC;UACxD;QAEF,KAAK,IAAI;UACPtB,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC;UACjCA,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC;UACjCA,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC;UAClCA,OAAO,CAAC,QAAQ,EAAE,mBAAmB,CAAC;UACtCA,OAAO,CAAC,QAAQ,EAAE,sBAAsB,CAAC;UACzC,KAAAQ,iBAAA,GAAIe,gBAAgB,cAAAf,iBAAA,eAAhBA,iBAAA,CAAkBgB,KAAK,EAAE;YAC3BxB,OAAO,CAAC,QAAQ,EAAE,wBAAwB,CAAC;UAC7C;UACA;QAEF,KAAK,YAAY;UACf,IAAIY,IAAI,CAACa,MAAM,GAAG,CAAC,EAAE;YACnBzB,OAAO,CAAC,OAAO,EAAE,0BAA0B,CAAC;YAC5C;UACF;UAEA,MAAM0B,MAAM,GAAGd,IAAI,CAAC,CAAC,CAAC;UACtBZ,OAAO,CAAC,QAAQ,EAAE,0CAA0C0B,MAAM,KAAK,CAAC;UAExE,IAAI;YACF,MAAMC,QAAQ,GAAG,MAAM7D,OAAO,CAAC8D,iBAAiB,CAACF,MAAM,EAAE,SAAS,CAAC;YAEnE,IAAIC,QAAQ,CAACE,OAAO,EAAE;cACpB7B,OAAO,CAAC,QAAQ,EAAE,oCAAoC,CAAC;cACvDA,OAAO,CAAC,QAAQ,EAAE,WAAW0B,MAAM,EAAE,CAAC;cACtC1B,OAAO,CAAC,QAAQ,EAAE,uBAAuB,CAAC;cAC1CA,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAAzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuD,UAAU,KAAI,CAAC,EAAE,CAAC;;cAE/D;cACAC,UAAU,CAAC,MAAM;gBACf/B,OAAO,CAAC,QAAQ,EAAE,oBAAoB,CAAC;cACzC,CAAC,EAAE,IAAI,CAAC;cAER+B,UAAU,CAAC,MAAM;gBACf/B,OAAO,CAAC,QAAQ,EAAE2B,QAAQ,CAACK,QAAQ,IAAI,kBAAkB,CAAC;cAC5D,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACLhC,OAAO,CAAC,OAAO,EAAE2B,QAAQ,CAACK,QAAQ,IAAI,qBAAqB,CAAC;YAC9D;UACF,CAAC,CAAC,OAAOC,KAAU,EAAE;YACnBjC,OAAO,CAAC,OAAO,EAAE,SAASiC,KAAK,CAACC,OAAO,EAAE,CAAC;UAC5C;UACA;QAEF,KAAK,MAAM;UACTlC,OAAO,CAAC,QAAQ,EAAE,0BAA0B,CAAC;UAE7C,IAAI;YACF,MAAM2B,QAAQ,GAAG,MAAM7D,OAAO,CAACqE,WAAW,CAAC,CAAC;YAE5C,IAAIR,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACS,KAAK,EAAE;cACtCpC,OAAO,CAAC,QAAQ,EAAE,eAAe2B,QAAQ,CAACS,KAAK,CAACX,MAAM,SAAS,CAAC;cAChEE,QAAQ,CAACS,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAEC,IAAS,IAAK;gBAChDvC,OAAO,CAAC,QAAQ,EAAE,KAAKuC,IAAI,CAACzB,EAAE,MAAMyB,IAAI,CAACxC,IAAI,WAAWwC,IAAI,CAACjB,KAAK,GAAG,CAAC;cACxE,CAAC,CAAC;YACJ,CAAC,MAAM;cACLtB,OAAO,CAAC,QAAQ,EAAE,wBAAwB,CAAC;YAC7C;UACF,CAAC,CAAC,OAAOiC,KAAU,EAAE;YACnBjC,OAAO,CAAC,OAAO,EAAE,iBAAiBiC,KAAK,CAACC,OAAO,EAAE,CAAC;UACpD;UACA;QAEF,KAAK,MAAM;UACTlC,OAAO,CAAC,QAAQ,EAAE,uBAAuB,CAAC;UAC1C+B,UAAU,CAAC,MAAM;YACfS,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC;UACvB,CAAC,EAAE,IAAI,CAAC;UACR;QAEF;UACE,IAAIjC,GAAG,CAACC,IAAI,CAAC,CAAC,EAAE;YACdV,OAAO,CAAC,OAAO,EAAE,2BAA2BY,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YACtDZ,OAAO,CAAC,QAAQ,EAAE,6CAA6C,CAAC;UAClE;UACA;MACJ;IACF,CAAC,CAAC,OAAOiC,KAAU,EAAE;MACnBjC,OAAO,CAAC,OAAO,EAAE,SAASiC,KAAK,CAACC,OAAO,EAAE,CAAC;IAC5C,CAAC,SAAS;MACRhD,cAAc,CAAC,KAAK,CAAC;MACrBc,OAAO,CAAC,QAAQ,EAAE,gBAAgB,CAAC;IACrC;EACF,CAAC;EAED,MAAM2C,YAAY,GAAIC,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI9D,YAAY,CAAC2B,IAAI,CAAC,CAAC,IAAI,CAACzB,WAAW,EAAE;MACvCoB,cAAc,CAACtB,YAAY,CAAC;MAC5BC,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC;EAED,MAAM8D,YAAY,GAAInD,IAA0B,IAAK;IACnD,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,QAAQ;QACX,OAAO,gBAAgB;MACzB,KAAK,OAAO;QACV,OAAO,cAAc;MACvB,KAAK,QAAQ;QACX,OAAO,eAAe;MACxB;QACE,OAAO,eAAe;IAC1B;EACF,CAAC;EAED,IAAI,CAACrB,eAAe,EAAE;IACpB,oBACEL,OAAA;MAAK8E,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/E/E,OAAA;QAAK8E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B/E,OAAA;UAAI8E,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DnF,OAAA;UAAG8E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnF,OAAA;IAAK8E,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBAEvE/E,OAAA;MAAK8E,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtE/E,OAAA;QAAK8E,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C/E,OAAA;UACEoF,OAAO,EAAEA,CAAA,KAAMb,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCK,SAAS,EAAC,+FAA+F;UAAAC,QAAA,eAEzG/E,OAAA;YAAM8E,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACTnF,OAAA;UAAK8E,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C/E,OAAA;YAAM8E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCnF,OAAA;YAAM8E,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CnF,OAAA;YAAM8E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,IAAE,EAAC3E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,EAAC,GAAC,EAAC1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,EAAE;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MACEqF,GAAG,EAAEnE,WAAY;MACjB4D,SAAS,EAAC,qCAAqC;MAAAC,QAAA,eAE/C/E,OAAA;QAAK8E,SAAS,EAAC,WAAW;QAAAC,QAAA,GACvBnE,aAAa,CAAC0E,GAAG,CAAEC,IAAI,iBACtBvF,OAAA;UAEE8E,SAAS,EAAE,GAAGD,YAAY,CAACU,IAAI,CAAC7D,IAAI,CAAC,0BAA2B;UAAAqD,QAAA,EAE/DQ,IAAI,CAAC5D;QAAO,GAHR4D,IAAI,CAAC9D,EAAE;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIT,CACN,CAAC,eAGFnF,OAAA;UAAMwF,QAAQ,EAAEd,YAAa;UAACI,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACzD/E,OAAA;YAAM8E,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1DnF,OAAA;YACEqF,GAAG,EAAElE,QAAS;YACdO,IAAI,EAAC,MAAM;YACX+D,KAAK,EAAE3E,YAAa;YACpB4E,QAAQ,EAAGf,CAAC,IAAK5D,eAAe,CAAC4D,CAAC,CAAClB,MAAM,CAACgC,KAAK,CAAE;YACjDX,SAAS,EAAC,mDAAmD;YAC7Da,QAAQ,EAAE3E,WAAY;YACtB4E,SAAS;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EACDnE,WAAW,iBACVhB,OAAA;YAAM8E,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC7D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAK8E,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtE/E,OAAA;QAAK8E,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxD/E,OAAA;UAAK8E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/E,OAAA;YAAA+E,QAAA,GAAM,OAAK,EAAC,CAAAzE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwC,GAAG,KAAI,CAAC;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CnF,OAAA;YAAA+E,QAAA,GAAM,OAAK,EAAC,CAAAzE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyC,GAAG,KAAI,CAAC;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CnF,OAAA;YAAA+E,QAAA,GAAM,cAAY,EAAC,CAAAzE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuD,UAAU,KAAI,CAAC;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNnF,OAAA;UAAK8E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAC5B,EAAA5E,kBAAA,GAAAmD,gBAAgB,cAAAnD,kBAAA,uBAAhBA,kBAAA,CAAkBoD,KAAK,kBACtBvD,OAAA;YAAM8E,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC5D,eACDnF,OAAA;YAAA+E,QAAA,EAAO,IAAIlD,IAAI,CAAC,CAAC,CAACgE,kBAAkB,CAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA,CAACF,UAAU;MAACgG,WAAW,EAAC;IAAU;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CAAC;AAEV,CAAC;AAACjF,EAAA,CA3TID,YAAsB;EAAA,QACQP,OAAO,EACfC,SAAS,EAO/BC,aAAa;AAAA;AAAAmG,EAAA,GATb9F,YAAsB;AA6T5B,eAAeA,YAAY;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}