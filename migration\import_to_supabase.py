"""
Script para importar dados do Firestore para Supabase
Execute: python migration/import_to_supabase.py
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any
import uuid

# Adiciona o diretório pai ao path para importar o módulo supabase
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.supabase_client import supabase_manager

def load_exported_data(export_file: str) -> Dict[str, List[Dict]]:
    """Carrega dados exportados do Firestore"""
    try:
        with open(export_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo {export_file}: {e}")
        return {}

def transform_user_data(firestore_user: Dict[str, Any]) -> Dict[str, Any]:
    """Transforma dados do usuário do Firestore para formato Supabase"""
    return {
        'uid': firestore_user.get('uid'),
        'nick': firestore_user.get('nick'),
        'email': firestore_user.get('email'),
        'ip': firestore_user.get('ip'),
        'dinheiro': firestore_user.get('dinheiro', 1000),
        'shack': firestore_user.get('shack', 100),
        'cpu': firestore_user.get('cpu', 1),
        'firewall': firestore_user.get('firewall', 1),
        'antivirus': firestore_user.get('antivirus', 1),
        'malware_kit': firestore_user.get('malware_kit', 1),
        'nivel': firestore_user.get('nivel', 1),
        'xp': firestore_user.get('xp', 0),
        'nivel_mineradora': firestore_user.get('nivel_mineradora', 1),
        'ultimo_recurso_coletado_timestamp': firestore_user.get('ultimo_recurso_coletado_timestamp'),
        'deface_points_individual': firestore_user.get('deface_points_individual', 0),
        'tournament_points_individual': firestore_user.get('tournament_points_individual', 0),
        'last_deface_timestamp': firestore_user.get('last_deface_timestamp', 0),
        'habilidades_adquiridas': firestore_user.get('habilidades_adquiridas', {}),
        'historico': firestore_user.get('historico', []),
        'log': firestore_user.get('log', []),
        'is_admin': firestore_user.get('is_admin', False),
        'is_active': True
    }

def transform_group_data(firestore_group: Dict[str, Any]) -> Dict[str, Any]:
    """Transforma dados do grupo do Firestore para formato Supabase"""
    return {
        'nome': firestore_group.get('nome'),
        'lider_uid': firestore_group.get('lider'),
        'membros': firestore_group.get('membros', []),
        'max_membros': firestore_group.get('max_membros', 5),
        'deface_points': firestore_group.get('deface_points', 0),
        'tournament_points': firestore_group.get('tournament_points', 0),
        'is_active': True
    }

def transform_nft_skill_data(firestore_skill: Dict[str, Any], skill_id: str) -> Dict[str, Any]:
    """Transforma dados da habilidade NFT do Firestore para formato Supabase"""
    return {
        'skill_id': skill_id,
        'nome': firestore_skill.get('nome'),
        'descricao': firestore_skill.get('descricao'),
        'efeito': float(firestore_skill.get('efeito', 0)),
        'preco_shack': firestore_skill.get('preco_shack'),
        'estoque_maximo': firestore_skill.get('estoque_maximo'),
        'estoque_atual': firestore_skill.get('estoque_atual'),
        'is_active': True
    }

def import_users(users_data: List[Dict[str, Any]]) -> int:
    """Importa usuários para Supabase"""
    print("👥 Importando usuários...")
    success_count = 0
    
    for user in users_data:
        try:
            transformed_user = transform_user_data(user)
            
            # Verifica se campos obrigatórios existem
            if not all(k in transformed_user and transformed_user[k] for k in ['uid', 'nick', 'email', 'ip']):
                print(f"⚠️ Usuário com dados incompletos ignorado: {user.get('nick', 'sem nome')}")
                continue
            
            result = supabase_manager.create_user(transformed_user)
            
            if result['sucesso']:
                success_count += 1
                print(f"✅ Usuário importado: {transformed_user['nick']}")
            else:
                print(f"❌ Erro ao importar usuário {transformed_user['nick']}: {result['erro']}")
        
        except Exception as e:
            print(f"❌ Erro ao processar usuário: {e}")
    
    print(f"👥 Usuários importados: {success_count}/{len(users_data)}")
    return success_count

def import_groups(groups_data: List[Dict[str, Any]]) -> tuple[int, Dict[str, str]]:
    """Importa grupos para Supabase"""
    print("👥 Importando grupos...")
    success_count = 0
    group_mapping = {}  # Para mapear nomes de grupos para IDs
    
    for group in groups_data:
        try:
            transformed_group = transform_group_data(group)
            
            # Verifica se campos obrigatórios existem
            if not all(k in transformed_group and transformed_group[k] for k in ['nome', 'lider_uid']):
                print(f"⚠️ Grupo com dados incompletos ignorado: {group.get('nome', 'sem nome')}")
                continue
            
            result = supabase_manager.create_group(transformed_group)
            
            if result['sucesso']:
                success_count += 1
                group_mapping[transformed_group['nome']] = result['data']['id']
                print(f"✅ Grupo importado: {transformed_group['nome']}")
            else:
                print(f"❌ Erro ao importar grupo {transformed_group['nome']}: {result['erro']}")
        
        except Exception as e:
            print(f"❌ Erro ao processar grupo: {e}")
    
    print(f"👥 Grupos importados: {success_count}/{len(groups_data)}")
    return success_count, group_mapping

def import_nft_skills(skills_data: Dict[str, Any]) -> int:
    """Importa habilidades NFT para Supabase"""
    print("⚡ Importando habilidades NFT...")
    success_count = 0
    
    for skill_id, skill_data in skills_data.items():
        try:
            transformed_skill = transform_nft_skill_data(skill_data, skill_id)
            
            # Verifica se já existe para evitar duplicatas
            existing = supabase_manager.get_nft_skill(skill_id)
            if existing:
                print(f"⚠️ Habilidade já existe, ignorando: {skill_id}")
                continue
            
            result = supabase_manager.create_nft_skill(transformed_skill)
            
            if result['sucesso']:
                success_count += 1
                print(f"✅ Habilidade NFT importada: {transformed_skill['nome']}")
            else:
                print(f"❌ Erro ao importar habilidade {transformed_skill['nome']}: {result['erro']}")
        
        except Exception as e:
            print(f"❌ Erro ao processar habilidade: {e}")
    
    print(f"⚡ Habilidades NFT importadas: {success_count}/{len(skills_data)}")
    return success_count

def update_user_group_references(users_data: List[Dict[str, Any]], group_mapping: Dict[str, str]) -> int:
    """Atualiza referências de grupos nos usuários"""
    print("🔗 Atualizando referências de grupos...")
    success_count = 0
    
    for user in users_data:
        try:
            # Verifica se usuário tem grupo (pode estar em diferentes campos)
            group_name = None
            if 'group' in user and user['group']:
                group_name = user['group']
            elif 'grupo' in user and user['grupo']:
                group_name = user['grupo']
            
            if group_name and group_name in group_mapping:
                group_id = group_mapping[group_name]
                uid = user['uid']
                
                result = supabase_manager.update_user(uid, {'grupo_id': group_id})
                if result['sucesso']:
                    success_count += 1
                    print(f"✅ Grupo atualizado para usuário: {user['nick']}")
                else:
                    print(f"❌ Erro ao atualizar grupo para {user['nick']}: {result['erro']}")
        
        except Exception as e:
            print(f"❌ Erro ao atualizar referência de grupo: {e}")
    
    print(f"🔗 Referências de grupo atualizadas: {success_count}")
    return success_count

def main():
    """Função principal de importação"""
    print("🚀 Iniciando importação para Supabase...")
    print("="*50)
    
    # Verifica se o cliente Supabase está funcionando
    if not supabase_manager.client:
        print("❌ Falha ao conectar com Supabase.")
        print("📝 Verifique se as variáveis de ambiente estão configuradas:")
        print("   SUPABASE_URL=https://sua-instancia.supabase.co")
        print("   SUPABASE_ANON_KEY=sua-chave-anonima")
        return
    
    # Procura por arquivos de exportação
    export_dir = "migration/firestore_export"
    if not os.path.exists(export_dir):
        print(f"❌ Diretório {export_dir} não encontrado.")
        print("🔧 Execute primeiro: python migration/export_firestore_data.py")
        return
    
    # Encontra o arquivo de exportação mais recente
    export_files = [f for f in os.listdir(export_dir) if f.startswith('firestore_full_export_')]
    if not export_files:
        print(f"❌ Nenhum arquivo de exportação encontrado em {export_dir}")
        print("🔧 Execute primeiro: python migration/export_firestore_data.py")
        return
    
    latest_export = sorted(export_files)[-1]
    export_path = os.path.join(export_dir, latest_export)
    
    print(f"📁 Carregando dados de: {export_path}")
    
    # Carrega dados exportados
    data = load_exported_data(export_path)
    if not data:
        print("❌ Falha ao carregar dados")
        return
    
    print(f"📊 Dados carregados:")
    for collection, items in data.items():
        print(f"  {collection}: {len(items)} itens")
    
    # Contadores de estatísticas
    stats = {
        'usuarios': 0,
        'grupos': 0,
        'habilidades_nft': 0,
        'referencias_grupo': 0
    }
    
    # Importa na ordem correta (grupos primeiro, depois usuários com referências)
    group_mapping = {}
    
    # 1. Importa grupos
    if 'grupos' in data and data['grupos']:
        stats['grupos'], group_mapping = import_groups(data['grupos'])
    
    # 2. Importa usuários (sem referências de grupo ainda)
    if 'usuarios' in data and data['usuarios']:
        stats['usuarios'] = import_users(data['usuarios'])
    
    # 3. Atualiza referências de grupos nos usuários
    if 'usuarios' in data and data['usuarios'] and group_mapping:
        stats['referencias_grupo'] = update_user_group_references(data['usuarios'], group_mapping)
    
    # 4. Importa habilidades NFT
    if 'habilidades_nft' in data and data['habilidades_nft']:
        stats['habilidades_nft'] = import_nft_skills(data['habilidades_nft'])
    
    # Estatísticas finais
    print("\n" + "="*60)
    print("✅ IMPORTAÇÃO CONCLUÍDA!")
    print("📊 ESTATÍSTICAS:")
    for collection, count in stats.items():
        print(f"  {collection}: {count} itens importados")
    print("="*60)
    
    print("\n🎯 Próximos passos:")
    print("1. Verifique os dados no painel do Supabase")
    print("2. Teste as funções básicas da aplicação")
    print("3. Substitua o models.py pelo novo supabase_models.py")

if __name__ == "__main__":
    # Carrega variáveis de ambiente se existir arquivo .env
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("⚠️ python-dotenv não instalado. Configure as variáveis de ambiente manualmente.")
    
    main()
