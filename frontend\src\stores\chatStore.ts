import { create } from 'zustand';
import mockApiService from '../services/mockApi';

export interface ChatMessage {
  id: number;
  usuario: string;
  mensagem: string;
  timestamp: string;
  tipo: 'global' | 'grupo' | 'privado';
}

interface ChatState {
  // Estado das mensagens
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  
  // Estado de envio
  isSending: boolean;
  sendError: string | null;
  
  // Configurações
  isPolling: boolean;
  pollInterval: number;
  lastMessageTime: string | null;
  unreadCount: number;
  
  // Estado da UI
  isOpen: boolean;
  isMinimized: boolean;
  
  // Ações
  loadMessages: () => Promise<void>;
  sendMessage: (message: string) => Promise<void>;
  startPolling: () => void;
  stopPolling: () => void;
  markAsRead: () => void;
  
  // UI Actions
  openChat: () => void;
  closeChat: () => void;
  toggleChat: () => void;
  minimizeChat: () => void;
  maximizeChat: () => void;
  
  // Utility actions
  clearError: () => void;
  clearMessages: () => void;
}

let pollIntervalId: NodeJS.Timeout | null = null;

export const useChatStore = create<ChatState>((set, get) => ({
  // Estado inicial
  messages: [],
  isLoading: false,
  error: null,
  
  isSending: false,
  sendError: null,
  
  isPolling: false,
  pollInterval: 2000, // 2 segundos
  lastMessageTime: null,
  unreadCount: 0,
  
  isOpen: false,
  isMinimized: false,

  // === AÇÕES DE MENSAGENS ===
  loadMessages: async () => {
    const state = get();
    if (state.isLoading) return; // Evitar múltiplas requisições simultâneas

    console.log('ChatStore - Carregando mensagens...');
    set({ isLoading: true, error: null });

    try {
      const response = await mockApiService.getChatMessages();
      
      if (response.sucesso && response.dados) {
        const newMessages = response.dados;
        const currentMessages = state.messages;
        
        // Verificar se há mensagens novas
        const lastCurrentMessageTime = currentMessages.length > 0 
          ? currentMessages[currentMessages.length - 1].timestamp 
          : null;
        
        const newUnreadCount = lastCurrentMessageTime
          ? newMessages.filter((msg: ChatMessage) => msg.timestamp > lastCurrentMessageTime).length
          : newMessages.length;
        
        set({
          messages: newMessages,
          isLoading: false,
          error: null,
          lastMessageTime: newMessages.length > 0 
            ? newMessages[newMessages.length - 1].timestamp 
            : state.lastMessageTime,
          unreadCount: state.isOpen ? 0 : state.unreadCount + newUnreadCount,
        });
      } else {
        throw new Error(response.mensagem || 'Erro ao carregar mensagens');
      }
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || 'Erro ao carregar mensagens',
      });
    }
  },

  sendMessage: async (message: string) => {
    if (!message.trim()) return;

    console.log('ChatStore - Enviando mensagem:', message);
    set({ isSending: true, sendError: null });

    try {
      const response = await mockApiService.sendChatMessage({
        mensagem: message.trim(),
        tipo: 'global'
      });
      
      if (response.sucesso) {
        set({
          isSending: false,
          sendError: null,
        });
        
        // Recarregar mensagens após enviar
        setTimeout(() => {
          get().loadMessages();
        }, 500);
      } else {
        throw new Error(response.mensagem || 'Erro ao enviar mensagem');
      }
    } catch (error: any) {
      set({
        isSending: false,
        sendError: error.message || 'Erro ao enviar mensagem',
      });
      throw error;
    }
  },

  // === AÇÕES DE POLLING ===
  startPolling: () => {
    const state = get();
    if (state.isPolling) return;
    
    set({ isPolling: true });
    
    // Carregar mensagens imediatamente
    get().loadMessages();
    
    // Configurar polling
    pollIntervalId = setInterval(() => {
      get().loadMessages();
    }, state.pollInterval);
  },

  stopPolling: () => {
    set({ isPolling: false });
    
    if (pollIntervalId) {
      clearInterval(pollIntervalId);
      pollIntervalId = null;
    }
  },

  markAsRead: () => {
    set({ unreadCount: 0 });
  },

  // === AÇÕES DE UI ===
  openChat: () => {
    set({ 
      isOpen: true, 
      isMinimized: false,
      unreadCount: 0,
    });
    
    // Iniciar polling quando abrir o chat
    if (!get().isPolling) {
      get().startPolling();
    }
  },

  closeChat: () => {
    set({ isOpen: false, isMinimized: false });
    
    // Parar polling quando fechar o chat
    get().stopPolling();
  },

  toggleChat: () => {
    const state = get();
    if (state.isOpen) {
      state.closeChat();
    } else {
      state.openChat();
    }
  },

  minimizeChat: () => {
    set({ isMinimized: true });
  },

  maximizeChat: () => {
    set({ isMinimized: false });
  },

  // === AÇÕES UTILITÁRIAS ===
  clearError: () => {
    set({ error: null, sendError: null });
  },

  clearMessages: () => {
    set({ messages: [], lastMessageTime: null, unreadCount: 0 });
  },
}));

// Hook personalizado para usar o chat
export const useChat = () => {
  const store = useChatStore();
  
  return {
    ...store,
    
    // Computed values
    hasMessages: store.messages.length > 0,
    hasUnread: store.unreadCount > 0,
    canSend: !store.isSending && !store.isLoading,
    
    // Utility functions
    formatTime: (timestamp: string) => {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit',
      });
    },
    
    formatDate: (timestamp: string) => {
      const date = new Date(timestamp);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (date.toDateString() === today.toDateString()) {
        return 'Hoje';
      } else if (date.toDateString() === yesterday.toDateString()) {
        return 'Ontem';
      } else {
        return date.toLocaleDateString('pt-BR');
      }
    },
    
    isMessageFromToday: (timestamp: string) => {
      const date = new Date(timestamp);
      const today = new Date();
      return date.toDateString() === today.toDateString();
    },
  };
};
