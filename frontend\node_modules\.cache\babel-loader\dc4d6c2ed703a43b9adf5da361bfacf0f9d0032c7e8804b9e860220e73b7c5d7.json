{"ast": null, "code": "import { createClient } from '@supabase/supabase-js';\n\n// Configurações do Supabase\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://your-project.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key';\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Tipos para o banco de dados\n\n// Funções utilitárias para o Supabase\nexport const supabaseUtils = {\n  // Verificar conexão\n  async testConnection() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('players').select('count').limit(1);\n      return {\n        success: !error,\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  // Gerar IP aleatório único\n  generateUniqueIP: async () => {\n    let attempts = 0;\n    const maxAttempts = 100;\n    while (attempts < maxAttempts) {\n      const ip = `${Math.floor(Math.random() * 255) + 1}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;\n      const {\n        data\n      } = await supabase.from('players').select('id').eq('ip', ip).limit(1);\n      if (!data || data.length === 0) {\n        return ip;\n      }\n      attempts++;\n    }\n\n    // Fallback se não conseguir gerar IP único\n    return `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;\n  },\n  // Verificar se nick já existe\n  async checkNickExists(nick) {\n    const {\n      data\n    } = await supabase.from('players').select('id').eq('nick', nick).limit(1);\n    return data ? data.length > 0 : false;\n  },\n  // Verificar se email já existe\n  async checkEmailExists(email) {\n    const {\n      data\n    } = await supabase.from('players').select('id').eq('email', email).limit(1);\n    return data ? data.length > 0 : false;\n  }\n};", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "process", "env", "REACT_APP_SUPABASE_URL", "supabaseAnonKey", "REACT_APP_SUPABASE_ANON_KEY", "supabase", "supabaseUtils", "testConnection", "data", "error", "from", "select", "limit", "success", "generateUniqueIP", "attempts", "maxAttempts", "ip", "Math", "floor", "random", "eq", "length", "checkNickExists", "nick", "checkEmailExists", "email"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\n// Configurações do Supabase\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://your-project.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key';\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Tipos para o banco de dados\nexport interface Player {\n  id: string;\n  nick: string;\n  email: string;\n  ip: string;\n  level: number;\n  xp: number;\n  cash: number;\n  created_at: string;\n  updated_at: string;\n  last_login: string;\n}\n\nexport interface PlayerApps {\n  id: string;\n  player_id: string;\n  antivirus: number;\n  bankguard: number;\n  bruteforce: number;\n  sdk: number;\n  firewall: number;\n  malwarekit: number;\n  proxyvpn: number;\n  updated_at: string;\n}\n\nexport interface GameNotification {\n  id: string;\n  player_id: string;\n  type: 'system' | 'upgrade' | 'level' | 'hack' | 'transfer';\n  title: string;\n  message: string;\n  read: boolean;\n  created_at: string;\n}\n\nexport interface HackTarget {\n  id: string;\n  player_id: string;\n  target_ip: string;\n  target_nick: string;\n  target_level: number;\n  target_cash: number;\n  firewall_level: number;\n  last_seen: string;\n}\n\nexport interface HackLog {\n  id: string;\n  attacker_id: string;\n  target_id: string;\n  success: boolean;\n  amount_stolen: number;\n  timestamp: string;\n}\n\nexport interface ChatMessage {\n  id: string;\n  player_id: string;\n  player_nick: string;\n  message: string;\n  created_at: string;\n}\n\n// Funções utilitárias para o Supabase\nexport const supabaseUtils = {\n  // Verificar conexão\n  async testConnection() {\n    try {\n      const { data, error } = await supabase.from('players').select('count').limit(1);\n      return { success: !error, data, error };\n    } catch (error) {\n      return { success: false, error };\n    }\n  },\n\n  // Gerar IP aleatório único\n  generateUniqueIP: async (): Promise<string> => {\n    let attempts = 0;\n    const maxAttempts = 100;\n\n    while (attempts < maxAttempts) {\n      const ip = `${Math.floor(Math.random() * 255) + 1}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;\n      \n      const { data } = await supabase\n        .from('players')\n        .select('id')\n        .eq('ip', ip)\n        .limit(1);\n\n      if (!data || data.length === 0) {\n        return ip;\n      }\n      \n      attempts++;\n    }\n\n    // Fallback se não conseguir gerar IP único\n    return `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;\n  },\n\n  // Verificar se nick já existe\n  async checkNickExists(nick: string): Promise<boolean> {\n    const { data } = await supabase\n      .from('players')\n      .select('id')\n      .eq('nick', nick)\n      .limit(1);\n\n    return data ? data.length > 0 : false;\n  },\n\n  // Verificar se email já existe\n  async checkEmailExists(email: string): Promise<boolean> {\n    const { data } = await supabase\n      .from('players')\n      .select('id')\n      .eq('email', email)\n      .limit(1);\n\n    return data ? data.length > 0 : false;\n  }\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,kCAAkC;AAC5F,MAAMC,eAAe,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B,IAAI,eAAe;AAElF,OAAO,MAAMC,QAAQ,GAAGP,YAAY,CAACC,WAAW,EAAEI,eAAe,CAAC;;AAElE;;AAiEA;AACA,OAAO,MAAMG,aAAa,GAAG;EAC3B;EACA,MAAMC,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM;QAAEC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,SAAS,CAAC,CAACC,MAAM,CAAC,OAAO,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;MAC/E,OAAO;QAAEC,OAAO,EAAE,CAACJ,KAAK;QAAED,IAAI;QAAEC;MAAM,CAAC;IACzC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEI,OAAO,EAAE,KAAK;QAAEJ;MAAM,CAAC;IAClC;EACF,CAAC;EAED;EACAK,gBAAgB,EAAE,MAAAA,CAAA,KAA6B;IAC7C,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMC,WAAW,GAAG,GAAG;IAEvB,OAAOD,QAAQ,GAAGC,WAAW,EAAE;MAC7B,MAAMC,EAAE,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,IAAIF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,IAAIF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE;MAE5J,MAAM;QAAEZ;MAAK,CAAC,GAAG,MAAMH,QAAQ,CAC5BK,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,IAAI,CAAC,CACZU,EAAE,CAAC,IAAI,EAAEJ,EAAE,CAAC,CACZL,KAAK,CAAC,CAAC,CAAC;MAEX,IAAI,CAACJ,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE;QAC9B,OAAOL,EAAE;MACX;MAEAF,QAAQ,EAAE;IACZ;;IAEA;IACA,OAAO,WAAWG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,IAAIF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE;EACxF,CAAC;EAED;EACA,MAAMG,eAAeA,CAACC,IAAY,EAAoB;IACpD,MAAM;MAAEhB;IAAK,CAAC,GAAG,MAAMH,QAAQ,CAC5BK,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,IAAI,CAAC,CACZU,EAAE,CAAC,MAAM,EAAEG,IAAI,CAAC,CAChBZ,KAAK,CAAC,CAAC,CAAC;IAEX,OAAOJ,IAAI,GAAGA,IAAI,CAACc,MAAM,GAAG,CAAC,GAAG,KAAK;EACvC,CAAC;EAED;EACA,MAAMG,gBAAgBA,CAACC,KAAa,EAAoB;IACtD,MAAM;MAAElB;IAAK,CAAC,GAAG,MAAMH,QAAQ,CAC5BK,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,IAAI,CAAC,CACZU,EAAE,CAAC,OAAO,EAAEK,KAAK,CAAC,CAClBd,KAAK,CAAC,CAAC,CAAC;IAEX,OAAOJ,IAAI,GAAGA,IAAI,CAACc,MAAM,GAAG,CAAC,GAAG,KAAK;EACvC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}