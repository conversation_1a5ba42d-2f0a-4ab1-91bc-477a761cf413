import {
    signInWithEmailAndPassword,
    createUserWithEmailAndPassword,
    signOut
} from 'https://www.gstatic.com/firebasejs/9.22.1/firebase-auth.js'; 

/**
 * Função auxiliar para fazer requisições à API Flask com o token de autenticação.
 * EXPORTA esta função para que outros módulos possam usá-la.
 */
export async function fetchAPI(endpoint, method = 'GET', body = null) {
    try {
        const user = window.auth.currentUser; // Obtém o usuário logado atualmente (acessando globalmente)
        if (!user) {
            throw new Error("Nenhum usuário logado. Token de autenticação não disponível.");
        }

        const idToken = await user.getIdToken(); 

        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${idToken}` 
        };

        const options = { method, headers }; 
        if (body) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch(endpoint, options);
        const data = await response.json();

        if (response.status === 401) {
            // console.error removido
            logoutUser(); 
        }

        return data;

    } catch (error) {
        // console.error removido
        throw error;
    }
}

/**
 * Registra um novo usuário NO Firebase Auth e depois cria o perfil no backend.
 * @param {string} email O email do usuário.
 * @param {string} password A senha do usuário.
 * @param {string} nick O nickname do usuário.
 * EXPORTA esta função para que outros módulos possam usá-la.
 */
export async function registerUser(email, password, nick) {
  try {
    // Primeiro, cria o usuário no Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(window.auth, email, password);
    const user = userCredential.user;
    
    // console.log removido
    
    // Depois, cria o perfil no backend usando o UID do Firebase
    const response = await fetch('/api/cadastro', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        uid: user.uid,
        email: email,
        nick: nick
      })
    });

    const result = await response.json();

    if (result.sucesso) {
        // console.log removido
        // O usuário já está logado automaticamente após createUserWithEmailAndPassword
        return result;
    } else {
        // Se falhou no backend, remove o usuário do Firebase Auth
        await user.delete();
        throw new Error(result.mensagem || "Falha ao criar perfil no backend.");
    }

  } catch (error) {
    // console.error removido
    if (error.code === 'auth/email-already-in-use') {
        throw new Error("Este email já está em uso.");
    } else if (error.code === 'auth/weak-password') {
        throw new Error("A senha deve ter pelo menos 6 caracteres.");
    } else if (error.code === 'auth/invalid-email') {
        throw new Error("Email inválido.");
    } else {
        throw new Error(error.message || "Erro desconhecido no cadastro.");
    }
  }
}

/**
 * Login usando a instância global 'window.auth' e a função modular.
 * @param {string} email O email do usuário.
 * @param {string} password A senha do usuário.
 */
export async function loginUser(email, password) {
  try {
    const userCredential = await signInWithEmailAndPassword(window.auth, email, password);
    // console.log removido
  } catch (error) {
    // console.error removido
    if (error.code === 'auth/user-not-found') {
        throw new Error("Usuário não encontrado.");
    } else if (error.code === 'auth/wrong-password') {
        throw new Error("Senha incorreta.");
    } else if (error.code === 'auth/invalid-email') {
        throw new Error("Email inválido.");
    } else if (error.code === 'auth/too-many-requests') {
        throw new Error("Muitas tentativas de login. Tente novamente mais tarde.");
    } else {
        throw new Error(error.message || "Erro desconhecido no login.");
    }
  }
}

/**
 * Logout também usando a instância global 'window.auth' e a função modular.
 */
export function logoutUser() {
  signOut(window.auth).then(() => { // <--- CORREÇÃO AQUI
    // console.log removido
  }).catch((error) => {
    // console.error removido
    alert("Erro ao deslogar: " + (error.message || "Ocorreu um erro desconhecido."));
  });
}