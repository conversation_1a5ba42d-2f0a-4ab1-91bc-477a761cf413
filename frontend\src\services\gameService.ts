import { supabase, supabaseUtils, Player, PlayerApps, GameNotification, HackTarget, ChatMessage } from '../lib/supabase';
import { GAME_CONFIG } from '../types/game';

export class GameService {
  // ==================== PLAYER OPERATIONS ====================
  
  async createPlayer(nick: string, email: string): Promise<{ success: boolean; player?: Player; error?: string }> {
    try {
      // Verificar se nick já existe
      const nickExists = await supabaseUtils.checkNickExists(nick);
      if (nickExists) {
        return { success: false, error: 'Nick já está em uso' };
      }

      // Verificar se email já existe
      const emailExists = await supabaseUtils.checkEmailExists(email);
      if (emailExists) {
        return { success: false, error: 'Email já está em uso' };
      }

      // Gerar IP único
      const ip = await supabaseUtils.generateUniqueIP();

      // Criar player
      const { data: playerData, error: playerError } = await supabase
        .from('players')
        .insert({
          nick,
          email,
          ip,
          level: 1,
          xp: 0,
          cash: GAME_CONFIG.INITIAL_CASH,
          last_login: new Date().toISOString()
        })
        .select()
        .single();

      if (playerError) {
        return { success: false, error: playerError.message };
      }

      // Criar apps iniciais
      const { error: appsError } = await supabase
        .from('player_apps')
        .insert({
          player_id: playerData.id,
          ...GAME_CONFIG.INITIAL_APPS
        });

      if (appsError) {
        return { success: false, error: appsError.message };
      }

      // Criar notificação de boas-vindas
      await this.createNotification(playerData.id, {
        type: 'system',
        title: 'Bem-vindo ao SHACK!',
        message: `Seu IP é ${ip}. Comece fazendo upgrades nos seus apps!`
      });

      return { success: true, player: playerData };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  async getPlayer(playerId: string): Promise<{ success: boolean; player?: Player; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('players')
        .select('*')
        .eq('id', playerId)
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, player: data };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  async updatePlayer(playerId: string, updates: Partial<Player>): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('players')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', playerId);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  // ==================== PLAYER APPS OPERATIONS ====================

  async getPlayerApps(playerId: string): Promise<{ success: boolean; apps?: PlayerApps; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('player_apps')
        .select('*')
        .eq('player_id', playerId)
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, apps: data };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  async updatePlayerApps(playerId: string, apps: Partial<PlayerApps>): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('player_apps')
        .update({ ...apps, updated_at: new Date().toISOString() })
        .eq('player_id', playerId);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  // ==================== NOTIFICATIONS ====================

  async createNotification(playerId: string, notification: Omit<GameNotification, 'id' | 'player_id' | 'created_at' | 'read'>): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('notifications')
        .insert({
          player_id: playerId,
          ...notification,
          read: false
        });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  async getNotifications(playerId: string): Promise<{ success: boolean; notifications?: GameNotification[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('player_id', playerId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, notifications: data || [] };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  async markNotificationRead(notificationId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  // ==================== CHAT ====================

  async sendChatMessage(playerId: string, playerNick: string, message: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('chat_messages')
        .insert({
          player_id: playerId,
          player_nick: playerNick,
          message
        });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  async getChatMessages(limit: number = 50): Promise<{ success: boolean; messages?: ChatMessage[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, messages: data?.reverse() || [] };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  // ==================== SCANNING & HACKING ====================

  async scanForTargets(playerId: string): Promise<{ success: boolean; targets?: HackTarget[]; error?: string }> {
    try {
      // Buscar outros jogadores online (exceto o próprio)
      const { data, error } = await supabase
        .from('players')
        .select('id, nick, ip, level, cash')
        .neq('id', playerId)
        .order('last_login', { ascending: false })
        .limit(10);

      if (error) {
        return { success: false, error: error.message };
      }

      // Converter para formato de targets
      const targets: HackTarget[] = data?.map(player => ({
        id: player.id,
        player_id: playerId,
        target_ip: player.ip,
        target_nick: player.nick,
        target_level: player.level,
        target_cash: player.cash,
        firewall_level: Math.floor(Math.random() * 5) + 1, // Simular nível de firewall
        last_seen: new Date().toISOString()
      })) || [];

      return { success: true, targets };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  // ==================== RANKING ====================

  async getTopPlayers(limit: number = 20): Promise<{ success: boolean; players?: Player[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('players')
        .select('nick, level, xp, cash')
        .order('level', { ascending: false })
        .order('xp', { ascending: false })
        .limit(limit);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, players: data as any || [] };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  // ==================== UTILITY ====================

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const result = await supabaseUtils.testConnection();
      return { success: result.success, error: result.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }
}

export const gameService = new GameService();
