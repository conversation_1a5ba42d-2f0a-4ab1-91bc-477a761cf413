import gameApi from './gameApi';

// Tipos para o sistema de jogo
export interface ScanTarget {
  uid: string;
  nick: string;
  ip: string;
  nivel: number;
  firewall: number;
  dinheiro: number;
  cpu?: number;
  ram?: number;
  antivirus?: number;
}

export interface ExploitedTarget extends ScanTarget {
  exploited: boolean;
  connectionId?: string;
  bruteforceStatus?: 'none' | 'running' | 'completed';
  bankAccess?: boolean;
}

export interface BruteforceStatus {
  isRunning: boolean;
  timeRemaining: number;
  totalTime: number;
  targetIp: string;
  progress: number;
}

export interface ConnectionStatus {
  id: string;
  targetIp: string;
  targetNick: string;
  isActive: boolean;
  bruteforceStatus?: BruteforceStatus;
  bankAccess: boolean;
}

class GameService {
  private currentPlayer: any = null;
  private exploitedTarget: ExploitedTarget | null = null;
  private activeConnections: Map<string, ConnectionStatus> = new Map();
  private bruteforceTimers: Map<string, NodeJS.Timeout> = new Map();

  // === SISTEMA DE CACHE ===
  private playerDataCache: any = null;
  private lastPlayerDataUpdate = 0;
  private readonly CACHE_DURATION = 15000; // 15 segundos

  // === CARREGAMENTO DE DADOS ===
  async loadPlayerData(forceRefresh = false): Promise<any> {
    const now = Date.now();
    
    // Usa cache se disponível e não forçou refresh
    if (!forceRefresh && this.playerDataCache && (now - this.lastPlayerDataUpdate) < this.CACHE_DURATION) {
      this.currentPlayer = this.playerDataCache;
      return this.playerDataCache;
    }

    try {
      const response = await gameApi.getPlayerData();
      if (response.sucesso) {
        this.currentPlayer = response.jogador;
        this.playerDataCache = response.jogador;
        this.lastPlayerDataUpdate = now;
        return response.jogador;
      } else {
        throw new Error(response.mensagem || 'Erro ao carregar dados');
      }
    } catch (error) {
      console.error('Erro ao carregar dados do jogador:', error);
      
      // Se temos cache e erro não é crítico, usa cache
      if (this.playerDataCache) {
        this.currentPlayer = this.playerDataCache;
        return this.playerDataCache;
      }
      
      throw error;
    }
  }

  getCurrentPlayer() {
    return this.currentPlayer;
  }

  // === SISTEMA DE SCAN ===
  async performQuickScan(): Promise<ScanTarget[]> {
    try {
      const response = await gameApi.scanNetwork();
      if (response.sucesso) {
        return response.alvos || [];
      } else {
        throw new Error(response.mensagem || 'Erro no scan');
      }
    } catch (error) {
      console.error('Erro no scan rápido:', error);
      throw error;
    }
  }

  async performAdvancedScan(targetIp: string): Promise<ScanTarget[]> {
    try {
      const response = await gameApi.scanSpecificTarget(targetIp);
      if (response.sucesso) {
        return response.alvos ? [response.alvos] : [];
      } else {
        throw new Error(response.mensagem || 'Alvo não encontrado');
      }
    } catch (error) {
      console.error('Erro no scan avançado:', error);
      throw error;
    }
  }

  // === SISTEMA DE EXPLOIT ===
  async performExploit(target: ScanTarget): Promise<ExploitedTarget> {
    try {
      // Verificar se o jogador tem CPU suficiente
      const playerCPU = this.currentPlayer?.cpu || 1;
      const targetFirewall = target.firewall || 1;
      
      if (playerCPU <= targetFirewall) {
        throw new Error(`CPU insuficiente! Seu CPU (${playerCPU}) não pode quebrar Firewall (${targetFirewall})`);
      }

      const response = await gameApi.exploitTarget(target.ip);
      
      if (response.sucesso && response.alvo_explorado) {
        this.exploitedTarget = {
          ...response.alvo_explorado,
          exploited: true,
          bruteforceStatus: 'none',
          bankAccess: false
        };
        
        // Adicionar à lista de conexões ativas
        this.activeConnections.set(target.ip, {
          id: response.conexao_id || target.ip,
          targetIp: target.ip,
          targetNick: target.nick,
          isActive: true,
          bankAccess: false
        });
        
        return this.exploitedTarget;
      } else {
        throw new Error(response.mensagem || 'Exploit falhou');
      }
    } catch (error) {
      console.error('Erro no exploit:', error);
      throw error;
    }
  }

  getExploitedTarget(): ExploitedTarget | null {
    return this.exploitedTarget;
  }

  // === SISTEMA DE TERMINAL E BRUTEFORCE ===
  async getActiveConnections(): Promise<ConnectionStatus[]> {
    try {
      const response = await gameApi.getActiveConnections();
      if (response.sucesso) {
        // Atualizar mapa local de conexões
        this.activeConnections.clear();
        response.conexoes?.forEach((conn: any) => {
          this.activeConnections.set(conn.alvo_ip, {
            id: conn.id,
            targetIp: conn.alvo_ip,
            targetNick: conn.alvo_nick,
            isActive: true,
            bankAccess: conn.banco_liberado || false
          });
        });
        
        return Array.from(this.activeConnections.values());
      }
      return [];
    } catch (error) {
      console.error('Erro ao carregar conexões:', error);
      return [];
    }
  }

  async startBruteforce(connectionId: string, targetIp: string): Promise<BruteforceStatus> {
    try {
      const response = await gameApi.startBruteforce(connectionId);
      
      if (response.sucesso) {
        const bruteforceStatus: BruteforceStatus = {
          isRunning: true,
          timeRemaining: response.tempo_total || 60,
          totalTime: response.tempo_total || 60,
          targetIp,
          progress: 0
        };
        
        // Atualizar status da conexão
        const connection = this.activeConnections.get(targetIp);
        if (connection) {
          connection.bruteforceStatus = bruteforceStatus;
        }
        
        // Iniciar timer local
        this.startBruteforceTimer(targetIp, bruteforceStatus.totalTime);
        
        return bruteforceStatus;
      } else {
        throw new Error(response.mensagem || 'Erro ao iniciar bruteforce');
      }
    } catch (error) {
      console.error('Erro ao iniciar bruteforce:', error);
      throw error;
    }
  }

  private startBruteforceTimer(targetIp: string, totalTime: number) {
    // Limpar timer existente
    if (this.bruteforceTimers.has(targetIp)) {
      clearInterval(this.bruteforceTimers.get(targetIp)!);
    }
    
    let timeRemaining = totalTime;
    
    const timer = setInterval(async () => {
      timeRemaining--;
      
      const connection = this.activeConnections.get(targetIp);
      if (connection?.bruteforceStatus) {
        connection.bruteforceStatus.timeRemaining = timeRemaining;
        connection.bruteforceStatus.progress = ((totalTime - timeRemaining) / totalTime) * 100;
      }
      
      if (timeRemaining <= 0) {
        clearInterval(timer);
        this.bruteforceTimers.delete(targetIp);
        
        // Verificar conclusão do bruteforce
        await this.checkBruteforceCompletion(targetIp);
      }
    }, 1000);
    
    this.bruteforceTimers.set(targetIp, timer);
  }

  private async checkBruteforceCompletion(targetIp: string) {
    try {
      const response = await gameApi.checkBruteforceStatus(targetIp);
      
      if (response.sucesso && response.finalizado) {
        const connection = this.activeConnections.get(targetIp);
        if (connection) {
          connection.bruteforceStatus = undefined;
          connection.bankAccess = true;
        }
        
        // Atualizar alvo exploitado se for o mesmo
        if (this.exploitedTarget?.ip === targetIp) {
          this.exploitedTarget.bruteforceStatus = 'completed';
          this.exploitedTarget.bankAccess = true;
        }
      }
    } catch (error) {
      console.error('Erro ao verificar conclusão do bruteforce:', error);
    }
  }

  async closeConnection(connectionId: string, targetIp: string): Promise<void> {
    try {
      const response = await gameApi.closeConnection(connectionId);
      
      if (response.sucesso) {
        this.activeConnections.delete(targetIp);
        
        // Limpar timer se existir
        if (this.bruteforceTimers.has(targetIp)) {
          clearInterval(this.bruteforceTimers.get(targetIp)!);
          this.bruteforceTimers.delete(targetIp);
        }
        
        // Limpar alvo exploitado se for o mesmo
        if (this.exploitedTarget?.ip === targetIp) {
          this.exploitedTarget = null;
        }
      } else {
        throw new Error(response.mensagem || 'Erro ao fechar conexão');
      }
    } catch (error) {
      console.error('Erro ao fechar conexão:', error);
      throw error;
    }
  }

  // === SISTEMA DE TRANSFERÊNCIA BANCÁRIA ===
  async performBankTransfer(targetIp: string, percentage: number): Promise<any> {
    try {
      const connection = this.activeConnections.get(targetIp);
      if (!connection?.bankAccess) {
        throw new Error('Acesso ao banco não liberado. Execute bruteforce primeiro.');
      }

      const response = await gameApi.transferMoney(targetIp, percentage);
      
      if (response.sucesso) {
        // Atualizar dados do jogador após transferência
        await this.loadPlayerData(true);
        return response;
      } else {
        throw new Error(response.mensagem || 'Erro na transferência');
      }
    } catch (error) {
      console.error('Erro na transferência bancária:', error);
      throw error;
    }
  }

  // === LIMPEZA ===
  cleanup() {
    // Limpar todos os timers
    this.bruteforceTimers.forEach(timer => clearInterval(timer));
    this.bruteforceTimers.clear();
    
    // Limpar dados
    this.activeConnections.clear();
    this.exploitedTarget = null;
  }
}

export default new GameService();
