{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\GameMainPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport ModernCard from '../components/ui/ModernCard';\nimport GlowingIcon from '../components/ui/GlowingIcon';\nimport AnimatedBackground from '../components/ui/AnimatedBackground';\n\n// Layout principal do jogo - Estilo celular antigo\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameMainPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    loadPlayerData,\n    hasPlayerData\n  } = usePlayer();\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('GameMainPage - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 1,\n    dinheiro: 0,\n    ip: '**************'\n  };\n  const handleIconClick = route => {\n    navigate(route);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900/20 to-purple-900/20 text-white flex flex-col relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(AnimatedBackground, {\n      variant: \"particles\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n      variant: \"glass\",\n      className: \"m-4 p-4 flex-shrink-0\",\n      glow: true,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"green\",\n            size: \"sm\",\n            glow: true,\n            pulse: true,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-bold\",\n              children: \"S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n              children: (user === null || user === void 0 ? void 0 : user.nick) || 'Ivo77'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400\",\n              children: [\"IP: \", playerData.ip]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-bold text-blue-400\",\n            children: [\"N\\xEDvel \", playerData.nivel]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-green-400 font-semibold\",\n            children: [\"$\", playerData.dinheiro || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-6 overflow-y-auto relative z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-3 gap-6 max-w-2xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/scanner'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"blue\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/apps'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"purple\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\uD83D\\uDCBB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Apps\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/upgrades'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"yellow\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Upgrades\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/chat'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"green\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\uD83D\\uDCE7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/transfer'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"cyan\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Transfer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/shop'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"pink\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Loja\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/logs'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"blue\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/ranking'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"yellow\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\uD83C\\uDFC6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Ranking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/bank'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"green\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\uD83C\\uDFE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Banco\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/mining'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"purple\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\u26CF\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Minera\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"neon\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/terminal'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"cyan\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\uD83D\\uDCBB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-cyan-200\",\n            children: \"Terminal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center\",\n          onClick: () => handleIconClick('/game/supporters'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"red\",\n            size: \"md\",\n            className: \"mx-auto mb-2\",\n            children: \"\\u2764\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Supporters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-around items-center max-w-md mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83C\\uDFE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/chat'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/scanner'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/transfer'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Transfer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n          onClick: () => handleIconClick('/game/config'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"Config\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(GameMainPage, \"Gzh4cCW8/G5MAMmfC9xUDiJ/DA0=\", false, function () {\n  return [useNavigate, useSimpleAuth, usePlayer];\n});\n_c = GameMainPage;\nexport default GameMainPage;\nvar _c;\n$RefreshReg$(_c, \"GameMainPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useSimpleAuth", "usePlayer", "ModernCard", "GlowingIcon", "AnimatedBackground", "jsxDEV", "_jsxDEV", "GameMainPage", "_s", "navigate", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "<PERSON><PERSON><PERSON>", "ip", "handleIconClick", "route", "className", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "glow", "color", "size", "pulse", "nick", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/GameMainPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport ModernCard from '../components/ui/ModernCard';\nimport GlowingIcon from '../components/ui/GlowingIcon';\nimport AnimatedBackground from '../components/ui/AnimatedBackground';\n\n// Layout principal do jogo - Estilo celular antigo\nconst GameMainPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('GameMainPage - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 1,\n    dinheiro: 0,\n    ip: '**************'\n  };\n\n  const handleIconClick = (route: string) => {\n    navigate(route);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900/20 to-purple-900/20 text-white flex flex-col relative overflow-hidden\">\n      {/* Background animado */}\n      <AnimatedBackground variant=\"particles\" />\n\n      {/* Header moderno */}\n      <ModernCard variant=\"glass\" className=\"m-4 p-4 flex-shrink-0\" glow>\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center space-x-3\">\n            <GlowingIcon color=\"green\" size=\"sm\" glow pulse>\n              <span className=\"text-xs font-bold\">S</span>\n            </GlowingIcon>\n            <div>\n              <div className=\"text-lg font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\">\n                {user?.nick || 'Ivo77'}\n              </div>\n              <div className=\"text-xs text-gray-400\">IP: {playerData.ip}</div>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-lg font-bold text-blue-400\">Nível {playerData.nivel}</div>\n            <div className=\"text-sm text-green-400 font-semibold\">\n              ${playerData.dinheiro || 0}\n            </div>\n          </div>\n        </div>\n      </ModernCard>\n\n      {/* Grid principal de ícones */}\n      <div className=\"flex-1 p-6 overflow-y-auto relative z-10\">\n        <div className=\"grid grid-cols-3 gap-6 max-w-2xl mx-auto\">\n          {/* Primeira linha */}\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/scanner')}\n            glow\n          >\n            <GlowingIcon color=\"blue\" size=\"md\" className=\"mx-auto mb-2\">\n              🔍\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Scan</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/apps')}\n            glow\n          >\n            <GlowingIcon color=\"purple\" size=\"md\" className=\"mx-auto mb-2\">\n              💻\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Apps</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/upgrades')}\n            glow\n          >\n            <GlowingIcon color=\"yellow\" size=\"md\" className=\"mx-auto mb-2\">\n              ⚙️\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Upgrades</span>\n          </ModernCard>\n\n          {/* Segunda linha */}\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/chat')}\n            glow\n          >\n            <GlowingIcon color=\"green\" size=\"md\" className=\"mx-auto mb-2\">\n              📧\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Chat</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/transfer')}\n            glow\n          >\n            <GlowingIcon color=\"cyan\" size=\"md\" className=\"mx-auto mb-2\">\n              💰\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Transfer</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/shop')}\n            glow\n          >\n            <GlowingIcon color=\"pink\" size=\"md\" className=\"mx-auto mb-2\">\n              🛒\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Loja</span>\n          </ModernCard>\n\n          {/* Terceira linha */}\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/logs')}\n            glow\n          >\n            <GlowingIcon color=\"blue\" size=\"md\" className=\"mx-auto mb-2\">\n              📊\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Logs</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/ranking')}\n            glow\n          >\n            <GlowingIcon color=\"yellow\" size=\"md\" className=\"mx-auto mb-2\">\n              🏆\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Ranking</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/bank')}\n            glow\n          >\n            <GlowingIcon color=\"green\" size=\"md\" className=\"mx-auto mb-2\">\n              🏦\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Banco</span>\n          </ModernCard>\n\n          {/* Quarta linha */}\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/mining')}\n            glow\n          >\n            <GlowingIcon color=\"purple\" size=\"md\" className=\"mx-auto mb-2\">\n              ⛏️\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Mineração</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"neon\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/terminal')}\n            glow\n          >\n            <GlowingIcon color=\"cyan\" size=\"md\" className=\"mx-auto mb-2\">\n              💻\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-cyan-200\">Terminal</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center\"\n            onClick={() => handleIconClick('/game/supporters')}\n            glow\n          >\n            <GlowingIcon color=\"red\" size=\"md\" className=\"mx-auto mb-2\">\n              ❤️\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Supporters</span>\n          </ModernCard>\n        </div>\n      </div>\n\n      {/* Footer com botões de navegação */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-around items-center max-w-md mx-auto\">\n          <button \n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">🏠</span>\n            </div>\n            <span className=\"text-xs\">Home</span>\n          </button>\n          \n          <button \n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/chat')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">💬</span>\n            </div>\n            <span className=\"text-xs\">Chat</span>\n          </button>\n          \n          <button \n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/scanner')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">🔍</span>\n            </div>\n            <span className=\"text-xs\">Scan</span>\n          </button>\n          \n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/transfer')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">💰</span>\n            </div>\n            <span className=\"text-xs\">Transfer</span>\n          </button>\n          \n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\"\n            onClick={() => handleIconClick('/game/config')}\n          >\n            <div className=\"w-6 h-6 flex items-center justify-center\">\n              <span className=\"text-lg\">⚙️</span>\n            </div>\n            <span className=\"text-xs\">Config</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GameMainPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,kBAAkB,MAAM,qCAAqC;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW;EAAK,CAAC,GAAGV,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEW,aAAa;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGb,SAAS,CAAC,CAAC;EAErFH,SAAS,CAAC,MAAM;IACd,IAAI,CAACgB,aAAa,IAAI,CAACF,eAAe,EAAE;MACtCG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DH,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACC,aAAa,EAAEF,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAEpD;EACA,MAAMI,UAAU,GAAGN,aAAa,IAAI;IAClCO,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,eAAe,GAAIC,KAAa,IAAK;IACzCd,QAAQ,CAACc,KAAK,CAAC;EACjB,CAAC;EAED,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,iIAAiI;IAAAC,QAAA,gBAE9InB,OAAA,CAACF,kBAAkB;MAACsB,OAAO,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG1CxB,OAAA,CAACJ,UAAU;MAACwB,OAAO,EAAC,OAAO;MAACF,SAAS,EAAC,uBAAuB;MAACO,IAAI;MAAAN,QAAA,eAChEnB,OAAA;QAAKkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnB,OAAA;UAAKkB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,OAAO;YAACC,IAAI,EAAC,IAAI;YAACF,IAAI;YAACG,KAAK;YAAAT,QAAA,eAC7CnB,OAAA;cAAMkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACdxB,OAAA;YAAAmB,QAAA,gBACEnB,OAAA;cAAKkB,SAAS,EAAC,8FAA8F;cAAAC,QAAA,EAC1G,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,IAAI,KAAI;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNxB,OAAA;cAAKkB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,MAAI,EAACR,UAAU,CAACI,EAAE;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxB,OAAA;UAAKkB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBnB,OAAA;YAAKkB,SAAS,EAAC,iCAAiC;YAAAC,QAAA,GAAC,WAAM,EAACR,UAAU,CAACE,KAAK;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/ExB,OAAA;YAAKkB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,GAAC,GACnD,EAACR,UAAU,CAACG,QAAQ,IAAI,CAAC;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbxB,OAAA;MAAKkB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,eACvDnB,OAAA;QAAKkB,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBAEvDnB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,eAAe,CAAE;UAChDS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE7D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAEbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,YAAY,CAAE;UAC7CS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,QAAQ;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE/D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAEbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,gBAAgB,CAAE;UACjDS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,QAAQ;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE/D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAGbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,YAAY,CAAE;UAC7CS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,OAAO;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE9D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAEbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,gBAAgB,CAAE;UACjDS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE7D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAEbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,YAAY,CAAE;UAC7CS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE7D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAGbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,YAAY,CAAE;UAC7CS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE7D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAEbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,eAAe,CAAE;UAChDS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,QAAQ;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE/D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eAEbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,YAAY,CAAE;UAC7CS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,OAAO;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE9D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eAGbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,cAAc,CAAE;UAC/CS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,QAAQ;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE/D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAEbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,MAAM;UACdF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,gBAAgB,CAAE;UACjDS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE7D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAEbxB,OAAA,CAACJ,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfF,SAAS,EAAC,iBAAiB;UAC3BY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,kBAAkB,CAAE;UACnDS,IAAI;UAAAN,QAAA,gBAEJnB,OAAA,CAACH,WAAW;YAAC6B,KAAK,EAAC,KAAK;YAACC,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdxB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxB,OAAA;MAAKkB,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEnB,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEnB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,OAAO,CAAE;UAAAG,QAAA,gBAExCnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNxB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAETxB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNxB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAETxB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,eAAe,CAAE;UAAAG,QAAA,gBAEhDnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNxB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAETxB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNxB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETxB,OAAA;UACEkB,SAAS,EAAC,uFAAuF;UACjGY,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,cAAc,CAAE;UAAAG,QAAA,gBAE/CnB,OAAA;YAAKkB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNxB,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CArQID,YAAsB;EAAA,QACTR,WAAW,EACXC,aAAa,EAC4CC,SAAS;AAAA;AAAAoC,EAAA,GAH/E9B,YAAsB;AAuQ5B,eAAeA,YAAY;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}