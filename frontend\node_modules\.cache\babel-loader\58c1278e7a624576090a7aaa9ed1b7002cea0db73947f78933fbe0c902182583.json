{"ast": null, "code": "var _s = $RefreshSig$();\nimport { create } from 'zustand';\nimport mockApiService from '../services/mockApi';\nlet pollIntervalId = null;\nexport const useChatStore = create((set, get) => ({\n  // Estado inicial\n  messages: [],\n  isLoading: false,\n  error: null,\n  isSending: false,\n  sendError: null,\n  isPolling: false,\n  pollInterval: 2000,\n  // 2 segundos\n  lastMessageTime: null,\n  unreadCount: 0,\n  isOpen: false,\n  isMinimized: false,\n  // === AÇÕES DE MENSAGENS ===\n  loadMessages: async () => {\n    const state = get();\n    if (state.isLoading) return; // Evitar múltiplas requisições simultâneas\n\n    console.log('ChatStore - Carregando mensagens...');\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const response = await mockApiService.getChatMessages();\n      if (response.sucesso && response.dados) {\n        const newMessages = response.dados;\n        const currentMessages = state.messages;\n\n        // Verificar se há mensagens novas\n        const lastCurrentMessageTime = currentMessages.length > 0 ? currentMessages[currentMessages.length - 1].timestamp : null;\n        const newUnreadCount = lastCurrentMessageTime ? newMessages.filter(msg => msg.timestamp > lastCurrentMessageTime).length : newMessages.length;\n        set({\n          messages: newMessages,\n          isLoading: false,\n          error: null,\n          lastMessageTime: newMessages.length > 0 ? newMessages[newMessages.length - 1].timestamp : state.lastMessageTime,\n          unreadCount: state.isOpen ? 0 : state.unreadCount + newUnreadCount\n        });\n      } else {\n        throw new Error(response.mensagem || 'Erro ao carregar mensagens');\n      }\n    } catch (error) {\n      set({\n        isLoading: false,\n        error: error.message || 'Erro ao carregar mensagens'\n      });\n    }\n  },\n  sendMessage: async message => {\n    if (!message.trim()) return;\n    console.log('ChatStore - Enviando mensagem:', message);\n    set({\n      isSending: true,\n      sendError: null\n    });\n    try {\n      const response = await mockApiService.sendChatMessage({\n        mensagem: message.trim(),\n        tipo: 'global'\n      });\n      if (response.sucesso) {\n        set({\n          isSending: false,\n          sendError: null\n        });\n\n        // Recarregar mensagens após enviar\n        setTimeout(() => {\n          get().loadMessages();\n        }, 500);\n      } else {\n        throw new Error(response.mensagem || 'Erro ao enviar mensagem');\n      }\n    } catch (error) {\n      set({\n        isSending: false,\n        sendError: error.message || 'Erro ao enviar mensagem'\n      });\n      throw error;\n    }\n  },\n  // === AÇÕES DE POLLING ===\n  startPolling: () => {\n    const state = get();\n    if (state.isPolling) return;\n    set({\n      isPolling: true\n    });\n\n    // Carregar mensagens imediatamente\n    get().loadMessages();\n\n    // Configurar polling\n    pollIntervalId = setInterval(() => {\n      get().loadMessages();\n    }, state.pollInterval);\n  },\n  stopPolling: () => {\n    set({\n      isPolling: false\n    });\n    if (pollIntervalId) {\n      clearInterval(pollIntervalId);\n      pollIntervalId = null;\n    }\n  },\n  markAsRead: () => {\n    set({\n      unreadCount: 0\n    });\n  },\n  // === AÇÕES DE UI ===\n  openChat: () => {\n    set({\n      isOpen: true,\n      isMinimized: false,\n      unreadCount: 0\n    });\n\n    // Iniciar polling quando abrir o chat\n    if (!get().isPolling) {\n      get().startPolling();\n    }\n  },\n  closeChat: () => {\n    set({\n      isOpen: false,\n      isMinimized: false\n    });\n\n    // Parar polling quando fechar o chat\n    get().stopPolling();\n  },\n  toggleChat: () => {\n    const state = get();\n    if (state.isOpen) {\n      state.closeChat();\n    } else {\n      state.openChat();\n    }\n  },\n  minimizeChat: () => {\n    set({\n      isMinimized: true\n    });\n  },\n  maximizeChat: () => {\n    set({\n      isMinimized: false\n    });\n  },\n  // === AÇÕES UTILITÁRIAS ===\n  clearError: () => {\n    set({\n      error: null,\n      sendError: null\n    });\n  },\n  clearMessages: () => {\n    set({\n      messages: [],\n      lastMessageTime: null,\n      unreadCount: 0\n    });\n  }\n}));\n\n// Hook personalizado para usar o chat\nexport const useChat = () => {\n  _s();\n  const store = useChatStore();\n  return {\n    ...store,\n    // Computed values\n    hasMessages: store.messages.length > 0,\n    hasUnread: store.unreadCount > 0,\n    canSend: !store.isSending && !store.isLoading,\n    // Utility functions\n    formatTime: timestamp => {\n      const date = new Date(timestamp);\n      return date.toLocaleTimeString('pt-BR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n    formatDate: timestamp => {\n      const date = new Date(timestamp);\n      const today = new Date();\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === today.toDateString()) {\n        return 'Hoje';\n      } else if (date.toDateString() === yesterday.toDateString()) {\n        return 'Ontem';\n      } else {\n        return date.toLocaleDateString('pt-BR');\n      }\n    },\n    isMessageFromToday: timestamp => {\n      const date = new Date(timestamp);\n      const today = new Date();\n      return date.toDateString() === today.toDateString();\n    }\n  };\n};\n_s(useChat, \"eBttsSSr/1EDYbiaHkm0/Ogdl38=\", false, function () {\n  return [useChatStore];\n});", "map": {"version": 3, "names": ["create", "mockApiService", "pollIntervalId", "useChatStore", "set", "get", "messages", "isLoading", "error", "isSending", "sendError", "isPolling", "pollInterval", "lastMessageTime", "unreadCount", "isOpen", "isMinimized", "loadMessages", "state", "console", "log", "response", "getChatMessages", "sucesso", "dados", "newMessages", "currentMessages", "lastCurrentMessageTime", "length", "timestamp", "newUnreadCount", "filter", "msg", "Error", "mensagem", "message", "sendMessage", "trim", "sendChatMessage", "tipo", "setTimeout", "startPolling", "setInterval", "stopPolling", "clearInterval", "mark<PERSON><PERSON><PERSON>", "openChat", "closeChat", "toggleChat", "minimizeChat", "maximizeChat", "clearError", "clearMessages", "useChat", "_s", "store", "hasMessages", "hasUnread", "canSend", "formatTime", "date", "Date", "toLocaleTimeString", "hour", "minute", "formatDate", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "isMessageFromToday"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/chatStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport mockApiService from '../services/mockApi';\n\nexport interface ChatMessage {\n  id: number;\n  usuario: string;\n  mensagem: string;\n  timestamp: string;\n  tipo: 'global' | 'grupo' | 'privado';\n}\n\ninterface ChatState {\n  // Estado das mensagens\n  messages: ChatMessage[];\n  isLoading: boolean;\n  error: string | null;\n  \n  // Estado de envio\n  isSending: boolean;\n  sendError: string | null;\n  \n  // Configurações\n  isPolling: boolean;\n  pollInterval: number;\n  lastMessageTime: string | null;\n  unreadCount: number;\n  \n  // Estado da UI\n  isOpen: boolean;\n  isMinimized: boolean;\n  \n  // Ações\n  loadMessages: () => Promise<void>;\n  sendMessage: (message: string) => Promise<void>;\n  startPolling: () => void;\n  stopPolling: () => void;\n  markAsRead: () => void;\n  \n  // UI Actions\n  openChat: () => void;\n  closeChat: () => void;\n  toggleChat: () => void;\n  minimizeChat: () => void;\n  maximizeChat: () => void;\n  \n  // Utility actions\n  clearError: () => void;\n  clearMessages: () => void;\n}\n\nlet pollIntervalId: NodeJS.Timeout | null = null;\n\nexport const useChatStore = create<ChatState>((set, get) => ({\n  // Estado inicial\n  messages: [],\n  isLoading: false,\n  error: null,\n  \n  isSending: false,\n  sendError: null,\n  \n  isPolling: false,\n  pollInterval: 2000, // 2 segundos\n  lastMessageTime: null,\n  unreadCount: 0,\n  \n  isOpen: false,\n  isMinimized: false,\n\n  // === AÇÕES DE MENSAGENS ===\n  loadMessages: async () => {\n    const state = get();\n    if (state.isLoading) return; // Evitar múltiplas requisições simultâneas\n\n    console.log('ChatStore - Carregando mensagens...');\n    set({ isLoading: true, error: null });\n\n    try {\n      const response = await mockApiService.getChatMessages();\n      \n      if (response.sucesso && response.dados) {\n        const newMessages = response.dados;\n        const currentMessages = state.messages;\n        \n        // Verificar se há mensagens novas\n        const lastCurrentMessageTime = currentMessages.length > 0 \n          ? currentMessages[currentMessages.length - 1].timestamp \n          : null;\n        \n        const newUnreadCount = lastCurrentMessageTime\n          ? newMessages.filter((msg: ChatMessage) => msg.timestamp > lastCurrentMessageTime).length\n          : newMessages.length;\n        \n        set({\n          messages: newMessages,\n          isLoading: false,\n          error: null,\n          lastMessageTime: newMessages.length > 0 \n            ? newMessages[newMessages.length - 1].timestamp \n            : state.lastMessageTime,\n          unreadCount: state.isOpen ? 0 : state.unreadCount + newUnreadCount,\n        });\n      } else {\n        throw new Error(response.mensagem || 'Erro ao carregar mensagens');\n      }\n    } catch (error: any) {\n      set({\n        isLoading: false,\n        error: error.message || 'Erro ao carregar mensagens',\n      });\n    }\n  },\n\n  sendMessage: async (message: string) => {\n    if (!message.trim()) return;\n\n    console.log('ChatStore - Enviando mensagem:', message);\n    set({ isSending: true, sendError: null });\n\n    try {\n      const response = await mockApiService.sendChatMessage({\n        mensagem: message.trim(),\n        tipo: 'global'\n      });\n      \n      if (response.sucesso) {\n        set({\n          isSending: false,\n          sendError: null,\n        });\n        \n        // Recarregar mensagens após enviar\n        setTimeout(() => {\n          get().loadMessages();\n        }, 500);\n      } else {\n        throw new Error(response.mensagem || 'Erro ao enviar mensagem');\n      }\n    } catch (error: any) {\n      set({\n        isSending: false,\n        sendError: error.message || 'Erro ao enviar mensagem',\n      });\n      throw error;\n    }\n  },\n\n  // === AÇÕES DE POLLING ===\n  startPolling: () => {\n    const state = get();\n    if (state.isPolling) return;\n    \n    set({ isPolling: true });\n    \n    // Carregar mensagens imediatamente\n    get().loadMessages();\n    \n    // Configurar polling\n    pollIntervalId = setInterval(() => {\n      get().loadMessages();\n    }, state.pollInterval);\n  },\n\n  stopPolling: () => {\n    set({ isPolling: false });\n    \n    if (pollIntervalId) {\n      clearInterval(pollIntervalId);\n      pollIntervalId = null;\n    }\n  },\n\n  markAsRead: () => {\n    set({ unreadCount: 0 });\n  },\n\n  // === AÇÕES DE UI ===\n  openChat: () => {\n    set({ \n      isOpen: true, \n      isMinimized: false,\n      unreadCount: 0,\n    });\n    \n    // Iniciar polling quando abrir o chat\n    if (!get().isPolling) {\n      get().startPolling();\n    }\n  },\n\n  closeChat: () => {\n    set({ isOpen: false, isMinimized: false });\n    \n    // Parar polling quando fechar o chat\n    get().stopPolling();\n  },\n\n  toggleChat: () => {\n    const state = get();\n    if (state.isOpen) {\n      state.closeChat();\n    } else {\n      state.openChat();\n    }\n  },\n\n  minimizeChat: () => {\n    set({ isMinimized: true });\n  },\n\n  maximizeChat: () => {\n    set({ isMinimized: false });\n  },\n\n  // === AÇÕES UTILITÁRIAS ===\n  clearError: () => {\n    set({ error: null, sendError: null });\n  },\n\n  clearMessages: () => {\n    set({ messages: [], lastMessageTime: null, unreadCount: 0 });\n  },\n}));\n\n// Hook personalizado para usar o chat\nexport const useChat = () => {\n  const store = useChatStore();\n  \n  return {\n    ...store,\n    \n    // Computed values\n    hasMessages: store.messages.length > 0,\n    hasUnread: store.unreadCount > 0,\n    canSend: !store.isSending && !store.isLoading,\n    \n    // Utility functions\n    formatTime: (timestamp: string) => {\n      const date = new Date(timestamp);\n      return date.toLocaleTimeString('pt-BR', {\n        hour: '2-digit',\n        minute: '2-digit',\n      });\n    },\n    \n    formatDate: (timestamp: string) => {\n      const date = new Date(timestamp);\n      const today = new Date();\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      \n      if (date.toDateString() === today.toDateString()) {\n        return 'Hoje';\n      } else if (date.toDateString() === yesterday.toDateString()) {\n        return 'Ontem';\n      } else {\n        return date.toLocaleDateString('pt-BR');\n      }\n    },\n    \n    isMessageFromToday: (timestamp: string) => {\n      const date = new Date(timestamp);\n      const today = new Date();\n      return date.toDateString() === today.toDateString();\n    },\n  };\n};\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,OAAOC,cAAc,MAAM,qBAAqB;AAiDhD,IAAIC,cAAqC,GAAG,IAAI;AAEhD,OAAO,MAAMC,YAAY,GAAGH,MAAM,CAAY,CAACI,GAAG,EAAEC,GAAG,MAAM;EAC3D;EACAC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEXC,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE,IAAI;EAEfC,SAAS,EAAE,KAAK;EAChBC,YAAY,EAAE,IAAI;EAAE;EACpBC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,CAAC;EAEdC,MAAM,EAAE,KAAK;EACbC,WAAW,EAAE,KAAK;EAElB;EACAC,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,MAAMC,KAAK,GAAGb,GAAG,CAAC,CAAC;IACnB,IAAIa,KAAK,CAACX,SAAS,EAAE,OAAO,CAAC;;IAE7BY,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClDhB,GAAG,CAAC;MAAEG,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMpB,cAAc,CAACqB,eAAe,CAAC,CAAC;MAEvD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,KAAK,EAAE;QACtC,MAAMC,WAAW,GAAGJ,QAAQ,CAACG,KAAK;QAClC,MAAME,eAAe,GAAGR,KAAK,CAACZ,QAAQ;;QAEtC;QACA,MAAMqB,sBAAsB,GAAGD,eAAe,CAACE,MAAM,GAAG,CAAC,GACrDF,eAAe,CAACA,eAAe,CAACE,MAAM,GAAG,CAAC,CAAC,CAACC,SAAS,GACrD,IAAI;QAER,MAAMC,cAAc,GAAGH,sBAAsB,GACzCF,WAAW,CAACM,MAAM,CAAEC,GAAgB,IAAKA,GAAG,CAACH,SAAS,GAAGF,sBAAsB,CAAC,CAACC,MAAM,GACvFH,WAAW,CAACG,MAAM;QAEtBxB,GAAG,CAAC;UACFE,QAAQ,EAAEmB,WAAW;UACrBlB,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE,IAAI;UACXK,eAAe,EAAEY,WAAW,CAACG,MAAM,GAAG,CAAC,GACnCH,WAAW,CAACA,WAAW,CAACG,MAAM,GAAG,CAAC,CAAC,CAACC,SAAS,GAC7CX,KAAK,CAACL,eAAe;UACzBC,WAAW,EAAEI,KAAK,CAACH,MAAM,GAAG,CAAC,GAAGG,KAAK,CAACJ,WAAW,GAAGgB;QACtD,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAACZ,QAAQ,CAACa,QAAQ,IAAI,4BAA4B,CAAC;MACpE;IACF,CAAC,CAAC,OAAO1B,KAAU,EAAE;MACnBJ,GAAG,CAAC;QACFG,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEA,KAAK,CAAC2B,OAAO,IAAI;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC;EAEDC,WAAW,EAAE,MAAOD,OAAe,IAAK;IACtC,IAAI,CAACA,OAAO,CAACE,IAAI,CAAC,CAAC,EAAE;IAErBlB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEe,OAAO,CAAC;IACtD/B,GAAG,CAAC;MAAEK,SAAS,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAEzC,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMpB,cAAc,CAACqC,eAAe,CAAC;QACpDJ,QAAQ,EAAEC,OAAO,CAACE,IAAI,CAAC,CAAC;QACxBE,IAAI,EAAE;MACR,CAAC,CAAC;MAEF,IAAIlB,QAAQ,CAACE,OAAO,EAAE;QACpBnB,GAAG,CAAC;UACFK,SAAS,EAAE,KAAK;UAChBC,SAAS,EAAE;QACb,CAAC,CAAC;;QAEF;QACA8B,UAAU,CAAC,MAAM;UACfnC,GAAG,CAAC,CAAC,CAACY,YAAY,CAAC,CAAC;QACtB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACL,MAAM,IAAIgB,KAAK,CAACZ,QAAQ,CAACa,QAAQ,IAAI,yBAAyB,CAAC;MACjE;IACF,CAAC,CAAC,OAAO1B,KAAU,EAAE;MACnBJ,GAAG,CAAC;QACFK,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAEF,KAAK,CAAC2B,OAAO,IAAI;MAC9B,CAAC,CAAC;MACF,MAAM3B,KAAK;IACb;EACF,CAAC;EAED;EACAiC,YAAY,EAAEA,CAAA,KAAM;IAClB,MAAMvB,KAAK,GAAGb,GAAG,CAAC,CAAC;IACnB,IAAIa,KAAK,CAACP,SAAS,EAAE;IAErBP,GAAG,CAAC;MAAEO,SAAS,EAAE;IAAK,CAAC,CAAC;;IAExB;IACAN,GAAG,CAAC,CAAC,CAACY,YAAY,CAAC,CAAC;;IAEpB;IACAf,cAAc,GAAGwC,WAAW,CAAC,MAAM;MACjCrC,GAAG,CAAC,CAAC,CAACY,YAAY,CAAC,CAAC;IACtB,CAAC,EAAEC,KAAK,CAACN,YAAY,CAAC;EACxB,CAAC;EAED+B,WAAW,EAAEA,CAAA,KAAM;IACjBvC,GAAG,CAAC;MAAEO,SAAS,EAAE;IAAM,CAAC,CAAC;IAEzB,IAAIT,cAAc,EAAE;MAClB0C,aAAa,CAAC1C,cAAc,CAAC;MAC7BA,cAAc,GAAG,IAAI;IACvB;EACF,CAAC;EAED2C,UAAU,EAAEA,CAAA,KAAM;IAChBzC,GAAG,CAAC;MAAEU,WAAW,EAAE;IAAE,CAAC,CAAC;EACzB,CAAC;EAED;EACAgC,QAAQ,EAAEA,CAAA,KAAM;IACd1C,GAAG,CAAC;MACFW,MAAM,EAAE,IAAI;MACZC,WAAW,EAAE,KAAK;MAClBF,WAAW,EAAE;IACf,CAAC,CAAC;;IAEF;IACA,IAAI,CAACT,GAAG,CAAC,CAAC,CAACM,SAAS,EAAE;MACpBN,GAAG,CAAC,CAAC,CAACoC,YAAY,CAAC,CAAC;IACtB;EACF,CAAC;EAEDM,SAAS,EAAEA,CAAA,KAAM;IACf3C,GAAG,CAAC;MAAEW,MAAM,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAM,CAAC,CAAC;;IAE1C;IACAX,GAAG,CAAC,CAAC,CAACsC,WAAW,CAAC,CAAC;EACrB,CAAC;EAEDK,UAAU,EAAEA,CAAA,KAAM;IAChB,MAAM9B,KAAK,GAAGb,GAAG,CAAC,CAAC;IACnB,IAAIa,KAAK,CAACH,MAAM,EAAE;MAChBG,KAAK,CAAC6B,SAAS,CAAC,CAAC;IACnB,CAAC,MAAM;MACL7B,KAAK,CAAC4B,QAAQ,CAAC,CAAC;IAClB;EACF,CAAC;EAEDG,YAAY,EAAEA,CAAA,KAAM;IAClB7C,GAAG,CAAC;MAAEY,WAAW,EAAE;IAAK,CAAC,CAAC;EAC5B,CAAC;EAEDkC,YAAY,EAAEA,CAAA,KAAM;IAClB9C,GAAG,CAAC;MAAEY,WAAW,EAAE;IAAM,CAAC,CAAC;EAC7B,CAAC;EAED;EACAmC,UAAU,EAAEA,CAAA,KAAM;IAChB/C,GAAG,CAAC;MAAEI,KAAK,EAAE,IAAI;MAAEE,SAAS,EAAE;IAAK,CAAC,CAAC;EACvC,CAAC;EAED0C,aAAa,EAAEA,CAAA,KAAM;IACnBhD,GAAG,CAAC;MAAEE,QAAQ,EAAE,EAAE;MAAEO,eAAe,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAE,CAAC,CAAC;EAC9D;AACF,CAAC,CAAC,CAAC;;AAEH;AACA,OAAO,MAAMuC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,KAAK,GAAGpD,YAAY,CAAC,CAAC;EAE5B,OAAO;IACL,GAAGoD,KAAK;IAER;IACAC,WAAW,EAAED,KAAK,CAACjD,QAAQ,CAACsB,MAAM,GAAG,CAAC;IACtC6B,SAAS,EAAEF,KAAK,CAACzC,WAAW,GAAG,CAAC;IAChC4C,OAAO,EAAE,CAACH,KAAK,CAAC9C,SAAS,IAAI,CAAC8C,KAAK,CAAChD,SAAS;IAE7C;IACAoD,UAAU,EAAG9B,SAAiB,IAAK;MACjC,MAAM+B,IAAI,GAAG,IAAIC,IAAI,CAAChC,SAAS,CAAC;MAChC,OAAO+B,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IAEDC,UAAU,EAAGpC,SAAiB,IAAK;MACjC,MAAM+B,IAAI,GAAG,IAAIC,IAAI,CAAChC,SAAS,CAAC;MAChC,MAAMqC,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC;MACxB,MAAMM,SAAS,GAAG,IAAIN,IAAI,CAACK,KAAK,CAAC;MACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MAE1C,IAAIT,IAAI,CAACU,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;QAChD,OAAO,MAAM;MACf,CAAC,MAAM,IAAIV,IAAI,CAACU,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;QAC3D,OAAO,OAAO;MAChB,CAAC,MAAM;QACL,OAAOV,IAAI,CAACW,kBAAkB,CAAC,OAAO,CAAC;MACzC;IACF,CAAC;IAEDC,kBAAkB,EAAG3C,SAAiB,IAAK;MACzC,MAAM+B,IAAI,GAAG,IAAIC,IAAI,CAAChC,SAAS,CAAC;MAChC,MAAMqC,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC;MACxB,OAAOD,IAAI,CAACU,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC;IACrD;EACF,CAAC;AACH,CAAC;AAAChB,EAAA,CAzCWD,OAAO;EAAA,QACJlD,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}