import React, { useState, useEffect } from 'react';
import { useAuth } from '../stores/authStore';
import { usePlayer } from '../stores/playerStore';
import gameApi from '../services/gameApi';
import GameFooter from '../components/common/GameFooter';

const ConfigPage: React.FC = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const { currentPlayer, loadPlayerData } = usePlayer();
  
  // Estados para configurações
  const [notifications, setNotifications] = useState(true);
  const [soundEffects, setSoundEffects] = useState(true);
  const [autoSave, setAutoSave] = useState(true);
  const [theme, setTheme] = useState('dark');
  const [language, setLanguage] = useState('pt-BR');
  
  // Estados para mudança de senha
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  
  // Estados para mensagens
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    // Carregar configurações do localStorage
    const savedSettings = localStorage.getItem('shack-settings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      setNotifications(settings.notifications ?? true);
      setSoundEffects(settings.soundEffects ?? true);
      setAutoSave(settings.autoSave ?? true);
      setTheme(settings.theme ?? 'dark');
      setLanguage(settings.language ?? 'pt-BR');
    }
  };

  const saveSettings = () => {
    const settings = {
      notifications,
      soundEffects,
      autoSave,
      theme,
      language,
    };
    
    localStorage.setItem('shack-settings', JSON.stringify(settings));
    setSuccessMessage('Configurações salvas com sucesso!');
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (newPassword !== confirmPassword) {
      setErrorMessage('As senhas não coincidem');
      return;
    }

    if (newPassword.length < 6) {
      setErrorMessage('A nova senha deve ter pelo menos 6 caracteres');
      return;
    }

    setIsChangingPassword(true);
    setErrorMessage(null);

    try {
      const response = await gameApi.changePassword({
        currentPassword,
        newPassword,
      });

      if (response.sucesso) {
        setSuccessMessage('Senha alterada com sucesso!');
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
      } else {
        setErrorMessage(response.mensagem || 'Erro ao alterar senha');
      }
    } catch (error: any) {
      setErrorMessage(error.message || 'Erro de conexão');
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleLogout = async () => {
    if (confirm('Tem certeza que deseja sair?')) {
      await logout();
      window.location.href = '/';
    }
  };

  const clearCache = () => {
    if (confirm('Isso irá limpar todos os dados salvos localmente. Continuar?')) {
      localStorage.clear();
      sessionStorage.clear();
      setSuccessMessage('Cache limpo com sucesso!');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">🔒 Acesso Negado</h1>
          <p className="text-gray-400">Você precisa estar logado para acessar as configurações</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => window.history.back()}
            className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600"
          >
            <span className="text-lg">←</span>
          </button>
          <div>
            <h1 className="text-lg font-bold">⚙️ Configurações</h1>
            <p className="text-xs text-gray-400">Preferências do Sistema</p>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {/* Mensagens */}
        {successMessage && (
          <div className="bg-green-900 border border-green-500 rounded-lg p-3">
            <p className="text-green-300 text-sm">✅ {successMessage}</p>
          </div>
        )}

        {errorMessage && (
          <div className="bg-red-900 border border-red-500 rounded-lg p-3">
            <p className="text-red-300 text-sm">❌ {errorMessage}</p>
          </div>
        )}

        {/* Informações da conta */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold mb-4 text-white">👤 Informações da Conta</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Nick:</span>
              <span className="text-white">{user?.nick}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Email:</span>
              <span className="text-white">{user?.email}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">IP:</span>
              <span className="text-white">{user?.ip || currentPlayer?.ip}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Nível:</span>
              <span className="text-white">{currentPlayer?.nivel || 1}</span>
            </div>
          </div>
        </div>

        {/* Configurações gerais */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold mb-4 text-white">🔧 Configurações Gerais</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Notificações</span>
              <button
                onClick={() => setNotifications(!notifications)}
                className={`w-12 h-6 rounded-full ${
                  notifications ? 'bg-blue-600' : 'bg-gray-600'
                } relative transition-colors`}
              >
                <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
                  notifications ? 'translate-x-7' : 'translate-x-1'
                }`}></div>
              </button>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-300">Efeitos Sonoros</span>
              <button
                onClick={() => setSoundEffects(!soundEffects)}
                className={`w-12 h-6 rounded-full ${
                  soundEffects ? 'bg-blue-600' : 'bg-gray-600'
                } relative transition-colors`}
              >
                <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
                  soundEffects ? 'translate-x-7' : 'translate-x-1'
                }`}></div>
              </button>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-300">Salvamento Automático</span>
              <button
                onClick={() => setAutoSave(!autoSave)}
                className={`w-12 h-6 rounded-full ${
                  autoSave ? 'bg-blue-600' : 'bg-gray-600'
                } relative transition-colors`}
              >
                <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
                  autoSave ? 'translate-x-7' : 'translate-x-1'
                }`}></div>
              </button>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-300">Tema</span>
              <select
                value={theme}
                onChange={(e) => setTheme(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white"
              >
                <option value="dark">Escuro</option>
                <option value="light">Claro</option>
                <option value="auto">Automático</option>
              </select>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-300">Idioma</span>
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white"
              >
                <option value="pt-BR">Português (BR)</option>
                <option value="en-US">English (US)</option>
                <option value="es-ES">Español</option>
              </select>
            </div>

            <button
              onClick={saveSettings}
              className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold"
            >
              Salvar Configurações
            </button>
          </div>
        </div>

        {/* Mudança de senha */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold mb-4 text-white">🔒 Alterar Senha</h3>
          <form onSubmit={handlePasswordChange} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Senha Atual
              </label>
              <input
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                disabled={isChangingPassword}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Nova Senha
              </label>
              <input
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                disabled={isChangingPassword}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Confirmar Nova Senha
              </label>
              <input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                disabled={isChangingPassword}
              />
            </div>

            <button
              type="submit"
              disabled={isChangingPassword || !currentPassword || !newPassword || !confirmPassword}
              className="w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white rounded-lg font-semibold"
            >
              {isChangingPassword ? 'Alterando...' : 'Alterar Senha'}
            </button>
          </form>
        </div>

        {/* Ações avançadas */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold mb-4 text-white">🛠️ Ações Avançadas</h3>
          <div className="space-y-3">
            <button
              onClick={clearCache}
              className="w-full py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-semibold"
            >
              Limpar Cache
            </button>

            <button
              onClick={handleLogout}
              className="w-full py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold"
            >
              Sair da Conta
            </button>
          </div>
        </div>

        {/* Informações do sistema */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold mb-4 text-white">ℹ️ Informações do Sistema</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-400">Versão:</span>
              <span className="text-white">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Build:</span>
              <span className="text-white">React-2024</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Servidor:</span>
              <span className="text-white">Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer com navegação */}
      <GameFooter currentPage="config" />
    </div>
  );
};

export default ConfigPage;
