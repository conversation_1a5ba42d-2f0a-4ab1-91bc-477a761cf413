{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea <PERSON> T<PERSON>alho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\ui\\\\AnimatedBackground.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnimatedBackground = ({\n  variant = 'matrix',\n  className = ''\n}) => {\n  const renderMatrix = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute inset-0 overflow-hidden opacity-10\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"matrix-bg\",\n      children: Array.from({\n        length: 20\n      }).map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"matrix-column\",\n        style: {\n          left: `${i * 5}%`,\n          animationDelay: `${Math.random() * 2}s`,\n          animationDuration: `${3 + Math.random() * 2}s`\n        },\n        children: Array.from({\n          length: 20\n        }).map((_, j) => /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"matrix-char\",\n          style: {\n            animationDelay: `${j * 0.1}s`\n          },\n          children: String.fromCharCode(0x30A0 + Math.random() * 96)\n        }, j, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 15\n        }, this))\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .matrix-bg {\n          position: relative;\n          width: 100%;\n          height: 100%;\n        }\n        .matrix-column {\n          position: absolute;\n          top: -100%;\n          width: 20px;\n          height: 200%;\n          animation: matrix-fall linear infinite;\n          font-family: 'Courier New', monospace;\n          font-size: 14px;\n          color: #00ff41;\n          display: flex;\n          flex-direction: column;\n        }\n        .matrix-char {\n          opacity: 0;\n          animation: matrix-fade 0.5s ease-in-out infinite alternate;\n        }\n        @keyframes matrix-fall {\n          to {\n            transform: translateY(100vh);\n          }\n        }\n        @keyframes matrix-fade {\n          to {\n            opacity: 1;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n  const renderParticles = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute inset-0 overflow-hidden opacity-20\",\n    children: Array.from({\n      length: 50\n    }).map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute w-1 h-1 bg-blue-400 rounded-full animate-pulse\",\n      style: {\n        left: `${Math.random() * 100}%`,\n        top: `${Math.random() * 100}%`,\n        animationDelay: `${Math.random() * 3}s`,\n        animationDuration: `${2 + Math.random() * 2}s`\n      }\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n  const renderGrid = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute inset-0 opacity-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full h-full\",\n      style: {\n        backgroundImage: `\n            linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px)\n          `,\n        backgroundSize: '50px 50px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n  const renderWaves = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute inset-0 overflow-hidden opacity-10\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"absolute bottom-0 w-full h-32\",\n      viewBox: \"0 0 1200 120\",\n      preserveAspectRatio: \"none\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\",\n        opacity: \".25\",\n        className: \"fill-blue-500 animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z\",\n        opacity: \".5\",\n        className: \"fill-blue-600 animate-pulse\",\n        style: {\n          animationDelay: '1s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z\",\n        className: \"fill-blue-700 animate-pulse\",\n        style: {\n          animationDelay: '2s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n  const backgrounds = {\n    matrix: renderMatrix,\n    particles: renderParticles,\n    grid: renderGrid,\n    waves: renderWaves\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `absolute inset-0 pointer-events-none ${className}`,\n    children: backgrounds[variant]()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_c = AnimatedBackground;\nexport default AnimatedBackground;\nvar _c;\n$RefreshReg$(_c, \"AnimatedBackground\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AnimatedBackground", "variant", "className", "renderMatrix", "children", "Array", "from", "length", "map", "_", "i", "style", "left", "animationDelay", "Math", "random", "animationDuration", "j", "String", "fromCharCode", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "renderParticles", "top", "renderGrid", "backgroundImage", "backgroundSize", "renderWaves", "viewBox", "preserveAspectRatio", "d", "opacity", "backgrounds", "matrix", "particles", "grid", "waves", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/ui/AnimatedBackground.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface AnimatedBackgroundProps {\n  variant?: 'matrix' | 'particles' | 'grid' | 'waves';\n  className?: string;\n}\n\nconst AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({\n  variant = 'matrix',\n  className = '',\n}) => {\n  const renderMatrix = () => (\n    <div className=\"absolute inset-0 overflow-hidden opacity-10\">\n      <div className=\"matrix-bg\">\n        {Array.from({ length: 20 }).map((_, i) => (\n          <div\n            key={i}\n            className=\"matrix-column\"\n            style={{\n              left: `${i * 5}%`,\n              animationDelay: `${Math.random() * 2}s`,\n              animationDuration: `${3 + Math.random() * 2}s`,\n            }}\n          >\n            {Array.from({ length: 20 }).map((_, j) => (\n              <span\n                key={j}\n                className=\"matrix-char\"\n                style={{\n                  animationDelay: `${j * 0.1}s`,\n                }}\n              >\n                {String.fromCharCode(0x30A0 + Math.random() * 96)}\n              </span>\n            ))}\n          </div>\n        ))}\n      </div>\n      <style jsx>{`\n        .matrix-bg {\n          position: relative;\n          width: 100%;\n          height: 100%;\n        }\n        .matrix-column {\n          position: absolute;\n          top: -100%;\n          width: 20px;\n          height: 200%;\n          animation: matrix-fall linear infinite;\n          font-family: 'Courier New', monospace;\n          font-size: 14px;\n          color: #00ff41;\n          display: flex;\n          flex-direction: column;\n        }\n        .matrix-char {\n          opacity: 0;\n          animation: matrix-fade 0.5s ease-in-out infinite alternate;\n        }\n        @keyframes matrix-fall {\n          to {\n            transform: translateY(100vh);\n          }\n        }\n        @keyframes matrix-fade {\n          to {\n            opacity: 1;\n          }\n        }\n      `}</style>\n    </div>\n  );\n\n  const renderParticles = () => (\n    <div className=\"absolute inset-0 overflow-hidden opacity-20\">\n      {Array.from({ length: 50 }).map((_, i) => (\n        <div\n          key={i}\n          className=\"absolute w-1 h-1 bg-blue-400 rounded-full animate-pulse\"\n          style={{\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`,\n            animationDelay: `${Math.random() * 3}s`,\n            animationDuration: `${2 + Math.random() * 2}s`,\n          }}\n        />\n      ))}\n    </div>\n  );\n\n  const renderGrid = () => (\n    <div className=\"absolute inset-0 opacity-5\">\n      <div\n        className=\"w-full h-full\"\n        style={{\n          backgroundImage: `\n            linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px',\n        }}\n      />\n    </div>\n  );\n\n  const renderWaves = () => (\n    <div className=\"absolute inset-0 overflow-hidden opacity-10\">\n      <svg\n        className=\"absolute bottom-0 w-full h-32\"\n        viewBox=\"0 0 1200 120\"\n        preserveAspectRatio=\"none\"\n      >\n        <path\n          d=\"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\"\n          opacity=\".25\"\n          className=\"fill-blue-500 animate-pulse\"\n        />\n        <path\n          d=\"M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z\"\n          opacity=\".5\"\n          className=\"fill-blue-600 animate-pulse\"\n          style={{ animationDelay: '1s' }}\n        />\n        <path\n          d=\"M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z\"\n          className=\"fill-blue-700 animate-pulse\"\n          style={{ animationDelay: '2s' }}\n        />\n      </svg>\n    </div>\n  );\n\n  const backgrounds = {\n    matrix: renderMatrix,\n    particles: renderParticles,\n    grid: renderGrid,\n    waves: renderWaves,\n  };\n\n  return (\n    <div className={`absolute inset-0 pointer-events-none ${className}`}>\n      {backgrounds[variant]()}\n    </div>\n  );\n};\n\nexport default AnimatedBackground;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO1B,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,OAAO,GAAG,QAAQ;EAClBC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAGA,CAAA,kBACnBJ,OAAA;IAAKG,SAAS,EAAC,6CAA6C;IAAAE,QAAA,gBAC1DL,OAAA;MAAKG,SAAS,EAAC,WAAW;MAAAE,QAAA,EACvBC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACnCX,OAAA;QAEEG,SAAS,EAAC,eAAe;QACzBS,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGF,CAAC,GAAG,CAAC,GAAG;UACjBG,cAAc,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCC,iBAAiB,EAAE,GAAG,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C,CAAE;QAAAX,QAAA,EAEDC,KAAK,CAACC,IAAI,CAAC;UAAEC,MAAM,EAAE;QAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEQ,CAAC,kBACnClB,OAAA;UAEEG,SAAS,EAAC,aAAa;UACvBS,KAAK,EAAE;YACLE,cAAc,EAAE,GAAGI,CAAC,GAAG,GAAG;UAC5B,CAAE;UAAAb,QAAA,EAEDc,MAAM,CAACC,YAAY,CAAC,MAAM,GAAGL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QAAC,GAN5CE,CAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOF,CACP;MAAC,GAlBGb,CAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmBH,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNxB,OAAA;MAAOyB,GAAG;MAAApB,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CACN;EAED,MAAME,eAAe,GAAGA,CAAA,kBACtB1B,OAAA;IAAKG,SAAS,EAAC,6CAA6C;IAAAE,QAAA,EACzDC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACnCX,OAAA;MAEEG,SAAS,EAAC,yDAAyD;MACnES,KAAK,EAAE;QACLC,IAAI,EAAE,GAAGE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAC/BW,GAAG,EAAE,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAC9BF,cAAc,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;QACvCC,iBAAiB,EAAE,GAAG,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;MAC7C;IAAE,GAPGL,CAAC;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQP,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMI,UAAU,GAAGA,CAAA,kBACjB5B,OAAA;IAAKG,SAAS,EAAC,4BAA4B;IAAAE,QAAA,eACzCL,OAAA;MACEG,SAAS,EAAC,eAAe;MACzBS,KAAK,EAAE;QACLiB,eAAe,EAAE;AAC3B;AACA;AACA,WAAW;QACDC,cAAc,EAAE;MAClB;IAAE;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMO,WAAW,GAAGA,CAAA,kBAClB/B,OAAA;IAAKG,SAAS,EAAC,6CAA6C;IAAAE,QAAA,eAC1DL,OAAA;MACEG,SAAS,EAAC,+BAA+B;MACzC6B,OAAO,EAAC,cAAc;MACtBC,mBAAmB,EAAC,MAAM;MAAA5B,QAAA,gBAE1BL,OAAA;QACEkC,CAAC,EAAC,uNAAuN;QACzNC,OAAO,EAAC,KAAK;QACbhC,SAAS,EAAC;MAA6B;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACFxB,OAAA;QACEkC,CAAC,EAAC,8WAA8W;QAChXC,OAAO,EAAC,IAAI;QACZhC,SAAS,EAAC,6BAA6B;QACvCS,KAAK,EAAE;UAAEE,cAAc,EAAE;QAAK;MAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACFxB,OAAA;QACEkC,CAAC,EAAC,qLAAqL;QACvL/B,SAAS,EAAC,6BAA6B;QACvCS,KAAK,EAAE;UAAEE,cAAc,EAAE;QAAK;MAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMY,WAAW,GAAG;IAClBC,MAAM,EAAEjC,YAAY;IACpBkC,SAAS,EAAEZ,eAAe;IAC1Ba,IAAI,EAAEX,UAAU;IAChBY,KAAK,EAAET;EACT,CAAC;EAED,oBACE/B,OAAA;IAAKG,SAAS,EAAE,wCAAwCA,SAAS,EAAG;IAAAE,QAAA,EACjE+B,WAAW,CAAClC,OAAO,CAAC,CAAC;EAAC;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC;AAEV,CAAC;AAACiB,EAAA,GA1IIxC,kBAAqD;AA4I3D,eAAeA,kBAAkB;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}