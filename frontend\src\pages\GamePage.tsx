import React, { useState, useEffect, useRef } from 'react';
import { Routes, Route } from 'react-router-dom';
import { useGame } from '../stores/gameStore';
import { useChat } from '../stores/chatStore';
import Header from '../components/layout/Header';
import Navigation from '../components/layout/Navigation';
import LoadingSpinner from '../components/common/LoadingSpinner';
import ChatSystem from '../components/chat/ChatSystem';
import ChatMessage from '../components/chat/ChatMessage';
import {
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline';

// Componentes do dashboard
import PlayerStats from '../components/dashboard/PlayerStats';
import RecentActivity from '../components/dashboard/RecentActivity';
import QuickActions from '../components/dashboard/QuickActions';

const GameDashboard = () => {
  const { currentPlayer, isLoadingPlayer } = useGame();

  if (isLoadingPlayer) {
    return (
      <div className="p-8">
        <div className="max-w-7xl mx-auto">
          <LoadingSpinner size="lg" text="Carregando dashboard..." />
        </div>
      </div>
    );
  }

  if (!currentPlayer) {
    return (
      <div className="p-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h2 className="text-xl text-text-primary mb-4">Erro ao carregar dados do jogador</h2>
            <button
              onClick={() => window.location.reload()}
              className="btn-primary"
            >
              Recarregar
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header do dashboard */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text-primary mb-2 flex items-center">
            <span className="mr-3">🎮</span>
            Dashboard
          </h1>
          <p className="text-text-secondary">
            Bem-vindo de volta, {currentPlayer.nick}! Aqui está um resumo da sua atividade.
          </p>
        </div>

        {/* Layout do dashboard */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Coluna principal - Estatísticas do jogador */}
          <div className="lg:col-span-2">
            <PlayerStats player={currentPlayer} />
          </div>

          {/* Coluna lateral */}
          <div className="space-y-6">
            {/* Ações rápidas */}
            <QuickActions />

            {/* Atividade recente */}
            <RecentActivity />
          </div>
        </div>
      </div>
    </div>
  );
};

const Scanner = () => (
  <div className="p-8">
    <div className="max-w-7xl mx-auto">
      <h2 className="text-2xl font-bold text-text-primary mb-6 flex items-center">
        <span className="mr-3">🔍</span>
        Scanner de Alvos
      </h2>

      <div className="card">
        <p className="text-text-secondary text-center py-8">
          Em breve: Sistema de escaneamento de alvos com interface React
        </p>
      </div>
    </div>
  </div>
);

const Chat = () => {
  const {
    messages,
    isLoading,
    error,
    isSending,
    sendMessage,
    loadMessages,
    startPolling,
    stopPolling,
    clearError
  } = useChat();

  const [messageInput, setMessageInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Iniciar polling quando a página carregar
  useEffect(() => {
    startPolling();
    return () => stopPolling();
  }, [startPolling, stopPolling]);

  // Auto-scroll para a última mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!messageInput.trim() || isSending) return;

    const message = messageInput.trim();
    setMessageInput('');

    try {
      await sendMessage(message);
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  return (
    <div className="p-4 md:p-8 h-full flex flex-col">
      <div className="max-w-4xl mx-auto flex-1 flex flex-col">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-text-primary mb-2 flex items-center">
            <span className="mr-3">💬</span>
            Chat Global
          </h2>
          <p className="text-text-secondary">
            Converse com outros jogadores em tempo real
          </p>
        </div>

        {/* Chat Container */}
        <div className="card flex-1 flex flex-col min-h-0">
          {/* Área de mensagens */}
          <div className="flex-1 overflow-y-auto p-4 space-y-2 min-h-0">
            {isLoading && messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <LoadingSpinner size="lg" text="Carregando mensagens..." />
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <p className="text-red-400 mb-4">{error}</p>
                  <button
                    onClick={() => {
                      clearError();
                      loadMessages();
                    }}
                    className="btn-primary"
                  >
                    Tentar Novamente
                  </button>
                </div>
              </div>
            ) : messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-text-muted">
                  <ChatBubbleLeftRightIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">Nenhuma mensagem ainda</p>
                  <p>Seja o primeiro a conversar!</p>
                </div>
              </div>
            ) : (
              <>
                {messages.map((message) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                    showAvatar={true}
                    showTimestamp={true}
                  />
                ))}
                <div ref={messagesEndRef} />
              </>
            )}
          </div>

          {/* Input de mensagem */}
          <div className="border-t border-border-color p-4">
            <form onSubmit={handleSendMessage} className="flex space-x-3">
              <input
                type="text"
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                placeholder="Digite sua mensagem..."
                disabled={isSending}
                className="flex-1 input-primary"
                maxLength={500}
              />
              <button
                type="submit"
                disabled={!messageInput.trim() || isSending}
                className="btn-primary px-6 flex items-center space-x-2"
              >
                {isSending ? (
                  <LoadingSpinner size="sm" color="white" />
                ) : (
                  <PaperAirplaneIcon className="h-5 w-5" />
                )}
                <span>Enviar</span>
              </button>
            </form>

            <div className="flex items-center justify-between mt-2 text-xs text-text-muted">
              <span>{messageInput.length}/500 caracteres</span>
              <span>Enter para enviar</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const Store = () => (
  <div className="p-8">
    <div className="max-w-7xl mx-auto">
      <h2 className="text-2xl font-bold text-text-primary mb-6 flex items-center">
        <span className="mr-3">🛒</span>
        Loja de Apps
      </h2>

      <div className="card">
        <p className="text-text-secondary text-center py-8">
          Em breve: Loja de aplicativos para comprar upgrades
        </p>
      </div>
    </div>
  </div>
);

const Security = () => (
  <div className="p-8">
    <div className="max-w-7xl mx-auto">
      <h2 className="text-2xl font-bold text-text-primary mb-6 flex items-center">
        <span className="mr-3">🛡️</span>
        Monitoramento de Segurança
      </h2>

      <div className="card">
        <p className="text-text-secondary text-center py-8">
          Em breve: Sistema de monitoramento de invasões
        </p>
      </div>
    </div>
  </div>
);

const GamePage: React.FC = () => {
  const { loadPlayerData, isLoadingPlayer, currentPlayer } = useGame();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Carregar dados do jogador ao montar o componente
  useEffect(() => {
    if (!currentPlayer) {
      loadPlayerData();
    }
  }, [loadPlayerData, currentPlayer]);

  // Mostrar loading se ainda estiver carregando dados do jogador
  if (isLoadingPlayer && !currentPlayer) {
    return (
      <LoadingSpinner
        fullScreen
        size="lg"
        text="Carregando dados do jogo..."
        color="primary"
      />
    );
  }

  return (
    <div className="min-h-screen bg-bg-primary flex">
      {/* Navegação lateral */}
      <Navigation
        isMobileMenuOpen={isMobileMenuOpen}
        onMobileMenuClose={() => setIsMobileMenuOpen(false)}
      />

      {/* Conteúdo principal */}
      <div className="flex-1 flex flex-col md:ml-0">
        {/* Header */}
        <Header onMenuToggle={() => setIsMobileMenuOpen(true)} />

        {/* Conteúdo das páginas */}
        <main className="flex-1 overflow-y-auto pb-16 md:pb-0">
          <Routes>
            <Route path="/" element={<GameDashboard />} />
            <Route path="/scanner" element={<Scanner />} />
            <Route path="/chat" element={<Chat />} />
            <Route path="/store" element={<Store />} />
            <Route path="/security" element={<Security />} />
            <Route path="/group" element={<div className="p-8"><div className="card"><p className="text-center py-8 text-text-secondary">Em breve: Gerenciamento de grupo</p></div></div>} />
            <Route path="/terminal" element={<div className="p-8"><div className="card"><p className="text-center py-8 text-text-secondary">Em breve: Terminal de conexões</p></div></div>} />
            <Route path="/bank" element={<div className="p-8"><div className="card"><p className="text-center py-8 text-text-secondary">Em breve: Sistema bancário</p></div></div>} />
            <Route path="*" element={<GameDashboard />} />
          </Routes>
        </main>
      </div>

      {/* Chat System - Disponível em todas as páginas */}
      <ChatSystem />
    </div>
  );
};

export default GamePage;
