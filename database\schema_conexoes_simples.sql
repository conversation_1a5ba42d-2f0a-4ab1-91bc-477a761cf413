-- SQL SIMPLIFICADO para criar tabela de conexões ativas
-- Execute este SQL no Supabase SQL Editor

-- 1. Criar tabela
CREATE TABLE IF NOT EXISTS conexoes_ativas (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    atacante_uid TEXT NOT NULL,
    alvo_ip TEXT NOT NULL,
    alvo_nick TEXT NOT NULL,
    alvo_bankguard INTEGER DEFAULT 1,
    alvo_dinheiro INTEGER DEFAULT 0,
    status TEXT DEFAULT 'ativa',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Constraint para evitar duplicatas
    CONSTRAINT unique_atacante_alvo UNIQUE (atacante_uid, alvo_ip)
);

-- 2. Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_conexoes_atacante ON conexoes_ativas (atacante_uid);
CREATE INDEX IF NOT EXISTS idx_conexoes_expires ON conexoes_ativas (expires_at);
CREATE INDEX IF NOT EXISTS idx_conexoes_status ON conexoes_ativas (status);

-- 3. Função para limpar conexões expiradas
CREATE OR REPLACE FUNCTION limpar_conexoes_expiradas()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM conexoes_ativas 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 4. Função para obter conexões ativas de um jogador
CREATE OR REPLACE FUNCTION obter_conexoes_jogador(uid_jogador TEXT)
RETURNS TABLE (
    id UUID,
    atacante_uid TEXT,
    alvo_ip TEXT,
    alvo_nick TEXT,
    alvo_bankguard INTEGER,
    alvo_dinheiro INTEGER,
    status TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- Limpa expiradas primeiro
    PERFORM limpar_conexoes_expiradas();
    
    -- Retorna conexões ativas do jogador
    RETURN QUERY
    SELECT c.id, c.atacante_uid, c.alvo_ip, c.alvo_nick, 
           c.alvo_bankguard, c.alvo_dinheiro, c.status, 
           c.created_at, c.expires_at
    FROM conexoes_ativas c
    WHERE c.atacante_uid = uid_jogador 
      AND c.status = 'ativa'
      AND c.expires_at > NOW()
    ORDER BY c.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Teste da tabela (opcional)
-- SELECT limpar_conexoes_expiradas(); -- Remove conexões expiradas
-- SELECT * FROM conexoes_ativas; -- Lista todas as conexões
