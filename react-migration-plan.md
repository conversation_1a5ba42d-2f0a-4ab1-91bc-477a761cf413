# 🚀 React Migration Plan for Shack Web Game

## Phase 1: Foundation Setup (Week 1-2)

### 1.1 Create React App Structure
```bash
# Create React app alongside existing Flask app
npx create-react-app shack-frontend
cd shack-frontend

# Install additional dependencies
npm install axios react-router-dom @tanstack/react-query
npm install zustand # For state management
npm install socket.io-client # For real-time features
npm install tailwindcss @headlessui/react # UI framework
```

### 1.2 Project Structure
```
shack-frontend/
├── src/
│   ├── components/
│   │   ├── auth/
│   │   ├── dashboard/
│   │   ├── game/
│   │   ├── chat/
│   │   └── common/
│   ├── hooks/
│   ├── services/
│   ├── stores/
│   ├── utils/
│   └── styles/
├── public/
└── package.json
```

### 1.3 API Service Layer
```javascript
// src/services/api.js
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

class ApiService {
  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add auth interceptor
    this.client.interceptors.request.use((config) => {
      const token = localStorage.getItem('shack_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  // Game API methods
  async getPlayerData() {
    const response = await this.client.get('/api/jogador');
    return response.data;
  }

  async scanTargets() {
    const response = await this.client.get('/api/scan');
    return response.data;
  }

  // Add other API methods...
}

export default new ApiService();
```

## Phase 2: Authentication Migration (Week 2-3)

### 2.1 Auth Components
```jsx
// src/components/auth/LoginForm.jsx
import React, { useState } from 'react';
import { useAuthStore } from '../../stores/authStore';

const LoginForm = () => {
  const [credentials, setCredentials] = useState({ email: '', password: '' });
  const { login, isLoading } = useAuthStore();

  const handleSubmit = async (e) => {
    e.preventDefault();
    await login(credentials);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <input
        type="email"
        placeholder="Email"
        value={credentials.email}
        onChange={(e) => setCredentials({...credentials, email: e.target.value})}
        className="w-full p-3 rounded-lg bg-surface-elevated text-primary-text border border-border-color focus:border-accent-blue focus:outline-none transition-colors"
        required
      />
      <input
        type="password"
        placeholder="Senha"
        value={credentials.password}
        onChange={(e) => setCredentials({...credentials, password: e.target.value})}
        className="w-full p-3 rounded-lg bg-surface-elevated text-primary-text border border-border-color focus:border-accent-blue focus:outline-none transition-colors"
        required
      />
      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-accent-blue hover:bg-blue-600 text-white py-3 rounded-lg font-medium transition-colors disabled:opacity-50"
      >
        {isLoading ? 'Entrando...' : 'Entrar'}
      </button>
    </form>
  );
};

export default LoginForm;
```

### 2.2 Auth Store (Zustand)
```javascript
// src/stores/authStore.js
import { create } from 'zustand';
import ApiService from '../services/api';

export const useAuthStore = create((set, get) => ({
  user: null,
  token: localStorage.getItem('shack_token'),
  isLoading: false,
  isAuthenticated: !!localStorage.getItem('shack_token'),

  login: async (credentials) => {
    set({ isLoading: true });
    try {
      const response = await ApiService.login(credentials);
      if (response.sucesso) {
        localStorage.setItem('shack_token', response.token);
        localStorage.setItem('shack_user', JSON.stringify(response.user));
        set({
          user: response.user,
          token: response.token,
          isAuthenticated: true,
          isLoading: false,
        });
      }
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  logout: () => {
    localStorage.removeItem('shack_token');
    localStorage.removeItem('shack_user');
    set({
      user: null,
      token: null,
      isAuthenticated: false,
    });
  },
}));
```

## Phase 3: Core Game Components (Week 3-5)

### 3.1 Game Dashboard
```jsx
// src/components/dashboard/GameDashboard.jsx
import React from 'react';
import { useGameStore } from '../../stores/gameStore';
import PlayerStats from './PlayerStats';
import Navigation from './Navigation';

const GameDashboard = () => {
  const { currentPlayer, isLoading } = useGameStore();

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="game-section">
      <h2 className="text-2xl font-semibold mb-4 flex items-center text-primary-text">
        <svg className="size-6 mr-3 text-accent-blue" viewBox="0 0 24 24" fill="currentColor">
          <path d="M18.375 2.25c-1.035 0-1.875.84-1.875 1.875v15.75c0 1.035.84 1.875 1.875 1.875h.75c1.035 0 1.875-.84 1.875-1.875V4.125c0-1.036-.84-1.875-1.875-1.875h-.75Z" />
        </svg>
        <span>{currentPlayer?.nick}</span>
      </h2>
      
      <PlayerStats player={currentPlayer} />
      <Navigation />
    </div>
  );
};

export default GameDashboard;
```

### 3.2 Game Store
```javascript
// src/stores/gameStore.js
import { create } from 'zustand';
import ApiService from '../services/api';

export const useGameStore = create((set, get) => ({
  currentPlayer: null,
  exploitedTarget: null,
  isLoading: false,
  
  loadPlayerData: async () => {
    set({ isLoading: true });
    try {
      const response = await ApiService.getPlayerData();
      if (response.sucesso) {
        set({
          currentPlayer: response.jogador,
          isLoading: false,
        });
      }
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  updatePlayerMoney: (newAmount) => {
    set((state) => ({
      currentPlayer: {
        ...state.currentPlayer,
        dinheiro: newAmount,
      },
    }));
  },
}));
```

## Phase 4: Real-time Features (Week 5-6)

### 4.1 Chat System
```jsx
// src/components/chat/ChatSystem.jsx
import React, { useState, useEffect, useRef } from 'react';
import { useChatStore } from '../../stores/chatStore';

const ChatSystem = () => {
  const [message, setMessage] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const messagesEndRef = useRef(null);
  
  const { messages, sendMessage, loadMessages, isLoading } = useChatStore();

  useEffect(() => {
    loadMessages();
    // Set up polling for new messages
    const interval = setInterval(loadMessages, 2000);
    return () => clearInterval(interval);
  }, [loadMessages]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (message.trim()) {
      await sendMessage(message);
      setMessage('');
    }
  };

  return (
    <div className={`chat-container ${isOpen ? 'open' : 'closed'}`}>
      {/* Chat implementation */}
    </div>
  );
};

export default ChatSystem;
```

## Phase 5: Advanced Features (Week 6-8)

### 5.1 Scanner System
### 5.2 Security Monitoring
### 5.3 App Store
### 5.4 Group Management

## Technical Considerations

### State Management Strategy
- **Zustand**: Lightweight, simple state management
- **React Query**: Server state management and caching
- **Local State**: useState for component-specific state

### Real-time Updates
- **Polling**: Continue current approach initially
- **WebSockets**: Upgrade later for true real-time
- **Server-Sent Events**: Alternative for one-way updates

### Styling Approach
- **TailwindCSS**: Keep existing utility-first approach
- **CSS Modules**: For component-specific styles
- **Styled Components**: Alternative for dynamic styling

### Performance Optimizations
- **Code Splitting**: Route-based and component-based
- **Memoization**: React.memo, useMemo, useCallback
- **Virtual Scrolling**: For large lists (chat, rankings)
- **Image Optimization**: Lazy loading, WebP format
