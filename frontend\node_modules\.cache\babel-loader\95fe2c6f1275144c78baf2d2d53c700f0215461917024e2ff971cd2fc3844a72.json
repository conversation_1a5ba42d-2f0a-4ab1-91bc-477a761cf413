{"ast": null, "code": "import{create}from'zustand';import mockApiService from'../services/mockApi';export const usePlayerStore=create((set,get)=>({// Estado inicial\ncurrentPlayer:null,isLoadingPlayer:false,playerError:null,// Carregar dados do jogador\nloadPlayerData:async()=>{const{isLoadingPlayer}=get();if(isLoadingPlayer){console.log('PlayerStore - Carregamento já em andamento');return;}console.log('PlayerStore - Carregando dados do jogador...');set({isLoadingPlayer:true,playerError:null});try{const response=await mockApiService.getPlayerData();if(response.sucesso&&response.dados){console.log('PlayerStore - Dados carregados:',response.dados);set({currentPlayer:response.dados,isLoadingPlayer:false,playerError:null});}else{set({isLoadingPlayer:false,playerError:response.mensagem||'Erro ao carregar dados'});}}catch(error){console.error('PlayerStore - Erro:',error);set({isLoadingPlayer:false,playerError:'Erro de conexão'});}},// Atualizar estatísticas do jogador\nupdatePlayerStats:stats=>{const{currentPlayer}=get();if(currentPlayer){console.log('PlayerStore - Atualizando stats:',stats);set({currentPlayer:{...currentPlayer,...stats}});}},// Limpar erro\nclearPlayerError:()=>{set({playerError:null});}}));// Hook personalizado\nexport const usePlayer=()=>{const{currentPlayer,isLoadingPlayer,playerError,loadPlayerData,clearPlayerError,updatePlayerStats}=usePlayerStore();return{// Estado\ncurrentPlayer,isLoadingPlayer,playerError,// Ações\nloadPlayerData,clearPlayerError,updatePlayerStats,// Computed\nhasPlayerData:!!currentPlayer,playerLevel:(currentPlayer===null||currentPlayer===void 0?void 0:currentPlayer.nivel)||0,playerPoints:(currentPlayer===null||currentPlayer===void 0?void 0:currentPlayer.pontos)||0,playerRank:(currentPlayer===null||currentPlayer===void 0?void 0:currentPlayer.ranking)||0};};", "map": {"version": 3, "names": ["create", "mockApiService", "usePlayerStore", "set", "get", "currentPlayer", "isLoadingPlayer", "playerError", "loadPlayerData", "console", "log", "response", "getPlayerData", "sucesso", "dados", "mensagem", "error", "updatePlayerStats", "stats", "clearPlayerError", "usePlayer", "hasPlayerData", "playerLevel", "nivel", "playerPoints", "pontos", "<PERSON><PERSON><PERSON><PERSON>", "ranking"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/playerStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport mockApiService, { PlayerData } from '../services/mockApi';\n\ninterface PlayerState {\n  // Estado\n  currentPlayer: PlayerData | null;\n  isLoadingPlayer: boolean;\n  playerError: string | null;\n\n  // Ações\n  loadPlayerData: () => Promise<void>;\n  clearPlayerError: () => void;\n  updatePlayerStats: (stats: Partial<PlayerData>) => void;\n}\n\nexport const usePlayerStore = create<PlayerState>((set, get) => ({\n  // Estado inicial\n  currentPlayer: null,\n  isLoadingPlayer: false,\n  playerError: null,\n\n  // Carregar dados do jogador\n  loadPlayerData: async () => {\n    const { isLoadingPlayer } = get();\n    \n    if (isLoadingPlayer) {\n      console.log('PlayerStore - Carregamento já em andamento');\n      return;\n    }\n\n    console.log('PlayerStore - Carregando dados do jogador...');\n    set({ isLoadingPlayer: true, playerError: null });\n\n    try {\n      const response = await mockApiService.getPlayerData();\n      \n      if (response.sucesso && response.dados) {\n        console.log('PlayerStore - Dados carregados:', response.dados);\n        set({\n          currentPlayer: response.dados,\n          isLoadingPlayer: false,\n          playerError: null,\n        });\n      } else {\n        set({\n          isLoadingPlayer: false,\n          playerError: response.mensagem || 'Erro ao carregar dados',\n        });\n      }\n    } catch (error) {\n      console.error('PlayerStore - Erro:', error);\n      set({\n        isLoadingPlayer: false,\n        playerError: 'Erro de conexão',\n      });\n    }\n  },\n\n  // Atualizar estatísticas do jogador\n  updatePlayerStats: (stats: Partial<PlayerData>) => {\n    const { currentPlayer } = get();\n    \n    if (currentPlayer) {\n      console.log('PlayerStore - Atualizando stats:', stats);\n      set({\n        currentPlayer: {\n          ...currentPlayer,\n          ...stats,\n        },\n      });\n    }\n  },\n\n  // Limpar erro\n  clearPlayerError: () => {\n    set({ playerError: null });\n  },\n}));\n\n// Hook personalizado\nexport const usePlayer = () => {\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    playerError,\n    loadPlayerData,\n    clearPlayerError,\n    updatePlayerStats,\n  } = usePlayerStore();\n\n  return {\n    // Estado\n    currentPlayer,\n    isLoadingPlayer,\n    playerError,\n    \n    // Ações\n    loadPlayerData,\n    clearPlayerError,\n    updatePlayerStats,\n    \n    // Computed\n    hasPlayerData: !!currentPlayer,\n    playerLevel: currentPlayer?.nivel || 0,\n    playerPoints: currentPlayer?.pontos || 0,\n    playerRank: currentPlayer?.ranking || 0,\n  };\n};\n"], "mappings": "AAAA,OAASA,MAAM,KAAQ,SAAS,CAChC,MAAO,CAAAC,cAAc,KAAsB,qBAAqB,CAchE,MAAO,MAAM,CAAAC,cAAc,CAAGF,MAAM,CAAc,CAACG,GAAG,CAAEC,GAAG,IAAM,CAC/D;AACAC,aAAa,CAAE,IAAI,CACnBC,eAAe,CAAE,KAAK,CACtBC,WAAW,CAAE,IAAI,CAEjB;AACAC,cAAc,CAAE,KAAAA,CAAA,GAAY,CAC1B,KAAM,CAAEF,eAAgB,CAAC,CAAGF,GAAG,CAAC,CAAC,CAEjC,GAAIE,eAAe,CAAE,CACnBG,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC,CACzD,OACF,CAEAD,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC,CAC3DP,GAAG,CAAC,CAAEG,eAAe,CAAE,IAAI,CAAEC,WAAW,CAAE,IAAK,CAAC,CAAC,CAEjD,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAV,cAAc,CAACW,aAAa,CAAC,CAAC,CAErD,GAAID,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,KAAK,CAAE,CACtCL,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAEC,QAAQ,CAACG,KAAK,CAAC,CAC9DX,GAAG,CAAC,CACFE,aAAa,CAAEM,QAAQ,CAACG,KAAK,CAC7BR,eAAe,CAAE,KAAK,CACtBC,WAAW,CAAE,IACf,CAAC,CAAC,CACJ,CAAC,IAAM,CACLJ,GAAG,CAAC,CACFG,eAAe,CAAE,KAAK,CACtBC,WAAW,CAAEI,QAAQ,CAACI,QAAQ,EAAI,wBACpC,CAAC,CAAC,CACJ,CACF,CAAE,MAAOC,KAAK,CAAE,CACdP,OAAO,CAACO,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3Cb,GAAG,CAAC,CACFG,eAAe,CAAE,KAAK,CACtBC,WAAW,CAAE,iBACf,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACAU,iBAAiB,CAAGC,KAA0B,EAAK,CACjD,KAAM,CAAEb,aAAc,CAAC,CAAGD,GAAG,CAAC,CAAC,CAE/B,GAAIC,aAAa,CAAE,CACjBI,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEQ,KAAK,CAAC,CACtDf,GAAG,CAAC,CACFE,aAAa,CAAE,CACb,GAAGA,aAAa,CAChB,GAAGa,KACL,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACAC,gBAAgB,CAAEA,CAAA,GAAM,CACtBhB,GAAG,CAAC,CAAEI,WAAW,CAAE,IAAK,CAAC,CAAC,CAC5B,CACF,CAAC,CAAC,CAAC,CAEH;AACA,MAAO,MAAM,CAAAa,SAAS,CAAGA,CAAA,GAAM,CAC7B,KAAM,CACJf,aAAa,CACbC,eAAe,CACfC,WAAW,CACXC,cAAc,CACdW,gBAAgB,CAChBF,iBACF,CAAC,CAAGf,cAAc,CAAC,CAAC,CAEpB,MAAO,CACL;AACAG,aAAa,CACbC,eAAe,CACfC,WAAW,CAEX;AACAC,cAAc,CACdW,gBAAgB,CAChBF,iBAAiB,CAEjB;AACAI,aAAa,CAAE,CAAC,CAAChB,aAAa,CAC9BiB,WAAW,CAAE,CAAAjB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEkB,KAAK,GAAI,CAAC,CACtCC,YAAY,CAAE,CAAAnB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEoB,MAAM,GAAI,CAAC,CACxCC,UAAU,CAAE,CAAArB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEsB,OAAO,GAAI,CACxC,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}