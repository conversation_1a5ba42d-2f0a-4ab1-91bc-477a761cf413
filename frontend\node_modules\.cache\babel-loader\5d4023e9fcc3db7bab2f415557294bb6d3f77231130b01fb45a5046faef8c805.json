{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\LoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../stores/authStore';\nimport LoginForm from '../components/auth/LoginForm';\nimport RegisterForm from '../components/auth/RegisterForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  const {\n    isAuthenticated,\n    user\n  } = useAuth();\n  const [isLoginMode, setIsLoginMode] = useState(true);\n\n  // Redirecionar se já estiver logado\n  if (isAuthenticated && user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/game\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-primary opacity-10 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-accent opacity-10 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 w-full max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center space-x-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-blue-500 text-2xl\",\n            children: \"\\u25CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-bold text-3xl text-text-primary\",\n            children: \"SHACK\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-blue-400 text-2xl\",\n            children: \"WEB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-blue-300 text-2xl\",\n            children: \"\\u25CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-secondary text-sm\",\n          children: \"Vers\\xE3o React - Sistema de Hacking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-fadeInUp\",\n        children: isLoginMode ? /*#__PURE__*/_jsxDEV(LoginForm, {\n          onSwitchToRegister: () => setIsLoginMode(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(RegisterForm, {\n          onSwitchToLogin: () => setIsLoginMode(true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-8 text-text-muted text-xs\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2024 SHACK Web Game - Todos os direitos reservados\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"4FAFNhTR2B1OIARf1+0o7yIl6iA=\", false, function () {\n  return [useAuth];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "Navigate", "useAuth", "LoginForm", "RegisterForm", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "isAuthenticated", "user", "isLoginMode", "setIsLoginMode", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onSwitchToRegister", "onSwitchToLogin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/LoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../stores/authStore';\nimport LoginForm from '../components/auth/LoginForm';\nimport RegisterForm from '../components/auth/RegisterForm';\n\nconst LoginPage: React.FC = () => {\n  const { isAuthenticated, user } = useAuth();\n  const [isLoginMode, setIsLoginMode] = useState(true);\n\n  // Redirecionar se já estiver logado\n  if (isAuthenticated && user) {\n    return <Navigate to=\"/game\" replace />;\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center p-4\">\n      {/* Background com gradiente */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary\" />\n      \n      {/* Efeitos visuais de fundo */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-primary opacity-10 rounded-full blur-3xl\" />\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-accent opacity-10 rounded-full blur-3xl\" />\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"relative z-10 w-full max-w-md\">\n        {/* Header da página */}\n        <div className=\"text-center mb-8\">\n          <div className=\"flex items-center justify-center space-x-3 mb-4\">\n            <span className=\"text-blue-500 text-2xl\">●</span>\n            <span className=\"font-bold text-3xl text-text-primary\">SHACK</span>\n            <span className=\"text-blue-400 text-2xl\">WEB</span>\n            <span className=\"text-blue-300 text-2xl\">●</span>\n          </div>\n          <p className=\"text-text-secondary text-sm\">\n            Versão React - Sistema de Hacking\n          </p>\n        </div>\n\n        {/* Formulários */}\n        <div className=\"animate-fadeInUp\">\n          {isLoginMode ? (\n            <LoginForm onSwitchToRegister={() => setIsLoginMode(false)} />\n          ) : (\n            <RegisterForm onSwitchToLogin={() => setIsLoginMode(true)} />\n          )}\n        </div>\n\n        {/* Footer */}\n        <div className=\"text-center mt-8 text-text-muted text-xs\">\n          <p>© 2024 SHACK Web Game - Todos os direitos reservados</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,8BAA8B;AACpD,OAAOC,YAAY,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,IAAIS,eAAe,IAAIC,IAAI,EAAE;IAC3B,oBAAOJ,OAAA,CAACL,QAAQ;MAACY,EAAE,EAAC,OAAO;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxC;EAEA,oBACEZ,OAAA;IAAKa,SAAS,EAAC,mDAAmD;IAAAC,QAAA,gBAEhEd,OAAA;MAAKa,SAAS,EAAC;IAAmF;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrGZ,OAAA;MAAKa,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/Cd,OAAA;QAAKa,SAAS,EAAC;MAAkF;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpGZ,OAAA;QAAKa,SAAS,EAAC;MAAmF;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClG,CAAC,eAGNZ,OAAA;MAAKa,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAE5Cd,OAAA;QAAKa,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/Bd,OAAA;UAAKa,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9Dd,OAAA;YAAMa,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjDZ,OAAA;YAAMa,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnEZ,OAAA;YAAMa,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDZ,OAAA;YAAMa,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNZ,OAAA;UAAGa,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAE3C;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNZ,OAAA;QAAKa,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAC9BT,WAAW,gBACVL,OAAA,CAACH,SAAS;UAACkB,kBAAkB,EAAEA,CAAA,KAAMT,cAAc,CAAC,KAAK;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE9DZ,OAAA,CAACF,YAAY;UAACkB,eAAe,EAAEA,CAAA,KAAMV,cAAc,CAAC,IAAI;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC7D;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNZ,OAAA;QAAKa,SAAS,EAAC,0CAA0C;QAAAC,QAAA,eACvDd,OAAA;UAAAc,QAAA,EAAG;QAAoD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CAnDID,SAAmB;EAAA,QACWL,OAAO;AAAA;AAAAqB,EAAA,GADrChB,SAAmB;AAqDzB,eAAeA,SAAS;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}