{"ast": null, "code": "import axios from 'axios';\n\n// Configuração do cliente HTTP para o backend Flask\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Interceptor para adicionar token de autenticação\napiClient.interceptors.request.use(config => {\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Interceptor para tratar respostas\napiClient.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expirado ou inválido\n    localStorage.removeItem('auth_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport class BackendService {\n  constructor() {\n    this.isBackendAvailable = true;\n  }\n  // Verificar se o backend está disponível\n  async checkBackendHealth() {\n    try {\n      const response = await fetch(`${API_BASE_URL}/`, {\n        method: 'GET',\n        timeout: 5000\n      });\n      return response.ok;\n    } catch (error) {\n      console.warn('Backend não disponível, usando dados mock');\n      this.isBackendAvailable = false;\n      return false;\n    }\n  }\n\n  // ==================== AUTENTICAÇÃO ====================\n\n  async login(email, password) {\n    try {\n      // Verificar se backend está disponível\n      const backendOk = await this.checkBackendHealth();\n      if (!backendOk) {\n        // Fallback: usar dados mock\n        return this.mockLogin(email, password);\n      }\n      const response = await apiClient.post('/api/auth/login', {\n        email,\n        password\n      });\n      if (response.data.sucesso && response.data.token) {\n        const token = response.data.token;\n\n        // Salvar token para próximas requisições\n        localStorage.setItem('auth_token', token);\n\n        // Buscar dados do jogador após login\n        const playerResult = await this.getPlayer();\n        if (playerResult.success && playerResult.player) {\n          return {\n            success: true,\n            token,\n            player: playerResult.player\n          };\n        }\n      }\n      return {\n        success: false,\n        error: response.data.mensagem || 'Erro no login'\n      };\n    } catch (error) {\n      console.warn('Erro no backend, usando fallback mock');\n      return this.mockLogin(email, password);\n    }\n  }\n  async register(nick, email, password) {\n    try {\n      const response = await apiClient.post('/api/auth/register', {\n        nick,\n        email,\n        password\n      });\n      if (response.data.sucesso && response.data.token) {\n        const token = response.data.token;\n\n        // Salvar token para próximas requisições\n        localStorage.setItem('auth_token', token);\n\n        // Buscar dados do jogador após registro\n        const playerResult = await this.getPlayer();\n        if (playerResult.success && playerResult.player) {\n          return {\n            success: true,\n            token,\n            player: playerResult.player\n          };\n        }\n      }\n      return {\n        success: false,\n        error: response.data.mensagem || 'Erro no registro'\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.mensagem) || 'Erro de conexão'\n      };\n    }\n  }\n\n  // ==================== JOGADOR ====================\n\n  async getPlayer() {\n    try {\n      const response = await apiClient.get('/api/jogador');\n      if (response.data.sucesso) {\n        return {\n          success: true,\n          player: response.data.jogador\n        };\n      }\n      return {\n        success: false,\n        error: response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      return {\n        success: false,\n        error: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.mensagem) || 'Erro ao carregar jogador'\n      };\n    }\n  }\n\n  // ==================== UPGRADES ====================\n\n  async upgradeApp(appName, quantity = 1) {\n    try {\n      // Mapear nomes dos apps do React para o backend\n      const appMapping = {\n        antivirus: 'antivirus',\n        bankguard: 'bankguard',\n        bruteforce: 'bruteforce',\n        sdk: 'cpu',\n        // SDK no React = CPU no backend\n        firewall: 'firewall',\n        malwarekit: 'malware_kit',\n        proxyvpn: 'proxyvpn',\n        mineradora: 'mineradora' // Novo app\n      };\n      const backendAppName = appMapping[appName] || appName;\n      const response = await apiClient.post('/api/appstore/comprar', {\n        item: backendAppName,\n        quantidade: quantity\n      });\n      return {\n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      return {\n        success: false,\n        error: ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.mensagem) || 'Erro ao fazer upgrade'\n      };\n    }\n  }\n\n  // ==================== SCANNER ====================\n\n  async scanTargets() {\n    try {\n      const response = await apiClient.get('/api/scan');\n      return {\n        success: response.data.sucesso,\n        targets: response.data.alvos || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      return {\n        success: false,\n        error: ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.mensagem) || 'Erro ao escanear'\n      };\n    }\n  }\n\n  // ==================== CHAT ====================\n\n  async getChatMessages() {\n    try {\n      const response = await apiClient.get('/api/chat/messages');\n      return {\n        success: response.data.sucesso,\n        messages: response.data.mensagens || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      return {\n        success: false,\n        error: ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.mensagem) || 'Erro ao carregar chat'\n      };\n    }\n  }\n  async sendChatMessage(message) {\n    try {\n      const response = await apiClient.post('/api/chat/send', {\n        mensagem: message\n      });\n      return {\n        success: response.data.sucesso,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      return {\n        success: false,\n        error: ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.mensagem) || 'Erro ao enviar mensagem'\n      };\n    }\n  }\n\n  // ==================== HACKING ====================\n\n  async exploitTarget(targetData) {\n    try {\n      const response = await apiClient.post('/api/transferir', targetData);\n      return {\n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      return {\n        success: false,\n        error: ((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.mensagem) || 'Erro ao exploitar alvo'\n      };\n    }\n  }\n\n  // ==================== UTILITÁRIOS ====================\n\n  async testConnection() {\n    try {\n      const response = await apiClient.get('/api/status');\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Erro de conexão com o servidor'\n      };\n    }\n  }\n\n  // Converter jogador do backend para formato do React\n  convertBackendPlayer(backendPlayer) {\n    return {\n      uid: backendPlayer.uid,\n      nick: backendPlayer.nick,\n      email: backendPlayer.email,\n      ip: backendPlayer.ip,\n      level: backendPlayer.nivel,\n      xp: backendPlayer.xp,\n      xpToNextLevel: 100,\n      // Será calculado depois\n      cash: backendPlayer.dinheiro,\n      shack: backendPlayer.shack || 0,\n      // Nova moeda\n      createdAt: backendPlayer.created_at,\n      lastLogin: backendPlayer.last_login\n    };\n  }\n\n  // Converter apps do backend para formato do React\n  convertBackendApps(backendPlayer) {\n    return {\n      antivirus: backendPlayer.antivirus || 1,\n      bankguard: backendPlayer.bankguard || 1,\n      bruteforce: backendPlayer.bruteforce || 1,\n      sdk: backendPlayer.cpu || 1,\n      // CPU no backend = SDK no React\n      firewall: backendPlayer.firewall || 1,\n      malware_kit: backendPlayer.malware_kit || 1,\n      proxyvpn: backendPlayer.proxyvpn || 1,\n      mineradora: backendPlayer.mineradora || 1 // Novo app\n    };\n  }\n  // ==================== MÉTODOS MOCK (FALLBACK) ====================\n\n  async mockLogin(email, password) {\n    // Simular delay de rede\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Dados mock do jogador\n    const mockPlayer = {\n      uid: 'test-user-123',\n      nick: 'TestPlayer',\n      email: email,\n      ip: '*************',\n      nivel: 5,\n      xp: 250,\n      dinheiro: 150,\n      antivirus: 3,\n      bankguard: 2,\n      bruteforce: 4,\n      cpu: 2,\n      firewall: 3,\n      malware_kit: 2,\n      proxyvpn: 1,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n    const token = 'mock-token-' + Date.now();\n    localStorage.setItem('auth_token', token);\n    return {\n      success: true,\n      token,\n      player: mockPlayer\n    };\n  }\n  async mockRegister(nick, email, password) {\n    // Simular delay de rede\n    await new Promise(resolve => setTimeout(resolve, 1500));\n\n    // Dados mock do novo jogador\n    const mockPlayer = {\n      uid: 'new-user-' + Date.now(),\n      nick: nick,\n      email: email,\n      ip: '192.168.1.' + Math.floor(Math.random() * 255),\n      nivel: 1,\n      xp: 0,\n      dinheiro: 10,\n      antivirus: 1,\n      bankguard: 1,\n      bruteforce: 1,\n      cpu: 1,\n      firewall: 1,\n      malware_kit: 1,\n      proxyvpn: 1,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n    const token = 'mock-token-' + Date.now();\n    localStorage.setItem('auth_token', token);\n    return {\n      success: true,\n      token,\n      player: mockPlayer\n    };\n  }\n}\nexport const backendService = new BackendService();", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "apiClient", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "response", "error", "_error$response", "status", "removeItem", "window", "location", "href", "Promise", "reject", "BackendService", "constructor", "isBackendAvailable", "checkBackendHealth", "fetch", "method", "ok", "console", "warn", "login", "email", "password", "backendOk", "mockLogin", "post", "data", "sucesso", "setItem", "player<PERSON><PERSON><PERSON>", "getPlayer", "success", "player", "mensagem", "register", "nick", "_error$response2", "_error$response2$data", "get", "jogador", "_error$response3", "_error$response3$data", "upgradeApp", "appName", "quantity", "appMapping", "antivirus", "bankguard", "bruteforce", "sdk", "firewall", "malwarekit", "proxyvpn", "mineradora", "backendAppName", "item", "quantidade", "undefined", "_error$response4", "_error$response4$data", "scanTargets", "targets", "alvos", "_error$response5", "_error$response5$data", "getChatMessages", "messages", "mensagens", "_error$response6", "_error$response6$data", "sendChatMessage", "message", "_error$response7", "_error$response7$data", "exploitTarget", "targetData", "_error$response8", "_error$response8$data", "testConnection", "convertBackendPlayer", "backendPlayer", "uid", "ip", "level", "nivel", "xp", "xpToNextLevel", "cash", "<PERSON><PERSON><PERSON>", "shack", "createdAt", "created_at", "lastLogin", "last_login", "convertBackendApps", "cpu", "malware_kit", "resolve", "setTimeout", "mockPlayer", "Date", "toISOString", "now", "mockRegister", "Math", "floor", "random", "backendService"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/backendService.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Configuração do cliente HTTP para o backend Flask\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Interceptor para adicionar token de autenticação\napiClient.interceptors.request.use((config) => {\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Interceptor para tratar respostas\napiClient.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expirado ou inválido\n      localStorage.removeItem('auth_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport interface BackendPlayer {\n  uid: string;\n  nick: string;\n  email: string;\n  ip: string;\n  nivel: number;\n  xp: number;\n  dinheiro: number;\n  // Apps\n  cpu: number;\n  firewall: number;\n  antivirus: number;\n  malware_kit: number;\n  bruteforce: number;\n  bankguard: number;\n  proxyvpn: number;\n  // Timestamps\n  created_at: string;\n  last_login: string;\n}\n\nexport interface UpgradeResponse {\n  sucesso: boolean;\n  mensagem: string;\n  nivel_novo?: number;\n  custo_dinheiro?: number;\n  custo_shacks?: number;\n  xp_ganho?: number;\n  nivel_jogador?: number;\n  level_up?: boolean;\n}\n\nexport class BackendService {\n  private isBackendAvailable = true;\n\n  // Verificar se o backend está disponível\n  private async checkBackendHealth(): Promise<boolean> {\n    try {\n      const response = await fetch(`${API_BASE_URL}/`, {\n        method: 'GET',\n        timeout: 5000\n      } as any);\n      return response.ok;\n    } catch (error) {\n      console.warn('Backend não disponível, usando dados mock');\n      this.isBackendAvailable = false;\n      return false;\n    }\n  }\n\n  // ==================== AUTENTICAÇÃO ====================\n  \n  async login(email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {\n    try {\n      // Verificar se backend está disponível\n      const backendOk = await this.checkBackendHealth();\n\n      if (!backendOk) {\n        // Fallback: usar dados mock\n        return this.mockLogin(email, password);\n      }\n\n      const response = await apiClient.post('/api/auth/login', {\n        email,\n        password,\n      });\n\n      if (response.data.sucesso && response.data.token) {\n        const token = response.data.token;\n\n        // Salvar token para próximas requisições\n        localStorage.setItem('auth_token', token);\n\n        // Buscar dados do jogador após login\n        const playerResult = await this.getPlayer();\n\n        if (playerResult.success && playerResult.player) {\n          return {\n            success: true,\n            token,\n            player: playerResult.player\n          };\n        }\n      }\n\n      return { success: false, error: response.data.mensagem || 'Erro no login' };\n    } catch (error: any) {\n      console.warn('Erro no backend, usando fallback mock');\n      return this.mockLogin(email, password);\n    }\n  }\n\n  async register(nick: string, email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {\n    try {\n      const response = await apiClient.post('/api/auth/register', {\n        nick,\n        email,\n        password,\n      });\n\n      if (response.data.sucesso && response.data.token) {\n        const token = response.data.token;\n\n        // Salvar token para próximas requisições\n        localStorage.setItem('auth_token', token);\n\n        // Buscar dados do jogador após registro\n        const playerResult = await this.getPlayer();\n\n        if (playerResult.success && playerResult.player) {\n          return {\n            success: true,\n            token,\n            player: playerResult.player\n          };\n        }\n      }\n\n      return {\n        success: false,\n        error: response.data.mensagem || 'Erro no registro'\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro de conexão' };\n    }\n  }\n\n  // ==================== JOGADOR ====================\n\n  async getPlayer(): Promise<{ success: boolean; player?: BackendPlayer; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/jogador');\n      \n      if (response.data.sucesso) {\n        return { success: true, player: response.data.jogador };\n      }\n\n      return { success: false, error: response.data.mensagem };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao carregar jogador' };\n    }\n  }\n\n  // ==================== UPGRADES ====================\n\n  async upgradeApp(appName: string, quantity: number = 1): Promise<{ success: boolean; data?: UpgradeResponse; error?: string }> {\n    try {\n      // Mapear nomes dos apps do React para o backend\n      const appMapping: { [key: string]: string } = {\n        antivirus: 'antivirus',\n        bankguard: 'bankguard',\n        bruteforce: 'bruteforce',\n        sdk: 'cpu', // SDK no React = CPU no backend\n        firewall: 'firewall',\n        malwarekit: 'malware_kit',\n        proxyvpn: 'proxyvpn',\n        mineradora: 'mineradora', // Novo app\n      };\n\n      const backendAppName = appMapping[appName] || appName;\n\n      const response = await apiClient.post('/api/appstore/comprar', {\n        item: backendAppName,\n        quantidade: quantity,\n      });\n\n      return { \n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao fazer upgrade' };\n    }\n  }\n\n  // ==================== SCANNER ====================\n\n  async scanTargets(): Promise<{ success: boolean; targets?: any[]; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/scan');\n      \n      return { \n        success: response.data.sucesso,\n        targets: response.data.alvos || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao escanear' };\n    }\n  }\n\n  // ==================== CHAT ====================\n\n  async getChatMessages(): Promise<{ success: boolean; messages?: any[]; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/chat/messages');\n      \n      return { \n        success: response.data.sucesso,\n        messages: response.data.mensagens || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao carregar chat' };\n    }\n  }\n\n  async sendChatMessage(message: string): Promise<{ success: boolean; error?: string }> {\n    try {\n      const response = await apiClient.post('/api/chat/send', {\n        mensagem: message,\n      });\n\n      return { \n        success: response.data.sucesso,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao enviar mensagem' };\n    }\n  }\n\n  // ==================== HACKING ====================\n\n  async exploitTarget(targetData: any): Promise<{ success: boolean; data?: any; error?: string }> {\n    try {\n      const response = await apiClient.post('/api/transferir', targetData);\n      \n      return { \n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao exploitar alvo' };\n    }\n  }\n\n  // ==================== UTILITÁRIOS ====================\n\n  async testConnection(): Promise<{ success: boolean; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/status');\n      return { success: true };\n    } catch (error: any) {\n      return { success: false, error: 'Erro de conexão com o servidor' };\n    }\n  }\n\n  // Converter jogador do backend para formato do React\n  convertBackendPlayer(backendPlayer: BackendPlayer): any {\n    return {\n      uid: backendPlayer.uid,\n      nick: backendPlayer.nick,\n      email: backendPlayer.email,\n      ip: backendPlayer.ip,\n      level: backendPlayer.nivel,\n      xp: backendPlayer.xp,\n      xpToNextLevel: 100, // Será calculado depois\n      cash: backendPlayer.dinheiro,\n      shack: (backendPlayer as any).shack || 0, // Nova moeda\n      createdAt: backendPlayer.created_at,\n      lastLogin: backendPlayer.last_login,\n    };\n  }\n\n  // Converter apps do backend para formato do React\n  convertBackendApps(backendPlayer: BackendPlayer): any {\n    return {\n      antivirus: backendPlayer.antivirus || 1,\n      bankguard: backendPlayer.bankguard || 1,\n      bruteforce: backendPlayer.bruteforce || 1,\n      sdk: backendPlayer.cpu || 1, // CPU no backend = SDK no React\n      firewall: backendPlayer.firewall || 1,\n      malware_kit: backendPlayer.malware_kit || 1,\n      proxyvpn: backendPlayer.proxyvpn || 1,\n      mineradora: (backendPlayer as any).mineradora || 1, // Novo app\n    };\n  }\n  // ==================== MÉTODOS MOCK (FALLBACK) ====================\n\n  private async mockLogin(email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {\n    // Simular delay de rede\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Dados mock do jogador\n    const mockPlayer: BackendPlayer = {\n      uid: 'test-user-123',\n      nick: 'TestPlayer',\n      email: email,\n      ip: '*************',\n      nivel: 5,\n      xp: 250,\n      dinheiro: 150,\n      antivirus: 3,\n      bankguard: 2,\n      bruteforce: 4,\n      cpu: 2,\n      firewall: 3,\n      malware_kit: 2,\n      proxyvpn: 1,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString(),\n    };\n\n    const token = 'mock-token-' + Date.now();\n    localStorage.setItem('auth_token', token);\n\n    return {\n      success: true,\n      token,\n      player: mockPlayer\n    };\n  }\n\n  private async mockRegister(nick: string, email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {\n    // Simular delay de rede\n    await new Promise(resolve => setTimeout(resolve, 1500));\n\n    // Dados mock do novo jogador\n    const mockPlayer: BackendPlayer = {\n      uid: 'new-user-' + Date.now(),\n      nick: nick,\n      email: email,\n      ip: '192.168.1.' + Math.floor(Math.random() * 255),\n      nivel: 1,\n      xp: 0,\n      dinheiro: 10,\n      antivirus: 1,\n      bankguard: 1,\n      bruteforce: 1,\n      cpu: 1,\n      firewall: 1,\n      malware_kit: 1,\n      proxyvpn: 1,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString(),\n    };\n\n    const token = 'mock-token-' + Date.now();\n    localStorage.setItem('auth_token', token);\n\n    return {\n      success: true,\n      token,\n      player: mockPlayer\n    };\n  }\n}\n\nexport const backendService = new BackendService();\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,SAAS,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC7BC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,SAAS,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EAC7C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;;AAEF;AACAR,SAAS,CAACK,YAAY,CAACQ,QAAQ,CAACN,GAAG,CAChCM,QAAQ,IAAKA,QAAQ,EACrBC,KAAK,IAAK;EAAA,IAAAC,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAD,KAAK,CAACD,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAN,YAAY,CAACO,UAAU,CAAC,YAAY,CAAC;IACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOC,OAAO,CAACC,MAAM,CAACR,KAAK,CAAC;AAC9B,CACF,CAAC;AAkCD,OAAO,MAAMS,cAAc,CAAC;EAAAC,YAAA;IAAA,KAClBC,kBAAkB,GAAG,IAAI;EAAA;EAEjC;EACA,MAAcC,kBAAkBA,CAAA,EAAqB;IACnD,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMc,KAAK,CAAC,GAAG/B,YAAY,GAAG,EAAE;QAC/CgC,MAAM,EAAE,KAAK;QACbzB,OAAO,EAAE;MACX,CAAQ,CAAC;MACT,OAAOU,QAAQ,CAACgB,EAAE;IACpB,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdgB,OAAO,CAACC,IAAI,CAAC,2CAA2C,CAAC;MACzD,IAAI,CAACN,kBAAkB,GAAG,KAAK;MAC/B,OAAO,KAAK;IACd;EACF;;EAEA;;EAEA,MAAMO,KAAKA,CAACC,KAAa,EAAEC,QAAgB,EAAyF;IAClI,IAAI;MACF;MACA,MAAMC,SAAS,GAAG,MAAM,IAAI,CAACT,kBAAkB,CAAC,CAAC;MAEjD,IAAI,CAACS,SAAS,EAAE;QACd;QACA,OAAO,IAAI,CAACC,SAAS,CAACH,KAAK,EAAEC,QAAQ,CAAC;MACxC;MAEA,MAAMrB,QAAQ,GAAG,MAAMb,SAAS,CAACqC,IAAI,CAAC,iBAAiB,EAAE;QACvDJ,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,IAAIrB,QAAQ,CAACyB,IAAI,CAACC,OAAO,IAAI1B,QAAQ,CAACyB,IAAI,CAAC7B,KAAK,EAAE;QAChD,MAAMA,KAAK,GAAGI,QAAQ,CAACyB,IAAI,CAAC7B,KAAK;;QAEjC;QACAC,YAAY,CAAC8B,OAAO,CAAC,YAAY,EAAE/B,KAAK,CAAC;;QAEzC;QACA,MAAMgC,YAAY,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC,CAAC;QAE3C,IAAID,YAAY,CAACE,OAAO,IAAIF,YAAY,CAACG,MAAM,EAAE;UAC/C,OAAO;YACLD,OAAO,EAAE,IAAI;YACblC,KAAK;YACLmC,MAAM,EAAEH,YAAY,CAACG;UACvB,CAAC;QACH;MACF;MAEA,OAAO;QAAED,OAAO,EAAE,KAAK;QAAE7B,KAAK,EAAED,QAAQ,CAACyB,IAAI,CAACO,QAAQ,IAAI;MAAgB,CAAC;IAC7E,CAAC,CAAC,OAAO/B,KAAU,EAAE;MACnBgB,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;MACrD,OAAO,IAAI,CAACK,SAAS,CAACH,KAAK,EAAEC,QAAQ,CAAC;IACxC;EACF;EAEA,MAAMY,QAAQA,CAACC,IAAY,EAAEd,KAAa,EAAEC,QAAgB,EAAyF;IACnJ,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMb,SAAS,CAACqC,IAAI,CAAC,oBAAoB,EAAE;QAC1DU,IAAI;QACJd,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,IAAIrB,QAAQ,CAACyB,IAAI,CAACC,OAAO,IAAI1B,QAAQ,CAACyB,IAAI,CAAC7B,KAAK,EAAE;QAChD,MAAMA,KAAK,GAAGI,QAAQ,CAACyB,IAAI,CAAC7B,KAAK;;QAEjC;QACAC,YAAY,CAAC8B,OAAO,CAAC,YAAY,EAAE/B,KAAK,CAAC;;QAEzC;QACA,MAAMgC,YAAY,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC,CAAC;QAE3C,IAAID,YAAY,CAACE,OAAO,IAAIF,YAAY,CAACG,MAAM,EAAE;UAC/C,OAAO;YACLD,OAAO,EAAE,IAAI;YACblC,KAAK;YACLmC,MAAM,EAAEH,YAAY,CAACG;UACvB,CAAC;QACH;MACF;MAEA,OAAO;QACLD,OAAO,EAAE,KAAK;QACd7B,KAAK,EAAED,QAAQ,CAACyB,IAAI,CAACO,QAAQ,IAAI;MACnC,CAAC;IACH,CAAC,CAAC,OAAO/B,KAAU,EAAE;MAAA,IAAAkC,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEN,OAAO,EAAE,KAAK;QAAE7B,KAAK,EAAE,EAAAkC,gBAAA,GAAAlC,KAAK,CAACD,QAAQ,cAAAmC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,uBAApBA,qBAAA,CAAsBJ,QAAQ,KAAI;MAAkB,CAAC;IACvF;EACF;;EAEA;;EAEA,MAAMH,SAASA,CAAA,EAA0E;IACvF,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMb,SAAS,CAACkD,GAAG,CAAC,cAAc,CAAC;MAEpD,IAAIrC,QAAQ,CAACyB,IAAI,CAACC,OAAO,EAAE;QACzB,OAAO;UAAEI,OAAO,EAAE,IAAI;UAAEC,MAAM,EAAE/B,QAAQ,CAACyB,IAAI,CAACa;QAAQ,CAAC;MACzD;MAEA,OAAO;QAAER,OAAO,EAAE,KAAK;QAAE7B,KAAK,EAAED,QAAQ,CAACyB,IAAI,CAACO;MAAS,CAAC;IAC1D,CAAC,CAAC,OAAO/B,KAAU,EAAE;MAAA,IAAAsC,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEV,OAAO,EAAE,KAAK;QAAE7B,KAAK,EAAE,EAAAsC,gBAAA,GAAAtC,KAAK,CAACD,QAAQ,cAAAuC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBR,QAAQ,KAAI;MAA2B,CAAC;IAChG;EACF;;EAEA;;EAEA,MAAMS,UAAUA,CAACC,OAAe,EAAEC,QAAgB,GAAG,CAAC,EAAyE;IAC7H,IAAI;MACF;MACA,MAAMC,UAAqC,GAAG;QAC5CC,SAAS,EAAE,WAAW;QACtBC,SAAS,EAAE,WAAW;QACtBC,UAAU,EAAE,YAAY;QACxBC,GAAG,EAAE,KAAK;QAAE;QACZC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,aAAa;QACzBC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,YAAY,CAAE;MAC5B,CAAC;MAED,MAAMC,cAAc,GAAGT,UAAU,CAACF,OAAO,CAAC,IAAIA,OAAO;MAErD,MAAM1C,QAAQ,GAAG,MAAMb,SAAS,CAACqC,IAAI,CAAC,uBAAuB,EAAE;QAC7D8B,IAAI,EAAED,cAAc;QACpBE,UAAU,EAAEZ;MACd,CAAC,CAAC;MAEF,OAAO;QACLb,OAAO,EAAE9B,QAAQ,CAACyB,IAAI,CAACC,OAAO;QAC9BD,IAAI,EAAEzB,QAAQ,CAACyB,IAAI;QACnBxB,KAAK,EAAED,QAAQ,CAACyB,IAAI,CAACC,OAAO,GAAG8B,SAAS,GAAGxD,QAAQ,CAACyB,IAAI,CAACO;MAC3D,CAAC;IACH,CAAC,CAAC,OAAO/B,KAAU,EAAE;MAAA,IAAAwD,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAE5B,OAAO,EAAE,KAAK;QAAE7B,KAAK,EAAE,EAAAwD,gBAAA,GAAAxD,KAAK,CAACD,QAAQ,cAAAyD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsB1B,QAAQ,KAAI;MAAwB,CAAC;IAC7F;EACF;;EAEA;;EAEA,MAAM2B,WAAWA,CAAA,EAAmE;IAClF,IAAI;MACF,MAAM3D,QAAQ,GAAG,MAAMb,SAAS,CAACkD,GAAG,CAAC,WAAW,CAAC;MAEjD,OAAO;QACLP,OAAO,EAAE9B,QAAQ,CAACyB,IAAI,CAACC,OAAO;QAC9BkC,OAAO,EAAE5D,QAAQ,CAACyB,IAAI,CAACoC,KAAK,IAAI,EAAE;QAClC5D,KAAK,EAAED,QAAQ,CAACyB,IAAI,CAACC,OAAO,GAAG8B,SAAS,GAAGxD,QAAQ,CAACyB,IAAI,CAACO;MAC3D,CAAC;IACH,CAAC,CAAC,OAAO/B,KAAU,EAAE;MAAA,IAAA6D,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEjC,OAAO,EAAE,KAAK;QAAE7B,KAAK,EAAE,EAAA6D,gBAAA,GAAA7D,KAAK,CAACD,QAAQ,cAAA8D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrC,IAAI,cAAAsC,qBAAA,uBAApBA,qBAAA,CAAsB/B,QAAQ,KAAI;MAAmB,CAAC;IACxF;EACF;;EAEA;;EAEA,MAAMgC,eAAeA,CAAA,EAAoE;IACvF,IAAI;MACF,MAAMhE,QAAQ,GAAG,MAAMb,SAAS,CAACkD,GAAG,CAAC,oBAAoB,CAAC;MAE1D,OAAO;QACLP,OAAO,EAAE9B,QAAQ,CAACyB,IAAI,CAACC,OAAO;QAC9BuC,QAAQ,EAAEjE,QAAQ,CAACyB,IAAI,CAACyC,SAAS,IAAI,EAAE;QACvCjE,KAAK,EAAED,QAAQ,CAACyB,IAAI,CAACC,OAAO,GAAG8B,SAAS,GAAGxD,QAAQ,CAACyB,IAAI,CAACO;MAC3D,CAAC;IACH,CAAC,CAAC,OAAO/B,KAAU,EAAE;MAAA,IAAAkE,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEtC,OAAO,EAAE,KAAK;QAAE7B,KAAK,EAAE,EAAAkE,gBAAA,GAAAlE,KAAK,CAACD,QAAQ,cAAAmE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1C,IAAI,cAAA2C,qBAAA,uBAApBA,qBAAA,CAAsBpC,QAAQ,KAAI;MAAwB,CAAC;IAC7F;EACF;EAEA,MAAMqC,eAAeA,CAACC,OAAe,EAAiD;IACpF,IAAI;MACF,MAAMtE,QAAQ,GAAG,MAAMb,SAAS,CAACqC,IAAI,CAAC,gBAAgB,EAAE;QACtDQ,QAAQ,EAAEsC;MACZ,CAAC,CAAC;MAEF,OAAO;QACLxC,OAAO,EAAE9B,QAAQ,CAACyB,IAAI,CAACC,OAAO;QAC9BzB,KAAK,EAAED,QAAQ,CAACyB,IAAI,CAACC,OAAO,GAAG8B,SAAS,GAAGxD,QAAQ,CAACyB,IAAI,CAACO;MAC3D,CAAC;IACH,CAAC,CAAC,OAAO/B,KAAU,EAAE;MAAA,IAAAsE,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAE1C,OAAO,EAAE,KAAK;QAAE7B,KAAK,EAAE,EAAAsE,gBAAA,GAAAtE,KAAK,CAACD,QAAQ,cAAAuE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9C,IAAI,cAAA+C,qBAAA,uBAApBA,qBAAA,CAAsBxC,QAAQ,KAAI;MAA0B,CAAC;IAC/F;EACF;;EAEA;;EAEA,MAAMyC,aAAaA,CAACC,UAAe,EAA6D;IAC9F,IAAI;MACF,MAAM1E,QAAQ,GAAG,MAAMb,SAAS,CAACqC,IAAI,CAAC,iBAAiB,EAAEkD,UAAU,CAAC;MAEpE,OAAO;QACL5C,OAAO,EAAE9B,QAAQ,CAACyB,IAAI,CAACC,OAAO;QAC9BD,IAAI,EAAEzB,QAAQ,CAACyB,IAAI;QACnBxB,KAAK,EAAED,QAAQ,CAACyB,IAAI,CAACC,OAAO,GAAG8B,SAAS,GAAGxD,QAAQ,CAACyB,IAAI,CAACO;MAC3D,CAAC;IACH,CAAC,CAAC,OAAO/B,KAAU,EAAE;MAAA,IAAA0E,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAE9C,OAAO,EAAE,KAAK;QAAE7B,KAAK,EAAE,EAAA0E,gBAAA,GAAA1E,KAAK,CAACD,QAAQ,cAAA2E,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlD,IAAI,cAAAmD,qBAAA,uBAApBA,qBAAA,CAAsB5C,QAAQ,KAAI;MAAyB,CAAC;IAC9F;EACF;;EAEA;;EAEA,MAAM6C,cAAcA,CAAA,EAAkD;IACpE,IAAI;MACF,MAAM7E,QAAQ,GAAG,MAAMb,SAAS,CAACkD,GAAG,CAAC,aAAa,CAAC;MACnD,OAAO;QAAEP,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAO7B,KAAU,EAAE;MACnB,OAAO;QAAE6B,OAAO,EAAE,KAAK;QAAE7B,KAAK,EAAE;MAAiC,CAAC;IACpE;EACF;;EAEA;EACA6E,oBAAoBA,CAACC,aAA4B,EAAO;IACtD,OAAO;MACLC,GAAG,EAAED,aAAa,CAACC,GAAG;MACtB9C,IAAI,EAAE6C,aAAa,CAAC7C,IAAI;MACxBd,KAAK,EAAE2D,aAAa,CAAC3D,KAAK;MAC1B6D,EAAE,EAAEF,aAAa,CAACE,EAAE;MACpBC,KAAK,EAAEH,aAAa,CAACI,KAAK;MAC1BC,EAAE,EAAEL,aAAa,CAACK,EAAE;MACpBC,aAAa,EAAE,GAAG;MAAE;MACpBC,IAAI,EAAEP,aAAa,CAACQ,QAAQ;MAC5BC,KAAK,EAAGT,aAAa,CAASS,KAAK,IAAI,CAAC;MAAE;MAC1CC,SAAS,EAAEV,aAAa,CAACW,UAAU;MACnCC,SAAS,EAAEZ,aAAa,CAACa;IAC3B,CAAC;EACH;;EAEA;EACAC,kBAAkBA,CAACd,aAA4B,EAAO;IACpD,OAAO;MACLlC,SAAS,EAAEkC,aAAa,CAAClC,SAAS,IAAI,CAAC;MACvCC,SAAS,EAAEiC,aAAa,CAACjC,SAAS,IAAI,CAAC;MACvCC,UAAU,EAAEgC,aAAa,CAAChC,UAAU,IAAI,CAAC;MACzCC,GAAG,EAAE+B,aAAa,CAACe,GAAG,IAAI,CAAC;MAAE;MAC7B7C,QAAQ,EAAE8B,aAAa,CAAC9B,QAAQ,IAAI,CAAC;MACrC8C,WAAW,EAAEhB,aAAa,CAACgB,WAAW,IAAI,CAAC;MAC3C5C,QAAQ,EAAE4B,aAAa,CAAC5B,QAAQ,IAAI,CAAC;MACrCC,UAAU,EAAG2B,aAAa,CAAS3B,UAAU,IAAI,CAAC,CAAE;IACtD,CAAC;EACH;EACA;;EAEA,MAAc7B,SAASA,CAACH,KAAa,EAAEC,QAAgB,EAAyF;IAC9I;IACA,MAAM,IAAIb,OAAO,CAACwF,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,MAAME,UAAyB,GAAG;MAChClB,GAAG,EAAE,eAAe;MACpB9C,IAAI,EAAE,YAAY;MAClBd,KAAK,EAAEA,KAAK;MACZ6D,EAAE,EAAE,eAAe;MACnBE,KAAK,EAAE,CAAC;MACRC,EAAE,EAAE,GAAG;MACPG,QAAQ,EAAE,GAAG;MACb1C,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACb+C,GAAG,EAAE,CAAC;MACN7C,QAAQ,EAAE,CAAC;MACX8C,WAAW,EAAE,CAAC;MACd5C,QAAQ,EAAE,CAAC;MACXuC,UAAU,EAAE,IAAIS,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCR,UAAU,EAAE,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACrC,CAAC;IAED,MAAMxG,KAAK,GAAG,aAAa,GAAGuG,IAAI,CAACE,GAAG,CAAC,CAAC;IACxCxG,YAAY,CAAC8B,OAAO,CAAC,YAAY,EAAE/B,KAAK,CAAC;IAEzC,OAAO;MACLkC,OAAO,EAAE,IAAI;MACblC,KAAK;MACLmC,MAAM,EAAEmE;IACV,CAAC;EACH;EAEA,MAAcI,YAAYA,CAACpE,IAAY,EAAEd,KAAa,EAAEC,QAAgB,EAAyF;IAC/J;IACA,MAAM,IAAIb,OAAO,CAACwF,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,MAAME,UAAyB,GAAG;MAChClB,GAAG,EAAE,WAAW,GAAGmB,IAAI,CAACE,GAAG,CAAC,CAAC;MAC7BnE,IAAI,EAAEA,IAAI;MACVd,KAAK,EAAEA,KAAK;MACZ6D,EAAE,EAAE,YAAY,GAAGsB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;MAClDtB,KAAK,EAAE,CAAC;MACRC,EAAE,EAAE,CAAC;MACLG,QAAQ,EAAE,EAAE;MACZ1C,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACb+C,GAAG,EAAE,CAAC;MACN7C,QAAQ,EAAE,CAAC;MACX8C,WAAW,EAAE,CAAC;MACd5C,QAAQ,EAAE,CAAC;MACXuC,UAAU,EAAE,IAAIS,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCR,UAAU,EAAE,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACrC,CAAC;IAED,MAAMxG,KAAK,GAAG,aAAa,GAAGuG,IAAI,CAACE,GAAG,CAAC,CAAC;IACxCxG,YAAY,CAAC8B,OAAO,CAAC,YAAY,EAAE/B,KAAK,CAAC;IAEzC,OAAO;MACLkC,OAAO,EAAE,IAAI;MACblC,KAAK;MACLmC,MAAM,EAAEmE;IACV,CAAC;EACH;AACF;AAEA,OAAO,MAAMQ,cAAc,GAAG,IAAIhG,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}