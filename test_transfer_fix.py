#!/usr/bin/env python3
"""
Script para testar as correções de transferência de dinheiro
"""

import sys
import os

# Adicionar o diretório do projeto ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game import new_models

def test_transfer_validations():
    """Testa as validações de transferência"""
    print("=== TESTE DE VALIDAÇÕES DE TRANSFERÊNCIA ===")
    
    # Teste 1: Porcentagem muito baixa
    print("\n1. Testando porcentagem muito baixa (10%)...")
    result = new_models.transferir_dinheiro_alvo("test_uid1", "test_uid2", 10)
    print(f"Resultado: {result}")
    
    # Teste 2: Porcentagem muito alta
    print("\n2. Testando porcentagem muito alta (90%)...")
    result = new_models.transferir_dinheiro_alvo("test_uid1", "test_uid2", 90)
    print(f"Resultado: {result}")
    
    # Teste 3: Porcentagem válida
    print("\n3. Testando porcentagem válida (50%)...")
    result = new_models.transferir_dinheiro_alvo("test_uid1", "test_uid2", 50)
    print(f"Resultado: {result}")

def check_negative_balances():
    """Verifica e corrige saldos negativos"""
    print("\n=== VERIFICAÇÃO DE SALDOS NEGATIVOS ===")
    
    try:
        result = new_models.verificar_e_corrigir_saldos_negativos()
        print(f"Resultado da correção: {result}")
        
        if result['sucesso']:
            print(f"✅ {result['corrigidos']} jogadores corrigidos")
            print(f"📝 {result['mensagem']}")
        else:
            print(f"❌ Erro: {result['mensagem']}")
            
    except Exception as e:
        print(f"❌ Erro ao executar correção: {str(e)}")

def main():
    """Função principal"""
    print("🔧 TESTE DE CORREÇÕES DE TRANSFERÊNCIA")
    print("=" * 50)
    
    # Verificar e corrigir saldos negativos primeiro
    check_negative_balances()
    
    # Testar validações (apenas se houver jogadores de teste)
    # test_transfer_validations()
    
    print("\n✅ Testes concluídos!")

if __name__ == "__main__":
    main()
