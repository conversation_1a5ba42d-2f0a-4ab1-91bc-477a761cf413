{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\GameMainPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\n\n// Layout principal do jogo - Estilo celular antigo\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameMainPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    loadPlayerData,\n    hasPlayerData\n  } = usePlayer();\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('GameMainPage - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 1,\n    dinheiro: 0,\n    ip: '**************'\n  };\n  const handleIconClick = route => {\n    navigate(route);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-bold\",\n              children: \"S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold\",\n              children: (user === null || user === void 0 ? void 0 : user.nick) || 'Ivo77'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400\",\n              children: [\"IP: \", playerData.ip]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-bold text-blue-400\",\n            children: [\"N\\xEDvel \", playerData.nivel]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-green-400 font-semibold\",\n            children: [\"$\", playerData.dinheiro || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-6 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-3 gap-6 max-w-2xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/scanner'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/apps'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCBB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Apps\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/upgrades'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Upgrades\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/chat'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCE7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/transfer'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Transfer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\",\n          onClick: () => handleIconClick('/game/shop'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mb-2\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Loja\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center hover-lift animate-fade-in-scale stagger-7\",\n          onClick: () => handleIconClick('/game/logs'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"blue\",\n            size: \"md\",\n            className: \"mx-auto mb-2 animate-pulse-glow\",\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center hover-lift animate-fade-in-scale stagger-8\",\n          onClick: () => handleIconClick('/game/ranking'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"yellow\",\n            size: \"md\",\n            className: \"mx-auto mb-2 animate-pulse-glow\",\n            children: \"\\uD83C\\uDFC6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Ranking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center hover-lift animate-fade-in-scale stagger-9\",\n          onClick: () => handleIconClick('/game/bank'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"green\",\n            size: \"md\",\n            className: \"mx-auto mb-2 animate-pulse-glow\",\n            children: \"\\uD83C\\uDFE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Banco\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center hover-lift animate-fade-in-scale stagger-7\",\n          onClick: () => handleIconClick('/game/mining'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"purple\",\n            size: \"md\",\n            className: \"mx-auto mb-2 animate-pulse-glow\",\n            children: \"\\u26CF\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Minera\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"neon\",\n          className: \"p-4 text-center hover-lift animate-fade-in-scale stagger-8 animate-glow\",\n          onClick: () => handleIconClick('/game/terminal'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"cyan\",\n            size: \"md\",\n            className: \"mx-auto mb-2 animate-pulse-glow\",\n            children: \"\\uD83D\\uDCBB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-cyan-200 neon-blue\",\n            children: \"Terminal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n          variant: \"glass\",\n          className: \"p-4 text-center hover-lift animate-fade-in-scale stagger-9\",\n          onClick: () => handleIconClick('/game/supporters'),\n          glow: true,\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"red\",\n            size: \"md\",\n            className: \"mx-auto mb-2 animate-pulse-glow\",\n            children: \"\\u2764\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-200\",\n            children: \"Supporters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModernCard, {\n      variant: \"glass\",\n      className: \"m-4 p-4 flex-shrink-0 animate-slide-in-right\",\n      glow: true,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-around items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow\",\n          onClick: () => handleIconClick('/game'),\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"blue\",\n            size: \"sm\",\n            className: \"animate-float\",\n            children: \"\\uD83C\\uDFE0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow\",\n          onClick: () => handleIconClick('/game/chat'),\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"green\",\n            size: \"sm\",\n            className: \"animate-float\",\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium\",\n            children: \"Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow\",\n          onClick: () => handleIconClick('/game/scanner'),\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"purple\",\n            size: \"sm\",\n            className: \"animate-float\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium\",\n            children: \"Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow\",\n          onClick: () => handleIconClick('/game/transfer'),\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"cyan\",\n            size: \"sm\",\n            className: \"animate-float\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium\",\n            children: \"Transfer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow\",\n          onClick: () => handleIconClick('/game/config'),\n          children: [/*#__PURE__*/_jsxDEV(GlowingIcon, {\n            color: \"yellow\",\n            size: \"sm\",\n            className: \"animate-float\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-medium\",\n            children: \"Config\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(GameMainPage, \"Gzh4cCW8/G5MAMmfC9xUDiJ/DA0=\", false, function () {\n  return [useNavigate, useSimpleAuth, usePlayer];\n});\n_c = GameMainPage;\nexport default GameMainPage;\nvar _c;\n$RefreshReg$(_c, \"GameMainPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useSimpleAuth", "usePlayer", "jsxDEV", "_jsxDEV", "GameMainPage", "_s", "navigate", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "<PERSON><PERSON><PERSON>", "ip", "handleIconClick", "route", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "onClick", "ModernCard", "variant", "glow", "GlowingIcon", "color", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/GameMainPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\n\n// Layout principal do jogo - Estilo celular antigo\nconst GameMainPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('GameMainPage - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 1,\n    dinheiro: 0,\n    ip: '**************'\n  };\n\n  const handleIconClick = (route: string) => {\n    navigate(route);\n  };\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-xs font-bold\">S</span>\n            </div>\n            <div>\n              <div className=\"text-lg font-bold\">{user?.nick || 'Ivo77'}</div>\n              <div className=\"text-xs text-gray-400\">IP: {playerData.ip}</div>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-lg font-bold text-blue-400\">Nível {playerData.nivel}</div>\n            <div className=\"text-sm text-green-400 font-semibold\">\n              ${playerData.dinheiro || 0}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Grid principal de ícones */}\n      <div className=\"flex-1 p-6 overflow-y-auto\">\n        <div className=\"grid grid-cols-3 gap-6 max-w-2xl mx-auto\">\n          {/* Primeira linha */}\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/scanner')}\n          >\n            <div className=\"text-3xl mb-2\">🔍</div>\n            <span className=\"text-sm font-medium text-gray-200\">Scan</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/apps')}\n          >\n            <div className=\"text-3xl mb-2\">💻</div>\n            <span className=\"text-sm font-medium text-gray-200\">Apps</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/upgrades')}\n          >\n            <div className=\"text-3xl mb-2\">⚙️</div>\n            <span className=\"text-sm font-medium text-gray-200\">Upgrades</span>\n          </div>\n\n          {/* Segunda linha */}\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/chat')}\n          >\n            <div className=\"text-3xl mb-2\">📧</div>\n            <span className=\"text-sm font-medium text-gray-200\">Chat</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/transfer')}\n          >\n            <div className=\"text-3xl mb-2\">💰</div>\n            <span className=\"text-sm font-medium text-gray-200\">Transfer</span>\n          </div>\n\n          <div\n            className=\"bg-gray-800 rounded-lg p-4 text-center border border-gray-600 hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => handleIconClick('/game/shop')}\n          >\n            <div className=\"text-3xl mb-2\">🛒</div>\n            <span className=\"text-sm font-medium text-gray-200\">Loja</span>\n          </div>\n\n          {/* Terceira linha */}\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center hover-lift animate-fade-in-scale stagger-7\"\n            onClick={() => handleIconClick('/game/logs')}\n            glow\n          >\n            <GlowingIcon color=\"blue\" size=\"md\" className=\"mx-auto mb-2 animate-pulse-glow\">\n              📊\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Logs</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center hover-lift animate-fade-in-scale stagger-8\"\n            onClick={() => handleIconClick('/game/ranking')}\n            glow\n          >\n            <GlowingIcon color=\"yellow\" size=\"md\" className=\"mx-auto mb-2 animate-pulse-glow\">\n              🏆\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Ranking</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center hover-lift animate-fade-in-scale stagger-9\"\n            onClick={() => handleIconClick('/game/bank')}\n            glow\n          >\n            <GlowingIcon color=\"green\" size=\"md\" className=\"mx-auto mb-2 animate-pulse-glow\">\n              🏦\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Banco</span>\n          </ModernCard>\n\n          {/* Quarta linha */}\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center hover-lift animate-fade-in-scale stagger-7\"\n            onClick={() => handleIconClick('/game/mining')}\n            glow\n          >\n            <GlowingIcon color=\"purple\" size=\"md\" className=\"mx-auto mb-2 animate-pulse-glow\">\n              ⛏️\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Mineração</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"neon\"\n            className=\"p-4 text-center hover-lift animate-fade-in-scale stagger-8 animate-glow\"\n            onClick={() => handleIconClick('/game/terminal')}\n            glow\n          >\n            <GlowingIcon color=\"cyan\" size=\"md\" className=\"mx-auto mb-2 animate-pulse-glow\">\n              💻\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-cyan-200 neon-blue\">Terminal</span>\n          </ModernCard>\n\n          <ModernCard\n            variant=\"glass\"\n            className=\"p-4 text-center hover-lift animate-fade-in-scale stagger-9\"\n            onClick={() => handleIconClick('/game/supporters')}\n            glow\n          >\n            <GlowingIcon color=\"red\" size=\"md\" className=\"mx-auto mb-2 animate-pulse-glow\">\n              ❤️\n            </GlowingIcon>\n            <span className=\"text-sm font-medium text-gray-200\">Supporters</span>\n          </ModernCard>\n        </div>\n      </div>\n\n      {/* Footer moderno com navegação principal */}\n      <ModernCard variant=\"glass\" className=\"m-4 p-4 flex-shrink-0 animate-slide-in-right\" glow>\n        <div className=\"flex justify-around items-center\">\n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow\"\n            onClick={() => handleIconClick('/game')}\n          >\n            <GlowingIcon color=\"blue\" size=\"sm\" className=\"animate-float\">\n              🏠\n            </GlowingIcon>\n            <span className=\"text-xs font-medium\">Home</span>\n          </button>\n          \n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow\"\n            onClick={() => handleIconClick('/game/chat')}\n          >\n            <GlowingIcon color=\"green\" size=\"sm\" className=\"animate-float\">\n              💬\n            </GlowingIcon>\n            <span className=\"text-xs font-medium\">Chat</span>\n          </button>\n\n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow\"\n            onClick={() => handleIconClick('/game/scanner')}\n          >\n            <GlowingIcon color=\"purple\" size=\"sm\" className=\"animate-float\">\n              🔍\n            </GlowingIcon>\n            <span className=\"text-xs font-medium\">Scan</span>\n          </button>\n\n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow\"\n            onClick={() => handleIconClick('/game/transfer')}\n          >\n            <GlowingIcon color=\"cyan\" size=\"sm\" className=\"animate-float\">\n              💰\n            </GlowingIcon>\n            <span className=\"text-xs font-medium\">Transfer</span>\n          </button>\n\n          <button\n            className=\"flex flex-col items-center space-y-1 text-gray-300 hover:text-white transition-all duration-300 hover:scale-110 hover-glow\"\n            onClick={() => handleIconClick('/game/config')}\n          >\n            <GlowingIcon color=\"yellow\" size=\"sm\" className=\"animate-float\">\n              ⚙️\n            </GlowingIcon>\n            <span className=\"text-xs font-medium\">Config</span>\n          </button>\n        </div>\n      </ModernCard>\n    </div>\n  );\n};\n\nexport default GameMainPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAK,CAAC,GAAGP,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEQ,aAAa;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGV,SAAS,CAAC,CAAC;EAErFH,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,aAAa,IAAI,CAACF,eAAe,EAAE;MACtCG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DH,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACC,aAAa,EAAEF,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAEpD;EACA,MAAMI,UAAU,GAAGN,aAAa,IAAI;IAClCO,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,eAAe,GAAIC,KAAa,IAAK;IACzCd,QAAQ,CAACc,KAAK,CAAC;EACjB,CAAC;EAED,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DnB,OAAA;MAAKkB,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEnB,OAAA;QAAKkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnB,OAAA;UAAKkB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CnB,OAAA;YAAKkB,SAAS,EAAC,kEAAkE;YAAAC,QAAA,eAC/EnB,OAAA;cAAMkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNvB,OAAA;YAAAmB,QAAA,gBACEnB,OAAA;cAAKkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,KAAI;YAAO;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEvB,OAAA;cAAKkB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,MAAI,EAACR,UAAU,CAACI,EAAE;YAAA;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvB,OAAA;UAAKkB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBnB,OAAA;YAAKkB,SAAS,EAAC,iCAAiC;YAAAC,QAAA,GAAC,WAAM,EAACR,UAAU,CAACE,KAAK;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/EvB,OAAA;YAAKkB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,GAAC,GACnD,EAACR,UAAU,CAACG,QAAQ,IAAI,CAAC;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCnB,OAAA;QAAKkB,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBAEvDnB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,eAAe,CAAE;UAAAG,QAAA,gBAEhDnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAGNvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAENvB,OAAA;UACEkB,SAAS,EAAC,kHAAkH;UAC5HO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAGNvB,OAAA,CAAC0B,UAAU;UACTC,OAAO,EAAC,OAAO;UACfT,SAAS,EAAC,4DAA4D;UACtEO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAC7CY,IAAI;UAAAT,QAAA,gBAEJnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAEhF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAEbvB,OAAA,CAAC0B,UAAU;UACTC,OAAO,EAAC,OAAO;UACfT,SAAS,EAAC,4DAA4D;UACtEO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,eAAe,CAAE;UAChDY,IAAI;UAAAT,QAAA,gBAEJnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,QAAQ;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAElF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eAEbvB,OAAA,CAAC0B,UAAU;UACTC,OAAO,EAAC,OAAO;UACfT,SAAS,EAAC,4DAA4D;UACtEO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAC7CY,IAAI;UAAAT,QAAA,gBAEJnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,OAAO;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAEjF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eAGbvB,OAAA,CAAC0B,UAAU;UACTC,OAAO,EAAC,OAAO;UACfT,SAAS,EAAC,4DAA4D;UACtEO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,cAAc,CAAE;UAC/CY,IAAI;UAAAT,QAAA,gBAEJnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,QAAQ;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAElF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAEbvB,OAAA,CAAC0B,UAAU;UACTC,OAAO,EAAC,MAAM;UACdT,SAAS,EAAC,yEAAyE;UACnFO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UACjDY,IAAI;UAAAT,QAAA,gBAEJnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAEhF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eAEbvB,OAAA,CAAC0B,UAAU;UACTC,OAAO,EAAC,OAAO;UACfT,SAAS,EAAC,4DAA4D;UACtEO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,kBAAkB,CAAE;UACnDY,IAAI;UAAAT,QAAA,gBAEJnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,KAAK;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAE/E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA,CAAC0B,UAAU;MAACC,OAAO,EAAC,OAAO;MAACT,SAAS,EAAC,8CAA8C;MAACU,IAAI;MAAAT,QAAA,eACvFnB,OAAA;QAAKkB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CnB,OAAA;UACEkB,SAAS,EAAC,4HAA4H;UACtIO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,OAAO,CAAE;UAAAG,QAAA,gBAExCnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,4HAA4H;UACtIO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,YAAY,CAAE;UAAAG,QAAA,gBAE7CnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,OAAO;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,4HAA4H;UACtIO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,eAAe,CAAE;UAAAG,QAAA,gBAEhDnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,QAAQ;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,4HAA4H;UACtIO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,gBAAgB,CAAE;UAAAG,QAAA,gBAEjDnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAETvB,OAAA;UACEkB,SAAS,EAAC,4HAA4H;UACtIO,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,cAAc,CAAE;UAAAG,QAAA,gBAE/CnB,OAAA,CAAC6B,WAAW;YAACC,KAAK,EAAC,QAAQ;YAACC,IAAI,EAAC,IAAI;YAACb,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdvB,OAAA;YAAMkB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACrB,EAAA,CAxOID,YAAsB;EAAA,QACTL,WAAW,EACXC,aAAa,EAC4CC,SAAS;AAAA;AAAAkC,EAAA,GAH/E/B,YAAsB;AA0O5B,eAAeA,YAAY;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}