{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\GameMainPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport GameFooter from '../components/common/GameFooter';\nimport PlayerProfile from '../components/ui/PlayerProfile';\nimport { ScanIcon, AppsIcon, ConfigIcon, ChatIcon, ShopIcon, TerminalIcon, MiningIcon, LogsIcon, RankingIcon, BankIcon, SkillsIcon, SecurityIcon } from '../components/ui/GameIcons';\n\n// Layout principal do jogo - Estilo celular antigo\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameMainPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    loadPlayerData,\n    hasPlayerData\n  } = usePlayer();\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('GameMainPage - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 1,\n    dinheiro: 0,\n    ip: '**************',\n    shack: 38\n  };\n  const handleIconClick = route => {\n    navigate(route);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-gray-700/50 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center space-x-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white font-bold text-sm\",\n              children: \"S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n              children: \"SHACK WEB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-400\",\n              children: \"Vers\\xE3o de desenvolvimento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-lg p-3 border border-gray-700/50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Chat Global\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500\",\n            children: \"|\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-blue-400 font-semibold\",\n            children: \"Ceifador:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-300\",\n            children: \"Td pobre pqp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-auto flex items-center space-x-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Online\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PlayerProfile, {\n        nick: (user === null || user === void 0 ? void 0 : user.nick) || 'zakedev',\n        ip: playerData.ip || '*************',\n        level: playerData.nivel,\n        cash: playerData.dinheiro || 3241,\n        shack: playerData.shack || 38,\n        exp: 750,\n        maxExp: 2900\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/scanner'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-blue-400 group-hover:text-blue-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(ScanIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"Scan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/apps'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-purple-400 group-hover:text-purple-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(AppsIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"Apps\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/chat'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-green-400 group-hover:text-green-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(ChatIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"Grupo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/mining'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-yellow-400 group-hover:text-yellow-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(MiningIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"Minera\\xE7\\xE3o\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/shop'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-cyan-400 group-hover:text-cyan-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(ShopIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"Mercado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/apps'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-pink-400 group-hover:text-pink-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(SkillsIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"Habilidades\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/logs'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-blue-400 group-hover:text-blue-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(LogsIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"Logs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/ranking'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-yellow-400 group-hover:text-yellow-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(RankingIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"Ranking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/bank'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-green-400 group-hover:text-green-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(BankIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"BANK\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/config'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-gray-400 group-hover:text-gray-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(ConfigIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"CONFIG\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/terminal'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-cyan-400 group-hover:text-cyan-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(TerminalIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"Terminal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\",\n            onClick: () => handleIconClick('/game/security'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 flex items-center justify-center mb-2 text-red-400 group-hover:text-red-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\",\n              children: \"Seguran\\xE7a\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(GameFooter, {\n      currentPage: \"home\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(GameMainPage, \"Gzh4cCW8/G5MAMmfC9xUDiJ/DA0=\", false, function () {\n  return [useNavigate, useSimpleAuth, usePlayer];\n});\n_c = GameMainPage;\nexport default GameMainPage;\nvar _c;\n$RefreshReg$(_c, \"GameMainPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useSimpleAuth", "usePlayer", "GameFooter", "PlayerProfile", "ScanIcon", "AppsIcon", "ConfigIcon", "ChatIcon", "ShopIcon", "TerminalIcon", "MiningIcon", "LogsIcon", "RankingIcon", "BankIcon", "SkillsIcon", "SecurityIcon", "jsxDEV", "_jsxDEV", "GameMainPage", "_s", "navigate", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "<PERSON><PERSON><PERSON>", "ip", "shack", "handleIconClick", "route", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "level", "cash", "exp", "maxExp", "onClick", "size", "currentPage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/GameMainPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport GameFooter from '../components/common/GameFooter';\nimport PlayerProfile from '../components/ui/PlayerProfile';\nimport {\n  ScanIcon,\n  AppsIcon,\n  ConfigIcon,\n  ChatIcon,\n  ShopIcon,\n  TerminalIcon,\n  MiningIcon,\n  LogsIcon,\n  RankingIcon,\n  BankIcon,\n  SkillsIcon,\n  SecurityIcon\n} from '../components/ui/GameIcons';\n\n// Layout principal do jogo - Estilo celular antigo\nconst GameMainPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('GameMainPage - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 1,\n    dinheiro: 0,\n    ip: '**************',\n    shack: 38\n  };\n\n  const handleIconClick = (route: string) => {\n    navigate(route);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white flex flex-col\">\n      {/* Header com logo */}\n      <div className=\"bg-gradient-to-r from-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-gray-700/50 p-4 flex-shrink-0\">\n        <div className=\"flex items-center justify-center space-x-2\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg\">\n              <span className=\"text-white font-bold text-sm\">S</span>\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\">\n                SHACK WEB\n              </h1>\n              <p className=\"text-xs text-gray-400\">Versão de desenvolvimento</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-4\">\n        {/* Chat Global */}\n        <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-lg p-3 border border-gray-700/50\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-xs text-gray-400\">Chat Global</span>\n            <span className=\"text-xs text-gray-500\">|</span>\n            <span className=\"text-xs text-blue-400 font-semibold\">Ceifador:</span>\n            <span className=\"text-xs text-gray-300\">Td pobre pqp</span>\n            <div className=\"ml-auto flex items-center space-x-1\">\n              <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n              <span className=\"text-xs text-gray-500\">Online</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Perfil do jogador */}\n        <PlayerProfile\n          nick={user?.nick || 'zakedev'}\n          ip={playerData.ip || '*************'}\n          level={playerData.nivel}\n          cash={playerData.dinheiro || 3241}\n          shack={playerData.shack || 38}\n          exp={750}\n          maxExp={2900}\n        />\n\n        {/* Grid de aplicativos */}\n        <div className=\"bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50\">\n          <div className=\"grid grid-cols-3 gap-4\">\n            {/* Primeira linha */}\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/scanner')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-blue-400 group-hover:text-blue-300 transition-colors\">\n                <ScanIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">Scan</span>\n            </button>\n\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/apps')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-purple-400 group-hover:text-purple-300 transition-colors\">\n                <AppsIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">Apps</span>\n            </button>\n\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/chat')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-green-400 group-hover:text-green-300 transition-colors\">\n                <ChatIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">Grupo</span>\n            </button>\n\n            {/* Segunda linha */}\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/mining')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-yellow-400 group-hover:text-yellow-300 transition-colors\">\n                <MiningIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">Mineração</span>\n            </button>\n\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/shop')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-cyan-400 group-hover:text-cyan-300 transition-colors\">\n                <ShopIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">Mercado</span>\n            </button>\n\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/apps')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-pink-400 group-hover:text-pink-300 transition-colors\">\n                <SkillsIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">Habilidades</span>\n            </button>\n\n            {/* Terceira linha */}\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/logs')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-blue-400 group-hover:text-blue-300 transition-colors\">\n                <LogsIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">Logs</span>\n            </button>\n\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/ranking')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-yellow-400 group-hover:text-yellow-300 transition-colors\">\n                <RankingIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">Ranking</span>\n            </button>\n\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/bank')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-green-400 group-hover:text-green-300 transition-colors\">\n                <BankIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">BANK</span>\n            </button>\n\n            {/* Quarta linha */}\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/config')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-gray-400 group-hover:text-gray-300 transition-colors\">\n                <ConfigIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">CONFIG</span>\n            </button>\n\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/terminal')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-cyan-400 group-hover:text-cyan-300 transition-colors\">\n                <TerminalIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">Terminal</span>\n            </button>\n\n            <button\n              className=\"flex flex-col items-center p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 hover:scale-105 group\"\n              onClick={() => handleIconClick('/game/security')}\n            >\n              <div className=\"w-12 h-12 flex items-center justify-center mb-2 text-red-400 group-hover:text-red-300 transition-colors\">\n                <SecurityIcon size={32} />\n              </div>\n              <span className=\"text-sm font-medium text-gray-200 group-hover:text-white transition-colors\">Segurança</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer com navegação */}\n      <GameFooter currentPage=\"home\" />\n    </div>\n  );\n};\n\nexport default GameMainPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SACEC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,QAAQ,EACRC,UAAU,EACVC,YAAY,QACP,4BAA4B;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsB;EAAK,CAAC,GAAGrB,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEsB,aAAa;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGxB,SAAS,CAAC,CAAC;EAErFH,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2B,aAAa,IAAI,CAACF,eAAe,EAAE;MACtCG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DH,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACC,aAAa,EAAEF,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAEpD;EACA,MAAMI,UAAU,GAAGN,aAAa,IAAI;IAClCO,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,EAAE,EAAE,gBAAgB;IACpBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,eAAe,GAAIC,KAAa,IAAK;IACzCf,QAAQ,CAACe,KAAK,CAAC;EACjB,CAAC;EAED,oBACElB,OAAA;IAAKmB,SAAS,EAAC,gGAAgG;IAAAC,QAAA,gBAE7GpB,OAAA;MAAKmB,SAAS,EAAC,iHAAiH;MAAAC,QAAA,eAC9HpB,OAAA;QAAKmB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDpB,OAAA;UAAKmB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CpB,OAAA;YAAKmB,SAAS,EAAC,6GAA6G;YAAAC,QAAA,eAC1HpB,OAAA;cAAMmB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNxB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA;cAAImB,SAAS,EAAC,8FAA8F;cAAAC,QAAA,EAAC;YAE7G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxB,OAAA;cAAGmB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxB,OAAA;MAAKmB,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBAEnDpB,OAAA;QAAKmB,SAAS,EAAC,4GAA4G;QAAAC,QAAA,eACzHpB,OAAA;UAAKmB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CpB,OAAA;YAAMmB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1DxB,OAAA;YAAMmB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDxB,OAAA;YAAMmB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtExB,OAAA;YAAMmB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DxB,OAAA;YAAKmB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBAClDpB,OAAA;cAAKmB,SAAS,EAAC;YAAiD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvExB,OAAA;cAAMmB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxB,OAAA,CAACd,aAAa;QACZuC,IAAI,EAAE,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,KAAI,SAAU;QAC9BV,EAAE,EAAEJ,UAAU,CAACI,EAAE,IAAI,eAAgB;QACrCW,KAAK,EAAEf,UAAU,CAACE,KAAM;QACxBc,IAAI,EAAEhB,UAAU,CAACG,QAAQ,IAAI,IAAK;QAClCE,KAAK,EAAEL,UAAU,CAACK,KAAK,IAAI,EAAG;QAC9BY,GAAG,EAAE,GAAI;QACTC,MAAM,EAAE;MAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAGFxB,OAAA;QAAKmB,SAAS,EAAC,6GAA6G;QAAAC,QAAA,eAC1HpB,OAAA;UAAKmB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAErCpB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,eAAe,CAAE;YAAAG,QAAA,gBAEhDpB,OAAA;cAAKmB,SAAS,EAAC,2GAA2G;cAAAC,QAAA,eACxHpB,OAAA,CAACb,QAAQ;gBAAC4C,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eAETxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,YAAY,CAAE;YAAAG,QAAA,gBAE7CpB,OAAA;cAAKmB,SAAS,EAAC,+GAA+G;cAAAC,QAAA,eAC5HpB,OAAA,CAACZ,QAAQ;gBAAC2C,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eAETxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,YAAY,CAAE;YAAAG,QAAA,gBAE7CpB,OAAA;cAAKmB,SAAS,EAAC,6GAA6G;cAAAC,QAAA,eAC1HpB,OAAA,CAACV,QAAQ;gBAACyC,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnG,CAAC,eAGTxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,cAAc,CAAE;YAAAG,QAAA,gBAE/CpB,OAAA;cAAKmB,SAAS,EAAC,+GAA+G;cAAAC,QAAA,eAC5HpB,OAAA,CAACP,UAAU;gBAACsC,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC,eAETxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,YAAY,CAAE;YAAAG,QAAA,gBAE7CpB,OAAA;cAAKmB,SAAS,EAAC,2GAA2G;cAAAC,QAAA,eACxHpB,OAAA,CAACT,QAAQ;gBAACwC,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,eAETxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,YAAY,CAAE;YAAAG,QAAA,gBAE7CpB,OAAA;cAAKmB,SAAS,EAAC,2GAA2G;cAAAC,QAAA,eACxHpB,OAAA,CAACH,UAAU;gBAACkC,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzG,CAAC,eAGTxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,YAAY,CAAE;YAAAG,QAAA,gBAE7CpB,OAAA;cAAKmB,SAAS,EAAC,2GAA2G;cAAAC,QAAA,eACxHpB,OAAA,CAACN,QAAQ;gBAACqC,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eAETxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,eAAe,CAAE;YAAAG,QAAA,gBAEhDpB,OAAA;cAAKmB,SAAS,EAAC,+GAA+G;cAAAC,QAAA,eAC5HpB,OAAA,CAACL,WAAW;gBAACoC,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,eAETxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,YAAY,CAAE;YAAAG,QAAA,gBAE7CpB,OAAA;cAAKmB,SAAS,EAAC,6GAA6G;cAAAC,QAAA,eAC1HpB,OAAA,CAACJ,QAAQ;gBAACmC,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eAGTxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,cAAc,CAAE;YAAAG,QAAA,gBAE/CpB,OAAA;cAAKmB,SAAS,EAAC,2GAA2G;cAAAC,QAAA,eACxHpB,OAAA,CAACX,UAAU;gBAAC0C,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpG,CAAC,eAETxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,gBAAgB,CAAE;YAAAG,QAAA,gBAEjDpB,OAAA;cAAKmB,SAAS,EAAC,2GAA2G;cAAAC,QAAA,eACxHpB,OAAA,CAACR,YAAY;gBAACuC,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,eAETxB,OAAA;YACEmB,SAAS,EAAC,oLAAoL;YAC9LW,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,gBAAgB,CAAE;YAAAG,QAAA,gBAEjDpB,OAAA;cAAKmB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,eACtHpB,OAAA,CAACF,YAAY;gBAACiC,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNxB,OAAA;cAAMmB,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxB,OAAA,CAACf,UAAU;MAAC+C,WAAW,EAAC;IAAM;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9B,CAAC;AAEV,CAAC;AAACtB,EAAA,CA7MID,YAAsB;EAAA,QACTnB,WAAW,EACXC,aAAa,EAC4CC,SAAS;AAAA;AAAAiD,EAAA,GAH/EhC,YAAsB;AA+M5B,eAAeA,YAAY;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}