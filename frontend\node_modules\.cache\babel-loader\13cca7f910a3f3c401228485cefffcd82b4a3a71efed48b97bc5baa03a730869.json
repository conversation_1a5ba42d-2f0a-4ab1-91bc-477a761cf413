{"ast": null, "code": "import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { GAME_CONFIG, getPlayerLevel, getXpForNextLevel, calculateUpgradeCost, calculateXpReward } from '../types/game';\nconst initialPlayerApps = {\n  ...GAME_CONFIG.INITIAL_APPS\n};\nexport const useHackGameStore = create()(persist((set, get) => ({\n  // Initial state\n  player: null,\n  playerApps: initialPlayerApps,\n  isOnline: false,\n  notifications: [],\n  availableTargets: [],\n  hackHistory: [],\n  currentScreen: 'home',\n  isLoading: false,\n  error: null,\n  // Actions\n  initializePlayer: (nick, email) => {\n    const newPlayer = {\n      uid: `player_${Date.now()}`,\n      nick,\n      email,\n      ip: generateRandomIP(),\n      level: 1,\n      xp: 0,\n      xpToNextLevel: GAME_CONFIG.BASE_XP_PER_LEVEL,\n      cash: GAME_CONFIG.INITIAL_CASH,\n      createdAt: new Date().toISOString(),\n      lastLogin: new Date().toISOString()\n    };\n    set({\n      player: newPlayer,\n      playerApps: {\n        ...GAME_CONFIG.INITIAL_APPS\n      },\n      isOnline: true,\n      notifications: [{\n        id: `notif_${Date.now()}`,\n        type: 'system',\n        title: 'Bem-vindo ao SHACK!',\n        message: `Seu IP é ${newPlayer.ip}. Comece fazendo upgrades nos seus apps!`,\n        timestamp: new Date().toISOString(),\n        read: false\n      }]\n    });\n  },\n  updatePlayerApps: apps => {\n    set(state => ({\n      playerApps: {\n        ...state.playerApps,\n        ...apps\n      }\n    }));\n  },\n  upgradeApp: appId => {\n    const state = get();\n    const {\n      player,\n      playerApps\n    } = state;\n    if (!player) return false;\n    const currentLevel = playerApps[appId];\n    const upgradeCost = calculateUpgradeCost(currentLevel);\n\n    // Verificar se tem dinheiro suficiente\n    if (player.cash < upgradeCost) {\n      set({\n        error: 'Dinheiro insuficiente para upgrade!'\n      });\n      return false;\n    }\n\n    // Verificar se não atingiu o nível máximo\n    if (currentLevel >= GAME_CONFIG.MAX_APP_LEVEL) {\n      set({\n        error: 'App já está no nível máximo!'\n      });\n      return false;\n    }\n\n    // Realizar upgrade\n    const xpReward = calculateXpReward(currentLevel);\n    const newApps = {\n      ...playerApps,\n      [appId]: currentLevel + 1\n    };\n    set(state => {\n      const newPlayer = {\n        ...state.player,\n        cash: state.player.cash - upgradeCost\n      };\n      return {\n        player: newPlayer,\n        playerApps: newApps,\n        error: null\n      };\n    });\n\n    // Adicionar XP\n    get().addXp(xpReward);\n\n    // Adicionar notificação\n    get().addNotification({\n      type: 'upgrade',\n      title: 'Upgrade Concluído!',\n      message: `${appId} foi atualizado para nível ${currentLevel + 1}. +${xpReward} XP`,\n      read: false\n    });\n    return true;\n  },\n  addXp: amount => {\n    set(state => {\n      if (!state.player) return state;\n      const newTotalXp = state.player.xp + amount;\n      const newLevel = getPlayerLevel(newTotalXp);\n      const xpToNext = getXpForNextLevel(newTotalXp);\n      const leveledUp = newLevel > state.player.level;\n      const updatedPlayer = {\n        ...state.player,\n        xp: newTotalXp,\n        level: newLevel,\n        xpToNextLevel: xpToNext\n      };\n\n      // Se subiu de nível, adicionar notificação\n      if (leveledUp) {\n        setTimeout(() => {\n          get().addNotification({\n            type: 'level',\n            title: 'Level Up!',\n            message: `Parabéns! Você atingiu o nível ${newLevel}!`,\n            read: false\n          });\n        }, 100);\n      }\n      return {\n        player: updatedPlayer\n      };\n    });\n  },\n  addCash: amount => {\n    set(state => ({\n      player: state.player ? {\n        ...state.player,\n        cash: state.player.cash + amount\n      } : null\n    }));\n  },\n  spendCash: amount => {\n    const state = get();\n    if (!state.player || state.player.cash < amount) {\n      return false;\n    }\n    set(state => ({\n      player: state.player ? {\n        ...state.player,\n        cash: state.player.cash - amount\n      } : null\n    }));\n    return true;\n  },\n  setCurrentScreen: screen => {\n    set({\n      currentScreen: screen\n    });\n  },\n  addNotification: notification => {\n    const newNotification = {\n      ...notification,\n      id: `notif_${Date.now()}_${Math.random()}`,\n      timestamp: new Date().toISOString()\n    };\n    set(state => ({\n      notifications: [newNotification, ...state.notifications].slice(0, 50) // Manter apenas 50 notificações\n    }));\n  },\n  markNotificationRead: id => {\n    set(state => ({\n      notifications: state.notifications.map(notif => notif.id === id ? {\n        ...notif,\n        read: true\n      } : notif)\n    }));\n  },\n  clearNotifications: () => {\n    set({\n      notifications: []\n    });\n  },\n  setError: error => {\n    set({\n      error\n    });\n    if (error) {\n      // Limpar erro após 5 segundos\n      setTimeout(() => {\n        set({\n          error: null\n        });\n      }, 5000);\n    }\n  },\n  setLoading: loading => {\n    set({\n      isLoading: loading\n    });\n  },\n  reset: () => {\n    set({\n      player: null,\n      playerApps: initialPlayerApps,\n      isOnline: false,\n      notifications: [],\n      availableTargets: [],\n      hackHistory: [],\n      currentScreen: 'home',\n      isLoading: false,\n      error: null\n    });\n  }\n}), {\n  name: 'shack-game-storage',\n  partialize: state => ({\n    player: state.player,\n    playerApps: state.playerApps,\n    notifications: state.notifications,\n    hackHistory: state.hackHistory\n  })\n}));", "map": {"version": 3, "names": ["create", "persist", "GAME_CONFIG", "getPlayerLevel", "getXpForNextLevel", "calculateUpgradeCost", "calculateXpReward", "initialPlayerApps", "INITIAL_APPS", "useHackGameStore", "set", "get", "player", "playerApps", "isOnline", "notifications", "availableTargets", "hack<PERSON><PERSON><PERSON>", "currentScreen", "isLoading", "error", "initializePlayer", "nick", "email", "newPlayer", "uid", "Date", "now", "ip", "generateRandomIP", "level", "xp", "xpToNextLevel", "BASE_XP_PER_LEVEL", "cash", "INITIAL_CASH", "createdAt", "toISOString", "lastLogin", "id", "type", "title", "message", "timestamp", "read", "updatePlayerApps", "apps", "state", "upgradeApp", "appId", "currentLevel", "upgradeCost", "MAX_APP_LEVEL", "xpReward", "newApps", "addXp", "addNotification", "amount", "newTotalXp", "newLevel", "xpToNext", "leveledUp", "updatedPlayer", "setTimeout", "addCash", "spendCash", "setCurrentScreen", "screen", "notification", "newNotification", "Math", "random", "slice", "markNotificationRead", "map", "notif", "clearNotifications", "setError", "setLoading", "loading", "reset", "name", "partialize"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/hackGameStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport {\n  GAME_CONFIG,\n  getPlayerLevel,\n  getXpForNextLevel,\n  calculateUpgradeCost,\n  calculateXpReward\n} from '../types/game';\nimport { gameService } from '../services/gameService';\nimport { Player, PlayerApps, GameNotification, HackTarget } from '../lib/supabase';\n\ninterface GameState {\n  // Player data\n  player: Player | null;\n  playerApps: PlayerApps;\n  \n  // Game state\n  isOnline: boolean;\n  notifications: GameNotification[];\n  \n  // Targets and hacking\n  availableTargets: HackTarget[];\n  hackHistory: any[];\n  \n  // UI state\n  currentScreen: 'home' | 'apps' | 'scanner' | 'terminal' | 'profile' | 'settings' | 'shop' | 'ranking' | 'logs' | 'chat';\n  isLoading: boolean;\n  error: string | null;\n  \n  // Actions\n  initializePlayer: (nick: string, email: string) => Promise<boolean>;\n  loadPlayer: (playerId: string) => Promise<boolean>;\n  updatePlayerApps: (apps: Partial<PlayerApps>) => Promise<boolean>;\n  upgradeApp: (appId: keyof PlayerApps) => Promise<boolean>;\n  addXp: (amount: number) => Promise<void>;\n  addCash: (amount: number) => Promise<void>;\n  spendCash: (amount: number) => Promise<boolean>;\n  setCurrentScreen: (screen: GameState['currentScreen']) => void;\n  loadNotifications: () => Promise<void>;\n  addNotification: (notification: Omit<GameNotification, 'id' | 'player_id' | 'created_at' | 'read'>) => Promise<void>;\n  markNotificationRead: (id: string) => Promise<void>;\n  clearNotifications: () => void;\n  setError: (error: string | null) => void;\n  setLoading: (loading: boolean) => void;\n  syncWithServer: () => Promise<void>;\n  reset: () => void;\n}\n\nconst initialPlayerApps: PlayerApps = { ...GAME_CONFIG.INITIAL_APPS };\n\nexport const useHackGameStore = create<GameState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      player: null,\n      playerApps: initialPlayerApps,\n      isOnline: false,\n      notifications: [],\n      availableTargets: [],\n      hackHistory: [],\n      currentScreen: 'home',\n      isLoading: false,\n      error: null,\n\n      // Actions\n      initializePlayer: (nick: string, email: string) => {\n        const newPlayer: Player = {\n          uid: `player_${Date.now()}`,\n          nick,\n          email,\n          ip: generateRandomIP(),\n          level: 1,\n          xp: 0,\n          xpToNextLevel: GAME_CONFIG.BASE_XP_PER_LEVEL,\n          cash: GAME_CONFIG.INITIAL_CASH,\n          createdAt: new Date().toISOString(),\n          lastLogin: new Date().toISOString(),\n        };\n\n        set({\n          player: newPlayer,\n          playerApps: { ...GAME_CONFIG.INITIAL_APPS },\n          isOnline: true,\n          notifications: [{\n            id: `notif_${Date.now()}`,\n            type: 'system',\n            title: 'Bem-vindo ao SHACK!',\n            message: `Seu IP é ${newPlayer.ip}. Comece fazendo upgrades nos seus apps!`,\n            timestamp: new Date().toISOString(),\n            read: false,\n          }],\n        });\n      },\n\n      updatePlayerApps: (apps: Partial<PlayerApps>) => {\n        set((state) => ({\n          playerApps: { ...state.playerApps, ...apps }\n        }));\n      },\n\n      upgradeApp: (appId: keyof PlayerApps) => {\n        const state = get();\n        const { player, playerApps } = state;\n        \n        if (!player) return false;\n\n        const currentLevel = playerApps[appId];\n        const upgradeCost = calculateUpgradeCost(currentLevel);\n        \n        // Verificar se tem dinheiro suficiente\n        if (player.cash < upgradeCost) {\n          set({ error: 'Dinheiro insuficiente para upgrade!' });\n          return false;\n        }\n\n        // Verificar se não atingiu o nível máximo\n        if (currentLevel >= GAME_CONFIG.MAX_APP_LEVEL) {\n          set({ error: 'App já está no nível máximo!' });\n          return false;\n        }\n\n        // Realizar upgrade\n        const xpReward = calculateXpReward(currentLevel);\n        const newApps = { ...playerApps, [appId]: currentLevel + 1 };\n        \n        set((state) => {\n          const newPlayer = {\n            ...state.player!,\n            cash: state.player!.cash - upgradeCost,\n          };\n          \n          return {\n            player: newPlayer,\n            playerApps: newApps,\n            error: null,\n          };\n        });\n\n        // Adicionar XP\n        get().addXp(xpReward);\n\n        // Adicionar notificação\n        get().addNotification({\n          type: 'upgrade',\n          title: 'Upgrade Concluído!',\n          message: `${appId} foi atualizado para nível ${currentLevel + 1}. +${xpReward} XP`,\n          read: false,\n        });\n\n        return true;\n      },\n\n      addXp: (amount: number) => {\n        set((state) => {\n          if (!state.player) return state;\n\n          const newTotalXp = state.player.xp + amount;\n          const newLevel = getPlayerLevel(newTotalXp);\n          const xpToNext = getXpForNextLevel(newTotalXp);\n          \n          const leveledUp = newLevel > state.player.level;\n          \n          const updatedPlayer = {\n            ...state.player,\n            xp: newTotalXp,\n            level: newLevel,\n            xpToNextLevel: xpToNext,\n          };\n\n          // Se subiu de nível, adicionar notificação\n          if (leveledUp) {\n            setTimeout(() => {\n              get().addNotification({\n                type: 'level',\n                title: 'Level Up!',\n                message: `Parabéns! Você atingiu o nível ${newLevel}!`,\n                read: false,\n              });\n            }, 100);\n          }\n\n          return { player: updatedPlayer };\n        });\n      },\n\n      addCash: (amount: number) => {\n        set((state) => ({\n          player: state.player ? {\n            ...state.player,\n            cash: state.player.cash + amount\n          } : null\n        }));\n      },\n\n      spendCash: (amount: number) => {\n        const state = get();\n        if (!state.player || state.player.cash < amount) {\n          return false;\n        }\n\n        set((state) => ({\n          player: state.player ? {\n            ...state.player,\n            cash: state.player.cash - amount\n          } : null\n        }));\n\n        return true;\n      },\n\n      setCurrentScreen: (screen: GameState['currentScreen']) => {\n        set({ currentScreen: screen });\n      },\n\n      addNotification: (notification: Omit<GameNotification, 'id' | 'timestamp'>) => {\n        const newNotification: GameNotification = {\n          ...notification,\n          id: `notif_${Date.now()}_${Math.random()}`,\n          timestamp: new Date().toISOString(),\n        };\n\n        set((state) => ({\n          notifications: [newNotification, ...state.notifications].slice(0, 50) // Manter apenas 50 notificações\n        }));\n      },\n\n      markNotificationRead: (id: string) => {\n        set((state) => ({\n          notifications: state.notifications.map(notif =>\n            notif.id === id ? { ...notif, read: true } : notif\n          )\n        }));\n      },\n\n      clearNotifications: () => {\n        set({ notifications: [] });\n      },\n\n      setError: (error: string | null) => {\n        set({ error });\n        if (error) {\n          // Limpar erro após 5 segundos\n          setTimeout(() => {\n            set({ error: null });\n          }, 5000);\n        }\n      },\n\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n\n      reset: () => {\n        set({\n          player: null,\n          playerApps: initialPlayerApps,\n          isOnline: false,\n          notifications: [],\n          availableTargets: [],\n          hackHistory: [],\n          currentScreen: 'home',\n          isLoading: false,\n          error: null,\n        });\n      },\n    }),\n    {\n      name: 'shack-game-storage',\n      partialize: (state) => ({\n        player: state.player,\n        playerApps: state.playerApps,\n        notifications: state.notifications,\n        hackHistory: state.hackHistory,\n      }),\n    }\n  )\n);\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SACEC,WAAW,EACXC,cAAc,EACdC,iBAAiB,EACjBC,oBAAoB,EACpBC,iBAAiB,QACZ,eAAe;AAyCtB,MAAMC,iBAA6B,GAAG;EAAE,GAAGL,WAAW,CAACM;AAAa,CAAC;AAErE,OAAO,MAAMC,gBAAgB,GAAGT,MAAM,CAAY,CAAC,CACjDC,OAAO,CACL,CAACS,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAEN,iBAAiB;EAC7BO,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE,EAAE;EACjBC,gBAAgB,EAAE,EAAE;EACpBC,WAAW,EAAE,EAAE;EACfC,aAAa,EAAE,MAAM;EACrBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEX;EACAC,gBAAgB,EAAEA,CAACC,IAAY,EAAEC,KAAa,KAAK;IACjD,MAAMC,SAAiB,GAAG;MACxBC,GAAG,EAAE,UAAUC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC3BL,IAAI;MACJC,KAAK;MACLK,EAAE,EAAEC,gBAAgB,CAAC,CAAC;MACtBC,KAAK,EAAE,CAAC;MACRC,EAAE,EAAE,CAAC;MACLC,aAAa,EAAE9B,WAAW,CAAC+B,iBAAiB;MAC5CC,IAAI,EAAEhC,WAAW,CAACiC,YAAY;MAC9BC,SAAS,EAAE,IAAIV,IAAI,CAAC,CAAC,CAACW,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIZ,IAAI,CAAC,CAAC,CAACW,WAAW,CAAC;IACpC,CAAC;IAED3B,GAAG,CAAC;MACFE,MAAM,EAAEY,SAAS;MACjBX,UAAU,EAAE;QAAE,GAAGX,WAAW,CAACM;MAAa,CAAC;MAC3CM,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE,CAAC;QACdwB,EAAE,EAAE,SAASb,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QACzBa,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,qBAAqB;QAC5BC,OAAO,EAAE,YAAYlB,SAAS,CAACI,EAAE,0CAA0C;QAC3Ee,SAAS,EAAE,IAAIjB,IAAI,CAAC,CAAC,CAACW,WAAW,CAAC,CAAC;QACnCO,IAAI,EAAE;MACR,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAEDC,gBAAgB,EAAGC,IAAyB,IAAK;IAC/CpC,GAAG,CAAEqC,KAAK,KAAM;MACdlC,UAAU,EAAE;QAAE,GAAGkC,KAAK,CAAClC,UAAU;QAAE,GAAGiC;MAAK;IAC7C,CAAC,CAAC,CAAC;EACL,CAAC;EAEDE,UAAU,EAAGC,KAAuB,IAAK;IACvC,MAAMF,KAAK,GAAGpC,GAAG,CAAC,CAAC;IACnB,MAAM;MAAEC,MAAM;MAAEC;IAAW,CAAC,GAAGkC,KAAK;IAEpC,IAAI,CAACnC,MAAM,EAAE,OAAO,KAAK;IAEzB,MAAMsC,YAAY,GAAGrC,UAAU,CAACoC,KAAK,CAAC;IACtC,MAAME,WAAW,GAAG9C,oBAAoB,CAAC6C,YAAY,CAAC;;IAEtD;IACA,IAAItC,MAAM,CAACsB,IAAI,GAAGiB,WAAW,EAAE;MAC7BzC,GAAG,CAAC;QAAEU,KAAK,EAAE;MAAsC,CAAC,CAAC;MACrD,OAAO,KAAK;IACd;;IAEA;IACA,IAAI8B,YAAY,IAAIhD,WAAW,CAACkD,aAAa,EAAE;MAC7C1C,GAAG,CAAC;QAAEU,KAAK,EAAE;MAA+B,CAAC,CAAC;MAC9C,OAAO,KAAK;IACd;;IAEA;IACA,MAAMiC,QAAQ,GAAG/C,iBAAiB,CAAC4C,YAAY,CAAC;IAChD,MAAMI,OAAO,GAAG;MAAE,GAAGzC,UAAU;MAAE,CAACoC,KAAK,GAAGC,YAAY,GAAG;IAAE,CAAC;IAE5DxC,GAAG,CAAEqC,KAAK,IAAK;MACb,MAAMvB,SAAS,GAAG;QAChB,GAAGuB,KAAK,CAACnC,MAAO;QAChBsB,IAAI,EAAEa,KAAK,CAACnC,MAAM,CAAEsB,IAAI,GAAGiB;MAC7B,CAAC;MAED,OAAO;QACLvC,MAAM,EAAEY,SAAS;QACjBX,UAAU,EAAEyC,OAAO;QACnBlC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC;;IAEF;IACAT,GAAG,CAAC,CAAC,CAAC4C,KAAK,CAACF,QAAQ,CAAC;;IAErB;IACA1C,GAAG,CAAC,CAAC,CAAC6C,eAAe,CAAC;MACpBhB,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,oBAAoB;MAC3BC,OAAO,EAAE,GAAGO,KAAK,8BAA8BC,YAAY,GAAG,CAAC,MAAMG,QAAQ,KAAK;MAClFT,IAAI,EAAE;IACR,CAAC,CAAC;IAEF,OAAO,IAAI;EACb,CAAC;EAEDW,KAAK,EAAGE,MAAc,IAAK;IACzB/C,GAAG,CAAEqC,KAAK,IAAK;MACb,IAAI,CAACA,KAAK,CAACnC,MAAM,EAAE,OAAOmC,KAAK;MAE/B,MAAMW,UAAU,GAAGX,KAAK,CAACnC,MAAM,CAACmB,EAAE,GAAG0B,MAAM;MAC3C,MAAME,QAAQ,GAAGxD,cAAc,CAACuD,UAAU,CAAC;MAC3C,MAAME,QAAQ,GAAGxD,iBAAiB,CAACsD,UAAU,CAAC;MAE9C,MAAMG,SAAS,GAAGF,QAAQ,GAAGZ,KAAK,CAACnC,MAAM,CAACkB,KAAK;MAE/C,MAAMgC,aAAa,GAAG;QACpB,GAAGf,KAAK,CAACnC,MAAM;QACfmB,EAAE,EAAE2B,UAAU;QACd5B,KAAK,EAAE6B,QAAQ;QACf3B,aAAa,EAAE4B;MACjB,CAAC;;MAED;MACA,IAAIC,SAAS,EAAE;QACbE,UAAU,CAAC,MAAM;UACfpD,GAAG,CAAC,CAAC,CAAC6C,eAAe,CAAC;YACpBhB,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,WAAW;YAClBC,OAAO,EAAE,kCAAkCiB,QAAQ,GAAG;YACtDf,IAAI,EAAE;UACR,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT;MAEA,OAAO;QAAEhC,MAAM,EAAEkD;MAAc,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EAEDE,OAAO,EAAGP,MAAc,IAAK;IAC3B/C,GAAG,CAAEqC,KAAK,KAAM;MACdnC,MAAM,EAAEmC,KAAK,CAACnC,MAAM,GAAG;QACrB,GAAGmC,KAAK,CAACnC,MAAM;QACfsB,IAAI,EAAEa,KAAK,CAACnC,MAAM,CAACsB,IAAI,GAAGuB;MAC5B,CAAC,GAAG;IACN,CAAC,CAAC,CAAC;EACL,CAAC;EAEDQ,SAAS,EAAGR,MAAc,IAAK;IAC7B,MAAMV,KAAK,GAAGpC,GAAG,CAAC,CAAC;IACnB,IAAI,CAACoC,KAAK,CAACnC,MAAM,IAAImC,KAAK,CAACnC,MAAM,CAACsB,IAAI,GAAGuB,MAAM,EAAE;MAC/C,OAAO,KAAK;IACd;IAEA/C,GAAG,CAAEqC,KAAK,KAAM;MACdnC,MAAM,EAAEmC,KAAK,CAACnC,MAAM,GAAG;QACrB,GAAGmC,KAAK,CAACnC,MAAM;QACfsB,IAAI,EAAEa,KAAK,CAACnC,MAAM,CAACsB,IAAI,GAAGuB;MAC5B,CAAC,GAAG;IACN,CAAC,CAAC,CAAC;IAEH,OAAO,IAAI;EACb,CAAC;EAEDS,gBAAgB,EAAGC,MAAkC,IAAK;IACxDzD,GAAG,CAAC;MAAEQ,aAAa,EAAEiD;IAAO,CAAC,CAAC;EAChC,CAAC;EAEDX,eAAe,EAAGY,YAAwD,IAAK;IAC7E,MAAMC,eAAiC,GAAG;MACxC,GAAGD,YAAY;MACf7B,EAAE,EAAE,SAASb,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI2C,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;MAC1C5B,SAAS,EAAE,IAAIjB,IAAI,CAAC,CAAC,CAACW,WAAW,CAAC;IACpC,CAAC;IAED3B,GAAG,CAAEqC,KAAK,KAAM;MACdhC,aAAa,EAAE,CAACsD,eAAe,EAAE,GAAGtB,KAAK,CAAChC,aAAa,CAAC,CAACyD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;EACL,CAAC;EAEDC,oBAAoB,EAAGlC,EAAU,IAAK;IACpC7B,GAAG,CAAEqC,KAAK,KAAM;MACdhC,aAAa,EAAEgC,KAAK,CAAChC,aAAa,CAAC2D,GAAG,CAACC,KAAK,IAC1CA,KAAK,CAACpC,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAGoC,KAAK;QAAE/B,IAAI,EAAE;MAAK,CAAC,GAAG+B,KAC/C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAEDC,kBAAkB,EAAEA,CAAA,KAAM;IACxBlE,GAAG,CAAC;MAAEK,aAAa,EAAE;IAAG,CAAC,CAAC;EAC5B,CAAC;EAED8D,QAAQ,EAAGzD,KAAoB,IAAK;IAClCV,GAAG,CAAC;MAAEU;IAAM,CAAC,CAAC;IACd,IAAIA,KAAK,EAAE;MACT;MACA2C,UAAU,CAAC,MAAM;QACfrD,GAAG,CAAC;UAAEU,KAAK,EAAE;QAAK,CAAC,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED0D,UAAU,EAAGC,OAAgB,IAAK;IAChCrE,GAAG,CAAC;MAAES,SAAS,EAAE4D;IAAQ,CAAC,CAAC;EAC7B,CAAC;EAEDC,KAAK,EAAEA,CAAA,KAAM;IACXtE,GAAG,CAAC;MACFE,MAAM,EAAE,IAAI;MACZC,UAAU,EAAEN,iBAAiB;MAC7BO,QAAQ,EAAE,KAAK;MACfC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,EAAE;MACpBC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,EACF;EACE6D,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAGnC,KAAK,KAAM;IACtBnC,MAAM,EAAEmC,KAAK,CAACnC,MAAM;IACpBC,UAAU,EAAEkC,KAAK,CAAClC,UAAU;IAC5BE,aAAa,EAAEgC,KAAK,CAAChC,aAAa;IAClCE,WAAW,EAAE8B,KAAK,CAAC9B;EACrB,CAAC;AACH,CACF,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}