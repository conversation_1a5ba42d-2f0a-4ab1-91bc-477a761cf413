-- Migração: Adici<PERSON>r colunas necessárias na tabela usuarios
-- Execute este script no SQL Editor do Supabase

-- Adicionar coluna password_hash (se não existir)
ALTER TABLE usuarios 
ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);

-- Ad<PERSON><PERSON>r coluna pontos (se não existir)
ALTER TABLE usuarios 
ADD COLUMN IF NOT EXISTS pontos BIGINT DEFAULT 0;

-- Atualizar pontos baseado em XP (se necessário)
UPDATE usuarios 
SET pontos = COALESCE(xp, 0) + COALESCE(dinhe<PERSON>, 0) 
WHERE pontos = 0;

-- Comentários explicativos
COMMENT ON COLUMN usuarios.password_hash IS 'Hash da senha para autenticação própria';
COMMENT ON COLUMN usuarios.pontos IS 'Pontos totais do jogador para ranking';
