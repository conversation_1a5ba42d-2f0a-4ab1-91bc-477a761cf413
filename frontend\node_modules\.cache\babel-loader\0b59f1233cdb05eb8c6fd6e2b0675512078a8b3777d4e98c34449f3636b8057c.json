{"ast": null, "code": "import { createLocalStorage, THEME_PREFERENCE, QueryDevtoolsContext, PiPProvider, ThemeContext, ParentPanel, ContentView } from '../chunk/L7Z3HDK6.js';\nimport { getPreferredColorScheme, createMemo, createComponent } from '../chunk/V5T5VJKG.js';\n\n// src/DevtoolsPanelComponent.tsx\nvar DevtoolsPanelComponent = props => {\n  const [localStore, setLocalStore] = createLocalStorage({\n    prefix: \"TanstackQueryDevtools\"\n  });\n  const colorScheme = getPreferredColorScheme();\n  const theme = createMemo(() => {\n    const preference = localStore.theme_preference || THEME_PREFERENCE;\n    if (preference !== \"system\") return preference;\n    return colorScheme();\n  });\n  return createComponent(QueryDevtoolsContext.Provider, {\n    value: props,\n    get children() {\n      return createComponent(PiPProvider, {\n        disabled: true,\n        localStore,\n        setLocalStore,\n        get children() {\n          return createComponent(ThemeContext.Provider, {\n            value: theme,\n            get children() {\n              return createComponent(ParentPanel, {\n                get children() {\n                  return createComponent(ContentView, {\n                    localStore,\n                    setLocalStore,\n                    get onClose() {\n                      return props.onClose;\n                    },\n                    showPanelViewOnly: true\n                  });\n                }\n              });\n            }\n          });\n        }\n      });\n    }\n  });\n};\nvar DevtoolsPanelComponent_default = DevtoolsPanelComponent;\nexport { DevtoolsPanelComponent_default as default };", "map": {"version": 3, "names": ["createLocalStorage", "THEME_PREFERENCE", "QueryDevtoolsContext", "<PERSON><PERSON><PERSON><PERSON>", "ThemeContext", "ParentPanel", "ContentView", "getPreferredColorScheme", "createMemo", "createComponent", "DevtoolsPanelComponent", "props", "localStore", "setLocalStore", "prefix", "colorScheme", "theme", "preference", "theme_preference", "Provider", "value", "children", "disabled", "onClose", "showPanelViewOnly", "DevtoolsPanelComponent_default", "default"], "sources": ["C:/Users/<USER>/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js"], "sourcesContent": ["import { createLocalStorage, THEME_PREFERENCE, QueryDevtoolsContext, PiPProvider, ThemeContext, ParentPanel, ContentView } from '../chunk/L7Z3HDK6.js';\nimport { getPreferredColorScheme, createMemo, createComponent } from '../chunk/V5T5VJKG.js';\n\n// src/DevtoolsPanelComponent.tsx\nvar DevtoolsPanelComponent = (props) => {\n  const [localStore, setLocalStore] = createLocalStorage({\n    prefix: \"TanstackQueryDevtools\"\n  });\n  const colorScheme = getPreferredColorScheme();\n  const theme = createMemo(() => {\n    const preference = localStore.theme_preference || THEME_PREFERENCE;\n    if (preference !== \"system\") return preference;\n    return colorScheme();\n  });\n  return createComponent(QueryDevtoolsContext.Provider, {\n    value: props,\n    get children() {\n      return createComponent(PiPProvider, {\n        disabled: true,\n        localStore,\n        setLocalStore,\n        get children() {\n          return createComponent(ThemeContext.Provider, {\n            value: theme,\n            get children() {\n              return createComponent(ParentPanel, {\n                get children() {\n                  return createComponent(ContentView, {\n                    localStore,\n                    setLocalStore,\n                    get onClose() {\n                      return props.onClose;\n                    },\n                    showPanelViewOnly: true\n                  });\n                }\n              });\n            }\n          });\n        }\n      });\n    }\n  });\n};\nvar DevtoolsPanelComponent_default = DevtoolsPanelComponent;\n\nexport { DevtoolsPanelComponent_default as default };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AACtJ,SAASC,uBAAuB,EAAEC,UAAU,EAAEC,eAAe,QAAQ,sBAAsB;;AAE3F;AACA,IAAIC,sBAAsB,GAAIC,KAAK,IAAK;EACtC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,kBAAkB,CAAC;IACrDc,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAMC,WAAW,GAAGR,uBAAuB,CAAC,CAAC;EAC7C,MAAMS,KAAK,GAAGR,UAAU,CAAC,MAAM;IAC7B,MAAMS,UAAU,GAAGL,UAAU,CAACM,gBAAgB,IAAIjB,gBAAgB;IAClE,IAAIgB,UAAU,KAAK,QAAQ,EAAE,OAAOA,UAAU;IAC9C,OAAOF,WAAW,CAAC,CAAC;EACtB,CAAC,CAAC;EACF,OAAON,eAAe,CAACP,oBAAoB,CAACiB,QAAQ,EAAE;IACpDC,KAAK,EAAET,KAAK;IACZ,IAAIU,QAAQA,CAAA,EAAG;MACb,OAAOZ,eAAe,CAACN,WAAW,EAAE;QAClCmB,QAAQ,EAAE,IAAI;QACdV,UAAU;QACVC,aAAa;QACb,IAAIQ,QAAQA,CAAA,EAAG;UACb,OAAOZ,eAAe,CAACL,YAAY,CAACe,QAAQ,EAAE;YAC5CC,KAAK,EAAEJ,KAAK;YACZ,IAAIK,QAAQA,CAAA,EAAG;cACb,OAAOZ,eAAe,CAACJ,WAAW,EAAE;gBAClC,IAAIgB,QAAQA,CAAA,EAAG;kBACb,OAAOZ,eAAe,CAACH,WAAW,EAAE;oBAClCM,UAAU;oBACVC,aAAa;oBACb,IAAIU,OAAOA,CAAA,EAAG;sBACZ,OAAOZ,KAAK,CAACY,OAAO;oBACtB,CAAC;oBACDC,iBAAiB,EAAE;kBACrB,CAAC,CAAC;gBACJ;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAIC,8BAA8B,GAAGf,sBAAsB;AAE3D,SAASe,8BAA8B,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}