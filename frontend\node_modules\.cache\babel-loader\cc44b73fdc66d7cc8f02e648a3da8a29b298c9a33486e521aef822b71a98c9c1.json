{"ast": null, "code": "// src/mutationCache.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Mutation } from \"./mutation.js\";\nimport { matchMutation, noop } from \"./utils.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar MutationCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */new Set();\n    this.#scopes = /* @__PURE__ */new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({\n      type: \"added\",\n      mutation\n    });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({\n      type: \"removed\",\n      mutation\n    });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(m => m.state.status === \"pending\");\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find(m => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.#mutations.forEach(mutation => {\n        this.notify({\n          type: \"removed\",\n          mutation\n        });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = {\n      exact: true,\n      ...filters\n    };\n    return this.getAll().find(mutation => matchMutation(defaultedFilters, mutation));\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter(mutation => matchMutation(filters, mutation));\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(listener => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter(x => x.state.isPaused);\n    return notifyManager.batch(() => Promise.all(pausedMutations.map(mutation => mutation.continue().catch(noop))));\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\nexport { MutationCache };", "map": {"version": 3, "names": ["notify<PERSON><PERSON>ger", "Mutation", "matchMutation", "noop", "Subscribable", "MutationCache", "constructor", "config", "mutations", "Set", "scopes", "Map", "mutationId", "build", "client", "options", "state", "mutation", "mutationCache", "defaultMutationOptions", "add", "scope", "scopeFor", "scopedMutations", "get", "push", "set", "notify", "type", "remove", "delete", "length", "index", "indexOf", "splice", "canRun", "mutationsWithSameScope", "firstPendingMutation", "find", "m", "status", "runNext", "foundMutation", "isPaused", "continue", "Promise", "resolve", "clear", "batch", "for<PERSON>ach", "getAll", "Array", "from", "filters", "defaultedFilters", "exact", "findAll", "filter", "event", "listeners", "listener", "resumePausedMutations", "pausedMutations", "x", "all", "map", "catch", "id"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\query-core\\src\\mutationCache.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { DefaultError, MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: DefaultError,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: DefaultError | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\nexport type MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  #mutations: Set<Mutation<any, any, any, any>>\n  #scopes: Map<string, Array<Mutation<any, any, any, any>>>\n  #mutationId: number\n\n  constructor(public config: MutationCacheConfig = {}) {\n    super()\n    this.#mutations = new Set()\n    this.#scopes = new Map()\n    this.#mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.#mutations.add(mutation)\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const scopedMutations = this.#scopes.get(scope)\n      if (scopedMutations) {\n        scopedMutations.push(mutation)\n      } else {\n        this.#scopes.set(scope, [mutation])\n      }\n    }\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation)\n      if (typeof scope === 'string') {\n        const scopedMutations = this.#scopes.get(scope)\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation)\n            if (index !== -1) {\n              scopedMutations.splice(index, 1)\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope)\n          }\n        }\n      }\n    }\n\n    // Currently we notify the removal even if the mutation was already removed.\n    // Consider making this an error or not notifying of the removal depending on the desired semantics.\n    this.notify({ type: 'removed', mutation })\n  }\n\n  canRun(mutation: Mutation<any, any, any, any>): boolean {\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const mutationsWithSameScope = this.#scopes.get(scope)\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === 'pending',\n      )\n      // we can run if there is no current pending mutation (start use-case)\n      // or if WE are the first pending mutation (continue use-case)\n      return !firstPendingMutation || firstPendingMutation === mutation\n    } else {\n      // For unscoped mutations there are never any pending mutations in front of the\n      // current mutation\n      return true\n    }\n  }\n\n  runNext(mutation: Mutation<any, any, any, any>): Promise<unknown> {\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const foundMutation = this.#scopes\n        .get(scope)\n        ?.find((m) => m !== mutation && m.state.isPaused)\n\n      return foundMutation?.continue() ?? Promise.resolve()\n    } else {\n      return Promise.resolve()\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: 'removed', mutation })\n      })\n      this.#mutations.clear()\n      this.#scopes.clear()\n    })\n  }\n\n  getAll(): Array<Mutation> {\n    return Array.from(this.#mutations)\n  }\n\n  find<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = any,\n    TContext = unknown,\n  >(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    const defaultedFilters = { exact: true, ...filters }\n\n    return this.getAll().find((mutation) =>\n      matchMutation(defaultedFilters, mutation),\n    ) as Mutation<TData, TError, TVariables, TContext> | undefined\n  }\n\n  findAll(filters: MutationFilters = {}): Array<Mutation> {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused)\n\n    return notifyManager.batch(() =>\n      Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop)),\n      ),\n    )\n  }\n}\n\nfunction scopeFor(mutation: Mutation<any, any, any, any>) {\n  return mutation.options.scope?.id\n}\n"], "mappings": ";AAAA,SAASA,aAAA,QAAqB;AAC9B,SAASC,QAAA,QAAgB;AACzB,SAASC,aAAA,EAAeC,IAAA,QAAY;AACpC,SAASC,YAAA,QAAoB;AAgFtB,IAAMC,aAAA,GAAN,cAA4BD,YAAA,CAAoC;EAKrEE,YAAmBC,MAAA,GAA8B,CAAC,GAAG;IACnD,MAAM;IADW,KAAAA,MAAA,GAAAA,MAAA;IAEjB,KAAK,CAAAC,SAAA,GAAa,mBAAIC,GAAA,CAAI;IAC1B,KAAK,CAAAC,MAAA,GAAU,mBAAIC,GAAA,CAAI;IACvB,KAAK,CAAAC,UAAA,GAAc;EACrB;EATA,CAAAJ,SAAA;EACA,CAAAE,MAAA;EACA,CAAAE,UAAA;EASAC,MACEC,MAAA,EACAC,OAAA,EACAC,KAAA,EAC+C;IAC/C,MAAMC,QAAA,GAAW,IAAIhB,QAAA,CAAS;MAC5BiB,aAAA,EAAe;MACfN,UAAA,EAAY,EAAE,KAAK,CAAAA,UAAA;MACnBG,OAAA,EAASD,MAAA,CAAOK,sBAAA,CAAuBJ,OAAO;MAC9CC;IACF,CAAC;IAED,KAAKI,GAAA,CAAIH,QAAQ;IAEjB,OAAOA,QAAA;EACT;EAEAG,IAAIH,QAAA,EAA8C;IAChD,KAAK,CAAAT,SAAA,CAAWY,GAAA,CAAIH,QAAQ;IAC5B,MAAMI,KAAA,GAAQC,QAAA,CAASL,QAAQ;IAC/B,IAAI,OAAOI,KAAA,KAAU,UAAU;MAC7B,MAAME,eAAA,GAAkB,KAAK,CAAAb,MAAA,CAAQc,GAAA,CAAIH,KAAK;MAC9C,IAAIE,eAAA,EAAiB;QACnBA,eAAA,CAAgBE,IAAA,CAAKR,QAAQ;MAC/B,OAAO;QACL,KAAK,CAAAP,MAAA,CAAQgB,GAAA,CAAIL,KAAA,EAAO,CAACJ,QAAQ,CAAC;MACpC;IACF;IACA,KAAKU,MAAA,CAAO;MAAEC,IAAA,EAAM;MAASX;IAAS,CAAC;EACzC;EAEAY,OAAOZ,QAAA,EAA8C;IACnD,IAAI,KAAK,CAAAT,SAAA,CAAWsB,MAAA,CAAOb,QAAQ,GAAG;MACpC,MAAMI,KAAA,GAAQC,QAAA,CAASL,QAAQ;MAC/B,IAAI,OAAOI,KAAA,KAAU,UAAU;QAC7B,MAAME,eAAA,GAAkB,KAAK,CAAAb,MAAA,CAAQc,GAAA,CAAIH,KAAK;QAC9C,IAAIE,eAAA,EAAiB;UACnB,IAAIA,eAAA,CAAgBQ,MAAA,GAAS,GAAG;YAC9B,MAAMC,KAAA,GAAQT,eAAA,CAAgBU,OAAA,CAAQhB,QAAQ;YAC9C,IAAIe,KAAA,KAAU,IAAI;cAChBT,eAAA,CAAgBW,MAAA,CAAOF,KAAA,EAAO,CAAC;YACjC;UACF,WAAWT,eAAA,CAAgB,CAAC,MAAMN,QAAA,EAAU;YAC1C,KAAK,CAAAP,MAAA,CAAQoB,MAAA,CAAOT,KAAK;UAC3B;QACF;MACF;IACF;IAIA,KAAKM,MAAA,CAAO;MAAEC,IAAA,EAAM;MAAWX;IAAS,CAAC;EAC3C;EAEAkB,OAAOlB,QAAA,EAAiD;IACtD,MAAMI,KAAA,GAAQC,QAAA,CAASL,QAAQ;IAC/B,IAAI,OAAOI,KAAA,KAAU,UAAU;MAC7B,MAAMe,sBAAA,GAAyB,KAAK,CAAA1B,MAAA,CAAQc,GAAA,CAAIH,KAAK;MACrD,MAAMgB,oBAAA,GAAuBD,sBAAA,EAAwBE,IAAA,CAClDC,CAAA,IAAMA,CAAA,CAAEvB,KAAA,CAAMwB,MAAA,KAAW,SAC5B;MAGA,OAAO,CAACH,oBAAA,IAAwBA,oBAAA,KAAyBpB,QAAA;IAC3D,OAAO;MAGL,OAAO;IACT;EACF;EAEAwB,QAAQxB,QAAA,EAA0D;IAChE,MAAMI,KAAA,GAAQC,QAAA,CAASL,QAAQ;IAC/B,IAAI,OAAOI,KAAA,KAAU,UAAU;MAC7B,MAAMqB,aAAA,GAAgB,KAAK,CAAAhC,MAAA,CACxBc,GAAA,CAAIH,KAAK,GACRiB,IAAA,CAAMC,CAAA,IAAMA,CAAA,KAAMtB,QAAA,IAAYsB,CAAA,CAAEvB,KAAA,CAAM2B,QAAQ;MAElD,OAAOD,aAAA,EAAeE,QAAA,CAAS,KAAKC,OAAA,CAAQC,OAAA,CAAQ;IACtD,OAAO;MACL,OAAOD,OAAA,CAAQC,OAAA,CAAQ;IACzB;EACF;EAEAC,MAAA,EAAc;IACZ/C,aAAA,CAAcgD,KAAA,CAAM,MAAM;MACxB,KAAK,CAAAxC,SAAA,CAAWyC,OAAA,CAAShC,QAAA,IAAa;QACpC,KAAKU,MAAA,CAAO;UAAEC,IAAA,EAAM;UAAWX;QAAS,CAAC;MAC3C,CAAC;MACD,KAAK,CAAAT,SAAA,CAAWuC,KAAA,CAAM;MACtB,KAAK,CAAArC,MAAA,CAAQqC,KAAA,CAAM;IACrB,CAAC;EACH;EAEAG,OAAA,EAA0B;IACxB,OAAOC,KAAA,CAAMC,IAAA,CAAK,KAAK,CAAA5C,SAAU;EACnC;EAEA8B,KAMEe,OAAA,EAC2D;IAC3D,MAAMC,gBAAA,GAAmB;MAAEC,KAAA,EAAO;MAAM,GAAGF;IAAQ;IAEnD,OAAO,KAAKH,MAAA,CAAO,EAAEZ,IAAA,CAAMrB,QAAA,IACzBf,aAAA,CAAcoD,gBAAA,EAAkBrC,QAAQ,CAC1C;EACF;EAEAuC,QAAQH,OAAA,GAA2B,CAAC,GAAoB;IACtD,OAAO,KAAKH,MAAA,CAAO,EAAEO,MAAA,CAAQxC,QAAA,IAAaf,aAAA,CAAcmD,OAAA,EAASpC,QAAQ,CAAC;EAC5E;EAEAU,OAAO+B,KAAA,EAAiC;IACtC1D,aAAA,CAAcgD,KAAA,CAAM,MAAM;MACxB,KAAKW,SAAA,CAAUV,OAAA,CAASW,QAAA,IAAa;QACnCA,QAAA,CAASF,KAAK;MAChB,CAAC;IACH,CAAC;EACH;EAEAG,sBAAA,EAA0C;IACxC,MAAMC,eAAA,GAAkB,KAAKZ,MAAA,CAAO,EAAEO,MAAA,CAAQM,CAAA,IAAMA,CAAA,CAAE/C,KAAA,CAAM2B,QAAQ;IAEpE,OAAO3C,aAAA,CAAcgD,KAAA,CAAM,MACzBH,OAAA,CAAQmB,GAAA,CACNF,eAAA,CAAgBG,GAAA,CAAKhD,QAAA,IAAaA,QAAA,CAAS2B,QAAA,CAAS,EAAEsB,KAAA,CAAM/D,IAAI,CAAC,CACnE,CACF;EACF;AACF;AAEA,SAASmB,SAASL,QAAA,EAAwC;EACxD,OAAOA,QAAA,CAASF,OAAA,CAAQM,KAAA,EAAO8C,EAAA;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}