{"ast": null, "code": "import React,{useState}from'react';import{useSimpleAuth}from'../stores/simpleAuthStore';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SimpleLoginPage=()=>{const{isLoading,error,login,register,simulateLogin,isAuthenticated,user,clearError}=useSimpleAuth();const[isRegisterMode,setIsRegisterMode]=useState(false);const[formData,setFormData]=useState({email:'',password:'',nick:''});console.log('SimpleLoginPage - Estado:',{isLoading,isAuthenticated,user:!!user});const handleInputChange=e=>{const{name,value}=e.target;setFormData(prev=>({...prev,[name]:value}));// Limpar erro quando usuário digita\nif(error)clearError();};const handleSubmit=async e=>{e.preventDefault();if(isRegisterMode){if(!formData.email||!formData.password||!formData.nick){return;}await register({email:formData.email,password:formData.password,nick:formData.nick});}else{if(!formData.email||!formData.password){return;}await login({email:formData.email,password:formData.password});}};const toggleMode=()=>{setIsRegisterMode(!isRegisterMode);setFormData({email:'',password:'',nick:''});clearError();};// Se já está autenticado, mostrar sucesso\nif(isAuthenticated&&user){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"card max-w-md w-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-green-400 mb-4\",children:\"\\u2705 Login Realizado!\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-text-muted mb-6\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"Bem-vindo, \",/*#__PURE__*/_jsx(\"strong\",{children:user.nick}),\"!\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm\",children:[\"UID: \",user.uid]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm\",children:[\"Email: \",user.email]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-green-300 mb-4\",children:\"\\uD83C\\uDFAE Agora voc\\xEA pode acessar o jogo!\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.location.href='/game',className:\"btn-primary\",children:\"Ir para o Jogo\"})]})]})})});}return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card max-w-md w-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-8\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold mb-2\",children:\"\\uD83C\\uDFAE SHACK Web Game\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-muted\",children:\"Login Simplificado (Modo Teste)\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-900 border border-red-500 text-red-100 p-4 rounded-lg mb-6\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:error})}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-6\",children:isRegisterMode?'📝 Criar Conta':'🔐 Fazer Login'}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,className:\"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\",placeholder:\"<EMAIL>\",required:true})]}),isRegisterMode&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"Nick\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"nick\",value:formData.nick,onChange:handleInputChange,className:\"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\",placeholder:\"Seu nick no jogo\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"Senha\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",name:\"password\",value:formData.password,onChange:handleInputChange,className:\"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\",placeholder:\"Sua senha\",required:true})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isLoading,className:`w-full py-3 px-4 rounded-lg font-medium transition-colors ${isLoading?'bg-gray-600 text-gray-300 cursor-not-allowed':'bg-blue-600 hover:bg-blue-700 text-white'}`,children:isLoading?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"}),isRegisterMode?'Criando conta...':'Fazendo login...']}):isRegisterMode?'✨ Criar Conta':'🚀 Entrar'})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-6 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:toggleMode,className:\"text-blue-400 hover:text-blue-300 text-sm\",children:isRegisterMode?'Já tem conta? Fazer login':'Não tem conta? Criar uma'})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card bg-bg-tertiary\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4\",children:\"\\uD83E\\uDDEA Teste R\\xE1pido\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-muted text-sm mb-4\",children:\"Para testar rapidamente sem preencher formul\\xE1rio:\"}),/*#__PURE__*/_jsx(\"button\",{onClick:simulateLogin,disabled:isLoading,className:\"w-full btn-secondary\",children:\"\\u26A1 Login Instant\\xE2neo\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card bg-yellow-900 border-yellow-500\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-yellow-100 mb-2\",children:\"\\u2139\\uFE0F Modo Desenvolvimento\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-yellow-200 text-sm\",children:\"Usando API mock para desenvolvimento. Os dados n\\xE3o s\\xE3o persistidos.\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-8 text-center text-text-muted text-xs\",children:/*#__PURE__*/_jsx(\"p\",{children:\"Vers\\xE3o de teste - React funcionando \\u2705\"})})]})});};export default SimpleLoginPage;", "map": {"version": 3, "names": ["React", "useState", "useSimpleAuth", "jsx", "_jsx", "jsxs", "_jsxs", "SimpleLoginPage", "isLoading", "error", "login", "register", "simulateLogin", "isAuthenticated", "user", "clearError", "isRegisterMode", "setIsRegisterMode", "formData", "setFormData", "email", "password", "nick", "console", "log", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "toggleMode", "className", "children", "uid", "onClick", "window", "location", "href", "onSubmit", "type", "onChange", "placeholder", "required", "disabled"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleLoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\n\nconst SimpleLoginPage: React.FC = () => {\n  const { isLoading, error, login, register, simulateLogin, isAuthenticated, user, clearError } = useSimpleAuth();\n\n  const [isRegisterMode, setIsRegisterMode] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    nick: ''\n  });\n\n  console.log('SimpleLoginPage - Estado:', { isLoading, isAuthenticated, user: !!user });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Limpar erro quando usuário digita\n    if (error) clearError();\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (isRegisterMode) {\n      if (!formData.email || !formData.password || !formData.nick) {\n        return;\n      }\n      await register({\n        email: formData.email,\n        password: formData.password,\n        nick: formData.nick\n      });\n    } else {\n      if (!formData.email || !formData.password) {\n        return;\n      }\n      await login({\n        email: formData.email,\n        password: formData.password\n      });\n    }\n  };\n\n  const toggleMode = () => {\n    setIsRegisterMode(!isRegisterMode);\n    setFormData({ email: '', password: '', nick: '' });\n    clearError();\n  };\n\n  // Se já está autenticado, mostrar sucesso\n  if (isAuthenticated && user) {\n    return (\n      <div className=\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\">\n        <div className=\"card max-w-md w-full\">\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-bold text-green-400 mb-4\">\n              ✅ Login Realizado!\n            </h1>\n            <div className=\"text-text-muted mb-6\">\n              <p>Bem-vindo, <strong>{user.nick}</strong>!</p>\n              <p className=\"text-sm\">UID: {user.uid}</p>\n              <p className=\"text-sm\">Email: {user.email}</p>\n            </div>\n            <div className=\"text-center\">\n              <p className=\"text-sm text-green-300 mb-4\">\n                🎮 Agora você pode acessar o jogo!\n              </p>\n              <button \n                onClick={() => window.location.href = '/game'}\n                className=\"btn-primary\"\n              >\n                Ir para o Jogo\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\">\n      <div className=\"card max-w-md w-full\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold mb-2\">\n            🎮 SHACK Web Game\n          </h1>\n          <p className=\"text-text-muted\">\n            Login Simplificado (Modo Teste)\n          </p>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900 border border-red-500 text-red-100 p-4 rounded-lg mb-6\">\n            <p className=\"text-sm\">{error}</p>\n          </div>\n        )}\n\n        <div className=\"space-y-6\">\n          {/* Formulário de Login/Registro */}\n          <div className=\"card\">\n            <h3 className=\"text-xl font-semibold mb-6\">\n              {isRegisterMode ? '📝 Criar Conta' : '🔐 Fazer Login'}\n            </h3>\n\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">\n                  Email\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                />\n              </div>\n\n              {isRegisterMode && (\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    Nick\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"nick\"\n                    value={formData.nick}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\"\n                    placeholder=\"Seu nick no jogo\"\n                    required\n                  />\n                </div>\n              )}\n\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">\n                  Senha\n                </label>\n                <input\n                  type=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\"\n                  placeholder=\"Sua senha\"\n                  required\n                />\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${\n                  isLoading\n                    ? 'bg-gray-600 text-gray-300 cursor-not-allowed'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white'\n                }`}\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                    {isRegisterMode ? 'Criando conta...' : 'Fazendo login...'}\n                  </div>\n                ) : (\n                  isRegisterMode ? '✨ Criar Conta' : '🚀 Entrar'\n                )}\n              </button>\n            </form>\n\n            <div className=\"mt-6 text-center\">\n              <button\n                onClick={toggleMode}\n                className=\"text-blue-400 hover:text-blue-300 text-sm\"\n              >\n                {isRegisterMode\n                  ? 'Já tem conta? Fazer login'\n                  : 'Não tem conta? Criar uma'\n                }\n              </button>\n            </div>\n          </div>\n\n          {/* Botão de teste rápido */}\n          <div className=\"card bg-bg-tertiary\">\n            <h3 className=\"text-lg font-semibold mb-4\">\n              🧪 Teste Rápido\n            </h3>\n            <p className=\"text-text-muted text-sm mb-4\">\n              Para testar rapidamente sem preencher formulário:\n            </p>\n\n            <button\n              onClick={simulateLogin}\n              disabled={isLoading}\n              className=\"w-full btn-secondary\"\n            >\n              ⚡ Login Instantâneo\n            </button>\n          </div>\n\n          <div className=\"card bg-yellow-900 border-yellow-500\">\n            <h3 className=\"text-lg font-semibold text-yellow-100 mb-2\">\n              ℹ️ Modo Desenvolvimento\n            </h3>\n            <p className=\"text-yellow-200 text-sm\">\n              Usando API mock para desenvolvimento. Os dados não são persistidos.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"mt-8 text-center text-text-muted text-xs\">\n          <p>Versão de teste - React funcionando ✅</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleLoginPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,aAAa,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1D,KAAM,CAAAC,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAAEC,SAAS,CAAEC,KAAK,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,aAAa,CAAEC,eAAe,CAAEC,IAAI,CAAEC,UAAW,CAAC,CAAGb,aAAa,CAAC,CAAC,CAE/G,KAAM,CAACc,cAAc,CAAEC,iBAAiB,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACiB,QAAQ,CAAEC,WAAW,CAAC,CAAGlB,QAAQ,CAAC,CACvCmB,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,IAAI,CAAE,EACR,CAAC,CAAC,CAEFC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAE,CAAEhB,SAAS,CAAEK,eAAe,CAAEC,IAAI,CAAE,CAAC,CAACA,IAAK,CAAC,CAAC,CAEtF,KAAM,CAAAW,iBAAiB,CAAIC,CAAsC,EAAK,CACpE,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChCV,WAAW,CAACW,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP,CAACH,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CACH;AACA,GAAInB,KAAK,CAAEM,UAAU,CAAC,CAAC,CACzB,CAAC,CAED,KAAM,CAAAgB,YAAY,CAAG,KAAO,CAAAL,CAAkB,EAAK,CACjDA,CAAC,CAACM,cAAc,CAAC,CAAC,CAElB,GAAIhB,cAAc,CAAE,CAClB,GAAI,CAACE,QAAQ,CAACE,KAAK,EAAI,CAACF,QAAQ,CAACG,QAAQ,EAAI,CAACH,QAAQ,CAACI,IAAI,CAAE,CAC3D,OACF,CACA,KAAM,CAAAX,QAAQ,CAAC,CACbS,KAAK,CAAEF,QAAQ,CAACE,KAAK,CACrBC,QAAQ,CAAEH,QAAQ,CAACG,QAAQ,CAC3BC,IAAI,CAAEJ,QAAQ,CAACI,IACjB,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,GAAI,CAACJ,QAAQ,CAACE,KAAK,EAAI,CAACF,QAAQ,CAACG,QAAQ,CAAE,CACzC,OACF,CACA,KAAM,CAAAX,KAAK,CAAC,CACVU,KAAK,CAAEF,QAAQ,CAACE,KAAK,CACrBC,QAAQ,CAAEH,QAAQ,CAACG,QACrB,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAY,UAAU,CAAGA,CAAA,GAAM,CACvBhB,iBAAiB,CAAC,CAACD,cAAc,CAAC,CAClCG,WAAW,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,QAAQ,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAClDP,UAAU,CAAC,CAAC,CACd,CAAC,CAED;AACA,GAAIF,eAAe,EAAIC,IAAI,CAAE,CAC3B,mBACEV,IAAA,QAAK8B,SAAS,CAAC,+EAA+E,CAAAC,QAAA,cAC5F/B,IAAA,QAAK8B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC7B,KAAA,QAAK4B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B/B,IAAA,OAAI8B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,yBAEvD,CAAI,CAAC,cACL7B,KAAA,QAAK4B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC7B,KAAA,MAAA6B,QAAA,EAAG,aAAW,cAAA/B,IAAA,WAAA+B,QAAA,CAASrB,IAAI,CAACQ,IAAI,CAAS,CAAC,IAAC,EAAG,CAAC,cAC/ChB,KAAA,MAAG4B,SAAS,CAAC,SAAS,CAAAC,QAAA,EAAC,OAAK,CAACrB,IAAI,CAACsB,GAAG,EAAI,CAAC,cAC1C9B,KAAA,MAAG4B,SAAS,CAAC,SAAS,CAAAC,QAAA,EAAC,SAAO,CAACrB,IAAI,CAACM,KAAK,EAAI,CAAC,EAC3C,CAAC,cACNd,KAAA,QAAK4B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B/B,IAAA,MAAG8B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,iDAE3C,CAAG,CAAC,cACJ/B,IAAA,WACEiC,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,OAAQ,CAC9CN,SAAS,CAAC,aAAa,CAAAC,QAAA,CACxB,gBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA,mBACE/B,IAAA,QAAK8B,SAAS,CAAC,+EAA+E,CAAAC,QAAA,cAC5F7B,KAAA,QAAK4B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC7B,KAAA,QAAK4B,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B/B,IAAA,OAAI8B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,6BAExC,CAAI,CAAC,cACL/B,IAAA,MAAG8B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,iCAE/B,CAAG,CAAC,EACD,CAAC,CAEL1B,KAAK,eACJL,IAAA,QAAK8B,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChF/B,IAAA,MAAG8B,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAE1B,KAAK,CAAI,CAAC,CAC/B,CACN,cAEDH,KAAA,QAAK4B,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB7B,KAAA,QAAK4B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB/B,IAAA,OAAI8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACvCnB,cAAc,CAAG,gBAAgB,CAAG,gBAAgB,CACnD,CAAC,cAELV,KAAA,SAAMmC,QAAQ,CAAEV,YAAa,CAACG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACjD7B,KAAA,QAAA6B,QAAA,eACE/B,IAAA,UAAO8B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,OAElD,CAAO,CAAC,cACR/B,IAAA,UACEsC,IAAI,CAAC,OAAO,CACZf,IAAI,CAAC,OAAO,CACZC,KAAK,CAAEV,QAAQ,CAACE,KAAM,CACtBuB,QAAQ,CAAElB,iBAAkB,CAC5BS,SAAS,CAAC,kHAAkH,CAC5HU,WAAW,CAAC,eAAe,CAC3BC,QAAQ,MACT,CAAC,EACC,CAAC,CAEL7B,cAAc,eACbV,KAAA,QAAA6B,QAAA,eACE/B,IAAA,UAAO8B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,MAElD,CAAO,CAAC,cACR/B,IAAA,UACEsC,IAAI,CAAC,MAAM,CACXf,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEV,QAAQ,CAACI,IAAK,CACrBqB,QAAQ,CAAElB,iBAAkB,CAC5BS,SAAS,CAAC,kHAAkH,CAC5HU,WAAW,CAAC,kBAAkB,CAC9BC,QAAQ,MACT,CAAC,EACC,CACN,cAEDvC,KAAA,QAAA6B,QAAA,eACE/B,IAAA,UAAO8B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,OAElD,CAAO,CAAC,cACR/B,IAAA,UACEsC,IAAI,CAAC,UAAU,CACff,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEV,QAAQ,CAACG,QAAS,CACzBsB,QAAQ,CAAElB,iBAAkB,CAC5BS,SAAS,CAAC,kHAAkH,CAC5HU,WAAW,CAAC,WAAW,CACvBC,QAAQ,MACT,CAAC,EACC,CAAC,cAENzC,IAAA,WACEsC,IAAI,CAAC,QAAQ,CACbI,QAAQ,CAAEtC,SAAU,CACpB0B,SAAS,CAAE,6DACT1B,SAAS,CACL,8CAA8C,CAC9C,0CAA0C,EAC7C,CAAA2B,QAAA,CAEF3B,SAAS,cACRF,KAAA,QAAK4B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C/B,IAAA,QAAK8B,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACrFlB,cAAc,CAAG,kBAAkB,CAAG,kBAAkB,EACtD,CAAC,CAENA,cAAc,CAAG,eAAe,CAAG,WACpC,CACK,CAAC,EACL,CAAC,cAEPZ,IAAA,QAAK8B,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/B/B,IAAA,WACEiC,OAAO,CAAEJ,UAAW,CACpBC,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAEpDnB,cAAc,CACX,2BAA2B,CAC3B,0BAA0B,CAExB,CAAC,CACN,CAAC,EACH,CAAC,cAGNV,KAAA,QAAK4B,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC/B,IAAA,OAAI8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,8BAE3C,CAAI,CAAC,cACL/B,IAAA,MAAG8B,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,sDAE5C,CAAG,CAAC,cAEJ/B,IAAA,WACEiC,OAAO,CAAEzB,aAAc,CACvBkC,QAAQ,CAAEtC,SAAU,CACpB0B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CACjC,6BAED,CAAQ,CAAC,EACN,CAAC,cAEN7B,KAAA,QAAK4B,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnD/B,IAAA,OAAI8B,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,mCAE3D,CAAI,CAAC,cACL/B,IAAA,MAAG8B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,2EAEvC,CAAG,CAAC,EACD,CAAC,EACH,CAAC,cAEN/B,IAAA,QAAK8B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvD/B,IAAA,MAAA+B,QAAA,CAAG,+CAAqC,CAAG,CAAC,CACzC,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}