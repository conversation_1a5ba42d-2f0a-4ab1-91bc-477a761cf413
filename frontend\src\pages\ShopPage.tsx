import React, { useState, useEffect } from 'react';
import { useAuth } from '../stores/authStore';
import { usePlayer } from '../stores/playerStore';
import gameApi from '../services/gameApi';

interface ShopItem {
  id: string;
  name: string;
  displayName: string;
  icon: string;
  description: string;
  price: {
    dinheiro?: number;
    shacks?: number;
  };
  category: 'hardware' | 'software' | 'tools' | 'special';
  available: boolean;
}

const ShopPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { currentPlayer, loadPlayerData } = usePlayer();
  
  const [shopItems, setShopItems] = useState<ShopItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [purchaseError, setPurchaseError] = useState<string | null>(null);
  const [purchaseSuccess, setPurchaseSuccess] = useState<string | null>(null);
  const [purchasingItem, setPurchasingItem] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated) {
      loadShopItems();
    }
  }, [isAuthenticated]);

  const loadShopItems = async () => {
    setIsLoading(true);
    try {
      // Tentar carregar do backend primeiro
      const response = await gameApi.getShopItems();
      if (response.sucesso && response.items) {
        setShopItems(response.items);
      } else {
        // Fallback para itens mockados
        loadMockItems();
      }
    } catch (error) {
      console.error('Erro ao carregar loja:', error);
      // Fallback para itens mockados
      loadMockItems();
    } finally {
      setIsLoading(false);
    }
  };

  const loadMockItems = () => {
    const mockItems: ShopItem[] = [
      // Hardware
      {
        id: 'cpu_upgrade',
        name: 'cpu_upgrade',
        displayName: 'CPU Upgrade Kit',
        icon: '🖥️',
        description: 'Kit para upgrade de CPU (+5 níveis)',
        price: { dinheiro: 50000 },
        category: 'hardware',
        available: true,
      },
      {
        id: 'ram_upgrade',
        name: 'ram_upgrade',
        displayName: 'RAM Upgrade Kit',
        icon: '💾',
        description: 'Kit para upgrade de RAM (+5 níveis)',
        price: { dinheiro: 40000 },
        category: 'hardware',
        available: true,
      },
      {
        id: 'firewall_pro',
        name: 'firewall_pro',
        displayName: 'Firewall Pro',
        icon: '🛡️',
        description: 'Firewall profissional (+10 níveis)',
        price: { dinheiro: 75000, shacks: 50 },
        category: 'software',
        available: true,
      },
      
      // Software
      {
        id: 'antivirus_premium',
        name: 'antivirus_premium',
        displayName: 'Antivírus Premium',
        icon: '🦠',
        description: 'Antivírus de última geração (+10 níveis)',
        price: { dinheiro: 60000, shacks: 30 },
        category: 'software',
        available: true,
      },
      {
        id: 'malware_toolkit',
        name: 'malware_toolkit',
        displayName: 'Malware Toolkit',
        icon: '🔧',
        description: 'Kit completo de ferramentas malware (+15 níveis)',
        price: { dinheiro: 100000, shacks: 75 },
        category: 'tools',
        available: true,
      },
      
      // Tools
      {
        id: 'bruteforce_accelerator',
        name: 'bruteforce_accelerator',
        displayName: 'BruteForce Accelerator',
        icon: '🔨',
        description: 'Acelera ataques de força bruta (+20 níveis)',
        price: { dinheiro: 150000, shacks: 100 },
        category: 'tools',
        available: true,
      },
      {
        id: 'proxy_network',
        name: 'proxy_network',
        displayName: 'Proxy Network',
        icon: '🌐',
        description: 'Rede de proxies premium (+25 níveis)',
        price: { dinheiro: 200000, shacks: 150 },
        category: 'tools',
        available: true,
      },
      
      // Special
      {
        id: 'hacker_bundle',
        name: 'hacker_bundle',
        displayName: 'Hacker Bundle',
        icon: '💎',
        description: 'Pacote completo do hacker (+50 todos os itens)',
        price: { dinheiro: 1000000, shacks: 500 },
        category: 'special',
        available: true,
      },
      {
        id: 'premium_account',
        name: 'premium_account',
        displayName: 'Conta Premium',
        icon: '👑',
        description: 'Acesso premium por 30 dias (bônus +100%)',
        price: { shacks: 1000 },
        category: 'special',
        available: true,
      },
    ];

    setShopItems(mockItems);
  };

  const handlePurchase = async (item: ShopItem) => {
    if (!currentPlayer) return;

    // Verificar se tem recursos suficientes
    if (item.price.dinheiro && (currentPlayer?.dinheiro || 0) < item.price.dinheiro) {
      setPurchaseError('Dinheiro insuficiente');
      return;
    }

    if (item.price.shacks && (currentPlayer?.shack || 0) < item.price.shacks) {
      setPurchaseError('Shacks insuficientes');
      return;
    }

    setPurchasingItem(item.id);
    setPurchaseError(null);
    setPurchaseSuccess(null);

    try {
      const response = await gameApi.buyShopItem(item.id);
      
      if (response.sucesso) {
        setPurchaseSuccess(`${item.displayName} comprado com sucesso!`);
        
        // Recarregar dados do jogador
        await loadPlayerData();
      } else {
        setPurchaseError(response.mensagem || 'Erro na compra');
      }
    } catch (error: any) {
      setPurchaseError(error.message || 'Erro de conexão');
    } finally {
      setPurchasingItem(null);
    }
  };

  const filteredItems = selectedCategory === 'all' 
    ? shopItems 
    : shopItems.filter(item => item.category === selectedCategory);

  const categories = [
    { id: 'all', name: 'Todos', icon: '🛍️' },
    { id: 'hardware', name: 'Hardware', icon: '🖥️' },
    { id: 'software', name: 'Software', icon: '💿' },
    { id: 'tools', name: 'Ferramentas', icon: '🔧' },
    { id: 'special', name: 'Especiais', icon: '💎' },
  ];

  if (!isAuthenticated) {
    return (
      <div className="h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">🔒 Acesso Negado</h1>
          <p className="text-gray-400">Você precisa estar logado para acessar a loja</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => window.history.back()}
            className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600"
          >
            <span className="text-lg">←</span>
          </button>
          <div>
            <h1 className="text-lg font-bold">🛒 Loja</h1>
            <p className="text-xs text-gray-400">Mercado Negro Digital</p>
          </div>
        </div>
      </div>

      {/* Recursos disponíveis */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-lg font-bold text-green-400">
              ${currentPlayer?.dinheiro?.toLocaleString() || '0'}
            </div>
            <div className="text-xs text-gray-400">Dinheiro</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-blue-400">
              {(currentPlayer?.shack || 0).toLocaleString()}
            </div>
            <div className="text-xs text-gray-400">Shacks</div>
          </div>
        </div>
      </div>

      {/* Filtros de categoria */}
      <div className="bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0">
        <div className="flex space-x-2 overflow-x-auto">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm whitespace-nowrap ${
                selectedCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <span>{category.icon}</span>
              <span>{category.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {/* Mensagens de erro/sucesso */}
        {purchaseError && (
          <div className="bg-red-900 border border-red-500 rounded-lg p-3">
            <p className="text-red-300 text-sm">❌ {purchaseError}</p>
          </div>
        )}

        {purchaseSuccess && (
          <div className="bg-green-900 border border-green-500 rounded-lg p-3">
            <p className="text-green-300 text-sm">✅ {purchaseSuccess}</p>
          </div>
        )}

        {/* Lista de itens */}
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-gray-400">Carregando itens...</p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredItems.map((item) => {
              const canAfford = currentPlayer &&
                (!item.price.dinheiro || (currentPlayer?.dinheiro || 0) >= item.price.dinheiro) &&
                (!item.price.shacks || (currentPlayer?.shack || 0) >= item.price.shacks);
              
              const isPurchasing = purchasingItem === item.id;

              return (
                <div key={item.id} className="bg-gray-800 rounded-lg p-4 border border-gray-600">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">{item.icon}</div>
                      <div>
                        <h4 className="font-bold text-white">{item.displayName}</h4>
                        <p className="text-xs text-gray-400">{item.description}</p>
                        <div className="text-xs text-blue-400 mt-1">
                          Categoria: {item.category}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      {item.price.dinheiro && (
                        <div className="text-sm text-green-400">
                          ${item.price.dinheiro.toLocaleString()}
                        </div>
                      )}
                      {item.price.shacks && (
                        <div className="text-xs text-blue-400">
                          {item.price.shacks} Shacks
                        </div>
                      )}
                    </div>
                  </div>

                  <button
                    onClick={() => handlePurchase(item)}
                    disabled={!canAfford || !item.available || isPurchasing}
                    className={`w-full py-2 rounded-lg font-semibold text-sm ${
                      !item.available
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : canAfford && !isPurchasing
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {isPurchasing ? 'Comprando...' : 
                     !item.available ? 'Indisponível' :
                     canAfford ? 'Comprar' : 'Recursos Insuficientes'}
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0">
        <div className="flex justify-center">
          <button 
            onClick={() => window.history.back()}
            className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
          >
            <span className="text-lg">←</span>
            <span className="text-sm">Voltar</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ShopPage;
