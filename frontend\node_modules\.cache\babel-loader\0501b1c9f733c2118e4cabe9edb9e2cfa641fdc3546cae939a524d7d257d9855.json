{"ast": null, "code": "import axios from 'axios';\n\n// Configuração do cliente HTTP para o backend Flask\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Interceptor para adicionar token de autenticação\napiClient.interceptors.request.use(config => {\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Interceptor para tratar respostas\napiClient.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expirado ou inválido\n    localStorage.removeItem('auth_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport class BackendService {\n  constructor() {\n    this.isBackendAvailable = true;\n  }\n  // Verificar se o backend está disponível\n  async checkBackendHealth() {\n    try {\n      console.log('[BackendService] Verificando saúde do backend...');\n\n      // TEMPORÁRIO: Forçar uso de dados mock devido a problemas com o backend\n      console.warn('[BackendService] Forçando uso de dados mock (modo debug)');\n      this.isBackendAvailable = false;\n      return false;\n\n      // Código original comentado temporariamente\n      /*\n      const response = await apiClient.get('/', { timeout: 5000 });\n      const isHealthy = response.status === 200;\n       console.log(`[BackendService] Backend health check: ${isHealthy ? 'OK' : 'FAIL'}`);\n      this.isBackendAvailable = isHealthy;\n      return isHealthy;\n      */\n    } catch (error) {\n      console.warn('[BackendService] Backend não disponível, usando dados mock:', error);\n      this.isBackendAvailable = false;\n      return false;\n    }\n  }\n\n  // ==================== AUTENTICAÇÃO ====================\n\n  async login(email, password) {\n    try {\n      console.log('[BackendService] Iniciando login:', {\n        email\n      });\n\n      // Verificar se backend está disponível\n      const backendOk = await this.checkBackendHealth();\n      console.log('[BackendService] Backend disponível:', backendOk);\n      if (!backendOk) {\n        console.log('[BackendService] Usando fallback mock para login');\n        return this.mockLogin(email, password);\n      }\n      console.log('[BackendService] Enviando requisição de login para backend');\n      const response = await apiClient.post('/api/auth/login', {\n        email,\n        password\n      });\n      console.log('[BackendService] Resposta do backend:', response.data);\n      if (response.data.sucesso && response.data.token) {\n        const token = response.data.token;\n\n        // Salvar token para próximas requisições\n        localStorage.setItem('auth_token', token);\n        console.log('[BackendService] Token salvo, buscando dados do jogador');\n\n        // Buscar dados do jogador após login\n        const playerResult = await this.getPlayer();\n        if (playerResult.success && playerResult.player) {\n          console.log('[BackendService] Login bem-sucedido:', playerResult.player);\n          return {\n            success: true,\n            token,\n            player: playerResult.player\n          };\n        }\n      }\n      console.error('[BackendService] Erro no login:', response.data);\n      return {\n        success: false,\n        error: response.data.mensagem || 'Erro no login'\n      };\n    } catch (error) {\n      console.error('[BackendService] Erro na requisição de login:', error);\n      console.warn('[BackendService] Usando fallback mock para login');\n      return this.mockLogin(email, password);\n    }\n  }\n  async register(nick, email, password) {\n    try {\n      console.log('[BackendService] Iniciando registro:', {\n        nick,\n        email\n      });\n\n      // Verificar se backend está disponível\n      const backendOk = await this.checkBackendHealth();\n      console.log('[BackendService] Backend disponível:', backendOk);\n      if (!backendOk) {\n        console.log('[BackendService] Usando fallback mock para registro');\n        return this.mockRegister(nick, email, password);\n      }\n      console.log('[BackendService] Enviando requisição de registro para backend');\n      const response = await apiClient.post('/api/auth/register', {\n        nick,\n        email,\n        password\n      });\n      console.log('[BackendService] Resposta do backend:', response.data);\n      if (response.data.sucesso && response.data.token) {\n        const token = response.data.token;\n\n        // Salvar token para próximas requisições\n        localStorage.setItem('auth_token', token);\n        console.log('[BackendService] Token salvo, buscando dados do jogador');\n\n        // Buscar dados do jogador após registro\n        const playerResult = await this.getPlayer();\n        if (playerResult.success && playerResult.player) {\n          console.log('[BackendService] Registro bem-sucedido:', playerResult.player);\n          return {\n            success: true,\n            token,\n            player: playerResult.player\n          };\n        }\n      }\n      console.error('[BackendService] Erro no registro:', response.data);\n      return {\n        success: false,\n        error: response.data.mensagem || 'Erro no registro'\n      };\n    } catch (error) {\n      console.error('[BackendService] Erro na requisição de registro:', error);\n      console.warn('[BackendService] Usando fallback mock para registro');\n      return this.mockRegister(nick, email, password);\n    }\n  }\n\n  // ==================== JOGADOR ====================\n\n  async getPlayer() {\n    try {\n      console.log('[BackendService] getPlayer: Iniciando...');\n\n      // Se não há token, retornar erro\n      const token = localStorage.getItem('auth_token');\n      console.log('[BackendService] getPlayer: Token encontrado:', !!token, (token === null || token === void 0 ? void 0 : token.substring(0, 20)) + '...');\n      if (!token) {\n        console.error('[BackendService] getPlayer: Token não encontrado');\n        return {\n          success: false,\n          error: 'Token não encontrado'\n        };\n      }\n\n      // Se é um token mock, retornar dados mock\n      if (token.startsWith('mock-token-')) {\n        console.log('[BackendService] getPlayer: Usando dados mock (token mock detectado)');\n        const mockResult = await this.getMockPlayer();\n        console.log('[BackendService] getPlayer: Dados mock retornados:', mockResult);\n        return mockResult;\n      }\n      console.log('[BackendService] getPlayer: Tentando carregar do backend...');\n      const response = await apiClient.get('/api/jogador');\n      if (response.data.sucesso) {\n        console.log('[BackendService] getPlayer: Dados do backend carregados com sucesso');\n        return {\n          success: true,\n          player: response.data.jogador\n        };\n      }\n      console.error('[BackendService] getPlayer: Backend retornou erro:', response.data.mensagem);\n      return {\n        success: false,\n        error: response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('[BackendService] getPlayer: Erro na requisição:', error);\n\n      // Fallback para dados mock se houver erro\n      const token = localStorage.getItem('auth_token');\n      if (token) {\n        console.warn('[BackendService] getPlayer: Usando fallback mock devido ao erro');\n        const mockResult = await this.getMockPlayer();\n        console.log('[BackendService] getPlayer: Fallback mock retornado:', mockResult);\n        return mockResult;\n      }\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.mensagem) || 'Erro ao carregar jogador'\n      };\n    }\n  }\n\n  // ==================== UPGRADES ====================\n\n  async upgradeApp(appName, quantity = 1) {\n    try {\n      // Mapear nomes dos apps do React para o backend\n      const appMapping = {\n        antivirus: 'antivirus',\n        bankguard: 'bankguard',\n        bruteforce: 'bruteforce',\n        sdk: 'cpu',\n        // SDK no React = CPU no backend\n        firewall: 'firewall',\n        malwarekit: 'malware_kit',\n        proxyvpn: 'proxyvpn',\n        mineradora: 'mineradora' // Novo app\n      };\n      const backendAppName = appMapping[appName] || appName;\n      const response = await apiClient.post('/api/appstore/comprar', {\n        item: backendAppName,\n        quantidade: quantity\n      });\n      return {\n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      return {\n        success: false,\n        error: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.mensagem) || 'Erro ao fazer upgrade'\n      };\n    }\n  }\n\n  // ==================== SCANNER ====================\n\n  async scanTargets() {\n    try {\n      const response = await apiClient.get('/api/scan');\n      return {\n        success: response.data.sucesso,\n        targets: response.data.alvos || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      return {\n        success: false,\n        error: ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.mensagem) || 'Erro ao escanear'\n      };\n    }\n  }\n\n  // ==================== CHAT ====================\n\n  async getChatMessages() {\n    try {\n      const response = await apiClient.get('/api/chat/messages');\n      return {\n        success: response.data.sucesso,\n        messages: response.data.mensagens || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      return {\n        success: false,\n        error: ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.mensagem) || 'Erro ao carregar chat'\n      };\n    }\n  }\n  async sendChatMessage(message) {\n    try {\n      const response = await apiClient.post('/api/chat/send', {\n        mensagem: message\n      });\n      return {\n        success: response.data.sucesso,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      return {\n        success: false,\n        error: ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.mensagem) || 'Erro ao enviar mensagem'\n      };\n    }\n  }\n\n  // ==================== HACKING ====================\n\n  async exploitTarget(targetData) {\n    try {\n      const response = await apiClient.post('/api/transferir', targetData);\n      return {\n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      return {\n        success: false,\n        error: ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.mensagem) || 'Erro ao exploitar alvo'\n      };\n    }\n  }\n\n  // ==================== UTILITÁRIOS ====================\n\n  async testConnection() {\n    try {\n      const response = await apiClient.get('/api/status');\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Erro de conexão com o servidor'\n      };\n    }\n  }\n\n  // Converter jogador do backend para formato do React\n  convertBackendPlayer(backendPlayer) {\n    return {\n      uid: backendPlayer.uid,\n      nick: backendPlayer.nick,\n      email: backendPlayer.email,\n      ip: backendPlayer.ip,\n      level: backendPlayer.nivel,\n      xp: backendPlayer.xp,\n      xpToNextLevel: 100,\n      // Será calculado depois\n      cash: backendPlayer.dinheiro,\n      shack: backendPlayer.shack || 0,\n      // Nova moeda\n      createdAt: backendPlayer.created_at,\n      lastLogin: backendPlayer.last_login\n    };\n  }\n\n  // Converter apps do backend para formato do React\n  convertBackendApps(backendPlayer) {\n    return {\n      antivirus: backendPlayer.antivirus || 1,\n      bankguard: backendPlayer.bankguard || 1,\n      bruteforce: backendPlayer.bruteforce || 1,\n      sdk: backendPlayer.cpu || 1,\n      // CPU no backend = SDK no React\n      firewall: backendPlayer.firewall || 1,\n      malware_kit: backendPlayer.malware_kit || 1,\n      proxyvpn: backendPlayer.proxyvpn || 1,\n      mineradora: backendPlayer.mineradora || 1 // Novo app\n    };\n  }\n  // ==================== MÉTODOS MOCK (FALLBACK) ====================\n\n  async mockLogin(email, password) {\n    // Simular delay de rede\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Dados mock do jogador\n    const mockPlayer = {\n      uid: 'test-user-123',\n      nick: 'TestPlayer',\n      email: email,\n      ip: '*************',\n      nivel: 5,\n      xp: 250,\n      dinheiro: 150,\n      shack: 25,\n      // Shack currency\n      antivirus: 3,\n      bankguard: 2,\n      bruteforce: 4,\n      cpu: 2,\n      firewall: 3,\n      malware_kit: 2,\n      proxyvpn: 1,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n    const token = 'mock-token-' + Date.now();\n    localStorage.setItem('auth_token', token);\n    return {\n      success: true,\n      token,\n      player: mockPlayer\n    };\n  }\n  async mockRegister(nick, email, password) {\n    // Simular delay de rede\n    await new Promise(resolve => setTimeout(resolve, 1500));\n\n    // Dados mock do novo jogador\n    const mockPlayer = {\n      uid: 'new-user-' + Date.now(),\n      nick: nick,\n      email: email,\n      ip: '192.168.1.' + Math.floor(Math.random() * 255),\n      nivel: 1,\n      xp: 0,\n      dinheiro: 10,\n      shack: 0,\n      // Novo jogador começa sem shack\n      antivirus: 1,\n      bankguard: 1,\n      bruteforce: 1,\n      cpu: 1,\n      firewall: 1,\n      malware_kit: 1,\n      proxyvpn: 1,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n    const token = 'mock-token-' + Date.now();\n    localStorage.setItem('auth_token', token);\n    return {\n      success: true,\n      token,\n      player: mockPlayer\n    };\n  }\n  async getMockPlayer() {\n    // Dados mock persistentes baseados no token\n    const token = localStorage.getItem('auth_token') || '';\n    const isNewUser = token.includes('new-user');\n    const mockPlayer = {\n      uid: isNewUser ? 'new-user-123' : 'test-user-123',\n      nick: isNewUser ? 'NewPlayer' : 'TestPlayer',\n      email: isNewUser ? '<EMAIL>' : '<EMAIL>',\n      ip: '*************',\n      nivel: isNewUser ? 1 : 5,\n      xp: isNewUser ? 0 : 250,\n      dinheiro: isNewUser ? 10 : 150,\n      shack: isNewUser ? 0 : 25,\n      // Shack currency\n      antivirus: isNewUser ? 1 : 3,\n      bankguard: isNewUser ? 1 : 2,\n      bruteforce: isNewUser ? 1 : 4,\n      cpu: isNewUser ? 1 : 2,\n      firewall: isNewUser ? 1 : 3,\n      malware_kit: isNewUser ? 1 : 2,\n      proxyvpn: isNewUser ? 1 : 1,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n    return {\n      success: true,\n      player: mockPlayer\n    };\n  }\n}\nexport const backendService = new BackendService();", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "apiClient", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "response", "error", "_error$response", "status", "removeItem", "window", "location", "href", "Promise", "reject", "BackendService", "constructor", "isBackendAvailable", "checkBackendHealth", "console", "log", "warn", "login", "email", "password", "backendOk", "mockLogin", "post", "data", "sucesso", "setItem", "player<PERSON><PERSON><PERSON>", "getPlayer", "success", "player", "mensagem", "register", "nick", "mockRegister", "substring", "startsWith", "mockResult", "getMockPlayer", "get", "jogador", "_error$response2", "_error$response2$data", "upgradeApp", "appName", "quantity", "appMapping", "antivirus", "bankguard", "bruteforce", "sdk", "firewall", "malwarekit", "proxyvpn", "mineradora", "backendAppName", "item", "quantidade", "undefined", "_error$response3", "_error$response3$data", "scanTargets", "targets", "alvos", "_error$response4", "_error$response4$data", "getChatMessages", "messages", "mensagens", "_error$response5", "_error$response5$data", "sendChatMessage", "message", "_error$response6", "_error$response6$data", "exploitTarget", "targetData", "_error$response7", "_error$response7$data", "testConnection", "convertBackendPlayer", "backendPlayer", "uid", "ip", "level", "nivel", "xp", "xpToNextLevel", "cash", "<PERSON><PERSON><PERSON>", "shack", "createdAt", "created_at", "lastLogin", "last_login", "convertBackendApps", "cpu", "malware_kit", "resolve", "setTimeout", "mockPlayer", "Date", "toISOString", "now", "Math", "floor", "random", "isNewUser", "includes", "backendService"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/backendService.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Configuração do cliente HTTP para o backend Flask\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Interceptor para adicionar token de autenticação\napiClient.interceptors.request.use((config) => {\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Interceptor para tratar respostas\napiClient.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expirado ou inválido\n      localStorage.removeItem('auth_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport interface BackendPlayer {\n  uid: string;\n  nick: string;\n  email: string;\n  ip: string;\n  nivel: number;\n  xp: number;\n  dinheiro: number;\n  shack?: number; // Shack currency\n  // Apps\n  cpu: number;\n  firewall: number;\n  antivirus: number;\n  malware_kit: number;\n  bruteforce: number;\n  bankguard: number;\n  proxyvpn: number;\n  // Timestamps\n  created_at: string;\n  last_login: string;\n}\n\nexport interface UpgradeResponse {\n  sucesso: boolean;\n  mensagem: string;\n  nivel_novo?: number;\n  custo_dinheiro?: number;\n  custo_shacks?: number;\n  xp_ganho?: number;\n  nivel_jogador?: number;\n  level_up?: boolean;\n}\n\nexport class BackendService {\n  private isBackendAvailable = true;\n\n  // Verificar se o backend está disponível\n  private async checkBackendHealth(): Promise<boolean> {\n    try {\n      console.log('[BackendService] Verificando saúde do backend...');\n\n      // TEMPORÁRIO: Forçar uso de dados mock devido a problemas com o backend\n      console.warn('[BackendService] Forçando uso de dados mock (modo debug)');\n      this.isBackendAvailable = false;\n      return false;\n\n      // Código original comentado temporariamente\n      /*\n      const response = await apiClient.get('/', { timeout: 5000 });\n      const isHealthy = response.status === 200;\n\n      console.log(`[BackendService] Backend health check: ${isHealthy ? 'OK' : 'FAIL'}`);\n      this.isBackendAvailable = isHealthy;\n      return isHealthy;\n      */\n    } catch (error) {\n      console.warn('[BackendService] Backend não disponível, usando dados mock:', error);\n      this.isBackendAvailable = false;\n      return false;\n    }\n  }\n\n  // ==================== AUTENTICAÇÃO ====================\n  \n  async login(email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {\n    try {\n      console.log('[BackendService] Iniciando login:', { email });\n\n      // Verificar se backend está disponível\n      const backendOk = await this.checkBackendHealth();\n      console.log('[BackendService] Backend disponível:', backendOk);\n\n      if (!backendOk) {\n        console.log('[BackendService] Usando fallback mock para login');\n        return this.mockLogin(email, password);\n      }\n\n      console.log('[BackendService] Enviando requisição de login para backend');\n      const response = await apiClient.post('/api/auth/login', {\n        email,\n        password,\n      });\n\n      console.log('[BackendService] Resposta do backend:', response.data);\n\n      if (response.data.sucesso && response.data.token) {\n        const token = response.data.token;\n\n        // Salvar token para próximas requisições\n        localStorage.setItem('auth_token', token);\n        console.log('[BackendService] Token salvo, buscando dados do jogador');\n\n        // Buscar dados do jogador após login\n        const playerResult = await this.getPlayer();\n\n        if (playerResult.success && playerResult.player) {\n          console.log('[BackendService] Login bem-sucedido:', playerResult.player);\n          return {\n            success: true,\n            token,\n            player: playerResult.player\n          };\n        }\n      }\n\n      console.error('[BackendService] Erro no login:', response.data);\n      return { success: false, error: response.data.mensagem || 'Erro no login' };\n    } catch (error: any) {\n      console.error('[BackendService] Erro na requisição de login:', error);\n      console.warn('[BackendService] Usando fallback mock para login');\n      return this.mockLogin(email, password);\n    }\n  }\n\n  async register(nick: string, email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {\n    try {\n      console.log('[BackendService] Iniciando registro:', { nick, email });\n\n      // Verificar se backend está disponível\n      const backendOk = await this.checkBackendHealth();\n      console.log('[BackendService] Backend disponível:', backendOk);\n\n      if (!backendOk) {\n        console.log('[BackendService] Usando fallback mock para registro');\n        return this.mockRegister(nick, email, password);\n      }\n\n      console.log('[BackendService] Enviando requisição de registro para backend');\n      const response = await apiClient.post('/api/auth/register', {\n        nick,\n        email,\n        password,\n      });\n\n      console.log('[BackendService] Resposta do backend:', response.data);\n\n      if (response.data.sucesso && response.data.token) {\n        const token = response.data.token;\n\n        // Salvar token para próximas requisições\n        localStorage.setItem('auth_token', token);\n        console.log('[BackendService] Token salvo, buscando dados do jogador');\n\n        // Buscar dados do jogador após registro\n        const playerResult = await this.getPlayer();\n\n        if (playerResult.success && playerResult.player) {\n          console.log('[BackendService] Registro bem-sucedido:', playerResult.player);\n          return {\n            success: true,\n            token,\n            player: playerResult.player\n          };\n        }\n      }\n\n      console.error('[BackendService] Erro no registro:', response.data);\n      return {\n        success: false,\n        error: response.data.mensagem || 'Erro no registro'\n      };\n    } catch (error: any) {\n      console.error('[BackendService] Erro na requisição de registro:', error);\n      console.warn('[BackendService] Usando fallback mock para registro');\n      return this.mockRegister(nick, email, password);\n    }\n  }\n\n  // ==================== JOGADOR ====================\n\n  async getPlayer(): Promise<{ success: boolean; player?: BackendPlayer; error?: string }> {\n    try {\n      console.log('[BackendService] getPlayer: Iniciando...');\n\n      // Se não há token, retornar erro\n      const token = localStorage.getItem('auth_token');\n      console.log('[BackendService] getPlayer: Token encontrado:', !!token, token?.substring(0, 20) + '...');\n\n      if (!token) {\n        console.error('[BackendService] getPlayer: Token não encontrado');\n        return { success: false, error: 'Token não encontrado' };\n      }\n\n      // Se é um token mock, retornar dados mock\n      if (token.startsWith('mock-token-')) {\n        console.log('[BackendService] getPlayer: Usando dados mock (token mock detectado)');\n        const mockResult = await this.getMockPlayer();\n        console.log('[BackendService] getPlayer: Dados mock retornados:', mockResult);\n        return mockResult;\n      }\n\n      console.log('[BackendService] getPlayer: Tentando carregar do backend...');\n      const response = await apiClient.get('/api/jogador');\n\n      if (response.data.sucesso) {\n        console.log('[BackendService] getPlayer: Dados do backend carregados com sucesso');\n        return { success: true, player: response.data.jogador };\n      }\n\n      console.error('[BackendService] getPlayer: Backend retornou erro:', response.data.mensagem);\n      return { success: false, error: response.data.mensagem };\n    } catch (error: any) {\n      console.error('[BackendService] getPlayer: Erro na requisição:', error);\n\n      // Fallback para dados mock se houver erro\n      const token = localStorage.getItem('auth_token');\n      if (token) {\n        console.warn('[BackendService] getPlayer: Usando fallback mock devido ao erro');\n        const mockResult = await this.getMockPlayer();\n        console.log('[BackendService] getPlayer: Fallback mock retornado:', mockResult);\n        return mockResult;\n      }\n\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao carregar jogador' };\n    }\n  }\n\n  // ==================== UPGRADES ====================\n\n  async upgradeApp(appName: string, quantity: number = 1): Promise<{ success: boolean; data?: UpgradeResponse; error?: string }> {\n    try {\n      // Mapear nomes dos apps do React para o backend\n      const appMapping: { [key: string]: string } = {\n        antivirus: 'antivirus',\n        bankguard: 'bankguard',\n        bruteforce: 'bruteforce',\n        sdk: 'cpu', // SDK no React = CPU no backend\n        firewall: 'firewall',\n        malwarekit: 'malware_kit',\n        proxyvpn: 'proxyvpn',\n        mineradora: 'mineradora', // Novo app\n      };\n\n      const backendAppName = appMapping[appName] || appName;\n\n      const response = await apiClient.post('/api/appstore/comprar', {\n        item: backendAppName,\n        quantidade: quantity,\n      });\n\n      return { \n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao fazer upgrade' };\n    }\n  }\n\n  // ==================== SCANNER ====================\n\n  async scanTargets(): Promise<{ success: boolean; targets?: any[]; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/scan');\n      \n      return { \n        success: response.data.sucesso,\n        targets: response.data.alvos || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao escanear' };\n    }\n  }\n\n  // ==================== CHAT ====================\n\n  async getChatMessages(): Promise<{ success: boolean; messages?: any[]; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/chat/messages');\n      \n      return { \n        success: response.data.sucesso,\n        messages: response.data.mensagens || [],\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao carregar chat' };\n    }\n  }\n\n  async sendChatMessage(message: string): Promise<{ success: boolean; error?: string }> {\n    try {\n      const response = await apiClient.post('/api/chat/send', {\n        mensagem: message,\n      });\n\n      return { \n        success: response.data.sucesso,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao enviar mensagem' };\n    }\n  }\n\n  // ==================== HACKING ====================\n\n  async exploitTarget(targetData: any): Promise<{ success: boolean; data?: any; error?: string }> {\n    try {\n      const response = await apiClient.post('/api/transferir', targetData);\n      \n      return { \n        success: response.data.sucesso,\n        data: response.data,\n        error: response.data.sucesso ? undefined : response.data.mensagem\n      };\n    } catch (error: any) {\n      return { success: false, error: error.response?.data?.mensagem || 'Erro ao exploitar alvo' };\n    }\n  }\n\n  // ==================== UTILITÁRIOS ====================\n\n  async testConnection(): Promise<{ success: boolean; error?: string }> {\n    try {\n      const response = await apiClient.get('/api/status');\n      return { success: true };\n    } catch (error: any) {\n      return { success: false, error: 'Erro de conexão com o servidor' };\n    }\n  }\n\n  // Converter jogador do backend para formato do React\n  convertBackendPlayer(backendPlayer: BackendPlayer): any {\n    return {\n      uid: backendPlayer.uid,\n      nick: backendPlayer.nick,\n      email: backendPlayer.email,\n      ip: backendPlayer.ip,\n      level: backendPlayer.nivel,\n      xp: backendPlayer.xp,\n      xpToNextLevel: 100, // Será calculado depois\n      cash: backendPlayer.dinheiro,\n      shack: (backendPlayer as any).shack || 0, // Nova moeda\n      createdAt: backendPlayer.created_at,\n      lastLogin: backendPlayer.last_login,\n    };\n  }\n\n  // Converter apps do backend para formato do React\n  convertBackendApps(backendPlayer: BackendPlayer): any {\n    return {\n      antivirus: backendPlayer.antivirus || 1,\n      bankguard: backendPlayer.bankguard || 1,\n      bruteforce: backendPlayer.bruteforce || 1,\n      sdk: backendPlayer.cpu || 1, // CPU no backend = SDK no React\n      firewall: backendPlayer.firewall || 1,\n      malware_kit: backendPlayer.malware_kit || 1,\n      proxyvpn: backendPlayer.proxyvpn || 1,\n      mineradora: (backendPlayer as any).mineradora || 1, // Novo app\n    };\n  }\n  // ==================== MÉTODOS MOCK (FALLBACK) ====================\n\n  private async mockLogin(email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {\n    // Simular delay de rede\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Dados mock do jogador\n    const mockPlayer: BackendPlayer = {\n      uid: 'test-user-123',\n      nick: 'TestPlayer',\n      email: email,\n      ip: '*************',\n      nivel: 5,\n      xp: 250,\n      dinheiro: 150,\n      shack: 25, // Shack currency\n      antivirus: 3,\n      bankguard: 2,\n      bruteforce: 4,\n      cpu: 2,\n      firewall: 3,\n      malware_kit: 2,\n      proxyvpn: 1,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString(),\n    };\n\n    const token = 'mock-token-' + Date.now();\n    localStorage.setItem('auth_token', token);\n\n    return {\n      success: true,\n      token,\n      player: mockPlayer\n    };\n  }\n\n  private async mockRegister(nick: string, email: string, password: string): Promise<{ success: boolean; token?: string; player?: BackendPlayer; error?: string }> {\n    // Simular delay de rede\n    await new Promise(resolve => setTimeout(resolve, 1500));\n\n    // Dados mock do novo jogador\n    const mockPlayer: BackendPlayer = {\n      uid: 'new-user-' + Date.now(),\n      nick: nick,\n      email: email,\n      ip: '192.168.1.' + Math.floor(Math.random() * 255),\n      nivel: 1,\n      xp: 0,\n      dinheiro: 10,\n      shack: 0, // Novo jogador começa sem shack\n      antivirus: 1,\n      bankguard: 1,\n      bruteforce: 1,\n      cpu: 1,\n      firewall: 1,\n      malware_kit: 1,\n      proxyvpn: 1,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString(),\n    };\n\n    const token = 'mock-token-' + Date.now();\n    localStorage.setItem('auth_token', token);\n\n    return {\n      success: true,\n      token,\n      player: mockPlayer\n    };\n  }\n\n  private async getMockPlayer(): Promise<{ success: boolean; player?: BackendPlayer; error?: string }> {\n    // Dados mock persistentes baseados no token\n    const token = localStorage.getItem('auth_token') || '';\n    const isNewUser = token.includes('new-user');\n\n    const mockPlayer: BackendPlayer = {\n      uid: isNewUser ? 'new-user-123' : 'test-user-123',\n      nick: isNewUser ? 'NewPlayer' : 'TestPlayer',\n      email: isNewUser ? '<EMAIL>' : '<EMAIL>',\n      ip: '*************',\n      nivel: isNewUser ? 1 : 5,\n      xp: isNewUser ? 0 : 250,\n      dinheiro: isNewUser ? 10 : 150,\n      shack: isNewUser ? 0 : 25, // Shack currency\n      antivirus: isNewUser ? 1 : 3,\n      bankguard: isNewUser ? 1 : 2,\n      bruteforce: isNewUser ? 1 : 4,\n      cpu: isNewUser ? 1 : 2,\n      firewall: isNewUser ? 1 : 3,\n      malware_kit: isNewUser ? 1 : 2,\n      proxyvpn: isNewUser ? 1 : 1,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString(),\n    };\n\n    return {\n      success: true,\n      player: mockPlayer\n    };\n  }\n}\n\nexport const backendService = new BackendService();\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,SAAS,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC7BC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,SAAS,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EAC7C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;;AAEF;AACAR,SAAS,CAACK,YAAY,CAACQ,QAAQ,CAACN,GAAG,CAChCM,QAAQ,IAAKA,QAAQ,EACrBC,KAAK,IAAK;EAAA,IAAAC,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAD,KAAK,CAACD,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAN,YAAY,CAACO,UAAU,CAAC,YAAY,CAAC;IACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOC,OAAO,CAACC,MAAM,CAACR,KAAK,CAAC;AAC9B,CACF,CAAC;AAmCD,OAAO,MAAMS,cAAc,CAAC;EAAAC,YAAA;IAAA,KAClBC,kBAAkB,GAAG,IAAI;EAAA;EAEjC;EACA,MAAcC,kBAAkBA,CAAA,EAAqB;IACnD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;MAE/D;MACAD,OAAO,CAACE,IAAI,CAAC,0DAA0D,CAAC;MACxE,IAAI,CAACJ,kBAAkB,GAAG,KAAK;MAC/B,OAAO,KAAK;;MAEZ;MACA;AACN;AACA;AACA;AACA;AACA;AACA;IAEI,CAAC,CAAC,OAAOX,KAAK,EAAE;MACda,OAAO,CAACE,IAAI,CAAC,6DAA6D,EAAEf,KAAK,CAAC;MAClF,IAAI,CAACW,kBAAkB,GAAG,KAAK;MAC/B,OAAO,KAAK;IACd;EACF;;EAEA;;EAEA,MAAMK,KAAKA,CAACC,KAAa,EAAEC,QAAgB,EAAyF;IAClI,IAAI;MACFL,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;QAAEG;MAAM,CAAC,CAAC;;MAE3D;MACA,MAAME,SAAS,GAAG,MAAM,IAAI,CAACP,kBAAkB,CAAC,CAAC;MACjDC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEK,SAAS,CAAC;MAE9D,IAAI,CAACA,SAAS,EAAE;QACdN,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAC/D,OAAO,IAAI,CAACM,SAAS,CAACH,KAAK,EAAEC,QAAQ,CAAC;MACxC;MAEAL,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,MAAMf,QAAQ,GAAG,MAAMb,SAAS,CAACmC,IAAI,CAAC,iBAAiB,EAAE;QACvDJ,KAAK;QACLC;MACF,CAAC,CAAC;MAEFL,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEf,QAAQ,CAACuB,IAAI,CAAC;MAEnE,IAAIvB,QAAQ,CAACuB,IAAI,CAACC,OAAO,IAAIxB,QAAQ,CAACuB,IAAI,CAAC3B,KAAK,EAAE;QAChD,MAAMA,KAAK,GAAGI,QAAQ,CAACuB,IAAI,CAAC3B,KAAK;;QAEjC;QACAC,YAAY,CAAC4B,OAAO,CAAC,YAAY,EAAE7B,KAAK,CAAC;QACzCkB,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;;QAEtE;QACA,MAAMW,YAAY,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC,CAAC;QAE3C,IAAID,YAAY,CAACE,OAAO,IAAIF,YAAY,CAACG,MAAM,EAAE;UAC/Cf,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEW,YAAY,CAACG,MAAM,CAAC;UACxE,OAAO;YACLD,OAAO,EAAE,IAAI;YACbhC,KAAK;YACLiC,MAAM,EAAEH,YAAY,CAACG;UACvB,CAAC;QACH;MACF;MAEAf,OAAO,CAACb,KAAK,CAAC,iCAAiC,EAAED,QAAQ,CAACuB,IAAI,CAAC;MAC/D,OAAO;QAAEK,OAAO,EAAE,KAAK;QAAE3B,KAAK,EAAED,QAAQ,CAACuB,IAAI,CAACO,QAAQ,IAAI;MAAgB,CAAC;IAC7E,CAAC,CAAC,OAAO7B,KAAU,EAAE;MACnBa,OAAO,CAACb,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrEa,OAAO,CAACE,IAAI,CAAC,kDAAkD,CAAC;MAChE,OAAO,IAAI,CAACK,SAAS,CAACH,KAAK,EAAEC,QAAQ,CAAC;IACxC;EACF;EAEA,MAAMY,QAAQA,CAACC,IAAY,EAAEd,KAAa,EAAEC,QAAgB,EAAyF;IACnJ,IAAI;MACFL,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAAEiB,IAAI;QAAEd;MAAM,CAAC,CAAC;;MAEpE;MACA,MAAME,SAAS,GAAG,MAAM,IAAI,CAACP,kBAAkB,CAAC,CAAC;MACjDC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEK,SAAS,CAAC;MAE9D,IAAI,CAACA,SAAS,EAAE;QACdN,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAClE,OAAO,IAAI,CAACkB,YAAY,CAACD,IAAI,EAAEd,KAAK,EAAEC,QAAQ,CAAC;MACjD;MAEAL,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;MAC5E,MAAMf,QAAQ,GAAG,MAAMb,SAAS,CAACmC,IAAI,CAAC,oBAAoB,EAAE;QAC1DU,IAAI;QACJd,KAAK;QACLC;MACF,CAAC,CAAC;MAEFL,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEf,QAAQ,CAACuB,IAAI,CAAC;MAEnE,IAAIvB,QAAQ,CAACuB,IAAI,CAACC,OAAO,IAAIxB,QAAQ,CAACuB,IAAI,CAAC3B,KAAK,EAAE;QAChD,MAAMA,KAAK,GAAGI,QAAQ,CAACuB,IAAI,CAAC3B,KAAK;;QAEjC;QACAC,YAAY,CAAC4B,OAAO,CAAC,YAAY,EAAE7B,KAAK,CAAC;QACzCkB,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;;QAEtE;QACA,MAAMW,YAAY,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC,CAAC;QAE3C,IAAID,YAAY,CAACE,OAAO,IAAIF,YAAY,CAACG,MAAM,EAAE;UAC/Cf,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEW,YAAY,CAACG,MAAM,CAAC;UAC3E,OAAO;YACLD,OAAO,EAAE,IAAI;YACbhC,KAAK;YACLiC,MAAM,EAAEH,YAAY,CAACG;UACvB,CAAC;QACH;MACF;MAEAf,OAAO,CAACb,KAAK,CAAC,oCAAoC,EAAED,QAAQ,CAACuB,IAAI,CAAC;MAClE,OAAO;QACLK,OAAO,EAAE,KAAK;QACd3B,KAAK,EAAED,QAAQ,CAACuB,IAAI,CAACO,QAAQ,IAAI;MACnC,CAAC;IACH,CAAC,CAAC,OAAO7B,KAAU,EAAE;MACnBa,OAAO,CAACb,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxEa,OAAO,CAACE,IAAI,CAAC,qDAAqD,CAAC;MACnE,OAAO,IAAI,CAACiB,YAAY,CAACD,IAAI,EAAEd,KAAK,EAAEC,QAAQ,CAAC;IACjD;EACF;;EAEA;;EAEA,MAAMQ,SAASA,CAAA,EAA0E;IACvF,IAAI;MACFb,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,MAAMnB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChDgB,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE,CAAC,CAACnB,KAAK,EAAE,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEsC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAG,KAAK,CAAC;MAEtG,IAAI,CAACtC,KAAK,EAAE;QACVkB,OAAO,CAACb,KAAK,CAAC,kDAAkD,CAAC;QACjE,OAAO;UAAE2B,OAAO,EAAE,KAAK;UAAE3B,KAAK,EAAE;QAAuB,CAAC;MAC1D;;MAEA;MACA,IAAIL,KAAK,CAACuC,UAAU,CAAC,aAAa,CAAC,EAAE;QACnCrB,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;QACnF,MAAMqB,UAAU,GAAG,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC;QAC7CvB,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEqB,UAAU,CAAC;QAC7E,OAAOA,UAAU;MACnB;MAEAtB,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1E,MAAMf,QAAQ,GAAG,MAAMb,SAAS,CAACmD,GAAG,CAAC,cAAc,CAAC;MAEpD,IAAItC,QAAQ,CAACuB,IAAI,CAACC,OAAO,EAAE;QACzBV,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;QAClF,OAAO;UAAEa,OAAO,EAAE,IAAI;UAAEC,MAAM,EAAE7B,QAAQ,CAACuB,IAAI,CAACgB;QAAQ,CAAC;MACzD;MAEAzB,OAAO,CAACb,KAAK,CAAC,oDAAoD,EAAED,QAAQ,CAACuB,IAAI,CAACO,QAAQ,CAAC;MAC3F,OAAO;QAAEF,OAAO,EAAE,KAAK;QAAE3B,KAAK,EAAED,QAAQ,CAACuB,IAAI,CAACO;MAAS,CAAC;IAC1D,CAAC,CAAC,OAAO7B,KAAU,EAAE;MAAA,IAAAuC,gBAAA,EAAAC,qBAAA;MACnB3B,OAAO,CAACb,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;;MAEvE;MACA,MAAML,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,IAAIF,KAAK,EAAE;QACTkB,OAAO,CAACE,IAAI,CAAC,iEAAiE,CAAC;QAC/E,MAAMoB,UAAU,GAAG,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC;QAC7CvB,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEqB,UAAU,CAAC;QAC/E,OAAOA,UAAU;MACnB;MAEA,OAAO;QAAER,OAAO,EAAE,KAAK;QAAE3B,KAAK,EAAE,EAAAuC,gBAAA,GAAAvC,KAAK,CAACD,QAAQ,cAAAwC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjB,IAAI,cAAAkB,qBAAA,uBAApBA,qBAAA,CAAsBX,QAAQ,KAAI;MAA2B,CAAC;IAChG;EACF;;EAEA;;EAEA,MAAMY,UAAUA,CAACC,OAAe,EAAEC,QAAgB,GAAG,CAAC,EAAyE;IAC7H,IAAI;MACF;MACA,MAAMC,UAAqC,GAAG;QAC5CC,SAAS,EAAE,WAAW;QACtBC,SAAS,EAAE,WAAW;QACtBC,UAAU,EAAE,YAAY;QACxBC,GAAG,EAAE,KAAK;QAAE;QACZC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,aAAa;QACzBC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,YAAY,CAAE;MAC5B,CAAC;MAED,MAAMC,cAAc,GAAGT,UAAU,CAACF,OAAO,CAAC,IAAIA,OAAO;MAErD,MAAM3C,QAAQ,GAAG,MAAMb,SAAS,CAACmC,IAAI,CAAC,uBAAuB,EAAE;QAC7DiC,IAAI,EAAED,cAAc;QACpBE,UAAU,EAAEZ;MACd,CAAC,CAAC;MAEF,OAAO;QACLhB,OAAO,EAAE5B,QAAQ,CAACuB,IAAI,CAACC,OAAO;QAC9BD,IAAI,EAAEvB,QAAQ,CAACuB,IAAI;QACnBtB,KAAK,EAAED,QAAQ,CAACuB,IAAI,CAACC,OAAO,GAAGiC,SAAS,GAAGzD,QAAQ,CAACuB,IAAI,CAACO;MAC3D,CAAC;IACH,CAAC,CAAC,OAAO7B,KAAU,EAAE;MAAA,IAAAyD,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAE/B,OAAO,EAAE,KAAK;QAAE3B,KAAK,EAAE,EAAAyD,gBAAA,GAAAzD,KAAK,CAACD,QAAQ,cAAA0D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnC,IAAI,cAAAoC,qBAAA,uBAApBA,qBAAA,CAAsB7B,QAAQ,KAAI;MAAwB,CAAC;IAC7F;EACF;;EAEA;;EAEA,MAAM8B,WAAWA,CAAA,EAAmE;IAClF,IAAI;MACF,MAAM5D,QAAQ,GAAG,MAAMb,SAAS,CAACmD,GAAG,CAAC,WAAW,CAAC;MAEjD,OAAO;QACLV,OAAO,EAAE5B,QAAQ,CAACuB,IAAI,CAACC,OAAO;QAC9BqC,OAAO,EAAE7D,QAAQ,CAACuB,IAAI,CAACuC,KAAK,IAAI,EAAE;QAClC7D,KAAK,EAAED,QAAQ,CAACuB,IAAI,CAACC,OAAO,GAAGiC,SAAS,GAAGzD,QAAQ,CAACuB,IAAI,CAACO;MAC3D,CAAC;IACH,CAAC,CAAC,OAAO7B,KAAU,EAAE;MAAA,IAAA8D,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEpC,OAAO,EAAE,KAAK;QAAE3B,KAAK,EAAE,EAAA8D,gBAAA,GAAA9D,KAAK,CAACD,QAAQ,cAAA+D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxC,IAAI,cAAAyC,qBAAA,uBAApBA,qBAAA,CAAsBlC,QAAQ,KAAI;MAAmB,CAAC;IACxF;EACF;;EAEA;;EAEA,MAAMmC,eAAeA,CAAA,EAAoE;IACvF,IAAI;MACF,MAAMjE,QAAQ,GAAG,MAAMb,SAAS,CAACmD,GAAG,CAAC,oBAAoB,CAAC;MAE1D,OAAO;QACLV,OAAO,EAAE5B,QAAQ,CAACuB,IAAI,CAACC,OAAO;QAC9B0C,QAAQ,EAAElE,QAAQ,CAACuB,IAAI,CAAC4C,SAAS,IAAI,EAAE;QACvClE,KAAK,EAAED,QAAQ,CAACuB,IAAI,CAACC,OAAO,GAAGiC,SAAS,GAAGzD,QAAQ,CAACuB,IAAI,CAACO;MAC3D,CAAC;IACH,CAAC,CAAC,OAAO7B,KAAU,EAAE;MAAA,IAAAmE,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEzC,OAAO,EAAE,KAAK;QAAE3B,KAAK,EAAE,EAAAmE,gBAAA,GAAAnE,KAAK,CAACD,QAAQ,cAAAoE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7C,IAAI,cAAA8C,qBAAA,uBAApBA,qBAAA,CAAsBvC,QAAQ,KAAI;MAAwB,CAAC;IAC7F;EACF;EAEA,MAAMwC,eAAeA,CAACC,OAAe,EAAiD;IACpF,IAAI;MACF,MAAMvE,QAAQ,GAAG,MAAMb,SAAS,CAACmC,IAAI,CAAC,gBAAgB,EAAE;QACtDQ,QAAQ,EAAEyC;MACZ,CAAC,CAAC;MAEF,OAAO;QACL3C,OAAO,EAAE5B,QAAQ,CAACuB,IAAI,CAACC,OAAO;QAC9BvB,KAAK,EAAED,QAAQ,CAACuB,IAAI,CAACC,OAAO,GAAGiC,SAAS,GAAGzD,QAAQ,CAACuB,IAAI,CAACO;MAC3D,CAAC;IACH,CAAC,CAAC,OAAO7B,KAAU,EAAE;MAAA,IAAAuE,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAE7C,OAAO,EAAE,KAAK;QAAE3B,KAAK,EAAE,EAAAuE,gBAAA,GAAAvE,KAAK,CAACD,QAAQ,cAAAwE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjD,IAAI,cAAAkD,qBAAA,uBAApBA,qBAAA,CAAsB3C,QAAQ,KAAI;MAA0B,CAAC;IAC/F;EACF;;EAEA;;EAEA,MAAM4C,aAAaA,CAACC,UAAe,EAA6D;IAC9F,IAAI;MACF,MAAM3E,QAAQ,GAAG,MAAMb,SAAS,CAACmC,IAAI,CAAC,iBAAiB,EAAEqD,UAAU,CAAC;MAEpE,OAAO;QACL/C,OAAO,EAAE5B,QAAQ,CAACuB,IAAI,CAACC,OAAO;QAC9BD,IAAI,EAAEvB,QAAQ,CAACuB,IAAI;QACnBtB,KAAK,EAAED,QAAQ,CAACuB,IAAI,CAACC,OAAO,GAAGiC,SAAS,GAAGzD,QAAQ,CAACuB,IAAI,CAACO;MAC3D,CAAC;IACH,CAAC,CAAC,OAAO7B,KAAU,EAAE;MAAA,IAAA2E,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QAAEjD,OAAO,EAAE,KAAK;QAAE3B,KAAK,EAAE,EAAA2E,gBAAA,GAAA3E,KAAK,CAACD,QAAQ,cAAA4E,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrD,IAAI,cAAAsD,qBAAA,uBAApBA,qBAAA,CAAsB/C,QAAQ,KAAI;MAAyB,CAAC;IAC9F;EACF;;EAEA;;EAEA,MAAMgD,cAAcA,CAAA,EAAkD;IACpE,IAAI;MACF,MAAM9E,QAAQ,GAAG,MAAMb,SAAS,CAACmD,GAAG,CAAC,aAAa,CAAC;MACnD,OAAO;QAAEV,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAO3B,KAAU,EAAE;MACnB,OAAO;QAAE2B,OAAO,EAAE,KAAK;QAAE3B,KAAK,EAAE;MAAiC,CAAC;IACpE;EACF;;EAEA;EACA8E,oBAAoBA,CAACC,aAA4B,EAAO;IACtD,OAAO;MACLC,GAAG,EAAED,aAAa,CAACC,GAAG;MACtBjD,IAAI,EAAEgD,aAAa,CAAChD,IAAI;MACxBd,KAAK,EAAE8D,aAAa,CAAC9D,KAAK;MAC1BgE,EAAE,EAAEF,aAAa,CAACE,EAAE;MACpBC,KAAK,EAAEH,aAAa,CAACI,KAAK;MAC1BC,EAAE,EAAEL,aAAa,CAACK,EAAE;MACpBC,aAAa,EAAE,GAAG;MAAE;MACpBC,IAAI,EAAEP,aAAa,CAACQ,QAAQ;MAC5BC,KAAK,EAAGT,aAAa,CAASS,KAAK,IAAI,CAAC;MAAE;MAC1CC,SAAS,EAAEV,aAAa,CAACW,UAAU;MACnCC,SAAS,EAAEZ,aAAa,CAACa;IAC3B,CAAC;EACH;;EAEA;EACAC,kBAAkBA,CAACd,aAA4B,EAAO;IACpD,OAAO;MACLlC,SAAS,EAAEkC,aAAa,CAAClC,SAAS,IAAI,CAAC;MACvCC,SAAS,EAAEiC,aAAa,CAACjC,SAAS,IAAI,CAAC;MACvCC,UAAU,EAAEgC,aAAa,CAAChC,UAAU,IAAI,CAAC;MACzCC,GAAG,EAAE+B,aAAa,CAACe,GAAG,IAAI,CAAC;MAAE;MAC7B7C,QAAQ,EAAE8B,aAAa,CAAC9B,QAAQ,IAAI,CAAC;MACrC8C,WAAW,EAAEhB,aAAa,CAACgB,WAAW,IAAI,CAAC;MAC3C5C,QAAQ,EAAE4B,aAAa,CAAC5B,QAAQ,IAAI,CAAC;MACrCC,UAAU,EAAG2B,aAAa,CAAS3B,UAAU,IAAI,CAAC,CAAE;IACtD,CAAC;EACH;EACA;;EAEA,MAAchC,SAASA,CAACH,KAAa,EAAEC,QAAgB,EAAyF;IAC9I;IACA,MAAM,IAAIX,OAAO,CAACyF,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,MAAME,UAAyB,GAAG;MAChClB,GAAG,EAAE,eAAe;MACpBjD,IAAI,EAAE,YAAY;MAClBd,KAAK,EAAEA,KAAK;MACZgE,EAAE,EAAE,eAAe;MACnBE,KAAK,EAAE,CAAC;MACRC,EAAE,EAAE,GAAG;MACPG,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE,EAAE;MAAE;MACX3C,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACb+C,GAAG,EAAE,CAAC;MACN7C,QAAQ,EAAE,CAAC;MACX8C,WAAW,EAAE,CAAC;MACd5C,QAAQ,EAAE,CAAC;MACXuC,UAAU,EAAE,IAAIS,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCR,UAAU,EAAE,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACrC,CAAC;IAED,MAAMzG,KAAK,GAAG,aAAa,GAAGwG,IAAI,CAACE,GAAG,CAAC,CAAC;IACxCzG,YAAY,CAAC4B,OAAO,CAAC,YAAY,EAAE7B,KAAK,CAAC;IAEzC,OAAO;MACLgC,OAAO,EAAE,IAAI;MACbhC,KAAK;MACLiC,MAAM,EAAEsE;IACV,CAAC;EACH;EAEA,MAAclE,YAAYA,CAACD,IAAY,EAAEd,KAAa,EAAEC,QAAgB,EAAyF;IAC/J;IACA,MAAM,IAAIX,OAAO,CAACyF,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,MAAME,UAAyB,GAAG;MAChClB,GAAG,EAAE,WAAW,GAAGmB,IAAI,CAACE,GAAG,CAAC,CAAC;MAC7BtE,IAAI,EAAEA,IAAI;MACVd,KAAK,EAAEA,KAAK;MACZgE,EAAE,EAAE,YAAY,GAAGqB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;MAClDrB,KAAK,EAAE,CAAC;MACRC,EAAE,EAAE,CAAC;MACLG,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,CAAC;MAAE;MACV3C,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACb+C,GAAG,EAAE,CAAC;MACN7C,QAAQ,EAAE,CAAC;MACX8C,WAAW,EAAE,CAAC;MACd5C,QAAQ,EAAE,CAAC;MACXuC,UAAU,EAAE,IAAIS,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCR,UAAU,EAAE,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACrC,CAAC;IAED,MAAMzG,KAAK,GAAG,aAAa,GAAGwG,IAAI,CAACE,GAAG,CAAC,CAAC;IACxCzG,YAAY,CAAC4B,OAAO,CAAC,YAAY,EAAE7B,KAAK,CAAC;IAEzC,OAAO;MACLgC,OAAO,EAAE,IAAI;MACbhC,KAAK;MACLiC,MAAM,EAAEsE;IACV,CAAC;EACH;EAEA,MAAc9D,aAAaA,CAAA,EAA0E;IACnG;IACA,MAAMzC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;IACtD,MAAM4G,SAAS,GAAG9G,KAAK,CAAC+G,QAAQ,CAAC,UAAU,CAAC;IAE5C,MAAMR,UAAyB,GAAG;MAChClB,GAAG,EAAEyB,SAAS,GAAG,cAAc,GAAG,eAAe;MACjD1E,IAAI,EAAE0E,SAAS,GAAG,WAAW,GAAG,YAAY;MAC5CxF,KAAK,EAAEwF,SAAS,GAAG,iBAAiB,GAAG,kBAAkB;MACzDxB,EAAE,EAAE,eAAe;MACnBE,KAAK,EAAEsB,SAAS,GAAG,CAAC,GAAG,CAAC;MACxBrB,EAAE,EAAEqB,SAAS,GAAG,CAAC,GAAG,GAAG;MACvBlB,QAAQ,EAAEkB,SAAS,GAAG,EAAE,GAAG,GAAG;MAC9BjB,KAAK,EAAEiB,SAAS,GAAG,CAAC,GAAG,EAAE;MAAE;MAC3B5D,SAAS,EAAE4D,SAAS,GAAG,CAAC,GAAG,CAAC;MAC5B3D,SAAS,EAAE2D,SAAS,GAAG,CAAC,GAAG,CAAC;MAC5B1D,UAAU,EAAE0D,SAAS,GAAG,CAAC,GAAG,CAAC;MAC7BX,GAAG,EAAEW,SAAS,GAAG,CAAC,GAAG,CAAC;MACtBxD,QAAQ,EAAEwD,SAAS,GAAG,CAAC,GAAG,CAAC;MAC3BV,WAAW,EAAEU,SAAS,GAAG,CAAC,GAAG,CAAC;MAC9BtD,QAAQ,EAAEsD,SAAS,GAAG,CAAC,GAAG,CAAC;MAC3Bf,UAAU,EAAE,IAAIS,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCR,UAAU,EAAE,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACrC,CAAC;IAED,OAAO;MACLzE,OAAO,EAAE,IAAI;MACbC,MAAM,EAAEsE;IACV,CAAC;EACH;AACF;AAEA,OAAO,MAAMS,cAAc,GAAG,IAAIlG,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}