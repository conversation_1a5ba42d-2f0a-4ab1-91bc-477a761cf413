{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\TransferPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TransferPage = () => {\n  _s();\n  var _currentPlayer$dinhei;\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    currentPlayer,\n    loadPlayerData\n  } = usePlayer();\n\n  // Estados para transferência voluntária\n  const [targetNick, setTargetNick] = useState('');\n  const [amount, setAmount] = useState('');\n  const [description, setDescription] = useState('');\n  const [isTransferring, setIsTransferring] = useState(false);\n  const [transferError, setTransferError] = useState(null);\n  const [transferSuccess, setTransferSuccess] = useState(null);\n\n  // Estados para transferência forçada\n  const [forceTargetUid, setForceTargetUid] = useState('');\n  const [forcePercentage, setForcePercentage] = useState('50');\n  const [isForcingTransfer, setIsForcingTransfer] = useState(false);\n\n  // Estados para conexões ativas\n  const [activeConnections, setActiveConnections] = useState([]);\n  const [isLoadingConnections, setIsLoadingConnections] = useState(false);\n\n  // Estados para histórico\n  const [transferHistory, setTransferHistory] = useState([]);\n  const [isLoadingHistory, setIsLoadingHistory] = useState(false);\n  useEffect(() => {\n    if (isAuthenticated && !currentPlayer) {\n      loadPlayerData();\n    }\n    loadTransferHistory();\n  }, [isAuthenticated, currentPlayer, loadPlayerData]);\n  const loadTransferHistory = async () => {\n    setIsLoadingHistory(true);\n    try {\n      const response = await gameApi.getTransferHistory();\n      if (response.sucesso) {\n        setTransferHistory(response.transferencias || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar histórico:', error);\n    } finally {\n      setIsLoadingHistory(false);\n    }\n  };\n  const handleVoluntaryTransfer = async e => {\n    e.preventDefault();\n    if (!targetNick.trim() || !amount.trim()) {\n      setTransferError('Nick do destinatário e valor são obrigatórios');\n      return;\n    }\n    const transferAmount = parseInt(amount);\n    if (isNaN(transferAmount) || transferAmount <= 0) {\n      setTransferError('Valor deve ser um número positivo');\n      return;\n    }\n    if (currentPlayer && transferAmount > currentPlayer.dinheiro) {\n      setTransferError('Saldo insuficiente');\n      return;\n    }\n    setIsTransferring(true);\n    setTransferError(null);\n    setTransferSuccess(null);\n    try {\n      const response = await gameApi.transferMoney(targetNick, transferAmount, description);\n      if (response.sucesso) {\n        setTransferSuccess(`Transferência realizada com sucesso! ${response.mensagem}`);\n        setTargetNick('');\n        setAmount('');\n        setDescription('');\n\n        // Recarregar dados do jogador\n        loadPlayerData();\n        loadTransferHistory();\n      } else {\n        setTransferError(response.mensagem || 'Erro na transferência');\n      }\n    } catch (error) {\n      setTransferError(error.message || 'Erro de conexão');\n    } finally {\n      setIsTransferring(false);\n    }\n  };\n  const handleForceTransfer = async e => {\n    e.preventDefault();\n    if (!forceTargetUid.trim()) {\n      setTransferError('UID do alvo é obrigatório');\n      return;\n    }\n    const percentage = parseInt(forcePercentage);\n    if (isNaN(percentage) || percentage < 20 || percentage > 80) {\n      setTransferError('Porcentagem deve estar entre 20% e 80%');\n      return;\n    }\n    setIsForcingTransfer(true);\n    setTransferError(null);\n    setTransferSuccess(null);\n    try {\n      const response = await gameApi.forceTransfer(forceTargetUid, percentage);\n      if (response.sucesso) {\n        setTransferSuccess(`Transferência forçada realizada! ${response.mensagem}`);\n        setForceTargetUid('');\n\n        // Recarregar dados do jogador\n        loadPlayerData();\n        loadTransferHistory();\n      } else {\n        setTransferError(response.mensagem || 'Erro na transferência forçada');\n      }\n    } catch (error) {\n      setTransferError(error.message || 'Erro de conexão');\n    } finally {\n      setIsForcingTransfer(false);\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-900 text-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"\\uD83D\\uDD12 Acesso Negado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Voc\\xEA precisa estar logado para acessar as transfer\\xEAncias\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold\",\n            children: \"\\uD83D\\uDCB0 Transfer\\xEAncias\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Sistema de Pagamentos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-2 text-white\",\n          children: \"\\uD83D\\uDCB3 Saldo Atual\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-green-400\",\n          children: [\"$\", (currentPlayer === null || currentPlayer === void 0 ? void 0 : (_currentPlayer$dinhei = currentPlayer.dinheiro) === null || _currentPlayer$dinhei === void 0 ? void 0 : _currentPlayer$dinhei.toLocaleString()) || '0']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-400\",\n          children: \"Dispon\\xEDvel para transfer\\xEAncia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), transferError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-300 text-sm\",\n          children: [\"\\u274C \", transferError]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this), transferSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-900 border border-green-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-300 text-sm\",\n          children: [\"\\u2705 \", transferSuccess]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: \"\\uD83D\\uDCE4 Transfer\\xEAncia Volunt\\xE1ria\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleVoluntaryTransfer,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Nick do Destinat\\xE1rio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: targetNick,\n              onChange: e => setTargetNick(e.target.value),\n              placeholder: \"Digite o nick do jogador\",\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isTransferring\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Valor ($)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: amount,\n              onChange: e => setAmount(e.target.value),\n              placeholder: \"0\",\n              min: \"1\",\n              max: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.dinheiro) || 0,\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isTransferring\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Descri\\xE7\\xE3o (opcional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: description,\n              onChange: e => setDescription(e.target.value),\n              placeholder: \"Motivo da transfer\\xEAncia\",\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isTransferring\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isTransferring || !targetNick.trim() || !amount.trim(),\n            className: \"w-full py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n            children: isTransferring ? 'Transferindo...' : 'Transferir Dinheiro'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4 border border-red-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4 text-red-400\",\n          children: \"\\u2694\\uFE0F Transfer\\xEAncia For\\xE7ada\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-400 mb-4\",\n          children: \"Apenas dispon\\xEDvel ap\\xF3s exploit bem-sucedido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleForceTransfer,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"UID do Alvo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: forceTargetUid,\n              onChange: e => setForceTargetUid(e.target.value),\n              placeholder: \"UID do jogador exploitado\",\n              className: \"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\",\n              disabled: isForcingTransfer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Porcentagem (20% - 80%)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: \"20\",\n              max: \"80\",\n              value: forcePercentage,\n              onChange: e => setForcePercentage(e.target.value),\n              className: \"w-full\",\n              disabled: isForcingTransfer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-sm text-gray-400 mt-1\",\n              children: [forcePercentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isForcingTransfer || !forceTargetUid.trim(),\n            className: \"w-full py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n            children: isForcingTransfer ? 'Forçando...' : 'Forçar Transferência'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"Voltar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(TransferPage, \"5tAsHJ4BYcxQFw7+WLoYFJnKMU4=\", false, function () {\n  return [useAuth, usePlayer];\n});\n_c = TransferPage;\nexport default TransferPage;\nvar _c;\n$RefreshReg$(_c, \"TransferPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "usePlayer", "gameApi", "jsxDEV", "_jsxDEV", "TransferPage", "_s", "_currentPlayer$dinhei", "user", "isAuthenticated", "currentPlayer", "loadPlayerData", "targetNick", "setTargetNick", "amount", "setAmount", "description", "setDescription", "isTransferring", "setIsTransferring", "transferError", "setTransferError", "transferSuccess", "setTransferSuccess", "forceTargetUid", "setForceTargetUid", "forcePercentage", "setForcePercentage", "isForcingTransfer", "setIsForcingTransfer", "activeConnections", "setActiveConnections", "isLoadingConnections", "setIsLoadingConnections", "transferHistory", "setTransferHistory", "isLoadingHistory", "setIsLoadingHistory", "loadTransferHistory", "response", "getTransferHistory", "sucesso", "transferencias", "error", "console", "handleVoluntaryTransfer", "e", "preventDefault", "trim", "transferAmount", "parseInt", "isNaN", "<PERSON><PERSON><PERSON>", "transferMoney", "mensagem", "message", "handleForceTransfer", "percentage", "forceTransfer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "history", "back", "toLocaleString", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "min", "max", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/TransferPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\n\nconst TransferPage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const { currentPlayer, loadPlayerData } = usePlayer();\n  \n  // Estados para transferência voluntária\n  const [targetNick, setTargetNick] = useState('');\n  const [amount, setAmount] = useState('');\n  const [description, setDescription] = useState('');\n  const [isTransferring, setIsTransferring] = useState(false);\n  const [transferError, setTransferError] = useState<string | null>(null);\n  const [transferSuccess, setTransferSuccess] = useState<string | null>(null);\n  \n  // Estados para transferência forçada\n  const [forceTargetUid, setForceTargetUid] = useState('');\n  const [forcePercentage, setForcePercentage] = useState('50');\n  const [isForcingTransfer, setIsForcingTransfer] = useState(false);\n\n  // Estados para conexões ativas\n  const [activeConnections, setActiveConnections] = useState<any[]>([]);\n  const [isLoadingConnections, setIsLoadingConnections] = useState(false);\n  \n  // Estados para histórico\n  const [transferHistory, setTransferHistory] = useState<any[]>([]);\n  const [isLoadingHistory, setIsLoadingHistory] = useState(false);\n\n  useEffect(() => {\n    if (isAuthenticated && !currentPlayer) {\n      loadPlayerData();\n    }\n    loadTransferHistory();\n  }, [isAuthenticated, currentPlayer, loadPlayerData]);\n\n  const loadTransferHistory = async () => {\n    setIsLoadingHistory(true);\n    try {\n      const response = await gameApi.getTransferHistory();\n      if (response.sucesso) {\n        setTransferHistory(response.transferencias || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar histórico:', error);\n    } finally {\n      setIsLoadingHistory(false);\n    }\n  };\n\n  const handleVoluntaryTransfer = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!targetNick.trim() || !amount.trim()) {\n      setTransferError('Nick do destinatário e valor são obrigatórios');\n      return;\n    }\n\n    const transferAmount = parseInt(amount);\n    if (isNaN(transferAmount) || transferAmount <= 0) {\n      setTransferError('Valor deve ser um número positivo');\n      return;\n    }\n\n    if (currentPlayer && transferAmount > currentPlayer.dinheiro) {\n      setTransferError('Saldo insuficiente');\n      return;\n    }\n\n    setIsTransferring(true);\n    setTransferError(null);\n    setTransferSuccess(null);\n\n    try {\n      const response = await gameApi.transferMoney(targetNick, transferAmount, description);\n      \n      if (response.sucesso) {\n        setTransferSuccess(`Transferência realizada com sucesso! ${response.mensagem}`);\n        setTargetNick('');\n        setAmount('');\n        setDescription('');\n        \n        // Recarregar dados do jogador\n        loadPlayerData();\n        loadTransferHistory();\n      } else {\n        setTransferError(response.mensagem || 'Erro na transferência');\n      }\n    } catch (error: any) {\n      setTransferError(error.message || 'Erro de conexão');\n    } finally {\n      setIsTransferring(false);\n    }\n  };\n\n  const handleForceTransfer = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!forceTargetUid.trim()) {\n      setTransferError('UID do alvo é obrigatório');\n      return;\n    }\n\n    const percentage = parseInt(forcePercentage);\n    if (isNaN(percentage) || percentage < 20 || percentage > 80) {\n      setTransferError('Porcentagem deve estar entre 20% e 80%');\n      return;\n    }\n\n    setIsForcingTransfer(true);\n    setTransferError(null);\n    setTransferSuccess(null);\n\n    try {\n      const response = await gameApi.forceTransfer(forceTargetUid, percentage);\n      \n      if (response.sucesso) {\n        setTransferSuccess(`Transferência forçada realizada! ${response.mensagem}`);\n        setForceTargetUid('');\n        \n        // Recarregar dados do jogador\n        loadPlayerData();\n        loadTransferHistory();\n      } else {\n        setTransferError(response.mensagem || 'Erro na transferência forçada');\n      }\n    } catch (error: any) {\n      setTransferError(error.message || 'Erro de conexão');\n    } finally {\n      setIsForcingTransfer(false);\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"h-screen bg-gray-900 text-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-4\">🔒 Acesso Negado</h1>\n          <p className=\"text-gray-400\">Você precisa estar logado para acessar as transferências</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n          >\n            <span className=\"text-lg\">←</span>\n          </button>\n          <div>\n            <h1 className=\"text-lg font-bold\">💰 Transferências</h1>\n            <p className=\"text-xs text-gray-400\">Sistema de Pagamentos</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-4\">\n        {/* Saldo atual */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-2 text-white\">💳 Saldo Atual</h3>\n          <div className=\"text-2xl font-bold text-green-400\">\n            ${currentPlayer?.dinheiro?.toLocaleString() || '0'}\n          </div>\n          <p className=\"text-xs text-gray-400\">Disponível para transferência</p>\n        </div>\n\n        {/* Mensagens de erro/sucesso */}\n        {transferError && (\n          <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n            <p className=\"text-red-300 text-sm\">❌ {transferError}</p>\n          </div>\n        )}\n\n        {transferSuccess && (\n          <div className=\"bg-green-900 border border-green-500 rounded-lg p-3\">\n            <p className=\"text-green-300 text-sm\">✅ {transferSuccess}</p>\n          </div>\n        )}\n\n        {/* Transferência Voluntária */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n          <h3 className=\"text-lg font-semibold mb-4 text-white\">📤 Transferência Voluntária</h3>\n          <form onSubmit={handleVoluntaryTransfer} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Nick do Destinatário\n              </label>\n              <input\n                type=\"text\"\n                value={targetNick}\n                onChange={(e) => setTargetNick(e.target.value)}\n                placeholder=\"Digite o nick do jogador\"\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isTransferring}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Valor ($)\n              </label>\n              <input\n                type=\"number\"\n                value={amount}\n                onChange={(e) => setAmount(e.target.value)}\n                placeholder=\"0\"\n                min=\"1\"\n                max={currentPlayer?.dinheiro || 0}\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isTransferring}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Descrição (opcional)\n              </label>\n              <input\n                type=\"text\"\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                placeholder=\"Motivo da transferência\"\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isTransferring}\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isTransferring || !targetNick.trim() || !amount.trim()}\n              className=\"w-full py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n            >\n              {isTransferring ? 'Transferindo...' : 'Transferir Dinheiro'}\n            </button>\n          </form>\n        </div>\n\n        {/* Transferência Forçada */}\n        <div className=\"bg-gray-800 rounded-lg p-4 border border-red-600\">\n          <h3 className=\"text-lg font-semibold mb-4 text-red-400\">⚔️ Transferência Forçada</h3>\n          <p className=\"text-xs text-gray-400 mb-4\">\n            Apenas disponível após exploit bem-sucedido\n          </p>\n          <form onSubmit={handleForceTransfer} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                UID do Alvo\n              </label>\n              <input\n                type=\"text\"\n                value={forceTargetUid}\n                onChange={(e) => setForceTargetUid(e.target.value)}\n                placeholder=\"UID do jogador exploitado\"\n                className=\"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white\"\n                disabled={isForcingTransfer}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Porcentagem (20% - 80%)\n              </label>\n              <input\n                type=\"range\"\n                min=\"20\"\n                max=\"80\"\n                value={forcePercentage}\n                onChange={(e) => setForcePercentage(e.target.value)}\n                className=\"w-full\"\n                disabled={isForcingTransfer}\n              />\n              <div className=\"text-center text-sm text-gray-400 mt-1\">\n                {forcePercentage}%\n              </div>\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isForcingTransfer || !forceTargetUid.trim()}\n              className=\"w-full py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n            >\n              {isForcingTransfer ? 'Forçando...' : 'Forçar Transferência'}\n            </button>\n          </form>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-center\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\"\n          >\n            <span className=\"text-lg\">←</span>\n            <span className=\"text-sm\">Voltar</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TransferPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACnC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEU,aAAa;IAAEC;EAAe,CAAC,GAAGV,SAAS,CAAC,CAAC;;EAErD;EACA,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;;EAE3E;EACA,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACgC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjC,QAAQ,CAAQ,EAAE,CAAC;EACrE,MAAM,CAACkC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAQ,EAAE,CAAC;EACjE,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAE/DC,SAAS,CAAC,MAAM;IACd,IAAIU,eAAe,IAAI,CAACC,aAAa,EAAE;MACrCC,cAAc,CAAC,CAAC;IAClB;IACA2B,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC7B,eAAe,EAAEC,aAAa,EAAEC,cAAc,CAAC,CAAC;EAEpD,MAAM2B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCD,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMrC,OAAO,CAACsC,kBAAkB,CAAC,CAAC;MACnD,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBN,kBAAkB,CAACI,QAAQ,CAACG,cAAc,IAAI,EAAE,CAAC;MACnD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACRN,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMQ,uBAAuB,GAAG,MAAOC,CAAkB,IAAK;IAC5DA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACnC,UAAU,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,MAAM,CAACkC,IAAI,CAAC,CAAC,EAAE;MACxC3B,gBAAgB,CAAC,+CAA+C,CAAC;MACjE;IACF;IAEA,MAAM4B,cAAc,GAAGC,QAAQ,CAACpC,MAAM,CAAC;IACvC,IAAIqC,KAAK,CAACF,cAAc,CAAC,IAAIA,cAAc,IAAI,CAAC,EAAE;MAChD5B,gBAAgB,CAAC,mCAAmC,CAAC;MACrD;IACF;IAEA,IAAIX,aAAa,IAAIuC,cAAc,GAAGvC,aAAa,CAAC0C,QAAQ,EAAE;MAC5D/B,gBAAgB,CAAC,oBAAoB,CAAC;MACtC;IACF;IAEAF,iBAAiB,CAAC,IAAI,CAAC;IACvBE,gBAAgB,CAAC,IAAI,CAAC;IACtBE,kBAAkB,CAAC,IAAI,CAAC;IAExB,IAAI;MACF,MAAMgB,QAAQ,GAAG,MAAMrC,OAAO,CAACmD,aAAa,CAACzC,UAAU,EAAEqC,cAAc,EAAEjC,WAAW,CAAC;MAErF,IAAIuB,QAAQ,CAACE,OAAO,EAAE;QACpBlB,kBAAkB,CAAC,wCAAwCgB,QAAQ,CAACe,QAAQ,EAAE,CAAC;QAC/EzC,aAAa,CAAC,EAAE,CAAC;QACjBE,SAAS,CAAC,EAAE,CAAC;QACbE,cAAc,CAAC,EAAE,CAAC;;QAElB;QACAN,cAAc,CAAC,CAAC;QAChB2B,mBAAmB,CAAC,CAAC;MACvB,CAAC,MAAM;QACLjB,gBAAgB,CAACkB,QAAQ,CAACe,QAAQ,IAAI,uBAAuB,CAAC;MAChE;IACF,CAAC,CAAC,OAAOX,KAAU,EAAE;MACnBtB,gBAAgB,CAACsB,KAAK,CAACY,OAAO,IAAI,iBAAiB,CAAC;IACtD,CAAC,SAAS;MACRpC,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMqC,mBAAmB,GAAG,MAAOV,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACvB,cAAc,CAACwB,IAAI,CAAC,CAAC,EAAE;MAC1B3B,gBAAgB,CAAC,2BAA2B,CAAC;MAC7C;IACF;IAEA,MAAMoC,UAAU,GAAGP,QAAQ,CAACxB,eAAe,CAAC;IAC5C,IAAIyB,KAAK,CAACM,UAAU,CAAC,IAAIA,UAAU,GAAG,EAAE,IAAIA,UAAU,GAAG,EAAE,EAAE;MAC3DpC,gBAAgB,CAAC,wCAAwC,CAAC;MAC1D;IACF;IAEAQ,oBAAoB,CAAC,IAAI,CAAC;IAC1BR,gBAAgB,CAAC,IAAI,CAAC;IACtBE,kBAAkB,CAAC,IAAI,CAAC;IAExB,IAAI;MACF,MAAMgB,QAAQ,GAAG,MAAMrC,OAAO,CAACwD,aAAa,CAAClC,cAAc,EAAEiC,UAAU,CAAC;MAExE,IAAIlB,QAAQ,CAACE,OAAO,EAAE;QACpBlB,kBAAkB,CAAC,oCAAoCgB,QAAQ,CAACe,QAAQ,EAAE,CAAC;QAC3E7B,iBAAiB,CAAC,EAAE,CAAC;;QAErB;QACAd,cAAc,CAAC,CAAC;QAChB2B,mBAAmB,CAAC,CAAC;MACvB,CAAC,MAAM;QACLjB,gBAAgB,CAACkB,QAAQ,CAACe,QAAQ,IAAI,+BAA+B,CAAC;MACxE;IACF,CAAC,CAAC,OAAOX,KAAU,EAAE;MACnBtB,gBAAgB,CAACsB,KAAK,CAACY,OAAO,IAAI,iBAAiB,CAAC;IACtD,CAAC,SAAS;MACR1B,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,IAAI,CAACpB,eAAe,EAAE;IACpB,oBACEL,OAAA;MAAKuD,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/ExD,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxD,OAAA;UAAIuD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7D5D,OAAA;UAAGuD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAwD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5D,OAAA;IAAKuD,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DxD,OAAA;MAAKuD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrExD,OAAA;QAAKuD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CxD,OAAA;UACE6D,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCT,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7FxD,OAAA;YAAMuD,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACT5D,OAAA;UAAAwD,QAAA,gBACExD,OAAA;YAAIuD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxD5D,OAAA;YAAGuD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBAEnDxD,OAAA;QAAKuD,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChExD,OAAA;UAAIuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE5D,OAAA;UAAKuD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAAC,GAChD,EAAC,CAAAlD,aAAa,aAAbA,aAAa,wBAAAH,qBAAA,GAAbG,aAAa,CAAE0C,QAAQ,cAAA7C,qBAAA,uBAAvBA,qBAAA,CAAyB8D,cAAc,CAAC,CAAC,KAAI,GAAG;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACN5D,OAAA;UAAGuD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,EAGL5C,aAAa,iBACZhB,OAAA;QAAKuD,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DxD,OAAA;UAAGuD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,SAAE,EAACxC,aAAa;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN,EAEA1C,eAAe,iBACdlB,OAAA;QAAKuD,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClExD,OAAA;UAAGuD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,SAAE,EAACtC,eAAe;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CACN,eAGD5D,OAAA;QAAKuD,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChExD,OAAA;UAAIuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtF5D,OAAA;UAAMkE,QAAQ,EAAEzB,uBAAwB;UAACc,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAC5DxD,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEmE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE5D,UAAW;cAClB6D,QAAQ,EAAG3B,CAAC,IAAKjC,aAAa,CAACiC,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cAC/CG,WAAW,EAAC,0BAA0B;cACtChB,SAAS,EAAC,wEAAwE;cAClFiB,QAAQ,EAAE1D;YAAe;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEmE,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAE1D,MAAO;cACd2D,QAAQ,EAAG3B,CAAC,IAAK/B,SAAS,CAAC+B,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cAC3CG,WAAW,EAAC,GAAG;cACfE,GAAG,EAAC,GAAG;cACPC,GAAG,EAAE,CAAApE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0C,QAAQ,KAAI,CAAE;cAClCO,SAAS,EAAC,wEAAwE;cAClFiB,QAAQ,EAAE1D;YAAe;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEmE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExD,WAAY;cACnByD,QAAQ,EAAG3B,CAAC,IAAK7B,cAAc,CAAC6B,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cAChDG,WAAW,EAAC,4BAAyB;cACrChB,SAAS,EAAC,wEAAwE;cAClFiB,QAAQ,EAAE1D;YAAe;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5D,OAAA;YACEmE,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAE1D,cAAc,IAAI,CAACN,UAAU,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,MAAM,CAACkC,IAAI,CAAC,CAAE;YACjEW,SAAS,EAAC,sGAAsG;YAAAC,QAAA,EAE/G1C,cAAc,GAAG,iBAAiB,GAAG;UAAqB;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN5D,OAAA;QAAKuD,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/DxD,OAAA;UAAIuD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrF5D,OAAA;UAAGuD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ5D,OAAA;UAAMkE,QAAQ,EAAEd,mBAAoB;UAACG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxDxD,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEmE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEhD,cAAe;cACtBiD,QAAQ,EAAG3B,CAAC,IAAKrB,iBAAiB,CAACqB,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cACnDG,WAAW,EAAC,2BAA2B;cACvChB,SAAS,EAAC,wEAAwE;cAClFiB,QAAQ,EAAEhD;YAAkB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEmE,IAAI,EAAC,OAAO;cACZM,GAAG,EAAC,IAAI;cACRC,GAAG,EAAC,IAAI;cACRN,KAAK,EAAE9C,eAAgB;cACvB+C,QAAQ,EAAG3B,CAAC,IAAKnB,kBAAkB,CAACmB,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cACpDb,SAAS,EAAC,QAAQ;cAClBiB,QAAQ,EAAEhD;YAAkB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACF5D,OAAA;cAAKuD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,GACpDlC,eAAe,EAAC,GACnB;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5D,OAAA;YACEmE,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAEhD,iBAAiB,IAAI,CAACJ,cAAc,CAACwB,IAAI,CAAC,CAAE;YACtDW,SAAS,EAAC,kGAAkG;YAAAC,QAAA,EAE3GhC,iBAAiB,GAAG,aAAa,GAAG;UAAsB;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrExD,OAAA;QAAKuD,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClCxD,OAAA;UACE6D,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCT,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExFxD,OAAA;YAAMuD,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClC5D,OAAA;YAAMuD,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAhTID,YAAsB;EAAA,QACQL,OAAO,EACCC,SAAS;AAAA;AAAA8E,EAAA,GAF/C1E,YAAsB;AAkT5B,eAAeA,YAAY;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}