{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\MiningPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MiningPage = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    currentPlayer,\n    loadPlayerData\n  } = usePlayer();\n  const [miningStatus, setMiningStatus] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isCollecting, setIsCollecting] = useState(false);\n  const [isUpgrading, setIsUpgrading] = useState(false);\n  const [actionError, setActionError] = useState(null);\n  const [actionSuccess, setActionSuccess] = useState(null);\n\n  // Timer para atualização automática\n  const [timeLeft, setTimeLeft] = useState('');\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadMiningStatus();\n\n      // Atualizar a cada 30 segundos\n      const interval = setInterval(() => {\n        loadMiningStatus();\n      }, 30000);\n      return () => clearInterval(interval);\n    }\n  }, [isAuthenticated]);\n  useEffect(() => {\n    // Timer para mostrar tempo até próxima coleta\n    const timer = setInterval(() => {\n      if (miningStatus !== null && miningStatus !== void 0 && miningStatus.ultimo_coleta) {\n        const lastCollect = new Date(miningStatus.ultimo_coleta);\n        const nextCollect = new Date(lastCollect.getTime() + 60 * 60 * 1000); // +1 hora\n        const now = new Date();\n        if (now >= nextCollect) {\n          setTimeLeft('Disponível para coleta!');\n        } else {\n          const diff = nextCollect.getTime() - now.getTime();\n          const minutes = Math.floor(diff / 60000);\n          const seconds = Math.floor(diff % 60000 / 1000);\n          setTimeLeft(`${minutes}m ${seconds}s`);\n        }\n      }\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [miningStatus]);\n  const loadMiningStatus = async () => {\n    setIsLoading(true);\n    try {\n      const response = await gameApi.getMiningStatus();\n      if (response.sucesso) {\n        setMiningStatus(response.status);\n      } else {\n        // Fallback para dados mockados\n        loadMockMiningStatus();\n      }\n    } catch (error) {\n      console.error('Erro ao carregar status de mineração:', error);\n      loadMockMiningStatus();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const loadMockMiningStatus = () => {\n    if (!currentPlayer) return;\n    const mockStatus = {\n      nivel_mineradora: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.nivel_mineradora) || 1,\n      shacks_disponiveis: Math.floor(Math.random() * 100) + 10,\n      dinheiro_gerado: Math.floor(Math.random() * 5000) + 1000,\n      ultimo_coleta: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n      // 30 min atrás\n      geracao_por_segundo: {\n        shacks: ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.nivel_mineradora) || 1) * 0.01,\n        dinheiro: ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.nivel_mineradora) || 1) * 0.5\n      },\n      custo_upgrade: Math.floor(1000 * Math.pow(1.5, ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.nivel_mineradora) || 1) - 1))\n    };\n    setMiningStatus(mockStatus);\n  };\n  const handleCollectShacks = async () => {\n    setIsCollecting(true);\n    setActionError(null);\n    setActionSuccess(null);\n    try {\n      const response = await gameApi.collectShacks();\n      if (response.sucesso) {\n        setActionSuccess(`Coletados ${response.shacks_coletados} Shacks!`);\n\n        // Recarregar dados\n        await loadPlayerData();\n        await loadMiningStatus();\n      } else {\n        setActionError(response.mensagem || 'Erro ao coletar Shacks');\n      }\n    } catch (error) {\n      setActionError(error.message || 'Erro de conexão');\n    } finally {\n      setIsCollecting(false);\n    }\n  };\n  const handleUpgradeMiner = async () => {\n    if (!miningStatus || !currentPlayer) return;\n    if (((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.dinheiro) || 0) < miningStatus.custo_upgrade) {\n      setActionError('Dinheiro insuficiente para upgrade');\n      return;\n    }\n    setIsUpgrading(true);\n    setActionError(null);\n    setActionSuccess(null);\n    try {\n      const response = await gameApi.upgradeMiner();\n      if (response.sucesso) {\n        setActionSuccess(`Mineradora atualizada para nível ${miningStatus.nivel_mineradora + 1}!`);\n\n        // Recarregar dados\n        await loadPlayerData();\n        await loadMiningStatus();\n      } else {\n        setActionError(response.mensagem || 'Erro ao fazer upgrade');\n      }\n    } catch (error) {\n      setActionError(error.message || 'Erro de conexão');\n    } finally {\n      setIsUpgrading(false);\n    }\n  };\n  const calculateHourlyGeneration = () => {\n    if (!miningStatus) return {\n      shacks: 0,\n      dinheiro: 0\n    };\n    return {\n      shacks: Math.floor(miningStatus.geracao_por_segundo.shacks * 3600),\n      dinheiro: Math.floor(miningStatus.geracao_por_segundo.dinheiro * 3600)\n    };\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gray-900 text-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"\\uD83D\\uDD12 Acesso Negado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Voc\\xEA precisa estar logado para acessar a minera\\xE7\\xE3o\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this);\n  }\n  const hourlyGen = calculateHourlyGeneration();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold\",\n            children: \"\\u26CF\\uFE0F Minera\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Sistema de Gera\\xE7\\xE3o de Recursos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n      children: [actionError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-300 text-sm\",\n          children: [\"\\u274C \", actionError]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), actionSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-900 border border-green-500 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-300 text-sm\",\n          children: [\"\\u2705 \", actionSuccess]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Carregando status de minera\\xE7\\xE3o...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this) : miningStatus ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4 text-white\",\n            children: \"\\uD83C\\uDFED Status da Mineradora\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-blue-400\",\n                children: [\"N\\xEDvel \", miningStatus.nivel_mineradora]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Mineradora\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-green-400\",\n                children: [hourlyGen.shacks, \"/h\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Shacks por Hora\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4 text-white\",\n            children: \"\\uD83D\\uDC8E Recursos Dispon\\xEDveis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-bold text-blue-400\",\n                  children: [miningStatus.shacks_disponiveis, \" Shacks\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"Prontos para coleta\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleCollectShacks,\n                disabled: isCollecting || miningStatus.shacks_disponiveis <= 0,\n                className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n                children: isCollecting ? 'Coletando...' : 'Coletar Shacks'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-bold text-green-400\",\n                  children: [\"$\", miningStatus.dinheiro_gerado.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"Dinheiro gerado automaticamente\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: timeLeft\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4 text-white\",\n            children: \"\\uD83D\\uDCCA Estat\\xEDsticas de Gera\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-lg font-bold text-blue-400\",\n                children: [miningStatus.geracao_por_segundo.shacks.toFixed(3), \"/s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Shacks por Segundo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-lg font-bold text-green-400\",\n                children: [\"$\", miningStatus.geracao_por_segundo.dinheiro.toFixed(2), \"/s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Dinheiro por Segundo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 border border-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4 text-white\",\n            children: \"\\uD83D\\uDD27 Upgrade da Mineradora\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-bold text-white\",\n                  children: [\"N\\xEDvel \", miningStatus.nivel_mineradora, \" \\u2192 \", miningStatus.nivel_mineradora + 1]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"Aumenta gera\\xE7\\xE3o de recursos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-yellow-400\",\n                  children: [\"$\", miningStatus.custo_upgrade.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"Custo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"Ap\\xF3s upgrade:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"\\u2022 Shacks: \", ((miningStatus.nivel_mineradora + 1) * 0.01 * 3600).toFixed(0), \"/h\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"\\u2022 Dinheiro: $\", ((miningStatus.nivel_mineradora + 1) * 0.5 * 3600).toFixed(0), \"/h\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleUpgradeMiner,\n              disabled: isUpgrading || !currentPlayer || ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.dinheiro) || 0) < miningStatus.custo_upgrade,\n              className: \"w-full py-3 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\",\n              children: isUpgrading ? 'Fazendo Upgrade...' : !currentPlayer || ((currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.dinheiro) || 0) < miningStatus.custo_upgrade ? 'Dinheiro Insuficiente' : 'Fazer Upgrade'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-4xl mb-4\",\n          children: \"\\u26CF\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Erro ao carregar dados de minera\\xE7\\xE3o\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"Voltar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(MiningPage, \"CCOtIoZzTkaNrBGx38tTcaZsd4s=\", false, function () {\n  return [useAuth, usePlayer];\n});\n_c = MiningPage;\nexport default MiningPage;\nvar _c;\n$RefreshReg$(_c, \"MiningPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "usePlayer", "gameApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MiningPage", "_s", "user", "isAuthenticated", "currentPlayer", "loadPlayerData", "miningStatus", "setMiningStatus", "isLoading", "setIsLoading", "isCollecting", "setIsCollecting", "isUpgrading", "setIsUpgrading", "actionError", "setActionError", "actionSuccess", "setActionSuccess", "timeLeft", "setTimeLeft", "loadMiningStatus", "interval", "setInterval", "clearInterval", "timer", "ultimo_coleta", "lastCollect", "Date", "nextCollect", "getTime", "now", "diff", "minutes", "Math", "floor", "seconds", "response", "getMiningStatus", "sucesso", "status", "loadMockMiningStatus", "error", "console", "mockStatus", "nivel_mineradora", "shacks_disponiveis", "random", "dinheiro_gerado", "toISOString", "geracao_por_segundo", "shacks", "<PERSON><PERSON><PERSON>", "custo_upgrade", "pow", "handleCollectShacks", "collectShacks", "shacks_coletados", "mensagem", "message", "handleUpgradeMiner", "upgradeMiner", "calculateHourlyGeneration", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "hourlyGen", "onClick", "window", "history", "back", "disabled", "toLocaleString", "toFixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/MiningPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../stores/authStore';\nimport { usePlayer } from '../stores/playerStore';\nimport gameApi from '../services/gameApi';\n\ninterface MiningStatus {\n  nivel_mineradora: number;\n  shacks_disponiveis: number;\n  dinheiro_gerado: number;\n  ultimo_coleta: string;\n  geracao_por_segundo: {\n    shacks: number;\n    dinheiro: number;\n  };\n  custo_upgrade: number;\n}\n\nconst MiningPage: React.FC = () => {\n  const { user, isAuthenticated } = useAuth();\n  const { currentPlayer, loadPlayerData } = usePlayer();\n  \n  const [miningStatus, setMiningStatus] = useState<MiningStatus | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isCollecting, setIsCollecting] = useState(false);\n  const [isUpgrading, setIsUpgrading] = useState(false);\n  const [actionError, setActionError] = useState<string | null>(null);\n  const [actionSuccess, setActionSuccess] = useState<string | null>(null);\n  \n  // Timer para atualização automática\n  const [timeLeft, setTimeLeft] = useState<string>('');\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadMiningStatus();\n      \n      // Atualizar a cada 30 segundos\n      const interval = setInterval(() => {\n        loadMiningStatus();\n      }, 30000);\n\n      return () => clearInterval(interval);\n    }\n  }, [isAuthenticated]);\n\n  useEffect(() => {\n    // Timer para mostrar tempo até próxima coleta\n    const timer = setInterval(() => {\n      if (miningStatus?.ultimo_coleta) {\n        const lastCollect = new Date(miningStatus.ultimo_coleta);\n        const nextCollect = new Date(lastCollect.getTime() + 60 * 60 * 1000); // +1 hora\n        const now = new Date();\n        \n        if (now >= nextCollect) {\n          setTimeLeft('Disponível para coleta!');\n        } else {\n          const diff = nextCollect.getTime() - now.getTime();\n          const minutes = Math.floor(diff / 60000);\n          const seconds = Math.floor((diff % 60000) / 1000);\n          setTimeLeft(`${minutes}m ${seconds}s`);\n        }\n      }\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [miningStatus]);\n\n  const loadMiningStatus = async () => {\n    setIsLoading(true);\n    try {\n      const response = await gameApi.getMiningStatus();\n      if (response.sucesso) {\n        setMiningStatus(response.status);\n      } else {\n        // Fallback para dados mockados\n        loadMockMiningStatus();\n      }\n    } catch (error) {\n      console.error('Erro ao carregar status de mineração:', error);\n      loadMockMiningStatus();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loadMockMiningStatus = () => {\n    if (!currentPlayer) return;\n\n    const mockStatus: MiningStatus = {\n      nivel_mineradora: currentPlayer?.nivel_mineradora || 1,\n      shacks_disponiveis: Math.floor(Math.random() * 100) + 10,\n      dinheiro_gerado: Math.floor(Math.random() * 5000) + 1000,\n      ultimo_coleta: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 min atrás\n      geracao_por_segundo: {\n        shacks: (currentPlayer?.nivel_mineradora || 1) * 0.01,\n        dinheiro: (currentPlayer?.nivel_mineradora || 1) * 0.5,\n      },\n      custo_upgrade: Math.floor(1000 * Math.pow(1.5, (currentPlayer?.nivel_mineradora || 1) - 1)),\n    };\n\n    setMiningStatus(mockStatus);\n  };\n\n  const handleCollectShacks = async () => {\n    setIsCollecting(true);\n    setActionError(null);\n    setActionSuccess(null);\n\n    try {\n      const response = await gameApi.collectShacks();\n      \n      if (response.sucesso) {\n        setActionSuccess(`Coletados ${response.shacks_coletados} Shacks!`);\n        \n        // Recarregar dados\n        await loadPlayerData();\n        await loadMiningStatus();\n      } else {\n        setActionError(response.mensagem || 'Erro ao coletar Shacks');\n      }\n    } catch (error: any) {\n      setActionError(error.message || 'Erro de conexão');\n    } finally {\n      setIsCollecting(false);\n    }\n  };\n\n  const handleUpgradeMiner = async () => {\n    if (!miningStatus || !currentPlayer) return;\n\n    if ((currentPlayer?.dinheiro || 0) < miningStatus.custo_upgrade) {\n      setActionError('Dinheiro insuficiente para upgrade');\n      return;\n    }\n\n    setIsUpgrading(true);\n    setActionError(null);\n    setActionSuccess(null);\n\n    try {\n      const response = await gameApi.upgradeMiner();\n      \n      if (response.sucesso) {\n        setActionSuccess(`Mineradora atualizada para nível ${miningStatus.nivel_mineradora + 1}!`);\n        \n        // Recarregar dados\n        await loadPlayerData();\n        await loadMiningStatus();\n      } else {\n        setActionError(response.mensagem || 'Erro ao fazer upgrade');\n      }\n    } catch (error: any) {\n      setActionError(error.message || 'Erro de conexão');\n    } finally {\n      setIsUpgrading(false);\n    }\n  };\n\n  const calculateHourlyGeneration = () => {\n    if (!miningStatus) return { shacks: 0, dinheiro: 0 };\n    \n    return {\n      shacks: Math.floor(miningStatus.geracao_por_segundo.shacks * 3600),\n      dinheiro: Math.floor(miningStatus.geracao_por_segundo.dinheiro * 3600),\n    };\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"h-screen bg-gray-900 text-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-4\">🔒 Acesso Negado</h1>\n          <p className=\"text-gray-400\">Você precisa estar logado para acessar a mineração</p>\n        </div>\n      </div>\n    );\n  }\n\n  const hourlyGen = calculateHourlyGeneration();\n\n  return (\n    <div className=\"h-screen bg-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gray-800 border-b border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex items-center space-x-2\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600\"\n          >\n            <span className=\"text-lg\">←</span>\n          </button>\n          <div>\n            <h1 className=\"text-lg font-bold\">⛏️ Mineração</h1>\n            <p className=\"text-xs text-gray-400\">Sistema de Geração de Recursos</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-4\">\n        {/* Mensagens de erro/sucesso */}\n        {actionError && (\n          <div className=\"bg-red-900 border border-red-500 rounded-lg p-3\">\n            <p className=\"text-red-300 text-sm\">❌ {actionError}</p>\n          </div>\n        )}\n\n        {actionSuccess && (\n          <div className=\"bg-green-900 border border-green-500 rounded-lg p-3\">\n            <p className=\"text-green-300 text-sm\">✅ {actionSuccess}</p>\n          </div>\n        )}\n\n        {isLoading ? (\n          <div className=\"text-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4\"></div>\n            <p className=\"text-gray-400\">Carregando status de mineração...</p>\n          </div>\n        ) : miningStatus ? (\n          <>\n            {/* Status da Mineradora */}\n            <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n              <h3 className=\"text-lg font-semibold mb-4 text-white\">🏭 Status da Mineradora</h3>\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">\n                    Nível {miningStatus.nivel_mineradora}\n                  </div>\n                  <div className=\"text-xs text-gray-400\">Mineradora</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">\n                    {hourlyGen.shacks}/h\n                  </div>\n                  <div className=\"text-xs text-gray-400\">Shacks por Hora</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recursos Disponíveis */}\n            <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n              <h3 className=\"text-lg font-semibold mb-4 text-white\">💎 Recursos Disponíveis</h3>\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between items-center\">\n                  <div>\n                    <div className=\"font-bold text-blue-400\">\n                      {miningStatus.shacks_disponiveis} Shacks\n                    </div>\n                    <div className=\"text-xs text-gray-400\">Prontos para coleta</div>\n                  </div>\n                  <button\n                    onClick={handleCollectShacks}\n                    disabled={isCollecting || miningStatus.shacks_disponiveis <= 0}\n                    className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n                  >\n                    {isCollecting ? 'Coletando...' : 'Coletar Shacks'}\n                  </button>\n                </div>\n\n                <div className=\"flex justify-between items-center\">\n                  <div>\n                    <div className=\"font-bold text-green-400\">\n                      ${miningStatus.dinheiro_gerado.toLocaleString()}\n                    </div>\n                    <div className=\"text-xs text-gray-400\">Dinheiro gerado automaticamente</div>\n                  </div>\n                  <div className=\"text-xs text-gray-400\">\n                    {timeLeft}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Estatísticas de Geração */}\n            <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n              <h3 className=\"text-lg font-semibold mb-4 text-white\">📊 Estatísticas de Geração</h3>\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-blue-400\">\n                    {miningStatus.geracao_por_segundo.shacks.toFixed(3)}/s\n                  </div>\n                  <div className=\"text-xs text-gray-400\">Shacks por Segundo</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-green-400\">\n                    ${miningStatus.geracao_por_segundo.dinheiro.toFixed(2)}/s\n                  </div>\n                  <div className=\"text-xs text-gray-400\">Dinheiro por Segundo</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Upgrade da Mineradora */}\n            <div className=\"bg-gray-800 rounded-lg p-4 border border-gray-600\">\n              <h3 className=\"text-lg font-semibold mb-4 text-white\">🔧 Upgrade da Mineradora</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between items-center\">\n                  <div>\n                    <div className=\"font-bold text-white\">\n                      Nível {miningStatus.nivel_mineradora} → {miningStatus.nivel_mineradora + 1}\n                    </div>\n                    <div className=\"text-xs text-gray-400\">\n                      Aumenta geração de recursos\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm text-yellow-400\">\n                      ${miningStatus.custo_upgrade.toLocaleString()}\n                    </div>\n                    <div className=\"text-xs text-gray-400\">Custo</div>\n                  </div>\n                </div>\n\n                <div className=\"text-sm text-gray-300\">\n                  <div>Após upgrade:</div>\n                  <div>• Shacks: {((miningStatus.nivel_mineradora + 1) * 0.01 * 3600).toFixed(0)}/h</div>\n                  <div>• Dinheiro: ${((miningStatus.nivel_mineradora + 1) * 0.5 * 3600).toFixed(0)}/h</div>\n                </div>\n\n                <button\n                  onClick={handleUpgradeMiner}\n                  disabled={isUpgrading || !currentPlayer || (currentPlayer?.dinheiro || 0) < miningStatus.custo_upgrade}\n                  className=\"w-full py-3 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white rounded-lg font-semibold\"\n                >\n                  {isUpgrading ? 'Fazendo Upgrade...' : \n                   !currentPlayer || (currentPlayer?.dinheiro || 0) < miningStatus.custo_upgrade ? 'Dinheiro Insuficiente' :\n                   'Fazer Upgrade'}\n                </button>\n              </div>\n            </div>\n          </>\n        ) : (\n          <div className=\"text-center py-8\">\n            <div className=\"text-4xl mb-4\">⛏️</div>\n            <p className=\"text-gray-400\">Erro ao carregar dados de mineração</p>\n          </div>\n        )}\n      </div>\n\n      {/* Footer */}\n      <div className=\"bg-gray-800 border-t border-gray-700 p-4 flex-shrink-0\">\n        <div className=\"flex justify-center\">\n          <button \n            onClick={() => window.history.back()}\n            className=\"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\"\n          >\n            <span className=\"text-lg\">←</span>\n            <span className=\"text-sm\">Voltar</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MiningPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAc1C,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEW,aAAa;IAAEC;EAAe,CAAC,GAAGX,SAAS,CAAC,CAAC;EAErD,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;;EAEvE;EACA,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAS,EAAE,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd,IAAIW,eAAe,EAAE;MACnBiB,gBAAgB,CAAC,CAAC;;MAElB;MACA,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCF,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,KAAK,CAAC;MAET,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAAClB,eAAe,CAAC,CAAC;EAErBX,SAAS,CAAC,MAAM;IACd;IACA,MAAMgC,KAAK,GAAGF,WAAW,CAAC,MAAM;MAC9B,IAAIhB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEmB,aAAa,EAAE;QAC/B,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAACrB,YAAY,CAACmB,aAAa,CAAC;QACxD,MAAMG,WAAW,GAAG,IAAID,IAAI,CAACD,WAAW,CAACG,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QACtE,MAAMC,GAAG,GAAG,IAAIH,IAAI,CAAC,CAAC;QAEtB,IAAIG,GAAG,IAAIF,WAAW,EAAE;UACtBT,WAAW,CAAC,yBAAyB,CAAC;QACxC,CAAC,MAAM;UACL,MAAMY,IAAI,GAAGH,WAAW,CAACC,OAAO,CAAC,CAAC,GAAGC,GAAG,CAACD,OAAO,CAAC,CAAC;UAClD,MAAMG,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,GAAG,KAAK,CAAC;UACxC,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEH,IAAI,GAAG,KAAK,GAAI,IAAI,CAAC;UACjDZ,WAAW,CAAC,GAAGa,OAAO,KAAKG,OAAO,GAAG,CAAC;QACxC;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMZ,aAAa,CAACC,KAAK,CAAC;EACnC,CAAC,EAAE,CAAClB,YAAY,CAAC,CAAC;EAElB,MAAMc,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCX,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAM2B,QAAQ,GAAG,MAAMzC,OAAO,CAAC0C,eAAe,CAAC,CAAC;MAChD,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpB/B,eAAe,CAAC6B,QAAQ,CAACG,MAAM,CAAC;MAClC,CAAC,MAAM;QACL;QACAC,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DD,oBAAoB,CAAC,CAAC;IACxB,CAAC,SAAS;MACR/B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM+B,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACpC,aAAa,EAAE;IAEpB,MAAMuC,UAAwB,GAAG;MAC/BC,gBAAgB,EAAE,CAAAxC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwC,gBAAgB,KAAI,CAAC;MACtDC,kBAAkB,EAAEZ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACa,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE;MACxDC,eAAe,EAAEd,IAAI,CAACC,KAAK,CAACD,IAAI,CAACa,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI;MACxDrB,aAAa,EAAE,IAAIE,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACkB,WAAW,CAAC,CAAC;MAAE;MACpEC,mBAAmB,EAAE;QACnBC,MAAM,EAAE,CAAC,CAAA9C,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwC,gBAAgB,KAAI,CAAC,IAAI,IAAI;QACrDO,QAAQ,EAAE,CAAC,CAAA/C,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwC,gBAAgB,KAAI,CAAC,IAAI;MACrD,CAAC;MACDQ,aAAa,EAAEnB,IAAI,CAACC,KAAK,CAAC,IAAI,GAAGD,IAAI,CAACoB,GAAG,CAAC,GAAG,EAAE,CAAC,CAAAjD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwC,gBAAgB,KAAI,CAAC,IAAI,CAAC,CAAC;IAC5F,CAAC;IAEDrC,eAAe,CAACoC,UAAU,CAAC;EAC7B,CAAC;EAED,MAAMW,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC3C,eAAe,CAAC,IAAI,CAAC;IACrBI,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAMzC,OAAO,CAAC4D,aAAa,CAAC,CAAC;MAE9C,IAAInB,QAAQ,CAACE,OAAO,EAAE;QACpBrB,gBAAgB,CAAC,aAAamB,QAAQ,CAACoB,gBAAgB,UAAU,CAAC;;QAElE;QACA,MAAMnD,cAAc,CAAC,CAAC;QACtB,MAAMe,gBAAgB,CAAC,CAAC;MAC1B,CAAC,MAAM;QACLL,cAAc,CAACqB,QAAQ,CAACqB,QAAQ,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOhB,KAAU,EAAE;MACnB1B,cAAc,CAAC0B,KAAK,CAACiB,OAAO,IAAI,iBAAiB,CAAC;IACpD,CAAC,SAAS;MACR/C,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMgD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACrD,YAAY,IAAI,CAACF,aAAa,EAAE;IAErC,IAAI,CAAC,CAAAA,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+C,QAAQ,KAAI,CAAC,IAAI7C,YAAY,CAAC8C,aAAa,EAAE;MAC/DrC,cAAc,CAAC,oCAAoC,CAAC;MACpD;IACF;IAEAF,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAMzC,OAAO,CAACiE,YAAY,CAAC,CAAC;MAE7C,IAAIxB,QAAQ,CAACE,OAAO,EAAE;QACpBrB,gBAAgB,CAAC,oCAAoCX,YAAY,CAACsC,gBAAgB,GAAG,CAAC,GAAG,CAAC;;QAE1F;QACA,MAAMvC,cAAc,CAAC,CAAC;QACtB,MAAMe,gBAAgB,CAAC,CAAC;MAC1B,CAAC,MAAM;QACLL,cAAc,CAACqB,QAAQ,CAACqB,QAAQ,IAAI,uBAAuB,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOhB,KAAU,EAAE;MACnB1B,cAAc,CAAC0B,KAAK,CAACiB,OAAO,IAAI,iBAAiB,CAAC;IACpD,CAAC,SAAS;MACR7C,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMgD,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAACvD,YAAY,EAAE,OAAO;MAAE4C,MAAM,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAE,CAAC;IAEpD,OAAO;MACLD,MAAM,EAAEjB,IAAI,CAACC,KAAK,CAAC5B,YAAY,CAAC2C,mBAAmB,CAACC,MAAM,GAAG,IAAI,CAAC;MAClEC,QAAQ,EAAElB,IAAI,CAACC,KAAK,CAAC5B,YAAY,CAAC2C,mBAAmB,CAACE,QAAQ,GAAG,IAAI;IACvE,CAAC;EACH,CAAC;EAED,IAAI,CAAChD,eAAe,EAAE;IACpB,oBACEN,OAAA;MAAKiE,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/ElE,OAAA;QAAKiE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlE,OAAA;UAAIiE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DtE,OAAA;UAAGiE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMC,SAAS,GAAGP,yBAAyB,CAAC,CAAC;EAE7C,oBACEhE,OAAA;IAAKiE,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DlE,OAAA;MAAKiE,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrElE,OAAA;QAAKiE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClE,OAAA;UACEwE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCV,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7FlE,OAAA;YAAMiE,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACTtE,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAIiE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDtE,OAAA;YAAGiE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtE,OAAA;MAAKiE,SAAS,EAAC,sCAAsC;MAAAC,QAAA,GAElDjD,WAAW,iBACVjB,OAAA;QAAKiE,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DlE,OAAA;UAAGiE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,SAAE,EAACjD,WAAW;QAAA;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACN,EAEAnD,aAAa,iBACZnB,OAAA;QAAKiE,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClElE,OAAA;UAAGiE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,SAAE,EAAC/C,aAAa;QAAA;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CACN,EAEA3D,SAAS,gBACRX,OAAA;QAAKiE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlE,OAAA;UAAKiE,SAAS,EAAC;QAA2E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGtE,OAAA;UAAGiE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,GACJ7D,YAAY,gBACdT,OAAA,CAAAE,SAAA;QAAAgE,QAAA,gBAEElE,OAAA;UAAKiE,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChElE,OAAA;YAAIiE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFtE,OAAA;YAAKiE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrClE,OAAA;cAAKiE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlE,OAAA;gBAAKiE,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAC,WAC1C,EAACzD,YAAY,CAACsC,gBAAgB;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNtE,OAAA;cAAKiE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlE,OAAA;gBAAKiE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAC/CK,SAAS,CAAClB,MAAM,EAAC,IACpB;cAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtE,OAAA;UAAKiE,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChElE,OAAA;YAAIiE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFtE,OAAA;YAAKiE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlE,OAAA;cAAKiE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDlE,OAAA;gBAAAkE,QAAA,gBACElE,OAAA;kBAAKiE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,GACrCzD,YAAY,CAACuC,kBAAkB,EAAC,SACnC;gBAAA;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtE,OAAA;kBAAKiE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNtE,OAAA;gBACEwE,OAAO,EAAEf,mBAAoB;gBAC7BmB,QAAQ,EAAE/D,YAAY,IAAIJ,YAAY,CAACuC,kBAAkB,IAAI,CAAE;gBAC/DiB,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,EAE3GrD,YAAY,GAAG,cAAc,GAAG;cAAgB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENtE,OAAA;cAAKiE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDlE,OAAA;gBAAAkE,QAAA,gBACElE,OAAA;kBAAKiE,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,GAAC,GACvC,EAACzD,YAAY,CAACyC,eAAe,CAAC2B,cAAc,CAAC,CAAC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNtE,OAAA;kBAAKiE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnC7C;cAAQ;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtE,OAAA;UAAKiE,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChElE,OAAA;YAAIiE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrFtE,OAAA;YAAKiE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrClE,OAAA;cAAKiE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlE,OAAA;gBAAKiE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAC7CzD,YAAY,CAAC2C,mBAAmB,CAACC,MAAM,CAACyB,OAAO,CAAC,CAAC,CAAC,EAAC,IACtD;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNtE,OAAA;cAAKiE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlE,OAAA;gBAAKiE,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAC,GAC/C,EAACzD,YAAY,CAAC2C,mBAAmB,CAACE,QAAQ,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAC,IACzD;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtE,OAAA;UAAKiE,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChElE,OAAA;YAAIiE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnFtE,OAAA;YAAKiE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlE,OAAA;cAAKiE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDlE,OAAA;gBAAAkE,QAAA,gBACElE,OAAA;kBAAKiE,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAAC,WAC9B,EAACzD,YAAY,CAACsC,gBAAgB,EAAC,UAAG,EAACtC,YAAY,CAACsC,gBAAgB,GAAG,CAAC;gBAAA;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACNtE,OAAA;kBAAKiE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAEvC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlE,OAAA;kBAAKiE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,GAAC,GACtC,EAACzD,YAAY,CAAC8C,aAAa,CAACsB,cAAc,CAAC,CAAC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACNtE,OAAA;kBAAKiE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtE,OAAA;cAAKiE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpClE,OAAA;gBAAAkE,QAAA,EAAK;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxBtE,OAAA;gBAAAkE,QAAA,GAAK,iBAAU,EAAC,CAAC,CAACzD,YAAY,CAACsC,gBAAgB,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,EAAE+B,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvFtE,OAAA;gBAAAkE,QAAA,GAAK,oBAAa,EAAC,CAAC,CAACzD,YAAY,CAACsC,gBAAgB,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,EAAE+B,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,eAENtE,OAAA;cACEwE,OAAO,EAAEV,kBAAmB;cAC5Bc,QAAQ,EAAE7D,WAAW,IAAI,CAACR,aAAa,IAAI,CAAC,CAAAA,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+C,QAAQ,KAAI,CAAC,IAAI7C,YAAY,CAAC8C,aAAc;cACvGU,SAAS,EAAC,wGAAwG;cAAAC,QAAA,EAEjHnD,WAAW,GAAG,oBAAoB,GAClC,CAACR,aAAa,IAAI,CAAC,CAAAA,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+C,QAAQ,KAAI,CAAC,IAAI7C,YAAY,CAAC8C,aAAa,GAAG,uBAAuB,GACvG;YAAe;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CAAC,gBAEHtE,OAAA;QAAKiE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlE,OAAA;UAAKiE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCtE,OAAA;UAAGiE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNtE,OAAA;MAAKiE,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrElE,OAAA;QAAKiE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClClE,OAAA;UACEwE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCV,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExFlE,OAAA;YAAMiE,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCtE,OAAA;YAAMiE,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClE,EAAA,CA/UID,UAAoB;EAAA,QACUP,OAAO,EACCC,SAAS;AAAA;AAAAkF,EAAA,GAF/C5E,UAAoB;AAiV1B,eAAeA,UAAU;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}