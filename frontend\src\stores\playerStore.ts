import { create } from 'zustand';
import mockApiService, { PlayerData } from '../services/mockApi';

interface PlayerState {
  // Estado
  currentPlayer: PlayerData | null;
  isLoadingPlayer: boolean;
  playerError: string | null;

  // Ações
  loadPlayerData: () => Promise<void>;
  clearPlayerError: () => void;
  updatePlayerStats: (stats: Partial<PlayerData>) => void;
}

export const usePlayerStore = create<PlayerState>((set, get) => ({
  // Estado inicial
  currentPlayer: null,
  isLoadingPlayer: false,
  playerError: null,

  // Carregar dados do jogador
  loadPlayerData: async () => {
    const { isLoadingPlayer } = get();
    
    if (isLoadingPlayer) {
      console.log('PlayerStore - Carregamento já em andamento');
      return;
    }

    console.log('PlayerStore - Carregando dados do jogador...');
    set({ isLoadingPlayer: true, playerError: null });

    try {
      const response = await mockApiService.getPlayerData();
      
      if (response.sucesso && response.dados) {
        console.log('PlayerStore - Dados carregados:', response.dados);
        set({
          currentPlayer: response.dados,
          isLoadingPlayer: false,
          playerError: null,
        });
      } else {
        set({
          isLoadingPlayer: false,
          playerError: response.mensagem || 'Erro ao carregar dados',
        });
      }
    } catch (error) {
      console.error('PlayerStore - Erro:', error);
      set({
        isLoadingPlayer: false,
        playerError: 'Erro de conexão',
      });
    }
  },

  // Atualizar estatísticas do jogador
  updatePlayerStats: (stats: Partial<PlayerData>) => {
    const { currentPlayer } = get();
    
    if (currentPlayer) {
      console.log('PlayerStore - Atualizando stats:', stats);
      set({
        currentPlayer: {
          ...currentPlayer,
          ...stats,
        },
      });
    }
  },

  // Limpar erro
  clearPlayerError: () => {
    set({ playerError: null });
  },
}));

// Hook personalizado
export const usePlayer = () => {
  const {
    currentPlayer,
    isLoadingPlayer,
    playerError,
    loadPlayerData,
    clearPlayerError,
    updatePlayerStats,
  } = usePlayerStore();

  return {
    // Estado
    currentPlayer,
    isLoadingPlayer,
    playerError,
    
    // Ações
    loadPlayerData,
    clearPlayerError,
    updatePlayerStats,
    
    // Computed
    hasPlayerData: !!currentPlayer,
    playerLevel: currentPlayer?.nivel || 0,
    playerPoints: currentPlayer?.pontos || 0,
    playerRank: currentPlayer?.ranking || 0,
  };
};
