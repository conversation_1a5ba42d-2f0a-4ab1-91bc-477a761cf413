{"ast": null, "code": "\"use client\";\n\n// src/QueryClientProvider.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar QueryClientContext = React.createContext(void 0);\nvar useQueryClient = queryClient => {\n  const client = React.useContext(QueryClientContext);\n  if (queryClient) {\n    return queryClient;\n  }\n  if (!client) {\n    throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n  }\n  return client;\n};\nvar QueryClientProvider = ({\n  client,\n  children\n}) => {\n  React.useEffect(() => {\n    client.mount();\n    return () => {\n      client.unmount();\n    };\n  }, [client]);\n  return /* @__PURE__ */jsx(QueryClientContext.Provider, {\n    value: client,\n    children\n  });\n};\nexport { QueryClientContext, QueryClientProvider, useQueryClient };", "map": {"version": 3, "names": ["React", "jsx", "QueryClientContext", "createContext", "useQueryClient", "queryClient", "client", "useContext", "Error", "QueryClientProvider", "children", "useEffect", "mount", "unmount", "Provider", "value"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\react-query\\src\\QueryClientProvider.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport type { QueryClient } from '@tanstack/query-core'\n\nexport const QueryClientContext = React.createContext<QueryClient | undefined>(\n  undefined,\n)\n\nexport const useQueryClient = (queryClient?: QueryClient) => {\n  const client = React.useContext(QueryClientContext)\n\n  if (queryClient) {\n    return queryClient\n  }\n\n  if (!client) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return client\n}\n\nexport type QueryClientProviderProps = {\n  client: QueryClient\n  children?: React.ReactNode\n}\n\nexport const QueryClientProvider = ({\n  client,\n  children,\n}: QueryClientProviderProps): React.JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  return (\n    <QueryClientContext.Provider value={client}>\n      {children}\n    </QueryClientContext.Provider>\n  )\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AAuCnB,SAAAC,GAAA;AAnCG,IAAMC,kBAAA,GAA2BF,KAAA,CAAAG,aAAA,CACtC,MACF;AAEO,IAAMC,cAAA,GAAkBC,WAAA,IAA8B;EAC3D,MAAMC,MAAA,GAAeN,KAAA,CAAAO,UAAA,CAAWL,kBAAkB;EAElD,IAAIG,WAAA,EAAa;IACf,OAAOA,WAAA;EACT;EAEA,IAAI,CAACC,MAAA,EAAQ;IACX,MAAM,IAAIE,KAAA,CAAM,wDAAwD;EAC1E;EAEA,OAAOF,MAAA;AACT;AAOO,IAAMG,mBAAA,GAAsBA,CAAC;EAClCH,MAAA;EACAI;AACF,MAAmD;EAC3CV,KAAA,CAAAW,SAAA,CAAU,MAAM;IACpBL,MAAA,CAAOM,KAAA,CAAM;IACb,OAAO,MAAM;MACXN,MAAA,CAAOO,OAAA,CAAQ;IACjB;EACF,GAAG,CAACP,MAAM,CAAC;EAEX,OACE,eAAAL,GAAA,CAACC,kBAAA,CAAmBY,QAAA,EAAnB;IAA4BC,KAAA,EAAOT,MAAA;IACjCI;EAAA,CACH;AAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}