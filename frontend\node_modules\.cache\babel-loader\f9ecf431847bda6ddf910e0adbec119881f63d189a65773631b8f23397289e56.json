{"ast": null, "code": "// Mock API Service para desenvolvimento sem Flask\n// Simula as respostas da API real\nclass MockApiService{constructor(){this.delay=ms=>new Promise(resolve=>setTimeout(resolve,ms));}// Simular delay de rede\nasync simulateNetworkDelay(){await this.delay(Math.random()*1000+500);// 500-1500ms\n}// === AUTENTICAÇÃO ===\nasync login(credentials){await this.simulateNetworkDelay();console.log('MockAPI - Login:',credentials);// Simular validação\nif(!credentials.email||!credentials.password){return{sucesso:false,mensagem:'Email e senha são obrigatórios'};}// Simular usuário válido\nconst mockUser={uid:'mock-user-'+Date.now(),nick:credentials.email.split('@')[0]||'Player',email:credentials.email};const mockToken='mock-jwt-token-'+Date.now();return{sucesso:true,mensagem:'Login realizado com sucesso',user:mockUser,token:mockToken};}async register(userData){await this.simulateNetworkDelay();console.log('MockAPI - Register:',userData);// Simular validação\nif(!userData.email||!userData.password||!userData.nick){return{sucesso:false,mensagem:'Todos os campos são obrigatórios'};}// Simular criação de usuário\nconst mockUser={uid:'mock-user-'+Date.now(),nick:userData.nick,email:userData.email};const mockToken='mock-jwt-token-'+Date.now();return{sucesso:true,mensagem:'Conta criada com sucesso',user:mockUser,token:mockToken};}async verifyToken(){await this.simulateNetworkDelay();console.log('MockAPI - Verificando token...');// Simular token sempre válido para desenvolvimento\nreturn true;}// === DADOS DO JOGADOR ===\nasync getPlayerData(){await this.simulateNetworkDelay();console.log('MockAPI - Carregando dados do jogador...');const mockPlayerData={uid:'mock-player-123',nick:'TestPlayer',email:'<EMAIL>',pontos:1250,nivel:15,conquistas:42,ranking:7,grupo:'Alpha',ultimo_acesso:new Date().toISOString()};return{sucesso:true,mensagem:'Dados carregados com sucesso',dados:mockPlayerData};}// === CHAT ===\nasync getChatMessages(){await this.simulateNetworkDelay();const mockMessages=[{id:1,usuario:'Player1',mensagem:'Olá pessoal!',timestamp:new Date(Date.now()-300000).toISOString(),tipo:'global'},{id:2,usuario:'Player2',mensagem:'Como estão?',timestamp:new Date(Date.now()-180000).toISOString(),tipo:'global'},{id:3,usuario:'TestPlayer',mensagem:'Tudo bem por aqui!',timestamp:new Date(Date.now()-60000).toISOString(),tipo:'global'}];return{sucesso:true,mensagem:'Mensagens carregadas',dados:mockMessages};}async sendChatMessage(message){await this.simulateNetworkDelay();console.log('MockAPI - Enviando mensagem:',message);return{sucesso:true,mensagem:'Mensagem enviada com sucesso'};}// === SCANNER ===\nasync scanTarget(target){await this.simulateNetworkDelay();console.log('MockAPI - Escaneando alvo:',target);// Simular resultado de scan\nconst mockScanResult={alvo:target,vulnerabilidades:Math.floor(Math.random()*5)+1,dificuldade:['Fácil','Médio','Difícil'][Math.floor(Math.random()*3)],pontos_potenciais:Math.floor(Math.random()*500)+100,tempo_estimado:Math.floor(Math.random()*30)+5+' minutos'};return{sucesso:true,mensagem:'Scan realizado com sucesso',dados:mockScanResult};}// === LOJA ===\nasync getShopItems(){await this.simulateNetworkDelay();const mockItems=[{id:1,nome:'Scanner Avançado',descricao:'Melhora a precisão dos scans',preco:500,tipo:'ferramenta'},{id:2,nome:'Boost de XP',descricao:'Dobra a experiência por 1 hora',preco:200,tipo:'boost'},{id:3,nome:'Proteção Extra',descricao:'Reduz danos recebidos',preco:750,tipo:'defesa'}];return{sucesso:true,mensagem:'Itens da loja carregados',dados:mockItems};}}// Instância singleton\nconst mockApiService=new MockApiService();export default mockApiService;", "map": {"version": 3, "names": ["MockApiService", "constructor", "delay", "ms", "Promise", "resolve", "setTimeout", "simulateNetworkDelay", "Math", "random", "login", "credentials", "console", "log", "email", "password", "sucesso", "mensagem", "mockUser", "uid", "Date", "now", "nick", "split", "mockToken", "user", "token", "register", "userData", "verifyToken", "getPlayerData", "mockPlayerData", "pontos", "nivel", "conquistas", "ranking", "grupo", "ultimo_acesso", "toISOString", "dados", "getChatMessages", "mockMessages", "id", "usuario", "timestamp", "tipo", "sendChatMessage", "message", "scanTarget", "target", "mockScanResult", "alvo", "vulnerabilidades", "floor", "dificuldade", "pontos_potenciais", "tempo_estimado", "getShopItems", "mockItems", "nome", "descricao", "preco", "mockApiService"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/mockApi.ts"], "sourcesContent": ["// Mock API Service para desenvolvimento sem Flask\n// Simula as respostas da API real\n\nexport interface ApiResponse<T = any> {\n  sucesso: boolean;\n  mensagem: string;\n  dados?: T;\n  user?: any;\n  token?: string;\n}\n\nexport interface PlayerData {\n  uid: string;\n  nick: string;\n  email: string;\n  pontos: number;\n  nivel: number;\n  conquistas: number;\n  ranking: number;\n  grupo?: string;\n  ultimo_acesso: string;\n}\n\nclass MockApiService {\n  private delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\n\n  // Simular delay de rede\n  private async simulateNetworkDelay(): Promise<void> {\n    await this.delay(Math.random() * 1000 + 500); // 500-1500ms\n  }\n\n  // === AUTENTICAÇÃO ===\n  async login(credentials: { email: string; password: string }): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Login:', credentials);\n    \n    // Simular validação\n    if (!credentials.email || !credentials.password) {\n      return {\n        sucesso: false,\n        mensagem: 'Email e senha são obrigatórios'\n      };\n    }\n\n    // Simular usuário válido\n    const mockUser = {\n      uid: 'mock-user-' + Date.now(),\n      nick: credentials.email.split('@')[0] || 'Player',\n      email: credentials.email\n    };\n\n    const mockToken = 'mock-jwt-token-' + Date.now();\n\n    return {\n      sucesso: true,\n      mensagem: 'Login realizado com sucesso',\n      user: mockUser,\n      token: mockToken\n    };\n  }\n\n  async register(userData: { email: string; password: string; nick: string }): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Register:', userData);\n    \n    // Simular validação\n    if (!userData.email || !userData.password || !userData.nick) {\n      return {\n        sucesso: false,\n        mensagem: 'Todos os campos são obrigatórios'\n      };\n    }\n\n    // Simular criação de usuário\n    const mockUser = {\n      uid: 'mock-user-' + Date.now(),\n      nick: userData.nick,\n      email: userData.email\n    };\n\n    const mockToken = 'mock-jwt-token-' + Date.now();\n\n    return {\n      sucesso: true,\n      mensagem: 'Conta criada com sucesso',\n      user: mockUser,\n      token: mockToken\n    };\n  }\n\n  async verifyToken(): Promise<boolean> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Verificando token...');\n    \n    // Simular token sempre válido para desenvolvimento\n    return true;\n  }\n\n  // === DADOS DO JOGADOR ===\n  async getPlayerData(): Promise<ApiResponse<PlayerData>> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Carregando dados do jogador...');\n    \n    const mockPlayerData: PlayerData = {\n      uid: 'mock-player-123',\n      nick: 'TestPlayer',\n      email: '<EMAIL>',\n      pontos: 1250,\n      nivel: 15,\n      conquistas: 42,\n      ranking: 7,\n      grupo: 'Alpha',\n      ultimo_acesso: new Date().toISOString()\n    };\n\n    return {\n      sucesso: true,\n      mensagem: 'Dados carregados com sucesso',\n      dados: mockPlayerData\n    };\n  }\n\n  // === CHAT ===\n  async getChatMessages(): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    const mockMessages = [\n      {\n        id: 1,\n        usuario: 'Player1',\n        mensagem: 'Olá pessoal!',\n        timestamp: new Date(Date.now() - 300000).toISOString(),\n        tipo: 'global'\n      },\n      {\n        id: 2,\n        usuario: 'Player2',\n        mensagem: 'Como estão?',\n        timestamp: new Date(Date.now() - 180000).toISOString(),\n        tipo: 'global'\n      },\n      {\n        id: 3,\n        usuario: 'TestPlayer',\n        mensagem: 'Tudo bem por aqui!',\n        timestamp: new Date(Date.now() - 60000).toISOString(),\n        tipo: 'global'\n      }\n    ];\n\n    return {\n      sucesso: true,\n      mensagem: 'Mensagens carregadas',\n      dados: mockMessages\n    };\n  }\n\n  async sendChatMessage(message: { mensagem: string; tipo: string }): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Enviando mensagem:', message);\n    \n    return {\n      sucesso: true,\n      mensagem: 'Mensagem enviada com sucesso'\n    };\n  }\n\n  // === SCANNER ===\n  async scanTarget(target: string): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    console.log('MockAPI - Escaneando alvo:', target);\n    \n    // Simular resultado de scan\n    const mockScanResult = {\n      alvo: target,\n      vulnerabilidades: Math.floor(Math.random() * 5) + 1,\n      dificuldade: ['Fácil', 'Médio', 'Difícil'][Math.floor(Math.random() * 3)],\n      pontos_potenciais: Math.floor(Math.random() * 500) + 100,\n      tempo_estimado: Math.floor(Math.random() * 30) + 5 + ' minutos'\n    };\n\n    return {\n      sucesso: true,\n      mensagem: 'Scan realizado com sucesso',\n      dados: mockScanResult\n    };\n  }\n\n  // === LOJA ===\n  async getShopItems(): Promise<ApiResponse> {\n    await this.simulateNetworkDelay();\n    \n    const mockItems = [\n      {\n        id: 1,\n        nome: 'Scanner Avançado',\n        descricao: 'Melhora a precisão dos scans',\n        preco: 500,\n        tipo: 'ferramenta'\n      },\n      {\n        id: 2,\n        nome: 'Boost de XP',\n        descricao: 'Dobra a experiência por 1 hora',\n        preco: 200,\n        tipo: 'boost'\n      },\n      {\n        id: 3,\n        nome: 'Proteção Extra',\n        descricao: 'Reduz danos recebidos',\n        preco: 750,\n        tipo: 'defesa'\n      }\n    ];\n\n    return {\n      sucesso: true,\n      mensagem: 'Itens da loja carregados',\n      dados: mockItems\n    };\n  }\n}\n\n// Instância singleton\nconst mockApiService = new MockApiService();\nexport default mockApiService;\n"], "mappings": "AAAA;AACA;AAsBA,KAAM,CAAAA,cAAe,CAAAC,YAAA,OACXC,KAAK,CAAIC,EAAU,EAAK,GAAI,CAAAC,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAEF,EAAE,CAAC,CAAC,EAE/E;AACA,KAAc,CAAAI,oBAAoBA,CAAA,CAAkB,CAClD,KAAM,KAAI,CAACL,KAAK,CAACM,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,IAAI,CAAG,GAAG,CAAC,CAAE;AAChD,CAEA;AACA,KAAM,CAAAC,KAAKA,CAACC,WAAgD,CAAwB,CAClF,KAAM,KAAI,CAACJ,oBAAoB,CAAC,CAAC,CAEjCK,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEF,WAAW,CAAC,CAE5C;AACA,GAAI,CAACA,WAAW,CAACG,KAAK,EAAI,CAACH,WAAW,CAACI,QAAQ,CAAE,CAC/C,MAAO,CACLC,OAAO,CAAE,KAAK,CACdC,QAAQ,CAAE,gCACZ,CAAC,CACH,CAEA;AACA,KAAM,CAAAC,QAAQ,CAAG,CACfC,GAAG,CAAE,YAAY,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAC9BC,IAAI,CAAEX,WAAW,CAACG,KAAK,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAI,QAAQ,CACjDT,KAAK,CAAEH,WAAW,CAACG,KACrB,CAAC,CAED,KAAM,CAAAU,SAAS,CAAG,iBAAiB,CAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC,CAEhD,MAAO,CACLL,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,6BAA6B,CACvCQ,IAAI,CAAEP,QAAQ,CACdQ,KAAK,CAAEF,SACT,CAAC,CACH,CAEA,KAAM,CAAAG,QAAQA,CAACC,QAA2D,CAAwB,CAChG,KAAM,KAAI,CAACrB,oBAAoB,CAAC,CAAC,CAEjCK,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEe,QAAQ,CAAC,CAE5C;AACA,GAAI,CAACA,QAAQ,CAACd,KAAK,EAAI,CAACc,QAAQ,CAACb,QAAQ,EAAI,CAACa,QAAQ,CAACN,IAAI,CAAE,CAC3D,MAAO,CACLN,OAAO,CAAE,KAAK,CACdC,QAAQ,CAAE,kCACZ,CAAC,CACH,CAEA;AACA,KAAM,CAAAC,QAAQ,CAAG,CACfC,GAAG,CAAE,YAAY,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAC9BC,IAAI,CAAEM,QAAQ,CAACN,IAAI,CACnBR,KAAK,CAAEc,QAAQ,CAACd,KAClB,CAAC,CAED,KAAM,CAAAU,SAAS,CAAG,iBAAiB,CAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC,CAEhD,MAAO,CACLL,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,0BAA0B,CACpCQ,IAAI,CAAEP,QAAQ,CACdQ,KAAK,CAAEF,SACT,CAAC,CACH,CAEA,KAAM,CAAAK,WAAWA,CAAA,CAAqB,CACpC,KAAM,KAAI,CAACtB,oBAAoB,CAAC,CAAC,CAEjCK,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAE7C;AACA,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAiB,aAAaA,CAAA,CAAqC,CACtD,KAAM,KAAI,CAACvB,oBAAoB,CAAC,CAAC,CAEjCK,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CAEvD,KAAM,CAAAkB,cAA0B,CAAG,CACjCZ,GAAG,CAAE,iBAAiB,CACtBG,IAAI,CAAE,YAAY,CAClBR,KAAK,CAAE,kBAAkB,CACzBkB,MAAM,CAAE,IAAI,CACZC,KAAK,CAAE,EAAE,CACTC,UAAU,CAAE,EAAE,CACdC,OAAO,CAAE,CAAC,CACVC,KAAK,CAAE,OAAO,CACdC,aAAa,CAAE,GAAI,CAAAjB,IAAI,CAAC,CAAC,CAACkB,WAAW,CAAC,CACxC,CAAC,CAED,MAAO,CACLtB,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,8BAA8B,CACxCsB,KAAK,CAAER,cACT,CAAC,CACH,CAEA;AACA,KAAM,CAAAS,eAAeA,CAAA,CAAyB,CAC5C,KAAM,KAAI,CAACjC,oBAAoB,CAAC,CAAC,CAEjC,KAAM,CAAAkC,YAAY,CAAG,CACnB,CACEC,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,SAAS,CAClB1B,QAAQ,CAAE,cAAc,CACxB2B,SAAS,CAAE,GAAI,CAAAxB,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,MAAM,CAAC,CAACiB,WAAW,CAAC,CAAC,CACtDO,IAAI,CAAE,QACR,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,SAAS,CAClB1B,QAAQ,CAAE,aAAa,CACvB2B,SAAS,CAAE,GAAI,CAAAxB,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,MAAM,CAAC,CAACiB,WAAW,CAAC,CAAC,CACtDO,IAAI,CAAE,QACR,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,YAAY,CACrB1B,QAAQ,CAAE,oBAAoB,CAC9B2B,SAAS,CAAE,GAAI,CAAAxB,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,KAAK,CAAC,CAACiB,WAAW,CAAC,CAAC,CACrDO,IAAI,CAAE,QACR,CAAC,CACF,CAED,MAAO,CACL7B,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,sBAAsB,CAChCsB,KAAK,CAAEE,YACT,CAAC,CACH,CAEA,KAAM,CAAAK,eAAeA,CAACC,OAA2C,CAAwB,CACvF,KAAM,KAAI,CAACxC,oBAAoB,CAAC,CAAC,CAEjCK,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEkC,OAAO,CAAC,CAEpD,MAAO,CACL/B,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,8BACZ,CAAC,CACH,CAEA;AACA,KAAM,CAAA+B,UAAUA,CAACC,MAAc,CAAwB,CACrD,KAAM,KAAI,CAAC1C,oBAAoB,CAAC,CAAC,CAEjCK,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEoC,MAAM,CAAC,CAEjD;AACA,KAAM,CAAAC,cAAc,CAAG,CACrBC,IAAI,CAAEF,MAAM,CACZG,gBAAgB,CAAE5C,IAAI,CAAC6C,KAAK,CAAC7C,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAG,CAAC,CACnD6C,WAAW,CAAE,CAAC,OAAO,CAAE,OAAO,CAAE,SAAS,CAAC,CAAC9C,IAAI,CAAC6C,KAAK,CAAC7C,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAC,CACzE8C,iBAAiB,CAAE/C,IAAI,CAAC6C,KAAK,CAAC7C,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAC,CAAG,GAAG,CACxD+C,cAAc,CAAEhD,IAAI,CAAC6C,KAAK,CAAC7C,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAC,CAAG,CAAC,CAAG,UACvD,CAAC,CAED,MAAO,CACLO,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,4BAA4B,CACtCsB,KAAK,CAAEW,cACT,CAAC,CACH,CAEA;AACA,KAAM,CAAAO,YAAYA,CAAA,CAAyB,CACzC,KAAM,KAAI,CAAClD,oBAAoB,CAAC,CAAC,CAEjC,KAAM,CAAAmD,SAAS,CAAG,CAChB,CACEhB,EAAE,CAAE,CAAC,CACLiB,IAAI,CAAE,kBAAkB,CACxBC,SAAS,CAAE,8BAA8B,CACzCC,KAAK,CAAE,GAAG,CACVhB,IAAI,CAAE,YACR,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLiB,IAAI,CAAE,aAAa,CACnBC,SAAS,CAAE,gCAAgC,CAC3CC,KAAK,CAAE,GAAG,CACVhB,IAAI,CAAE,OACR,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLiB,IAAI,CAAE,gBAAgB,CACtBC,SAAS,CAAE,uBAAuB,CAClCC,KAAK,CAAE,GAAG,CACVhB,IAAI,CAAE,QACR,CAAC,CACF,CAED,MAAO,CACL7B,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,0BAA0B,CACpCsB,KAAK,CAAEmB,SACT,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAI,cAAc,CAAG,GAAI,CAAA9D,cAAc,CAAC,CAAC,CAC3C,cAAe,CAAA8D,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}