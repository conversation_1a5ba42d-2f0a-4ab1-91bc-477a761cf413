{"ast": null, "code": "import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { GAME_CONFIG, getPlayerLevel, calculateUpgradeCost, calculateXpReward } from '../types/game';\nconst initialPlayerApps = {\n  ...GAME_CONFIG.INITIAL_APPS\n};\nexport const useHackGameStore = create()(persist((set, get) => ({\n  // Initial state\n  player: null,\n  playerApps: initialPlayerApps,\n  isOnline: false,\n  notifications: [],\n  availableTargets: [],\n  hackHistory: [],\n  currentScreen: 'home',\n  isLoading: false,\n  error: null,\n  // Actions\n  initializePlayer: async (nick, email) => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const result = await gameService.createPlayer(nick, email);\n      if (result.success && result.player) {\n        const appsResult = await gameService.getPlayerApps(result.player.id);\n        if (appsResult.success && appsResult.apps) {\n          set({\n            player: result.player,\n            playerApps: {\n              antivirus: appsResult.apps.antivirus,\n              bankguard: appsResult.apps.bankguard,\n              bruteforce: appsResult.apps.bruteforce,\n              sdk: appsResult.apps.sdk,\n              firewall: appsResult.apps.firewall,\n              malwarekit: appsResult.apps.malwarekit,\n              proxyvpn: appsResult.apps.proxyvpn\n            },\n            isOnline: true,\n            isLoading: false\n          });\n\n          // Carregar notificações\n          await get().loadNotifications();\n          return true;\n        }\n      }\n      set({\n        error: result.error || 'Erro ao criar jogador',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  loadPlayer: async playerId => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const [playerResult, appsResult] = await Promise.all([gameService.getPlayer(playerId), gameService.getPlayerApps(playerId)]);\n      if (playerResult.success && playerResult.player && appsResult.success && appsResult.apps) {\n        set({\n          player: playerResult.player,\n          playerApps: {\n            antivirus: appsResult.apps.antivirus,\n            bankguard: appsResult.apps.bankguard,\n            bruteforce: appsResult.apps.bruteforce,\n            sdk: appsResult.apps.sdk,\n            firewall: appsResult.apps.firewall,\n            malwarekit: appsResult.apps.malwarekit,\n            proxyvpn: appsResult.apps.proxyvpn\n          },\n          isOnline: true,\n          isLoading: false\n        });\n\n        // Carregar notificações\n        await get().loadNotifications();\n        return true;\n      }\n      set({\n        error: 'Erro ao carregar jogador',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  updatePlayerApps: async apps => {\n    const state = get();\n    if (!state.player) return false;\n    try {\n      const result = await gameService.updatePlayerApps(state.player.id, apps);\n      if (result.success) {\n        set(state => ({\n          playerApps: {\n            ...state.playerApps,\n            ...apps\n          }\n        }));\n        return true;\n      }\n      set({\n        error: result.error || 'Erro ao atualizar apps'\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message\n      });\n      return false;\n    }\n  },\n  upgradeApp: async appId => {\n    const state = get();\n    const {\n      player,\n      playerApps\n    } = state;\n    if (!player) return false;\n    const currentLevel = playerApps[appId];\n    const upgradeCost = calculateUpgradeCost(currentLevel);\n\n    // Verificar se tem dinheiro suficiente\n    if (player.cash < upgradeCost) {\n      set({\n        error: 'Dinheiro insuficiente para upgrade!'\n      });\n      return false;\n    }\n\n    // Verificar se não atingiu o nível máximo\n    if (currentLevel >= GAME_CONFIG.MAX_APP_LEVEL) {\n      set({\n        error: 'App já está no nível máximo!'\n      });\n      return false;\n    }\n    try {\n      // Realizar upgrade no servidor\n      const newLevel = currentLevel + 1;\n      const newCash = player.cash - upgradeCost;\n      const xpReward = calculateXpReward(currentLevel);\n      const newXp = player.xp + xpReward;\n      const newPlayerLevel = getPlayerLevel(newXp);\n\n      // Atualizar apps\n      const appsUpdate = {\n        [appId]: newLevel\n      };\n      const appsResult = await gameService.updatePlayerApps(player.id, appsUpdate);\n\n      // Atualizar player\n      const playerUpdate = {\n        cash: newCash,\n        xp: newXp,\n        level: newPlayerLevel\n      };\n      const playerResult = await gameService.updatePlayer(player.id, playerUpdate);\n      if (appsResult.success && playerResult.success) {\n        // Atualizar estado local\n        set(state => ({\n          player: {\n            ...state.player,\n            cash: newCash,\n            xp: newXp,\n            level: newPlayerLevel\n          },\n          playerApps: {\n            ...state.playerApps,\n            [appId]: newLevel\n          },\n          error: null\n        }));\n\n        // Adicionar notificação\n        await get().addNotification({\n          type: 'upgrade',\n          title: 'Upgrade Concluído!',\n          message: `${appId} foi atualizado para nível ${newLevel}. +${xpReward} XP`\n        });\n\n        // Verificar se subiu de nível\n        if (newPlayerLevel > player.level) {\n          await get().addNotification({\n            type: 'level',\n            title: 'Level Up!',\n            message: `Parabéns! Você atingiu o nível ${newPlayerLevel}!`\n          });\n        }\n        return true;\n      }\n      set({\n        error: 'Erro ao realizar upgrade'\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message\n      });\n      return false;\n    }\n  },\n  addXp: async amount => {\n    const state = get();\n    if (!state.player) return;\n    try {\n      const newTotalXp = state.player.xp + amount;\n      const newLevel = getPlayerLevel(newTotalXp);\n      const leveledUp = newLevel > state.player.level;\n      const result = await gameService.updatePlayer(state.player.id, {\n        xp: newTotalXp,\n        level: newLevel\n      });\n      if (result.success) {\n        set(state => ({\n          player: state.player ? {\n            ...state.player,\n            xp: newTotalXp,\n            level: newLevel\n          } : null\n        }));\n\n        // Se subiu de nível, adicionar notificação\n        if (leveledUp) {\n          await get().addNotification({\n            type: 'level',\n            title: 'Level Up!',\n            message: `Parabéns! Você atingiu o nível ${newLevel}!`\n          });\n        }\n      }\n    } catch (error) {\n      set({\n        error: error.message\n      });\n    }\n  },\n  addCash: async amount => {\n    const state = get();\n    if (!state.player) return;\n    try {\n      const newCash = state.player.cash + amount;\n      const result = await gameService.updatePlayer(state.player.id, {\n        cash: newCash\n      });\n      if (result.success) {\n        set(state => ({\n          player: state.player ? {\n            ...state.player,\n            cash: newCash\n          } : null\n        }));\n      }\n    } catch (error) {\n      set({\n        error: error.message\n      });\n    }\n  },\n  spendCash: async amount => {\n    const state = get();\n    if (!state.player || state.player.cash < amount) {\n      return false;\n    }\n    try {\n      const newCash = state.player.cash - amount;\n      const result = await gameService.updatePlayer(state.player.id, {\n        cash: newCash\n      });\n      if (result.success) {\n        set(state => ({\n          player: state.player ? {\n            ...state.player,\n            cash: newCash\n          } : null\n        }));\n        return true;\n      }\n      return false;\n    } catch (error) {\n      set({\n        error: error.message\n      });\n      return false;\n    }\n  },\n  setCurrentScreen: screen => {\n    set({\n      currentScreen: screen\n    });\n  },\n  loadNotifications: async () => {\n    const state = get();\n    if (!state.player) return;\n    try {\n      const result = await gameService.getNotifications(state.player.id);\n      if (result.success && result.notifications) {\n        set({\n          notifications: result.notifications\n        });\n      }\n    } catch (error) {\n      set({\n        error: error.message\n      });\n    }\n  },\n  addNotification: async notification => {\n    const state = get();\n    if (!state.player) return;\n    try {\n      const result = await gameService.createNotification(state.player.id, notification);\n      if (result.success) {\n        // Recarregar notificações\n        await get().loadNotifications();\n      }\n    } catch (error) {\n      set({\n        error: error.message\n      });\n    }\n  },\n  markNotificationRead: async id => {\n    try {\n      const result = await gameService.markNotificationRead(id);\n      if (result.success) {\n        set(state => ({\n          notifications: state.notifications.map(notif => notif.id === id ? {\n            ...notif,\n            read: true\n          } : notif)\n        }));\n      }\n    } catch (error) {\n      set({\n        error: error.message\n      });\n    }\n  },\n  clearNotifications: () => {\n    set({\n      notifications: []\n    });\n  },\n  setError: error => {\n    set({\n      error\n    });\n    if (error) {\n      // Limpar erro após 5 segundos\n      setTimeout(() => {\n        set({\n          error: null\n        });\n      }, 5000);\n    }\n  },\n  setLoading: loading => {\n    set({\n      isLoading: loading\n    });\n  },\n  syncWithServer: async () => {\n    const state = get();\n    if (!state.player) return;\n    try {\n      set({\n        isLoading: true\n      });\n\n      // Recarregar dados do jogador\n      await get().loadPlayer(state.player.id);\n      set({\n        isLoading: false\n      });\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n    }\n  },\n  reset: () => {\n    set({\n      player: null,\n      playerApps: initialPlayerApps,\n      isOnline: false,\n      notifications: [],\n      availableTargets: [],\n      hackHistory: [],\n      currentScreen: 'home',\n      isLoading: false,\n      error: null\n    });\n  }\n}), {\n  name: 'shack-game-storage',\n  partialize: state => ({\n    player: state.player,\n    currentScreen: state.currentScreen\n  }),\n  // Recarregar dados do servidor quando a store for hidratada\n  onRehydrateStorage: () => state => {\n    var _state$player;\n    if (state !== null && state !== void 0 && (_state$player = state.player) !== null && _state$player !== void 0 && _state$player.id) {\n      // Recarregar dados do servidor em background\n      setTimeout(() => {\n        state.syncWithServer();\n      }, 1000);\n    }\n  }\n}));", "map": {"version": 3, "names": ["create", "persist", "GAME_CONFIG", "getPlayerLevel", "calculateUpgradeCost", "calculateXpReward", "initialPlayerApps", "INITIAL_APPS", "useHackGameStore", "set", "get", "player", "playerApps", "isOnline", "notifications", "availableTargets", "hack<PERSON><PERSON><PERSON>", "currentScreen", "isLoading", "error", "initializePlayer", "nick", "email", "result", "gameService", "createPlayer", "success", "appsResult", "getPlayerApps", "id", "apps", "antivirus", "bankguard", "bruteforce", "sdk", "firewall", "malwarekit", "proxyvpn", "loadNotifications", "message", "loadPlayer", "playerId", "player<PERSON><PERSON><PERSON>", "Promise", "all", "getPlayer", "updatePlayerApps", "state", "upgradeApp", "appId", "currentLevel", "upgradeCost", "cash", "MAX_APP_LEVEL", "newLevel", "newCash", "xpReward", "newXp", "xp", "newPlayerLevel", "appsUpdate", "playerUpdate", "level", "updatePlayer", "addNotification", "type", "title", "addXp", "amount", "newTotalXp", "leveledUp", "addCash", "spendCash", "setCurrentScreen", "screen", "getNotifications", "notification", "createNotification", "markNotificationRead", "map", "notif", "read", "clearNotifications", "setError", "setTimeout", "setLoading", "loading", "syncWithServer", "reset", "name", "partialize", "onRehydrateStorage", "_state$player"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/hackGameStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport {\n  GAME_CONFIG,\n  getPlayerLevel,\n  getXpForNextLevel,\n  calculateUpgradeCost,\n  calculateXpReward,\n  Player,\n  PlayerApps,\n  GameNotification,\n  HackTarget\n} from '../types/game';\nimport { backendService, BackendPlayer } from '../services/backendService';\n\ninterface GameState {\n  // Player data\n  player: Player | null;\n  playerApps: PlayerApps;\n  \n  // Game state\n  isOnline: boolean;\n  notifications: GameNotification[];\n  \n  // Targets and hacking\n  availableTargets: HackTarget[];\n  hackHistory: any[];\n  \n  // UI state\n  currentScreen: 'home' | 'apps' | 'scanner' | 'terminal' | 'profile' | 'settings' | 'shop' | 'ranking' | 'logs' | 'chat';\n  isLoading: boolean;\n  error: string | null;\n  \n  // Actions\n  initializePlayer: (nick: string, email: string) => Promise<boolean>;\n  loadPlayer: (playerId: string) => Promise<boolean>;\n  updatePlayerApps: (apps: Partial<PlayerApps>) => Promise<boolean>;\n  upgradeApp: (appId: keyof PlayerApps) => Promise<boolean>;\n  addXp: (amount: number) => Promise<void>;\n  addCash: (amount: number) => Promise<void>;\n  spendCash: (amount: number) => Promise<boolean>;\n  setCurrentScreen: (screen: GameState['currentScreen']) => void;\n  loadNotifications: () => Promise<void>;\n  addNotification: (notification: Omit<GameNotification, 'id' | 'player_id' | 'created_at' | 'read'>) => Promise<void>;\n  markNotificationRead: (id: string) => Promise<void>;\n  clearNotifications: () => void;\n  setError: (error: string | null) => void;\n  setLoading: (loading: boolean) => void;\n  syncWithServer: () => Promise<void>;\n  reset: () => void;\n}\n\nconst initialPlayerApps: PlayerApps = { ...GAME_CONFIG.INITIAL_APPS };\n\nexport const useHackGameStore = create<GameState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      player: null,\n      playerApps: initialPlayerApps,\n      isOnline: false,\n      notifications: [],\n      availableTargets: [],\n      hackHistory: [],\n      currentScreen: 'home',\n      isLoading: false,\n      error: null,\n\n      // Actions\n      initializePlayer: async (nick: string, email: string) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const result = await gameService.createPlayer(nick, email);\n\n          if (result.success && result.player) {\n            const appsResult = await gameService.getPlayerApps(result.player.id);\n\n            if (appsResult.success && appsResult.apps) {\n              set({\n                player: result.player,\n                playerApps: {\n                  antivirus: appsResult.apps.antivirus,\n                  bankguard: appsResult.apps.bankguard,\n                  bruteforce: appsResult.apps.bruteforce,\n                  sdk: appsResult.apps.sdk,\n                  firewall: appsResult.apps.firewall,\n                  malwarekit: appsResult.apps.malwarekit,\n                  proxyvpn: appsResult.apps.proxyvpn,\n                },\n                isOnline: true,\n                isLoading: false,\n              });\n\n              // Carregar notificações\n              await get().loadNotifications();\n              return true;\n            }\n          }\n\n          set({ error: result.error || 'Erro ao criar jogador', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      loadPlayer: async (playerId: string) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const [playerResult, appsResult] = await Promise.all([\n            gameService.getPlayer(playerId),\n            gameService.getPlayerApps(playerId)\n          ]);\n\n          if (playerResult.success && playerResult.player && appsResult.success && appsResult.apps) {\n            set({\n              player: playerResult.player,\n              playerApps: {\n                antivirus: appsResult.apps.antivirus,\n                bankguard: appsResult.apps.bankguard,\n                bruteforce: appsResult.apps.bruteforce,\n                sdk: appsResult.apps.sdk,\n                firewall: appsResult.apps.firewall,\n                malwarekit: appsResult.apps.malwarekit,\n                proxyvpn: appsResult.apps.proxyvpn,\n              },\n              isOnline: true,\n              isLoading: false,\n            });\n\n            // Carregar notificações\n            await get().loadNotifications();\n            return true;\n          }\n\n          set({ error: 'Erro ao carregar jogador', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      updatePlayerApps: async (apps: Partial<PlayerApps>) => {\n        const state = get();\n        if (!state.player) return false;\n\n        try {\n          const result = await gameService.updatePlayerApps(state.player.id, apps);\n\n          if (result.success) {\n            set((state) => ({\n              playerApps: { ...state.playerApps, ...apps }\n            }));\n            return true;\n          }\n\n          set({ error: result.error || 'Erro ao atualizar apps' });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message });\n          return false;\n        }\n      },\n\n      upgradeApp: async (appId: keyof PlayerApps) => {\n        const state = get();\n        const { player, playerApps } = state;\n\n        if (!player) return false;\n\n        const currentLevel = playerApps[appId];\n        const upgradeCost = calculateUpgradeCost(currentLevel);\n\n        // Verificar se tem dinheiro suficiente\n        if (player.cash < upgradeCost) {\n          set({ error: 'Dinheiro insuficiente para upgrade!' });\n          return false;\n        }\n\n        // Verificar se não atingiu o nível máximo\n        if (currentLevel >= GAME_CONFIG.MAX_APP_LEVEL) {\n          set({ error: 'App já está no nível máximo!' });\n          return false;\n        }\n\n        try {\n          // Realizar upgrade no servidor\n          const newLevel = currentLevel + 1;\n          const newCash = player.cash - upgradeCost;\n          const xpReward = calculateXpReward(currentLevel);\n          const newXp = player.xp + xpReward;\n          const newPlayerLevel = getPlayerLevel(newXp);\n\n          // Atualizar apps\n          const appsUpdate = { [appId]: newLevel };\n          const appsResult = await gameService.updatePlayerApps(player.id, appsUpdate);\n\n          // Atualizar player\n          const playerUpdate = {\n            cash: newCash,\n            xp: newXp,\n            level: newPlayerLevel\n          };\n          const playerResult = await gameService.updatePlayer(player.id, playerUpdate);\n\n          if (appsResult.success && playerResult.success) {\n            // Atualizar estado local\n            set((state) => ({\n              player: {\n                ...state.player!,\n                cash: newCash,\n                xp: newXp,\n                level: newPlayerLevel,\n              },\n              playerApps: {\n                ...state.playerApps,\n                [appId]: newLevel,\n              },\n              error: null,\n            }));\n\n            // Adicionar notificação\n            await get().addNotification({\n              type: 'upgrade',\n              title: 'Upgrade Concluído!',\n              message: `${appId} foi atualizado para nível ${newLevel}. +${xpReward} XP`,\n            });\n\n            // Verificar se subiu de nível\n            if (newPlayerLevel > player.level) {\n              await get().addNotification({\n                type: 'level',\n                title: 'Level Up!',\n                message: `Parabéns! Você atingiu o nível ${newPlayerLevel}!`,\n              });\n            }\n\n            return true;\n          }\n\n          set({ error: 'Erro ao realizar upgrade' });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message });\n          return false;\n        }\n      },\n\n      addXp: async (amount: number) => {\n        const state = get();\n        if (!state.player) return;\n\n        try {\n          const newTotalXp = state.player.xp + amount;\n          const newLevel = getPlayerLevel(newTotalXp);\n          const leveledUp = newLevel > state.player.level;\n\n          const result = await gameService.updatePlayer(state.player.id, {\n            xp: newTotalXp,\n            level: newLevel,\n          });\n\n          if (result.success) {\n            set((state) => ({\n              player: state.player ? {\n                ...state.player,\n                xp: newTotalXp,\n                level: newLevel,\n              } : null\n            }));\n\n            // Se subiu de nível, adicionar notificação\n            if (leveledUp) {\n              await get().addNotification({\n                type: 'level',\n                title: 'Level Up!',\n                message: `Parabéns! Você atingiu o nível ${newLevel}!`,\n              });\n            }\n          }\n        } catch (error: any) {\n          set({ error: error.message });\n        }\n      },\n\n      addCash: async (amount: number) => {\n        const state = get();\n        if (!state.player) return;\n\n        try {\n          const newCash = state.player.cash + amount;\n          const result = await gameService.updatePlayer(state.player.id, { cash: newCash });\n\n          if (result.success) {\n            set((state) => ({\n              player: state.player ? {\n                ...state.player,\n                cash: newCash\n              } : null\n            }));\n          }\n        } catch (error: any) {\n          set({ error: error.message });\n        }\n      },\n\n      spendCash: async (amount: number) => {\n        const state = get();\n        if (!state.player || state.player.cash < amount) {\n          return false;\n        }\n\n        try {\n          const newCash = state.player.cash - amount;\n          const result = await gameService.updatePlayer(state.player.id, { cash: newCash });\n\n          if (result.success) {\n            set((state) => ({\n              player: state.player ? {\n                ...state.player,\n                cash: newCash\n              } : null\n            }));\n            return true;\n          }\n\n          return false;\n        } catch (error: any) {\n          set({ error: error.message });\n          return false;\n        }\n      },\n\n      setCurrentScreen: (screen: GameState['currentScreen']) => {\n        set({ currentScreen: screen });\n      },\n\n      loadNotifications: async () => {\n        const state = get();\n        if (!state.player) return;\n\n        try {\n          const result = await gameService.getNotifications(state.player.id);\n\n          if (result.success && result.notifications) {\n            set({ notifications: result.notifications });\n          }\n        } catch (error: any) {\n          set({ error: error.message });\n        }\n      },\n\n      addNotification: async (notification: Omit<GameNotification, 'id' | 'player_id' | 'created_at' | 'read'>) => {\n        const state = get();\n        if (!state.player) return;\n\n        try {\n          const result = await gameService.createNotification(state.player.id, notification);\n\n          if (result.success) {\n            // Recarregar notificações\n            await get().loadNotifications();\n          }\n        } catch (error: any) {\n          set({ error: error.message });\n        }\n      },\n\n      markNotificationRead: async (id: string) => {\n        try {\n          const result = await gameService.markNotificationRead(id);\n\n          if (result.success) {\n            set((state) => ({\n              notifications: state.notifications.map(notif =>\n                notif.id === id ? { ...notif, read: true } : notif\n              )\n            }));\n          }\n        } catch (error: any) {\n          set({ error: error.message });\n        }\n      },\n\n      clearNotifications: () => {\n        set({ notifications: [] });\n      },\n\n      setError: (error: string | null) => {\n        set({ error });\n        if (error) {\n          // Limpar erro após 5 segundos\n          setTimeout(() => {\n            set({ error: null });\n          }, 5000);\n        }\n      },\n\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n\n      syncWithServer: async () => {\n        const state = get();\n        if (!state.player) return;\n\n        try {\n          set({ isLoading: true });\n\n          // Recarregar dados do jogador\n          await get().loadPlayer(state.player.id);\n\n          set({ isLoading: false });\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n        }\n      },\n\n      reset: () => {\n        set({\n          player: null,\n          playerApps: initialPlayerApps,\n          isOnline: false,\n          notifications: [],\n          availableTargets: [],\n          hackHistory: [],\n          currentScreen: 'home',\n          isLoading: false,\n          error: null,\n        });\n      },\n    }),\n    {\n      name: 'shack-game-storage',\n      partialize: (state) => ({\n        player: state.player,\n        currentScreen: state.currentScreen,\n      }),\n      // Recarregar dados do servidor quando a store for hidratada\n      onRehydrateStorage: () => (state) => {\n        if (state?.player?.id) {\n          // Recarregar dados do servidor em background\n          setTimeout(() => {\n            state.syncWithServer();\n          }, 1000);\n        }\n      },\n    }\n  )\n);\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SACEC,WAAW,EACXC,cAAc,EAEdC,oBAAoB,EACpBC,iBAAiB,QAKZ,eAAe;AAwCtB,MAAMC,iBAA6B,GAAG;EAAE,GAAGJ,WAAW,CAACK;AAAa,CAAC;AAErE,OAAO,MAAMC,gBAAgB,GAAGR,MAAM,CAAY,CAAC,CACjDC,OAAO,CACL,CAACQ,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAEN,iBAAiB;EAC7BO,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE,EAAE;EACjBC,gBAAgB,EAAE,EAAE;EACpBC,WAAW,EAAE,EAAE;EACfC,aAAa,EAAE,MAAM;EACrBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEX;EACAC,gBAAgB,EAAE,MAAAA,CAAOC,IAAY,EAAEC,KAAa,KAAK;IACvDb,GAAG,CAAC;MAAES,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMC,WAAW,CAACC,YAAY,CAACJ,IAAI,EAAEC,KAAK,CAAC;MAE1D,IAAIC,MAAM,CAACG,OAAO,IAAIH,MAAM,CAACZ,MAAM,EAAE;QACnC,MAAMgB,UAAU,GAAG,MAAMH,WAAW,CAACI,aAAa,CAACL,MAAM,CAACZ,MAAM,CAACkB,EAAE,CAAC;QAEpE,IAAIF,UAAU,CAACD,OAAO,IAAIC,UAAU,CAACG,IAAI,EAAE;UACzCrB,GAAG,CAAC;YACFE,MAAM,EAAEY,MAAM,CAACZ,MAAM;YACrBC,UAAU,EAAE;cACVmB,SAAS,EAAEJ,UAAU,CAACG,IAAI,CAACC,SAAS;cACpCC,SAAS,EAAEL,UAAU,CAACG,IAAI,CAACE,SAAS;cACpCC,UAAU,EAAEN,UAAU,CAACG,IAAI,CAACG,UAAU;cACtCC,GAAG,EAAEP,UAAU,CAACG,IAAI,CAACI,GAAG;cACxBC,QAAQ,EAAER,UAAU,CAACG,IAAI,CAACK,QAAQ;cAClCC,UAAU,EAAET,UAAU,CAACG,IAAI,CAACM,UAAU;cACtCC,QAAQ,EAAEV,UAAU,CAACG,IAAI,CAACO;YAC5B,CAAC;YACDxB,QAAQ,EAAE,IAAI;YACdK,SAAS,EAAE;UACb,CAAC,CAAC;;UAEF;UACA,MAAMR,GAAG,CAAC,CAAC,CAAC4B,iBAAiB,CAAC,CAAC;UAC/B,OAAO,IAAI;QACb;MACF;MAEA7B,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI,uBAAuB;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MACzE,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB,OAAO;QAAErB,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAEDsB,UAAU,EAAE,MAAOC,QAAgB,IAAK;IACtChC,GAAG,CAAC;MAAES,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAM,CAACuB,YAAY,EAAEf,UAAU,CAAC,GAAG,MAAMgB,OAAO,CAACC,GAAG,CAAC,CACnDpB,WAAW,CAACqB,SAAS,CAACJ,QAAQ,CAAC,EAC/BjB,WAAW,CAACI,aAAa,CAACa,QAAQ,CAAC,CACpC,CAAC;MAEF,IAAIC,YAAY,CAAChB,OAAO,IAAIgB,YAAY,CAAC/B,MAAM,IAAIgB,UAAU,CAACD,OAAO,IAAIC,UAAU,CAACG,IAAI,EAAE;QACxFrB,GAAG,CAAC;UACFE,MAAM,EAAE+B,YAAY,CAAC/B,MAAM;UAC3BC,UAAU,EAAE;YACVmB,SAAS,EAAEJ,UAAU,CAACG,IAAI,CAACC,SAAS;YACpCC,SAAS,EAAEL,UAAU,CAACG,IAAI,CAACE,SAAS;YACpCC,UAAU,EAAEN,UAAU,CAACG,IAAI,CAACG,UAAU;YACtCC,GAAG,EAAEP,UAAU,CAACG,IAAI,CAACI,GAAG;YACxBC,QAAQ,EAAER,UAAU,CAACG,IAAI,CAACK,QAAQ;YAClCC,UAAU,EAAET,UAAU,CAACG,IAAI,CAACM,UAAU;YACtCC,QAAQ,EAAEV,UAAU,CAACG,IAAI,CAACO;UAC5B,CAAC;UACDxB,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;QACb,CAAC,CAAC;;QAEF;QACA,MAAMR,GAAG,CAAC,CAAC,CAAC4B,iBAAiB,CAAC,CAAC;QAC/B,OAAO,IAAI;MACb;MAEA7B,GAAG,CAAC;QAAEU,KAAK,EAAE,0BAA0B;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MAC5D,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB,OAAO;QAAErB,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAED4B,gBAAgB,EAAE,MAAOhB,IAAyB,IAAK;IACrD,MAAMiB,KAAK,GAAGrC,GAAG,CAAC,CAAC;IACnB,IAAI,CAACqC,KAAK,CAACpC,MAAM,EAAE,OAAO,KAAK;IAE/B,IAAI;MACF,MAAMY,MAAM,GAAG,MAAMC,WAAW,CAACsB,gBAAgB,CAACC,KAAK,CAACpC,MAAM,CAACkB,EAAE,EAAEC,IAAI,CAAC;MAExE,IAAIP,MAAM,CAACG,OAAO,EAAE;QAClBjB,GAAG,CAAEsC,KAAK,KAAM;UACdnC,UAAU,EAAE;YAAE,GAAGmC,KAAK,CAACnC,UAAU;YAAE,GAAGkB;UAAK;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,IAAI;MACb;MAEArB,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI;MAAyB,CAAC,CAAC;MACxD,OAAO,KAAK;IACd,CAAC,CAAC,OAAOA,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB;MAAQ,CAAC,CAAC;MAC7B,OAAO,KAAK;IACd;EACF,CAAC;EAEDS,UAAU,EAAE,MAAOC,KAAuB,IAAK;IAC7C,MAAMF,KAAK,GAAGrC,GAAG,CAAC,CAAC;IACnB,MAAM;MAAEC,MAAM;MAAEC;IAAW,CAAC,GAAGmC,KAAK;IAEpC,IAAI,CAACpC,MAAM,EAAE,OAAO,KAAK;IAEzB,MAAMuC,YAAY,GAAGtC,UAAU,CAACqC,KAAK,CAAC;IACtC,MAAME,WAAW,GAAG/C,oBAAoB,CAAC8C,YAAY,CAAC;;IAEtD;IACA,IAAIvC,MAAM,CAACyC,IAAI,GAAGD,WAAW,EAAE;MAC7B1C,GAAG,CAAC;QAAEU,KAAK,EAAE;MAAsC,CAAC,CAAC;MACrD,OAAO,KAAK;IACd;;IAEA;IACA,IAAI+B,YAAY,IAAIhD,WAAW,CAACmD,aAAa,EAAE;MAC7C5C,GAAG,CAAC;QAAEU,KAAK,EAAE;MAA+B,CAAC,CAAC;MAC9C,OAAO,KAAK;IACd;IAEA,IAAI;MACF;MACA,MAAMmC,QAAQ,GAAGJ,YAAY,GAAG,CAAC;MACjC,MAAMK,OAAO,GAAG5C,MAAM,CAACyC,IAAI,GAAGD,WAAW;MACzC,MAAMK,QAAQ,GAAGnD,iBAAiB,CAAC6C,YAAY,CAAC;MAChD,MAAMO,KAAK,GAAG9C,MAAM,CAAC+C,EAAE,GAAGF,QAAQ;MAClC,MAAMG,cAAc,GAAGxD,cAAc,CAACsD,KAAK,CAAC;;MAE5C;MACA,MAAMG,UAAU,GAAG;QAAE,CAACX,KAAK,GAAGK;MAAS,CAAC;MACxC,MAAM3B,UAAU,GAAG,MAAMH,WAAW,CAACsB,gBAAgB,CAACnC,MAAM,CAACkB,EAAE,EAAE+B,UAAU,CAAC;;MAE5E;MACA,MAAMC,YAAY,GAAG;QACnBT,IAAI,EAAEG,OAAO;QACbG,EAAE,EAAED,KAAK;QACTK,KAAK,EAAEH;MACT,CAAC;MACD,MAAMjB,YAAY,GAAG,MAAMlB,WAAW,CAACuC,YAAY,CAACpD,MAAM,CAACkB,EAAE,EAAEgC,YAAY,CAAC;MAE5E,IAAIlC,UAAU,CAACD,OAAO,IAAIgB,YAAY,CAAChB,OAAO,EAAE;QAC9C;QACAjB,GAAG,CAAEsC,KAAK,KAAM;UACdpC,MAAM,EAAE;YACN,GAAGoC,KAAK,CAACpC,MAAO;YAChByC,IAAI,EAAEG,OAAO;YACbG,EAAE,EAAED,KAAK;YACTK,KAAK,EAAEH;UACT,CAAC;UACD/C,UAAU,EAAE;YACV,GAAGmC,KAAK,CAACnC,UAAU;YACnB,CAACqC,KAAK,GAAGK;UACX,CAAC;UACDnC,KAAK,EAAE;QACT,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMT,GAAG,CAAC,CAAC,CAACsD,eAAe,CAAC;UAC1BC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,oBAAoB;UAC3B3B,OAAO,EAAE,GAAGU,KAAK,8BAA8BK,QAAQ,MAAME,QAAQ;QACvE,CAAC,CAAC;;QAEF;QACA,IAAIG,cAAc,GAAGhD,MAAM,CAACmD,KAAK,EAAE;UACjC,MAAMpD,GAAG,CAAC,CAAC,CAACsD,eAAe,CAAC;YAC1BC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,WAAW;YAClB3B,OAAO,EAAE,kCAAkCoB,cAAc;UAC3D,CAAC,CAAC;QACJ;QAEA,OAAO,IAAI;MACb;MAEAlD,GAAG,CAAC;QAAEU,KAAK,EAAE;MAA2B,CAAC,CAAC;MAC1C,OAAO,KAAK;IACd,CAAC,CAAC,OAAOA,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB;MAAQ,CAAC,CAAC;MAC7B,OAAO,KAAK;IACd;EACF,CAAC;EAED4B,KAAK,EAAE,MAAOC,MAAc,IAAK;IAC/B,MAAMrB,KAAK,GAAGrC,GAAG,CAAC,CAAC;IACnB,IAAI,CAACqC,KAAK,CAACpC,MAAM,EAAE;IAEnB,IAAI;MACF,MAAM0D,UAAU,GAAGtB,KAAK,CAACpC,MAAM,CAAC+C,EAAE,GAAGU,MAAM;MAC3C,MAAMd,QAAQ,GAAGnD,cAAc,CAACkE,UAAU,CAAC;MAC3C,MAAMC,SAAS,GAAGhB,QAAQ,GAAGP,KAAK,CAACpC,MAAM,CAACmD,KAAK;MAE/C,MAAMvC,MAAM,GAAG,MAAMC,WAAW,CAACuC,YAAY,CAAChB,KAAK,CAACpC,MAAM,CAACkB,EAAE,EAAE;QAC7D6B,EAAE,EAAEW,UAAU;QACdP,KAAK,EAAER;MACT,CAAC,CAAC;MAEF,IAAI/B,MAAM,CAACG,OAAO,EAAE;QAClBjB,GAAG,CAAEsC,KAAK,KAAM;UACdpC,MAAM,EAAEoC,KAAK,CAACpC,MAAM,GAAG;YACrB,GAAGoC,KAAK,CAACpC,MAAM;YACf+C,EAAE,EAAEW,UAAU;YACdP,KAAK,EAAER;UACT,CAAC,GAAG;QACN,CAAC,CAAC,CAAC;;QAEH;QACA,IAAIgB,SAAS,EAAE;UACb,MAAM5D,GAAG,CAAC,CAAC,CAACsD,eAAe,CAAC;YAC1BC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,WAAW;YAClB3B,OAAO,EAAE,kCAAkCe,QAAQ;UACrD,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAOnC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB;MAAQ,CAAC,CAAC;IAC/B;EACF,CAAC;EAEDgC,OAAO,EAAE,MAAOH,MAAc,IAAK;IACjC,MAAMrB,KAAK,GAAGrC,GAAG,CAAC,CAAC;IACnB,IAAI,CAACqC,KAAK,CAACpC,MAAM,EAAE;IAEnB,IAAI;MACF,MAAM4C,OAAO,GAAGR,KAAK,CAACpC,MAAM,CAACyC,IAAI,GAAGgB,MAAM;MAC1C,MAAM7C,MAAM,GAAG,MAAMC,WAAW,CAACuC,YAAY,CAAChB,KAAK,CAACpC,MAAM,CAACkB,EAAE,EAAE;QAAEuB,IAAI,EAAEG;MAAQ,CAAC,CAAC;MAEjF,IAAIhC,MAAM,CAACG,OAAO,EAAE;QAClBjB,GAAG,CAAEsC,KAAK,KAAM;UACdpC,MAAM,EAAEoC,KAAK,CAACpC,MAAM,GAAG;YACrB,GAAGoC,KAAK,CAACpC,MAAM;YACfyC,IAAI,EAAEG;UACR,CAAC,GAAG;QACN,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOpC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB;MAAQ,CAAC,CAAC;IAC/B;EACF,CAAC;EAEDiC,SAAS,EAAE,MAAOJ,MAAc,IAAK;IACnC,MAAMrB,KAAK,GAAGrC,GAAG,CAAC,CAAC;IACnB,IAAI,CAACqC,KAAK,CAACpC,MAAM,IAAIoC,KAAK,CAACpC,MAAM,CAACyC,IAAI,GAAGgB,MAAM,EAAE;MAC/C,OAAO,KAAK;IACd;IAEA,IAAI;MACF,MAAMb,OAAO,GAAGR,KAAK,CAACpC,MAAM,CAACyC,IAAI,GAAGgB,MAAM;MAC1C,MAAM7C,MAAM,GAAG,MAAMC,WAAW,CAACuC,YAAY,CAAChB,KAAK,CAACpC,MAAM,CAACkB,EAAE,EAAE;QAAEuB,IAAI,EAAEG;MAAQ,CAAC,CAAC;MAEjF,IAAIhC,MAAM,CAACG,OAAO,EAAE;QAClBjB,GAAG,CAAEsC,KAAK,KAAM;UACdpC,MAAM,EAAEoC,KAAK,CAACpC,MAAM,GAAG;YACrB,GAAGoC,KAAK,CAACpC,MAAM;YACfyC,IAAI,EAAEG;UACR,CAAC,GAAG;QACN,CAAC,CAAC,CAAC;QACH,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOpC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB;MAAQ,CAAC,CAAC;MAC7B,OAAO,KAAK;IACd;EACF,CAAC;EAEDkC,gBAAgB,EAAGC,MAAkC,IAAK;IACxDjE,GAAG,CAAC;MAAEQ,aAAa,EAAEyD;IAAO,CAAC,CAAC;EAChC,CAAC;EAEDpC,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,MAAMS,KAAK,GAAGrC,GAAG,CAAC,CAAC;IACnB,IAAI,CAACqC,KAAK,CAACpC,MAAM,EAAE;IAEnB,IAAI;MACF,MAAMY,MAAM,GAAG,MAAMC,WAAW,CAACmD,gBAAgB,CAAC5B,KAAK,CAACpC,MAAM,CAACkB,EAAE,CAAC;MAElE,IAAIN,MAAM,CAACG,OAAO,IAAIH,MAAM,CAACT,aAAa,EAAE;QAC1CL,GAAG,CAAC;UAAEK,aAAa,EAAES,MAAM,CAACT;QAAc,CAAC,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOK,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB;MAAQ,CAAC,CAAC;IAC/B;EACF,CAAC;EAEDyB,eAAe,EAAE,MAAOY,YAAgF,IAAK;IAC3G,MAAM7B,KAAK,GAAGrC,GAAG,CAAC,CAAC;IACnB,IAAI,CAACqC,KAAK,CAACpC,MAAM,EAAE;IAEnB,IAAI;MACF,MAAMY,MAAM,GAAG,MAAMC,WAAW,CAACqD,kBAAkB,CAAC9B,KAAK,CAACpC,MAAM,CAACkB,EAAE,EAAE+C,YAAY,CAAC;MAElF,IAAIrD,MAAM,CAACG,OAAO,EAAE;QAClB;QACA,MAAMhB,GAAG,CAAC,CAAC,CAAC4B,iBAAiB,CAAC,CAAC;MACjC;IACF,CAAC,CAAC,OAAOnB,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB;MAAQ,CAAC,CAAC;IAC/B;EACF,CAAC;EAEDuC,oBAAoB,EAAE,MAAOjD,EAAU,IAAK;IAC1C,IAAI;MACF,MAAMN,MAAM,GAAG,MAAMC,WAAW,CAACsD,oBAAoB,CAACjD,EAAE,CAAC;MAEzD,IAAIN,MAAM,CAACG,OAAO,EAAE;QAClBjB,GAAG,CAAEsC,KAAK,KAAM;UACdjC,aAAa,EAAEiC,KAAK,CAACjC,aAAa,CAACiE,GAAG,CAACC,KAAK,IAC1CA,KAAK,CAACnD,EAAE,KAAKA,EAAE,GAAG;YAAE,GAAGmD,KAAK;YAAEC,IAAI,EAAE;UAAK,CAAC,GAAGD,KAC/C;QACF,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAO7D,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB;MAAQ,CAAC,CAAC;IAC/B;EACF,CAAC;EAED2C,kBAAkB,EAAEA,CAAA,KAAM;IACxBzE,GAAG,CAAC;MAAEK,aAAa,EAAE;IAAG,CAAC,CAAC;EAC5B,CAAC;EAEDqE,QAAQ,EAAGhE,KAAoB,IAAK;IAClCV,GAAG,CAAC;MAAEU;IAAM,CAAC,CAAC;IACd,IAAIA,KAAK,EAAE;MACT;MACAiE,UAAU,CAAC,MAAM;QACf3E,GAAG,CAAC;UAAEU,KAAK,EAAE;QAAK,CAAC,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAEDkE,UAAU,EAAGC,OAAgB,IAAK;IAChC7E,GAAG,CAAC;MAAES,SAAS,EAAEoE;IAAQ,CAAC,CAAC;EAC7B,CAAC;EAEDC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,MAAMxC,KAAK,GAAGrC,GAAG,CAAC,CAAC;IACnB,IAAI,CAACqC,KAAK,CAACpC,MAAM,EAAE;IAEnB,IAAI;MACFF,GAAG,CAAC;QAAES,SAAS,EAAE;MAAK,CAAC,CAAC;;MAExB;MACA,MAAMR,GAAG,CAAC,CAAC,CAAC8B,UAAU,CAACO,KAAK,CAACpC,MAAM,CAACkB,EAAE,CAAC;MAEvCpB,GAAG,CAAC;QAAES,SAAS,EAAE;MAAM,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACoB,OAAO;QAAErB,SAAS,EAAE;MAAM,CAAC,CAAC;IACjD;EACF,CAAC;EAEDsE,KAAK,EAAEA,CAAA,KAAM;IACX/E,GAAG,CAAC;MACFE,MAAM,EAAE,IAAI;MACZC,UAAU,EAAEN,iBAAiB;MAC7BO,QAAQ,EAAE,KAAK;MACfC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,EAAE;MACpBC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,EACF;EACEsE,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAG3C,KAAK,KAAM;IACtBpC,MAAM,EAAEoC,KAAK,CAACpC,MAAM;IACpBM,aAAa,EAAE8B,KAAK,CAAC9B;EACvB,CAAC,CAAC;EACF;EACA0E,kBAAkB,EAAEA,CAAA,KAAO5C,KAAK,IAAK;IAAA,IAAA6C,aAAA;IACnC,IAAI7C,KAAK,aAALA,KAAK,gBAAA6C,aAAA,GAAL7C,KAAK,CAAEpC,MAAM,cAAAiF,aAAA,eAAbA,aAAA,CAAe/D,EAAE,EAAE;MACrB;MACAuD,UAAU,CAAC,MAAM;QACfrC,KAAK,CAACwC,cAAc,CAAC,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC;IACV;EACF;AACF,CACF,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}