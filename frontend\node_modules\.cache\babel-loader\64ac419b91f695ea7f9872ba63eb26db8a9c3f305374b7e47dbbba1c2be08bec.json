{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\game\\\\MobilePhone.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useHackGameStore } from '../../stores/hackGameStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MobilePhone = ({\n  children\n}) => {\n  _s();\n  const {\n    player,\n    notifications,\n    setCurrentScreen\n  } = useHackGameStore();\n  const unreadCount = notifications.filter(n => !n.read).length;\n  const handleHomeClick = () => {\n    // Feedback tátil visual\n    const button = document.querySelector('.home-indicator');\n    if (button) {\n      button.style.transform = 'scaleY(0.8)';\n      setTimeout(() => {\n        button.style.transform = 'scaleY(1)';\n      }, 100);\n    }\n    setCurrentScreen('home');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-[420px] h-[880px] bg-black rounded-[3rem] p-3 shadow-2xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-full bg-gray-900 rounded-[2.5rem] overflow-hidden relative flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 left-1/2 transform -translate-x-1/2 w-36 h-7 bg-black rounded-b-2xl z-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center px-6 pt-4 pb-2 text-white text-sm relative z-40 flex-shrink-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: \"9:41\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1 h-3 bg-white rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1 h-3 bg-white rounded-full opacity-60\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1 h-3 bg-white rounded-full opacity-30\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 48,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-7 h-3 border border-white rounded-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-5 h-1 bg-green-500 rounded-sm m-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-hidden\",\n            children: children\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 pb-4 pt-3 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleHomeClick,\n              className: \"w-36 h-1.5 bg-white rounded-full transition-all duration-200 hover:bg-gray-200 active:bg-gray-300 active:scale-95\",\n              style: {\n                background: 'linear-gradient(135deg, #ffffff 0%, #e5e5e5 100%)',\n                boxShadow: '0 1px 2px rgba(0,0,0,0.2), inset 0 0.5px 0 rgba(255,255,255,0.9)',\n                opacity: 0.9\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.opacity = '1';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.opacity = '0.9';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-0 top-24 w-1 h-14 bg-gray-700 rounded-l-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-0 top-44 w-1 h-10 bg-gray-700 rounded-l-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-0 top-60 w-1 h-10 bg-gray-700 rounded-l-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-0 top-36 w-1 h-18 bg-gray-700 rounded-r-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(MobilePhone, \"jmzuN9oEsW7tEWft5a2vGfKrtI4=\", false, function () {\n  return [useHackGameStore];\n});\n_c = MobilePhone;\nexport default MobilePhone;\nvar _c;\n$RefreshReg$(_c, \"MobilePhone\");", "map": {"version": 3, "names": ["React", "useHackGameStore", "jsxDEV", "_jsxDEV", "MobilePhone", "children", "_s", "player", "notifications", "setCurrentScreen", "unreadCount", "filter", "n", "read", "length", "handleHomeClick", "button", "document", "querySelector", "style", "transform", "setTimeout", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "background", "boxShadow", "opacity", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/game/MobilePhone.tsx"], "sourcesContent": ["import React from 'react';\nimport { useHackGameStore } from '../../stores/hackGameStore';\n\ninterface MobilePhoneProps {\n  children: React.ReactNode;\n}\n\nconst MobilePhone: React.FC<MobilePhoneProps> = ({ children }) => {\n  const { player, notifications, setCurrentScreen } = useHackGameStore();\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  const handleHomeClick = () => {\n    // Feedback tátil visual\n    const button = document.querySelector('.home-indicator') as HTMLElement;\n    if (button) {\n      button.style.transform = 'scaleY(0.8)';\n      setTimeout(() => {\n        button.style.transform = 'scaleY(1)';\n      }, 100);\n    }\n\n    setCurrentScreen('home');\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black p-4\">\n      {/* Moldura do celular */}\n      <div className=\"relative\">\n        {/* Corpo do celular */}\n        <div className=\"w-[420px] h-[880px] bg-black rounded-[3rem] p-3 shadow-2xl\">\n          {/* Tela do celular */}\n          <div className=\"w-full h-full bg-gray-900 rounded-[2.5rem] overflow-hidden relative flex flex-col\">\n            {/* Notch */}\n            <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 w-36 h-7 bg-black rounded-b-2xl z-50\"></div>\n\n            {/* Status bar */}\n            <div className=\"flex justify-between items-center px-6 pt-4 pb-2 text-white text-sm relative z-40 flex-shrink-0\">\n              <div className=\"flex items-center space-x-1\">\n                <span className=\"text-sm font-medium\">9:41</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                {unreadCount > 0 && (\n                  <div className=\"w-2 h-2 bg-red-500 rounded-full animate-pulse\"></div>\n                )}\n                <div className=\"flex space-x-1\">\n                  <div className=\"w-1 h-3 bg-white rounded-full\"></div>\n                  <div className=\"w-1 h-3 bg-white rounded-full opacity-60\"></div>\n                  <div className=\"w-1 h-3 bg-white rounded-full opacity-30\"></div>\n                </div>\n                <div className=\"w-7 h-3 border border-white rounded-sm\">\n                  <div className=\"w-5 h-1 bg-green-500 rounded-sm m-0.5\"></div>\n                </div>\n              </div>\n            </div>\n\n            {/* Conteúdo da tela */}\n            <div className=\"flex-1 overflow-hidden\">\n              {children}\n            </div>\n\n            {/* Botão Home estilo iPhone */}\n            <div className=\"flex-shrink-0 pb-4 pt-3 flex justify-center\">\n              <button\n                onClick={handleHomeClick}\n                className=\"w-36 h-1.5 bg-white rounded-full transition-all duration-200 hover:bg-gray-200 active:bg-gray-300 active:scale-95\"\n                style={{\n                  background: 'linear-gradient(135deg, #ffffff 0%, #e5e5e5 100%)',\n                  boxShadow: '0 1px 2px rgba(0,0,0,0.2), inset 0 0.5px 0 rgba(255,255,255,0.9)',\n                  opacity: 0.9\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.opacity = '1';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.opacity = '0.9';\n                }}\n              >\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Botões laterais */}\n        <div className=\"absolute left-0 top-24 w-1 h-14 bg-gray-700 rounded-l-lg\"></div>\n        <div className=\"absolute left-0 top-44 w-1 h-10 bg-gray-700 rounded-l-lg\"></div>\n        <div className=\"absolute left-0 top-60 w-1 h-10 bg-gray-700 rounded-l-lg\"></div>\n        <div className=\"absolute right-0 top-36 w-1 h-18 bg-gray-700 rounded-r-lg\"></div>\n      </div>\n    </div>\n  );\n};\n\nexport default MobilePhone;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM9D,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM;IAAEC,MAAM;IAAEC,aAAa;IAAEC;EAAiB,CAAC,GAAGR,gBAAgB,CAAC,CAAC;EACtE,MAAMS,WAAW,GAAGF,aAAa,CAACG,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,CAAC,CAACC,MAAM;EAE7D,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,iBAAiB,CAAgB;IACvE,IAAIF,MAAM,EAAE;MACVA,MAAM,CAACG,KAAK,CAACC,SAAS,GAAG,aAAa;MACtCC,UAAU,CAAC,MAAM;QACfL,MAAM,CAACG,KAAK,CAACC,SAAS,GAAG,WAAW;MACtC,CAAC,EAAE,GAAG,CAAC;IACT;IAEAX,gBAAgB,CAAC,MAAM,CAAC;EAC1B,CAAC;EAED,oBACEN,OAAA;IAAKmB,SAAS,EAAC,yGAAyG;IAAAjB,QAAA,eAEtHF,OAAA;MAAKmB,SAAS,EAAC,UAAU;MAAAjB,QAAA,gBAEvBF,OAAA;QAAKmB,SAAS,EAAC,4DAA4D;QAAAjB,QAAA,eAEzEF,OAAA;UAAKmB,SAAS,EAAC,mFAAmF;UAAAjB,QAAA,gBAEhGF,OAAA;YAAKmB,SAAS,EAAC;UAAyF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAG/GvB,OAAA;YAAKmB,SAAS,EAAC,iGAAiG;YAAAjB,QAAA,gBAC9GF,OAAA;cAAKmB,SAAS,EAAC,6BAA6B;cAAAjB,QAAA,eAC1CF,OAAA;gBAAMmB,SAAS,EAAC,qBAAqB;gBAAAjB,QAAA,EAAC;cAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNvB,OAAA;cAAKmB,SAAS,EAAC,6BAA6B;cAAAjB,QAAA,GACzCK,WAAW,GAAG,CAAC,iBACdP,OAAA;gBAAKmB,SAAS,EAAC;cAA+C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACrE,eACDvB,OAAA;gBAAKmB,SAAS,EAAC,gBAAgB;gBAAAjB,QAAA,gBAC7BF,OAAA;kBAAKmB,SAAS,EAAC;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDvB,OAAA;kBAAKmB,SAAS,EAAC;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChEvB,OAAA;kBAAKmB,SAAS,EAAC;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNvB,OAAA;gBAAKmB,SAAS,EAAC,wCAAwC;gBAAAjB,QAAA,eACrDF,OAAA;kBAAKmB,SAAS,EAAC;gBAAuC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvB,OAAA;YAAKmB,SAAS,EAAC,wBAAwB;YAAAjB,QAAA,EACpCA;UAAQ;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNvB,OAAA;YAAKmB,SAAS,EAAC,6CAA6C;YAAAjB,QAAA,eAC1DF,OAAA;cACEwB,OAAO,EAAEZ,eAAgB;cACzBO,SAAS,EAAC,mHAAmH;cAC7HH,KAAK,EAAE;gBACLS,UAAU,EAAE,mDAAmD;gBAC/DC,SAAS,EAAE,kEAAkE;gBAC7EC,OAAO,EAAE;cACX,CAAE;cACFC,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACd,KAAK,CAACW,OAAO,GAAG,GAAG;cACrC,CAAE;cACFI,YAAY,EAAGF,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACd,KAAK,CAACW,OAAO,GAAG,KAAK;cACvC;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKmB,SAAS,EAAC;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChFvB,OAAA;QAAKmB,SAAS,EAAC;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChFvB,OAAA;QAAKmB,SAAS,EAAC;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChFvB,OAAA;QAAKmB,SAAS,EAAC;MAA2D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9E;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpB,EAAA,CAnFIF,WAAuC;EAAA,QACSH,gBAAgB;AAAA;AAAAkC,EAAA,GADhE/B,WAAuC;AAqF7C,eAAeA,WAAW;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}