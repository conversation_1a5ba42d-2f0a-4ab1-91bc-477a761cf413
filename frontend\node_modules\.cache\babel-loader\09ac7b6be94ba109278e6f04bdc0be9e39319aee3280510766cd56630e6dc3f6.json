{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TestPage=()=>{console.log('TestPage - Renderizando página de teste...');return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-bg-primary text-text-primary p-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-4xl mx-auto\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl font-bold text-center mb-8\",children:\"\\uD83C\\uDFAE SHACK Web Game - Teste Simples\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-8\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold mb-4\",children:\"\\u2705 React Funcionando!\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-muted\",children:\"Esta \\xE9 uma p\\xE1gina de teste simples sem stores ou autentica\\xE7\\xE3o.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card bg-blue-900 border-blue-500\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-blue-100 mb-4\",children:\"\\uD83D\\uDD27 Status do Sistema\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-blue-200 text-sm space-y-2\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u2705 React: Funcionando\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u2705 Tailwind CSS: Funcionando\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u2705 Roteamento: Funcionando\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u274C Stores: Desabilitados\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u274C Autentica\\xE7\\xE3o: Desabilitada\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u274C Flask: N\\xE3o conectado\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card bg-green-900 border-green-500\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-green-100 mb-4\",children:\"\\uD83C\\uDFAF Pr\\xF3ximos Passos\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-green-200 text-sm space-y-2\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"1. Confirmar que n\\xE3o h\\xE1 loops\"}),/*#__PURE__*/_jsx(\"p\",{children:\"2. Corrigir o Flask\"}),/*#__PURE__*/_jsx(\"p\",{children:\"3. Reativar stores gradualmente\"}),/*#__PURE__*/_jsx(\"p\",{children:\"4. Testar autentica\\xE7\\xE3o\"}),/*#__PURE__*/_jsx(\"p\",{children:\"5. Integra\\xE7\\xE3o completa\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-8 text-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card bg-bg-tertiary\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4\",children:\"\\uD83E\\uDDEA Teste de Intera\\xE7\\xE3o\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{alert('Botão funcionando! React está OK.');console.log('Teste de clique funcionando');},className:\"btn-primary mr-4\",children:\"Testar Clique\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{console.log('Console log funcionando');console.log('Timestamp:',new Date().toISOString());},className:\"btn-secondary\",children:\"Testar Console\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 text-center text-text-muted text-sm\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Se esta p\\xE1gina carrega sem loops, o React est\\xE1 funcionando corretamente.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"O problema estava nos stores de autentica\\xE7\\xE3o.\"})]})]})})});};export default TestPage;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "TestPage", "console", "log", "className", "children", "onClick", "alert", "Date", "toISOString"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/TestPage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst TestPage: React.FC = () => {\n  console.log('TestPage - Renderizando página de teste...');\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"card\">\n          <h1 className=\"text-4xl font-bold text-center mb-8\">\n            🎮 SHACK Web Game - Teste Simples\n          </h1>\n          \n          <div className=\"text-center mb-8\">\n            <h2 className=\"text-2xl font-semibold mb-4\">\n              ✅ React Funcionando!\n            </h2>\n            <p className=\"text-text-muted\">\n              Esta é uma página de teste simples sem stores ou autenticação.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"card bg-blue-900 border-blue-500\">\n              <h3 className=\"text-lg font-semibold text-blue-100 mb-4\">\n                🔧 Status do Sistema\n              </h3>\n              <div className=\"text-blue-200 text-sm space-y-2\">\n                <p>✅ React: Funcionando</p>\n                <p>✅ Tailwind CSS: Funcionando</p>\n                <p>✅ Roteamento: Funcionando</p>\n                <p>❌ Stores: Desabilitados</p>\n                <p>❌ Autenticação: Desabilitada</p>\n                <p>❌ Flask: Não conectado</p>\n              </div>\n            </div>\n\n            <div className=\"card bg-green-900 border-green-500\">\n              <h3 className=\"text-lg font-semibold text-green-100 mb-4\">\n                🎯 Próximos Passos\n              </h3>\n              <div className=\"text-green-200 text-sm space-y-2\">\n                <p>1. Confirmar que não há loops</p>\n                <p>2. Corrigir o Flask</p>\n                <p>3. Reativar stores gradualmente</p>\n                <p>4. Testar autenticação</p>\n                <p>5. Integração completa</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-8 text-center\">\n            <div className=\"card bg-bg-tertiary\">\n              <h3 className=\"text-lg font-semibold mb-4\">\n                🧪 Teste de Interação\n              </h3>\n              <button \n                onClick={() => {\n                  alert('Botão funcionando! React está OK.');\n                  console.log('Teste de clique funcionando');\n                }}\n                className=\"btn-primary mr-4\"\n              >\n                Testar Clique\n              </button>\n              <button \n                onClick={() => {\n                  console.log('Console log funcionando');\n                  console.log('Timestamp:', new Date().toISOString());\n                }}\n                className=\"btn-secondary\"\n              >\n                Testar Console\n              </button>\n            </div>\n          </div>\n\n          <div className=\"mt-6 text-center text-text-muted text-sm\">\n            <p>Se esta página carrega sem loops, o React está funcionando corretamente.</p>\n            <p>O problema estava nos stores de autenticação.</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TestPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,CAC/BC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC,CAEzD,mBACEL,IAAA,QAAKM,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAC/DP,IAAA,QAAKM,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCL,KAAA,QAAKI,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBP,IAAA,OAAIM,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,6CAEpD,CAAI,CAAC,cAELL,KAAA,QAAKI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BP,IAAA,OAAIM,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,2BAE5C,CAAI,CAAC,cACLP,IAAA,MAAGM,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,4EAE/B,CAAG,CAAC,EACD,CAAC,cAENL,KAAA,QAAKI,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDL,KAAA,QAAKI,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CP,IAAA,OAAIM,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gCAEzD,CAAI,CAAC,cACLL,KAAA,QAAKI,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CP,IAAA,MAAAO,QAAA,CAAG,2BAAoB,CAAG,CAAC,cAC3BP,IAAA,MAAAO,QAAA,CAAG,kCAA2B,CAAG,CAAC,cAClCP,IAAA,MAAAO,QAAA,CAAG,gCAAyB,CAAG,CAAC,cAChCP,IAAA,MAAAO,QAAA,CAAG,8BAAuB,CAAG,CAAC,cAC9BP,IAAA,MAAAO,QAAA,CAAG,yCAA4B,CAAG,CAAC,cACnCP,IAAA,MAAAO,QAAA,CAAG,gCAAsB,CAAG,CAAC,EAC1B,CAAC,EACH,CAAC,cAENL,KAAA,QAAKI,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDP,IAAA,OAAIM,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,iCAE1D,CAAI,CAAC,cACLL,KAAA,QAAKI,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CP,IAAA,MAAAO,QAAA,CAAG,qCAA6B,CAAG,CAAC,cACpCP,IAAA,MAAAO,QAAA,CAAG,qBAAmB,CAAG,CAAC,cAC1BP,IAAA,MAAAO,QAAA,CAAG,iCAA+B,CAAG,CAAC,cACtCP,IAAA,MAAAO,QAAA,CAAG,8BAAsB,CAAG,CAAC,cAC7BP,IAAA,MAAAO,QAAA,CAAG,8BAAsB,CAAG,CAAC,EAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAENP,IAAA,QAAKM,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BL,KAAA,QAAKI,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCP,IAAA,OAAIM,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,uCAE3C,CAAI,CAAC,cACLP,IAAA,WACEQ,OAAO,CAAEA,CAAA,GAAM,CACbC,KAAK,CAAC,mCAAmC,CAAC,CAC1CL,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAC5C,CAAE,CACFC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC7B,eAED,CAAQ,CAAC,cACTP,IAAA,WACEQ,OAAO,CAAEA,CAAA,GAAM,CACbJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC,CACtCD,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE,GAAI,CAAAK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CACrD,CAAE,CACFL,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC1B,gBAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,cAENL,KAAA,QAAKI,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvDP,IAAA,MAAAO,QAAA,CAAG,gFAAwE,CAAG,CAAC,cAC/EP,IAAA,MAAAO,QAAA,CAAG,qDAA6C,CAAG,CAAC,EACjD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}