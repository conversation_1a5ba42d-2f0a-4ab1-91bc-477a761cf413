#!/usr/bin/env python3
"""
Servid<PERSON> simples para testar se o problema é com o Flask ou com o app.py
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import jwt
import datetime
import os
from datetime import timezone, timedelta

app = Flask(__name__)
CORS(app)

# Chave secreta
JWT_SECRET = os.environ.get('SECRET_KEY', 'shack-game-secret-key-2024-csrf-protection-enabled')

@app.route('/')
def home():
    return jsonify({"status": "OK", "message": "Servidor de teste funcionando!"})

@app.route('/api/auth/login', methods=['POST'])
def test_login():
    print("[TEST] Recebendo requisição de login...")
    
    data = request.get_json()
    email = data.get('email')
    password = data.get('password')
    
    print(f"[TEST] Email: {email}, Password: {password}")
    
    # Simular login bem-sucedido
    user_data = {
        'uid': 'test-user-123',
        'nick': 'TestUser',
        'email': email
    }
    
    # Gerar JWT
    payload = {
        'uid': user_data['uid'],
        'nick': user_data['nick'],
        'exp': datetime.datetime.now(timezone.utc) + timedelta(hours=24)
    }
    
    token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')
    print(f"[TEST] Token gerado: {token}")
    
    return jsonify({
        "sucesso": True,
        "user": user_data,
        "token": token
    })

@app.route('/api/jogador', methods=['GET'])
def test_player():
    print("[TEST] Recebendo requisição de dados do jogador...")
    
    # Verificar token
    auth_header = request.headers.get('Authorization')
    print(f"[TEST] Authorization header: {auth_header}")
    
    if not auth_header or not auth_header.startswith('Bearer '):
        print("[TEST] Token não fornecido")
        return jsonify({'sucesso': False, 'mensagem': 'Token não fornecido'}), 401
    
    token = auth_header.split(' ')[1]
    print(f"[TEST] Token extraído: {token[:50]}...")
    
    try:
        decoded = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        print(f"[TEST] Token válido: {decoded}")
        
        # Retornar dados mock do jogador
        jogador = {
            'uid': decoded['uid'],
            'nick': decoded['nick'],
            'email': '<EMAIL>',
            'ip': '*************',
            'nivel': 5,
            'xp': 250,
            'dinheiro': 150,
            'antivirus': 3,
            'bankguard': 2,
            'bruteforce': 4,
            'cpu': 2,
            'firewall': 3,
            'malware_kit': 2,
            'proxyvpn': 1
        }
        
        return jsonify({
            'sucesso': True,
            'jogador': jogador
        })
        
    except jwt.ExpiredSignatureError:
        print("[TEST] Token expirado")
        return jsonify({'sucesso': False, 'mensagem': 'Token expirado'}), 401
    except jwt.InvalidTokenError as e:
        print(f"[TEST] Token inválido: {e}")
        return jsonify({'sucesso': False, 'mensagem': 'Token inválido'}), 401

if __name__ == '__main__':
    print("=== SERVIDOR DE TESTE ===")
    print("Iniciando servidor Flask de teste...")
    print("URL: http://localhost:5000")
    print("Rotas disponíveis:")
    print("  GET  / - Status")
    print("  POST /api/auth/login - Login de teste")
    print("  GET  /api/jogador - Dados do jogador")
    print("========================")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
