from flask import Blueprint, request, jsonify, session
from functools import wraps
import jwt
import datetime
import os

# Blueprint para as rotas da API React
react_api = Blueprint('react_api', __name__, url_prefix='/api')

# Chave secreta para JWT
JWT_SECRET = os.environ.get('SECRET_KEY', 'shack-game-secret-key-2024-csrf-protection-enabled')

def token_required(f):
    """Decorator para verificar token JWT"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # Verificar token no header Authorization
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer TOKEN
            except IndexError:
                return jsonify({'sucesso': False, 'mensagem': 'Token inválido'}), 401
        
        if not token:
            return jsonify({'sucesso': False, 'mensagem': 'Token não fornecido'}), 401
        
        try:
            data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            current_user_uid = data['uid']
        except jwt.ExpiredSignatureError:
            return jsonify({'sucesso': False, 'mensagem': 'Token expirado'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'sucesso': False, 'mensagem': 'Token inválido'}), 401
        
        return f(current_user_uid, *args, **kwargs)
    
    return decorated

def generate_token(user_data):
    """Gera token JWT para o usuário"""
    payload = {
        'uid': user_data['uid'],
        'nick': user_data['nick'],
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

# === ROTAS DE AUTENTICAÇÃO ===

@react_api.route('/auth/login', methods=['POST'])
def api_login():
    """Login para React"""
    try:
        from game import new_models as models
        
        data = request.get_json()
        if not data:
            return jsonify({'sucesso': False, 'mensagem': 'Dados não fornecidos'}), 400
        
        email = data.get('email', '').strip()
        password = data.get('password', '').strip()
        
        if not email or not password:
            return jsonify({'sucesso': False, 'mensagem': 'Email e senha são obrigatórios'}), 400
        
        # Verificar credenciais
        user = models.verificar_credenciais_login(email, password)
        
        if user and user.get('sucesso'):
            # Gerar token JWT
            token = generate_token(user['usuario'])
            
            return jsonify({
                'sucesso': True,
                'mensagem': 'Login realizado com sucesso',
                'user': {
                    'uid': user['usuario']['uid'],
                    'nick': user['usuario']['nick'],
                    'email': user['usuario']['email']
                },
                'token': token
            })
        else:
            return jsonify({
                'sucesso': False,
                'mensagem': 'Email ou senha incorretos'
            }), 401
            
    except Exception as e:
        print(f"Erro no login API: {str(e)}")
        return jsonify({
            'sucesso': False,
            'mensagem': 'Erro interno do servidor'
        }), 500

@react_api.route('/auth/register', methods=['POST'])
def api_register():
    """Registro para React"""
    try:
        from game import new_models as models
        
        data = request.get_json()
        if not data:
            return jsonify({'sucesso': False, 'mensagem': 'Dados não fornecidos'}), 400
        
        email = data.get('email', '').strip()
        password = data.get('password', '').strip()
        nick = data.get('nick', '').strip()
        
        if not email or not password or not nick:
            return jsonify({'sucesso': False, 'mensagem': 'Todos os campos são obrigatórios'}), 400
        
        # Criar usuário
        result = models.criar_usuario(email, password, nick)
        
        if result and result.get('sucesso'):
            # Gerar token JWT
            token = generate_token(result['usuario'])
            
            return jsonify({
                'sucesso': True,
                'mensagem': 'Conta criada com sucesso',
                'user': {
                    'uid': result['usuario']['uid'],
                    'nick': result['usuario']['nick'],
                    'email': result['usuario']['email']
                },
                'token': token
            })
        else:
            return jsonify({
                'sucesso': False,
                'mensagem': result.get('mensagem', 'Erro ao criar conta')
            }), 400
            
    except Exception as e:
        print(f"Erro no registro API: {str(e)}")
        return jsonify({
            'sucesso': False,
            'mensagem': 'Erro interno do servidor'
        }), 500

@react_api.route('/auth/verify', methods=['GET'])
@token_required
def api_verify_token(current_user_uid):
    """Verificar se o token é válido"""
    try:
        from game import new_models as models
        
        # Buscar dados atuais do usuário
        jogador = models.get_jogador(current_user_uid)
        
        if jogador:
            return jsonify({
                'sucesso': True,
                'mensagem': 'Token válido',
                'user': {
                    'uid': jogador['uid'],
                    'nick': jogador['nick'],
                    'email': jogador['email']
                }
            })
        else:
            return jsonify({'sucesso': False, 'mensagem': 'Usuário não encontrado'}), 404
            
    except Exception as e:
        print(f"Erro na verificação do token: {str(e)}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno'}), 500

# === ROTAS DO JOGO ===

@react_api.route('/jogador', methods=['GET'])
@token_required
def api_get_player_data(current_user_uid):
    """Obter dados do jogador"""
    try:
        from game import new_models as models
        
        jogador = models.get_jogador(current_user_uid)
        
        if jogador:
            return jsonify({
                'sucesso': True,
                'jogador': jogador
            })
        else:
            return jsonify({'sucesso': False, 'mensagem': 'Jogador não encontrado'}), 404
            
    except Exception as e:
        print(f"Erro ao obter dados do jogador: {str(e)}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno'}), 500

@react_api.route('/scan', methods=['GET'])
@token_required
def api_scan_targets(current_user_uid):
    """Escanear alvos disponíveis"""
    try:
        from game import new_models as models
        
        resultado = models.escanear_alvos(current_user_uid)
        
        if resultado.get('sucesso'):
            return jsonify({
                'sucesso': True,
                'alvos': resultado.get('alvos', []),
                'mensagem': f"{len(resultado.get('alvos', []))} alvos encontrados"
            })
        else:
            return jsonify({
                'sucesso': False,
                'mensagem': resultado.get('mensagem', 'Erro ao escanear alvos')
            })
            
    except Exception as e:
        print(f"Erro no scan: {str(e)}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno'}), 500

@react_api.route('/transferir', methods=['POST'])
@token_required
def api_transfer_money(current_user_uid):
    """Exploitar alvo e transferir dinheiro"""
    try:
        from game import new_models as models
        
        data = request.get_json()
        if not data:
            return jsonify({'sucesso': False, 'mensagem': 'Dados não fornecidos'}), 400
        
        alvo_ip = data.get('alvo_ip')
        porcentagem = data.get('porcentagem')
        
        if not alvo_ip or not porcentagem:
            return jsonify({'sucesso': False, 'mensagem': 'IP do alvo e porcentagem são obrigatórios'}), 400
        
        # Validar porcentagem
        try:
            porcentagem = float(porcentagem)
            if porcentagem < 20 or porcentagem > 80:
                return jsonify({'sucesso': False, 'mensagem': 'Porcentagem deve estar entre 20% e 80%'}), 400
        except (ValueError, TypeError):
            return jsonify({'sucesso': False, 'mensagem': 'Porcentagem inválida'}), 400
        
        resultado = models.transferir_dinheiro_alvo(current_user_uid, alvo_ip, porcentagem)
        
        return jsonify(resultado)
        
    except Exception as e:
        print(f"Erro na transferência: {str(e)}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno'}), 500

# === ROTAS DO CHAT ===

@react_api.route('/chat/messages', methods=['GET'])
@token_required
def api_get_chat_messages(current_user_uid):
    """Obter mensagens do chat"""
    try:
        from game import new_models as models
        
        # Buscar mensagens recentes (últimas 50)
        mensagens = models.get_mensagens_chat(limite=50)
        
        return jsonify({
            'sucesso': True,
            'messages': mensagens
        })
        
    except Exception as e:
        print(f"Erro ao obter mensagens do chat: {str(e)}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno'}), 500

@react_api.route('/chat/send', methods=['POST'])
@token_required
def api_send_chat_message(current_user_uid):
    """Enviar mensagem no chat"""
    try:
        from game import new_models as models
        
        data = request.get_json()
        if not data:
            return jsonify({'sucesso': False, 'mensagem': 'Dados não fornecidos'}), 400
        
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'sucesso': False, 'mensagem': 'Mensagem não pode estar vazia'}), 400
        
        if len(message) > 500:
            return jsonify({'sucesso': False, 'mensagem': 'Mensagem muito longa (máximo 500 caracteres)'}), 400
        
        resultado = models.enviar_mensagem_chat(current_user_uid, message)
        
        return jsonify(resultado)
        
    except Exception as e:
        print(f"Erro ao enviar mensagem: {str(e)}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno'}), 500

# === ROTAS DA LOJA ===

@react_api.route('/appstore', methods=['GET'])
@token_required
def api_get_app_store(current_user_uid):
    """Obter itens da loja de apps"""
    try:
        from game import new_models as models
        
        loja = models.get_loja_apps()
        
        return jsonify({
            'sucesso': True,
            'loja': loja
        })
        
    except Exception as e:
        print(f"Erro ao obter loja: {str(e)}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno'}), 500

@react_api.route('/appstore/comprar', methods=['POST'])
@token_required
def api_purchase_app(current_user_uid):
    """Comprar app na loja"""
    try:
        from game import new_models as models
        
        data = request.get_json()
        if not data:
            return jsonify({'sucesso': False, 'mensagem': 'Dados não fornecidos'}), 400
        
        item = data.get('item')
        quantidade = data.get('quantidade', 1)
        
        if not item:
            return jsonify({'sucesso': False, 'mensagem': 'Item não especificado'}), 400
        
        resultado = models.comprar_app(current_user_uid, item, quantidade)
        
        return jsonify(resultado)
        
    except Exception as e:
        print(f"Erro na compra: {str(e)}")
        return jsonify({'sucesso': False, 'mensagem': 'Erro interno'}), 500
