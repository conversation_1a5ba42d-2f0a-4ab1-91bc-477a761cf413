{"ast": null, "code": "export class FunctionsError extends Error {\n  constructor(message, name = 'FunctionsError', context) {\n    super(message);\n    this.name = name;\n    this.context = context;\n  }\n}\nexport class FunctionsFetchError extends FunctionsError {\n  constructor(context) {\n    super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context);\n  }\n}\nexport class FunctionsRelayError extends FunctionsError {\n  constructor(context) {\n    super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context);\n  }\n}\nexport class FunctionsHttpError extends FunctionsError {\n  constructor(context) {\n    super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context);\n  }\n}\n// Define the enum for the 'region' property\nexport var FunctionRegion;\n(function (FunctionRegion) {\n  FunctionRegion[\"Any\"] = \"any\";\n  FunctionRegion[\"ApNortheast1\"] = \"ap-northeast-1\";\n  FunctionRegion[\"ApNortheast2\"] = \"ap-northeast-2\";\n  FunctionRegion[\"ApSouth1\"] = \"ap-south-1\";\n  FunctionRegion[\"ApSoutheast1\"] = \"ap-southeast-1\";\n  FunctionRegion[\"ApSoutheast2\"] = \"ap-southeast-2\";\n  FunctionRegion[\"CaCentral1\"] = \"ca-central-1\";\n  FunctionRegion[\"EuCentral1\"] = \"eu-central-1\";\n  FunctionRegion[\"EuWest1\"] = \"eu-west-1\";\n  FunctionRegion[\"EuWest2\"] = \"eu-west-2\";\n  FunctionRegion[\"EuWest3\"] = \"eu-west-3\";\n  FunctionRegion[\"SaEast1\"] = \"sa-east-1\";\n  FunctionRegion[\"UsEast1\"] = \"us-east-1\";\n  FunctionRegion[\"UsWest1\"] = \"us-west-1\";\n  FunctionRegion[\"UsWest2\"] = \"us-west-2\";\n})(FunctionRegion || (FunctionRegion = {}));", "map": {"version": 3, "names": ["FunctionsError", "Error", "constructor", "message", "name", "context", "FunctionsFetchError", "FunctionsRelayError", "FunctionsHttpError", "FunctionRegion"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\node_modules\\@supabase\\functions-js\\src\\types.ts"], "sourcesContent": ["export type Fetch = typeof fetch\n\n/**\n * Response format\n */\nexport interface FunctionsResponseSuccess<T> {\n  data: T\n  error: null\n  response?: Response\n}\nexport interface FunctionsResponseFailure {\n  data: null\n  error: any\n  response?: Response\n}\nexport type FunctionsResponse<T> = FunctionsResponseSuccess<T> | FunctionsResponseFailure\n\nexport class FunctionsError extends Error {\n  context: any\n  constructor(message: string, name = 'FunctionsError', context?: any) {\n    super(message)\n    this.name = name\n    this.context = context\n  }\n}\n\nexport class FunctionsFetchError extends FunctionsError {\n  constructor(context: any) {\n    super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context)\n  }\n}\n\nexport class FunctionsRelayError extends FunctionsError {\n  constructor(context: any) {\n    super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context)\n  }\n}\n\nexport class FunctionsHttpError extends FunctionsError {\n  constructor(context: any) {\n    super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context)\n  }\n}\n// Define the enum for the 'region' property\nexport enum FunctionRegion {\n  Any = 'any',\n  ApNortheast1 = 'ap-northeast-1',\n  ApNortheast2 = 'ap-northeast-2',\n  ApSouth1 = 'ap-south-1',\n  ApSoutheast1 = 'ap-southeast-1',\n  ApSoutheast2 = 'ap-southeast-2',\n  CaCentral1 = 'ca-central-1',\n  EuCentral1 = 'eu-central-1',\n  EuWest1 = 'eu-west-1',\n  EuWest2 = 'eu-west-2',\n  EuWest3 = 'eu-west-3',\n  SaEast1 = 'sa-east-1',\n  UsEast1 = 'us-east-1',\n  UsWest1 = 'us-west-1',\n  UsWest2 = 'us-west-2',\n}\n\nexport type FunctionInvokeOptions = {\n  /**\n   * Object representing the headers to send with the request.\n   */\n  headers?: { [key: string]: string }\n  /**\n   * The HTTP verb of the request\n   */\n  method?: 'POST' | 'GET' | 'PUT' | 'PATCH' | 'DELETE'\n  /**\n   * The Region to invoke the function in.\n   */\n  region?: FunctionRegion\n  /**\n   * The body of the request.\n   */\n  body?:\n    | File\n    | Blob\n    | ArrayBuffer\n    | FormData\n    | ReadableStream<Uint8Array>\n    | Record<string, any>\n    | string\n}\n"], "mappings": "AAiBA,OAAM,MAAOA,cAAe,SAAQC,KAAK;EAEvCC,YAAYC,OAAe,EAAEC,IAAI,GAAG,gBAAgB,EAAEC,OAAa;IACjE,KAAK,CAACF,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;EACxB;;AAGF,OAAM,MAAOC,mBAAoB,SAAQN,cAAc;EACrDE,YAAYG,OAAY;IACtB,KAAK,CAAC,+CAA+C,EAAE,qBAAqB,EAAEA,OAAO,CAAC;EACxF;;AAGF,OAAM,MAAOE,mBAAoB,SAAQP,cAAc;EACrDE,YAAYG,OAAY;IACtB,KAAK,CAAC,wCAAwC,EAAE,qBAAqB,EAAEA,OAAO,CAAC;EACjF;;AAGF,OAAM,MAAOG,kBAAmB,SAAQR,cAAc;EACpDE,YAAYG,OAAY;IACtB,KAAK,CAAC,8CAA8C,EAAE,oBAAoB,EAAEA,OAAO,CAAC;EACtF;;AAEF;AACA,WAAYI,cAgBX;AAhBD,WAAYA,cAAc;EACxBA,cAAA,eAAW;EACXA,cAAA,mCAA+B;EAC/BA,cAAA,mCAA+B;EAC/BA,cAAA,2BAAuB;EACvBA,cAAA,mCAA+B;EAC/BA,cAAA,mCAA+B;EAC/BA,cAAA,+BAA2B;EAC3BA,cAAA,+BAA2B;EAC3BA,cAAA,yBAAqB;EACrBA,cAAA,yBAAqB;EACrBA,cAAA,yBAAqB;EACrBA,cAAA,yBAAqB;EACrBA,cAAA,yBAAqB;EACrBA,cAAA,yBAAqB;EACrBA,cAAA,yBAAqB;AACvB,CAAC,EAhBWA,cAAc,KAAdA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}