{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\ui\\\\ModernButton.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernButton = ({\n  children,\n  onClick,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  className = '',\n  type = 'button',\n  icon,\n  iconPosition = 'left',\n  glow = false\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed';\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base',\n    xl: 'px-8 py-4 text-lg'\n  };\n  const variantClasses = {\n    primary: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl hover:shadow-blue-500/25 focus:ring-blue-500',\n    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white shadow-lg hover:shadow-xl hover:shadow-gray-500/25 focus:ring-gray-500',\n    success: 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl hover:shadow-green-500/25 focus:ring-green-500',\n    danger: 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white shadow-lg hover:shadow-xl hover:shadow-red-500/25 focus:ring-red-500',\n    warning: 'bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white shadow-lg hover:shadow-xl hover:shadow-yellow-500/25 focus:ring-yellow-500',\n    ghost: 'bg-transparent hover:bg-white/5 text-gray-300 hover:text-white border border-gray-600 hover:border-gray-500 focus:ring-gray-500',\n    neon: 'bg-transparent border-2 border-cyan-500 text-cyan-400 hover:bg-cyan-500/10 hover:text-cyan-300 shadow-lg shadow-cyan-500/20 hover:shadow-xl hover:shadow-cyan-500/30 focus:ring-cyan-500'\n  };\n  const glowClasses = glow ? {\n    primary: 'shadow-xl shadow-blue-500/30',\n    secondary: 'shadow-xl shadow-gray-500/30',\n    success: 'shadow-xl shadow-green-500/30',\n    danger: 'shadow-xl shadow-red-500/30',\n    warning: 'shadow-xl shadow-yellow-500/30',\n    ghost: 'shadow-lg shadow-gray-500/10',\n    neon: 'shadow-xl shadow-cyan-500/40'\n  } : {};\n  const classes = [baseClasses, sizeClasses[size], variantClasses[variant], glow ? glowClasses[variant] : '', !disabled && !loading ? 'hover:-translate-y-0.5 active:translate-y-0' : '', className].filter(Boolean).join(' ');\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    type: type,\n    className: classes,\n    onClick: onClick,\n    disabled: disabled || loading,\n    children: [loading && /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-current\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n        className: \"opacity-25\",\n        cx: \"12\",\n        cy: \"12\",\n        r: \"10\",\n        stroke: \"currentColor\",\n        strokeWidth: \"4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        className: \"opacity-75\",\n        fill: \"currentColor\",\n        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 9\n    }, this), icon && iconPosition === 'left' && !loading && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"mr-2\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 9\n    }, this), children, icon && iconPosition === 'right' && !loading && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"ml-2\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_c = ModernButton;\nexport default ModernButton;\nvar _c;\n$RefreshReg$(_c, \"ModernButton\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ModernButton", "children", "onClick", "variant", "size", "disabled", "loading", "className", "type", "icon", "iconPosition", "glow", "baseClasses", "sizeClasses", "sm", "md", "lg", "xl", "variantClasses", "primary", "secondary", "success", "danger", "warning", "ghost", "neon", "glowClasses", "classes", "filter", "Boolean", "join", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/ui/ModernButton.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ModernButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'ghost' | 'neon';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  disabled?: boolean;\n  loading?: boolean;\n  className?: string;\n  type?: 'button' | 'submit' | 'reset';\n  icon?: React.ReactNode;\n  iconPosition?: 'left' | 'right';\n  glow?: boolean;\n}\n\nconst ModernButton: React.FC<ModernButtonProps> = ({\n  children,\n  onClick,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  className = '',\n  type = 'button',\n  icon,\n  iconPosition = 'left',\n  glow = false,\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed';\n\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base',\n    xl: 'px-8 py-4 text-lg',\n  };\n\n  const variantClasses = {\n    primary: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl hover:shadow-blue-500/25 focus:ring-blue-500',\n    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white shadow-lg hover:shadow-xl hover:shadow-gray-500/25 focus:ring-gray-500',\n    success: 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl hover:shadow-green-500/25 focus:ring-green-500',\n    danger: 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white shadow-lg hover:shadow-xl hover:shadow-red-500/25 focus:ring-red-500',\n    warning: 'bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white shadow-lg hover:shadow-xl hover:shadow-yellow-500/25 focus:ring-yellow-500',\n    ghost: 'bg-transparent hover:bg-white/5 text-gray-300 hover:text-white border border-gray-600 hover:border-gray-500 focus:ring-gray-500',\n    neon: 'bg-transparent border-2 border-cyan-500 text-cyan-400 hover:bg-cyan-500/10 hover:text-cyan-300 shadow-lg shadow-cyan-500/20 hover:shadow-xl hover:shadow-cyan-500/30 focus:ring-cyan-500',\n  };\n\n  const glowClasses = glow ? {\n    primary: 'shadow-xl shadow-blue-500/30',\n    secondary: 'shadow-xl shadow-gray-500/30',\n    success: 'shadow-xl shadow-green-500/30',\n    danger: 'shadow-xl shadow-red-500/30',\n    warning: 'shadow-xl shadow-yellow-500/30',\n    ghost: 'shadow-lg shadow-gray-500/10',\n    neon: 'shadow-xl shadow-cyan-500/40',\n  } : {};\n\n  const classes = [\n    baseClasses,\n    sizeClasses[size],\n    variantClasses[variant],\n    glow ? glowClasses[variant] : '',\n    !disabled && !loading ? 'hover:-translate-y-0.5 active:translate-y-0' : '',\n    className,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <button\n      type={type}\n      className={classes}\n      onClick={onClick}\n      disabled={disabled || loading}\n    >\n      {loading && (\n        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\" fill=\"none\" viewBox=\"0 0 24 24\">\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n        </svg>\n      )}\n      \n      {icon && iconPosition === 'left' && !loading && (\n        <span className=\"mr-2\">{icon}</span>\n      )}\n      \n      {children}\n      \n      {icon && iconPosition === 'right' && !loading && (\n        <span className=\"ml-2\">{icon}</span>\n      )}\n    </button>\n  );\n};\n\nexport default ModernButton;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgB1B,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,QAAQ;EACRC,OAAO;EACPC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,IAAI;EACXC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,SAAS,GAAG,EAAE;EACdC,IAAI,GAAG,QAAQ;EACfC,IAAI;EACJC,YAAY,GAAG,MAAM;EACrBC,IAAI,GAAG;AACT,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG,yOAAyO;EAE7P,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,qBAAqB;IACzBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE,qBAAqB;IACzBC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,cAAc,GAAG;IACrBC,OAAO,EAAE,oKAAoK;IAC7KC,SAAS,EAAE,oKAAoK;IAC/KC,OAAO,EAAE,0KAA0K;IACnLC,MAAM,EAAE,8JAA8J;IACtKC,OAAO,EAAE,gLAAgL;IACzLC,KAAK,EAAE,iIAAiI;IACxIC,IAAI,EAAE;EACR,CAAC;EAED,MAAMC,WAAW,GAAGf,IAAI,GAAG;IACzBQ,OAAO,EAAE,8BAA8B;IACvCC,SAAS,EAAE,8BAA8B;IACzCC,OAAO,EAAE,+BAA+B;IACxCC,MAAM,EAAE,6BAA6B;IACrCC,OAAO,EAAE,gCAAgC;IACzCC,KAAK,EAAE,8BAA8B;IACrCC,IAAI,EAAE;EACR,CAAC,GAAG,CAAC,CAAC;EAEN,MAAME,OAAO,GAAG,CACdf,WAAW,EACXC,WAAW,CAACT,IAAI,CAAC,EACjBc,cAAc,CAACf,OAAO,CAAC,EACvBQ,IAAI,GAAGe,WAAW,CAACvB,OAAO,CAAC,GAAG,EAAE,EAChC,CAACE,QAAQ,IAAI,CAACC,OAAO,GAAG,6CAA6C,GAAG,EAAE,EAC1EC,SAAS,CACV,CAACqB,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAE3B,oBACE/B,OAAA;IACES,IAAI,EAAEA,IAAK;IACXD,SAAS,EAAEoB,OAAQ;IACnBzB,OAAO,EAAEA,OAAQ;IACjBG,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;IAAAL,QAAA,GAE7BK,OAAO,iBACNP,OAAA;MAAKQ,SAAS,EAAC,8CAA8C;MAACwB,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAAA/B,QAAA,gBAC3FF,OAAA;QAAQQ,SAAS,EAAC,YAAY;QAAC0B,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACrG1C,OAAA;QAAMQ,SAAS,EAAC,YAAY;QAACwB,IAAI,EAAC,cAAc;QAACW,CAAC,EAAC;MAAiH;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzK,CACN,EAEAhC,IAAI,IAAIC,YAAY,KAAK,MAAM,IAAI,CAACJ,OAAO,iBAC1CP,OAAA;MAAMQ,SAAS,EAAC,MAAM;MAAAN,QAAA,EAAEQ;IAAI;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACpC,EAEAxC,QAAQ,EAERQ,IAAI,IAAIC,YAAY,KAAK,OAAO,IAAI,CAACJ,OAAO,iBAC3CP,OAAA;MAAMQ,SAAS,EAAC,MAAM;MAAAN,QAAA,EAAEQ;IAAI;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACpC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACE,EAAA,GA5EI3C,YAAyC;AA8E/C,eAAeA,YAAY;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}