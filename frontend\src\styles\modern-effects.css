/* Efeitos visuais modernos para o jogo */

/* Animações de entrada */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px currentColor;
  }
  50% {
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes matrix-rain {
  0% {
    transform: translateY(-100vh);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

/* Classes utilitárias */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Efeitos de hover modernos */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  filter: brightness(1.2);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

/* Glassmorphism avançado */
.glass-advanced {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-advanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

/* Efeitos de neon */
.neon-blue {
  color: #00f5ff;
  text-shadow: 
    0 0 5px #00f5ff,
    0 0 10px #00f5ff,
    0 0 15px #00f5ff,
    0 0 20px #00f5ff;
}

.neon-green {
  color: #39ff14;
  text-shadow: 
    0 0 5px #39ff14,
    0 0 10px #39ff14,
    0 0 15px #39ff14,
    0 0 20px #39ff14;
}

.neon-purple {
  color: #bf00ff;
  text-shadow: 
    0 0 5px #bf00ff,
    0 0 10px #bf00ff,
    0 0 15px #bf00ff,
    0 0 20px #bf00ff;
}

/* Gradientes modernos */
.gradient-cyber {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-neon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-matrix {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-hacker {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Efeitos de partículas */
.particles-bg {
  position: relative;
  overflow: hidden;
}

.particles-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

/* Scrollbar personalizada */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #667eea, #764ba2);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #764ba2, #667eea);
}

/* Efeitos de loading */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Efeitos de texto */
.text-glow {
  text-shadow: 0 0 10px currentColor;
}

.text-cyber {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  letter-spacing: 1px;
}

/* Responsividade */
@media (max-width: 768px) {
  .glass-advanced {
    backdrop-filter: blur(10px);
  }
  
  .hover-lift:hover {
    transform: translateY(-4px);
  }
}

/* Modo escuro aprimorado */
@media (prefers-color-scheme: dark) {
  .glass-advanced {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Animações de entrada escalonadas */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }
.stagger-7 { animation-delay: 0.7s; }
.stagger-8 { animation-delay: 0.8s; }
.stagger-9 { animation-delay: 0.9s; }
