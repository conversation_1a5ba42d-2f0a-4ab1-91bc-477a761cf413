import React from 'react';
import { useSimpleAuth } from '../stores/simpleAuthStore';

const SimpleLoginPage: React.FC = () => {
  const { isLoading, error, simulateLogin, isAuthenticated, user } = useSimpleAuth();

  console.log('SimpleLoginPage - Estado:', { isLoading, isAuthenticated, user: !!user });

  // Se já está autenticado, mostrar sucesso
  if (isAuthenticated && user) {
    return (
      <div className="min-h-screen bg-bg-primary text-text-primary flex items-center justify-center">
        <div className="card max-w-md w-full">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-green-400 mb-4">
              ✅ Login Realizado!
            </h1>
            <div className="text-text-muted mb-6">
              <p>Bem-vindo, <strong>{user.nick}</strong>!</p>
              <p className="text-sm">UID: {user.uid}</p>
              <p className="text-sm">Email: {user.email}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-green-300 mb-4">
                🎮 Agora você pode acessar o jogo!
              </p>
              <button 
                onClick={() => window.location.href = '/game'}
                className="btn-primary"
              >
                Ir para o Jogo
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-bg-primary text-text-primary flex items-center justify-center">
      <div className="card max-w-md w-full">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">
            🎮 SHACK Web Game
          </h1>
          <p className="text-text-muted">
            Login Simplificado (Modo Teste)
          </p>
        </div>

        {error && (
          <div className="bg-red-900 border border-red-500 text-red-100 p-4 rounded-lg mb-6">
            <p className="text-sm">{error}</p>
          </div>
        )}

        <div className="space-y-6">
          <div className="card bg-bg-tertiary">
            <h3 className="text-lg font-semibold mb-4">
              🧪 Modo de Teste
            </h3>
            <p className="text-text-muted text-sm mb-4">
              Este é um login simulado para testar o React sem o Flask.
              Clique no botão abaixo para simular um login bem-sucedido.
            </p>
            
            <button
              onClick={simulateLogin}
              disabled={isLoading}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                isLoading
                  ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Simulando Login...
                </div>
              ) : (
                '🚀 Simular Login'
              )}
            </button>
          </div>

          <div className="card bg-yellow-900 border-yellow-500">
            <h3 className="text-lg font-semibold text-yellow-100 mb-2">
              ⚠️ Aviso
            </h3>
            <p className="text-yellow-200 text-sm">
              Esta é uma versão de teste. O Flask backend será conectado posteriormente.
            </p>
          </div>
        </div>

        <div className="mt-8 text-center text-text-muted text-xs">
          <p>Versão de teste - React funcionando ✅</p>
        </div>
      </div>
    </div>
  );
};

export default SimpleLoginPage;
