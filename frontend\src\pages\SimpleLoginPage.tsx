import React, { useState } from 'react';
import { useSimpleAuth } from '../stores/simpleAuthStore';

const SimpleLoginPage: React.FC = () => {
  const { isLoading, error, login, register, simulateLogin, isAuthenticated, user, clearError } = useSimpleAuth();

  const [isRegisterMode, setIsRegisterMode] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    nick: ''
  });

  console.log('SimpleLoginPage - Estado:', { isLoading, isAuthenticated, user: !!user });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Limpar erro quando usuário digita
    if (error) clearError();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isRegisterMode) {
      if (!formData.email || !formData.password || !formData.nick) {
        return;
      }
      await register({
        email: formData.email,
        password: formData.password,
        nick: formData.nick
      });
    } else {
      if (!formData.email || !formData.password) {
        return;
      }
      await login({
        email: formData.email,
        password: formData.password
      });
    }
  };

  const toggleMode = () => {
    setIsRegisterMode(!isRegisterMode);
    setFormData({ email: '', password: '', nick: '' });
    clearError();
  };

  // Se já está autenticado, mostrar sucesso e aguardar carregamento
  if (isAuthenticated && user) {
    return (
      <div className="h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">✅</div>
          <h1 className="text-2xl font-bold text-green-400 mb-4">
            Login Realizado!
          </h1>
          <div className="text-gray-300 mb-6">
            <p>Bem-vindo, <strong>{user.nick}</strong>!</p>
            <p className="text-sm">UID: {user.uid}</p>
            <p className="text-sm">Email: {user.email}</p>
          </div>
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-400 mx-auto mb-2"></div>
            <p className="text-sm text-green-300">
              🎮 Carregando o jogo...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-bg-primary text-text-primary flex items-center justify-center">
      <div className="card max-w-md w-full">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">
            🎮 SHACK Web Game
          </h1>
          <p className="text-text-muted">
            Login Simplificado (Modo Teste)
          </p>
        </div>

        {error && (
          <div className="bg-red-900 border border-red-500 text-red-100 p-4 rounded-lg mb-6">
            <p className="text-sm">{error}</p>
          </div>
        )}

        <div className="space-y-6">
          {/* Formulário de Login/Registro */}
          <div className="card">
            <h3 className="text-xl font-semibold mb-6">
              {isRegisterMode ? '📝 Criar Conta' : '🔐 Fazer Login'}
            </h3>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              {isRegisterMode && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Nick
                  </label>
                  <input
                    type="text"
                    name="nick"
                    value={formData.nick}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500"
                    placeholder="Seu nick no jogo"
                    required
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-2">
                  Senha
                </label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500"
                  placeholder="Sua senha"
                  required
                />
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                  isLoading
                    ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    {isRegisterMode ? 'Criando conta...' : 'Fazendo login...'}
                  </div>
                ) : (
                  isRegisterMode ? '✨ Criar Conta' : '🚀 Entrar'
                )}
              </button>
            </form>

            <div className="mt-6 text-center">
              <button
                onClick={toggleMode}
                className="text-blue-400 hover:text-blue-300 text-sm"
              >
                {isRegisterMode
                  ? 'Já tem conta? Fazer login'
                  : 'Não tem conta? Criar uma'
                }
              </button>
            </div>
          </div>

          {/* Botão de teste rápido */}
          <div className="card bg-bg-tertiary">
            <h3 className="text-lg font-semibold mb-4">
              🧪 Teste Rápido
            </h3>
            <p className="text-text-muted text-sm mb-4">
              Para testar rapidamente sem preencher formulário:
            </p>

            <button
              onClick={simulateLogin}
              disabled={isLoading}
              className="w-full btn-secondary"
            >
              ⚡ Login Instantâneo
            </button>
          </div>

          <div className="card bg-yellow-900 border-yellow-500">
            <h3 className="text-lg font-semibold text-yellow-100 mb-2">
              ℹ️ Modo Desenvolvimento
            </h3>
            <p className="text-yellow-200 text-sm">
              Usando API mock para desenvolvimento. Os dados não são persistidos.
            </p>
          </div>
        </div>

        <div className="mt-8 text-center text-text-muted text-xs">
          <p>Versão de teste - React funcionando ✅</p>
        </div>
      </div>
    </div>
  );
};

export default SimpleLoginPage;
