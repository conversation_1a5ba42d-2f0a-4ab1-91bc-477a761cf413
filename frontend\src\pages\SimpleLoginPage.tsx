import React, { useState } from 'react';
import { useSimpleAuth } from '../stores/simpleAuthStore';

const SimpleLoginPage: React.FC = () => {
  const { isLoading, error, login, register, simulateLogin, isAuthenticated, user, clearError } = useSimpleAuth();

  const [isRegisterMode, setIsRegisterMode] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    nick: ''
  });

  console.log('SimpleLoginPage - Estado:', { isLoading, isAuthenticated, user: !!user });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Limpar erro quando usuário digita
    if (error) clearError();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isRegisterMode) {
      if (!formData.email || !formData.password || !formData.nick) {
        return;
      }
      await register({
        email: formData.email,
        password: formData.password,
        nick: formData.nick
      });
    } else {
      if (!formData.email || !formData.password) {
        return;
      }
      await login({
        email: formData.email,
        password: formData.password
      });
    }
  };

  const toggleMode = () => {
    setIsRegisterMode(!isRegisterMode);
    setFormData({ email: '', password: '', nick: '' });
    clearError();
  };

  // Se já está autenticado, mostrar sucesso e aguardar carregamento
  if (isAuthenticated && user) {
    return (
      <div className="h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">✅</div>
          <h1 className="text-2xl font-bold text-green-400 mb-4">
            Login Realizado!
          </h1>
          <div className="text-gray-300 mb-6">
            <p>Bem-vindo, <strong>{user.nick}</strong>!</p>
            <p className="text-sm">UID: {user.uid}</p>
            <p className="text-sm">Email: {user.email}</p>
          </div>
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-400 mx-auto mb-2"></div>
            <p className="text-sm text-green-300">
              🎮 Carregando o jogo...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gradient-to-br from-gray-900 via-blue-900 to-black text-white flex flex-col">
      {/* Header */}
      <div className="p-6 text-center">
        <div className="text-6xl mb-4">🔒</div>
        <h1 className="text-2xl font-bold mb-2">SHACK</h1>
        <p className="text-sm text-gray-400">Simulador de Hacking Online</p>
      </div>

      {/* Formulário */}
      <div className="flex-1 px-6 py-4">
        <div className="bg-gray-800/50 rounded-2xl p-6 border border-gray-700/50">
          <div className="flex mb-6">
            <button
              onClick={() => setIsRegisterMode(false)}
              className={`flex-1 py-2 px-4 rounded-l-lg font-semibold transition-colors ${
                !isRegisterMode
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Login
            </button>
            <button
              onClick={() => setIsRegisterMode(true)}
              className={`flex-1 py-2 px-4 rounded-r-lg font-semibold transition-colors ${
                isRegisterMode
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Registrar
            </button>
          </div>

          {/* Erro */}
          {error && (
            <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg">
              <p className="text-red-300 text-sm">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {isRegisterMode && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Nome do Hacker
                </label>
                <input
                  type="text"
                  name="nick"
                  value={formData.nick}
                  onChange={handleInputChange}
                  placeholder="Digite seu nick"
                  maxLength={20}
                  disabled={isLoading}
                  className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50"
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Email
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                disabled={isLoading}
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Senha
              </label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Digite sua senha"
                disabled={isLoading}
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full mt-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>{isRegisterMode ? 'Criando conta...' : 'Entrando...'}</span>
                </div>
              ) : (
                isRegisterMode ? 'Criar Conta' : 'Entrar'
              )}
            </button>
          </form>
        </div>

        {/* Teste Rápido */}
        <div className="mt-6 bg-gray-800/30 rounded-xl p-4 border border-gray-700/30">
          <h3 className="font-semibold mb-2 flex items-center">
            🧪 Teste Rápido
          </h3>
          <p className="text-sm text-gray-300 mb-4">
            Para testar rapidamente sem preencher formulário:
          </p>
          <button
            onClick={simulateLogin}
            disabled={isLoading}
            className="w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white font-semibold rounded-lg transition-colors"
          >
            ⚡ Login Instantâneo
          </button>
        </div>

        {/* Informações do jogo */}
        <div className="mt-6 bg-gray-800/30 rounded-xl p-4 border border-gray-700/30">
          <h3 className="font-semibold mb-2 flex items-center">
            🎮 Sobre o SHACK
          </h3>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• Simulador de hacking online</li>
            <li>• Faça upgrades nos seus aplicativos</li>
            <li>• Invada outros jogadores</li>
            <li>• Chat global em tempo real</li>
            <li>• Sistema de ranking competitivo</li>
          </ul>
        </div>
      </div>

      {/* Footer */}
      <div className="px-4 py-2 text-center">
        <p className="text-xs text-gray-500">
          SHACK v2.0 - Simulador de Hacking Online
        </p>
      </div>
    </div>
  );
};

export default SimpleLoginPage;
