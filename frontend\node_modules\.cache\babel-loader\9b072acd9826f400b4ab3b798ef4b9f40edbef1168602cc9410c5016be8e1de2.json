{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\ScannerPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useGameSystem } from '../hooks/useGameSystem';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport GameFooter from '../components/common/GameFooter';\nimport { ScanIcon, TargetIcon, ExploitIcon } from '../components/ui/GameIcons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ScannerPage = () => {\n  _s();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer\n  } = usePlayer();\n  const navigate = useNavigate();\n  const {\n    scanTargets,\n    isScanning,\n    scanError,\n    isExploiting,\n    exploitError,\n    performQuickScan,\n    performAdvancedScan,\n    performExploit,\n    clearErrors\n  } = useGameSystem();\n  const [scanType, setScanType] = useState('quick');\n  const [targetIp, setTargetIp] = useState('');\n  useEffect(() => {\n    clearErrors();\n  }, [clearErrors]);\n  const handleQuickScan = async () => {\n    try {\n      await performQuickScan();\n    } catch (error) {\n      console.error('Erro no scan rápido:', error);\n    }\n  };\n  const handleAdvancedScan = async () => {\n    if (!targetIp.trim()) {\n      alert('Digite um IP válido');\n      return;\n    }\n    try {\n      await performAdvancedScan(targetIp);\n    } catch (error) {\n      console.error('Erro no scan avançado:', error);\n    }\n  };\n  const handleExploit = async target => {\n    try {\n      const exploited = await performExploit(target);\n      if (exploited) {\n        // Navegar para tela de invasão\n        navigate('/game/invaded', {\n          state: {\n            target: exploited\n          }\n        });\n      }\n    } catch (error) {\n      console.error('Erro no exploit:', error);\n    }\n  };\n  const canExploit = target => {\n    const playerCPU = (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.cpu) || 1;\n    const targetFirewall = target.firewall || 1;\n    return playerCPU > targetFirewall;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-gray-700/50 p-4 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/game'),\n            className: \"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: \"\\u2190\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n              children: \"Network Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-400\",\n              children: \"Sistema de Reconhecimento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-1 text-green-400 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Online\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-4 overflow-y-auto space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 text-white flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(ScanIcon, {\n            size: 20,\n            className: \"text-blue-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Controles de Scanner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setScanType('quick'),\n            className: `flex-1 py-2 px-4 rounded-lg font-medium transition-all ${scanType === 'quick' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,\n            children: \"Scan R\\xE1pido\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setScanType('advanced'),\n            className: `flex-1 py-2 px-4 rounded-lg font-medium transition-all ${scanType === 'advanced' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,\n            children: \"Scan Avan\\xE7ado\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), scanType === 'quick' ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleQuickScan,\n          disabled: isScanning,\n          className: \"w-full py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold flex items-center justify-center space-x-2\",\n          children: isScanning ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Escaneando...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ScanIcon, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Iniciar Scan R\\xE1pido\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: targetIp,\n            onChange: e => setTargetIp(e.target.value),\n            placeholder: \"Digite o IP do alvo (ex: *************)\",\n            className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAdvancedScan,\n            disabled: isScanning || !targetIp.trim(),\n            className: \"w-full py-3 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white rounded-lg font-semibold flex items-center justify-center space-x-2\",\n            children: isScanning ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Escaneando...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TargetIcon, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Scan por IP\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this), (scanError || exploitError) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 p-3 bg-red-900/50 border border-red-700 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-300 text-sm\",\n            children: scanError || exploitError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), scanTargets.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold mb-4 text-white\",\n          children: [\"Alvos Encontrados (\", scanTargets.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: scanTargets.map((target, index) => {\n            var _target$dinheiro;\n            const canExploitTarget = canExploit(target);\n            const playerCPU = (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.cpu) || 1;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-800/50 rounded-lg p-4 border border-gray-600/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-white\",\n                    children: target.nick\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-400 font-mono\",\n                    children: target.ip\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-400\",\n                    children: [\"N\\xEDvel \", target.nivel]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\"$\", ((_target$dinheiro = target.dinheiro) === null || _target$dinheiro === void 0 ? void 0 : _target$dinheiro.toLocaleString()) || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-3 mb-3 text-xs\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-700/50 rounded p-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-400\",\n                    children: \"Firewall:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-400 ml-1 font-semibold\",\n                    children: target.firewall || 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-700/50 rounded p-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-400\",\n                    children: \"Seu CPU:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-400 ml-1 font-semibold\",\n                    children: playerCPU\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleExploit(target),\n                disabled: !canExploitTarget || isExploiting,\n                className: `w-full py-2 rounded-lg font-semibold flex items-center justify-center space-x-2 ${canExploitTarget ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600/50 text-red-300 cursor-not-allowed'}`,\n                children: isExploiting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Explorando...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true) : canExploitTarget ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(ExploitIcon, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Explorar Sistema\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"CPU Insuficiente (precisa \", (target.firewall || 1) + 1, \"+)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this), scanTargets.length === 0 && !isScanning && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700/50 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(ScanIcon, {\n          size: 48,\n          className: \"text-gray-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-400 mb-2\",\n          children: \"Nenhum Alvo Encontrado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Execute um scan para encontrar sistemas vulner\\xE1veis na rede\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(GameFooter, {\n      currentPage: \"scanner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_s(ScannerPage, \"g99JsmSnCrTa4+kkmgdwOcEPBoM=\", false, function () {\n  return [useSimpleAuth, usePlayer, useNavigate, useGameSystem];\n});\n_c = ScannerPage;\nexport default ScannerPage;\nvar _c;\n$RefreshReg$(_c, \"ScannerPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useGameSystem", "useSimpleAuth", "usePlayer", "GameFooter", "ScanIcon", "TargetIcon", "ExploitIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ScannerPage", "_s", "user", "currentPlayer", "navigate", "scanTargets", "isScanning", "scanError", "isExploiting", "exploitError", "performQuickScan", "performAdvancedScan", "performExploit", "clearErrors", "scanType", "setScanType", "targetIp", "setTargetIp", "handleQuickScan", "error", "console", "handleAdvancedScan", "trim", "alert", "handleExploit", "target", "exploited", "state", "canExploit", "playerCPU", "cpu", "targetFirewall", "firewall", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "disabled", "type", "value", "onChange", "e", "placeholder", "length", "map", "index", "_target$dinheiro", "canExploitTarget", "nick", "ip", "nivel", "<PERSON><PERSON><PERSON>", "toLocaleString", "currentPage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/ScannerPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useGameSystem } from '../hooks/useGameSystem';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport GameFooter from '../components/common/GameFooter';\nimport { ScanIcon, TargetIcon, ExploitIcon } from '../components/ui/GameIcons';\n\nconst ScannerPage: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer } = usePlayer();\n  const navigate = useNavigate();\n  \n  const {\n    scanTargets,\n    isScanning,\n    scanError,\n    isExploiting,\n    exploitError,\n    performQuickScan,\n    performAdvancedScan,\n    performExploit,\n    clearErrors\n  } = useGameSystem();\n\n  const [scanType, setScanType] = useState<'quick' | 'advanced'>('quick');\n  const [targetIp, setTargetIp] = useState('');\n\n  useEffect(() => {\n    clearErrors();\n  }, [clearErrors]);\n\n  const handleQuickScan = async () => {\n    try {\n      await performQuickScan();\n    } catch (error) {\n      console.error('Erro no scan rápido:', error);\n    }\n  };\n\n  const handleAdvancedScan = async () => {\n    if (!targetIp.trim()) {\n      alert('Digite um IP válido');\n      return;\n    }\n\n    try {\n      await performAdvancedScan(targetIp);\n    } catch (error) {\n      console.error('Erro no scan avançado:', error);\n    }\n  };\n\n  const handleExploit = async (target: any) => {\n    try {\n      const exploited = await performExploit(target);\n      if (exploited) {\n        // Navegar para tela de invasão\n        navigate('/game/invaded', { state: { target: exploited } });\n      }\n    } catch (error) {\n      console.error('Erro no exploit:', error);\n    }\n  };\n\n  const canExploit = (target: any) => {\n    const playerCPU = currentPlayer?.cpu || 1;\n    const targetFirewall = target.firewall || 1;\n    return playerCPU > targetFirewall;\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-gray-800/90 to-gray-900/90 backdrop-blur-sm border-b border-gray-700/50 p-4 flex-shrink-0\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={() => navigate('/game')}\n              className=\"w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors\"\n            >\n              <span className=\"text-lg\">←</span>\n            </button>\n            <div>\n              <h1 className=\"text-xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\">\n                Network Scanner\n              </h1>\n              <p className=\"text-xs text-gray-400\">Sistema de Reconhecimento</p>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"flex items-center space-x-1 text-green-400 text-xs\">\n              <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n              <span>Online</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Conteúdo principal */}\n      <div className=\"flex-1 p-4 overflow-y-auto space-y-6\">\n        {/* Controles de Scan */}\n        <div className=\"bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50\">\n          <h2 className=\"text-lg font-semibold mb-4 text-white flex items-center space-x-2\">\n            <ScanIcon size={20} className=\"text-blue-400\" />\n            <span>Controles de Scanner</span>\n          </h2>\n          \n          {/* Tipo de scan */}\n          <div className=\"flex space-x-2 mb-4\">\n            <button\n              onClick={() => setScanType('quick')}\n              className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all ${\n                scanType === 'quick'\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n              }`}\n            >\n              Scan Rápido\n            </button>\n            <button\n              onClick={() => setScanType('advanced')}\n              className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all ${\n                scanType === 'advanced'\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n              }`}\n            >\n              Scan Avançado\n            </button>\n          </div>\n\n          {/* Controles específicos */}\n          {scanType === 'quick' ? (\n            <button\n              onClick={handleQuickScan}\n              disabled={isScanning}\n              className=\"w-full py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-semibold flex items-center justify-center space-x-2\"\n            >\n              {isScanning ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  <span>Escaneando...</span>\n                </>\n              ) : (\n                <>\n                  <ScanIcon size={16} />\n                  <span>Iniciar Scan Rápido</span>\n                </>\n              )}\n            </button>\n          ) : (\n            <div className=\"space-y-3\">\n              <input\n                type=\"text\"\n                value={targetIp}\n                onChange={(e) => setTargetIp(e.target.value)}\n                placeholder=\"Digite o IP do alvo (ex: *************)\"\n                className=\"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none\"\n              />\n              <button\n                onClick={handleAdvancedScan}\n                disabled={isScanning || !targetIp.trim()}\n                className=\"w-full py-3 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white rounded-lg font-semibold flex items-center justify-center space-x-2\"\n              >\n                {isScanning ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                    <span>Escaneando...</span>\n                  </>\n                ) : (\n                  <>\n                    <TargetIcon size={16} />\n                    <span>Scan por IP</span>\n                  </>\n                )}\n              </button>\n            </div>\n          )}\n\n          {/* Erros */}\n          {(scanError || exploitError) && (\n            <div className=\"mt-4 p-3 bg-red-900/50 border border-red-700 rounded-lg\">\n              <p className=\"text-red-300 text-sm\">{scanError || exploitError}</p>\n            </div>\n          )}\n        </div>\n\n        {/* Resultados do Scan */}\n        {scanTargets.length > 0 && (\n          <div className=\"bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50\">\n            <h2 className=\"text-lg font-semibold mb-4 text-white\">\n              Alvos Encontrados ({scanTargets.length})\n            </h2>\n            \n            <div className=\"space-y-3\">\n              {scanTargets.map((target, index) => {\n                const canExploitTarget = canExploit(target);\n                const playerCPU = currentPlayer?.cpu || 1;\n                \n                return (\n                  <div\n                    key={index}\n                    className=\"bg-gray-800/50 rounded-lg p-4 border border-gray-600/50\"\n                  >\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div>\n                        <h3 className=\"font-semibold text-white\">{target.nick}</h3>\n                        <p className=\"text-sm text-gray-400 font-mono\">{target.ip}</p>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-sm text-blue-400\">Nível {target.nivel}</div>\n                        <div className=\"text-xs text-gray-500\">${target.dinheiro?.toLocaleString() || '0'}</div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"grid grid-cols-2 gap-3 mb-3 text-xs\">\n                      <div className=\"bg-gray-700/50 rounded p-2\">\n                        <span className=\"text-gray-400\">Firewall:</span>\n                        <span className=\"text-red-400 ml-1 font-semibold\">{target.firewall || 1}</span>\n                      </div>\n                      <div className=\"bg-gray-700/50 rounded p-2\">\n                        <span className=\"text-gray-400\">Seu CPU:</span>\n                        <span className=\"text-blue-400 ml-1 font-semibold\">{playerCPU}</span>\n                      </div>\n                    </div>\n                    \n                    <button\n                      onClick={() => handleExploit(target)}\n                      disabled={!canExploitTarget || isExploiting}\n                      className={`w-full py-2 rounded-lg font-semibold flex items-center justify-center space-x-2 ${\n                        canExploitTarget\n                          ? 'bg-green-600 hover:bg-green-700 text-white'\n                          : 'bg-red-600/50 text-red-300 cursor-not-allowed'\n                      }`}\n                    >\n                      {isExploiting ? (\n                        <>\n                          <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                          <span>Explorando...</span>\n                        </>\n                      ) : canExploitTarget ? (\n                        <>\n                          <ExploitIcon size={16} />\n                          <span>Explorar Sistema</span>\n                        </>\n                      ) : (\n                        <span>CPU Insuficiente (precisa {(target.firewall || 1) + 1}+)</span>\n                      )}\n                    </button>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        )}\n\n        {/* Estado vazio */}\n        {scanTargets.length === 0 && !isScanning && (\n          <div className=\"bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700/50 text-center\">\n            <ScanIcon size={48} className=\"text-gray-500 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-400 mb-2\">Nenhum Alvo Encontrado</h3>\n            <p className=\"text-gray-500\">Execute um scan para encontrar sistemas vulneráveis na rede</p>\n          </div>\n        )}\n      </div>\n\n      {/* Footer */}\n      <GameFooter currentPage=\"scanner\" />\n    </div>\n  );\n};\n\nexport default ScannerPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAOC,UAAU,MAAM,iCAAiC;AACxD,SAASC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/E,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAK,CAAC,GAAGZ,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEa;EAAc,CAAC,GAAGZ,SAAS,CAAC,CAAC;EACrC,MAAMa,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAM;IACJiB,WAAW;IACXC,UAAU;IACVC,SAAS;IACTC,YAAY;IACZC,YAAY;IACZC,gBAAgB;IAChBC,mBAAmB;IACnBC,cAAc;IACdC;EACF,CAAC,GAAGxB,aAAa,CAAC,CAAC;EAEnB,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAuB,OAAO,CAAC;EACvE,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd0B,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB,MAAMK,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMR,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAME,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACL,QAAQ,CAACM,IAAI,CAAC,CAAC,EAAE;MACpBC,KAAK,CAAC,qBAAqB,CAAC;MAC5B;IACF;IAEA,IAAI;MACF,MAAMZ,mBAAmB,CAACK,QAAQ,CAAC;IACrC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMK,aAAa,GAAG,MAAOC,MAAW,IAAK;IAC3C,IAAI;MACF,MAAMC,SAAS,GAAG,MAAMd,cAAc,CAACa,MAAM,CAAC;MAC9C,IAAIC,SAAS,EAAE;QACb;QACAtB,QAAQ,CAAC,eAAe,EAAE;UAAEuB,KAAK,EAAE;YAAEF,MAAM,EAAEC;UAAU;QAAE,CAAC,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IAC1C;EACF,CAAC;EAED,MAAMS,UAAU,GAAIH,MAAW,IAAK;IAClC,MAAMI,SAAS,GAAG,CAAA1B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2B,GAAG,KAAI,CAAC;IACzC,MAAMC,cAAc,GAAGN,MAAM,CAACO,QAAQ,IAAI,CAAC;IAC3C,OAAOH,SAAS,GAAGE,cAAc;EACnC,CAAC;EAED,oBACElC,OAAA;IAAKoC,SAAS,EAAC,gGAAgG;IAAAC,QAAA,gBAE7GrC,OAAA;MAAKoC,SAAS,EAAC,iHAAiH;MAAAC,QAAA,eAC9HrC,OAAA;QAAKoC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDrC,OAAA;UAAKoC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CrC,OAAA;YACEsC,OAAO,EAAEA,CAAA,KAAM/B,QAAQ,CAAC,OAAO,CAAE;YACjC6B,SAAS,EAAC,qGAAqG;YAAAC,QAAA,eAE/GrC,OAAA;cAAMoC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACT1C,OAAA;YAAAqC,QAAA,gBACErC,OAAA;cAAIoC,SAAS,EAAC,4FAA4F;cAAAC,QAAA,EAAC;YAE3G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1C,OAAA;cAAGoC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1C,OAAA;UAAKoC,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBrC,OAAA;YAAKoC,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjErC,OAAA;cAAKoC,SAAS,EAAC;YAAiD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvE1C,OAAA;cAAAqC,QAAA,EAAM;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA;MAAKoC,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBAEnDrC,OAAA;QAAKoC,SAAS,EAAC,6GAA6G;QAAAC,QAAA,gBAC1HrC,OAAA;UAAIoC,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAC/ErC,OAAA,CAACJ,QAAQ;YAAC+C,IAAI,EAAE,EAAG;YAACP,SAAS,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChD1C,OAAA;YAAAqC,QAAA,EAAM;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAGL1C,OAAA;UAAKoC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCrC,OAAA;YACEsC,OAAO,EAAEA,CAAA,KAAMpB,WAAW,CAAC,OAAO,CAAE;YACpCkB,SAAS,EAAE,0DACTnB,QAAQ,KAAK,OAAO,GAChB,wBAAwB,GACxB,6CAA6C,EAChD;YAAAoB,QAAA,EACJ;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1C,OAAA;YACEsC,OAAO,EAAEA,CAAA,KAAMpB,WAAW,CAAC,UAAU,CAAE;YACvCkB,SAAS,EAAE,0DACTnB,QAAQ,KAAK,UAAU,GACnB,wBAAwB,GACxB,6CAA6C,EAChD;YAAAoB,QAAA,EACJ;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLzB,QAAQ,KAAK,OAAO,gBACnBjB,OAAA;UACEsC,OAAO,EAAEjB,eAAgB;UACzBuB,QAAQ,EAAEnC,UAAW;UACrB2B,SAAS,EAAC,+IAA+I;UAAAC,QAAA,EAExJ5B,UAAU,gBACTT,OAAA,CAAAE,SAAA;YAAAmC,QAAA,gBACErC,OAAA;cAAKoC,SAAS,EAAC;YAA2D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjF1C,OAAA;cAAAqC,QAAA,EAAM;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eAC1B,CAAC,gBAEH1C,OAAA,CAAAE,SAAA;YAAAmC,QAAA,gBACErC,OAAA,CAACJ,QAAQ;cAAC+C,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtB1C,OAAA;cAAAqC,QAAA,EAAM;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eAChC;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,gBAET1C,OAAA;UAAKoC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrC,OAAA;YACE6C,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE3B,QAAS;YAChB4B,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC4B,CAAC,CAACpB,MAAM,CAACkB,KAAK,CAAE;YAC7CG,WAAW,EAAC,yCAAyC;YACrDb,SAAS,EAAC;UAAmI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9I,CAAC,eACF1C,OAAA;YACEsC,OAAO,EAAEd,kBAAmB;YAC5BoB,QAAQ,EAAEnC,UAAU,IAAI,CAACU,QAAQ,CAACM,IAAI,CAAC,CAAE;YACzCW,SAAS,EAAC,+IAA+I;YAAAC,QAAA,EAExJ5B,UAAU,gBACTT,OAAA,CAAAE,SAAA;cAAAmC,QAAA,gBACErC,OAAA;gBAAKoC,SAAS,EAAC;cAA2D;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjF1C,OAAA;gBAAAqC,QAAA,EAAM;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eAC1B,CAAC,gBAEH1C,OAAA,CAAAE,SAAA;cAAAmC,QAAA,gBACErC,OAAA,CAACH,UAAU;gBAAC8C,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxB1C,OAAA;gBAAAqC,QAAA,EAAM;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACxB;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAGA,CAAChC,SAAS,IAAIE,YAAY,kBACzBZ,OAAA;UAAKoC,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eACtErC,OAAA;YAAGoC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAE3B,SAAS,IAAIE;UAAY;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLlC,WAAW,CAAC0C,MAAM,GAAG,CAAC,iBACrBlD,OAAA;QAAKoC,SAAS,EAAC,6GAA6G;QAAAC,QAAA,gBAC1HrC,OAAA;UAAIoC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GAAC,qBACjC,EAAC7B,WAAW,CAAC0C,MAAM,EAAC,GACzC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL1C,OAAA;UAAKoC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB7B,WAAW,CAAC2C,GAAG,CAAC,CAACvB,MAAM,EAAEwB,KAAK,KAAK;YAAA,IAAAC,gBAAA;YAClC,MAAMC,gBAAgB,GAAGvB,UAAU,CAACH,MAAM,CAAC;YAC3C,MAAMI,SAAS,GAAG,CAAA1B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2B,GAAG,KAAI,CAAC;YAEzC,oBACEjC,OAAA;cAEEoC,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBAEnErC,OAAA;gBAAKoC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDrC,OAAA;kBAAAqC,QAAA,gBACErC,OAAA;oBAAIoC,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EAAET,MAAM,CAAC2B;kBAAI;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3D1C,OAAA;oBAAGoC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAET,MAAM,CAAC4B;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACN1C,OAAA;kBAAKoC,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBrC,OAAA;oBAAKoC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,WAAM,EAACT,MAAM,CAAC6B,KAAK;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjE1C,OAAA;oBAAKoC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,GAAC,EAAC,EAAAgB,gBAAA,GAAAzB,MAAM,CAAC8B,QAAQ,cAAAL,gBAAA,uBAAfA,gBAAA,CAAiBM,cAAc,CAAC,CAAC,KAAI,GAAG;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1C,OAAA;gBAAKoC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDrC,OAAA;kBAAKoC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCrC,OAAA;oBAAMoC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChD1C,OAAA;oBAAMoC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAET,MAAM,CAACO,QAAQ,IAAI;kBAAC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACN1C,OAAA;kBAAKoC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCrC,OAAA;oBAAMoC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/C1C,OAAA;oBAAMoC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAEL;kBAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1C,OAAA;gBACEsC,OAAO,EAAEA,CAAA,KAAMX,aAAa,CAACC,MAAM,CAAE;gBACrCgB,QAAQ,EAAE,CAACU,gBAAgB,IAAI3C,YAAa;gBAC5CyB,SAAS,EAAE,mFACTkB,gBAAgB,GACZ,4CAA4C,GAC5C,+CAA+C,EAClD;gBAAAjB,QAAA,EAEF1B,YAAY,gBACXX,OAAA,CAAAE,SAAA;kBAAAmC,QAAA,gBACErC,OAAA;oBAAKoC,SAAS,EAAC;kBAA2D;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjF1C,OAAA;oBAAAqC,QAAA,EAAM;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC1B,CAAC,GACDY,gBAAgB,gBAClBtD,OAAA,CAAAE,SAAA;kBAAAmC,QAAA,gBACErC,OAAA,CAACF,WAAW;oBAAC6C,IAAI,EAAE;kBAAG;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzB1C,OAAA;oBAAAqC,QAAA,EAAM;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC7B,CAAC,gBAEH1C,OAAA;kBAAAqC,QAAA,GAAM,4BAA0B,EAAC,CAACT,MAAM,CAACO,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAC,IAAE;gBAAA;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACrE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA,GA/CJU,KAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgDP,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAlC,WAAW,CAAC0C,MAAM,KAAK,CAAC,IAAI,CAACzC,UAAU,iBACtCT,OAAA;QAAKoC,SAAS,EAAC,yHAAyH;QAAAC,QAAA,gBACtIrC,OAAA,CAACJ,QAAQ;UAAC+C,IAAI,EAAE,EAAG;UAACP,SAAS,EAAC;QAA4B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7D1C,OAAA;UAAIoC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF1C,OAAA;UAAGoC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA2D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzF,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1C,OAAA,CAACL,UAAU;MAACiE,WAAW,EAAC;IAAS;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjC,CAAC;AAEV,CAAC;AAACtC,EAAA,CAvQID,WAAqB;EAAA,QACRV,aAAa,EACJC,SAAS,EAClBH,WAAW,EAYxBC,aAAa;AAAA;AAAAqE,EAAA,GAfb1D,WAAqB;AAyQ3B,eAAeA,WAAW;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}