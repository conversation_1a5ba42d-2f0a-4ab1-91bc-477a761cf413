{"ast": null, "code": "import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { GAME_CONFIG, getPlayerLevel, getXpForNextLevel, calculateUpgradeCost, calculateXpReward } from '../types/game';\nimport { gameService } from '../services/gameService';\nconst initialPlayerApps = {\n  ...GAME_CONFIG.INITIAL_APPS\n};\nexport const useHackGameStore = create()(persist((set, get) => ({\n  // Initial state\n  player: null,\n  playerApps: initialPlayerApps,\n  isOnline: false,\n  notifications: [],\n  availableTargets: [],\n  hackHistory: [],\n  currentScreen: 'home',\n  isLoading: false,\n  error: null,\n  // Actions\n  initializePlayer: async (nick, email) => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const result = await gameService.createPlayer(nick, email);\n      if (result.success && result.player) {\n        const appsResult = await gameService.getPlayerApps(result.player.id);\n        if (appsResult.success && appsResult.apps) {\n          set({\n            player: result.player,\n            playerApps: {\n              antivirus: appsResult.apps.antivirus,\n              bankguard: appsResult.apps.bankguard,\n              bruteforce: appsResult.apps.bruteforce,\n              sdk: appsResult.apps.sdk,\n              firewall: appsResult.apps.firewall,\n              malwarekit: appsResult.apps.malwarekit,\n              proxyvpn: appsResult.apps.proxyvpn\n            },\n            isOnline: true,\n            isLoading: false\n          });\n\n          // Carregar notificações\n          await get().loadNotifications();\n          return true;\n        }\n      }\n      set({\n        error: result.error || 'Erro ao criar jogador',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  loadPlayer: async playerId => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      const [playerResult, appsResult] = await Promise.all([gameService.getPlayer(playerId), gameService.getPlayerApps(playerId)]);\n      if (playerResult.success && playerResult.player && appsResult.success && appsResult.apps) {\n        set({\n          player: playerResult.player,\n          playerApps: {\n            antivirus: appsResult.apps.antivirus,\n            bankguard: appsResult.apps.bankguard,\n            bruteforce: appsResult.apps.bruteforce,\n            sdk: appsResult.apps.sdk,\n            firewall: appsResult.apps.firewall,\n            malwarekit: appsResult.apps.malwarekit,\n            proxyvpn: appsResult.apps.proxyvpn\n          },\n          isOnline: true,\n          isLoading: false\n        });\n\n        // Carregar notificações\n        await get().loadNotifications();\n        return true;\n      }\n      set({\n        error: 'Erro ao carregar jogador',\n        isLoading: false\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message,\n        isLoading: false\n      });\n      return false;\n    }\n  },\n  updatePlayerApps: async apps => {\n    const state = get();\n    if (!state.player) return false;\n    try {\n      const result = await gameService.updatePlayerApps(state.player.id, apps);\n      if (result.success) {\n        set(state => ({\n          playerApps: {\n            ...state.playerApps,\n            ...apps\n          }\n        }));\n        return true;\n      }\n      set({\n        error: result.error || 'Erro ao atualizar apps'\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message\n      });\n      return false;\n    }\n  },\n  upgradeApp: async appId => {\n    const state = get();\n    const {\n      player,\n      playerApps\n    } = state;\n    if (!player) return false;\n    const currentLevel = playerApps[appId];\n    const upgradeCost = calculateUpgradeCost(currentLevel);\n\n    // Verificar se tem dinheiro suficiente\n    if (player.cash < upgradeCost) {\n      set({\n        error: 'Dinheiro insuficiente para upgrade!'\n      });\n      return false;\n    }\n\n    // Verificar se não atingiu o nível máximo\n    if (currentLevel >= GAME_CONFIG.MAX_APP_LEVEL) {\n      set({\n        error: 'App já está no nível máximo!'\n      });\n      return false;\n    }\n    try {\n      // Realizar upgrade no servidor\n      const newLevel = currentLevel + 1;\n      const newCash = player.cash - upgradeCost;\n      const xpReward = calculateXpReward(currentLevel);\n      const newXp = player.xp + xpReward;\n      const newPlayerLevel = getPlayerLevel(newXp);\n\n      // Atualizar apps\n      const appsUpdate = {\n        [appId]: newLevel\n      };\n      const appsResult = await gameService.updatePlayerApps(player.id, appsUpdate);\n\n      // Atualizar player\n      const playerUpdate = {\n        cash: newCash,\n        xp: newXp,\n        level: newPlayerLevel\n      };\n      const playerResult = await gameService.updatePlayer(player.id, playerUpdate);\n      if (appsResult.success && playerResult.success) {\n        // Atualizar estado local\n        set(state => ({\n          player: {\n            ...state.player,\n            cash: newCash,\n            xp: newXp,\n            level: newPlayerLevel\n          },\n          playerApps: {\n            ...state.playerApps,\n            [appId]: newLevel\n          },\n          error: null\n        }));\n\n        // Adicionar notificação\n        await get().addNotification({\n          type: 'upgrade',\n          title: 'Upgrade Concluído!',\n          message: `${appId} foi atualizado para nível ${newLevel}. +${xpReward} XP`\n        });\n\n        // Verificar se subiu de nível\n        if (newPlayerLevel > player.level) {\n          await get().addNotification({\n            type: 'level',\n            title: 'Level Up!',\n            message: `Parabéns! Você atingiu o nível ${newPlayerLevel}!`\n          });\n        }\n        return true;\n      }\n      set({\n        error: 'Erro ao realizar upgrade'\n      });\n      return false;\n    } catch (error) {\n      set({\n        error: error.message\n      });\n      return false;\n    }\n  },\n  addXp: amount => {\n    set(state => {\n      if (!state.player) return state;\n      const newTotalXp = state.player.xp + amount;\n      const newLevel = getPlayerLevel(newTotalXp);\n      const xpToNext = getXpForNextLevel(newTotalXp);\n      const leveledUp = newLevel > state.player.level;\n      const updatedPlayer = {\n        ...state.player,\n        xp: newTotalXp,\n        level: newLevel,\n        xpToNextLevel: xpToNext\n      };\n\n      // Se subiu de nível, adicionar notificação\n      if (leveledUp) {\n        setTimeout(() => {\n          get().addNotification({\n            type: 'level',\n            title: 'Level Up!',\n            message: `Parabéns! Você atingiu o nível ${newLevel}!`,\n            read: false\n          });\n        }, 100);\n      }\n      return {\n        player: updatedPlayer\n      };\n    });\n  },\n  addCash: amount => {\n    set(state => ({\n      player: state.player ? {\n        ...state.player,\n        cash: state.player.cash + amount\n      } : null\n    }));\n  },\n  spendCash: amount => {\n    const state = get();\n    if (!state.player || state.player.cash < amount) {\n      return false;\n    }\n    set(state => ({\n      player: state.player ? {\n        ...state.player,\n        cash: state.player.cash - amount\n      } : null\n    }));\n    return true;\n  },\n  setCurrentScreen: screen => {\n    set({\n      currentScreen: screen\n    });\n  },\n  addNotification: notification => {\n    const newNotification = {\n      ...notification,\n      id: `notif_${Date.now()}_${Math.random()}`,\n      timestamp: new Date().toISOString()\n    };\n    set(state => ({\n      notifications: [newNotification, ...state.notifications].slice(0, 50) // Manter apenas 50 notificações\n    }));\n  },\n  markNotificationRead: id => {\n    set(state => ({\n      notifications: state.notifications.map(notif => notif.id === id ? {\n        ...notif,\n        read: true\n      } : notif)\n    }));\n  },\n  clearNotifications: () => {\n    set({\n      notifications: []\n    });\n  },\n  setError: error => {\n    set({\n      error\n    });\n    if (error) {\n      // Limpar erro após 5 segundos\n      setTimeout(() => {\n        set({\n          error: null\n        });\n      }, 5000);\n    }\n  },\n  setLoading: loading => {\n    set({\n      isLoading: loading\n    });\n  },\n  reset: () => {\n    set({\n      player: null,\n      playerApps: initialPlayerApps,\n      isOnline: false,\n      notifications: [],\n      availableTargets: [],\n      hackHistory: [],\n      currentScreen: 'home',\n      isLoading: false,\n      error: null\n    });\n  }\n}), {\n  name: 'shack-game-storage',\n  partialize: state => ({\n    player: state.player,\n    playerApps: state.playerApps,\n    notifications: state.notifications,\n    hackHistory: state.hackHistory\n  })\n}));", "map": {"version": 3, "names": ["create", "persist", "GAME_CONFIG", "getPlayerLevel", "getXpForNextLevel", "calculateUpgradeCost", "calculateXpReward", "gameService", "initialPlayerApps", "INITIAL_APPS", "useHackGameStore", "set", "get", "player", "playerApps", "isOnline", "notifications", "availableTargets", "hack<PERSON><PERSON><PERSON>", "currentScreen", "isLoading", "error", "initializePlayer", "nick", "email", "result", "createPlayer", "success", "appsResult", "getPlayerApps", "id", "apps", "antivirus", "bankguard", "bruteforce", "sdk", "firewall", "malwarekit", "proxyvpn", "loadNotifications", "message", "loadPlayer", "playerId", "player<PERSON><PERSON><PERSON>", "Promise", "all", "getPlayer", "updatePlayerApps", "state", "upgradeApp", "appId", "currentLevel", "upgradeCost", "cash", "MAX_APP_LEVEL", "newLevel", "newCash", "xpReward", "newXp", "xp", "newPlayerLevel", "appsUpdate", "playerUpdate", "level", "updatePlayer", "addNotification", "type", "title", "addXp", "amount", "newTotalXp", "xpToNext", "leveledUp", "updatedPlayer", "xpToNextLevel", "setTimeout", "read", "addCash", "spendCash", "setCurrentScreen", "screen", "notification", "newNotification", "Date", "now", "Math", "random", "timestamp", "toISOString", "slice", "markNotificationRead", "map", "notif", "clearNotifications", "setError", "setLoading", "loading", "reset", "name", "partialize"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/hackGameStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport {\n  GAME_CONFIG,\n  getPlayerLevel,\n  getXpForNextLevel,\n  calculateUpgradeCost,\n  calculateXpReward\n} from '../types/game';\nimport { gameService } from '../services/gameService';\nimport { Player, PlayerApps, GameNotification, HackTarget } from '../lib/supabase';\n\ninterface GameState {\n  // Player data\n  player: Player | null;\n  playerApps: PlayerApps;\n  \n  // Game state\n  isOnline: boolean;\n  notifications: GameNotification[];\n  \n  // Targets and hacking\n  availableTargets: HackTarget[];\n  hackHistory: any[];\n  \n  // UI state\n  currentScreen: 'home' | 'apps' | 'scanner' | 'terminal' | 'profile' | 'settings' | 'shop' | 'ranking' | 'logs' | 'chat';\n  isLoading: boolean;\n  error: string | null;\n  \n  // Actions\n  initializePlayer: (nick: string, email: string) => Promise<boolean>;\n  loadPlayer: (playerId: string) => Promise<boolean>;\n  updatePlayerApps: (apps: Partial<PlayerApps>) => Promise<boolean>;\n  upgradeApp: (appId: keyof PlayerApps) => Promise<boolean>;\n  addXp: (amount: number) => Promise<void>;\n  addCash: (amount: number) => Promise<void>;\n  spendCash: (amount: number) => Promise<boolean>;\n  setCurrentScreen: (screen: GameState['currentScreen']) => void;\n  loadNotifications: () => Promise<void>;\n  addNotification: (notification: Omit<GameNotification, 'id' | 'player_id' | 'created_at' | 'read'>) => Promise<void>;\n  markNotificationRead: (id: string) => Promise<void>;\n  clearNotifications: () => void;\n  setError: (error: string | null) => void;\n  setLoading: (loading: boolean) => void;\n  syncWithServer: () => Promise<void>;\n  reset: () => void;\n}\n\nconst initialPlayerApps: PlayerApps = { ...GAME_CONFIG.INITIAL_APPS };\n\nexport const useHackGameStore = create<GameState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      player: null,\n      playerApps: initialPlayerApps,\n      isOnline: false,\n      notifications: [],\n      availableTargets: [],\n      hackHistory: [],\n      currentScreen: 'home',\n      isLoading: false,\n      error: null,\n\n      // Actions\n      initializePlayer: async (nick: string, email: string) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const result = await gameService.createPlayer(nick, email);\n\n          if (result.success && result.player) {\n            const appsResult = await gameService.getPlayerApps(result.player.id);\n\n            if (appsResult.success && appsResult.apps) {\n              set({\n                player: result.player,\n                playerApps: {\n                  antivirus: appsResult.apps.antivirus,\n                  bankguard: appsResult.apps.bankguard,\n                  bruteforce: appsResult.apps.bruteforce,\n                  sdk: appsResult.apps.sdk,\n                  firewall: appsResult.apps.firewall,\n                  malwarekit: appsResult.apps.malwarekit,\n                  proxyvpn: appsResult.apps.proxyvpn,\n                },\n                isOnline: true,\n                isLoading: false,\n              });\n\n              // Carregar notificações\n              await get().loadNotifications();\n              return true;\n            }\n          }\n\n          set({ error: result.error || 'Erro ao criar jogador', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      loadPlayer: async (playerId: string) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const [playerResult, appsResult] = await Promise.all([\n            gameService.getPlayer(playerId),\n            gameService.getPlayerApps(playerId)\n          ]);\n\n          if (playerResult.success && playerResult.player && appsResult.success && appsResult.apps) {\n            set({\n              player: playerResult.player,\n              playerApps: {\n                antivirus: appsResult.apps.antivirus,\n                bankguard: appsResult.apps.bankguard,\n                bruteforce: appsResult.apps.bruteforce,\n                sdk: appsResult.apps.sdk,\n                firewall: appsResult.apps.firewall,\n                malwarekit: appsResult.apps.malwarekit,\n                proxyvpn: appsResult.apps.proxyvpn,\n              },\n              isOnline: true,\n              isLoading: false,\n            });\n\n            // Carregar notificações\n            await get().loadNotifications();\n            return true;\n          }\n\n          set({ error: 'Erro ao carregar jogador', isLoading: false });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message, isLoading: false });\n          return false;\n        }\n      },\n\n      updatePlayerApps: async (apps: Partial<PlayerApps>) => {\n        const state = get();\n        if (!state.player) return false;\n\n        try {\n          const result = await gameService.updatePlayerApps(state.player.id, apps);\n\n          if (result.success) {\n            set((state) => ({\n              playerApps: { ...state.playerApps, ...apps }\n            }));\n            return true;\n          }\n\n          set({ error: result.error || 'Erro ao atualizar apps' });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message });\n          return false;\n        }\n      },\n\n      upgradeApp: async (appId: keyof PlayerApps) => {\n        const state = get();\n        const { player, playerApps } = state;\n\n        if (!player) return false;\n\n        const currentLevel = playerApps[appId];\n        const upgradeCost = calculateUpgradeCost(currentLevel);\n\n        // Verificar se tem dinheiro suficiente\n        if (player.cash < upgradeCost) {\n          set({ error: 'Dinheiro insuficiente para upgrade!' });\n          return false;\n        }\n\n        // Verificar se não atingiu o nível máximo\n        if (currentLevel >= GAME_CONFIG.MAX_APP_LEVEL) {\n          set({ error: 'App já está no nível máximo!' });\n          return false;\n        }\n\n        try {\n          // Realizar upgrade no servidor\n          const newLevel = currentLevel + 1;\n          const newCash = player.cash - upgradeCost;\n          const xpReward = calculateXpReward(currentLevel);\n          const newXp = player.xp + xpReward;\n          const newPlayerLevel = getPlayerLevel(newXp);\n\n          // Atualizar apps\n          const appsUpdate = { [appId]: newLevel };\n          const appsResult = await gameService.updatePlayerApps(player.id, appsUpdate);\n\n          // Atualizar player\n          const playerUpdate = {\n            cash: newCash,\n            xp: newXp,\n            level: newPlayerLevel\n          };\n          const playerResult = await gameService.updatePlayer(player.id, playerUpdate);\n\n          if (appsResult.success && playerResult.success) {\n            // Atualizar estado local\n            set((state) => ({\n              player: {\n                ...state.player!,\n                cash: newCash,\n                xp: newXp,\n                level: newPlayerLevel,\n              },\n              playerApps: {\n                ...state.playerApps,\n                [appId]: newLevel,\n              },\n              error: null,\n            }));\n\n            // Adicionar notificação\n            await get().addNotification({\n              type: 'upgrade',\n              title: 'Upgrade Concluído!',\n              message: `${appId} foi atualizado para nível ${newLevel}. +${xpReward} XP`,\n            });\n\n            // Verificar se subiu de nível\n            if (newPlayerLevel > player.level) {\n              await get().addNotification({\n                type: 'level',\n                title: 'Level Up!',\n                message: `Parabéns! Você atingiu o nível ${newPlayerLevel}!`,\n              });\n            }\n\n            return true;\n          }\n\n          set({ error: 'Erro ao realizar upgrade' });\n          return false;\n        } catch (error: any) {\n          set({ error: error.message });\n          return false;\n        }\n      },\n\n      addXp: (amount: number) => {\n        set((state) => {\n          if (!state.player) return state;\n\n          const newTotalXp = state.player.xp + amount;\n          const newLevel = getPlayerLevel(newTotalXp);\n          const xpToNext = getXpForNextLevel(newTotalXp);\n          \n          const leveledUp = newLevel > state.player.level;\n          \n          const updatedPlayer = {\n            ...state.player,\n            xp: newTotalXp,\n            level: newLevel,\n            xpToNextLevel: xpToNext,\n          };\n\n          // Se subiu de nível, adicionar notificação\n          if (leveledUp) {\n            setTimeout(() => {\n              get().addNotification({\n                type: 'level',\n                title: 'Level Up!',\n                message: `Parabéns! Você atingiu o nível ${newLevel}!`,\n                read: false,\n              });\n            }, 100);\n          }\n\n          return { player: updatedPlayer };\n        });\n      },\n\n      addCash: (amount: number) => {\n        set((state) => ({\n          player: state.player ? {\n            ...state.player,\n            cash: state.player.cash + amount\n          } : null\n        }));\n      },\n\n      spendCash: (amount: number) => {\n        const state = get();\n        if (!state.player || state.player.cash < amount) {\n          return false;\n        }\n\n        set((state) => ({\n          player: state.player ? {\n            ...state.player,\n            cash: state.player.cash - amount\n          } : null\n        }));\n\n        return true;\n      },\n\n      setCurrentScreen: (screen: GameState['currentScreen']) => {\n        set({ currentScreen: screen });\n      },\n\n      addNotification: (notification: Omit<GameNotification, 'id' | 'timestamp'>) => {\n        const newNotification: GameNotification = {\n          ...notification,\n          id: `notif_${Date.now()}_${Math.random()}`,\n          timestamp: new Date().toISOString(),\n        };\n\n        set((state) => ({\n          notifications: [newNotification, ...state.notifications].slice(0, 50) // Manter apenas 50 notificações\n        }));\n      },\n\n      markNotificationRead: (id: string) => {\n        set((state) => ({\n          notifications: state.notifications.map(notif =>\n            notif.id === id ? { ...notif, read: true } : notif\n          )\n        }));\n      },\n\n      clearNotifications: () => {\n        set({ notifications: [] });\n      },\n\n      setError: (error: string | null) => {\n        set({ error });\n        if (error) {\n          // Limpar erro após 5 segundos\n          setTimeout(() => {\n            set({ error: null });\n          }, 5000);\n        }\n      },\n\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n\n      reset: () => {\n        set({\n          player: null,\n          playerApps: initialPlayerApps,\n          isOnline: false,\n          notifications: [],\n          availableTargets: [],\n          hackHistory: [],\n          currentScreen: 'home',\n          isLoading: false,\n          error: null,\n        });\n      },\n    }),\n    {\n      name: 'shack-game-storage',\n      partialize: (state) => ({\n        player: state.player,\n        playerApps: state.playerApps,\n        notifications: state.notifications,\n        hackHistory: state.hackHistory,\n      }),\n    }\n  )\n);\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SACEC,WAAW,EACXC,cAAc,EACdC,iBAAiB,EACjBC,oBAAoB,EACpBC,iBAAiB,QACZ,eAAe;AACtB,SAASC,WAAW,QAAQ,yBAAyB;AAwCrD,MAAMC,iBAA6B,GAAG;EAAE,GAAGN,WAAW,CAACO;AAAa,CAAC;AAErE,OAAO,MAAMC,gBAAgB,GAAGV,MAAM,CAAY,CAAC,CACjDC,OAAO,CACL,CAACU,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAEN,iBAAiB;EAC7BO,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE,EAAE;EACjBC,gBAAgB,EAAE,EAAE;EACpBC,WAAW,EAAE,EAAE;EACfC,aAAa,EAAE,MAAM;EACrBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEX;EACAC,gBAAgB,EAAE,MAAAA,CAAOC,IAAY,EAAEC,KAAa,KAAK;IACvDb,GAAG,CAAC;MAAES,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMlB,WAAW,CAACmB,YAAY,CAACH,IAAI,EAAEC,KAAK,CAAC;MAE1D,IAAIC,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACZ,MAAM,EAAE;QACnC,MAAMe,UAAU,GAAG,MAAMrB,WAAW,CAACsB,aAAa,CAACJ,MAAM,CAACZ,MAAM,CAACiB,EAAE,CAAC;QAEpE,IAAIF,UAAU,CAACD,OAAO,IAAIC,UAAU,CAACG,IAAI,EAAE;UACzCpB,GAAG,CAAC;YACFE,MAAM,EAAEY,MAAM,CAACZ,MAAM;YACrBC,UAAU,EAAE;cACVkB,SAAS,EAAEJ,UAAU,CAACG,IAAI,CAACC,SAAS;cACpCC,SAAS,EAAEL,UAAU,CAACG,IAAI,CAACE,SAAS;cACpCC,UAAU,EAAEN,UAAU,CAACG,IAAI,CAACG,UAAU;cACtCC,GAAG,EAAEP,UAAU,CAACG,IAAI,CAACI,GAAG;cACxBC,QAAQ,EAAER,UAAU,CAACG,IAAI,CAACK,QAAQ;cAClCC,UAAU,EAAET,UAAU,CAACG,IAAI,CAACM,UAAU;cACtCC,QAAQ,EAAEV,UAAU,CAACG,IAAI,CAACO;YAC5B,CAAC;YACDvB,QAAQ,EAAE,IAAI;YACdK,SAAS,EAAE;UACb,CAAC,CAAC;;UAEF;UACA,MAAMR,GAAG,CAAC,CAAC,CAAC2B,iBAAiB,CAAC,CAAC;UAC/B,OAAO,IAAI;QACb;MACF;MAEA5B,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI,uBAAuB;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MACzE,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACmB,OAAO;QAAEpB,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAEDqB,UAAU,EAAE,MAAOC,QAAgB,IAAK;IACtC/B,GAAG,CAAC;MAAES,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACF,MAAM,CAACsB,YAAY,EAAEf,UAAU,CAAC,GAAG,MAAMgB,OAAO,CAACC,GAAG,CAAC,CACnDtC,WAAW,CAACuC,SAAS,CAACJ,QAAQ,CAAC,EAC/BnC,WAAW,CAACsB,aAAa,CAACa,QAAQ,CAAC,CACpC,CAAC;MAEF,IAAIC,YAAY,CAAChB,OAAO,IAAIgB,YAAY,CAAC9B,MAAM,IAAIe,UAAU,CAACD,OAAO,IAAIC,UAAU,CAACG,IAAI,EAAE;QACxFpB,GAAG,CAAC;UACFE,MAAM,EAAE8B,YAAY,CAAC9B,MAAM;UAC3BC,UAAU,EAAE;YACVkB,SAAS,EAAEJ,UAAU,CAACG,IAAI,CAACC,SAAS;YACpCC,SAAS,EAAEL,UAAU,CAACG,IAAI,CAACE,SAAS;YACpCC,UAAU,EAAEN,UAAU,CAACG,IAAI,CAACG,UAAU;YACtCC,GAAG,EAAEP,UAAU,CAACG,IAAI,CAACI,GAAG;YACxBC,QAAQ,EAAER,UAAU,CAACG,IAAI,CAACK,QAAQ;YAClCC,UAAU,EAAET,UAAU,CAACG,IAAI,CAACM,UAAU;YACtCC,QAAQ,EAAEV,UAAU,CAACG,IAAI,CAACO;UAC5B,CAAC;UACDvB,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;QACb,CAAC,CAAC;;QAEF;QACA,MAAMR,GAAG,CAAC,CAAC,CAAC2B,iBAAiB,CAAC,CAAC;QAC/B,OAAO,IAAI;MACb;MAEA5B,GAAG,CAAC;QAAEU,KAAK,EAAE,0BAA0B;QAAED,SAAS,EAAE;MAAM,CAAC,CAAC;MAC5D,OAAO,KAAK;IACd,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACmB,OAAO;QAAEpB,SAAS,EAAE;MAAM,CAAC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;EAED2B,gBAAgB,EAAE,MAAOhB,IAAyB,IAAK;IACrD,MAAMiB,KAAK,GAAGpC,GAAG,CAAC,CAAC;IACnB,IAAI,CAACoC,KAAK,CAACnC,MAAM,EAAE,OAAO,KAAK;IAE/B,IAAI;MACF,MAAMY,MAAM,GAAG,MAAMlB,WAAW,CAACwC,gBAAgB,CAACC,KAAK,CAACnC,MAAM,CAACiB,EAAE,EAAEC,IAAI,CAAC;MAExE,IAAIN,MAAM,CAACE,OAAO,EAAE;QAClBhB,GAAG,CAAEqC,KAAK,KAAM;UACdlC,UAAU,EAAE;YAAE,GAAGkC,KAAK,CAAClC,UAAU;YAAE,GAAGiB;UAAK;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,IAAI;MACb;MAEApB,GAAG,CAAC;QAAEU,KAAK,EAAEI,MAAM,CAACJ,KAAK,IAAI;MAAyB,CAAC,CAAC;MACxD,OAAO,KAAK;IACd,CAAC,CAAC,OAAOA,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC,CAAC;MAC7B,OAAO,KAAK;IACd;EACF,CAAC;EAEDS,UAAU,EAAE,MAAOC,KAAuB,IAAK;IAC7C,MAAMF,KAAK,GAAGpC,GAAG,CAAC,CAAC;IACnB,MAAM;MAAEC,MAAM;MAAEC;IAAW,CAAC,GAAGkC,KAAK;IAEpC,IAAI,CAACnC,MAAM,EAAE,OAAO,KAAK;IAEzB,MAAMsC,YAAY,GAAGrC,UAAU,CAACoC,KAAK,CAAC;IACtC,MAAME,WAAW,GAAG/C,oBAAoB,CAAC8C,YAAY,CAAC;;IAEtD;IACA,IAAItC,MAAM,CAACwC,IAAI,GAAGD,WAAW,EAAE;MAC7BzC,GAAG,CAAC;QAAEU,KAAK,EAAE;MAAsC,CAAC,CAAC;MACrD,OAAO,KAAK;IACd;;IAEA;IACA,IAAI8B,YAAY,IAAIjD,WAAW,CAACoD,aAAa,EAAE;MAC7C3C,GAAG,CAAC;QAAEU,KAAK,EAAE;MAA+B,CAAC,CAAC;MAC9C,OAAO,KAAK;IACd;IAEA,IAAI;MACF;MACA,MAAMkC,QAAQ,GAAGJ,YAAY,GAAG,CAAC;MACjC,MAAMK,OAAO,GAAG3C,MAAM,CAACwC,IAAI,GAAGD,WAAW;MACzC,MAAMK,QAAQ,GAAGnD,iBAAiB,CAAC6C,YAAY,CAAC;MAChD,MAAMO,KAAK,GAAG7C,MAAM,CAAC8C,EAAE,GAAGF,QAAQ;MAClC,MAAMG,cAAc,GAAGzD,cAAc,CAACuD,KAAK,CAAC;;MAE5C;MACA,MAAMG,UAAU,GAAG;QAAE,CAACX,KAAK,GAAGK;MAAS,CAAC;MACxC,MAAM3B,UAAU,GAAG,MAAMrB,WAAW,CAACwC,gBAAgB,CAAClC,MAAM,CAACiB,EAAE,EAAE+B,UAAU,CAAC;;MAE5E;MACA,MAAMC,YAAY,GAAG;QACnBT,IAAI,EAAEG,OAAO;QACbG,EAAE,EAAED,KAAK;QACTK,KAAK,EAAEH;MACT,CAAC;MACD,MAAMjB,YAAY,GAAG,MAAMpC,WAAW,CAACyD,YAAY,CAACnD,MAAM,CAACiB,EAAE,EAAEgC,YAAY,CAAC;MAE5E,IAAIlC,UAAU,CAACD,OAAO,IAAIgB,YAAY,CAAChB,OAAO,EAAE;QAC9C;QACAhB,GAAG,CAAEqC,KAAK,KAAM;UACdnC,MAAM,EAAE;YACN,GAAGmC,KAAK,CAACnC,MAAO;YAChBwC,IAAI,EAAEG,OAAO;YACbG,EAAE,EAAED,KAAK;YACTK,KAAK,EAAEH;UACT,CAAC;UACD9C,UAAU,EAAE;YACV,GAAGkC,KAAK,CAAClC,UAAU;YACnB,CAACoC,KAAK,GAAGK;UACX,CAAC;UACDlC,KAAK,EAAE;QACT,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMT,GAAG,CAAC,CAAC,CAACqD,eAAe,CAAC;UAC1BC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,oBAAoB;UAC3B3B,OAAO,EAAE,GAAGU,KAAK,8BAA8BK,QAAQ,MAAME,QAAQ;QACvE,CAAC,CAAC;;QAEF;QACA,IAAIG,cAAc,GAAG/C,MAAM,CAACkD,KAAK,EAAE;UACjC,MAAMnD,GAAG,CAAC,CAAC,CAACqD,eAAe,CAAC;YAC1BC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,WAAW;YAClB3B,OAAO,EAAE,kCAAkCoB,cAAc;UAC3D,CAAC,CAAC;QACJ;QAEA,OAAO,IAAI;MACb;MAEAjD,GAAG,CAAC;QAAEU,KAAK,EAAE;MAA2B,CAAC,CAAC;MAC1C,OAAO,KAAK;IACd,CAAC,CAAC,OAAOA,KAAU,EAAE;MACnBV,GAAG,CAAC;QAAEU,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC,CAAC;MAC7B,OAAO,KAAK;IACd;EACF,CAAC;EAED4B,KAAK,EAAGC,MAAc,IAAK;IACzB1D,GAAG,CAAEqC,KAAK,IAAK;MACb,IAAI,CAACA,KAAK,CAACnC,MAAM,EAAE,OAAOmC,KAAK;MAE/B,MAAMsB,UAAU,GAAGtB,KAAK,CAACnC,MAAM,CAAC8C,EAAE,GAAGU,MAAM;MAC3C,MAAMd,QAAQ,GAAGpD,cAAc,CAACmE,UAAU,CAAC;MAC3C,MAAMC,QAAQ,GAAGnE,iBAAiB,CAACkE,UAAU,CAAC;MAE9C,MAAME,SAAS,GAAGjB,QAAQ,GAAGP,KAAK,CAACnC,MAAM,CAACkD,KAAK;MAE/C,MAAMU,aAAa,GAAG;QACpB,GAAGzB,KAAK,CAACnC,MAAM;QACf8C,EAAE,EAAEW,UAAU;QACdP,KAAK,EAAER,QAAQ;QACfmB,aAAa,EAAEH;MACjB,CAAC;;MAED;MACA,IAAIC,SAAS,EAAE;QACbG,UAAU,CAAC,MAAM;UACf/D,GAAG,CAAC,CAAC,CAACqD,eAAe,CAAC;YACpBC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,WAAW;YAClB3B,OAAO,EAAE,kCAAkCe,QAAQ,GAAG;YACtDqB,IAAI,EAAE;UACR,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT;MAEA,OAAO;QAAE/D,MAAM,EAAE4D;MAAc,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EAEDI,OAAO,EAAGR,MAAc,IAAK;IAC3B1D,GAAG,CAAEqC,KAAK,KAAM;MACdnC,MAAM,EAAEmC,KAAK,CAACnC,MAAM,GAAG;QACrB,GAAGmC,KAAK,CAACnC,MAAM;QACfwC,IAAI,EAAEL,KAAK,CAACnC,MAAM,CAACwC,IAAI,GAAGgB;MAC5B,CAAC,GAAG;IACN,CAAC,CAAC,CAAC;EACL,CAAC;EAEDS,SAAS,EAAGT,MAAc,IAAK;IAC7B,MAAMrB,KAAK,GAAGpC,GAAG,CAAC,CAAC;IACnB,IAAI,CAACoC,KAAK,CAACnC,MAAM,IAAImC,KAAK,CAACnC,MAAM,CAACwC,IAAI,GAAGgB,MAAM,EAAE;MAC/C,OAAO,KAAK;IACd;IAEA1D,GAAG,CAAEqC,KAAK,KAAM;MACdnC,MAAM,EAAEmC,KAAK,CAACnC,MAAM,GAAG;QACrB,GAAGmC,KAAK,CAACnC,MAAM;QACfwC,IAAI,EAAEL,KAAK,CAACnC,MAAM,CAACwC,IAAI,GAAGgB;MAC5B,CAAC,GAAG;IACN,CAAC,CAAC,CAAC;IAEH,OAAO,IAAI;EACb,CAAC;EAEDU,gBAAgB,EAAGC,MAAkC,IAAK;IACxDrE,GAAG,CAAC;MAAEQ,aAAa,EAAE6D;IAAO,CAAC,CAAC;EAChC,CAAC;EAEDf,eAAe,EAAGgB,YAAwD,IAAK;IAC7E,MAAMC,eAAiC,GAAG;MACxC,GAAGD,YAAY;MACfnD,EAAE,EAAE,SAASqD,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;MAC1CC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACpC,CAAC;IAED7E,GAAG,CAAEqC,KAAK,KAAM;MACdhC,aAAa,EAAE,CAACkE,eAAe,EAAE,GAAGlC,KAAK,CAAChC,aAAa,CAAC,CAACyE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;EACL,CAAC;EAEDC,oBAAoB,EAAG5D,EAAU,IAAK;IACpCnB,GAAG,CAAEqC,KAAK,KAAM;MACdhC,aAAa,EAAEgC,KAAK,CAAChC,aAAa,CAAC2E,GAAG,CAACC,KAAK,IAC1CA,KAAK,CAAC9D,EAAE,KAAKA,EAAE,GAAG;QAAE,GAAG8D,KAAK;QAAEhB,IAAI,EAAE;MAAK,CAAC,GAAGgB,KAC/C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAEDC,kBAAkB,EAAEA,CAAA,KAAM;IACxBlF,GAAG,CAAC;MAAEK,aAAa,EAAE;IAAG,CAAC,CAAC;EAC5B,CAAC;EAED8E,QAAQ,EAAGzE,KAAoB,IAAK;IAClCV,GAAG,CAAC;MAAEU;IAAM,CAAC,CAAC;IACd,IAAIA,KAAK,EAAE;MACT;MACAsD,UAAU,CAAC,MAAM;QACfhE,GAAG,CAAC;UAAEU,KAAK,EAAE;QAAK,CAAC,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED0E,UAAU,EAAGC,OAAgB,IAAK;IAChCrF,GAAG,CAAC;MAAES,SAAS,EAAE4E;IAAQ,CAAC,CAAC;EAC7B,CAAC;EAEDC,KAAK,EAAEA,CAAA,KAAM;IACXtF,GAAG,CAAC;MACFE,MAAM,EAAE,IAAI;MACZC,UAAU,EAAEN,iBAAiB;MAC7BO,QAAQ,EAAE,KAAK;MACfC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,EAAE;MACpBC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,EACF;EACE6E,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAGnD,KAAK,KAAM;IACtBnC,MAAM,EAAEmC,KAAK,CAACnC,MAAM;IACpBC,UAAU,EAAEkC,KAAK,CAAClC,UAAU;IAC5BE,aAAa,EAAEgC,KAAK,CAAChC,aAAa;IAClCE,WAAW,EAAE8B,KAAK,CAAC9B;EACrB,CAAC;AACH,CACF,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}