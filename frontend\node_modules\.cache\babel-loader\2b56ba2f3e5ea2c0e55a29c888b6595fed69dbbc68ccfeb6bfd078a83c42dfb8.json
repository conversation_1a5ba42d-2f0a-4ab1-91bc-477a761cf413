{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../../stores/authStore';\nimport LoadingSpinner from '../common/LoadingSpinner';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AuthGuard = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading,\n    checkAuth,\n    user\n  } = useAuth();\n  useEffect(() => {\n    console.log('AuthGuard - Estado:', {\n      isAuthenticated,\n      isLoading,\n      user: !!user\n    });\n\n    // Verificar autenticação ao montar o componente\n    if (!isAuthenticated && !isLoading) {\n      console.log('AuthGuard - Verificando autenticação...');\n      checkAuth();\n    }\n  }, [isAuthenticated, isLoading, checkAuth]);\n\n  // Mostrar loading enquanto verifica autenticação\n  if (isLoading) {\n    console.log('AuthGuard - Mostrando loading...');\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      fullScreen: true,\n      size: \"lg\",\n      text: \"Verificando autentica\\xE7\\xE3o...\",\n      color: \"primary\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirecionar para login se não autenticado\n  if (!isAuthenticated || !user) {\n    console.log('AuthGuard - Redirecionando para login...');\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Renderizar children se autenticado\n  console.log('AuthGuard - Usuário autenticado, renderizando children...');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(AuthGuard, \"3YTpPOPccDsdZ1SjNoBjupFw90I=\", false, function () {\n  return [useAuth];\n});\n_c = AuthGuard;\nexport default AuthGuard;\nvar _c;\n$RefreshReg$(_c, \"AuthGuard\");", "map": {"version": 3, "names": ["React", "useEffect", "Navigate", "useAuth", "LoadingSpinner", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON>", "children", "_s", "isAuthenticated", "isLoading", "checkAuth", "user", "console", "log", "fullScreen", "size", "text", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/auth/AuthGuard.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../../stores/authStore';\nimport LoadingSpinner from '../common/LoadingSpinner';\n\ninterface AuthGuardProps {\n  children: React.ReactNode;\n}\n\nconst AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {\n  const { isAuthenticated, isLoading, checkAuth, user } = useAuth();\n\n  useEffect(() => {\n    console.log('AuthGuard - Estado:', { isAuthenticated, isLoading, user: !!user });\n\n    // Verificar autenticação ao montar o componente\n    if (!isAuthenticated && !isLoading) {\n      console.log('AuthGuard - Verificando autenticação...');\n      checkAuth();\n    }\n  }, [isAuthenticated, isLoading, checkAuth]);\n\n  // Mostrar loading enquanto verifica autenticação\n  if (isLoading) {\n    console.log('AuthGuard - Mostrando loading...');\n    return (\n      <LoadingSpinner\n        fullScreen\n        size=\"lg\"\n        text=\"Verificando autenticação...\"\n        color=\"primary\"\n      />\n    );\n  }\n\n  // Redirecionar para login se não autenticado\n  if (!isAuthenticated || !user) {\n    console.log('AuthGuard - Redirecionando para login...');\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // Renderizar children se autenticado\n  console.log('AuthGuard - Usuário autenticado, renderizando children...');\n  return <>{children}</>;\n};\n\nexport default AuthGuard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,cAAc,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMtD,MAAMC,SAAmC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAM;IAAEC,eAAe;IAAEC,SAAS;IAAEC,SAAS;IAAEC;EAAK,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAEjEF,SAAS,CAAC,MAAM;IACde,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MAAEL,eAAe;MAAEC,SAAS;MAAEE,IAAI,EAAE,CAAC,CAACA;IAAK,CAAC,CAAC;;IAEhF;IACA,IAAI,CAACH,eAAe,IAAI,CAACC,SAAS,EAAE;MAClCG,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDH,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACF,eAAe,EAAEC,SAAS,EAAEC,SAAS,CAAC,CAAC;;EAE3C;EACA,IAAID,SAAS,EAAE;IACbG,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,oBACEX,OAAA,CAACF,cAAc;MACbc,UAAU;MACVC,IAAI,EAAC,IAAI;MACTC,IAAI,EAAC,mCAA6B;MAClCC,KAAK,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAEN;;EAEA;EACA,IAAI,CAACb,eAAe,IAAI,CAACG,IAAI,EAAE;IAC7BC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvD,oBAAOX,OAAA,CAACJ,QAAQ;MAACwB,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;;EAEA;EACAT,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;EACxE,oBAAOX,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACC,EAAA,CAnCIF,SAAmC;EAAA,QACiBN,OAAO;AAAA;AAAAyB,EAAA,GAD3DnB,SAAmC;AAqCzC,eAAeA,SAAS;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}