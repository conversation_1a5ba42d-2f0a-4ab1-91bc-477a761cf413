"""
Script para exportar dados do Firestore para migração para Supabase
Execute: python migration/export_firestore_data.py
"""

import firebase_admin
from firebase_admin import credentials, firestore
import json
import os
from datetime import datetime

def init_firestore():
    """Inicializa conexão com Firestore usando a mesma configuração do game/models.py"""
    try:
        # O caminho padrão onde o Render coloca os Secret Files
        secret_path_on_render = '/etc/secrets/firebase-credentials.json'

        # Verifica se está rodando no Render
        if os.path.exists(secret_path_on_render):
            cred = credentials.Certificate(secret_path_on_render)
            print("✅ Usando credenciais do Render")
        else:
            # Se não, assume que está rodando no seu PC
            local_path = r"C:\Users\<USER>\OneDrive\Área de Trabalho\SHACK 1.0\firebase-credentials.json"
            if os.path.exists(local_path):
                cred = credentials.Certificate(local_path)
                print("✅ Usando credenciais locais")
            else:
                print("❌ Nenhuma credencial encontrada!")
                print("❌ Procurou em:")
                print(f"   - {secret_path_on_render}")
                print(f"   - {local_path}")
                return None
        
        # Verifica se já foi inicializado
        try:
            firebase_admin.initialize_app(cred)
            print("✅ Firebase inicializado com sucesso")
        except ValueError:
            # Já foi inicializado
            print("✅ Firebase já estava inicializado")
            pass
        
        return firestore.client()
    
    except Exception as e:
        print(f"❌ Erro ao inicializar Firestore: {e}")
        return None

def export_collection(db, collection_name):
    """Exporta uma coleção do Firestore"""
    try:
        print(f"📦 Exportando coleção: {collection_name}")
        docs = db.collection(collection_name).stream()
        
        data = []
        count = 0
        
        for doc in docs:
            doc_data = doc.to_dict()
            doc_data['_firestore_id'] = doc.id  # Preserva o ID original
            
            # Converte timestamps para strings ISO
            for key, value in doc_data.items():
                if hasattr(value, 'timestamp'):  # Firestore timestamp
                    doc_data[key] = value.isoformat()
                elif hasattr(value, 'isoformat'):  # datetime
                    doc_data[key] = value.isoformat()
            
            data.append(doc_data)
            count += 1
        
        print(f"✅ {collection_name}: {count} documentos exportados")
        return data
    
    except Exception as e:
        print(f"❌ Erro ao exportar {collection_name}: {e}")
        return []

def main():
    """Função principal de exportação"""
    print("🚀 Iniciando exportação do Firestore...")
    print("="*50)
    
    # Inicializa Firestore
    db = init_firestore()
    if not db:
        print("❌ Falha ao conectar com Firestore")
        return
    
    # Cria diretório de exportação
    export_dir = "migration/firestore_export"
    os.makedirs(export_dir, exist_ok=True)
    
    # Lista de coleções para exportar
    collections = [
        'usuarios',
        'grupos', 
        'habilidades_nft',
        'noticias',
        'vitimas'
    ]
    
    export_data = {}
    total_docs = 0
    
    # Exporta cada coleção
    for collection in collections:
        data = export_collection(db, collection)
        export_data[collection] = data
        total_docs += len(data)
    
    # Salva dados em arquivos JSON
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Arquivo único com todos os dados
    full_export_file = f"{export_dir}/firestore_full_export_{timestamp}.json"
    with open(full_export_file, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
    
    # Arquivos separados por coleção
    for collection, data in export_data.items():
        if data:  # Só cria arquivo se tem dados
            collection_file = f"{export_dir}/{collection}_{timestamp}.json"
            with open(collection_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
    
    print("\n" + "="*60)
    print("✅ EXPORTAÇÃO CONCLUÍDA!")
    print(f"📊 Total de documentos exportados: {total_docs}")
    print(f"📁 Arquivos salvos em: {export_dir}/")
    print(f"📄 Exportação completa: {full_export_file}")
    print("="*60)
    
    # Estatísticas detalhadas
    print("\n📈 ESTATÍSTICAS POR COLEÇÃO:")
    for collection, data in export_data.items():
        print(f"  {collection}: {len(data)} documentos")
    
    if total_docs > 0:
        print(f"\n🎯 Próximo passo: Configure o Supabase e execute a importação")
        print(f"   python migration/import_to_supabase.py")
    else:
        print(f"\n⚠️  Nenhum dado foi exportado. Verifique se há dados no Firestore.")

if __name__ == "__main__":
    main()
