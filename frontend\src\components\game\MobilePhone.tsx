import React from 'react';
import { useHackGameStore } from '../../stores/hackGameStore';

interface MobilePhoneProps {
  children: React.ReactNode;
}

const MobilePhone: React.FC<MobilePhoneProps> = ({ children }) => {
  const { player, notifications, setCurrentScreen } = useHackGameStore();
  const unreadCount = notifications.filter(n => !n.read).length;

  const handleHomeClick = () => {
    setCurrentScreen('home');
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black p-4">
      {/* Moldura do celular */}
      <div className="relative">
        {/* Corpo do celular */}
        <div className="w-[420px] h-[900px] bg-black rounded-[3rem] p-3 shadow-2xl">
          {/* Tela do celular */}
          <div className="w-full h-full bg-gray-900 rounded-[2.5rem] overflow-hidden relative flex flex-col">
            {/* Notch */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-36 h-7 bg-black rounded-b-2xl z-50"></div>

            {/* Status bar */}
            <div className="flex justify-between items-center px-6 pt-4 pb-2 text-white text-sm relative z-40 flex-shrink-0">
              <div className="flex items-center space-x-1">
                <span className="text-sm font-medium">9:41</span>
              </div>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                )}
                <div className="flex space-x-1">
                  <div className="w-1 h-3 bg-white rounded-full"></div>
                  <div className="w-1 h-3 bg-white rounded-full opacity-60"></div>
                  <div className="w-1 h-3 bg-white rounded-full opacity-30"></div>
                </div>
                <div className="w-7 h-3 border border-white rounded-sm">
                  <div className="w-5 h-1 bg-green-500 rounded-sm m-0.5"></div>
                </div>
              </div>
            </div>

            {/* Conteúdo da tela */}
            <div className="flex-1 overflow-hidden">
              {children}
            </div>

            {/* Botão Home sempre visível */}
            <div className="flex-shrink-0 p-4 flex justify-center">
              <button
                onClick={handleHomeClick}
                className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95 border-2 border-white/20"
              >
                <div className="text-2xl">🏠</div>
              </button>
            </div>

            {/* Indicador home tradicional */}
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-36 h-1 bg-white rounded-full opacity-20"></div>
          </div>
        </div>

        {/* Botões laterais */}
        <div className="absolute left-0 top-24 w-1 h-14 bg-gray-700 rounded-l-lg"></div>
        <div className="absolute left-0 top-44 w-1 h-10 bg-gray-700 rounded-l-lg"></div>
        <div className="absolute left-0 top-60 w-1 h-10 bg-gray-700 rounded-l-lg"></div>
        <div className="absolute right-0 top-36 w-1 h-18 bg-gray-700 rounded-r-lg"></div>
      </div>
    </div>
  );
};

export default MobilePhone;
