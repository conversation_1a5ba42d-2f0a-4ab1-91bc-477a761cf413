import React from 'react';
import { useHackGameStore } from '../../stores/hackGameStore';

interface MobilePhoneProps {
  children: React.ReactNode;
}

const MobilePhone: React.FC<MobilePhoneProps> = ({ children }) => {
  const { player, notifications, setCurrentScreen } = useHackGameStore();
  const unreadCount = notifications.filter(n => !n.read).length;

  const handleHomeClick = () => {
    // Feedback tátil visual
    const button = document.querySelector('.home-indicator') as HTMLElement;
    if (button) {
      button.style.transform = 'scaleY(0.8)';
      setTimeout(() => {
        button.style.transform = 'scaleY(1)';
      }, 100);
    }

    setCurrentScreen('home');
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black p-4">
      {/* Moldura do celular */}
      <div className="relative">
        {/* Corpo do celular */}
        <div className="w-[420px] h-[880px] bg-black rounded-[3rem] p-3 shadow-2xl">
          {/* Tela do celular */}
          <div className="w-full h-full bg-gray-900 rounded-[2.5rem] overflow-hidden relative flex flex-col">
            {/* Notch */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-36 h-7 bg-black rounded-b-2xl z-50"></div>

            {/* Status bar */}
            <div className="flex justify-between items-center px-6 pt-4 pb-2 text-white text-sm relative z-40 flex-shrink-0">
              <div className="flex items-center space-x-1">
                <span className="text-sm font-medium">9:41</span>
              </div>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                )}
                <div className="flex space-x-1">
                  <div className="w-1 h-3 bg-white rounded-full"></div>
                  <div className="w-1 h-3 bg-white rounded-full opacity-60"></div>
                  <div className="w-1 h-3 bg-white rounded-full opacity-30"></div>
                </div>
                <div className="w-7 h-3 border border-white rounded-sm">
                  <div className="w-5 h-1 bg-green-500 rounded-sm m-0.5"></div>
                </div>
              </div>
            </div>

            {/* Conteúdo da tela */}
            <div className="flex-1 overflow-hidden">
              {children}
            </div>

            {/* Botão Home estilo iPhone */}
            <div className="flex-shrink-0 pb-4 pt-3 flex justify-center">
              <button
                onClick={handleHomeClick}
                className="home-indicator w-36 h-1.5 bg-white rounded-full transition-all duration-200 hover:bg-gray-200 active:bg-gray-300"
                style={{
                  background: 'linear-gradient(135deg, #ffffff 0%, #e5e5e5 100%)',
                  boxShadow: '0 1px 2px rgba(0,0,0,0.2), inset 0 0.5px 0 rgba(255,255,255,0.9)',
                  opacity: 0.9
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.opacity = '1';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.opacity = '0.9';
                }}
              >
              </button>
            </div>
          </div>
        </div>

        {/* Botões laterais */}
        <div className="absolute left-0 top-24 w-1 h-14 bg-gray-700 rounded-l-lg"></div>
        <div className="absolute left-0 top-44 w-1 h-10 bg-gray-700 rounded-l-lg"></div>
        <div className="absolute left-0 top-60 w-1 h-10 bg-gray-700 rounded-l-lg"></div>
        <div className="absolute right-0 top-36 w-1 h-18 bg-gray-700 rounded-r-lg"></div>
      </div>
    </div>
  );
};

export default MobilePhone;
