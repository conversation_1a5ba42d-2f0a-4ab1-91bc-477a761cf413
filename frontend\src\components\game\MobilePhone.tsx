import React from 'react';
import { useHackGameStore } from '../../stores/hackGameStore';

interface MobilePhoneProps {
  children: React.ReactNode;
}

const MobilePhone: React.FC<MobilePhoneProps> = ({ children }) => {
  const { player, notifications } = useHackGameStore();
  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black p-4">
      {/* Moldura do celular */}
      <div className="relative">
        {/* Corpo do celular */}
        <div className="w-[375px] h-[812px] bg-black rounded-[3rem] p-2 shadow-2xl">
          {/* Tela do celular */}
          <div className="w-full h-full bg-gray-900 rounded-[2.5rem] overflow-hidden relative">
            {/* Notch */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black rounded-b-2xl z-50"></div>
            
            {/* Status bar */}
            <div className="flex justify-between items-center px-6 pt-3 pb-1 text-white text-sm relative z-40">
              <div className="flex items-center space-x-1">
                <span className="text-xs">9:41</span>
              </div>
              <div className="flex items-center space-x-1">
                {unreadCount > 0 && (
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                )}
                <div className="flex space-x-1">
                  <div className="w-1 h-3 bg-white rounded-full"></div>
                  <div className="w-1 h-3 bg-white rounded-full opacity-60"></div>
                  <div className="w-1 h-3 bg-white rounded-full opacity-30"></div>
                </div>
                <div className="w-6 h-3 border border-white rounded-sm">
                  <div className="w-4 h-1 bg-green-500 rounded-sm m-0.5"></div>
                </div>
              </div>
            </div>

            {/* Conteúdo da tela */}
            <div className="flex-1 h-full pt-2">
              {children}
            </div>

            {/* Indicador home */}
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white rounded-full opacity-30"></div>
          </div>
        </div>

        {/* Botões laterais */}
        <div className="absolute left-0 top-20 w-1 h-12 bg-gray-700 rounded-l-lg"></div>
        <div className="absolute left-0 top-36 w-1 h-8 bg-gray-700 rounded-l-lg"></div>
        <div className="absolute left-0 top-48 w-1 h-8 bg-gray-700 rounded-l-lg"></div>
        <div className="absolute right-0 top-32 w-1 h-16 bg-gray-700 rounded-r-lg"></div>
      </div>
    </div>
  );
};

export default MobilePhone;
