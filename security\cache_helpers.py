# 🛡️ Cache Security Helper
# Funções auxiliares para usar o cache seguro no seu jogo

from flask import current_app
import json
import hashlib

class SecureGameCache:
    """
    Helper class para usar o cache seguro nas funções do jogo
    """
    
    @staticmethod
    def get_cache_security():
        """Obter instância do cache security"""
        try:
            return current_app.cache_security
        except:
            return None
    
    @staticmethod
    def cache_player_data(uid, player_data, ttl=300):
        """
        Cachear dados do jogador de forma segura
        
        Args:
            uid: UID do jogador
            player_data: Dados do jogador
            ttl: Tempo de vida em segundos (padrão: 5 minutos)
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return False
        
        cache_key = f"player_data:{uid}"
        cache_security.secure_cache_set(cache_key, player_data, ttl)
        return True
    
    @staticmethod
    def get_player_data(uid):
        """
        Recuperar dados do jogador do cache seguro
        
        Args:
            uid: UID do jogador
            
        Returns:
            dict|None: Dados do jogador ou None se não estiver em cache
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return None
        
        cache_key = f"player_data:{uid}"
        return cache_security.secure_cache_get(cache_key)
    
    @staticmethod
    def cache_skill_data(skills_data, ttl=600):
        """
        Cachear dados das habilidades NFT
        
        Args:
            skills_data: Dados das habilidades
            ttl: Tempo de vida em segundos (padrão: 10 minutos)
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return False
        
        cache_key = "nft_skills_data"
        cache_security.secure_cache_set(cache_key, skills_data, ttl)
        return True
    
    @staticmethod
    def get_skill_data():
        """
        Recuperar dados das habilidades do cache
        
        Returns:
            dict|None: Dados das habilidades ou None se não estiver em cache
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return None
        
        cache_key = "nft_skills_data"
        return cache_security.secure_cache_get(cache_key)
    
    @staticmethod
    def cache_game_config(config_data, ttl=1800):
        """
        Cachear configurações do jogo
        
        Args:
            config_data: Dados de configuração
            ttl: Tempo de vida em segundos (padrão: 30 minutos)
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return False
        
        cache_key = "game_config"
        cache_security.secure_cache_set(cache_key, config_data, ttl)
        return True
    
    @staticmethod
    def get_game_config():
        """
        Recuperar configurações do jogo do cache
        
        Returns:
            dict|None: Configurações ou None se não estiver em cache
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return None
        
        cache_key = "game_config"
        return cache_security.secure_cache_get(cache_key)
    
    @staticmethod
    def invalidate_player_cache(uid):
        """
        Invalidar cache de um jogador específico
        
        Args:
            uid: UID do jogador
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return False
        
        cache_key = f"player_data:{uid}"
        cache_security.secure_cache_delete(cache_key)
        return True
    
    @staticmethod
    def cache_tournament_data(tournament_data, ttl=180):
        """
        Cachear dados do torneio
        
        Args:
            tournament_data: Dados do torneio
            ttl: Tempo de vida em segundos (padrão: 3 minutos)
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return False
        
        cache_key = "tournament_data"
        cache_security.secure_cache_set(cache_key, tournament_data, ttl)
        return True
    
    @staticmethod
    def get_tournament_data():
        """
        Recuperar dados do torneio do cache
        
        Returns:
            dict|None: Dados do torneio ou None se não estiver em cache
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return None
        
        cache_key = "tournament_data"
        return cache_security.secure_cache_get(cache_key)
    
    @staticmethod
    def cache_ranking_data(ranking_type, ranking_data, ttl=300):
        """
        Cachear dados de ranking
        
        Args:
            ranking_type: Tipo de ranking (ex: 'level', 'deface', 'groups')
            ranking_data: Dados do ranking
            ttl: Tempo de vida em segundos (padrão: 5 minutos)
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return False
        
        cache_key = f"ranking:{ranking_type}"
        cache_security.secure_cache_set(cache_key, ranking_data, ttl)
        return True
    
    @staticmethod
    def get_ranking_data(ranking_type):
        """
        Recuperar dados de ranking do cache
        
        Args:
            ranking_type: Tipo de ranking
            
        Returns:
            dict|None: Dados do ranking ou None se não estiver em cache
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return None
        
        cache_key = f"ranking:{ranking_type}"
        return cache_security.secure_cache_get(cache_key)
    
    @staticmethod
    def create_cache_key(*parts):
        """
        Criar chave de cache segura a partir de múltiplas partes
        
        Args:
            *parts: Partes da chave
            
        Returns:
            str: Chave de cache única
        """
        # Converter todas as partes para string e juntar
        key_parts = [str(part) for part in parts]
        raw_key = ":".join(key_parts)
        
        # Hash para garantir tamanho consistente e caracteres válidos
        return hashlib.md5(raw_key.encode('utf-8')).hexdigest()
    
    @staticmethod
    def batch_invalidate(pattern):
        """
        Invalidar múltiplas chaves de cache baseado em padrão
        
        Args:
            pattern: Padrão para buscar chaves (ex: 'player_data:')
        """
        cache_security = SecureGameCache.get_cache_security()
        if not cache_security:
            return 0
        
        invalidated = 0
        with cache_security.lock:
            # Encontrar chaves que correspondem ao padrão
            keys_to_remove = [
                key for key in cache_security.secure_cache.keys()
                if pattern in key
            ]
            
            # Remover chaves encontradas
            for key in keys_to_remove:
                cache_security.secure_cache_delete(key)
                invalidated += 1
        
        return invalidated
