{"ast": null, "code": "import{create}from'zustand';import mockApiService from'../services/mockApi';let pollIntervalId=null;export const useChatStore=create((set,get)=>({// Estado inicial\nmessages:[],isLoading:false,error:null,isSending:false,sendError:null,isPolling:false,pollInterval:2000,// 2 segundos\nlastMessageTime:null,unreadCount:0,isOpen:false,isMinimized:false,// === AÇÕES DE MENSAGENS ===\nloadMessages:async()=>{const state=get();if(state.isLoading)return;// Evitar múltiplas requisições simultâneas\nconsole.log('ChatStore - Carregando mensagens...');set({isLoading:true,error:null});try{const response=await mockApiService.getChatMessages();if(response.sucesso&&response.dados){const newMessages=response.dados;const currentMessages=state.messages;// Verificar se há mensagens novas\nconst lastCurrentMessageTime=currentMessages.length>0?currentMessages[currentMessages.length-1].timestamp:null;const newUnreadCount=lastCurrentMessageTime?newMessages.filter(msg=>msg.timestamp>lastCurrentMessageTime).length:newMessages.length;set({messages:newMessages,isLoading:false,error:null,lastMessageTime:newMessages.length>0?newMessages[newMessages.length-1].timestamp:state.lastMessageTime,unreadCount:state.isOpen?0:state.unreadCount+newUnreadCount});}else{throw new Error(response.mensagem||'Erro ao carregar mensagens');}}catch(error){set({isLoading:false,error:error.message||'Erro ao carregar mensagens'});}},sendMessage:async message=>{if(!message.trim())return;const{messages}=get();console.log('ChatStore - Enviando mensagem:',message);set({isSending:true,sendError:null});// Adicionar mensagem otimisticamente\nconst newMessage={id:Date.now(),usuario:'Você',mensagem:message.trim(),timestamp:new Date().toISOString(),tipo:'global'};set({messages:[...messages,newMessage]});try{const response=await mockApiService.sendChatMessage({mensagem:message.trim(),tipo:'global'});if(response.sucesso){console.log('ChatStore - Mensagem enviada com sucesso');set({isSending:false,sendError:null});}else{// Remover mensagem otimística em caso de erro\nset({messages:messages,isSending:false,sendError:response.mensagem||'Erro ao enviar mensagem'});}}catch(error){// Remover mensagem otimística em caso de erro\nset({messages:messages,isSending:false,sendError:error.message||'Erro ao enviar mensagem'});}},// === AÇÕES DE POLLING ===\nstartPolling:()=>{const state=get();if(state.isPolling)return;set({isPolling:true});// Carregar mensagens imediatamente\nget().loadMessages();// Configurar polling\npollIntervalId=setInterval(()=>{get().loadMessages();},state.pollInterval);},stopPolling:()=>{set({isPolling:false});if(pollIntervalId){clearInterval(pollIntervalId);pollIntervalId=null;}},markAsRead:()=>{set({unreadCount:0});},// === AÇÕES DE UI ===\nopenChat:()=>{set({isOpen:true,isMinimized:false,unreadCount:0});// Iniciar polling quando abrir o chat\nif(!get().isPolling){get().startPolling();}},closeChat:()=>{set({isOpen:false,isMinimized:false});// Parar polling quando fechar o chat\nget().stopPolling();},toggleChat:()=>{const state=get();if(state.isOpen){state.closeChat();}else{state.openChat();}},minimizeChat:()=>{set({isMinimized:true});},maximizeChat:()=>{set({isMinimized:false});},// === AÇÕES UTILITÁRIAS ===\nclearError:()=>{set({error:null,sendError:null});},clearMessages:()=>{set({messages:[],lastMessageTime:null,unreadCount:0});}}));// Hook personalizado para usar o chat\nexport const useChat=()=>{const store=useChatStore();return{...store,// Computed values\nhasMessages:store.messages.length>0,hasUnread:store.unreadCount>0,canSend:!store.isSending&&!store.isLoading,// Utility functions\nformatTime:timestamp=>{const date=new Date(timestamp);return date.toLocaleTimeString('pt-BR',{hour:'2-digit',minute:'2-digit'});},formatDate:timestamp=>{const date=new Date(timestamp);const today=new Date();const yesterday=new Date(today);yesterday.setDate(yesterday.getDate()-1);if(date.toDateString()===today.toDateString()){return'Hoje';}else if(date.toDateString()===yesterday.toDateString()){return'Ontem';}else{return date.toLocaleDateString('pt-BR');}},isMessageFromToday:timestamp=>{const date=new Date(timestamp);const today=new Date();return date.toDateString()===today.toDateString();}};};", "map": {"version": 3, "names": ["create", "mockApiService", "pollIntervalId", "useChatStore", "set", "get", "messages", "isLoading", "error", "isSending", "sendError", "isPolling", "pollInterval", "lastMessageTime", "unreadCount", "isOpen", "isMinimized", "loadMessages", "state", "console", "log", "response", "getChatMessages", "sucesso", "dados", "newMessages", "currentMessages", "lastCurrentMessageTime", "length", "timestamp", "newUnreadCount", "filter", "msg", "Error", "mensagem", "message", "sendMessage", "trim", "newMessage", "id", "Date", "now", "usuario", "toISOString", "tipo", "sendChatMessage", "startPolling", "setInterval", "stopPolling", "clearInterval", "mark<PERSON><PERSON><PERSON>", "openChat", "closeChat", "toggleChat", "minimizeChat", "maximizeChat", "clearError", "clearMessages", "useChat", "store", "hasMessages", "hasUnread", "canSend", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "formatDate", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "isMessageFromToday"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/chatStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport mockApiService from '../services/mockApi';\n\nexport interface ChatMessage {\n  id: number;\n  usuario: string;\n  mensagem: string;\n  timestamp: string;\n  tipo: 'global' | 'grupo' | 'privado';\n}\n\ninterface ChatState {\n  // Estado das mensagens\n  messages: ChatMessage[];\n  isLoading: boolean;\n  error: string | null;\n  \n  // Estado de envio\n  isSending: boolean;\n  sendError: string | null;\n  \n  // Configurações\n  isPolling: boolean;\n  pollInterval: number;\n  lastMessageTime: string | null;\n  unreadCount: number;\n  \n  // Estado da UI\n  isOpen: boolean;\n  isMinimized: boolean;\n  \n  // Ações\n  loadMessages: () => Promise<void>;\n  sendMessage: (message: string) => Promise<void>;\n  startPolling: () => void;\n  stopPolling: () => void;\n  markAsRead: () => void;\n  \n  // UI Actions\n  openChat: () => void;\n  closeChat: () => void;\n  toggleChat: () => void;\n  minimizeChat: () => void;\n  maximizeChat: () => void;\n  \n  // Utility actions\n  clearError: () => void;\n  clearMessages: () => void;\n}\n\nlet pollIntervalId: NodeJS.Timeout | null = null;\n\nexport const useChatStore = create<ChatState>((set, get) => ({\n  // Estado inicial\n  messages: [],\n  isLoading: false,\n  error: null,\n  \n  isSending: false,\n  sendError: null,\n  \n  isPolling: false,\n  pollInterval: 2000, // 2 segundos\n  lastMessageTime: null,\n  unreadCount: 0,\n  \n  isOpen: false,\n  isMinimized: false,\n\n  // === AÇÕES DE MENSAGENS ===\n  loadMessages: async () => {\n    const state = get();\n    if (state.isLoading) return; // Evitar múltiplas requisições simultâneas\n\n    console.log('ChatStore - Carregando mensagens...');\n    set({ isLoading: true, error: null });\n\n    try {\n      const response = await mockApiService.getChatMessages();\n      \n      if (response.sucesso && response.dados) {\n        const newMessages = response.dados;\n        const currentMessages = state.messages;\n        \n        // Verificar se há mensagens novas\n        const lastCurrentMessageTime = currentMessages.length > 0 \n          ? currentMessages[currentMessages.length - 1].timestamp \n          : null;\n        \n        const newUnreadCount = lastCurrentMessageTime\n          ? newMessages.filter((msg: ChatMessage) => msg.timestamp > lastCurrentMessageTime).length\n          : newMessages.length;\n        \n        set({\n          messages: newMessages,\n          isLoading: false,\n          error: null,\n          lastMessageTime: newMessages.length > 0 \n            ? newMessages[newMessages.length - 1].timestamp \n            : state.lastMessageTime,\n          unreadCount: state.isOpen ? 0 : state.unreadCount + newUnreadCount,\n        });\n      } else {\n        throw new Error(response.mensagem || 'Erro ao carregar mensagens');\n      }\n    } catch (error: any) {\n      set({\n        isLoading: false,\n        error: error.message || 'Erro ao carregar mensagens',\n      });\n    }\n  },\n\n  sendMessage: async (message: string) => {\n    if (!message.trim()) return;\n\n    const { messages } = get();\n    console.log('ChatStore - Enviando mensagem:', message);\n    set({ isSending: true, sendError: null });\n\n    // Adicionar mensagem otimisticamente\n    const newMessage = {\n      id: Date.now(),\n      usuario: 'Você',\n      mensagem: message.trim(),\n      timestamp: new Date().toISOString(),\n      tipo: 'global' as const\n    };\n\n    set({\n      messages: [...messages, newMessage]\n    });\n\n    try {\n      const response = await mockApiService.sendChatMessage({\n        mensagem: message.trim(),\n        tipo: 'global'\n      });\n\n      if (response.sucesso) {\n        console.log('ChatStore - Mensagem enviada com sucesso');\n        set({\n          isSending: false,\n          sendError: null,\n        });\n      } else {\n        // Remover mensagem otimística em caso de erro\n        set({\n          messages: messages,\n          isSending: false,\n          sendError: response.mensagem || 'Erro ao enviar mensagem',\n        });\n      }\n    } catch (error: any) {\n      // Remover mensagem otimística em caso de erro\n      set({\n        messages: messages,\n        isSending: false,\n        sendError: error.message || 'Erro ao enviar mensagem',\n      });\n    }\n  },\n\n  // === AÇÕES DE POLLING ===\n  startPolling: () => {\n    const state = get();\n    if (state.isPolling) return;\n    \n    set({ isPolling: true });\n    \n    // Carregar mensagens imediatamente\n    get().loadMessages();\n    \n    // Configurar polling\n    pollIntervalId = setInterval(() => {\n      get().loadMessages();\n    }, state.pollInterval);\n  },\n\n  stopPolling: () => {\n    set({ isPolling: false });\n    \n    if (pollIntervalId) {\n      clearInterval(pollIntervalId);\n      pollIntervalId = null;\n    }\n  },\n\n  markAsRead: () => {\n    set({ unreadCount: 0 });\n  },\n\n  // === AÇÕES DE UI ===\n  openChat: () => {\n    set({ \n      isOpen: true, \n      isMinimized: false,\n      unreadCount: 0,\n    });\n    \n    // Iniciar polling quando abrir o chat\n    if (!get().isPolling) {\n      get().startPolling();\n    }\n  },\n\n  closeChat: () => {\n    set({ isOpen: false, isMinimized: false });\n    \n    // Parar polling quando fechar o chat\n    get().stopPolling();\n  },\n\n  toggleChat: () => {\n    const state = get();\n    if (state.isOpen) {\n      state.closeChat();\n    } else {\n      state.openChat();\n    }\n  },\n\n  minimizeChat: () => {\n    set({ isMinimized: true });\n  },\n\n  maximizeChat: () => {\n    set({ isMinimized: false });\n  },\n\n  // === AÇÕES UTILITÁRIAS ===\n  clearError: () => {\n    set({ error: null, sendError: null });\n  },\n\n  clearMessages: () => {\n    set({ messages: [], lastMessageTime: null, unreadCount: 0 });\n  },\n}));\n\n// Hook personalizado para usar o chat\nexport const useChat = () => {\n  const store = useChatStore();\n  \n  return {\n    ...store,\n    \n    // Computed values\n    hasMessages: store.messages.length > 0,\n    hasUnread: store.unreadCount > 0,\n    canSend: !store.isSending && !store.isLoading,\n    \n    // Utility functions\n    formatTime: (timestamp: string) => {\n      const date = new Date(timestamp);\n      return date.toLocaleTimeString('pt-BR', {\n        hour: '2-digit',\n        minute: '2-digit',\n      });\n    },\n    \n    formatDate: (timestamp: string) => {\n      const date = new Date(timestamp);\n      const today = new Date();\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      \n      if (date.toDateString() === today.toDateString()) {\n        return 'Hoje';\n      } else if (date.toDateString() === yesterday.toDateString()) {\n        return 'Ontem';\n      } else {\n        return date.toLocaleDateString('pt-BR');\n      }\n    },\n    \n    isMessageFromToday: (timestamp: string) => {\n      const date = new Date(timestamp);\n      const today = new Date();\n      return date.toDateString() === today.toDateString();\n    },\n  };\n};\n"], "mappings": "AAAA,OAASA,MAAM,KAAQ,SAAS,CAChC,MAAO,CAAAC,cAAc,KAAM,qBAAqB,CAiDhD,GAAI,CAAAC,cAAqC,CAAG,IAAI,CAEhD,MAAO,MAAM,CAAAC,YAAY,CAAGH,MAAM,CAAY,CAACI,GAAG,CAAEC,GAAG,IAAM,CAC3D;AACAC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IAAI,CAEXC,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,IAAI,CAEfC,SAAS,CAAE,KAAK,CAChBC,YAAY,CAAE,IAAI,CAAE;AACpBC,eAAe,CAAE,IAAI,CACrBC,WAAW,CAAE,CAAC,CAEdC,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,KAAK,CAElB;AACAC,YAAY,CAAE,KAAAA,CAAA,GAAY,CACxB,KAAM,CAAAC,KAAK,CAAGb,GAAG,CAAC,CAAC,CACnB,GAAIa,KAAK,CAACX,SAAS,CAAE,OAAQ;AAE7BY,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC,CAClDhB,GAAG,CAAC,CAAEG,SAAS,CAAE,IAAI,CAAEC,KAAK,CAAE,IAAK,CAAC,CAAC,CAErC,GAAI,CACF,KAAM,CAAAa,QAAQ,CAAG,KAAM,CAAApB,cAAc,CAACqB,eAAe,CAAC,CAAC,CAEvD,GAAID,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,KAAK,CAAE,CACtC,KAAM,CAAAC,WAAW,CAAGJ,QAAQ,CAACG,KAAK,CAClC,KAAM,CAAAE,eAAe,CAAGR,KAAK,CAACZ,QAAQ,CAEtC;AACA,KAAM,CAAAqB,sBAAsB,CAAGD,eAAe,CAACE,MAAM,CAAG,CAAC,CACrDF,eAAe,CAACA,eAAe,CAACE,MAAM,CAAG,CAAC,CAAC,CAACC,SAAS,CACrD,IAAI,CAER,KAAM,CAAAC,cAAc,CAAGH,sBAAsB,CACzCF,WAAW,CAACM,MAAM,CAAEC,GAAgB,EAAKA,GAAG,CAACH,SAAS,CAAGF,sBAAsB,CAAC,CAACC,MAAM,CACvFH,WAAW,CAACG,MAAM,CAEtBxB,GAAG,CAAC,CACFE,QAAQ,CAAEmB,WAAW,CACrBlB,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IAAI,CACXK,eAAe,CAAEY,WAAW,CAACG,MAAM,CAAG,CAAC,CACnCH,WAAW,CAACA,WAAW,CAACG,MAAM,CAAG,CAAC,CAAC,CAACC,SAAS,CAC7CX,KAAK,CAACL,eAAe,CACzBC,WAAW,CAAEI,KAAK,CAACH,MAAM,CAAG,CAAC,CAAGG,KAAK,CAACJ,WAAW,CAAGgB,cACtD,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,KAAM,IAAI,CAAAG,KAAK,CAACZ,QAAQ,CAACa,QAAQ,EAAI,4BAA4B,CAAC,CACpE,CACF,CAAE,MAAO1B,KAAU,CAAE,CACnBJ,GAAG,CAAC,CACFG,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAEA,KAAK,CAAC2B,OAAO,EAAI,4BAC1B,CAAC,CAAC,CACJ,CACF,CAAC,CAEDC,WAAW,CAAE,KAAO,CAAAD,OAAe,EAAK,CACtC,GAAI,CAACA,OAAO,CAACE,IAAI,CAAC,CAAC,CAAE,OAErB,KAAM,CAAE/B,QAAS,CAAC,CAAGD,GAAG,CAAC,CAAC,CAC1Bc,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEe,OAAO,CAAC,CACtD/B,GAAG,CAAC,CAAEK,SAAS,CAAE,IAAI,CAAEC,SAAS,CAAE,IAAK,CAAC,CAAC,CAEzC;AACA,KAAM,CAAA4B,UAAU,CAAG,CACjBC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,OAAO,CAAE,MAAM,CACfR,QAAQ,CAAEC,OAAO,CAACE,IAAI,CAAC,CAAC,CACxBR,SAAS,CAAE,GAAI,CAAAW,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CACnCC,IAAI,CAAE,QACR,CAAC,CAEDxC,GAAG,CAAC,CACFE,QAAQ,CAAE,CAAC,GAAGA,QAAQ,CAAEgC,UAAU,CACpC,CAAC,CAAC,CAEF,GAAI,CACF,KAAM,CAAAjB,QAAQ,CAAG,KAAM,CAAApB,cAAc,CAAC4C,eAAe,CAAC,CACpDX,QAAQ,CAAEC,OAAO,CAACE,IAAI,CAAC,CAAC,CACxBO,IAAI,CAAE,QACR,CAAC,CAAC,CAEF,GAAIvB,QAAQ,CAACE,OAAO,CAAE,CACpBJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACvDhB,GAAG,CAAC,CACFK,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACAN,GAAG,CAAC,CACFE,QAAQ,CAAEA,QAAQ,CAClBG,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAEW,QAAQ,CAACa,QAAQ,EAAI,yBAClC,CAAC,CAAC,CACJ,CACF,CAAE,MAAO1B,KAAU,CAAE,CACnB;AACAJ,GAAG,CAAC,CACFE,QAAQ,CAAEA,QAAQ,CAClBG,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAEF,KAAK,CAAC2B,OAAO,EAAI,yBAC9B,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACAW,YAAY,CAAEA,CAAA,GAAM,CAClB,KAAM,CAAA5B,KAAK,CAAGb,GAAG,CAAC,CAAC,CACnB,GAAIa,KAAK,CAACP,SAAS,CAAE,OAErBP,GAAG,CAAC,CAAEO,SAAS,CAAE,IAAK,CAAC,CAAC,CAExB;AACAN,GAAG,CAAC,CAAC,CAACY,YAAY,CAAC,CAAC,CAEpB;AACAf,cAAc,CAAG6C,WAAW,CAAC,IAAM,CACjC1C,GAAG,CAAC,CAAC,CAACY,YAAY,CAAC,CAAC,CACtB,CAAC,CAAEC,KAAK,CAACN,YAAY,CAAC,CACxB,CAAC,CAEDoC,WAAW,CAAEA,CAAA,GAAM,CACjB5C,GAAG,CAAC,CAAEO,SAAS,CAAE,KAAM,CAAC,CAAC,CAEzB,GAAIT,cAAc,CAAE,CAClB+C,aAAa,CAAC/C,cAAc,CAAC,CAC7BA,cAAc,CAAG,IAAI,CACvB,CACF,CAAC,CAEDgD,UAAU,CAAEA,CAAA,GAAM,CAChB9C,GAAG,CAAC,CAAEU,WAAW,CAAE,CAAE,CAAC,CAAC,CACzB,CAAC,CAED;AACAqC,QAAQ,CAAEA,CAAA,GAAM,CACd/C,GAAG,CAAC,CACFW,MAAM,CAAE,IAAI,CACZC,WAAW,CAAE,KAAK,CAClBF,WAAW,CAAE,CACf,CAAC,CAAC,CAEF;AACA,GAAI,CAACT,GAAG,CAAC,CAAC,CAACM,SAAS,CAAE,CACpBN,GAAG,CAAC,CAAC,CAACyC,YAAY,CAAC,CAAC,CACtB,CACF,CAAC,CAEDM,SAAS,CAAEA,CAAA,GAAM,CACfhD,GAAG,CAAC,CAAEW,MAAM,CAAE,KAAK,CAAEC,WAAW,CAAE,KAAM,CAAC,CAAC,CAE1C;AACAX,GAAG,CAAC,CAAC,CAAC2C,WAAW,CAAC,CAAC,CACrB,CAAC,CAEDK,UAAU,CAAEA,CAAA,GAAM,CAChB,KAAM,CAAAnC,KAAK,CAAGb,GAAG,CAAC,CAAC,CACnB,GAAIa,KAAK,CAACH,MAAM,CAAE,CAChBG,KAAK,CAACkC,SAAS,CAAC,CAAC,CACnB,CAAC,IAAM,CACLlC,KAAK,CAACiC,QAAQ,CAAC,CAAC,CAClB,CACF,CAAC,CAEDG,YAAY,CAAEA,CAAA,GAAM,CAClBlD,GAAG,CAAC,CAAEY,WAAW,CAAE,IAAK,CAAC,CAAC,CAC5B,CAAC,CAEDuC,YAAY,CAAEA,CAAA,GAAM,CAClBnD,GAAG,CAAC,CAAEY,WAAW,CAAE,KAAM,CAAC,CAAC,CAC7B,CAAC,CAED;AACAwC,UAAU,CAAEA,CAAA,GAAM,CAChBpD,GAAG,CAAC,CAAEI,KAAK,CAAE,IAAI,CAAEE,SAAS,CAAE,IAAK,CAAC,CAAC,CACvC,CAAC,CAED+C,aAAa,CAAEA,CAAA,GAAM,CACnBrD,GAAG,CAAC,CAAEE,QAAQ,CAAE,EAAE,CAAEO,eAAe,CAAE,IAAI,CAAEC,WAAW,CAAE,CAAE,CAAC,CAAC,CAC9D,CACF,CAAC,CAAC,CAAC,CAEH;AACA,MAAO,MAAM,CAAA4C,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,KAAK,CAAGxD,YAAY,CAAC,CAAC,CAE5B,MAAO,CACL,GAAGwD,KAAK,CAER;AACAC,WAAW,CAAED,KAAK,CAACrD,QAAQ,CAACsB,MAAM,CAAG,CAAC,CACtCiC,SAAS,CAAEF,KAAK,CAAC7C,WAAW,CAAG,CAAC,CAChCgD,OAAO,CAAE,CAACH,KAAK,CAAClD,SAAS,EAAI,CAACkD,KAAK,CAACpD,SAAS,CAE7C;AACAwD,UAAU,CAAGlC,SAAiB,EAAK,CACjC,KAAM,CAAAmC,IAAI,CAAG,GAAI,CAAAxB,IAAI,CAACX,SAAS,CAAC,CAChC,MAAO,CAAAmC,IAAI,CAACC,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAAC,CAEDC,UAAU,CAAGvC,SAAiB,EAAK,CACjC,KAAM,CAAAmC,IAAI,CAAG,GAAI,CAAAxB,IAAI,CAACX,SAAS,CAAC,CAChC,KAAM,CAAAwC,KAAK,CAAG,GAAI,CAAA7B,IAAI,CAAC,CAAC,CACxB,KAAM,CAAA8B,SAAS,CAAG,GAAI,CAAA9B,IAAI,CAAC6B,KAAK,CAAC,CACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC,CAE1C,GAAIR,IAAI,CAACS,YAAY,CAAC,CAAC,GAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,CAAE,CAChD,MAAO,MAAM,CACf,CAAC,IAAM,IAAIT,IAAI,CAACS,YAAY,CAAC,CAAC,GAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,CAAE,CAC3D,MAAO,OAAO,CAChB,CAAC,IAAM,CACL,MAAO,CAAAT,IAAI,CAACU,kBAAkB,CAAC,OAAO,CAAC,CACzC,CACF,CAAC,CAEDC,kBAAkB,CAAG9C,SAAiB,EAAK,CACzC,KAAM,CAAAmC,IAAI,CAAG,GAAI,CAAAxB,IAAI,CAACX,SAAS,CAAC,CAChC,KAAM,CAAAwC,KAAK,CAAG,GAAI,CAAA7B,IAAI,CAAC,CAAC,CACxB,MAAO,CAAAwB,IAAI,CAACS,YAAY,CAAC,CAAC,GAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,CACrD,CACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}