{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleGamePage.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useChat } from '../stores/chatStore';\n\n// Componente do Dashboard Simplificado\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleDashboard = () => {\n  _s();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    loadPlayerData,\n    hasPlayerData\n  } = usePlayer();\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold mb-2\",\n          children: \"\\uD83C\\uDFAE Dashboard do Jogo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-muted\",\n          children: [\"Bem-vindo de volta, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: (user === null || user === void 0 ? void 0 : user.nick) || 'Jogador'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 33\n          }, this), \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-semibold mb-6\",\n              children: \"\\uD83D\\uDCCA Estat\\xEDsticas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-blue-900 border-blue-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-blue-100\",\n                    children: isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-300\",\n                    children: \"Pontos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-green-900 border-green-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-green-100\",\n                    children: isLoadingPlayer ? '...' : playerData.nivel\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 55,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-green-300\",\n                    children: \"N\\xEDvel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 58,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-purple-900 border-purple-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-purple-100\",\n                    children: isLoadingPlayer ? '...' : playerData.conquistas\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 63,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-purple-300\",\n                    children: \"Conquistas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-orange-900 border-orange-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-orange-100\",\n                    children: isLoadingPlayer ? '...' : playerData.ranking\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 71,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-orange-300\",\n                    children: \"Ranking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 74,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"\\u26A1 A\\xE7\\xF5es R\\xE1pidas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-primary\",\n                children: \"\\uD83D\\uDD0D Scanner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-secondary\",\n                children: \"\\uD83D\\uDCAC Chat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-accent\",\n                children: \"\\uD83C\\uDFC6 Loja\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-outline\",\n                children: \"\\u2699\\uFE0F Configura\\xE7\\xF5es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-semibold mb-6\",\n            children: \"\\uD83D\\uDCC8 Atividade Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Pontos ganhos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-text-muted\",\n                    children: \"H\\xE1 2 horas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-green-400 font-bold\",\n                children: \"+150 pts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold\",\n                    children: \"\\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Nova conquista\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-text-muted\",\n                    children: \"H\\xE1 1 dia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-400 font-bold\",\n                children: \"Explorador\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold\",\n                    children: \"\\u2B06\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Subiu de n\\xEDvel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-text-muted\",\n                    children: \"H\\xE1 3 dias\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-purple-400 font-bold\",\n                children: \"N\\xEDvel 15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n\n// Componentes de placeholder para outras páginas\n_s(SimpleDashboard, \"4ZBWbfZBu+lAD/wW1WhGuHvBS6I=\", false, function () {\n  return [useSimpleAuth, usePlayer];\n});\n_c = SimpleDashboard;\nconst SimpleScanner = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-4\",\n        children: \"\\uD83D\\uDD0D Scanner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-muted mb-6\",\n        children: \"Funcionalidade do scanner ser\\xE1 implementada aqui.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-bg-tertiary p-8 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDEA7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Em desenvolvimento...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 156,\n  columnNumber: 3\n}, this);\n_c2 = SimpleScanner;\nconst SimpleChat = () => {\n  _s2();\n  const {\n    messages,\n    isLoadingMessages,\n    loadMessages,\n    sendMessage,\n    isSending\n  } = useChat();\n  const [newMessage, setNewMessage] = React.useState('');\n  useEffect(() => {\n    if (messages.length === 0 && !isLoadingMessages) {\n      console.log('SimpleChat - Carregando mensagens...');\n      loadMessages();\n    }\n  }, [messages.length, isLoadingMessages, loadMessages]);\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    if (newMessage.trim() && !isSending) {\n      await sendMessage(newMessage.trim());\n      setNewMessage('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-6\",\n          children: \"\\uD83D\\uDCAC Chat Global\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-bg-tertiary rounded-lg p-4 h-96 overflow-y-auto mb-4\",\n          children: isLoadingMessages ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-text-muted\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this), \"Carregando mensagens...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this) : messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-text-muted\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-2\",\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Nenhuma mensagem ainda. Seja o primeiro a falar!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold\",\n                children: message.usuario.charAt(0).toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-sm\",\n                    children: message.usuario\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-text-muted\",\n                    children: new Date(message.timestamp).toLocaleTimeString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: message.mensagem\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this)]\n            }, message.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSendMessage,\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newMessage,\n            onChange: e => setNewMessage(e.target.value),\n            placeholder: \"Digite sua mensagem...\",\n            className: \"flex-1 px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\",\n            disabled: isSending\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: !newMessage.trim() || isSending,\n            className: `px-4 py-2 rounded-lg font-medium transition-colors ${!newMessage.trim() || isSending ? 'bg-gray-600 text-gray-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'}`,\n            children: isSending ? '...' : 'Enviar'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n\n// Navegação Simples\n_s2(SimpleChat, \"K6y/suJKM2mj5L1aGCzrZ2Eu4Ug=\", false, function () {\n  return [useChat];\n});\n_c3 = SimpleChat;\nconst SimpleNavigation = () => {\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-bg-secondary border-b border-border-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl font-bold\",\n            children: \"\\uD83C\\uDFAE SHACK\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/game\",\n              className: \"nav-link\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/game/scanner\",\n              className: \"nav-link\",\n              children: \"Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/game/chat\",\n              className: \"nav-link\",\n              children: \"Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-text-muted\",\n            children: \"Modo Teste\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n\n// Página Principal do Jogo Simplificada\n_c4 = SimpleNavigation;\nconst SimpleGamePage = () => {\n  console.log('SimpleGamePage - Renderizando...');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-bg-primary text-text-primary\",\n    children: [/*#__PURE__*/_jsxDEV(SimpleNavigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(SimpleDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/scanner\",\n        element: /*#__PURE__*/_jsxDEV(SimpleScanner, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/chat\",\n        element: /*#__PURE__*/_jsxDEV(SimpleChat, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(SimpleDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 285,\n    columnNumber: 5\n  }, this);\n};\n_c5 = SimpleGamePage;\nexport default SimpleGamePage;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"SimpleDashboard\");\n$RefreshReg$(_c2, \"SimpleScanner\");\n$RefreshReg$(_c3, \"SimpleChat\");\n$RefreshReg$(_c4, \"SimpleNavigation\");\n$RefreshReg$(_c5, \"SimpleGamePage\");", "map": {"version": 3, "names": ["React", "useEffect", "Routes", "Route", "useSimpleAuth", "usePlayer", "useChat", "jsxDEV", "_jsxDEV", "SimpleDashboard", "_s", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "conquistas", "ranking", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "toLocaleString", "_c", "SimpleScanner", "_c2", "SimpleChat", "_s2", "messages", "isLoadingMessages", "loadMessages", "sendMessage", "isSending", "newMessage", "setNewMessage", "useState", "length", "handleSendMessage", "e", "preventDefault", "trim", "map", "message", "usuario", "char<PERSON>t", "toUpperCase", "Date", "timestamp", "toLocaleTimeString", "mensagem", "id", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "_c3", "SimpleNavigation", "href", "_c4", "SimpleGamePage", "path", "element", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleGamePage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useChat } from '../stores/chatStore';\n\n// Componente do Dashboard Simplificado\nconst SimpleDashboard: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n\n  return (\n    <div className=\"p-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold mb-2\">\n            🎮 Dashboard do Jogo\n          </h1>\n          <p className=\"text-text-muted\">\n            Bem-vindo de volta, <strong>{user?.nick || 'Jogador'}</strong>!\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Stats do Jogador */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"card\">\n              <h2 className=\"text-2xl font-semibold mb-6\">📊 Estatísticas</h2>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"card bg-blue-900 border-blue-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-blue-100\">\n                      {isLoadingPlayer ? '...' : playerData.pontos.toLocaleString()}\n                    </div>\n                    <div className=\"text-sm text-blue-300\">Pontos</div>\n                  </div>\n                </div>\n                <div className=\"card bg-green-900 border-green-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-green-100\">\n                      {isLoadingPlayer ? '...' : playerData.nivel}\n                    </div>\n                    <div className=\"text-sm text-green-300\">Nível</div>\n                  </div>\n                </div>\n                <div className=\"card bg-purple-900 border-purple-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-purple-100\">\n                      {isLoadingPlayer ? '...' : playerData.conquistas}\n                    </div>\n                    <div className=\"text-sm text-purple-300\">Conquistas</div>\n                  </div>\n                </div>\n                <div className=\"card bg-orange-900 border-orange-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-orange-100\">\n                      {isLoadingPlayer ? '...' : playerData.ranking}\n                    </div>\n                    <div className=\"text-sm text-orange-300\">Ranking</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Ações Rápidas */}\n          <div>\n            <div className=\"card\">\n              <h2 className=\"text-xl font-semibold mb-4\">⚡ Ações Rápidas</h2>\n              <div className=\"space-y-3\">\n                <button className=\"w-full btn-primary\">\n                  🔍 Scanner\n                </button>\n                <button className=\"w-full btn-secondary\">\n                  💬 Chat\n                </button>\n                <button className=\"w-full btn-accent\">\n                  🏆 Loja\n                </button>\n                <button className=\"w-full btn-outline\">\n                  ⚙️ Configurações\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Atividade Recente */}\n        <div className=\"mt-8\">\n          <div className=\"card\">\n            <h2 className=\"text-2xl font-semibold mb-6\">📈 Atividade Recente</h2>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold\">+</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium\">Pontos ganhos</div>\n                    <div className=\"text-sm text-text-muted\">Há 2 horas</div>\n                  </div>\n                </div>\n                <div className=\"text-green-400 font-bold\">+150 pts</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold\">🏆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium\">Nova conquista</div>\n                    <div className=\"text-sm text-text-muted\">Há 1 dia</div>\n                  </div>\n                </div>\n                <div className=\"text-blue-400 font-bold\">Explorador</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold\">⬆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium\">Subiu de nível</div>\n                    <div className=\"text-sm text-text-muted\">Há 3 dias</div>\n                  </div>\n                </div>\n                <div className=\"text-purple-400 font-bold\">Nível 15</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Componentes de placeholder para outras páginas\nconst SimpleScanner: React.FC = () => (\n  <div className=\"p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"card text-center\">\n        <h1 className=\"text-3xl font-bold mb-4\">🔍 Scanner</h1>\n        <p className=\"text-text-muted mb-6\">Funcionalidade do scanner será implementada aqui.</p>\n        <div className=\"bg-bg-tertiary p-8 rounded-lg\">\n          <div className=\"text-6xl mb-4\">🚧</div>\n          <p>Em desenvolvimento...</p>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nconst SimpleChat: React.FC = () => {\n  const { messages, isLoadingMessages, loadMessages, sendMessage, isSending } = useChat();\n  const [newMessage, setNewMessage] = React.useState('');\n\n  useEffect(() => {\n    if (messages.length === 0 && !isLoadingMessages) {\n      console.log('SimpleChat - Carregando mensagens...');\n      loadMessages();\n    }\n  }, [messages.length, isLoadingMessages, loadMessages]);\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (newMessage.trim() && !isSending) {\n      await sendMessage(newMessage.trim());\n      setNewMessage('');\n    }\n  };\n\n  return (\n    <div className=\"p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"card\">\n          <h1 className=\"text-3xl font-bold mb-6\">💬 Chat Global</h1>\n\n          {/* Área de mensagens */}\n          <div className=\"bg-bg-tertiary rounded-lg p-4 h-96 overflow-y-auto mb-4\">\n            {isLoadingMessages ? (\n              <div className=\"text-center text-text-muted\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n                Carregando mensagens...\n              </div>\n            ) : messages.length === 0 ? (\n              <div className=\"text-center text-text-muted\">\n                <div className=\"text-4xl mb-2\">💬</div>\n                <p>Nenhuma mensagem ainda. Seja o primeiro a falar!</p>\n              </div>\n            ) : (\n              <div className=\"space-y-3\">\n                {messages.map((message) => (\n                  <div key={message.id} className=\"flex items-start space-x-3\">\n                    <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                      {message.usuario.charAt(0).toUpperCase()}\n                    </div>\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-2 mb-1\">\n                        <span className=\"font-medium text-sm\">{message.usuario}</span>\n                        <span className=\"text-xs text-text-muted\">\n                          {new Date(message.timestamp).toLocaleTimeString()}\n                        </span>\n                      </div>\n                      <p className=\"text-sm\">{message.mensagem}</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Formulário de envio */}\n          <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n            <input\n              type=\"text\"\n              value={newMessage}\n              onChange={(e) => setNewMessage(e.target.value)}\n              placeholder=\"Digite sua mensagem...\"\n              className=\"flex-1 px-3 py-2 bg-bg-tertiary border border-border-primary rounded-lg focus:outline-none focus:border-blue-500\"\n              disabled={isSending}\n            />\n            <button\n              type=\"submit\"\n              disabled={!newMessage.trim() || isSending}\n              className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n                !newMessage.trim() || isSending\n                  ? 'bg-gray-600 text-gray-300 cursor-not-allowed'\n                  : 'bg-blue-600 hover:bg-blue-700 text-white'\n              }`}\n            >\n              {isSending ? '...' : 'Enviar'}\n            </button>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Navegação Simples\nconst SimpleNavigation: React.FC = () => {\n  return (\n    <nav className=\"bg-bg-secondary border-b border-border-primary\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-8\">\n            <div className=\"text-xl font-bold\">🎮 SHACK</div>\n            <div className=\"flex space-x-4\">\n              <a href=\"/game\" className=\"nav-link\">Dashboard</a>\n              <a href=\"/game/scanner\" className=\"nav-link\">Scanner</a>\n              <a href=\"/game/chat\" className=\"nav-link\">Chat</a>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-sm text-text-muted\">Modo Teste</span>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\n// Página Principal do Jogo Simplificada\nconst SimpleGamePage: React.FC = () => {\n  console.log('SimpleGamePage - Renderizando...');\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary\">\n      <SimpleNavigation />\n      \n      <Routes>\n        <Route path=\"/\" element={<SimpleDashboard />} />\n        <Route path=\"/scanner\" element={<SimpleScanner />} />\n        <Route path=\"/chat\" element={<SimpleChat />} />\n        <Route path=\"*\" element={<SimpleDashboard />} />\n      </Routes>\n    </div>\n  );\n};\n\nexport default SimpleGamePage;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,OAAO,QAAQ,qBAAqB;;AAE7C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGP,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEQ,aAAa;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGV,SAAS,CAAC,CAAC;EAErFJ,SAAS,CAAC,MAAM;IACd,IAAI,CAACc,aAAa,IAAI,CAACF,eAAe,EAAE;MACtCG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/DH,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACC,aAAa,EAAEF,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAEpD;EACA,MAAMI,UAAU,GAAGN,aAAa,IAAI;IAClCO,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE;EACX,CAAC;EAED,oBACEd,OAAA;IAAKe,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBhB,OAAA;MAAKe,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChB,OAAA;QAAKe,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhB,OAAA;UAAIe,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpB,OAAA;UAAGe,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,sBACT,eAAAhB,OAAA;YAAAgB,QAAA,EAAS,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,KAAI;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,KAChE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDhB,OAAA;UAAKe,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BhB,OAAA;YAAKe,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhB,OAAA;cAAIe,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEpB,OAAA;cAAKe,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDhB,OAAA;gBAAKe,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,eAC/ChB,OAAA;kBAAKe,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhB,OAAA;oBAAKe,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9CX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACC,MAAM,CAACW,cAAc,CAAC;kBAAC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACNpB,OAAA;oBAAKe,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDhB,OAAA;kBAAKe,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhB,OAAA;oBAAKe,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC/CX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACE;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACNpB,OAAA;oBAAKe,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,eACnDhB,OAAA;kBAAKe,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhB,OAAA;oBAAKe,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAChDX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACG;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACNpB,OAAA;oBAAKe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,eACnDhB,OAAA;kBAAKe,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhB,OAAA;oBAAKe,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAChDX,eAAe,GAAG,KAAK,GAAGK,UAAU,CAACI;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACNpB,OAAA;oBAAKe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpB,OAAA;UAAAgB,QAAA,eACEhB,OAAA;YAAKe,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhB,OAAA;cAAIe,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DpB,OAAA;cAAKe,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhB,OAAA;gBAAQe,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpB,OAAA;gBAAQe,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpB,OAAA;gBAAQe,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAEtC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpB,OAAA;gBAAQe,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpB,OAAA;QAAKe,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhB,OAAA;UAAKe,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBhB,OAAA;YAAIe,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrEpB,OAAA;YAAKe,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhB,OAAA;cAAKe,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EhB,OAAA;gBAAKe,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChB,OAAA;kBAAKe,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,eACxFhB,OAAA;oBAAMe,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNpB,OAAA;kBAAAgB,QAAA,gBACEhB,OAAA;oBAAKe,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChDpB,OAAA;oBAAKe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eAENpB,OAAA;cAAKe,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EhB,OAAA;gBAAKe,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChB,OAAA;kBAAKe,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,eACvFhB,OAAA;oBAAMe,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNpB,OAAA;kBAAAgB,QAAA,gBACEhB,OAAA;oBAAKe,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjDpB,OAAA;oBAAKe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eAENpB,OAAA;cAAKe,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EhB,OAAA;gBAAKe,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChB,OAAA;kBAAKe,SAAS,EAAC,4EAA4E;kBAAAC,QAAA,eACzFhB,OAAA;oBAAMe,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNpB,OAAA;kBAAAgB,QAAA,gBACEhB,OAAA;oBAAKe,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjDpB,OAAA;oBAAKe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAlB,EAAA,CAlJMD,eAAyB;EAAA,QACZL,aAAa,EAC4CC,SAAS;AAAA;AAAA0B,EAAA,GAF/EtB,eAAyB;AAmJ/B,MAAMuB,aAAuB,GAAGA,CAAA,kBAC9BxB,OAAA;EAAKe,SAAS,EAAC,KAAK;EAAAC,QAAA,eAClBhB,OAAA;IAAKe,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChChB,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BhB,OAAA;QAAIe,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvDpB,OAAA;QAAGe,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAiD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACzFpB,OAAA;QAAKe,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5ChB,OAAA;UAAKe,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCpB,OAAA;UAAAgB,QAAA,EAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACK,GAAA,GAbID,aAAuB;AAe7B,MAAME,UAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM;IAAEC,QAAQ;IAAEC,iBAAiB;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGlC,OAAO,CAAC,CAAC;EACvF,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,KAAK,CAAC2C,QAAQ,CAAC,EAAE,CAAC;EAEtD1C,SAAS,CAAC,MAAM;IACd,IAAImC,QAAQ,CAACQ,MAAM,KAAK,CAAC,IAAI,CAACP,iBAAiB,EAAE;MAC/CrB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnDqB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACF,QAAQ,CAACQ,MAAM,EAAEP,iBAAiB,EAAEC,YAAY,CAAC,CAAC;EAEtD,MAAMO,iBAAiB,GAAG,MAAOC,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIN,UAAU,CAACO,IAAI,CAAC,CAAC,IAAI,CAACR,SAAS,EAAE;MACnC,MAAMD,WAAW,CAACE,UAAU,CAACO,IAAI,CAAC,CAAC,CAAC;MACpCN,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,oBACElC,OAAA;IAAKe,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBhB,OAAA;MAAKe,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChChB,OAAA;QAAKe,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhB,OAAA;UAAIe,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAG3DpB,OAAA;UAAKe,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EACrEa,iBAAiB,gBAChB7B,OAAA;YAAKe,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChB,OAAA;cAAKe,SAAS,EAAC;YAA2E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,2BAEnG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GACJQ,QAAQ,CAACQ,MAAM,KAAK,CAAC,gBACvBpC,OAAA;YAAKe,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChB,OAAA;cAAKe,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCpB,OAAA;cAAAgB,QAAA,EAAG;YAAgD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,gBAENpB,OAAA;YAAKe,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBY,QAAQ,CAACa,GAAG,CAAEC,OAAO,iBACpB1C,OAAA;cAAsBe,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAC1DhB,OAAA;gBAAKe,SAAS,EAAC,gGAAgG;gBAAAC,QAAA,EAC5G0B,OAAO,CAACC,OAAO,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBhB,OAAA;kBAAKe,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/ChB,OAAA;oBAAMe,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAE0B,OAAO,CAACC;kBAAO;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9DpB,OAAA;oBAAMe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EACtC,IAAI8B,IAAI,CAACJ,OAAO,CAACK,SAAS,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNpB,OAAA;kBAAGe,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAE0B,OAAO,CAACO;gBAAQ;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA,GAZEsB,OAAO,CAACQ,EAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaf,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNpB,OAAA;UAAMmD,QAAQ,EAAEd,iBAAkB;UAACtB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3DhB,OAAA;YACEoD,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEpB,UAAW;YAClBqB,QAAQ,EAAGhB,CAAC,IAAKJ,aAAa,CAACI,CAAC,CAACiB,MAAM,CAACF,KAAK,CAAE;YAC/CG,WAAW,EAAC,wBAAwB;YACpCzC,SAAS,EAAC,kHAAkH;YAC5H0C,QAAQ,EAAEzB;UAAU;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACFpB,OAAA;YACEoD,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAE,CAACxB,UAAU,CAACO,IAAI,CAAC,CAAC,IAAIR,SAAU;YAC1CjB,SAAS,EAAE,sDACT,CAACkB,UAAU,CAACO,IAAI,CAAC,CAAC,IAAIR,SAAS,GAC3B,8CAA8C,GAC9C,0CAA0C,EAC7C;YAAAhB,QAAA,EAEFgB,SAAS,GAAG,KAAK,GAAG;UAAQ;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAO,GAAA,CAvFMD,UAAoB;EAAA,QACsD5B,OAAO;AAAA;AAAA4D,GAAA,GADjFhC,UAAoB;AAwF1B,MAAMiC,gBAA0B,GAAGA,CAAA,KAAM;EACvC,oBACE3D,OAAA;IAAKe,SAAS,EAAC,gDAAgD;IAAAC,QAAA,eAC7DhB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrChB,OAAA;QAAKe,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDhB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChB,OAAA;YAAKe,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjDpB,OAAA;YAAKe,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhB,OAAA;cAAG4D,IAAI,EAAC,OAAO;cAAC7C,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClDpB,OAAA;cAAG4D,IAAI,EAAC,eAAe;cAAC7C,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDpB,OAAA;cAAG4D,IAAI,EAAC,YAAY;cAAC7C,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1ChB,OAAA;YAAMe,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAyC,GAAA,GAtBMF,gBAA0B;AAuBhC,MAAMG,cAAwB,GAAGA,CAAA,KAAM;EACrCtD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAE/C,oBACET,OAAA;IAAKe,SAAS,EAAC,8CAA8C;IAAAC,QAAA,gBAC3DhB,OAAA,CAAC2D,gBAAgB;MAAA1C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpBpB,OAAA,CAACN,MAAM;MAAAsB,QAAA,gBACLhB,OAAA,CAACL,KAAK;QAACoE,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEhE,OAAA,CAACC,eAAe;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChDpB,OAAA,CAACL,KAAK;QAACoE,IAAI,EAAC,UAAU;QAACC,OAAO,eAAEhE,OAAA,CAACwB,aAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDpB,OAAA,CAACL,KAAK;QAACoE,IAAI,EAAC,OAAO;QAACC,OAAO,eAAEhE,OAAA,CAAC0B,UAAU;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CpB,OAAA,CAACL,KAAK;QAACoE,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEhE,OAAA,CAACC,eAAe;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC6C,GAAA,GAfIH,cAAwB;AAiB9B,eAAeA,cAAc;AAAC,IAAAvC,EAAA,EAAAE,GAAA,EAAAiC,GAAA,EAAAG,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAA3C,EAAA;AAAA2C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}