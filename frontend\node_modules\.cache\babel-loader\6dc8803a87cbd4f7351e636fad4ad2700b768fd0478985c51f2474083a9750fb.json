{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\";\nimport React from 'react';\nimport { ArrowUpIcon, ArrowDownIcon, ShieldExclamationIcon, CpuChipIcon, UserIcon, ClockIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Dados de exemplo - em produção viriam da API\nconst mockActivities = [{\n  id: '1',\n  type: 'money_gained',\n  title: 'Exploit Realizado',\n  description: 'Você exploitou ************* e ganhou dinheiro',\n  amount: 1500,\n  timestamp: '2024-01-15T10:30:00Z',\n  severity: 'low'\n}, {\n  id: '2',\n  type: 'attack',\n  title: 'Tentativa de Invasão',\n  description: 'Alguém tentou exploitar seu sistema',\n  timestamp: '2024-01-15T09:15:00Z',\n  severity: 'medium'\n}, {\n  id: '3',\n  type: 'upgrade',\n  title: 'Upgrade Realizado',\n  description: 'CPU atualizado para nível 5',\n  timestamp: '2024-01-15T08:45:00Z',\n  severity: 'low'\n}, {\n  id: '4',\n  type: 'money_lost',\n  title: 'Sistema Exploitado',\n  description: 'Você perdeu dinheiro em um ataque',\n  amount: 800,\n  timestamp: '2024-01-15T07:20:00Z',\n  severity: 'high'\n}, {\n  id: '5',\n  type: 'defense',\n  title: 'Ataque Bloqueado',\n  description: 'Seu firewall bloqueou uma tentativa de invasão',\n  timestamp: '2024-01-15T06:10:00Z',\n  severity: 'low'\n}];\nconst RecentActivity = ({\n  activities = mockActivities,\n  isLoading = false\n}) => {\n  const getActivityIcon = type => {\n    switch (type) {\n      case 'money_gained':\n        return /*#__PURE__*/_jsxDEV(ArrowUpIcon, {\n          className: \"h-5 w-5 text-green-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 16\n        }, this);\n      case 'money_lost':\n        return /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n          className: \"h-5 w-5 text-red-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 16\n        }, this);\n      case 'attack':\n        return /*#__PURE__*/_jsxDEV(ShieldExclamationIcon, {\n          className: \"h-5 w-5 text-orange-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 16\n        }, this);\n      case 'defense':\n        return /*#__PURE__*/_jsxDEV(ShieldExclamationIcon, {\n          className: \"h-5 w-5 text-blue-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 16\n        }, this);\n      case 'upgrade':\n        return /*#__PURE__*/_jsxDEV(CpuChipIcon, {\n          className: \"h-5 w-5 text-purple-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 16\n        }, this);\n      case 'login':\n        return /*#__PURE__*/_jsxDEV(UserIcon, {\n          className: \"h-5 w-5 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ClockIcon, {\n          className: \"h-5 w-5 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getActivityColor = (type, severity) => {\n    if (severity === 'high') return 'border-l-red-500';\n    if (severity === 'medium') return 'border-l-orange-500';\n    switch (type) {\n      case 'money_gained':\n        return 'border-l-green-500';\n      case 'money_lost':\n        return 'border-l-red-500';\n      case 'attack':\n        return 'border-l-orange-500';\n      case 'defense':\n        return 'border-l-blue-500';\n      case 'upgrade':\n        return 'border-l-purple-500';\n      default:\n        return 'border-l-gray-500';\n    }\n  };\n  const formatTime = timestamp => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'Agora';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  };\n  const formatMoney = amount => {\n    return new Intl.NumberFormat('pt-BR', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 0\n    }).format(amount);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-text-primary mb-4\",\n        children: \"Atividade Recente\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 p-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-bg-tertiary rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-bg-tertiary rounded w-3/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-3 bg-bg-tertiary rounded w-1/2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-3 bg-bg-tertiary rounded w-8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-text-primary\",\n        children: \"Atividade Recente\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"text-accent-blue hover:text-primary-light text-sm font-medium transition-colors\",\n        children: \"Ver todas\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-1\",\n      children: activities.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n          className: \"h-12 w-12 text-text-muted mx-auto mb-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-muted\",\n          children: \"Nenhuma atividade recente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this) : activities.map(activity => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                flex items-center space-x-3 p-3 rounded-lg border-l-4 \n                bg-bg-tertiary hover:bg-bg-primary transition-colors cursor-pointer\n                ${getActivityColor(activity.type, activity.severity)}\n              `,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0 w-10 h-10 bg-bg-secondary rounded-full flex items-center justify-center\",\n          children: getActivityIcon(activity.type)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-text-primary truncate\",\n              children: activity.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-text-muted flex-shrink-0 ml-2\",\n              children: formatTime(activity.timestamp)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-text-muted truncate\",\n              children: activity.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this), activity.amount && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `\n                      text-xs font-mono font-bold flex-shrink-0 ml-2\n                      ${activity.type === 'money_gained' ? 'text-green-400' : 'text-red-400'}\n                    `,\n              children: [activity.type === 'money_gained' ? '+' : '-', formatMoney(activity.amount)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 15\n        }, this)]\n      }, activity.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 pt-4 border-t border-border-color\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-3 gap-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-bold text-green-400\",\n            children: activities.filter(a => a.type === 'money_gained').length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-text-muted\",\n            children: \"Ganhos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-bold text-orange-400\",\n            children: activities.filter(a => a.type === 'attack').length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-text-muted\",\n            children: \"Ataques\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-bold text-blue-400\",\n            children: activities.filter(a => a.type === 'defense').length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-text-muted\",\n            children: \"Defesas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 5\n  }, this);\n};\n_c = RecentActivity;\nexport default RecentActivity;\nvar _c;\n$RefreshReg$(_c, \"RecentActivity\");", "map": {"version": 3, "names": ["React", "ArrowUpIcon", "ArrowDownIcon", "ShieldExclamationIcon", "CpuChipIcon", "UserIcon", "ClockIcon", "jsxDEV", "_jsxDEV", "mockActivities", "id", "type", "title", "description", "amount", "timestamp", "severity", "RecentActivity", "activities", "isLoading", "getActivityIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getActivityColor", "formatTime", "date", "Date", "now", "diffInMinutes", "Math", "floor", "getTime", "diffInHours", "diffInDays", "formatMoney", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "children", "Array", "map", "_", "i", "length", "activity", "filter", "a", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/components/dashboard/RecentActivity.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  ArrowUpIcon,\n  ArrowDownIcon,\n  ShieldExclamationIcon,\n  CpuChipIcon,\n  UserIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\n\ninterface ActivityItem {\n  id: string;\n  type: 'money_gained' | 'money_lost' | 'attack' | 'defense' | 'upgrade' | 'login';\n  title: string;\n  description: string;\n  amount?: number;\n  timestamp: string;\n  severity?: 'low' | 'medium' | 'high';\n}\n\ninterface RecentActivityProps {\n  activities?: ActivityItem[];\n  isLoading?: boolean;\n}\n\n// Dados de exemplo - em produção viriam da API\nconst mockActivities: ActivityItem[] = [\n  {\n    id: '1',\n    type: 'money_gained',\n    title: 'Exploit Realizado',\n    description: 'Você exploitou ************* e ganhou dinheiro',\n    amount: 1500,\n    timestamp: '2024-01-15T10:30:00Z',\n    severity: 'low'\n  },\n  {\n    id: '2',\n    type: 'attack',\n    title: 'Tentativa de Invasão',\n    description: 'Alguém tentou exploitar seu sistema',\n    timestamp: '2024-01-15T09:15:00Z',\n    severity: 'medium'\n  },\n  {\n    id: '3',\n    type: 'upgrade',\n    title: 'Upgrade Realizado',\n    description: 'CPU atualizado para nível 5',\n    timestamp: '2024-01-15T08:45:00Z',\n    severity: 'low'\n  },\n  {\n    id: '4',\n    type: 'money_lost',\n    title: 'Sistema Exploitado',\n    description: 'Você perdeu dinheiro em um ataque',\n    amount: 800,\n    timestamp: '2024-01-15T07:20:00Z',\n    severity: 'high'\n  },\n  {\n    id: '5',\n    type: 'defense',\n    title: 'Ataque Bloqueado',\n    description: 'Seu firewall bloqueou uma tentativa de invasão',\n    timestamp: '2024-01-15T06:10:00Z',\n    severity: 'low'\n  }\n];\n\nconst RecentActivity: React.FC<RecentActivityProps> = ({ \n  activities = mockActivities, \n  isLoading = false \n}) => {\n  const getActivityIcon = (type: string) => {\n    switch (type) {\n      case 'money_gained':\n        return <ArrowUpIcon className=\"h-5 w-5 text-green-400\" />;\n      case 'money_lost':\n        return <ArrowDownIcon className=\"h-5 w-5 text-red-400\" />;\n      case 'attack':\n        return <ShieldExclamationIcon className=\"h-5 w-5 text-orange-400\" />;\n      case 'defense':\n        return <ShieldExclamationIcon className=\"h-5 w-5 text-blue-400\" />;\n      case 'upgrade':\n        return <CpuChipIcon className=\"h-5 w-5 text-purple-400\" />;\n      case 'login':\n        return <UserIcon className=\"h-5 w-5 text-gray-400\" />;\n      default:\n        return <ClockIcon className=\"h-5 w-5 text-gray-400\" />;\n    }\n  };\n\n  const getActivityColor = (type: string, severity?: string) => {\n    if (severity === 'high') return 'border-l-red-500';\n    if (severity === 'medium') return 'border-l-orange-500';\n    \n    switch (type) {\n      case 'money_gained':\n        return 'border-l-green-500';\n      case 'money_lost':\n        return 'border-l-red-500';\n      case 'attack':\n        return 'border-l-orange-500';\n      case 'defense':\n        return 'border-l-blue-500';\n      case 'upgrade':\n        return 'border-l-purple-500';\n      default:\n        return 'border-l-gray-500';\n    }\n  };\n\n  const formatTime = (timestamp: string) => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'Agora';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  };\n\n  const formatMoney = (amount: number) => {\n    return new Intl.NumberFormat('pt-BR', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 0,\n    }).format(amount);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"card\">\n        <h3 className=\"text-lg font-semibold text-text-primary mb-4\">\n          Atividade Recente\n        </h3>\n        <div className=\"space-y-3\">\n          {[...Array(5)].map((_, i) => (\n            <div key={i} className=\"animate-pulse\">\n              <div className=\"flex items-center space-x-3 p-3\">\n                <div className=\"w-10 h-10 bg-bg-tertiary rounded-full\" />\n                <div className=\"flex-1 space-y-2\">\n                  <div className=\"h-4 bg-bg-tertiary rounded w-3/4\" />\n                  <div className=\"h-3 bg-bg-tertiary rounded w-1/2\" />\n                </div>\n                <div className=\"h-3 bg-bg-tertiary rounded w-8\" />\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"card\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-text-primary\">\n          Atividade Recente\n        </h3>\n        <button className=\"text-accent-blue hover:text-primary-light text-sm font-medium transition-colors\">\n          Ver todas\n        </button>\n      </div>\n\n      <div className=\"space-y-1\">\n        {activities.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <ClockIcon className=\"h-12 w-12 text-text-muted mx-auto mb-3\" />\n            <p className=\"text-text-muted\">Nenhuma atividade recente</p>\n          </div>\n        ) : (\n          activities.map((activity) => (\n            <div\n              key={activity.id}\n              className={`\n                flex items-center space-x-3 p-3 rounded-lg border-l-4 \n                bg-bg-tertiary hover:bg-bg-primary transition-colors cursor-pointer\n                ${getActivityColor(activity.type, activity.severity)}\n              `}\n            >\n              {/* Ícone */}\n              <div className=\"flex-shrink-0 w-10 h-10 bg-bg-secondary rounded-full flex items-center justify-center\">\n                {getActivityIcon(activity.type)}\n              </div>\n\n              {/* Conteúdo */}\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between\">\n                  <p className=\"text-sm font-medium text-text-primary truncate\">\n                    {activity.title}\n                  </p>\n                  <span className=\"text-xs text-text-muted flex-shrink-0 ml-2\">\n                    {formatTime(activity.timestamp)}\n                  </span>\n                </div>\n                \n                <div className=\"flex items-center justify-between mt-1\">\n                  <p className=\"text-xs text-text-muted truncate\">\n                    {activity.description}\n                  </p>\n                  \n                  {activity.amount && (\n                    <span className={`\n                      text-xs font-mono font-bold flex-shrink-0 ml-2\n                      ${activity.type === 'money_gained' ? 'text-green-400' : 'text-red-400'}\n                    `}>\n                      {activity.type === 'money_gained' ? '+' : '-'}\n                      {formatMoney(activity.amount)}\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      {/* Resumo de estatísticas */}\n      <div className=\"mt-4 pt-4 border-t border-border-color\">\n        <div className=\"grid grid-cols-3 gap-4 text-center\">\n          <div>\n            <div className=\"text-lg font-bold text-green-400\">\n              {activities.filter(a => a.type === 'money_gained').length}\n            </div>\n            <div className=\"text-xs text-text-muted\">Ganhos</div>\n          </div>\n          \n          <div>\n            <div className=\"text-lg font-bold text-orange-400\">\n              {activities.filter(a => a.type === 'attack').length}\n            </div>\n            <div className=\"text-xs text-text-muted\">Ataques</div>\n          </div>\n          \n          <div>\n            <div className=\"text-lg font-bold text-blue-400\">\n              {activities.filter(a => a.type === 'defense').length}\n            </div>\n            <div className=\"text-xs text-text-muted\">Defesas</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RecentActivity;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,WAAW,EACXC,aAAa,EACbC,qBAAqB,EACrBC,WAAW,EACXC,QAAQ,EACRC,SAAS,QACJ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiBrC;AACA,MAAMC,cAA8B,GAAG,CACrC;EACEC,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,mBAAmB;EAC1BC,WAAW,EAAE,gDAAgD;EAC7DC,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,sBAAsB;EACjCC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEN,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EAAE,qCAAqC;EAClDE,SAAS,EAAE,sBAAsB;EACjCC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEN,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,mBAAmB;EAC1BC,WAAW,EAAE,6BAA6B;EAC1CE,SAAS,EAAE,sBAAsB;EACjCC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEN,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE,mCAAmC;EAChDC,MAAM,EAAE,GAAG;EACXC,SAAS,EAAE,sBAAsB;EACjCC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEN,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,kBAAkB;EACzBC,WAAW,EAAE,gDAAgD;EAC7DE,SAAS,EAAE,sBAAsB;EACjCC,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,UAAU,GAAGT,cAAc;EAC3BU,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,eAAe,GAAIT,IAAY,IAAK;IACxC,QAAQA,IAAI;MACV,KAAK,cAAc;QACjB,oBAAOH,OAAA,CAACP,WAAW;UAACoB,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,YAAY;QACf,oBAAOjB,OAAA,CAACN,aAAa;UAACmB,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,QAAQ;QACX,oBAAOjB,OAAA,CAACL,qBAAqB;UAACkB,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtE,KAAK,SAAS;QACZ,oBAAOjB,OAAA,CAACL,qBAAqB;UAACkB,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpE,KAAK,SAAS;QACZ,oBAAOjB,OAAA,CAACJ,WAAW;UAACiB,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,OAAO;QACV,oBAAOjB,OAAA,CAACH,QAAQ;UAACgB,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD;QACE,oBAAOjB,OAAA,CAACF,SAAS;UAACe,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC1D;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACf,IAAY,EAAEK,QAAiB,KAAK;IAC5D,IAAIA,QAAQ,KAAK,MAAM,EAAE,OAAO,kBAAkB;IAClD,IAAIA,QAAQ,KAAK,QAAQ,EAAE,OAAO,qBAAqB;IAEvD,QAAQL,IAAI;MACV,KAAK,cAAc;QACjB,OAAO,oBAAoB;MAC7B,KAAK,YAAY;QACf,OAAO,kBAAkB;MAC3B,KAAK,QAAQ;QACX,OAAO,qBAAqB;MAC9B,KAAK,SAAS;QACZ,OAAO,mBAAmB;MAC5B,KAAK,SAAS;QACZ,OAAO,qBAAqB;MAC9B;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;EAED,MAAMgB,UAAU,GAAIZ,SAAiB,IAAK;IACxC,MAAMa,IAAI,GAAG,IAAIC,IAAI,CAACd,SAAS,CAAC;IAChC,MAAMe,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGN,IAAI,CAACM,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,OAAO;IACrC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAII,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB,CAAC;EAED,MAAMC,WAAW,GAAIvB,MAAc,IAAK;IACtC,OAAO,IAAIwB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAAC7B,MAAM,CAAC;EACnB,CAAC;EAED,IAAIK,SAAS,EAAE;IACb,oBACEX,OAAA;MAAKa,SAAS,EAAC,MAAM;MAAAuB,QAAA,gBACnBpC,OAAA;QAAIa,SAAS,EAAC,8CAA8C;QAAAuB,QAAA,EAAC;MAE7D;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLjB,OAAA;QAAKa,SAAS,EAAC,WAAW;QAAAuB,QAAA,EACvB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBxC,OAAA;UAAaa,SAAS,EAAC,eAAe;UAAAuB,QAAA,eACpCpC,OAAA;YAAKa,SAAS,EAAC,iCAAiC;YAAAuB,QAAA,gBAC9CpC,OAAA;cAAKa,SAAS,EAAC;YAAuC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDjB,OAAA;cAAKa,SAAS,EAAC,kBAAkB;cAAAuB,QAAA,gBAC/BpC,OAAA;gBAAKa,SAAS,EAAC;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDjB,OAAA;gBAAKa,SAAS,EAAC;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACNjB,OAAA;cAAKa,SAAS,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC,GAREuB,CAAC;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASN,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEjB,OAAA;IAAKa,SAAS,EAAC,MAAM;IAAAuB,QAAA,gBACnBpC,OAAA;MAAKa,SAAS,EAAC,wCAAwC;MAAAuB,QAAA,gBACrDpC,OAAA;QAAIa,SAAS,EAAC,yCAAyC;QAAAuB,QAAA,EAAC;MAExD;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLjB,OAAA;QAAQa,SAAS,EAAC,iFAAiF;QAAAuB,QAAA,EAAC;MAEpG;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENjB,OAAA;MAAKa,SAAS,EAAC,WAAW;MAAAuB,QAAA,EACvB1B,UAAU,CAAC+B,MAAM,KAAK,CAAC,gBACtBzC,OAAA;QAAKa,SAAS,EAAC,kBAAkB;QAAAuB,QAAA,gBAC/BpC,OAAA,CAACF,SAAS;UAACe,SAAS,EAAC;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEjB,OAAA;UAAGa,SAAS,EAAC,iBAAiB;UAAAuB,QAAA,EAAC;QAAyB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,GAENP,UAAU,CAAC4B,GAAG,CAAEI,QAAQ,iBACtB1C,OAAA;QAEEa,SAAS,EAAE;AACzB;AACA;AACA,kBAAkBK,gBAAgB,CAACwB,QAAQ,CAACvC,IAAI,EAAEuC,QAAQ,CAAClC,QAAQ,CAAC;AACpE,eAAgB;QAAA4B,QAAA,gBAGFpC,OAAA;UAAKa,SAAS,EAAC,uFAAuF;UAAAuB,QAAA,EACnGxB,eAAe,CAAC8B,QAAQ,CAACvC,IAAI;QAAC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGNjB,OAAA;UAAKa,SAAS,EAAC,gBAAgB;UAAAuB,QAAA,gBAC7BpC,OAAA;YAAKa,SAAS,EAAC,mCAAmC;YAAAuB,QAAA,gBAChDpC,OAAA;cAAGa,SAAS,EAAC,gDAAgD;cAAAuB,QAAA,EAC1DM,QAAQ,CAACtC;YAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACJjB,OAAA;cAAMa,SAAS,EAAC,4CAA4C;cAAAuB,QAAA,EACzDjB,UAAU,CAACuB,QAAQ,CAACnC,SAAS;YAAC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENjB,OAAA;YAAKa,SAAS,EAAC,wCAAwC;YAAAuB,QAAA,gBACrDpC,OAAA;cAAGa,SAAS,EAAC,kCAAkC;cAAAuB,QAAA,EAC5CM,QAAQ,CAACrC;YAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,EAEHyB,QAAQ,CAACpC,MAAM,iBACdN,OAAA;cAAMa,SAAS,EAAE;AACrC;AACA,wBAAwB6B,QAAQ,CAACvC,IAAI,KAAK,cAAc,GAAG,gBAAgB,GAAG,cAAc;AAC5F,qBAAsB;cAAAiC,QAAA,GACCM,QAAQ,CAACvC,IAAI,KAAK,cAAc,GAAG,GAAG,GAAG,GAAG,EAC5C0B,WAAW,CAACa,QAAQ,CAACpC,MAAM,CAAC;YAAA;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GAtCDyB,QAAQ,CAACxC,EAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuCb,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNjB,OAAA;MAAKa,SAAS,EAAC,wCAAwC;MAAAuB,QAAA,eACrDpC,OAAA;QAAKa,SAAS,EAAC,oCAAoC;QAAAuB,QAAA,gBACjDpC,OAAA;UAAAoC,QAAA,gBACEpC,OAAA;YAAKa,SAAS,EAAC,kCAAkC;YAAAuB,QAAA,EAC9C1B,UAAU,CAACiC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzC,IAAI,KAAK,cAAc,CAAC,CAACsC;UAAM;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNjB,OAAA;YAAKa,SAAS,EAAC,yBAAyB;YAAAuB,QAAA,EAAC;UAAM;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eAENjB,OAAA;UAAAoC,QAAA,gBACEpC,OAAA;YAAKa,SAAS,EAAC,mCAAmC;YAAAuB,QAAA,EAC/C1B,UAAU,CAACiC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzC,IAAI,KAAK,QAAQ,CAAC,CAACsC;UAAM;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNjB,OAAA;YAAKa,SAAS,EAAC,yBAAyB;YAAAuB,QAAA,EAAC;UAAO;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAENjB,OAAA;UAAAoC,QAAA,gBACEpC,OAAA;YAAKa,SAAS,EAAC,iCAAiC;YAAAuB,QAAA,EAC7C1B,UAAU,CAACiC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzC,IAAI,KAAK,SAAS,CAAC,CAACsC;UAAM;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNjB,OAAA;YAAKa,SAAS,EAAC,yBAAyB;YAAAuB,QAAA,EAAC;UAAO;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC4B,EAAA,GArLIpC,cAA6C;AAuLnD,eAAeA,cAAc;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}