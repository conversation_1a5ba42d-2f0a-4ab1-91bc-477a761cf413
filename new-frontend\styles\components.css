/* Componentes do Jogo */

/* Tela de Login */
.login-container {
    padding: 40px 24px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.app-logo {
    text-align: center;
    margin-bottom: 40px;
}

.logo-icon {
    font-size: 64px;
    margin-bottom: 16px;
}

.app-logo h1 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.app-logo p {
    color: #94a3b8;
    font-size: 14px;
}

.login-form {
    background: rgba(30, 41, 59, 0.3);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 20px;
    padding: 24px;
    backdrop-filter: blur(10px);
}

.quick-test {
    margin-top: 24px;
    padding: 20px;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 16px;
}

.quick-test h3 {
    font-size: 16px;
    margin-bottom: 8px;
    color: #fbbf24;
}

.quick-test p {
    font-size: 14px;
    color: #94a3b8;
    margin-bottom: 16px;
}

/* Home Screen */
.home-container {
    padding: 20px;
}

.player-profile {
    background: rgba(30, 41, 59, 0.4);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 20px;
    padding: 24px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 16px;
}

.player-info h2 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 4px;
}

.player-info p {
    color: #94a3b8;
    font-size: 14px;
}

/* Barras de Progresso */
.progress-bars {
    margin-bottom: 20px;
}

.progress-item {
    margin-bottom: 12px;
}

.progress-item label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #94a3b8;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.progress-bar {
    height: 8px;
    background: rgba(30, 41, 59, 0.8);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 4px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-item span {
    font-size: 12px;
    color: #e2e8f0;
    font-weight: 500;
}

/* Recursos */
.resources {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.resource-item {
    background: rgba(15, 23, 42, 0.6);
    border: 1px solid rgba(148, 163, 184, 0.1);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
}

.resource-icon {
    font-size: 24px;
    display: block;
    margin-bottom: 8px;
}

.resource-label {
    display: block;
    font-size: 12px;
    color: #94a3b8;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.resource-value {
    font-size: 18px;
    font-weight: 700;
    color: #e2e8f0;
}

/* Notificações */
.notifications {
    margin-bottom: 20px;
}

.notification {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    position: relative;
}

.notification.success {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
}

.notification.warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
}

.notification.error {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
}

/* Ações Rápidas */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.action-btn {
    background: rgba(30, 41, 59, 0.4);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 16px;
    padding: 20px 12px;
    color: #e2e8f0;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.action-btn:hover {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateY(-2px);
}

.action-icon {
    font-size: 24px;
}

.action-btn span:last-child {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Apps Grid */
.apps-container {
    padding: 20px;
}

.apps-container h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
}

.apps-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.app-card {
    background: rgba(30, 41, 59, 0.4);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 20px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.app-card:hover {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.app-icon {
    font-size: 40px;
    margin-bottom: 12px;
    display: block;
}

.app-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
}

.app-level {
    font-size: 14px;
    color: #94a3b8;
    margin-bottom: 12px;
}

.app-upgrade-btn {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.app-upgrade-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.app-upgrade-btn:disabled {
    background: #374151;
    color: #6b7280;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Outras telas */
.scanner-container,
.terminal-container,
.chat-container {
    padding: 40px 20px;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.scanner-container h2,
.terminal-container h2,
.chat-container h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 16px;
}

.scanner-container p,
.terminal-container p,
.chat-container p {
    color: #94a3b8;
    font-size: 16px;
}
