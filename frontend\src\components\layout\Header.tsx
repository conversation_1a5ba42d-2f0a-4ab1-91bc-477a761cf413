import React, { useState } from 'react';
import { useAuth } from '../../stores/authStore';
import { useGame } from '../../stores/gameStore';
import {
  Bars3Icon,
  UserCircleIcon,
  CogIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';

interface HeaderProps {
  onMenuToggle?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onMenuToggle }) => {
  const { user, logout } = useAuth();
  const { currentPlayer, playerMoney, playerNick } = useGame();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleLogout = () => {
    logout();
    setShowUserMenu(false);
  };

  return (
    <header className="bg-bg-secondary border-b border-border-color shadow-lg">
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo e Menu Mobile */}
          <div className="flex items-center space-x-4">
            {/* Botão do menu mobile */}
            <button
              onClick={onMenuToggle}
              className="md:hidden p-2 rounded-lg hover:bg-bg-tertiary transition-colors"
            >
              <Bars3Icon className="h-6 w-6 text-text-primary" />
            </button>

            {/* Logo */}
            <div className="flex items-center space-x-3">
              <span className="text-blue-500 text-xl">●</span>
              <span className="font-bold text-xl text-text-primary">SHACK</span>
              <span className="text-blue-400 text-lg">WEB</span>
              <span className="text-blue-300 text-xl">●</span>
            </div>

            {/* Versão */}
            <div className="hidden sm:block">
              <span className="text-xs text-text-muted bg-bg-tertiary px-2 py-1 rounded">
                React v1.0
              </span>
            </div>
          </div>

          {/* Informações do Jogador */}
          <div className="flex items-center space-x-4">
            {/* Dinheiro do jogador */}
            {currentPlayer && (
              <div className="hidden sm:flex items-center space-x-2 bg-bg-tertiary px-3 py-2 rounded-lg">
                <span className="text-accent-green text-lg">💰</span>
                <span className="text-text-primary font-mono font-bold">
                  ${playerMoney.toLocaleString()}
                </span>
              </div>
            )}

            {/* Menu do usuário */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-2 p-2 rounded-lg hover:bg-bg-tertiary transition-colors"
              >
                <UserCircleIcon className="h-8 w-8 text-text-secondary" />
                <div className="hidden sm:block text-left">
                  <div className="text-sm font-medium text-text-primary">
                    {playerNick || user?.nick}
                  </div>
                  <div className="text-xs text-text-muted">
                    {user?.email}
                  </div>
                </div>
              </button>

              {/* Dropdown do usuário */}
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-bg-secondary border border-border-color rounded-lg shadow-xl z-50">
                  <div className="py-2">
                    {/* Informações do usuário */}
                    <div className="px-4 py-2 border-b border-border-color">
                      <div className="text-sm font-medium text-text-primary">
                        {playerNick || user?.nick}
                      </div>
                      <div className="text-xs text-text-muted">
                        {user?.email}
                      </div>
                    </div>

                    {/* Opções do menu */}
                    <button
                      onClick={() => setShowUserMenu(false)}
                      className="w-full flex items-center px-4 py-2 text-sm text-text-secondary hover:text-text-primary hover:bg-bg-tertiary transition-colors"
                    >
                      <CogIcon className="h-4 w-4 mr-3" />
                      Configurações
                    </button>

                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-bg-tertiary transition-colors"
                    >
                      <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3" />
                      Sair
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Barra de status (mobile) */}
        {currentPlayer && (
          <div className="sm:hidden mt-3 flex items-center justify-between bg-bg-tertiary px-3 py-2 rounded-lg">
            <div className="flex items-center space-x-2">
              <span className="text-accent-green">💰</span>
              <span className="text-text-primary font-mono font-bold text-sm">
                ${playerMoney.toLocaleString()}
              </span>
            </div>
            <div className="text-xs text-text-muted">
              CPU: {currentPlayer.cpu} | FW: {currentPlayer.firewall}
            </div>
          </div>
        )}
      </div>

      {/* Overlay para fechar menu */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </header>
  );
};

export default Header;
