{"ast": null, "code": "import { version } from './version';\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `storage-js/${version}`\n};", "map": {"version": 3, "names": ["version", "DEFAULT_HEADERS"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\SHACKWEBGIT\\shackweb1\\frontend\\node_modules\\@supabase\\storage-js\\src\\lib\\constants.ts"], "sourcesContent": ["import { version } from './version'\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `storage-js/${version}` }\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,OAAO,MAAMC,eAAe,GAAG;EAAE,eAAe,EAAE,cAAcD,OAAO;AAAE,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}