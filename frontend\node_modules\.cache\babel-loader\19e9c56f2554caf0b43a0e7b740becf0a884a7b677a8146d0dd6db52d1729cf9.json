{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleGamePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Componente do Dashboard Simplificado\nconst SimpleDashboard = () => {\n  _s();\n  const {\n    user\n  } = useSimpleAuth();\n  const {\n    currentPlayer,\n    isLoadingPlayer,\n    loadPlayerData,\n    hasPlayerData\n  } = usePlayer();\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold mb-2\",\n          children: \"\\uD83C\\uDFAE Dashboard do Jogo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-muted\",\n          children: [\"Bem-vindo de volta, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: (user === null || user === void 0 ? void 0 : user.nick) || 'Jogador'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 33\n          }, this), \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-semibold mb-6\",\n              children: \"\\uD83D\\uDCCA Estat\\xEDsticas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-blue-900 border-blue-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-blue-100\",\n                    children: \"1,250\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-300\",\n                    children: \"Pontos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 48,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-green-900 border-green-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-green-100\",\n                    children: \"15\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 53,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-green-300\",\n                    children: \"N\\xEDvel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 54,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-purple-900 border-purple-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-purple-100\",\n                    children: \"42\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-purple-300\",\n                    children: \"Conquistas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card bg-orange-900 border-orange-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-orange-100\",\n                    children: \"7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 65,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-orange-300\",\n                    children: \"Ranking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"\\u26A1 A\\xE7\\xF5es R\\xE1pidas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-primary\",\n                children: \"\\uD83D\\uDD0D Scanner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-secondary\",\n                children: \"\\uD83D\\uDCAC Chat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-accent\",\n                children: \"\\uD83C\\uDFC6 Loja\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full btn-outline\",\n                children: \"\\u2699\\uFE0F Configura\\xE7\\xF5es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-semibold mb-6\",\n            children: \"\\uD83D\\uDCC8 Atividade Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Pontos ganhos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-text-muted\",\n                    children: \"H\\xE1 2 horas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-green-400 font-bold\",\n                children: \"+150 pts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold\",\n                    children: \"\\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 116,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Nova conquista\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-text-muted\",\n                    children: \"H\\xE1 1 dia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-400 font-bold\",\n                children: \"Explorador\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold\",\n                    children: \"\\u2B06\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Subiu de n\\xEDvel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-text-muted\",\n                    children: \"H\\xE1 3 dias\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-purple-400 font-bold\",\n                children: \"N\\xEDvel 15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n\n// Componentes de placeholder para outras páginas\n_s(SimpleDashboard, \"4ZBWbfZBu+lAD/wW1WhGuHvBS6I=\", false, function () {\n  return [useSimpleAuth, usePlayer];\n});\n_c = SimpleDashboard;\nconst SimpleScanner = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-4\",\n        children: \"\\uD83D\\uDD0D Scanner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-muted mb-6\",\n        children: \"Funcionalidade do scanner ser\\xE1 implementada aqui.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-bg-tertiary p-8 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDEA7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Em desenvolvimento...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 148,\n  columnNumber: 3\n}, this);\n_c2 = SimpleScanner;\nconst SimpleChat = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-4\",\n        children: \"\\uD83D\\uDCAC Chat\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-muted mb-6\",\n        children: \"Sistema de chat ser\\xE1 implementado aqui.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-bg-tertiary p-8 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDEA7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Em desenvolvimento...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 163,\n  columnNumber: 3\n}, this);\n\n// Navegação Simples\n_c3 = SimpleChat;\nconst SimpleNavigation = () => {\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-bg-secondary border-b border-border-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl font-bold\",\n            children: \"\\uD83C\\uDFAE SHACK\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/game\",\n              className: \"nav-link\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/game/scanner\",\n              className: \"nav-link\",\n              children: \"Scanner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/game/chat\",\n              className: \"nav-link\",\n              children: \"Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-text-muted\",\n            children: \"Modo Teste\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n};\n\n// Página Principal do Jogo Simplificada\n_c4 = SimpleNavigation;\nconst SimpleGamePage = () => {\n  console.log('SimpleGamePage - Renderizando...');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-bg-primary text-text-primary\",\n    children: [/*#__PURE__*/_jsxDEV(SimpleNavigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(SimpleDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/scanner\",\n        element: /*#__PURE__*/_jsxDEV(SimpleScanner, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/chat\",\n        element: /*#__PURE__*/_jsxDEV(SimpleChat, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(SimpleDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n};\n_c5 = SimpleGamePage;\nexport default SimpleGamePage;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"SimpleDashboard\");\n$RefreshReg$(_c2, \"SimpleScanner\");\n$RefreshReg$(_c3, \"SimpleChat\");\n$RefreshReg$(_c4, \"SimpleNavigation\");\n$RefreshReg$(_c5, \"SimpleGamePage\");", "map": {"version": 3, "names": ["React", "useEffect", "Routes", "Route", "useSimpleAuth", "usePlayer", "jsxDEV", "_jsxDEV", "SimpleDashboard", "_s", "user", "currentPlayer", "isLoadingPlayer", "loadPlayerData", "hasPlayerData", "console", "log", "player<PERSON><PERSON>", "pontos", "nivel", "conquistas", "ranking", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nick", "_c", "SimpleScanner", "_c2", "SimpleChat", "_c3", "SimpleNavigation", "href", "_c4", "SimpleGamePage", "path", "element", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleGamePage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { usePlayer } from '../stores/playerStore';\nimport { useChat } from '../stores/chatStore';\n\n// Componente do Dashboard Simplificado\nconst SimpleDashboard: React.FC = () => {\n  const { user } = useSimpleAuth();\n  const { currentPlayer, isLoadingPlayer, loadPlayerData, hasPlayerData } = usePlayer();\n\n  useEffect(() => {\n    if (!hasPlayerData && !isLoadingPlayer) {\n      console.log('SimpleDashboard - Carregando dados do jogador...');\n      loadPlayerData();\n    }\n  }, [hasPlayerData, isLoadingPlayer, loadPlayerData]);\n\n  // Usar dados do player se disponível, senão usar dados mockados\n  const playerData = currentPlayer || {\n    pontos: 1250,\n    nivel: 15,\n    conquistas: 42,\n    ranking: 7\n  };\n\n  return (\n    <div className=\"p-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold mb-2\">\n            🎮 Dashboard do Jogo\n          </h1>\n          <p className=\"text-text-muted\">\n            Bem-vindo de volta, <strong>{user?.nick || 'Jogador'}</strong>!\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Stats do Jogador */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"card\">\n              <h2 className=\"text-2xl font-semibold mb-6\">📊 Estatísticas</h2>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"card bg-blue-900 border-blue-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-blue-100\">1,250</div>\n                    <div className=\"text-sm text-blue-300\">Pontos</div>\n                  </div>\n                </div>\n                <div className=\"card bg-green-900 border-green-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-green-100\">15</div>\n                    <div className=\"text-sm text-green-300\">Nível</div>\n                  </div>\n                </div>\n                <div className=\"card bg-purple-900 border-purple-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-purple-100\">42</div>\n                    <div className=\"text-sm text-purple-300\">Conquistas</div>\n                  </div>\n                </div>\n                <div className=\"card bg-orange-900 border-orange-500\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-orange-100\">7</div>\n                    <div className=\"text-sm text-orange-300\">Ranking</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Ações Rápidas */}\n          <div>\n            <div className=\"card\">\n              <h2 className=\"text-xl font-semibold mb-4\">⚡ Ações Rápidas</h2>\n              <div className=\"space-y-3\">\n                <button className=\"w-full btn-primary\">\n                  🔍 Scanner\n                </button>\n                <button className=\"w-full btn-secondary\">\n                  💬 Chat\n                </button>\n                <button className=\"w-full btn-accent\">\n                  🏆 Loja\n                </button>\n                <button className=\"w-full btn-outline\">\n                  ⚙️ Configurações\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Atividade Recente */}\n        <div className=\"mt-8\">\n          <div className=\"card\">\n            <h2 className=\"text-2xl font-semibold mb-6\">📈 Atividade Recente</h2>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold\">+</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium\">Pontos ganhos</div>\n                    <div className=\"text-sm text-text-muted\">Há 2 horas</div>\n                  </div>\n                </div>\n                <div className=\"text-green-400 font-bold\">+150 pts</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold\">🏆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium\">Nova conquista</div>\n                    <div className=\"text-sm text-text-muted\">Há 1 dia</div>\n                  </div>\n                </div>\n                <div className=\"text-blue-400 font-bold\">Explorador</div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-4 bg-bg-tertiary rounded-lg\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3\">\n                    <span className=\"text-white font-bold\">⬆</span>\n                  </div>\n                  <div>\n                    <div className=\"font-medium\">Subiu de nível</div>\n                    <div className=\"text-sm text-text-muted\">Há 3 dias</div>\n                  </div>\n                </div>\n                <div className=\"text-purple-400 font-bold\">Nível 15</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Componentes de placeholder para outras páginas\nconst SimpleScanner: React.FC = () => (\n  <div className=\"p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"card text-center\">\n        <h1 className=\"text-3xl font-bold mb-4\">🔍 Scanner</h1>\n        <p className=\"text-text-muted mb-6\">Funcionalidade do scanner será implementada aqui.</p>\n        <div className=\"bg-bg-tertiary p-8 rounded-lg\">\n          <div className=\"text-6xl mb-4\">🚧</div>\n          <p>Em desenvolvimento...</p>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nconst SimpleChat: React.FC = () => (\n  <div className=\"p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"card text-center\">\n        <h1 className=\"text-3xl font-bold mb-4\">💬 Chat</h1>\n        <p className=\"text-text-muted mb-6\">Sistema de chat será implementado aqui.</p>\n        <div className=\"bg-bg-tertiary p-8 rounded-lg\">\n          <div className=\"text-6xl mb-4\">🚧</div>\n          <p>Em desenvolvimento...</p>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\n// Navegação Simples\nconst SimpleNavigation: React.FC = () => {\n  return (\n    <nav className=\"bg-bg-secondary border-b border-border-primary\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-8\">\n            <div className=\"text-xl font-bold\">🎮 SHACK</div>\n            <div className=\"flex space-x-4\">\n              <a href=\"/game\" className=\"nav-link\">Dashboard</a>\n              <a href=\"/game/scanner\" className=\"nav-link\">Scanner</a>\n              <a href=\"/game/chat\" className=\"nav-link\">Chat</a>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-sm text-text-muted\">Modo Teste</span>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\n// Página Principal do Jogo Simplificada\nconst SimpleGamePage: React.FC = () => {\n  console.log('SimpleGamePage - Renderizando...');\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary\">\n      <SimpleNavigation />\n      \n      <Routes>\n        <Route path=\"/\" element={<SimpleDashboard />} />\n        <Route path=\"/scanner\" element={<SimpleScanner />} />\n        <Route path=\"/chat\" element={<SimpleChat />} />\n        <Route path=\"*\" element={<SimpleDashboard />} />\n      </Routes>\n    </div>\n  );\n};\n\nexport default SimpleGamePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlD;AACA,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGN,aAAa,CAAC,CAAC;EAChC,MAAM;IAAEO,aAAa;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGT,SAAS,CAAC,CAAC;EAErFJ,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,aAAa,IAAI,CAACF,eAAe,EAAE;MACtCG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/DH,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACC,aAAa,EAAEF,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAEpD;EACA,MAAMI,UAAU,GAAGN,aAAa,IAAI;IAClCO,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE;EACX,CAAC;EAED,oBACEd,OAAA;IAAKe,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBhB,OAAA;MAAKe,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChB,OAAA;QAAKe,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhB,OAAA;UAAIe,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpB,OAAA;UAAGe,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,sBACT,eAAAhB,OAAA;YAAAgB,QAAA,EAAS,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,KAAI;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,KAChE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDhB,OAAA;UAAKe,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BhB,OAAA;YAAKe,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhB,OAAA;cAAIe,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEpB,OAAA;cAAKe,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDhB,OAAA;gBAAKe,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,eAC/ChB,OAAA;kBAAKe,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhB,OAAA;oBAAKe,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7DpB,OAAA;oBAAKe,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDhB,OAAA;kBAAKe,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhB,OAAA;oBAAKe,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3DpB,OAAA;oBAAKe,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,eACnDhB,OAAA;kBAAKe,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhB,OAAA;oBAAKe,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DpB,OAAA;oBAAKe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,eACnDhB,OAAA;kBAAKe,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhB,OAAA;oBAAKe,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3DpB,OAAA;oBAAKe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpB,OAAA;UAAAgB,QAAA,eACEhB,OAAA;YAAKe,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhB,OAAA;cAAIe,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DpB,OAAA;cAAKe,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhB,OAAA;gBAAQe,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpB,OAAA;gBAAQe,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpB,OAAA;gBAAQe,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAEtC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpB,OAAA;gBAAQe,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpB,OAAA;QAAKe,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhB,OAAA;UAAKe,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBhB,OAAA;YAAIe,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrEpB,OAAA;YAAKe,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhB,OAAA;cAAKe,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EhB,OAAA;gBAAKe,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChB,OAAA;kBAAKe,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,eACxFhB,OAAA;oBAAMe,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNpB,OAAA;kBAAAgB,QAAA,gBACEhB,OAAA;oBAAKe,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChDpB,OAAA;oBAAKe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eAENpB,OAAA;cAAKe,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EhB,OAAA;gBAAKe,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChB,OAAA;kBAAKe,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,eACvFhB,OAAA;oBAAMe,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNpB,OAAA;kBAAAgB,QAAA,gBACEhB,OAAA;oBAAKe,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjDpB,OAAA;oBAAKe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eAENpB,OAAA;cAAKe,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EhB,OAAA;gBAAKe,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChB,OAAA;kBAAKe,SAAS,EAAC,4EAA4E;kBAAAC,QAAA,eACzFhB,OAAA;oBAAMe,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACNpB,OAAA;kBAAAgB,QAAA,gBACEhB,OAAA;oBAAKe,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjDpB,OAAA;oBAAKe,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAlB,EAAA,CA1IMD,eAAyB;EAAA,QACZJ,aAAa,EAC4CC,SAAS;AAAA;AAAAwB,EAAA,GAF/ErB,eAAyB;AA2I/B,MAAMsB,aAAuB,GAAGA,CAAA,kBAC9BvB,OAAA;EAAKe,SAAS,EAAC,KAAK;EAAAC,QAAA,eAClBhB,OAAA;IAAKe,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChChB,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BhB,OAAA;QAAIe,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvDpB,OAAA;QAAGe,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAiD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACzFpB,OAAA;QAAKe,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5ChB,OAAA;UAAKe,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCpB,OAAA;UAAAgB,QAAA,EAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACI,GAAA,GAbID,aAAuB;AAe7B,MAAME,UAAoB,GAAGA,CAAA,kBAC3BzB,OAAA;EAAKe,SAAS,EAAC,KAAK;EAAAC,QAAA,eAClBhB,OAAA;IAAKe,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChChB,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BhB,OAAA;QAAIe,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDpB,OAAA;QAAGe,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC/EpB,OAAA;QAAKe,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5ChB,OAAA;UAAKe,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCpB,OAAA;UAAAgB,QAAA,EAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;;AAED;AAAAM,GAAA,GAfMD,UAAoB;AAgB1B,MAAME,gBAA0B,GAAGA,CAAA,KAAM;EACvC,oBACE3B,OAAA;IAAKe,SAAS,EAAC,gDAAgD;IAAAC,QAAA,eAC7DhB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrChB,OAAA;QAAKe,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDhB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChB,OAAA;YAAKe,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjDpB,OAAA;YAAKe,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhB,OAAA;cAAG4B,IAAI,EAAC,OAAO;cAACb,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClDpB,OAAA;cAAG4B,IAAI,EAAC,eAAe;cAACb,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDpB,OAAA;cAAG4B,IAAI,EAAC,YAAY;cAACb,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1ChB,OAAA;YAAMe,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAS,GAAA,GAtBMF,gBAA0B;AAuBhC,MAAMG,cAAwB,GAAGA,CAAA,KAAM;EACrCtB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAE/C,oBACET,OAAA;IAAKe,SAAS,EAAC,8CAA8C;IAAAC,QAAA,gBAC3DhB,OAAA,CAAC2B,gBAAgB;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpBpB,OAAA,CAACL,MAAM;MAAAqB,QAAA,gBACLhB,OAAA,CAACJ,KAAK;QAACmC,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEhC,OAAA,CAACC,eAAe;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChDpB,OAAA,CAACJ,KAAK;QAACmC,IAAI,EAAC,UAAU;QAACC,OAAO,eAAEhC,OAAA,CAACuB,aAAa;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDpB,OAAA,CAACJ,KAAK;QAACmC,IAAI,EAAC,OAAO;QAACC,OAAO,eAAEhC,OAAA,CAACyB,UAAU;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CpB,OAAA,CAACJ,KAAK;QAACmC,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEhC,OAAA,CAACC,eAAe;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACa,GAAA,GAfIH,cAAwB;AAiB9B,eAAeA,cAAc;AAAC,IAAAR,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAZ,EAAA;AAAAY,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}