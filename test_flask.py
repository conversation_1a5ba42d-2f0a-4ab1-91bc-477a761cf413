from flask import Flask, jsonify
from flask_cors import CORS

# Criar app Flask simples para teste
app = Flask(__name__)

# Configurar CORS
CORS(app, origins=["http://localhost:3000"])

@app.route('/')
def home():
    return jsonify({
        "sucesso": True,
        "mensagem": "Flask funcionando!",
        "status": "OK"
    })

@app.route('/api/auth/check')
def check_auth():
    return jsonify({
        "sucesso": True,
        "mensagem": "Endpoint de verificação funcionando"
    })

@app.route('/api/test')
def test():
    return jsonify({
        "sucesso": True,
        "mensagem": "API de teste funcionando",
        "dados": {
            "servidor": "Flask",
            "versao": "1.0",
            "status": "online"
        }
    })

if __name__ == '__main__':
    print("🚀 Iniciando Flask de teste...")
    print("📍 Servidor: http://localhost:5000")
    print("🔗 CORS habilitado para: http://localhost:3000")

    try:
        # Tentar diferentes configurações
        print("Tentando iniciar servidor...")
        app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")
        import traceback
        traceback.print_exc()

        # Tentar porta alternativa
        try:
            print("Tentando porta 5001...")
            app.run(host='127.0.0.1', port=5001, debug=False, use_reloader=False)
        except Exception as e2:
            print(f"❌ Erro na porta 5001: {e2}")
