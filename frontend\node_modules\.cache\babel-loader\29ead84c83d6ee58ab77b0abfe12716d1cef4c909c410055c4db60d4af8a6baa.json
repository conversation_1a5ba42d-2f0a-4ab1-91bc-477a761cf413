{"ast": null, "code": "// Serviço para APIs do jogo que se conecta ao Flask backend\nclass GameApiService {\n  constructor() {\n    this.baseURL = 'http://localhost:5000';\n  }\n  async makeRequest(endpoint, options = {}) {\n    const defaultHeaders = {\n      'Content-Type': 'application/json'\n    };\n    const config = {\n      ...options,\n      credentials: 'include',\n      // Importante para sessões Flask\n      headers: {\n        ...defaultHeaders,\n        ...options.headers\n      }\n    };\n    try {\n      console.log(`🌐 Fazendo requisição para: ${this.baseURL}${endpoint}`);\n      const response = await fetch(`${this.baseURL}${endpoint}`, config);\n      console.log(`📡 Resposta recebida: ${response.status} ${response.statusText}`);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n      const data = await response.json();\n      console.log('📦 Dados recebidos:', data);\n      return data;\n    } catch (error) {\n      console.error(`❌ Erro na requisição para ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  // === AUTENTICAÇÃO ===\n  async login(credentials) {\n    return this.makeRequest('/api/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials)\n    });\n  }\n  async register(userData) {\n    return this.makeRequest('/api/auth/register', {\n      method: 'POST',\n      body: JSON.stringify(userData)\n    });\n  }\n  async checkAuth() {\n    return this.makeRequest('/api/auth/check');\n  }\n\n  // === MÉTODO DE TESTE RÁPIDO ===\n  async quickLogin() {\n    try {\n      // Tenta fazer login com usuário de teste\n      console.log('🔐 Tentando login com usuário de teste...');\n      const loginResult = await this.login({\n        email: '<EMAIL>',\n        password: 'test123'\n      });\n      if (loginResult.sucesso) {\n        console.log('✅ Login realizado com sucesso:', loginResult);\n        return loginResult;\n      } else {\n        // Se login falhou, tenta criar usuário\n        console.log('👤 Usuário não existe, criando...');\n        const registerResult = await this.register({\n          email: '<EMAIL>',\n          password: 'test123',\n          nick: 'TestPlayer'\n        });\n        if (registerResult.sucesso) {\n          console.log('✅ Usuário criado e logado:', registerResult);\n          return registerResult;\n        } else {\n          throw new Error(registerResult.mensagem || 'Erro ao criar usuário');\n        }\n      }\n    } catch (error) {\n      console.error('❌ Erro no login rápido:', error);\n      throw error;\n    }\n  }\n\n  // === SCANNER ===\n  async scanTargets() {\n    return this.makeRequest('/api/scan');\n  }\n  async scanSpecificIP(ip) {\n    return this.makeRequest(`/api/scan/ip/${ip}`);\n  }\n\n  // === EXPLOITS ===\n  async exploitTarget(ip) {\n    return this.makeRequest(`/api/alvo/${ip}/exploit`, {\n      method: 'POST'\n    });\n  }\n\n  // === DADOS DO JOGADOR ===\n  async getPlayerData() {\n    return this.makeRequest('/api/jogador');\n  }\n\n  // === CHAT ===\n  async getChatMessages() {\n    return this.makeRequest('/api/chat/mensagens');\n  }\n  async sendChatMessage(message) {\n    return this.makeRequest('/api/chat/enviar', {\n      method: 'POST',\n      body: JSON.stringify({\n        mensagem: message\n      })\n    });\n  }\n\n  // === TRANSFERÊNCIAS ===\n  async transferPoints(targetNick, amount) {\n    return this.makeRequest('/api/transferir', {\n      method: 'POST',\n      body: JSON.stringify({\n        alvo_nick: targetNick,\n        quantidade: amount\n      })\n    });\n  }\n\n  // === LOJA ===\n  async getShopItems() {\n    return this.makeRequest('/api/shop/items');\n  }\n  async buyShopItem(itemId) {\n    return this.makeRequest('/api/shop/comprar', {\n      method: 'POST',\n      body: JSON.stringify({\n        item_id: itemId\n      })\n    });\n  }\n\n  // === UPGRADES ===\n  async upgradeItem(item, quantity = 1) {\n    return this.makeRequest('/api/upgrade', {\n      method: 'POST',\n      body: JSON.stringify({\n        item: item,\n        quantidade: quantity\n      })\n    });\n  }\n\n  // === RANKING ===\n  async getRanking() {\n    return this.makeRequest('/api/ranking');\n  }\n\n  // === LOGS ===\n  async getLogs() {\n    return this.makeRequest('/api/logs');\n  }\n\n  // === TESTE DE CONECTIVIDADE ===\n  async testConnection() {\n    try {\n      const response = await fetch(`${this.baseURL}/`);\n      const data = await response.json();\n      return {\n        success: true,\n        data\n      };\n    } catch (error) {\n      console.error('❌ Erro de conectividade:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  }\n}\nexport const gameApi = new GameApiService();\nexport default gameApi;", "map": {"version": 3, "names": ["GameApiService", "constructor", "baseURL", "makeRequest", "endpoint", "options", "defaultHeaders", "config", "credentials", "headers", "console", "log", "response", "fetch", "status", "statusText", "ok", "Error", "data", "json", "error", "login", "method", "body", "JSON", "stringify", "register", "userData", "checkAuth", "quickLogin", "loginResult", "email", "password", "sucesso", "registerResult", "nick", "mensagem", "scanTargets", "scanSpecificIP", "ip", "exploitTarget", "getPlayerData", "getChatMessages", "sendChatMessage", "message", "transferPoints", "targetNick", "amount", "alvo_nick", "quantidade", "getShopItems", "buyShopItem", "itemId", "item_id", "upgradeItem", "item", "quantity", "getRanking", "getLogs", "testConnection", "success", "gameApi"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/services/gameApi.ts"], "sourcesContent": ["// Serviço para APIs do jogo que se conecta ao Flask backend\nclass GameApiService {\n  private baseURL = 'http://localhost:5000';\n  \n  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {\n    const defaultHeaders = {\n      'Content-Type': 'application/json',\n    };\n\n    const config: RequestInit = {\n      ...options,\n      credentials: 'include', // Importante para sessões Flask\n      headers: {\n        ...defaultHeaders,\n        ...options.headers,\n      },\n    };\n\n    try {\n      console.log(`🌐 Fazendo requisição para: ${this.baseURL}${endpoint}`);\n      \n      const response = await fetch(`${this.baseURL}${endpoint}`, config);\n      \n      console.log(`📡 Resposta recebida: ${response.status} ${response.statusText}`);\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      console.log('📦 Dados recebidos:', data);\n      \n      return data;\n    } catch (error) {\n      console.error(`❌ Erro na requisição para ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  // === AUTENTICAÇÃO ===\n  async login(credentials: { email: string; password: string }) {\n    return this.makeRequest('/api/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    });\n  }\n\n  async register(userData: { email: string; password: string; nick: string }) {\n    return this.makeRequest('/api/auth/register', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async checkAuth() {\n    return this.makeRequest('/api/auth/check');\n  }\n\n  // === MÉTODO DE TESTE RÁPIDO ===\n  async quickLogin() {\n    try {\n      // Tenta fazer login com usuário de teste\n      console.log('🔐 Tentando login com usuário de teste...');\n\n      const loginResult = await this.login({\n        email: '<EMAIL>',\n        password: 'test123'\n      });\n\n      if (loginResult.sucesso) {\n        console.log('✅ Login realizado com sucesso:', loginResult);\n        return loginResult;\n      } else {\n        // Se login falhou, tenta criar usuário\n        console.log('👤 Usuário não existe, criando...');\n\n        const registerResult = await this.register({\n          email: '<EMAIL>',\n          password: 'test123',\n          nick: 'TestPlayer'\n        });\n\n        if (registerResult.sucesso) {\n          console.log('✅ Usuário criado e logado:', registerResult);\n          return registerResult;\n        } else {\n          throw new Error(registerResult.mensagem || 'Erro ao criar usuário');\n        }\n      }\n    } catch (error) {\n      console.error('❌ Erro no login rápido:', error);\n      throw error;\n    }\n  }\n\n  // === SCANNER ===\n  async scanTargets() {\n    return this.makeRequest('/api/scan');\n  }\n\n  async scanSpecificIP(ip: string) {\n    return this.makeRequest(`/api/scan/ip/${ip}`);\n  }\n\n  // === EXPLOITS ===\n  async exploitTarget(ip: string) {\n    return this.makeRequest(`/api/alvo/${ip}/exploit`, {\n      method: 'POST',\n    });\n  }\n\n  // === DADOS DO JOGADOR ===\n  async getPlayerData() {\n    return this.makeRequest('/api/jogador');\n  }\n\n  // === CHAT ===\n  async getChatMessages() {\n    return this.makeRequest('/api/chat/mensagens');\n  }\n\n  async sendChatMessage(message: string) {\n    return this.makeRequest('/api/chat/enviar', {\n      method: 'POST',\n      body: JSON.stringify({ mensagem: message }),\n    });\n  }\n\n  // === TRANSFERÊNCIAS ===\n  async transferPoints(targetNick: string, amount: number) {\n    return this.makeRequest('/api/transferir', {\n      method: 'POST',\n      body: JSON.stringify({\n        alvo_nick: targetNick,\n        quantidade: amount,\n      }),\n    });\n  }\n\n  // === LOJA ===\n  async getShopItems() {\n    return this.makeRequest('/api/shop/items');\n  }\n\n  async buyShopItem(itemId: string) {\n    return this.makeRequest('/api/shop/comprar', {\n      method: 'POST',\n      body: JSON.stringify({ item_id: itemId }),\n    });\n  }\n\n  // === UPGRADES ===\n  async upgradeItem(item: string, quantity: number = 1) {\n    return this.makeRequest('/api/upgrade', {\n      method: 'POST',\n      body: JSON.stringify({\n        item: item,\n        quantidade: quantity,\n      }),\n    });\n  }\n\n  // === RANKING ===\n  async getRanking() {\n    return this.makeRequest('/api/ranking');\n  }\n\n  // === LOGS ===\n  async getLogs() {\n    return this.makeRequest('/api/logs');\n  }\n\n  // === TESTE DE CONECTIVIDADE ===\n  async testConnection() {\n    try {\n      const response = await fetch(`${this.baseURL}/`);\n      const data = await response.json();\n      return { success: true, data };\n    } catch (error) {\n      console.error('❌ Erro de conectividade:', error);\n      return { success: false, error };\n    }\n  }\n}\n\nexport const gameApi = new GameApiService();\nexport default gameApi;\n"], "mappings": "AAAA;AACA,MAAMA,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,OAAO,GAAG,uBAAuB;EAAA;EAEzC,MAAcC,WAAWA,CAACC,QAAgB,EAAEC,OAAoB,GAAG,CAAC,CAAC,EAAgB;IACnF,MAAMC,cAAc,GAAG;MACrB,cAAc,EAAE;IAClB,CAAC;IAED,MAAMC,MAAmB,GAAG;MAC1B,GAAGF,OAAO;MACVG,WAAW,EAAE,SAAS;MAAE;MACxBC,OAAO,EAAE;QACP,GAAGH,cAAc;QACjB,GAAGD,OAAO,CAACI;MACb;IACF,CAAC;IAED,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,+BAA+B,IAAI,CAACT,OAAO,GAAGE,QAAQ,EAAE,CAAC;MAErE,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACX,OAAO,GAAGE,QAAQ,EAAE,EAAEG,MAAM,CAAC;MAElEG,OAAO,CAACC,GAAG,CAAC,yBAAyBC,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACG,UAAU,EAAE,CAAC;MAE9E,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,QAAQL,QAAQ,CAACE,MAAM,KAAKF,QAAQ,CAACG,UAAU,EAAE,CAAC;MACpE;MAEA,MAAMG,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClCT,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,IAAI,CAAC;MAExC,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,6BAA6BhB,QAAQ,GAAG,EAAEgB,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMC,KAAKA,CAACb,WAAgD,EAAE;IAC5D,OAAO,IAAI,CAACL,WAAW,CAAC,iBAAiB,EAAE;MACzCmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjB,WAAW;IAClC,CAAC,CAAC;EACJ;EAEA,MAAMkB,QAAQA,CAACC,QAA2D,EAAE;IAC1E,OAAO,IAAI,CAACxB,WAAW,CAAC,oBAAoB,EAAE;MAC5CmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,QAAQ;IAC/B,CAAC,CAAC;EACJ;EAEA,MAAMC,SAASA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACzB,WAAW,CAAC,iBAAiB,CAAC;EAC5C;;EAEA;EACA,MAAM0B,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF;MACAnB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAExD,MAAMmB,WAAW,GAAG,MAAM,IAAI,CAACT,KAAK,CAAC;QACnCU,KAAK,EAAE,eAAe;QACtBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAIF,WAAW,CAACG,OAAO,EAAE;QACvBvB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmB,WAAW,CAAC;QAC1D,OAAOA,WAAW;MACpB,CAAC,MAAM;QACL;QACApB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAEhD,MAAMuB,cAAc,GAAG,MAAM,IAAI,CAACR,QAAQ,CAAC;UACzCK,KAAK,EAAE,eAAe;UACtBC,QAAQ,EAAE,SAAS;UACnBG,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAID,cAAc,CAACD,OAAO,EAAE;UAC1BvB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuB,cAAc,CAAC;UACzD,OAAOA,cAAc;QACvB,CAAC,MAAM;UACL,MAAM,IAAIjB,KAAK,CAACiB,cAAc,CAACE,QAAQ,IAAI,uBAAuB,CAAC;QACrE;MACF;IACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMiB,WAAWA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAClC,WAAW,CAAC,WAAW,CAAC;EACtC;EAEA,MAAMmC,cAAcA,CAACC,EAAU,EAAE;IAC/B,OAAO,IAAI,CAACpC,WAAW,CAAC,gBAAgBoC,EAAE,EAAE,CAAC;EAC/C;;EAEA;EACA,MAAMC,aAAaA,CAACD,EAAU,EAAE;IAC9B,OAAO,IAAI,CAACpC,WAAW,CAAC,aAAaoC,EAAE,UAAU,EAAE;MACjDjB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMmB,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACtC,WAAW,CAAC,cAAc,CAAC;EACzC;;EAEA;EACA,MAAMuC,eAAeA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACvC,WAAW,CAAC,qBAAqB,CAAC;EAChD;EAEA,MAAMwC,eAAeA,CAACC,OAAe,EAAE;IACrC,OAAO,IAAI,CAACzC,WAAW,CAAC,kBAAkB,EAAE;MAC1CmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEW,QAAQ,EAAEQ;MAAQ,CAAC;IAC5C,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,cAAcA,CAACC,UAAkB,EAAEC,MAAc,EAAE;IACvD,OAAO,IAAI,CAAC5C,WAAW,CAAC,iBAAiB,EAAE;MACzCmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBuB,SAAS,EAAEF,UAAU;QACrBG,UAAU,EAAEF;MACd,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMG,YAAYA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC/C,WAAW,CAAC,iBAAiB,CAAC;EAC5C;EAEA,MAAMgD,WAAWA,CAACC,MAAc,EAAE;IAChC,OAAO,IAAI,CAACjD,WAAW,CAAC,mBAAmB,EAAE;MAC3CmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE4B,OAAO,EAAED;MAAO,CAAC;IAC1C,CAAC,CAAC;EACJ;;EAEA;EACA,MAAME,WAAWA,CAACC,IAAY,EAAEC,QAAgB,GAAG,CAAC,EAAE;IACpD,OAAO,IAAI,CAACrD,WAAW,CAAC,cAAc,EAAE;MACtCmB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnB8B,IAAI,EAAEA,IAAI;QACVN,UAAU,EAAEO;MACd,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,UAAUA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACtD,WAAW,CAAC,cAAc,CAAC;EACzC;;EAEA;EACA,MAAMuD,OAAOA,CAAA,EAAG;IACd,OAAO,IAAI,CAACvD,WAAW,CAAC,WAAW,CAAC;EACtC;;EAEA;EACA,MAAMwD,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM/C,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACX,OAAO,GAAG,CAAC;MAChD,MAAMgB,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC,OAAO;QAAEyC,OAAO,EAAE,IAAI;QAAE1C;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QAAEwC,OAAO,EAAE,KAAK;QAAExC;MAAM,CAAC;IAClC;EACF;AACF;AAEA,OAAO,MAAMyC,OAAO,GAAG,IAAI7D,cAAc,CAAC,CAAC;AAC3C,eAAe6D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}