{"ast": null, "code": "// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const {\n    exact,\n    status,\n    predicate,\n    mutationKey\n  } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(queryKey, (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n    result[key] = val[key];\n    return result;\n  }, {}) : val);\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every(key => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    const aItemsSet = new Set(aItems);\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItemsSet.has(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== \"production\") {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(`Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`);\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (options.queryFn === skipToken) {\n      console.error(`Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`);\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\nexport { addToEnd, addToStart, ensureQueryFn, functionalUpdate, hashKey, hashQueryKeyByOptions, isPlainArray, isPlainObject, isServer, isValidTimeout, keepPreviousData, matchMutation, matchQuery, noop, partialMatchKey, replaceData, replaceEqualDeep, resolveEnabled, resolveStaleTime, shallowEqualObjects, shouldThrowError, skipToken, sleep, timeUntilStale };", "map": {"version": 3, "names": ["isServer", "window", "globalThis", "noop", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "Infinity", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "resolveStaleTime", "query", "resolveEnabled", "enabled", "matchQuery", "filters", "type", "exact", "fetchStatus", "predicate", "query<PERSON><PERSON>", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "isActive", "isStale", "state", "matchMutation", "mutation", "status", "<PERSON><PERSON><PERSON>", "hash<PERSON><PERSON>", "hashFn", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "Object", "keys", "sort", "reduce", "result", "key", "a", "b", "every", "replaceEqualDeep", "array", "is<PERSON><PERSON>A<PERSON>y", "aItems", "aSize", "length", "bItems", "bSize", "copy", "aItemsSet", "Set", "equalItems", "i", "has", "shallowEqualObjects", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "constructor", "prot", "prototype", "hasOwnProperty", "getPrototypeOf", "toString", "call", "sleep", "timeout", "Promise", "resolve", "setTimeout", "replaceData", "prevData", "data", "structuralSharing", "process", "env", "NODE_ENV", "error", "console", "keepPreviousData", "previousData", "addToEnd", "items", "item", "newItems", "slice", "addToStart", "skipToken", "Symbol", "ensureQueryFn", "fetchOptions", "queryFn", "initialPromise", "reject", "Error", "shouldThrowError", "throwOnError", "params"], "sources": ["C:\\Users\\<USER>\\node_modules\\@tanstack\\query-core\\src\\utils.ts"], "sourcesContent": ["import type {\n  DefaultError,\n  Enabled,\n  FetchStatus,\n  MutationKey,\n  MutationStatus,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n  StaleTime,\n  StaleTimeFunction,\n} from './types'\nimport type { Mutation } from './mutation'\nimport type { FetchOptions, Query } from './query'\n\n// TYPES\n\nexport interface QueryFilters<TQueryKey extends QueryKey = QueryKey> {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: TQueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (\n    mutation: Mutation<TData, TError, TVariables, TContext>,\n  ) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: MutationKey\n  /**\n   * Filter by mutation status\n   */\n  status?: MutationStatus\n}\n\nexport type Updater<TInput, TOutput> = TOutput | ((input: TInput) => TOutput)\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in globalThis\n\nexport function noop(): void\nexport function noop(): undefined\nexport function noop() {}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as (_: TInput) => TOutput)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function resolveStaleTime<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  staleTime:\n    | undefined\n    | StaleTimeFunction<TQueryFnData, TError, TData, TQueryKey>,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n): StaleTime | undefined {\n  return typeof staleTime === 'function' ? staleTime(query) : staleTime\n}\n\nexport function resolveEnabled<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  enabled: undefined | Enabled<TQueryFnData, TError, TData, TQueryKey>,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n): boolean | undefined {\n  return typeof enabled === 'function' ? enabled(query) : enabled\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, status, predicate, mutationKey } = filters\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (status && mutation.state.status !== status) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: Pick<QueryOptions<any, any, any, any>, 'queryKeyHashFn'>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query & mutation keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashKey(queryKey: QueryKey | MutationKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean\nexport function partialMatchKey(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aItems = array ? a : Object.keys(a)\n    const aSize = aItems.length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n    const aItemsSet = new Set(aItems)\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      if (\n        ((!array && aItemsSet.has(key)) || array) &&\n        a[key] === undefined &&\n        b[key] === undefined\n      ) {\n        copy[key] = undefined\n        equalItems++\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key])\n        if (copy[key] === a[key] && a[key] !== undefined) {\n          equalItems++\n        }\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects.\n */\nexport function shallowEqualObjects<T extends Record<string, any>>(\n  a: T,\n  b: T | undefined,\n): boolean {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\n// eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has no constructor\n  const ctor = o.constructor\n  if (ctor === undefined) {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Handles Objects created by Object.create(<arbitrary prototype>)\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data) as TData\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== 'production') {\n      try {\n        return replaceEqualDeep(prevData, data)\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`,\n        )\n\n        // Prevent the replaceEqualDeep from being called again down below.\n        throw error\n      }\n    }\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n\nexport function keepPreviousData<T>(\n  previousData: T | undefined,\n): T | undefined {\n  return previousData\n}\n\nexport function addToEnd<T>(items: Array<T>, item: T, max = 0): Array<T> {\n  const newItems = [...items, item]\n  return max && newItems.length > max ? newItems.slice(1) : newItems\n}\n\nexport function addToStart<T>(items: Array<T>, item: T, max = 0): Array<T> {\n  const newItems = [item, ...items]\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems\n}\n\nexport const skipToken = Symbol()\nexport type SkipToken = typeof skipToken\n\nexport function ensureQueryFn<\n  TQueryFnData = unknown,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: {\n    queryFn?: QueryFunction<TQueryFnData, TQueryKey> | SkipToken\n    queryHash?: string\n  },\n  fetchOptions?: FetchOptions<TQueryFnData>,\n): QueryFunction<TQueryFnData, TQueryKey> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`,\n      )\n    }\n  }\n\n  // if we attempt to retry a fetch that was triggered from an initialPromise\n  // when we don't have a queryFn yet, we can't retry, so we just return the already rejected initialPromise\n  // if an observer has already mounted, we will be able to retry with that queryFn\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise!\n  }\n\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () =>\n      Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`))\n  }\n\n  return options.queryFn\n}\n\nexport function shouldThrowError<T extends (...args: Array<any>) => boolean>(\n  throwOnError: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow throwOnError function to override throwing behavior on a per-error basis\n  if (typeof throwOnError === 'function') {\n    return throwOnError(...params)\n  }\n\n  return !!throwOnError\n}\n"], "mappings": ";AA4EO,IAAMA,QAAA,GAAW,OAAOC,MAAA,KAAW,eAAe,UAAUC,UAAA;AAI5D,SAASC,KAAA,EAAO,CAAC;AAEjB,SAASC,iBACdC,OAAA,EACAC,KAAA,EACS;EACT,OAAO,OAAOD,OAAA,KAAY,aACrBA,OAAA,CAAmCC,KAAK,IACzCD,OAAA;AACN;AAEO,SAASE,eAAeC,KAAA,EAAiC;EAC9D,OAAO,OAAOA,KAAA,KAAU,YAAYA,KAAA,IAAS,KAAKA,KAAA,KAAUC,QAAA;AAC9D;AAEO,SAASC,eAAeC,SAAA,EAAmBC,SAAA,EAA4B;EAC5E,OAAOC,IAAA,CAAKC,GAAA,CAAIH,SAAA,IAAaC,SAAA,IAAa,KAAKG,IAAA,CAAKC,GAAA,CAAI,GAAG,CAAC;AAC9D;AAEO,SAASC,iBAMdL,SAAA,EAGAM,KAAA,EACuB;EACvB,OAAO,OAAON,SAAA,KAAc,aAAaA,SAAA,CAAUM,KAAK,IAAIN,SAAA;AAC9D;AAEO,SAASO,eAMdC,OAAA,EACAF,KAAA,EACqB;EACrB,OAAO,OAAOE,OAAA,KAAY,aAAaA,OAAA,CAAQF,KAAK,IAAIE,OAAA;AAC1D;AAEO,SAASC,WACdC,OAAA,EACAJ,KAAA,EACS;EACT,MAAM;IACJK,IAAA,GAAO;IACPC,KAAA;IACAC,WAAA;IACAC,SAAA;IACAC,QAAA;IACAC;EACF,IAAIN,OAAA;EAEJ,IAAIK,QAAA,EAAU;IACZ,IAAIH,KAAA,EAAO;MACT,IAAIN,KAAA,CAAMW,SAAA,KAAcC,qBAAA,CAAsBH,QAAA,EAAUT,KAAA,CAAMa,OAAO,GAAG;QACtE,OAAO;MACT;IACF,WAAW,CAACC,eAAA,CAAgBd,KAAA,CAAMS,QAAA,EAAUA,QAAQ,GAAG;MACrD,OAAO;IACT;EACF;EAEA,IAAIJ,IAAA,KAAS,OAAO;IAClB,MAAMU,QAAA,GAAWf,KAAA,CAAMe,QAAA,CAAS;IAChC,IAAIV,IAAA,KAAS,YAAY,CAACU,QAAA,EAAU;MAClC,OAAO;IACT;IACA,IAAIV,IAAA,KAAS,cAAcU,QAAA,EAAU;MACnC,OAAO;IACT;EACF;EAEA,IAAI,OAAOL,KAAA,KAAU,aAAaV,KAAA,CAAMgB,OAAA,CAAQ,MAAMN,KAAA,EAAO;IAC3D,OAAO;EACT;EAEA,IAAIH,WAAA,IAAeA,WAAA,KAAgBP,KAAA,CAAMiB,KAAA,CAAMV,WAAA,EAAa;IAC1D,OAAO;EACT;EAEA,IAAIC,SAAA,IAAa,CAACA,SAAA,CAAUR,KAAK,GAAG;IAClC,OAAO;EACT;EAEA,OAAO;AACT;AAEO,SAASkB,cACdd,OAAA,EACAe,QAAA,EACS;EACT,MAAM;IAAEb,KAAA;IAAOc,MAAA;IAAQZ,SAAA;IAAWa;EAAY,IAAIjB,OAAA;EAClD,IAAIiB,WAAA,EAAa;IACf,IAAI,CAACF,QAAA,CAASN,OAAA,CAAQQ,WAAA,EAAa;MACjC,OAAO;IACT;IACA,IAAIf,KAAA,EAAO;MACT,IAAIgB,OAAA,CAAQH,QAAA,CAASN,OAAA,CAAQQ,WAAW,MAAMC,OAAA,CAAQD,WAAW,GAAG;QAClE,OAAO;MACT;IACF,WAAW,CAACP,eAAA,CAAgBK,QAAA,CAASN,OAAA,CAAQQ,WAAA,EAAaA,WAAW,GAAG;MACtE,OAAO;IACT;EACF;EAEA,IAAID,MAAA,IAAUD,QAAA,CAASF,KAAA,CAAMG,MAAA,KAAWA,MAAA,EAAQ;IAC9C,OAAO;EACT;EAEA,IAAIZ,SAAA,IAAa,CAACA,SAAA,CAAUW,QAAQ,GAAG;IACrC,OAAO;EACT;EAEA,OAAO;AACT;AAEO,SAASP,sBACdH,QAAA,EACAI,OAAA,EACQ;EACR,MAAMU,MAAA,GAASV,OAAA,EAASW,cAAA,IAAkBF,OAAA;EAC1C,OAAOC,MAAA,CAAOd,QAAQ;AACxB;AAMO,SAASa,QAAQb,QAAA,EAA0C;EAChE,OAAOgB,IAAA,CAAKC,SAAA,CAAUjB,QAAA,EAAU,CAACkB,CAAA,EAAGC,GAAA,KAClCC,aAAA,CAAcD,GAAG,IACbE,MAAA,CAAOC,IAAA,CAAKH,GAAG,EACZI,IAAA,CAAK,EACLC,MAAA,CAAO,CAACC,MAAA,EAAQC,GAAA,KAAQ;IACvBD,MAAA,CAAOC,GAAG,IAAIP,GAAA,CAAIO,GAAG;IACrB,OAAOD,MAAA;EACT,GAAG,CAAC,CAAQ,IACdN,GACN;AACF;AAMO,SAASd,gBAAgBsB,CAAA,EAAQC,CAAA,EAAiB;EACvD,IAAID,CAAA,KAAMC,CAAA,EAAG;IACX,OAAO;EACT;EAEA,IAAI,OAAOD,CAAA,KAAM,OAAOC,CAAA,EAAG;IACzB,OAAO;EACT;EAEA,IAAID,CAAA,IAAKC,CAAA,IAAK,OAAOD,CAAA,KAAM,YAAY,OAAOC,CAAA,KAAM,UAAU;IAC5D,OAAOP,MAAA,CAAOC,IAAA,CAAKM,CAAC,EAAEC,KAAA,CAAOH,GAAA,IAAQrB,eAAA,CAAgBsB,CAAA,CAAED,GAAG,GAAGE,CAAA,CAAEF,GAAG,CAAC,CAAC;EACtE;EAEA,OAAO;AACT;AAQO,SAASI,iBAAiBH,CAAA,EAAQC,CAAA,EAAa;EACpD,IAAID,CAAA,KAAMC,CAAA,EAAG;IACX,OAAOD,CAAA;EACT;EAEA,MAAMI,KAAA,GAAQC,YAAA,CAAaL,CAAC,KAAKK,YAAA,CAAaJ,CAAC;EAE/C,IAAIG,KAAA,IAAUX,aAAA,CAAcO,CAAC,KAAKP,aAAA,CAAcQ,CAAC,GAAI;IACnD,MAAMK,MAAA,GAASF,KAAA,GAAQJ,CAAA,GAAIN,MAAA,CAAOC,IAAA,CAAKK,CAAC;IACxC,MAAMO,KAAA,GAAQD,MAAA,CAAOE,MAAA;IACrB,MAAMC,MAAA,GAASL,KAAA,GAAQH,CAAA,GAAIP,MAAA,CAAOC,IAAA,CAAKM,CAAC;IACxC,MAAMS,KAAA,GAAQD,MAAA,CAAOD,MAAA;IACrB,MAAMG,IAAA,GAAYP,KAAA,GAAQ,EAAC,GAAI,CAAC;IAChC,MAAMQ,SAAA,GAAY,IAAIC,GAAA,CAAIP,MAAM;IAEhC,IAAIQ,UAAA,GAAa;IAEjB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIL,KAAA,EAAOK,CAAA,IAAK;MAC9B,MAAMhB,GAAA,GAAMK,KAAA,GAAQW,CAAA,GAAIN,MAAA,CAAOM,CAAC;MAChC,KACI,CAACX,KAAA,IAASQ,SAAA,CAAUI,GAAA,CAAIjB,GAAG,KAAMK,KAAA,KACnCJ,CAAA,CAAED,GAAG,MAAM,UACXE,CAAA,CAAEF,GAAG,MAAM,QACX;QACAY,IAAA,CAAKZ,GAAG,IAAI;QACZe,UAAA;MACF,OAAO;QACLH,IAAA,CAAKZ,GAAG,IAAII,gBAAA,CAAiBH,CAAA,CAAED,GAAG,GAAGE,CAAA,CAAEF,GAAG,CAAC;QAC3C,IAAIY,IAAA,CAAKZ,GAAG,MAAMC,CAAA,CAAED,GAAG,KAAKC,CAAA,CAAED,GAAG,MAAM,QAAW;UAChDe,UAAA;QACF;MACF;IACF;IAEA,OAAOP,KAAA,KAAUG,KAAA,IAASI,UAAA,KAAeP,KAAA,GAAQP,CAAA,GAAIW,IAAA;EACvD;EAEA,OAAOV,CAAA;AACT;AAKO,SAASgB,oBACdjB,CAAA,EACAC,CAAA,EACS;EACT,IAAI,CAACA,CAAA,IAAKP,MAAA,CAAOC,IAAA,CAAKK,CAAC,EAAEQ,MAAA,KAAWd,MAAA,CAAOC,IAAA,CAAKM,CAAC,EAAEO,MAAA,EAAQ;IACzD,OAAO;EACT;EAEA,WAAWT,GAAA,IAAOC,CAAA,EAAG;IACnB,IAAIA,CAAA,CAAED,GAAG,MAAME,CAAA,CAAEF,GAAG,GAAG;MACrB,OAAO;IACT;EACF;EAEA,OAAO;AACT;AAEO,SAASM,aAAanD,KAAA,EAAgB;EAC3C,OAAOgE,KAAA,CAAMC,OAAA,CAAQjE,KAAK,KAAKA,KAAA,CAAMsD,MAAA,KAAWd,MAAA,CAAOC,IAAA,CAAKzC,KAAK,EAAEsD,MAAA;AACrE;AAIO,SAASf,cAAc2B,CAAA,EAAqB;EACjD,IAAI,CAACC,kBAAA,CAAmBD,CAAC,GAAG;IAC1B,OAAO;EACT;EAGA,MAAME,IAAA,GAAOF,CAAA,CAAEG,WAAA;EACf,IAAID,IAAA,KAAS,QAAW;IACtB,OAAO;EACT;EAGA,MAAME,IAAA,GAAOF,IAAA,CAAKG,SAAA;EAClB,IAAI,CAACJ,kBAAA,CAAmBG,IAAI,GAAG;IAC7B,OAAO;EACT;EAGA,IAAI,CAACA,IAAA,CAAKE,cAAA,CAAe,eAAe,GAAG;IACzC,OAAO;EACT;EAGA,IAAIhC,MAAA,CAAOiC,cAAA,CAAeP,CAAC,MAAM1B,MAAA,CAAO+B,SAAA,EAAW;IACjD,OAAO;EACT;EAGA,OAAO;AACT;AAEA,SAASJ,mBAAmBD,CAAA,EAAiB;EAC3C,OAAO1B,MAAA,CAAO+B,SAAA,CAAUG,QAAA,CAASC,IAAA,CAAKT,CAAC,MAAM;AAC/C;AAEO,SAASU,MAAMC,OAAA,EAAgC;EACpD,OAAO,IAAIC,OAAA,CAASC,OAAA,IAAY;IAC9BC,UAAA,CAAWD,OAAA,EAASF,OAAO;EAC7B,CAAC;AACH;AAEO,SAASI,YAGdC,QAAA,EAA6BC,IAAA,EAAa5D,OAAA,EAA0B;EACpE,IAAI,OAAOA,OAAA,CAAQ6D,iBAAA,KAAsB,YAAY;IACnD,OAAO7D,OAAA,CAAQ6D,iBAAA,CAAkBF,QAAA,EAAUC,IAAI;EACjD,WAAW5D,OAAA,CAAQ6D,iBAAA,KAAsB,OAAO;IAC9C,IAAIC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,IAAI;QACF,OAAOtC,gBAAA,CAAiBiC,QAAA,EAAUC,IAAI;MACxC,SAASK,KAAA,EAAO;QACdC,OAAA,CAAQD,KAAA,CACN,0JAA0JjE,OAAA,CAAQF,SAAS,MAAMmE,KAAK,EACxL;QAGA,MAAMA,KAAA;MACR;IACF;IAEA,OAAOvC,gBAAA,CAAiBiC,QAAA,EAAUC,IAAI;EACxC;EACA,OAAOA,IAAA;AACT;AAEO,SAASO,iBACdC,YAAA,EACe;EACf,OAAOA,YAAA;AACT;AAEO,SAASC,SAAYC,KAAA,EAAiBC,IAAA,EAASxF,GAAA,GAAM,GAAa;EACvE,MAAMyF,QAAA,GAAW,CAAC,GAAGF,KAAA,EAAOC,IAAI;EAChC,OAAOxF,GAAA,IAAOyF,QAAA,CAASzC,MAAA,GAAShD,GAAA,GAAMyF,QAAA,CAASC,KAAA,CAAM,CAAC,IAAID,QAAA;AAC5D;AAEO,SAASE,WAAcJ,KAAA,EAAiBC,IAAA,EAASxF,GAAA,GAAM,GAAa;EACzE,MAAMyF,QAAA,GAAW,CAACD,IAAA,EAAM,GAAGD,KAAK;EAChC,OAAOvF,GAAA,IAAOyF,QAAA,CAASzC,MAAA,GAAShD,GAAA,GAAMyF,QAAA,CAASC,KAAA,CAAM,GAAG,EAAE,IAAID,QAAA;AAChE;AAEO,IAAMG,SAAA,GAAYC,MAAA,CAAO;AAGzB,SAASC,cAId7E,OAAA,EAIA8E,YAAA,EACwC;EACxC,IAAIhB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAIhE,OAAA,CAAQ+E,OAAA,KAAYJ,SAAA,EAAW;MACjCT,OAAA,CAAQD,KAAA,CACN,yGAAyGjE,OAAA,CAAQF,SAAS,GAC5H;IACF;EACF;EAKA,IAAI,CAACE,OAAA,CAAQ+E,OAAA,IAAWD,YAAA,EAAcE,cAAA,EAAgB;IACpD,OAAO,MAAMF,YAAA,CAAaE,cAAA;EAC5B;EAEA,IAAI,CAAChF,OAAA,CAAQ+E,OAAA,IAAW/E,OAAA,CAAQ+E,OAAA,KAAYJ,SAAA,EAAW;IACrD,OAAO,MACLpB,OAAA,CAAQ0B,MAAA,CAAO,IAAIC,KAAA,CAAM,qBAAqBlF,OAAA,CAAQF,SAAS,GAAG,CAAC;EACvE;EAEA,OAAOE,OAAA,CAAQ+E,OAAA;AACjB;AAEO,SAASI,iBACdC,YAAA,EACAC,MAAA,EACS;EAET,IAAI,OAAOD,YAAA,KAAiB,YAAY;IACtC,OAAOA,YAAA,CAAa,GAAGC,MAAM;EAC/B;EAEA,OAAO,CAAC,CAACD,YAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}