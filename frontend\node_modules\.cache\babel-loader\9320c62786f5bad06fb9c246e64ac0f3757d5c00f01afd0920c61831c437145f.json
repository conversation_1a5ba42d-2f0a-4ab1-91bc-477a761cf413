{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\n\n// Componentes\nimport TestPage from './pages/TestPage';\nimport SimpleLoginPage from './pages/SimpleLoginPage';\nimport { useSimpleAuth } from './stores/simpleAuthStore';\n\n// Estilos\nimport './styles/globals.css';\n\n// Componente para roteamento baseado em autenticação\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppRouter = () => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading\n  } = useSimpleAuth();\n  console.log('AppRouter - Estado:', {\n    isAuthenticated,\n    isLoading\n  });\n\n  // Mostrar loading se necessário\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Carregando...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: /*#__PURE__*/_jsxDEV(SimpleLoginPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 37\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/test\",\n      element: /*#__PURE__*/_jsxDEV(TestPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 36\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/game\",\n      element: isAuthenticated ? /*#__PURE__*/_jsxDEV(TestPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 13\n      }, this) // Por enquanto, usar TestPage como placeholder\n      : /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/game\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(AppRouter, \"7tNBMeIOr6XhgHIuV+z39eQ/ftQ=\", false, function () {\n  return [useSimpleAuth];\n});\n_c = AppRouter;\nfunction App() {\n  console.log('App - Renderizando com autenticação simples...');\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-bg-primary text-text-primary\",\n      children: /*#__PURE__*/_jsxDEV(AppRouter, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppRouter\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "TestPage", "SimpleLoginPage", "useSimpleAuth", "jsxDEV", "_jsxDEV", "AppRouter", "_s", "isAuthenticated", "isLoading", "console", "log", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "Navigate", "to", "replace", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\n\n// Componentes\nimport TestPage from './pages/TestPage';\nimport SimpleLoginPage from './pages/SimpleLoginPage';\nimport { useSimpleAuth } from './stores/simpleAuthStore';\n\n// Estilos\nimport './styles/globals.css';\n\n// Componente para roteamento baseado em autenticação\nconst AppRouter: React.FC = () => {\n  const { isAuthenticated, isLoading } = useSimpleAuth();\n\n  console.log('AppRouter - Estado:', { isAuthenticated, isLoading });\n\n  // Mostrar loading se necessário\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n          <p>Carregando...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <Routes>\n      {/* Rota de login */}\n      <Route path=\"/login\" element={<SimpleLoginPage />} />\n\n      {/* Rota de teste */}\n      <Route path=\"/test\" element={<TestPage />} />\n\n      {/* Rota do jogo (protegida) */}\n      <Route\n        path=\"/game\"\n        element={\n          isAuthenticated ? (\n            <TestPage />  // Por enquanto, usar TestPage como placeholder\n          ) : (\n            <Navigate to=\"/login\" replace />\n          )\n        }\n      />\n\n      {/* Rota raiz */}\n      <Route\n        path=\"/\"\n        element={\n          isAuthenticated ? (\n            <Navigate to=\"/game\" replace />\n          ) : (\n            <Navigate to=\"/login\" replace />\n          )\n        }\n      />\n\n      {/* 404 */}\n      <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n    </Routes>\n  );\n};\n\nfunction App() {\n  console.log('App - Renderizando com autenticação simples...');\n\n  return (\n    <Router>\n      <div className=\"min-h-screen bg-bg-primary text-text-primary\">\n        <AppRouter />\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;;AAEzE;AACA,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,eAAe,MAAM,yBAAyB;AACrD,SAASC,aAAa,QAAQ,0BAA0B;;AAExD;AACA,OAAO,sBAAsB;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGN,aAAa,CAAC,CAAC;EAEtDO,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;IAAEH,eAAe;IAAEC;EAAU,CAAC,CAAC;;EAElE;EACA,IAAIA,SAAS,EAAE;IACb,oBACEJ,OAAA;MAAKO,SAAS,EAAC,+EAA+E;MAAAC,QAAA,eAC5FR,OAAA;QAAKO,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BR,OAAA;UAAKO,SAAS,EAAC;QAA2E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGZ,OAAA;UAAAQ,QAAA,EAAG;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEZ,OAAA,CAACN,MAAM;IAAAc,QAAA,gBAELR,OAAA,CAACL,KAAK;MAACkB,IAAI,EAAC,QAAQ;MAACC,OAAO,eAAEd,OAAA,CAACH,eAAe;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrDZ,OAAA,CAACL,KAAK;MAACkB,IAAI,EAAC,OAAO;MAACC,OAAO,eAAEd,OAAA,CAACJ,QAAQ;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG7CZ,OAAA,CAACL,KAAK;MACJkB,IAAI,EAAC,OAAO;MACZC,OAAO,EACLX,eAAe,gBACbH,OAAA,CAACJ,QAAQ;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,CAAE;MAAA,eAEdZ,OAAA,CAACe,QAAQ;QAACC,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAElC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFZ,OAAA,CAACL,KAAK;MACJkB,IAAI,EAAC,GAAG;MACRC,OAAO,EACLX,eAAe,gBACbH,OAAA,CAACe,QAAQ;QAACC,EAAE,EAAC,OAAO;QAACC,OAAO;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE/BZ,OAAA,CAACe,QAAQ;QAACC,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAElC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFZ,OAAA,CAACL,KAAK;MAACkB,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEd,OAAA,CAACe,QAAQ;QAACC,EAAE,EAAC,GAAG;QAACC,OAAO;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClD,CAAC;AAEb,CAAC;AAACV,EAAA,CArDID,SAAmB;EAAA,QACgBH,aAAa;AAAA;AAAAoB,EAAA,GADhDjB,SAAmB;AAuDzB,SAASkB,GAAGA,CAAA,EAAG;EACbd,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;EAE7D,oBACEN,OAAA,CAACP,MAAM;IAAAe,QAAA,eACLR,OAAA;MAAKO,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3DR,OAAA,CAACC,SAAS;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACQ,GAAA,GAVQD,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}