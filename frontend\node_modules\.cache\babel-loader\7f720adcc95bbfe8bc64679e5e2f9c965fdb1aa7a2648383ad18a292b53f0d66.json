{"ast": null, "code": "// Tipos do sistema de jogo\n\n// Configurações do jogo\nexport const GAME_CONFIG = {\n  // Sistema de XP\n  BASE_XP_PER_LEVEL: 100,\n  XP_MULTIPLIER: 1.5,\n  // Apps iniciais\n  INITIAL_APPS: {\n    antivirus: 1,\n    bankguard: 1,\n    bruteforce: 1,\n    sdk: 1,\n    firewall: 1,\n    malware_kit: 1,\n    proxyvpn: 1,\n    mineradora: 1 // Novo app para gerar dinheiro e shack\n  },\n  // Custos de upgrade\n  BASE_UPGRADE_COST: 1,\n  // Custo base de $1\n  UPGRADE_COST_MULTIPLIER: 1.1,\n  // Aumenta 1.1x a cada upgrade\n\n  // XP por upgrade\n  BASE_XP_REWARD: 25,\n  XP_REWARD_MULTIPLIER: 1.2,\n  // Limites\n  MAX_PLAYER_LEVEL: 100,\n  DUAL_CURRENCY_LEVEL: 10,\n  // A partir do nível 10, upgrades custam dinheiro + shack\n\n  // Valores iniciais\n  INITIAL_CASH: 10,\n  // Dinheiro inicial\n  INITIAL_SHACK: 0,\n  // Shack inicial (nova moeda)\n\n  // Configurações da nova moeda SHACK\n  BASE_SHACK_COST: 1,\n  // Custo base em shack para upgrades 10+\n  SHACK_COST_MULTIPLIER: 1.2 // Multiplicador para custo em shack\n};\n\n// Definições dos aplicativos\nexport const GAME_APPS = {\n  antivirus: {\n    id: 'antivirus',\n    name: 'AntiVirus',\n    description: 'Protege contra malware e ataques',\n    icon: '🛡️',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'security'\n  },\n  bankguard: {\n    id: 'bankguard',\n    name: 'BankGuard',\n    description: 'Protege transações bancárias',\n    icon: '🏦',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'security'\n  },\n  bruteforce: {\n    id: 'bruteforce',\n    name: 'BruteForce',\n    description: 'Quebra senhas e sistemas',\n    icon: '🔨',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'attack'\n  },\n  sdk: {\n    id: 'sdk',\n    name: 'SDK',\n    description: 'Kit de desenvolvimento de software',\n    icon: '⚙️',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'utility'\n  },\n  firewall: {\n    id: 'firewall',\n    name: 'Firewall',\n    description: 'Bloqueia conexões maliciosas',\n    icon: '🔥',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'security'\n  },\n  malware_kit: {\n    id: 'malware_kit',\n    name: 'Malware Kit',\n    description: 'Cria e distribui malware',\n    icon: '🦠',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'attack'\n  },\n  proxyvpn: {\n    id: 'proxyvpn',\n    name: 'ProxyVPN',\n    description: 'Oculta identidade e localização',\n    icon: '🌐',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'utility'\n  }\n};\n\n// Funções utilitárias\nexport const calculateXpForLevel = level => {\n  return Math.floor(GAME_CONFIG.BASE_XP_PER_LEVEL * Math.pow(GAME_CONFIG.XP_MULTIPLIER, level - 1));\n};\nexport const calculateUpgradeCost = currentLevel => {\n  // Custo para ir do nível atual para o próximo nível\n  return Math.ceil(GAME_CONFIG.BASE_UPGRADE_COST * Math.pow(GAME_CONFIG.UPGRADE_COST_MULTIPLIER, currentLevel));\n};\nexport const calculateXpReward = appLevel => {\n  return Math.floor(GAME_CONFIG.BASE_XP_REWARD * Math.pow(GAME_CONFIG.XP_REWARD_MULTIPLIER, appLevel - 1));\n};\nexport const generateRandomIP = () => {\n  const segments = [];\n  for (let i = 0; i < 4; i++) {\n    segments.push(Math.floor(Math.random() * 256));\n  }\n  return segments.join('.');\n};\nexport const getPlayerLevel = totalXp => {\n  let level = 1;\n  let xpRequired = 0;\n  while (level <= GAME_CONFIG.MAX_PLAYER_LEVEL) {\n    const xpForThisLevel = calculateXpForLevel(level);\n    if (totalXp < xpRequired + xpForThisLevel) {\n      break;\n    }\n    xpRequired += xpForThisLevel;\n    level++;\n  }\n  return level;\n};\nexport const getXpForNextLevel = totalXp => {\n  const currentLevel = getPlayerLevel(totalXp);\n  let xpRequired = 0;\n  for (let i = 1; i < currentLevel; i++) {\n    xpRequired += calculateXpForLevel(i);\n  }\n  const xpForCurrentLevel = calculateXpForLevel(currentLevel);\n  const xpInCurrentLevel = totalXp - xpRequired;\n  return xpForCurrentLevel - xpInCurrentLevel;\n};", "map": {"version": 3, "names": ["GAME_CONFIG", "BASE_XP_PER_LEVEL", "XP_MULTIPLIER", "INITIAL_APPS", "antivirus", "bankguard", "bruteforce", "sdk", "firewall", "malware_kit", "proxyvpn", "mineradora", "BASE_UPGRADE_COST", "UPGRADE_COST_MULTIPLIER", "BASE_XP_REWARD", "XP_REWARD_MULTIPLIER", "MAX_PLAYER_LEVEL", "DUAL_CURRENCY_LEVEL", "INITIAL_CASH", "INITIAL_SHACK", "BASE_SHACK_COST", "SHACK_COST_MULTIPLIER", "GAME_APPS", "id", "name", "description", "icon", "maxLevel", "MAX_APP_LEVEL", "category", "calculateXpForLevel", "level", "Math", "floor", "pow", "calculateUpgradeCost", "currentLevel", "ceil", "calculateXpReward", "appLevel", "generateRandomIP", "segments", "i", "push", "random", "join", "getPlayerLevel", "totalXp", "xpRequired", "xpForThisLevel", "getXpForNextLevel", "xpForCurrentLevel", "xpInCurrentLevel"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/types/game.ts"], "sourcesContent": ["// Tipos do sistema de jogo\n\nexport interface Player {\n  uid: string;\n  nick: string;\n  email: string;\n  ip: string;\n  level: number;\n  xp: number;\n  xpToNextLevel: number;\n  cash: number;\n  shack: number; // Nova moeda para upgrades avançados\n  createdAt: string;\n  lastLogin: string;\n}\n\nexport interface GameApp {\n  id: string;\n  name: string;\n  description: string;\n  icon: string;\n  level: number;\n  maxLevel: number;\n  upgradeCost: number;\n  xpReward: number;\n  category: 'security' | 'attack' | 'utility';\n}\n\nexport interface PlayerApps {\n  antivirus: number;\n  bankguard: number;\n  bruteforce: number;\n  sdk: number;\n  firewall: number;\n  malware_kit: number;\n  proxyvpn: number;\n  mineradora: number; // Novo app para gerar dinheiro e shack\n}\n\nexport interface GameStats {\n  totalPlayers: number;\n  onlinePlayers: number;\n  totalHacks: number;\n  totalMoney: number;\n}\n\nexport interface HackTarget {\n  uid: string;\n  nick: string;\n  ip: string;\n  level: number;\n  cash: number;\n  apps: PlayerApps;\n  isOnline: boolean;\n  lastSeen: string;\n}\n\nexport interface HackAttempt {\n  id: string;\n  attackerIp: string;\n  targetIp: string;\n  type: 'scan' | 'exploit' | 'bruteforce';\n  status: 'pending' | 'success' | 'failed';\n  timestamp: string;\n  reward?: number;\n}\n\nexport interface GameNotification {\n  id: string;\n  type: 'hack' | 'upgrade' | 'level' | 'system';\n  title: string;\n  message: string;\n  timestamp: string;\n  read: boolean;\n}\n\n// Configurações do jogo\nexport const GAME_CONFIG = {\n  // Sistema de XP\n  BASE_XP_PER_LEVEL: 100,\n  XP_MULTIPLIER: 1.5,\n  \n  // Apps iniciais\n  INITIAL_APPS: {\n    antivirus: 1,\n    bankguard: 1,\n    bruteforce: 1,\n    sdk: 1,\n    firewall: 1,\n    malware_kit: 1,\n    proxyvpn: 1,\n    mineradora: 1, // Novo app para gerar dinheiro e shack\n  } as PlayerApps,\n  \n  // Custos de upgrade\n  BASE_UPGRADE_COST: 1, // Custo base de $1\n  UPGRADE_COST_MULTIPLIER: 1.1, // Aumenta 1.1x a cada upgrade\n  \n  // XP por upgrade\n  BASE_XP_REWARD: 25,\n  XP_REWARD_MULTIPLIER: 1.2,\n  \n  // Limites\n  MAX_PLAYER_LEVEL: 100,\n  DUAL_CURRENCY_LEVEL: 10, // A partir do nível 10, upgrades custam dinheiro + shack\n  \n  // Valores iniciais\n  INITIAL_CASH: 10, // Dinheiro inicial\n  INITIAL_SHACK: 0, // Shack inicial (nova moeda)\n\n  // Configurações da nova moeda SHACK\n  BASE_SHACK_COST: 1, // Custo base em shack para upgrades 10+\n  SHACK_COST_MULTIPLIER: 1.2, // Multiplicador para custo em shack\n};\n\n// Definições dos aplicativos\nexport const GAME_APPS: Record<keyof PlayerApps, Omit<GameApp, 'level' | 'upgradeCost' | 'xpReward'>> = {\n  antivirus: {\n    id: 'antivirus',\n    name: 'AntiVirus',\n    description: 'Protege contra malware e ataques',\n    icon: '🛡️',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'security',\n  },\n  bankguard: {\n    id: 'bankguard',\n    name: 'BankGuard',\n    description: 'Protege transações bancárias',\n    icon: '🏦',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'security',\n  },\n  bruteforce: {\n    id: 'bruteforce',\n    name: 'BruteForce',\n    description: 'Quebra senhas e sistemas',\n    icon: '🔨',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'attack',\n  },\n  sdk: {\n    id: 'sdk',\n    name: 'SDK',\n    description: 'Kit de desenvolvimento de software',\n    icon: '⚙️',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'utility',\n  },\n  firewall: {\n    id: 'firewall',\n    name: 'Firewall',\n    description: 'Bloqueia conexões maliciosas',\n    icon: '🔥',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'security',\n  },\n  malware_kit: {\n    id: 'malware_kit',\n    name: 'Malware Kit',\n    description: 'Cria e distribui malware',\n    icon: '🦠',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'attack',\n  },\n  proxyvpn: {\n    id: 'proxyvpn',\n    name: 'ProxyVPN',\n    description: 'Oculta identidade e localização',\n    icon: '🌐',\n    maxLevel: GAME_CONFIG.MAX_APP_LEVEL,\n    category: 'utility',\n  },\n};\n\n// Funções utilitárias\nexport const calculateXpForLevel = (level: number): number => {\n  return Math.floor(GAME_CONFIG.BASE_XP_PER_LEVEL * Math.pow(GAME_CONFIG.XP_MULTIPLIER, level - 1));\n};\n\nexport const calculateUpgradeCost = (currentLevel: number): number => {\n  // Custo para ir do nível atual para o próximo nível\n  return Math.ceil(GAME_CONFIG.BASE_UPGRADE_COST * Math.pow(GAME_CONFIG.UPGRADE_COST_MULTIPLIER, currentLevel));\n};\n\nexport const calculateXpReward = (appLevel: number): number => {\n  return Math.floor(GAME_CONFIG.BASE_XP_REWARD * Math.pow(GAME_CONFIG.XP_REWARD_MULTIPLIER, appLevel - 1));\n};\n\nexport const generateRandomIP = (): string => {\n  const segments = [];\n  for (let i = 0; i < 4; i++) {\n    segments.push(Math.floor(Math.random() * 256));\n  }\n  return segments.join('.');\n};\n\nexport const getPlayerLevel = (totalXp: number): number => {\n  let level = 1;\n  let xpRequired = 0;\n  \n  while (level <= GAME_CONFIG.MAX_PLAYER_LEVEL) {\n    const xpForThisLevel = calculateXpForLevel(level);\n    if (totalXp < xpRequired + xpForThisLevel) {\n      break;\n    }\n    xpRequired += xpForThisLevel;\n    level++;\n  }\n  \n  return level;\n};\n\nexport const getXpForNextLevel = (totalXp: number): number => {\n  const currentLevel = getPlayerLevel(totalXp);\n  let xpRequired = 0;\n  \n  for (let i = 1; i < currentLevel; i++) {\n    xpRequired += calculateXpForLevel(i);\n  }\n  \n  const xpForCurrentLevel = calculateXpForLevel(currentLevel);\n  const xpInCurrentLevel = totalXp - xpRequired;\n  \n  return xpForCurrentLevel - xpInCurrentLevel;\n};\n"], "mappings": "AAAA;;AA4EA;AACA,OAAO,MAAMA,WAAW,GAAG;EACzB;EACAC,iBAAiB,EAAE,GAAG;EACtBC,aAAa,EAAE,GAAG;EAElB;EACAC,YAAY,EAAE;IACZC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,CAAC;IACbC,GAAG,EAAE,CAAC;IACNC,QAAQ,EAAE,CAAC;IACXC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC,CAAE;EACjB,CAAe;EAEf;EACAC,iBAAiB,EAAE,CAAC;EAAE;EACtBC,uBAAuB,EAAE,GAAG;EAAE;;EAE9B;EACAC,cAAc,EAAE,EAAE;EAClBC,oBAAoB,EAAE,GAAG;EAEzB;EACAC,gBAAgB,EAAE,GAAG;EACrBC,mBAAmB,EAAE,EAAE;EAAE;;EAEzB;EACAC,YAAY,EAAE,EAAE;EAAE;EAClBC,aAAa,EAAE,CAAC;EAAE;;EAElB;EACAC,eAAe,EAAE,CAAC;EAAE;EACpBC,qBAAqB,EAAE,GAAG,CAAE;AAC9B,CAAC;;AAED;AACA,OAAO,MAAMC,SAAwF,GAAG;EACtGlB,SAAS,EAAE;IACTmB,EAAE,EAAE,WAAW;IACfC,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE,kCAAkC;IAC/CC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE3B,WAAW,CAAC4B,aAAa;IACnCC,QAAQ,EAAE;EACZ,CAAC;EACDxB,SAAS,EAAE;IACTkB,EAAE,EAAE,WAAW;IACfC,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE,8BAA8B;IAC3CC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE3B,WAAW,CAAC4B,aAAa;IACnCC,QAAQ,EAAE;EACZ,CAAC;EACDvB,UAAU,EAAE;IACViB,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,0BAA0B;IACvCC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE3B,WAAW,CAAC4B,aAAa;IACnCC,QAAQ,EAAE;EACZ,CAAC;EACDtB,GAAG,EAAE;IACHgB,EAAE,EAAE,KAAK;IACTC,IAAI,EAAE,KAAK;IACXC,WAAW,EAAE,oCAAoC;IACjDC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE3B,WAAW,CAAC4B,aAAa;IACnCC,QAAQ,EAAE;EACZ,CAAC;EACDrB,QAAQ,EAAE;IACRe,EAAE,EAAE,UAAU;IACdC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,8BAA8B;IAC3CC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE3B,WAAW,CAAC4B,aAAa;IACnCC,QAAQ,EAAE;EACZ,CAAC;EACDpB,WAAW,EAAE;IACXc,EAAE,EAAE,aAAa;IACjBC,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,0BAA0B;IACvCC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE3B,WAAW,CAAC4B,aAAa;IACnCC,QAAQ,EAAE;EACZ,CAAC;EACDnB,QAAQ,EAAE;IACRa,EAAE,EAAE,UAAU;IACdC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,iCAAiC;IAC9CC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE3B,WAAW,CAAC4B,aAAa;IACnCC,QAAQ,EAAE;EACZ;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAIC,KAAa,IAAa;EAC5D,OAAOC,IAAI,CAACC,KAAK,CAACjC,WAAW,CAACC,iBAAiB,GAAG+B,IAAI,CAACE,GAAG,CAAClC,WAAW,CAACE,aAAa,EAAE6B,KAAK,GAAG,CAAC,CAAC,CAAC;AACnG,CAAC;AAED,OAAO,MAAMI,oBAAoB,GAAIC,YAAoB,IAAa;EACpE;EACA,OAAOJ,IAAI,CAACK,IAAI,CAACrC,WAAW,CAACY,iBAAiB,GAAGoB,IAAI,CAACE,GAAG,CAAClC,WAAW,CAACa,uBAAuB,EAAEuB,YAAY,CAAC,CAAC;AAC/G,CAAC;AAED,OAAO,MAAME,iBAAiB,GAAIC,QAAgB,IAAa;EAC7D,OAAOP,IAAI,CAACC,KAAK,CAACjC,WAAW,CAACc,cAAc,GAAGkB,IAAI,CAACE,GAAG,CAAClC,WAAW,CAACe,oBAAoB,EAAEwB,QAAQ,GAAG,CAAC,CAAC,CAAC;AAC1G,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAc;EAC5C,MAAMC,QAAQ,GAAG,EAAE;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1BD,QAAQ,CAACE,IAAI,CAACX,IAAI,CAACC,KAAK,CAACD,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;EAChD;EACA,OAAOH,QAAQ,CAACI,IAAI,CAAC,GAAG,CAAC;AAC3B,CAAC;AAED,OAAO,MAAMC,cAAc,GAAIC,OAAe,IAAa;EACzD,IAAIhB,KAAK,GAAG,CAAC;EACb,IAAIiB,UAAU,GAAG,CAAC;EAElB,OAAOjB,KAAK,IAAI/B,WAAW,CAACgB,gBAAgB,EAAE;IAC5C,MAAMiC,cAAc,GAAGnB,mBAAmB,CAACC,KAAK,CAAC;IACjD,IAAIgB,OAAO,GAAGC,UAAU,GAAGC,cAAc,EAAE;MACzC;IACF;IACAD,UAAU,IAAIC,cAAc;IAC5BlB,KAAK,EAAE;EACT;EAEA,OAAOA,KAAK;AACd,CAAC;AAED,OAAO,MAAMmB,iBAAiB,GAAIH,OAAe,IAAa;EAC5D,MAAMX,YAAY,GAAGU,cAAc,CAACC,OAAO,CAAC;EAC5C,IAAIC,UAAU,GAAG,CAAC;EAElB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,YAAY,EAAEM,CAAC,EAAE,EAAE;IACrCM,UAAU,IAAIlB,mBAAmB,CAACY,CAAC,CAAC;EACtC;EAEA,MAAMS,iBAAiB,GAAGrB,mBAAmB,CAACM,YAAY,CAAC;EAC3D,MAAMgB,gBAAgB,GAAGL,OAAO,GAAGC,UAAU;EAE7C,OAAOG,iBAAiB,GAAGC,gBAAgB;AAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}