/* Interface Mobile */
.mobile-phone {
    width: 375px;
    height: 812px;
    background: linear-gradient(145deg, #1e293b, #334155);
    border-radius: 40px;
    padding: 8px;
    box-shadow: 
        0 0 0 2px rgba(148, 163, 184, 0.2),
        0 20px 60px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.mobile-screen {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
    border-radius: 32px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header */
.mobile-header {
    padding: 12px 20px 8px;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(10px);
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #e2e8f0;
}

.status-icons {
    display: flex;
    gap: 8px;
    font-size: 12px;
}

/* Conteúdo */
.mobile-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
}

.screen {
    display: none;
    height: 100%;
    overflow-y: auto;
}

.screen.active {
    display: block;
}

/* Footer */
.mobile-footer {
    background: rgba(15, 23, 42, 0.9);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(148, 163, 184, 0.1);
    padding: 8px 16px 20px;
    position: relative;
}

.nav-bar {
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: relative;
}

.nav-btn {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    min-width: 60px;
}

.nav-btn:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.nav-btn.active {
    color: #3b82f6;
}

.nav-icon {
    font-size: 20px;
    line-height: 1;
}

.nav-label {
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Botão Home Central */
.home-button {
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(145deg, #1e293b, #334155);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(148, 163, 184, 0.2);
}

.home-button:hover {
    transform: translateX(-50%) translateY(-2px);
    box-shadow: 
        0 6px 25px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.home-button:active {
    transform: translateX(-50%) translateY(0);
}

.home-btn-inner {
    font-size: 24px;
    color: #3b82f6;
}

/* Responsividade Mobile */
@media (max-width: 400px) {
    .mobile-phone {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
        padding: 0;
        box-shadow: none;
    }
    
    .mobile-screen {
        border-radius: 0;
    }
    
    .home-button {
        bottom: 15px;
    }
}

/* Animações específicas mobile */
.nav-btn {
    transition: all 0.2s ease;
}

.nav-btn:active {
    transform: scale(0.95);
}

/* Indicador de notificação */
.nav-btn::after {
    content: '';
    position: absolute;
    top: 4px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: #ef4444;
    border-radius: 50%;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.nav-btn.has-notification::after {
    opacity: 1;
    transform: scale(1);
}

/* Efeito de vibração para feedback tátil */
@keyframes vibrate {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-1px); }
    75% { transform: translateX(1px); }
}

.mobile-phone.vibrate {
    animation: vibrate 0.1s ease-in-out 3;
}
