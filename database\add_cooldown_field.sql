-- Script para adicionar campo de cooldown na tabela usuarios existente
-- Execute este script no SQL Editor do Supabase

-- Adiciona o campo cooldown_roubo_ips se não existir
ALTER TABLE usuarios 
ADD COLUMN IF NOT EXISTS cooldown_roubo_ips JSONB DEFAULT '{}';

-- Atualiza usuários existentes que não têm o campo
UPDATE usuarios 
SET cooldown_roubo_ips = '{}'::jsonb 
WHERE cooldown_roubo_ips IS NULL;
