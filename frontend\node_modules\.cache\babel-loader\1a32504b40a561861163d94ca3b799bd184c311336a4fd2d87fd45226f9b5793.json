{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\App.gradual.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport './styles/globals.css';\n\n// Componente de teste simples\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestPage = ({\n  title,\n  color\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"min-h-screen bg-bg-primary text-text-primary p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"text-accent-blue hover:text-primary-light\",\n        children: \"\\u2190 Voltar ao in\\xEDcio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `card border-${color}-500`,\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-4\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-secondary\",\n        children: [\"Esta \\xE9 a p\\xE1gina de teste: \", title]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 7,\n  columnNumber: 3\n}, this);\n\n// Página inicial\n_c = TestPage;\nconst HomePage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"min-h-screen bg-bg-primary text-text-primary p-8\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-4xl font-bold text-center mb-8\",\n      children: \"\\uD83C\\uDFAE SHACK Web Game - Teste Gradual\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-semibold mb-6\",\n        children: \"React Router Funcionando!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"card bg-blue-900 border-blue-500 hover:bg-blue-800 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-blue-100 mb-2\",\n            children: \"\\uD83D\\uDD10 Teste Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-200 text-sm\",\n            children: \"Testar p\\xE1gina de login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/game\",\n          className: \"card bg-green-900 border-green-500 hover:bg-green-800 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-green-100 mb-2\",\n            children: \"\\uD83C\\uDFAE Teste Game\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-green-200 text-sm\",\n            children: \"Testar p\\xE1gina do jogo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-text-muted\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2705 React funcionando\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2705 React Router funcionando\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2705 Tailwind CSS funcionando\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 27,\n  columnNumber: 3\n}, this);\n_c2 = HomePage;\nfunction App() {\n  console.log('App Gradual - Renderizando...');\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(TestPage, {\n          title: \"P\\xE1gina de Login\",\n          color: \"blue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/game\",\n        element: /*#__PURE__*/_jsxDEV(TestPage, {\n          title: \"P\\xE1gina do Jogo\",\n          color: \"green\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(TestPage, {\n          title: \"P\\xE1gina 404\",\n          color: \"red\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"TestPage\");\n$RefreshReg$(_c2, \"HomePage\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "jsxDEV", "_jsxDEV", "TestPage", "title", "color", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "HomePage", "_c2", "App", "console", "log", "path", "element", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/App.gradual.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport './styles/globals.css';\n\n// Componente de teste simples\nconst TestPage = ({ title, color }: { title: string; color: string }) => (\n  <div className=\"min-h-screen bg-bg-primary text-text-primary p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"mb-6\">\n        <Link to=\"/\" className=\"text-accent-blue hover:text-primary-light\">\n          ← Voltar ao início\n        </Link>\n      </div>\n      \n      <div className={`card border-${color}-500`}>\n        <h1 className=\"text-3xl font-bold mb-4\">{title}</h1>\n        <p className=\"text-text-secondary\">\n          Esta é a página de teste: {title}\n        </p>\n      </div>\n    </div>\n  </div>\n);\n\n// Página inicial\nconst HomePage = () => (\n  <div className=\"min-h-screen bg-bg-primary text-text-primary p-8\">\n    <div className=\"max-w-4xl mx-auto\">\n      <h1 className=\"text-4xl font-bold text-center mb-8\">\n        🎮 SHACK Web Game - Teste Gradual\n      </h1>\n      \n      <div className=\"card text-center\">\n        <h2 className=\"text-2xl font-semibold mb-6\">\n          React Router Funcionando!\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n          <Link to=\"/login\" className=\"card bg-blue-900 border-blue-500 hover:bg-blue-800 transition-colors\">\n            <h3 className=\"text-lg font-semibold text-blue-100 mb-2\">\n              🔐 Teste Login\n            </h3>\n            <p className=\"text-blue-200 text-sm\">\n              Testar página de login\n            </p>\n          </Link>\n          \n          <Link to=\"/game\" className=\"card bg-green-900 border-green-500 hover:bg-green-800 transition-colors\">\n            <h3 className=\"text-lg font-semibold text-green-100 mb-2\">\n              🎮 Teste Game\n            </h3>\n            <p className=\"text-green-200 text-sm\">\n              Testar página do jogo\n            </p>\n          </Link>\n        </div>\n        \n        <div className=\"text-text-muted\">\n          <p>✅ React funcionando</p>\n          <p>✅ React Router funcionando</p>\n          <p>✅ Tailwind CSS funcionando</p>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nfunction App() {\n  console.log('App Gradual - Renderizando...');\n  \n  return (\n    <Router>\n      <Routes>\n        <Route path=\"/\" element={<HomePage />} />\n        <Route path=\"/login\" element={<TestPage title=\"Página de Login\" color=\"blue\" />} />\n        <Route path=\"/game\" element={<TestPage title=\"Página do Jogo\" color=\"green\" />} />\n        <Route path=\"*\" element={<TestPage title=\"Página 404\" color=\"red\" />} />\n      </Routes>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,OAAO,sBAAsB;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAwC,CAAC,kBAClEH,OAAA;EAAKI,SAAS,EAAC,kDAAkD;EAAAC,QAAA,eAC/DL,OAAA;IAAKI,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCL,OAAA;MAAKI,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBL,OAAA,CAACF,IAAI;QAACQ,EAAE,EAAC,GAAG;QAACF,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAEnE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENV,OAAA;MAAKI,SAAS,EAAE,eAAeD,KAAK,MAAO;MAAAE,QAAA,gBACzCL,OAAA;QAAII,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAEH;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpDV,OAAA;QAAGI,SAAS,EAAC,qBAAqB;QAAAC,QAAA,GAAC,kCACP,EAACH,KAAK;MAAA;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;;AAED;AAAAC,EAAA,GAnBMV,QAAQ;AAoBd,MAAMW,QAAQ,GAAGA,CAAA,kBACfZ,OAAA;EAAKI,SAAS,EAAC,kDAAkD;EAAAC,QAAA,eAC/DL,OAAA;IAAKI,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCL,OAAA;MAAII,SAAS,EAAC,qCAAqC;MAAAC,QAAA,EAAC;IAEpD;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELV,OAAA;MAAKI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BL,OAAA;QAAII,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAE5C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELV,OAAA;QAAKI,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDL,OAAA,CAACF,IAAI;UAACQ,EAAE,EAAC,QAAQ;UAACF,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBAChGL,OAAA;YAAII,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLV,OAAA;YAAGI,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEPV,OAAA,CAACF,IAAI;UAACQ,EAAE,EAAC,OAAO;UAACF,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBAClGL,OAAA;YAAII,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAE1D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLV,OAAA;YAAGI,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAEtC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENV,OAAA;QAAKI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BL,OAAA;UAAAK,QAAA,EAAG;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1BV,OAAA;UAAAK,QAAA,EAAG;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjCV,OAAA;UAAAK,QAAA,EAAG;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACG,GAAA,GAxCID,QAAQ;AA0Cd,SAASE,GAAGA,CAAA,EAAG;EACbC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAE5C,oBACEhB,OAAA,CAACL,MAAM;IAAAU,QAAA,eACLL,OAAA,CAACJ,MAAM;MAAAS,QAAA,gBACLL,OAAA,CAACH,KAAK;QAACoB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAElB,OAAA,CAACY,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzCV,OAAA,CAACH,KAAK;QAACoB,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAElB,OAAA,CAACC,QAAQ;UAACC,KAAK,EAAC,oBAAiB;UAACC,KAAK,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnFV,OAAA,CAACH,KAAK;QAACoB,IAAI,EAAC,OAAO;QAACC,OAAO,eAAElB,OAAA,CAACC,QAAQ;UAACC,KAAK,EAAC,mBAAgB;UAACC,KAAK,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClFV,OAAA,CAACH,KAAK;QAACoB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAElB,OAAA,CAACC,QAAQ;UAACC,KAAK,EAAC,eAAY;UAACC,KAAK,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACS,GAAA,GAbQL,GAAG;AAeZ,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAM,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}