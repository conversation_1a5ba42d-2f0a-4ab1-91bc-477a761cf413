-- Migração para IDs de grupo personalizados
-- Converte de UUID para VARCHAR(6) permitindo IDs como C184C2

-- 1. Backup dos dados atuais
CREATE TABLE grupos_backup AS SELECT * FROM grupos;

-- 2. Remove a foreign key constraint
ALTER TABLE usuarios DROP CONSTRAINT IF EXISTS fk_usuarios_grupo;

-- 3. Altera o tipo da coluna id na tabela grupos
-- Primeiro, cria uma nova coluna temporária
ALTER TABLE grupos ADD COLUMN new_id VARCHAR(6);

-- 4. Gera novos IDs no formato antigo para grupos existentes
DO $$
DECLARE
    grupo_record RECORD;
    novo_id VARCHAR(6);
    tentativas INTEGER;
BEGIN
    FOR grupo_record IN SELECT * FROM grupos LOOP
        tentativas := 0;
        LOOP
            -- Gera um ID aleatório de 6 caracteres
            novo_id := UPPER(
                CHR(65 + FLOOR(RANDOM() * 26)::INTEGER) ||  -- Letra A-Z
                CHR(65 + FLOOR(RANDOM() * 26)::INTEGER) ||  -- Letra A-Z
                CHR(48 + FLOOR(RANDOM() * 10)::INTEGER) ||  -- Número 0-9
                CHR(48 + FLOOR(RANDOM() * 10)::INTEGER) ||  -- Número 0-9
                CHR(65 + FLOOR(RANDOM() * 26)::INTEGER) ||  -- Letra A-Z
                CHR(48 + FLOOR(RANDOM() * 10)::INTEGER)     -- Número 0-9
            );
            
            -- Verifica se já existe
            IF NOT EXISTS (SELECT 1 FROM grupos WHERE new_id = novo_id) THEN
                UPDATE grupos SET new_id = novo_id WHERE id = grupo_record.id;
                EXIT;
            END IF;
            
            tentativas := tentativas + 1;
            IF tentativas > 100 THEN
                RAISE EXCEPTION 'Não foi possível gerar ID único para o grupo %', grupo_record.nome;
            END IF;
        END LOOP;
    END LOOP;
END $$;

-- 5. Atualiza a tabela usuarios para usar os novos IDs
UPDATE usuarios 
SET grupo_id = (
    SELECT grupos.new_id::text 
    FROM grupos 
    WHERE grupos.id::text = usuarios.grupo_id
) 
WHERE grupo_id IS NOT NULL;

-- 6. Remove a coluna antiga e renomeia a nova
ALTER TABLE grupos DROP COLUMN id;
ALTER TABLE grupos RENAME COLUMN new_id TO id;
ALTER TABLE grupos ADD PRIMARY KEY (id);

-- 7. Altera o tipo da coluna grupo_id na tabela usuarios
ALTER TABLE usuarios ALTER COLUMN grupo_id TYPE VARCHAR(6);

-- 8. Recria a foreign key constraint
ALTER TABLE usuarios ADD CONSTRAINT fk_usuarios_grupo 
    FOREIGN KEY (grupo_id) REFERENCES grupos(id);

-- 9. Recria os índices
DROP INDEX IF EXISTS idx_grupos_nome;
CREATE INDEX idx_grupos_nome ON grupos(nome);
CREATE INDEX idx_grupos_id ON grupos(id);

-- 10. Adiciona constraint para formato do ID
ALTER TABLE grupos ADD CONSTRAINT chk_id_formato 
    CHECK (id ~ '^[A-Z0-9]{6}$');

COMMIT;
