{"ast": null, "code": "var _s = $RefreshSig$();\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { backendService } from '../services/backendService';\nexport const useSimpleAuthStore = create()(persist((set, get) => ({\n  // Estado inicial\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n  // Login com backend real\n  login: async credentials => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      console.log('SimpleAuth - Fazendo login...');\n      const response = await backendService.login(credentials.email, credentials.password);\n      if (response.success && response.token && response.player) {\n        // Salvar token no localStorage para o hackGameStore\n        localStorage.setItem('auth_token', response.token);\n        const user = {\n          uid: response.player.uid,\n          nick: response.player.nick,\n          email: response.player.email\n        };\n        set({\n          user,\n          token: response.token,\n          isAuthenticated: true,\n          isLoading: false,\n          error: null\n        });\n        console.log('SimpleAuth - Login realizado com sucesso!', user);\n      } else {\n        set({\n          isLoading: false,\n          error: response.error || 'Erro no login'\n        });\n      }\n    } catch (error) {\n      console.error('SimpleAuth - Erro no login:', error);\n      set({\n        isLoading: false,\n        error: 'Erro de conexão'\n      });\n    }\n  },\n  // Registro com backend real\n  register: async userData => {\n    set({\n      isLoading: true,\n      error: null\n    });\n    try {\n      console.log('SimpleAuth - Criando conta...');\n      const response = await backendService.register(userData.nick, userData.email, userData.password);\n      if (response.success && response.token && response.player) {\n        // Salvar token no localStorage para o hackGameStore\n        localStorage.setItem('auth_token', response.token);\n        const user = {\n          uid: response.player.uid,\n          nick: response.player.nick,\n          email: response.player.email\n        };\n        set({\n          user,\n          token: response.token,\n          isAuthenticated: true,\n          isLoading: false,\n          error: null\n        });\n        console.log('SimpleAuth - Conta criada com sucesso!', user);\n      } else {\n        set({\n          isLoading: false,\n          error: response.error || 'Erro no registro'\n        });\n      }\n    } catch (error) {\n      console.error('SimpleAuth - Erro no registro:', error);\n      set({\n        isLoading: false,\n        error: 'Erro de conexão'\n      });\n    }\n  },\n  // Logout\n  logout: () => {\n    console.log('SimpleAuth - Fazendo logout');\n\n    // Remover token do localStorage\n    localStorage.removeItem('auth_token');\n    set({\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null\n    });\n  },\n  // Verificar autenticação\n  checkAuth: async () => {\n    const {\n      isLoading\n    } = get();\n    if (isLoading) {\n      console.log('SimpleAuth - Verificação já em andamento');\n      return;\n    }\n\n    // Verificar token no localStorage\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      console.log('SimpleAuth - Sem token no localStorage');\n      set({\n        isAuthenticated: false,\n        isLoading: false,\n        token: null,\n        user: null\n      });\n      return;\n    }\n    console.log('SimpleAuth - Token encontrado, verificando validade...');\n    set({\n      isLoading: true\n    });\n    try {\n      // Tentar carregar dados do jogador para verificar se o token é válido\n      const result = await backendService.getPlayer();\n      if (result.success && result.player) {\n        const user = {\n          uid: result.player.uid,\n          nick: result.player.nick,\n          email: result.player.email\n        };\n        console.log('SimpleAuth - Token válido, usuário carregado:', user);\n        set({\n          user,\n          token,\n          isAuthenticated: true,\n          isLoading: false\n        });\n      } else {\n        console.log('SimpleAuth - Token inválido');\n        get().logout();\n      }\n    } catch (error) {\n      console.error('SimpleAuth - Erro na verificação:', error);\n      get().logout();\n    }\n  },\n  // Limpar erro\n  clearError: () => {\n    set({\n      error: null\n    });\n  },\n  // Simulação de login para teste\n  simulateLogin: () => {\n    console.log('SimpleAuth - Simulando login...');\n    set({\n      isLoading: true\n    });\n\n    // Simular delay de API\n    setTimeout(() => {\n      const mockUser = {\n        uid: 'test-user-123',\n        nick: 'TestPlayer',\n        email: '<EMAIL>'\n      };\n      const mockToken = 'mock-jwt-token-123';\n      set({\n        user: mockUser,\n        token: mockToken,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      });\n      console.log('SimpleAuth - Login simulado com sucesso!');\n    }, 1000);\n  }\n}), {\n  name: 'simple-auth-storage',\n  partialize: state => ({\n    user: state.user,\n    token: state.token,\n    isAuthenticated: state.isAuthenticated\n  })\n}));\n\n// Hook simplificado\nexport const useSimpleAuth = () => {\n  _s();\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    register,\n    logout,\n    clearError,\n    checkAuth,\n    simulateLogin\n  } = useSimpleAuthStore();\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    // Ações\n    login,\n    register,\n    logout,\n    clearError,\n    checkAuth,\n    simulateLogin,\n    // Computed\n    isLoggedIn: isAuthenticated && !!user\n  };\n};\n_s(useSimpleAuth, \"36Bk4VLrS/KDxHWPVaagtmOhgfc=\", false, function () {\n  return [useSimpleAuthStore];\n});", "map": {"version": 3, "names": ["create", "persist", "backendService", "useSimpleAuthStore", "set", "get", "user", "token", "isAuthenticated", "isLoading", "error", "login", "credentials", "console", "log", "response", "email", "password", "success", "player", "localStorage", "setItem", "uid", "nick", "register", "userData", "logout", "removeItem", "checkAuth", "getItem", "result", "getPlayer", "clearError", "simulateLogin", "setTimeout", "mockUser", "mockToken", "name", "partialize", "state", "useSimpleAuth", "_s", "isLoggedIn"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/stores/simpleAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { backendService } from '../services/backendService';\n\nexport interface User {\n  uid: string;\n  nick: string;\n  email: string;\n}\n\ninterface SimpleAuthState {\n  // Estado\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n\n  // Ações com API mock\n  login: (credentials: { email: string; password: string }) => Promise<void>;\n  register: (userData: { email: string; password: string; nick: string }) => Promise<void>;\n  logout: () => void;\n  clearError: () => void;\n  checkAuth: () => Promise<void>;\n\n  // Simulação de login para teste\n  simulateLogin: () => void;\n}\n\nexport const useSimpleAuthStore = create<SimpleAuthState>()(\n  persist(\n    (set, get) => ({\n      // Estado inicial\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // Login com backend real\n      login: async (credentials) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          console.log('SimpleAuth - Fazendo login...');\n          const response = await backendService.login(credentials.email, credentials.password);\n\n          if (response.success && response.token && response.player) {\n            // Salvar token no localStorage para o hackGameStore\n            localStorage.setItem('auth_token', response.token);\n\n            const user: User = {\n              uid: response.player.uid,\n              nick: response.player.nick,\n              email: response.player.email,\n            };\n\n            set({\n              user,\n              token: response.token,\n              isAuthenticated: true,\n              isLoading: false,\n              error: null,\n            });\n\n            console.log('SimpleAuth - Login realizado com sucesso!', user);\n          } else {\n            set({\n              isLoading: false,\n              error: response.error || 'Erro no login',\n            });\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro no login:', error);\n          set({\n            isLoading: false,\n            error: 'Erro de conexão',\n          });\n        }\n      },\n\n      // Registro com backend real\n      register: async (userData) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          console.log('SimpleAuth - Criando conta...');\n          const response = await backendService.register(userData.nick, userData.email, userData.password);\n\n          if (response.success && response.token && response.player) {\n            // Salvar token no localStorage para o hackGameStore\n            localStorage.setItem('auth_token', response.token);\n\n            const user: User = {\n              uid: response.player.uid,\n              nick: response.player.nick,\n              email: response.player.email,\n            };\n\n            set({\n              user,\n              token: response.token,\n              isAuthenticated: true,\n              isLoading: false,\n              error: null,\n            });\n\n            console.log('SimpleAuth - Conta criada com sucesso!', user);\n          } else {\n            set({\n              isLoading: false,\n              error: response.error || 'Erro no registro',\n            });\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro no registro:', error);\n          set({\n            isLoading: false,\n            error: 'Erro de conexão',\n          });\n        }\n      },\n\n      // Logout\n      logout: () => {\n        console.log('SimpleAuth - Fazendo logout');\n\n        // Remover token do localStorage\n        localStorage.removeItem('auth_token');\n\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: null,\n        });\n      },\n\n      // Verificar autenticação\n      checkAuth: async () => {\n        const { isLoading } = get();\n\n        if (isLoading) {\n          console.log('SimpleAuth - Verificação já em andamento');\n          return;\n        }\n\n        // Verificar token no localStorage\n        const token = localStorage.getItem('auth_token');\n\n        if (!token) {\n          console.log('SimpleAuth - Sem token no localStorage');\n          set({ isAuthenticated: false, isLoading: false, token: null, user: null });\n          return;\n        }\n\n        console.log('SimpleAuth - Token encontrado, verificando validade...');\n        set({ isLoading: true });\n\n        try {\n          // Tentar carregar dados do jogador para verificar se o token é válido\n          const result = await backendService.getPlayer();\n\n          if (result.success && result.player) {\n            const user: User = {\n              uid: result.player.uid,\n              nick: result.player.nick,\n              email: result.player.email,\n            };\n\n            console.log('SimpleAuth - Token válido, usuário carregado:', user);\n            set({\n              user,\n              token,\n              isAuthenticated: true,\n              isLoading: false\n            });\n          } else {\n            console.log('SimpleAuth - Token inválido');\n            get().logout();\n          }\n        } catch (error) {\n          console.error('SimpleAuth - Erro na verificação:', error);\n          get().logout();\n        }\n      },\n\n      // Limpar erro\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // Simulação de login para teste\n      simulateLogin: () => {\n        console.log('SimpleAuth - Simulando login...');\n        set({ isLoading: true });\n        \n        // Simular delay de API\n        setTimeout(() => {\n          const mockUser: User = {\n            uid: 'test-user-123',\n            nick: 'TestPlayer',\n            email: '<EMAIL>'\n          };\n          \n          const mockToken = 'mock-jwt-token-123';\n          \n          set({\n            user: mockUser,\n            token: mockToken,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n          \n          console.log('SimpleAuth - Login simulado com sucesso!');\n        }, 1000);\n      },\n    }),\n    {\n      name: 'simple-auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// Hook simplificado\nexport const useSimpleAuth = () => {\n  const {\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    register,\n    logout,\n    clearError,\n    checkAuth,\n    simulateLogin,\n  } = useSimpleAuthStore();\n\n  return {\n    // Estado\n    user,\n    token,\n    isAuthenticated,\n    isLoading,\n    error,\n\n    // Ações\n    login,\n    register,\n    logout,\n    clearError,\n    checkAuth,\n    simulateLogin,\n\n    // Computed\n    isLoggedIn: isAuthenticated && !!user,\n  };\n};\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,cAAc,QAAQ,4BAA4B;AA2B3D,OAAO,MAAMC,kBAAkB,GAAGH,MAAM,CAAkB,CAAC,CACzDC,OAAO,CACL,CAACG,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EAEX;EACAC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5BR,GAAG,CAAC;MAAEK,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACFG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,MAAMC,QAAQ,GAAG,MAAMb,cAAc,CAACS,KAAK,CAACC,WAAW,CAACI,KAAK,EAAEJ,WAAW,CAACK,QAAQ,CAAC;MAEpF,IAAIF,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACR,KAAK,IAAIQ,QAAQ,CAACI,MAAM,EAAE;QACzD;QACAC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEN,QAAQ,CAACR,KAAK,CAAC;QAElD,MAAMD,IAAU,GAAG;UACjBgB,GAAG,EAAEP,QAAQ,CAACI,MAAM,CAACG,GAAG;UACxBC,IAAI,EAAER,QAAQ,CAACI,MAAM,CAACI,IAAI;UAC1BP,KAAK,EAAED,QAAQ,CAACI,MAAM,CAACH;QACzB,CAAC;QAEDZ,GAAG,CAAC;UACFE,IAAI;UACJC,KAAK,EAAEQ,QAAQ,CAACR,KAAK;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;QAEFG,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAER,IAAI,CAAC;MAChE,CAAC,MAAM;QACLF,GAAG,CAAC;UACFK,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAEK,QAAQ,CAACL,KAAK,IAAI;QAC3B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDN,GAAG,CAAC;QACFK,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAc,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5BrB,GAAG,CAAC;MAAEK,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAErC,IAAI;MACFG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,MAAMC,QAAQ,GAAG,MAAMb,cAAc,CAACsB,QAAQ,CAACC,QAAQ,CAACF,IAAI,EAAEE,QAAQ,CAACT,KAAK,EAAES,QAAQ,CAACR,QAAQ,CAAC;MAEhG,IAAIF,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACR,KAAK,IAAIQ,QAAQ,CAACI,MAAM,EAAE;QACzD;QACAC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEN,QAAQ,CAACR,KAAK,CAAC;QAElD,MAAMD,IAAU,GAAG;UACjBgB,GAAG,EAAEP,QAAQ,CAACI,MAAM,CAACG,GAAG;UACxBC,IAAI,EAAER,QAAQ,CAACI,MAAM,CAACI,IAAI;UAC1BP,KAAK,EAAED,QAAQ,CAACI,MAAM,CAACH;QACzB,CAAC;QAEDZ,GAAG,CAAC;UACFE,IAAI;UACJC,KAAK,EAAEQ,QAAQ,CAACR,KAAK;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;QAEFG,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAER,IAAI,CAAC;MAC7D,CAAC,MAAM;QACLF,GAAG,CAAC;UACFK,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAEK,QAAQ,CAACL,KAAK,IAAI;QAC3B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDN,GAAG,CAAC;QACFK,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAgB,MAAM,EAAEA,CAAA,KAAM;IACZb,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;IAE1C;IACAM,YAAY,CAACO,UAAU,CAAC,YAAY,CAAC;IAErCvB,GAAG,CAAC;MACFE,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE,KAAK;MACtBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED;EACAkB,SAAS,EAAE,MAAAA,CAAA,KAAY;IACrB,MAAM;MAAEnB;IAAU,CAAC,GAAGJ,GAAG,CAAC,CAAC;IAE3B,IAAII,SAAS,EAAE;MACbI,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD;IACF;;IAEA;IACA,MAAMP,KAAK,GAAGa,YAAY,CAACS,OAAO,CAAC,YAAY,CAAC;IAEhD,IAAI,CAACtB,KAAK,EAAE;MACVM,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDV,GAAG,CAAC;QAAEI,eAAe,EAAE,KAAK;QAAEC,SAAS,EAAE,KAAK;QAAEF,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAC,CAAC;MAC1E;IACF;IAEAO,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACrEV,GAAG,CAAC;MAAEK,SAAS,EAAE;IAAK,CAAC,CAAC;IAExB,IAAI;MACF;MACA,MAAMqB,MAAM,GAAG,MAAM5B,cAAc,CAAC6B,SAAS,CAAC,CAAC;MAE/C,IAAID,MAAM,CAACZ,OAAO,IAAIY,MAAM,CAACX,MAAM,EAAE;QACnC,MAAMb,IAAU,GAAG;UACjBgB,GAAG,EAAEQ,MAAM,CAACX,MAAM,CAACG,GAAG;UACtBC,IAAI,EAAEO,MAAM,CAACX,MAAM,CAACI,IAAI;UACxBP,KAAK,EAAEc,MAAM,CAACX,MAAM,CAACH;QACvB,CAAC;QAEDH,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAER,IAAI,CAAC;QAClEF,GAAG,CAAC;UACFE,IAAI;UACJC,KAAK;UACLC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ,CAAC,MAAM;QACLI,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CT,GAAG,CAAC,CAAC,CAACqB,MAAM,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDL,GAAG,CAAC,CAAC,CAACqB,MAAM,CAAC,CAAC;IAChB;EACF,CAAC;EAED;EACAM,UAAU,EAAEA,CAAA,KAAM;IAChB5B,GAAG,CAAC;MAAEM,KAAK,EAAE;IAAK,CAAC,CAAC;EACtB,CAAC;EAED;EACAuB,aAAa,EAAEA,CAAA,KAAM;IACnBpB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CV,GAAG,CAAC;MAAEK,SAAS,EAAE;IAAK,CAAC,CAAC;;IAExB;IACAyB,UAAU,CAAC,MAAM;MACf,MAAMC,QAAc,GAAG;QACrBb,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,YAAY;QAClBP,KAAK,EAAE;MACT,CAAC;MAED,MAAMoB,SAAS,GAAG,oBAAoB;MAEtChC,GAAG,CAAC;QACFE,IAAI,EAAE6B,QAAQ;QACd5B,KAAK,EAAE6B,SAAS;QAChB5B,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;MAEFG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC,CAAC,EACF;EACEuB,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAGC,KAAK,KAAM;IACtBjC,IAAI,EAAEiC,KAAK,CAACjC,IAAI;IAChBC,KAAK,EAAEgC,KAAK,CAAChC,KAAK;IAClBC,eAAe,EAAE+B,KAAK,CAAC/B;EACzB,CAAC;AACH,CACF,CACF,CAAC;;AAED;AACA,OAAO,MAAMgC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IACJnC,IAAI;IACJC,KAAK;IACLC,eAAe;IACfC,SAAS;IACTC,KAAK;IACLC,KAAK;IACLa,QAAQ;IACRE,MAAM;IACNM,UAAU;IACVJ,SAAS;IACTK;EACF,CAAC,GAAG9B,kBAAkB,CAAC,CAAC;EAExB,OAAO;IACL;IACAG,IAAI;IACJC,KAAK;IACLC,eAAe;IACfC,SAAS;IACTC,KAAK;IAEL;IACAC,KAAK;IACLa,QAAQ;IACRE,MAAM;IACNM,UAAU;IACVJ,SAAS;IACTK,aAAa;IAEb;IACAS,UAAU,EAAElC,eAAe,IAAI,CAAC,CAACF;EACnC,CAAC;AACH,CAAC;AAACmC,EAAA,CAlCWD,aAAa;EAAA,QAapBrC,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}