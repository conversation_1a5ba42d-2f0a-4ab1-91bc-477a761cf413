# 🚀 Configuração Final - React + Flask

## 📋 **Passos para Configurar**

### **1. Instalar Dependências Python**
```bash
# Execute o arquivo de instalação
install_dependencies.bat

# Ou manualmente:
pip install PyJWT flask-cors python-dotenv
```

### **2. Configurar Tabela de Chat no Supabase**
1. Acesse https://supabase.com/dashboard
2. Vá no **SQL Editor** do seu projeto
3. Execute o conteúdo do arquivo `database/chat_table.sql`

### **3. Executar o Flask**
```bash
python app.py
```

### **4. Executar o React**
```bash
cd frontend
npm start
```

## ✅ **Funcionalidades Implementadas**

### **🔐 Autenticação JWT**
- Login com email/senha
- Registro de novos usuários
- Verificação de token automática
- Logout seguro

### **🎮 API do Jogo**
- **GET /api/jogador** - Dados do jogador
- **GET /api/scan** - Escanear alvos
- **POST /api/transferir** - Exploitar alvo
- **GET /api/chat/messages** - Mensagens do chat
- **POST /api/chat/send** - Enviar mensagem
- **GET /api/appstore** - Loja de apps
- **POST /api/appstore/comprar** - Comprar upgrade

### **💬 Sistema de Chat**
- Chat global em tempo real
- Polling automático (2 segundos)
- Interface minimizável
- Histórico de mensagens
- Contador de não lidas

### **🛒 Loja de Apps**
- CPU, Firewall, Antivírus
- BruteForce, BankGuard, ProxyVPN
- Cálculo automático de preços
- Verificação de níveis máximos

## 🔧 **Estrutura das APIs**

### **Autenticação**
```javascript
// Login
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "senha123"
}

// Resposta
{
  "sucesso": true,
  "user": { "uid": "...", "nick": "...", "email": "..." },
  "token": "jwt_token_aqui"
}
```

### **Dados do Jogador**
```javascript
// Headers necessários
Authorization: Bearer jwt_token_aqui

// Resposta
{
  "sucesso": true,
  "jogador": {
    "uid": "...",
    "nick": "...",
    "email": "...",
    "dinheiro": 5000,
    "cpu": 3,
    "firewall": 2,
    // ... outros campos
  }
}
```

### **Chat**
```javascript
// Enviar mensagem
POST /api/chat/send
{
  "message": "Olá pessoal!"
}

// Obter mensagens
GET /api/chat/messages
// Retorna array de mensagens
```

## 🎯 **Como Testar**

### **1. Teste de Login**
1. Acesse http://localhost:3000
2. Clique em "Criar Conta"
3. Preencha: email, senha, nickname
4. Faça login com as credenciais

### **2. Teste do Dashboard**
- Verifique se os dados do jogador aparecem
- Teste as ações rápidas
- Veja se as estatísticas estão corretas

### **3. Teste do Chat**
- Clique no ícone de chat (canto inferior direito)
- Envie algumas mensagens
- Verifique se aparecem em tempo real

### **4. Teste do Scanner**
- Vá em "Scanner" no menu
- Clique em "Scan Rápido" no dashboard
- Verifique se encontra alvos

## 🚨 **Solução de Problemas**

### **Erro: "CORS policy"**
- Certifique-se de que o Flask está rodando
- Verifique se flask-cors está instalado
- Reinicie o Flask após instalar CORS

### **Erro: "Token inválido"**
- Faça logout e login novamente
- Verifique se o JWT_SECRET está correto
- Limpe o localStorage do navegador

### **Erro: "Tabela não existe"**
- Execute o SQL do arquivo `database/chat_table.sql`
- Verifique conexão com Supabase
- Confirme se as tabelas foram criadas

### **Chat não funciona**
- Verifique se a tabela `chat_messages` existe
- Teste enviar mensagem via API diretamente
- Verifique logs do Flask

## 📈 **Próximos Passos**

Agora que a base está funcionando:

1. **Scanner Avançado** - Interface para escanear e atacar
2. **Loja Completa** - Interface visual para compras
3. **Sistema de Segurança** - Monitoramento de ataques
4. **WebSockets** - Chat em tempo real sem polling
5. **Notificações** - Sistema de alertas

## 🎉 **Sucesso!**

Se tudo estiver funcionando:
- ✅ React inicia sem erros
- ✅ Flask responde às APIs
- ✅ Login/registro funcionam
- ✅ Dashboard carrega dados
- ✅ Chat funciona em tempo real
- ✅ Navegação responsiva

**A migração para React está completa e funcional!** 🎊

### **URLs Importantes**
- **React**: http://localhost:3000
- **Flask**: http://localhost:5000
- **API Docs**: Todas as rotas começam com `/api/`

### **Estrutura Final**
```
shackweb1/
├── frontend/          # React App
├── game/             # Lógica do jogo
├── database/         # Scripts SQL
├── app.py           # Servidor Flask
├── react_api_routes.py  # APIs para React
└── install_dependencies.bat  # Instalador
```

**Parabéns! O SHACK Web Game agora tem uma interface React moderna e funcional!** 🚀
