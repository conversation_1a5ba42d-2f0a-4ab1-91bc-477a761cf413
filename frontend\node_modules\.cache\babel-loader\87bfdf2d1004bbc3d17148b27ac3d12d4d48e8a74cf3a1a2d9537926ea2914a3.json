{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\SHACKWEBGIT\\\\shackweb1\\\\frontend\\\\src\\\\pages\\\\SimpleLoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleLoginPage = () => {\n  _s();\n  const {\n    isLoading,\n    error,\n    login,\n    register,\n    simulateLogin,\n    isAuthenticated,\n    user,\n    clearError\n  } = useSimpleAuth();\n  const [isRegisterMode, setIsRegisterMode] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    nick: ''\n  });\n  console.log('SimpleLoginPage - Estado:', {\n    isLoading,\n    isAuthenticated,\n    user: !!user\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Limpar erro quando usuário digita\n    if (error) clearError();\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (isRegisterMode) {\n      if (!formData.email || !formData.password || !formData.nick) {\n        return;\n      }\n      await register({\n        email: formData.email,\n        password: formData.password,\n        nick: formData.nick\n      });\n    } else {\n      if (!formData.email || !formData.password) {\n        return;\n      }\n      await login({\n        email: formData.email,\n        password: formData.password\n      });\n    }\n  };\n  const toggleMode = () => {\n    setIsRegisterMode(!isRegisterMode);\n    setFormData({\n      email: '',\n      password: '',\n      nick: ''\n    });\n    clearError();\n  };\n\n  // Se já está autenticado, mostrar sucesso\n  if (isAuthenticated && user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card max-w-md w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-green-400 mb-4\",\n            children: \"\\u2705 Login Realizado!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-muted mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Bem-vindo, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: user.nick\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 29\n              }, this), \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: [\"UID: \", user.uid]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: [\"Email: \", user.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-green-300 mb-4\",\n              children: \"\\uD83C\\uDFAE Agora voc\\xEA pode acessar o jogo!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.href = '/game',\n              className: \"btn-primary\",\n              children: \"Ir para o Jogo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card max-w-md w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-2\",\n          children: \"\\uD83C\\uDFAE SHACK Web Game\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-muted\",\n          children: \"Login Simplificado (Modo Teste)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900 border border-red-500 text-red-100 p-4 rounded-lg mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card bg-bg-tertiary\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"\\uD83E\\uDDEA Modo de Teste\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-text-muted text-sm mb-4\",\n            children: \"Este \\xE9 um login simulado para testar o React sem o Flask. Clique no bot\\xE3o abaixo para simular um login bem-sucedido.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: simulateLogin,\n            disabled: isLoading,\n            className: `w-full py-3 px-4 rounded-lg font-medium transition-colors ${isLoading ? 'bg-gray-600 text-gray-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'}`,\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this), \"Simulando Login...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this) : '🚀 Simular Login'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card bg-yellow-900 border-yellow-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-yellow-100 mb-2\",\n            children: \"\\u26A0\\uFE0F Aviso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-yellow-200 text-sm\",\n            children: \"Esta \\xE9 uma vers\\xE3o de teste. O Flask backend ser\\xE1 conectado posteriormente.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 text-center text-text-muted text-xs\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Vers\\xE3o de teste - React funcionando \\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleLoginPage, \"roLM9JythJepd9gq3OU2o3qxVa4=\", false, function () {\n  return [useSimpleAuth];\n});\n_c = SimpleLoginPage;\nexport default SimpleLoginPage;\nvar _c;\n$RefreshReg$(_c, \"SimpleLoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useSimpleAuth", "jsxDEV", "_jsxDEV", "SimpleLoginPage", "_s", "isLoading", "error", "login", "register", "simulateLogin", "isAuthenticated", "user", "clearError", "isRegisterMode", "setIsRegisterMode", "formData", "setFormData", "email", "password", "nick", "console", "log", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "toggleMode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "uid", "onClick", "window", "location", "href", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/SHACKWEBGIT/shackweb1/frontend/src/pages/SimpleLoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSimpleAuth } from '../stores/simpleAuthStore';\n\nconst SimpleLoginPage: React.FC = () => {\n  const { isLoading, error, login, register, simulateLogin, isAuthenticated, user, clearError } = useSimpleAuth();\n\n  const [isRegisterMode, setIsRegisterMode] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    nick: ''\n  });\n\n  console.log('SimpleLoginPage - Estado:', { isLoading, isAuthenticated, user: !!user });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Limpar erro quando usuário digita\n    if (error) clearError();\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (isRegisterMode) {\n      if (!formData.email || !formData.password || !formData.nick) {\n        return;\n      }\n      await register({\n        email: formData.email,\n        password: formData.password,\n        nick: formData.nick\n      });\n    } else {\n      if (!formData.email || !formData.password) {\n        return;\n      }\n      await login({\n        email: formData.email,\n        password: formData.password\n      });\n    }\n  };\n\n  const toggleMode = () => {\n    setIsRegisterMode(!isRegisterMode);\n    setFormData({ email: '', password: '', nick: '' });\n    clearError();\n  };\n\n  // Se já está autenticado, mostrar sucesso\n  if (isAuthenticated && user) {\n    return (\n      <div className=\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\">\n        <div className=\"card max-w-md w-full\">\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-bold text-green-400 mb-4\">\n              ✅ Login Realizado!\n            </h1>\n            <div className=\"text-text-muted mb-6\">\n              <p>Bem-vindo, <strong>{user.nick}</strong>!</p>\n              <p className=\"text-sm\">UID: {user.uid}</p>\n              <p className=\"text-sm\">Email: {user.email}</p>\n            </div>\n            <div className=\"text-center\">\n              <p className=\"text-sm text-green-300 mb-4\">\n                🎮 Agora você pode acessar o jogo!\n              </p>\n              <button \n                onClick={() => window.location.href = '/game'}\n                className=\"btn-primary\"\n              >\n                Ir para o Jogo\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-bg-primary text-text-primary flex items-center justify-center\">\n      <div className=\"card max-w-md w-full\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold mb-2\">\n            🎮 SHACK Web Game\n          </h1>\n          <p className=\"text-text-muted\">\n            Login Simplificado (Modo Teste)\n          </p>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900 border border-red-500 text-red-100 p-4 rounded-lg mb-6\">\n            <p className=\"text-sm\">{error}</p>\n          </div>\n        )}\n\n        <div className=\"space-y-6\">\n          <div className=\"card bg-bg-tertiary\">\n            <h3 className=\"text-lg font-semibold mb-4\">\n              🧪 Modo de Teste\n            </h3>\n            <p className=\"text-text-muted text-sm mb-4\">\n              Este é um login simulado para testar o React sem o Flask.\n              Clique no botão abaixo para simular um login bem-sucedido.\n            </p>\n            \n            <button\n              onClick={simulateLogin}\n              disabled={isLoading}\n              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${\n                isLoading\n                  ? 'bg-gray-600 text-gray-300 cursor-not-allowed'\n                  : 'bg-blue-600 hover:bg-blue-700 text-white'\n              }`}\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center\">\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                  Simulando Login...\n                </div>\n              ) : (\n                '🚀 Simular Login'\n              )}\n            </button>\n          </div>\n\n          <div className=\"card bg-yellow-900 border-yellow-500\">\n            <h3 className=\"text-lg font-semibold text-yellow-100 mb-2\">\n              ⚠️ Aviso\n            </h3>\n            <p className=\"text-yellow-200 text-sm\">\n              Esta é uma versão de teste. O Flask backend será conectado posteriormente.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"mt-8 text-center text-text-muted text-xs\">\n          <p>Versão de teste - React funcionando ✅</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleLoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC,SAAS;IAAEC,KAAK;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,aAAa;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGZ,aAAa,CAAC,CAAC;EAE/G,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;EAEFC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;IAAEhB,SAAS;IAAEK,eAAe;IAAEC,IAAI,EAAE,CAAC,CAACA;EAAK,CAAC,CAAC;EAEtF,MAAMW,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCV,WAAW,CAACW,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAInB,KAAK,EAAEM,UAAU,CAAC,CAAC;EACzB,CAAC;EAED,MAAMgB,YAAY,GAAG,MAAOL,CAAkB,IAAK;IACjDA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,IAAIhB,cAAc,EAAE;MAClB,IAAI,CAACE,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,QAAQ,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;QAC3D;MACF;MACA,MAAMX,QAAQ,CAAC;QACbS,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BC,IAAI,EAAEJ,QAAQ,CAACI;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACJ,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;QACzC;MACF;MACA,MAAMX,KAAK,CAAC;QACVU,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvBhB,iBAAiB,CAAC,CAACD,cAAc,CAAC;IAClCG,WAAW,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC;IAClDP,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA,IAAIF,eAAe,IAAIC,IAAI,EAAE;IAC3B,oBACET,OAAA;MAAK6B,SAAS,EAAC,+EAA+E;MAAAC,QAAA,eAC5F9B,OAAA;QAAK6B,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnC9B,OAAA;UAAK6B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9B,OAAA;YAAI6B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlC,OAAA;YAAK6B,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC9B,OAAA;cAAA8B,QAAA,GAAG,aAAW,eAAA9B,OAAA;gBAAA8B,QAAA,EAASrB,IAAI,CAACQ;cAAI;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/ClC,OAAA;cAAG6B,SAAS,EAAC,SAAS;cAAAC,QAAA,GAAC,OAAK,EAACrB,IAAI,CAAC0B,GAAG;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1ClC,OAAA;cAAG6B,SAAS,EAAC,SAAS;cAAAC,QAAA,GAAC,SAAO,EAACrB,IAAI,CAACM,KAAK;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNlC,OAAA;YAAK6B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9B,OAAA;cAAG6B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAE3C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlC,OAAA;cACEoC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAQ;cAC9CV,SAAS,EAAC,aAAa;cAAAC,QAAA,EACxB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAK6B,SAAS,EAAC,+EAA+E;IAAAC,QAAA,eAC5F9B,OAAA;MAAK6B,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnC9B,OAAA;QAAK6B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9B,OAAA;UAAI6B,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAG6B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEL9B,KAAK,iBACJJ,OAAA;QAAK6B,SAAS,EAAC,mEAAmE;QAAAC,QAAA,eAChF9B,OAAA;UAAG6B,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAE1B;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CACN,eAEDlC,OAAA;QAAK6B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9B,OAAA;UAAK6B,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC9B,OAAA;YAAI6B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlC,OAAA;YAAG6B,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAG5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJlC,OAAA;YACEoC,OAAO,EAAE7B,aAAc;YACvBiC,QAAQ,EAAErC,SAAU;YACpB0B,SAAS,EAAE,6DACT1B,SAAS,GACL,8CAA8C,GAC9C,0CAA0C,EAC7C;YAAA2B,QAAA,EAEF3B,SAAS,gBACRH,OAAA;cAAK6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C9B,OAAA;gBAAK6B,SAAS,EAAC;cAAgE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,sBAExF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENlC,OAAA;UAAK6B,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACnD9B,OAAA;YAAI6B,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAE3D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlC,OAAA;YAAG6B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlC,OAAA;QAAK6B,SAAS,EAAC,0CAA0C;QAAAC,QAAA,eACvD9B,OAAA;UAAA8B,QAAA,EAAG;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CAlJID,eAAyB;EAAA,QACmEH,aAAa;AAAA;AAAA2C,EAAA,GADzGxC,eAAyB;AAoJ/B,eAAeA,eAAe;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}